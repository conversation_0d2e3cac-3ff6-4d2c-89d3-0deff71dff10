const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Main function to analyze cost data by model
async function analyzeModelData() {
  console.log('Starting model-specific analysis...');
  
  // Path to the CSV file
  const csvFilePath = path.resolve(__dirname, 'cost.csv');
  
  // Store all entries
  const allEntries = [];
  
  // Read the CSV file
  await new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        allEntries.push(row);
      })
      .on('end', () => {
        resolve();
      })
      .on('error', (err) => {
        reject(err);
      });
  });
  
  console.log(`Total entries loaded: ${allEntries.length}`);
  
  // 1. Filter and analyze by model
  const modelGroups = {};
  
  allEntries.forEach(entry => {
    const model = entry.model_permaslug || 'unknown';
    if (!modelGroups[model]) {
      modelGroups[model] = [];
    }
    modelGroups[model].push(entry);
  });
  
  // Print model-specific insights
  console.log('\n===== MODEL-SPECIFIC INSIGHTS =====');
  Object.entries(modelGroups)
    .sort((a, b) => b[1].length - a[1].length)
    .forEach(([model, entries]) => {
      // Calculate statistics
      const totalCost = entries.reduce((sum, entry) => sum + parseFloat(entry.cost_total || 0), 0);
      const totalPromptTokens = entries.reduce((sum, entry) => sum + parseInt(entry.tokens_prompt || 0), 0);
      const totalCompletionTokens = entries.reduce((sum, entry) => sum + parseInt(entry.tokens_completion || 0), 0);
      const totalReasoningTokens = entries.reduce((sum, entry) => sum + parseInt(entry.tokens_reasoning || 0), 0);
      const totalTokens = totalPromptTokens + totalCompletionTokens + totalReasoningTokens;
      
      // Analyze finish reasons
      const finishReasons = {};
      entries.forEach(entry => {
        const reason = entry.finish_reason_normalized || 'unknown';
        finishReasons[reason] = (finishReasons[reason] || 0) + 1;
      });
      
      // Print insights
      console.log(`\n${model}:`);
      console.log(`  Count: ${entries.length} (${(entries.length / allEntries.length * 100).toFixed(2)}%)`);
      console.log(`  Total cost: $${totalCost.toFixed(2)}`);
      console.log(`  Avg cost per call: $${(totalCost / entries.length).toFixed(4)}`);
      console.log(`  Total tokens: ${totalTokens.toLocaleString()}`);
      console.log(`  Avg tokens per call: ${Math.round(totalTokens / entries.length).toLocaleString()}`);
      console.log(`  Token distribution:`);
      console.log(`    Prompt: ${totalPromptTokens.toLocaleString()} (${(totalPromptTokens / totalTokens * 100).toFixed(2)}%)`);
      console.log(`    Completion: ${totalCompletionTokens.toLocaleString()} (${(totalCompletionTokens / totalTokens * 100).toFixed(2)}%)`);
      console.log(`    Reasoning: ${totalReasoningTokens.toLocaleString()} (${(totalReasoningTokens / totalTokens * 100).toFixed(2)}%)`);
      console.log(`  Finish reasons:`);
      Object.entries(finishReasons)
        .sort((a, b) => b[1] - a[1])
        .forEach(([reason, count]) => {
          console.log(`    ${reason}: ${count} (${(count / entries.length * 100).toFixed(2)}%)`);
        });
    });
  
  // 2. Special analysis for Claude Sonnet 4
  console.log('\n===== CLAUDE SONNET 4 CONVERSATION CHAIN ANALYSIS =====');
  
  // Filter for Claude Sonnet 4 entries
  const sonnetEntries = modelGroups['anthropic/claude-4-sonnet-20250522'] || [];
  
  // Sort by creation time
  sonnetEntries.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  // Group entries into conversation chains
  const conversationChains = [];
  let currentChain = [];
  let previousTokens = 0;
  
  for (let i = 0; i < sonnetEntries.length; i++) {
    const entry = sonnetEntries[i];
    const promptTokens = parseInt(entry.tokens_prompt || 0);
    
    // Start a new chain if:
    // 1. This is the first entry
    // 2. Current chain already has 10 entries
    // 3. Prompt tokens decreased (indicating a new conversation)
    // 4. Previous entry had a finish reason of "stop" (conversation ended)
    if (
      currentChain.length === 0 ||
      currentChain.length >= 10 ||
      promptTokens < previousTokens ||
      (currentChain.length > 0 && 
       (currentChain[currentChain.length - 1].finish_reason_normalized === 'stop' ||
        (new Date(entry.created_at) - new Date(currentChain[currentChain.length - 1].created_at)) > 60000)) // 1 minute gap
    ) {
      if (currentChain.length > 0) {
        conversationChains.push([...currentChain]);
      }
      currentChain = [entry];
    } else {
      currentChain.push(entry);
    }
    
    previousTokens = promptTokens;
  }
  
  // Add the last chain if it exists
  if (currentChain.length > 0) {
    conversationChains.push(currentChain);
  }
  
  // Analyze conversation chains
  const chainLengths = {};
  let totalChainCost = 0;
  let totalChains = conversationChains.length;
  
  conversationChains.forEach(chain => {
    const length = chain.length;
    chainLengths[length] = (chainLengths[length] || 0) + 1;
    
    // Calculate chain cost
    const chainCost = chain.reduce((sum, entry) => sum + parseFloat(entry.cost_total || 0), 0);
    totalChainCost += chainCost;
  });
  
  // Print conversation chain insights
  console.log(`Total conversation chains: ${totalChains}`);
  console.log(`Average cost per conversation: $${(totalChainCost / totalChains).toFixed(4)}`);
  console.log(`Chain length distribution:`);
  Object.entries(chainLengths)
    .sort((a, b) => parseInt(a[0]) - parseInt(b[0]))
    .forEach(([length, count]) => {
      console.log(`  ${length} entries: ${count} chains (${(count / totalChains * 100).toFixed(2)}%)`);
    });
  
  // Sample analysis of a few chains
  console.log(`\nDetailed analysis of 5 sample conversation chains:`);
  for (let i = 0; i < Math.min(5, conversationChains.length); i++) {
    const chain = conversationChains[i];
    console.log(`\nChain #${i + 1} (${chain.length} entries):`);
    
    let totalChainPromptTokens = 0;
    let totalChainCompletionTokens = 0;
    let totalChainCost = 0;
    
    chain.forEach((entry, index) => {
      const promptTokens = parseInt(entry.tokens_prompt || 0);
      const completionTokens = parseInt(entry.tokens_completion || 0);
      const cost = parseFloat(entry.cost_total || 0);
      
      totalChainPromptTokens += promptTokens;
      totalChainCompletionTokens += completionTokens;
      totalChainCost += cost;
      
      console.log(`  Entry ${index + 1}:`);
      console.log(`    Created: ${entry.created_at}`);
      console.log(`    Prompt tokens: ${promptTokens.toLocaleString()}`);
      console.log(`    Completion tokens: ${completionTokens.toLocaleString()}`);
      console.log(`    Cost: $${cost.toFixed(4)}`);
      console.log(`    Finish reason: ${entry.finish_reason_normalized}`);
    });
    
    console.log(`  Chain summary:`);
    console.log(`    Total prompt tokens: ${totalChainPromptTokens.toLocaleString()}`);
    console.log(`    Total completion tokens: ${totalChainCompletionTokens.toLocaleString()}`);
    console.log(`    Total cost: $${totalChainCost.toFixed(4)}`);
  }
  
  // Estimate user message count based on conversation chains
  const estimatedUserMessages = conversationChains.length;
  console.log(`\nEstimated user messages based on conversation chains: ${estimatedUserMessages}`);
  
  // Calculate daily stats for Claude Sonnet 4 conversation chains
  const dailyChains = {};
  
  conversationChains.forEach(chain => {
    // Use the first entry's date for the chain
    const date = chain[0].created_at.split(' ')[0];
    if (!dailyChains[date]) {
      dailyChains[date] = {
        chains: 0,
        totalCost: 0,
        totalEntries: 0
      };
    }
    
    dailyChains[date].chains++;
    dailyChains[date].totalEntries += chain.length;
    dailyChains[date].totalCost += chain.reduce((sum, entry) => sum + parseFloat(entry.cost_total || 0), 0);
  });
  
  console.log(`\nDaily Claude Sonnet 4 conversation stats:`);
  Object.entries(dailyChains)
    .sort((a, b) => new Date(a[0]) - new Date(b[0]))
    .forEach(([date, stats]) => {
      console.log(`  ${date}:`);
      console.log(`    Conversations: ${stats.chains}`);
      console.log(`    Total entries: ${stats.totalEntries}`);
      console.log(`    Entries per conversation: ${(stats.totalEntries / stats.chains).toFixed(2)}`);
      console.log(`    Total cost: $${stats.totalCost.toFixed(2)}`);
      console.log(`    Cost per conversation: $${(stats.totalCost / stats.chains).toFixed(4)}`);
    });
}

// Run the analysis
analyzeModelData().catch(console.error);
