'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface TemperatureOptimizationData {
  id: string;
  messageId: string;
  chatId: string;
  projectId: string | null;
  userId: string | null;
  optimizedTemperature: number;
  selectedModel: string;
  reasoning: string;
  contextFactors: string[];
  userProgression: {
    isProgressing: boolean;
    isStuck: boolean;
    stuckSeverity?: "mild" | "moderate" | "severe";
  } | null;
  optimizationDuration: number | null;
  wasSuccessful: boolean;
  errorMessage: string | null;
  usedFallback: boolean;
  fallbackReason: string | null;
  createdAt: string;
}

interface AnalyticsData {
  totalOptimizations: number;
  successRate: number;
  averageTemperature: number;
  averageDuration: number;
  averageFileCount: number;
  mostCommonFactors: Array<{ factor: string; count: number }>;
  temperatureDistribution: Array<{ range: string; count: number }>;
  progressionAnalytics: {
    progressingRate: number;
    stuckRate: number;
    mildStuckRate: number;
    moderateStuckRate: number;
    severeStuckRate: number;
    totalSamples: number;
  };
  recentOptimizations: TemperatureOptimizationData[];
}

export function TemperatureOptimizationAnalytics() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/temperature-optimization-analytics');
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      const analyticsData = await response.json();
      setData(analyticsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error loading analytics</h3>
          <p className="text-red-600 text-sm mt-1">{error}</p>
          <button
            onClick={fetchAnalytics}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="p-6">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Temperature Optimization & User Progression Analytics</h1>
        <button
          onClick={fetchAnalytics}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Refresh
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Optimizations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalOptimizations.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {(data.successRate * 100).toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Avg Temperature</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.averageTemperature.toFixed(2)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Avg Duration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.averageDuration.toFixed(0)}ms</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Avg File Count</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{data.averageFileCount.toFixed(0)}</div>
            <div className="text-xs text-gray-500 mt-1">
              {data.averageFileCount < 20 ? 'Small projects' :
               data.averageFileCount < 50 ? 'Medium projects' :
               data.averageFileCount < 100 ? 'Large projects' : 'Complex codebases'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Progression Analytics */}
      <Card>
        <CardHeader>
          <CardTitle>User Progression Analytics (Key Development Platform Metric)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {(data.progressionAnalytics.progressingRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Progressing</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {(data.progressionAnalytics.stuckRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Stuck</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {(data.progressionAnalytics.mildStuckRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Mild Stuck</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(data.progressionAnalytics.moderateStuckRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Moderate Stuck</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-700">
                {(data.progressionAnalytics.severeStuckRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Severe Stuck</div>
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-center">
              <div className="text-lg text-gray-600">
                Total Samples: {data.progressionAnalytics.totalSamples}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Progression tracking for development platform users
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Context Factors */}
      <Card>
        <CardHeader>
          <CardTitle>Most Common Context Factors</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {data.mostCommonFactors.map((factor) => (
              <Badge key={factor.factor} variant="secondary" className="text-sm">
                {factor.factor.replace(/_/g, ' ')} ({factor.count})
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Temperature Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Temperature Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {data.temperatureDistribution.map((range) => (
              <div key={range.range} className="flex items-center justify-between">
                <span className="text-sm font-medium">{range.range}</span>
                <div className="flex items-center gap-2">
                  <div
                    className="h-2 bg-blue-500 rounded"
                    style={{
                      width: `${(range.count / data.totalOptimizations) * 200}px`,
                      minWidth: '4px'
                    }}
                  ></div>
                  <span className="text-sm text-gray-600">{range.count}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Optimizations */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Optimizations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.recentOptimizations.map((opt) => (
              <div key={opt.id} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant={opt.wasSuccessful ? "default" : "destructive"}>
                      {opt.wasSuccessful ? "Success" : "Failed"}
                    </Badge>
                    <span className="text-sm font-medium">
                      Temperature: {opt.optimizedTemperature.toFixed(2)}
                    </span>
                    {opt.optimizationDuration && (
                      <span className="text-sm text-gray-600">
                        ({opt.optimizationDuration}ms)
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-gray-500">
                    {new Date(opt.createdAt).toLocaleString()}
                  </span>
                </div>
                <p className="text-sm text-gray-700">{opt.reasoning}</p>
                <div className="flex flex-wrap gap-1">
                  {opt.contextFactors.map((factor) => (
                    <Badge key={factor} variant="outline" className="text-xs">
                      {factor.replace(/_/g, ' ')}
                    </Badge>
                  ))}
                </div>
                {opt.userProgression && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {opt.userProgression.isProgressing && (
                      <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                        🚀 Progressing
                      </Badge>
                    )}
                    {opt.userProgression.isStuck && (
                      <Badge
                        variant="destructive"
                        className={`text-xs ${
                          opt.userProgression.stuckSeverity === 'severe' ? 'bg-red-600' :
                          opt.userProgression.stuckSeverity === 'moderate' ? 'bg-orange-500' :
                          'bg-yellow-500'
                        }`}
                      >
                        🚫 Stuck ({opt.userProgression.stuckSeverity || 'unknown'})
                      </Badge>
                    )}
                  </div>
                )}
                {opt.errorMessage && (
                  <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                    Error: {opt.errorMessage}
                  </p>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
