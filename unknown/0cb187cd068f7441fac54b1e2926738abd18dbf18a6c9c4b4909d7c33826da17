CREATE TABLE "TemperatureOptimization" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"messageId" uuid NOT NULL,
	"chatId" uuid NOT NULL,
	"projectId" uuid,
	"userId" uuid,
	"optimizedTemperature" real NOT NULL,
	"selectedModel" varchar(100) NOT NULL,
	"reasoning" text NOT NULL,
	"contextFactors" json NOT NULL,
	"optimizationDuration" integer,
	"wasSuccessful" boolean DEFAULT true NOT NULL,
	"errorMessage" text,
	"usedFallback" boolean DEFAULT false NOT NULL,
	"fallbackReason" varchar(100),
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "TemperatureOptimization" ADD CONSTRAINT "TemperatureOptimization_messageId_Message_id_fk" FOREIGN KEY ("messageId") REFERENCES "public"."Message"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "TemperatureOptimization" ADD CONSTRAINT "TemperatureOptimization_chatId_Chat_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."Chat"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "TemperatureOptimization" ADD CONSTRAINT "TemperatureOptimization_projectId_Project_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "TemperatureOptimization" ADD CONSTRAINT "TemperatureOptimization_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "temperature_optimization_messageId_idx" ON "TemperatureOptimization" USING btree ("messageId");--> statement-breakpoint
CREATE INDEX "temperature_optimization_chatId_idx" ON "TemperatureOptimization" USING btree ("chatId");--> statement-breakpoint
CREATE INDEX "temperature_optimization_projectId_idx" ON "TemperatureOptimization" USING btree ("projectId");--> statement-breakpoint
CREATE INDEX "temperature_optimization_userId_idx" ON "TemperatureOptimization" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "temperature_optimization_createdAt_idx" ON "TemperatureOptimization" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "temperature_optimization_temperature_idx" ON "TemperatureOptimization" USING btree ("optimizedTemperature");--> statement-breakpoint
CREATE INDEX "temperature_optimization_successful_idx" ON "TemperatureOptimization" USING btree ("wasSuccessful");