const fs = require('fs');
const path = require('path');

// Read the request JSON file
const requestFile = path.join(__dirname, 'req.json');
const requestData = JSON.parse(fs.readFileSync(requestFile, 'utf8'));

// Function to count tokens (rough estimate - 4 characters per token)
function estimateTokens(text) {
    return Math.ceil(text.length / 4);
}

// Function to analyze message content
function analyzeMessage(message) {
    if (!message) return { tokens: 0, details: {} };
    
    let totalTokens = 0;
    const details = {};
    
    // Handle different message formats
    if (typeof message === 'string') {
        totalTokens = estimateTokens(message);
        details.text = totalTokens;
    } else if (Array.isArray(message)) {
        message.forEach((item, index) => {
            if (item.type === 'text') {
                const textTokens = estimateTokens(item.text);
                totalTokens += textTokens;
                details[`text_${index}`] = textTokens;
            } else if (item.type) {
                const itemTokens = estimateTokens(JSON.stringify(item));
                totalTokens += itemTokens;
                details[`${item.type}_${index}`] = itemTokens;
            }
        });
    } else if (typeof message === 'object') {
        if (message.content) {
            if (typeof message.content === 'string') {
                const contentTokens = estimateTokens(message.content);
                totalTokens += contentTokens;
                details.content = contentTokens;
            } else if (Array.isArray(message.content)) {
                message.content.forEach((item, index) => {
                    if (item.type === 'text') {
                        const textTokens = estimateTokens(item.text);
                        totalTokens += textTokens;
                        details[`content_text_${index}`] = textTokens;
                    } else if (item.type) {
                        const itemTokens = estimateTokens(JSON.stringify(item));
                        totalTokens += itemTokens;
                        details[`content_${item.type}_${index}`] = itemTokens;
                    }
                });
            }
        }
        
        // Add tokens for other properties
        const otherProps = { ...message };
        delete otherProps.content;
        const otherTokens = estimateTokens(JSON.stringify(otherProps));
        totalTokens += otherTokens;
        details.other_properties = otherTokens;
    }
    
    return { tokens: totalTokens, details };
}

// Analyze the request
function analyzeRequest(request) {
    const results = {
        model: request.model,
        totalTokens: 0,
        messageBreakdown: [],
        largeContentItems: []
    };
    
    // Analyze each message
    if (request.messages && Array.isArray(request.messages)) {
        request.messages.forEach((message, index) => {
            const analysis = analyzeMessage(message.content);
            
            results.totalTokens += analysis.tokens;
            
            // Find large content items (more than 1000 tokens)
            if (message.content && Array.isArray(message.content)) {
                message.content.forEach((item, itemIndex) => {
                    if (item.type === 'text') {
                        const itemTokens = estimateTokens(item.text);
                        if (itemTokens > 1000) {
                            results.largeContentItems.push({
                                messageIndex: index,
                                itemIndex,
                                type: item.type,
                                tokens: itemTokens,
                                preview: item.text.substring(0, 100) + '...'
                            });
                        }
                    }
                });
            }
            
            results.messageBreakdown.push({
                role: message.role,
                tokens: analysis.tokens,
                details: analysis.details
            });
        });
    }
    
    // Find the largest messages
    results.largestMessages = [...results.messageBreakdown]
        .sort((a, b) => b.tokens - a.tokens)
        .slice(0, 3);
    
    return results;
}

// Analyze the file content patterns
function analyzeFileContent(request) {
    const filePatterns = {
        totalFileMessages: 0,
        fileMessageSizes: [],
        averageFileSize: 0,
        totalFileTokens: 0,
        fileTypes: {}
    };
    
    // Look for MO_FILE tags in user messages
    if (request.messages && Array.isArray(request.messages)) {
        request.messages.forEach(message => {
            if (message.role === 'user' && message.content) {
                let content = '';
                if (typeof message.content === 'string') {
                    content = message.content;
                } else if (Array.isArray(message.content)) {
                    content = message.content
                        .filter(item => item.type === 'text')
                        .map(item => item.text)
                        .join('\n');
                }
                
                // Count MO_FILE occurrences
                const moFileMatches = content.match(/<MO_FILE[^>]*>/g) || [];
                filePatterns.totalFileMessages += moFileMatches.length;
                
                // Extract file types
                moFileMatches.forEach(match => {
                    const typeMatch = match.match(/type="([^"]+)"/);
                    if (typeMatch && typeMatch[1]) {
                        const fileType = typeMatch[1];
                        filePatterns.fileTypes[fileType] = (filePatterns.fileTypes[fileType] || 0) + 1;
                    }
                    
                    // Estimate file size
                    const startIdx = content.indexOf(match);
                    if (startIdx !== -1) {
                        const endTag = '</MO_FILE>';
                        const endIdx = content.indexOf(endTag, startIdx);
                        if (endIdx !== -1) {
                            const fileContent = content.substring(startIdx, endIdx + endTag.length);
                            const tokens = estimateTokens(fileContent);
                            filePatterns.fileMessageSizes.push(tokens);
                            filePatterns.totalFileTokens += tokens;
                        }
                    }
                });
            }
        });
    }
    
    // Calculate average file size
    if (filePatterns.fileMessageSizes.length > 0) {
        filePatterns.averageFileSize = Math.round(
            filePatterns.totalFileTokens / filePatterns.fileMessageSizes.length
        );
    }
    
    return filePatterns;
}

// Main analysis
const analysis = analyzeRequest(requestData);
const fileAnalysis = analyzeFileContent(requestData);

// Combine results
const results = {
    model: analysis.model,
    totalEstimatedTokens: analysis.totalTokens,
    messageCount: requestData.messages.length,
    largestMessages: analysis.largestMessages,
    largeContentItems: analysis.largeContentItems,
    fileAnalysis
};

// Output results
console.log('=== TOKEN USAGE ANALYSIS ===');
console.log(`Model: ${results.model}`);
console.log(`Total Estimated Tokens: ${results.totalEstimatedTokens}`);
console.log(`Total Messages: ${results.messageCount}`);
console.log('\n=== LARGEST MESSAGES ===');
results.largestMessages.forEach((msg, i) => {
    console.log(`${i+1}. Role: ${msg.role}, Tokens: ${msg.tokens}`);
});

console.log('\n=== FILE CONTENT ANALYSIS ===');
console.log(`Total File Messages: ${fileAnalysis.totalFileMessages}`);
console.log(`Average File Size (tokens): ${fileAnalysis.averageFileSize}`);
console.log(`Total File Tokens: ${fileAnalysis.totalFileTokens}`);
console.log('File Types:');
Object.entries(fileAnalysis.fileTypes).forEach(([type, count]) => {
    console.log(`  - ${type}: ${count}`);
});

console.log('\n=== LARGE CONTENT ITEMS (>1000 tokens) ===');
if (results.largeContentItems.length === 0) {
    console.log('None found');
} else {
    results.largeContentItems.forEach((item, i) => {
        console.log(`${i+1}. Message ${item.messageIndex}, Item ${item.itemIndex}: ${item.tokens} tokens`);
        console.log(`   Preview: ${item.preview}`);
    });
}

// Write detailed analysis to file
fs.writeFileSync(
    path.join(__dirname, 'token-analysis-results.json'), 
    JSON.stringify(results, null, 2)
);

console.log('\nDetailed analysis written to token-analysis-results.json');
