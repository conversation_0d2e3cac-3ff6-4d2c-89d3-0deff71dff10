const fs = require('fs');
const csv = require('csv-parser');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const path = require('path');

// Main function to analyze cost data
async function analyzeCostData() {
  console.log('Starting cost analysis...');
  
  // Path to the CSV file
  const csvFilePath = path.resolve(__dirname, 'cost.csv');
  
  // Create a worker to parse the CSV file in the background
  const worker = new Worker(__filename, {
    workerData: { csvFilePath }
  });
  
  // Process messages from the worker
  worker.on('message', (data) => {
    if (data.type === 'progress') {
      console.log(`Processing: ${data.processed} rows analyzed`);
    } else if (data.type === 'result') {
      console.log('\n===== CSV ANALYSIS RESULTS =====');
      console.log(`Total entries: ${data.totalEntries}`);
      console.log(`Date range: ${data.dateRange.start} to ${data.dateRange.end}`);
      console.log(`Total cost: $${data.totalCost.toFixed(2)}`);
      console.log(`Average cost per entry: $${data.avgCostPerEntry.toFixed(4)}`);
      
      console.log('\n===== MODEL DISTRIBUTION =====');
      Object.entries(data.modelDistribution)
        .sort((a, b) => b[1].count - a[1].count)
        .forEach(([model, stats]) => {
          console.log(`${model}:`);
          console.log(`  Count: ${stats.count} (${(stats.count / data.totalEntries * 100).toFixed(2)}%)`);
          console.log(`  Total cost: $${stats.cost.toFixed(2)}`);
          console.log(`  Avg cost per call: $${(stats.cost / stats.count).toFixed(4)}`);
          console.log(`  Total tokens: ${stats.tokens.toLocaleString()}`);
          console.log(`  Avg tokens per call: ${Math.round(stats.tokens / stats.count).toLocaleString()}`);
        });
      
      console.log('\n===== DAILY COST BREAKDOWN =====');
      Object.entries(data.dailyCosts)
        .sort((a, b) => new Date(a[0]) - new Date(b[0]))
        .forEach(([date, stats]) => {
          console.log(`${date}:`);
          console.log(`  Entries: ${stats.count}`);
          console.log(`  Total cost: $${stats.cost.toFixed(2)}`);
          console.log(`  Total tokens: ${stats.tokens.toLocaleString()}`);
        });
      
      // Now we need to compare this with database data
      console.log('\n===== ANALYSIS COMPLETE =====');
      console.log('Please run database queries using MCP Supabase to compare with these results.');
    }
  });
  
  worker.on('error', (err) => {
    console.error('Worker error:', err);
  });
  
  worker.on('exit', (code) => {
    if (code !== 0) {
      console.error(`Worker stopped with exit code ${code}`);
    }
  });
}

// Worker thread to parse the CSV file
if (!isMainThread) {
  const { csvFilePath } = workerData;
  
  const results = [];
  let processed = 0;
  const reportInterval = 5000; // Report progress every 5000 rows
  
  // Tracking variables
  const modelDistribution = {};
  const dailyCosts = {};
  let totalCost = 0;
  let minDate = null;
  let maxDate = null;
  
  fs.createReadStream(csvFilePath)
    .pipe(csv())
    .on('data', (row) => {
      processed++;
      
      // Report progress periodically
      if (processed % reportInterval === 0) {
        parentPort.postMessage({ type: 'progress', processed });
      }
      
      // Extract date (YYYY-MM-DD)
      const createdAt = row.created_at ? row.created_at.split(' ')[0] : 'unknown';
      
      // Update date range
      if (!minDate || createdAt < minDate) minDate = createdAt;
      if (!maxDate || createdAt > maxDate) maxDate = createdAt;
      
      // Parse cost
      const cost = parseFloat(row.cost_total) || 0;
      totalCost += cost;
      
      // Parse tokens
      const promptTokens = parseInt(row.tokens_prompt) || 0;
      const completionTokens = parseInt(row.tokens_completion) || 0;
      const reasoningTokens = parseInt(row.tokens_reasoning) || 0;
      const totalTokens = promptTokens + completionTokens + reasoningTokens;
      
      // Track model distribution
      const model = row.model_permaslug || 'unknown';
      if (!modelDistribution[model]) {
        modelDistribution[model] = { count: 0, cost: 0, tokens: 0 };
      }
      modelDistribution[model].count++;
      modelDistribution[model].cost += cost;
      modelDistribution[model].tokens += totalTokens;
      
      // Track daily costs
      if (!dailyCosts[createdAt]) {
        dailyCosts[createdAt] = { count: 0, cost: 0, tokens: 0 };
      }
      dailyCosts[createdAt].count++;
      dailyCosts[createdAt].cost += cost;
      dailyCosts[createdAt].tokens += totalTokens;
      
      // Store the row for potential further analysis
      results.push(row);
    })
    .on('end', () => {
      // Calculate final statistics
      const avgCostPerEntry = totalCost / processed;
      
      // Send results back to main thread
      parentPort.postMessage({
        type: 'result',
        totalEntries: processed,
        dateRange: { start: minDate, end: maxDate },
        totalCost,
        avgCostPerEntry,
        modelDistribution,
        dailyCosts,
        // We don't send the full results array as it could be very large
      });
    });
}

// Run the main function if this is the main thread
if (isMainThread) {
  analyzeCostData();
}
