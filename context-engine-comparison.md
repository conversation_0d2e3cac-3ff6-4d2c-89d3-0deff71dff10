# Comparison: Codebase Retrieval Tool vs. Context Engine

This document compares the output format and efficiency of the `codebase-retrieval` tool used in this conversation with the `ContextEngine` implementation in the codebase.

## 1. Sample Output from `codebase-retrieval` Tool

When querying "Find code in the chat tools or handlers that uses the context engine or the queryCodebase tool", the tool returned the following output in exactly this format:

```
The following code sections were retrieved:
Path: src/lib/chat/tools/query-codebase.ts
import {z} from 'zod';
import {ContextEngine} from '@/lib/services/context-engine';
import {getProjectById} from '@/lib/db/project-queries';
import {tool} from "ai";
import {Project} from "@/lib/db/schema";
import {FileItem} from "@/types/file";

/**
 * Schema for the query codebase tool
 */
export const QueryCodebaseSchema = z.object({
    query: z.string().describe('The natural language query about the codebase'),
});

/**
 * Tool for querying the codebase using the context engine
 * This replaces the need for multiple getFileContents calls
 */
export const queryCodebase = ({
                                  projectId,
                                  files
                              }: { projectId: string, files: FileItem[] }) => {
    return tool({
        description: 'Query the codebase to understand its structure, relationships, and relevant files for a specific task. Use this instead of requesting individual files.',
        parameters: QueryCodebaseSchema,
        execute: async ({query}: z.infer<typeof QueryCodebaseSchema>) => {
            try {
                if (!files || files.length === 0) {
                    throw new Error('No files available for context engine');
                }

                // Get project information if available
                let project: Project | null = null;
                if (projectId) {
                    try {
                        project = await getProjectById({id: projectId});
                    } catch (error) {
                        console.warn('Could not fetch project information:', error);
                        // Continue without project info
                    }
                }

                if (!project) {
                    throw new Error('No project information available');
                }

                // Initialize the context engine with the current files and project
                const contextEngine = new ContextEngine(files, project);

                // Query the context engine
                const result = await contextEngine.query(query);

                // Prepare a more informative response
                let message = `Found ${result.relevantFiles.length} relevant files for your query.`;

                if (result.actionPlan) {
                    message += `\n\nAction Plan: ${result.actionPlan.summary}\nComplexity: ${result.actionPlan.complexity}\nSteps: ${result.actionPlan.steps.length}`;
                }

                if (result.supabaseSchema) {
                    message += `\n\nSupabase integration detected with ${result.supabaseSchema.tables.length} tables.`;
                }

                return {
                    result,
                    message,
                };
            } catch (error) {
                console.error('Context engine tool error:', error);
                throw new Error(`Failed to query context engine: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        },
    });
}
...
Path: src/lib/chat/tools/get-supabase-instructions.ts
import { DataStreamWriter, tool } from "ai";
import { z } from "zod";
import { CreditUsageTracker } from "@/lib/credit/CreditUsageTracker";
import { SupabaseIntegrationProvider } from "@/lib/integrations/supabase/SupabaseIntegrationProvider";
import { Project } from "@/lib/db/schema";

/**
 * Tool to fetch Supabase instructions for a chat
 * This provides the AI with context about the Supabase project configuration
 * including schema, functions, and other relevant database information
 */
export const getSupabaseInstructions = ({
  dataStream,
  creditUsageTracker,
  project
}: {
  dataStream: DataStreamWriter;
  creditUsageTracker: CreditUsageTracker;
  project: Project;
}) => {
  return tool({
...
    execute: async ({ reason }: { reason: string }) => {
      try {
        console.log(`Fetching Supabase instructions. Reason: ${reason}`);

        // Create a new instance of the SupabaseIntegrationProvider
        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();

        // Get the latest instructions for the chat
        const result = await supabaseIntegrationProvider.getLatestInstructionsForChat({ project });

        // Track the operation for credit usage
        creditUsageTracker.trackOperation('tool_call');

        return JSON.stringify({
          instructions: result.instructions,
          comment: 'Use this Supabase schema information to implement database features correctly. Make sure to follow the schema structure and use proper RLS policies for security.'
        });
...
Path: src/lib/services/context-engine.ts
...

    /**
     * Query the context engine for information about the codebase
     * @param query Natural language query about the codebase
     * @returns Comprehensive information about the relevant parts of the codebase
     */
    async query(query: string): Promise<ContextResult> {
        console.time('context-engine-query');

        // First, determine which files are most relevant to the query
        console.time('find-relevant-files');
        const relevantFiles = await this.findRelevantFiles(query);
        console.timeEnd('find-relevant-files');

        // Start parallel operations
        const parallelOperations = [];
...
Path: src/app/api/chat/route.ts
...
import {addAiMemory} from "@/lib/chat/tools/add-ai-memory";
import {StreamService} from "@/lib/services/stream-service";
import {queryCodebase} from "@/lib/chat/tools/query-codebase";
...
//     ALWAYS use <MO_FILE path="absolute path without / prefix at the beginning" approx_lines="Approximate line count number of the file"></MO_FILE> and <MO_DIFF path="absolute path without / prefix at the beginning"></MO_DIFF> to make changes.
//     ALWAYS try to batch the get_file_contents to fetch similar/related files to save costs.
//     ALWAYS play close attention to warnings and ensure each file is not more than 300 lines long.
//
// You now have access to a powerful context engine tool called 'queryCodebase'. Use this tool FIRST before any other tools to understand the codebase structure and relationships.
//
// Context Engine Usage:
// 1. Call queryCodebase with a specific question about what you need to understand
...

               const enabledTools = ['aiAddMemory'];
               if (agentModeEnabled) {
                   if (!isFirstMessage) {
                       // enabledTools.push('getFileContents')
                       enabledTools.push('queryCodebase')
                       if (isSupabaseConnected) {
                           enabledTools.push('getSupabaseInstructions')
                       }
                   }
               }
               console.log('enabledTools ---->', enabledTools)

               const streamService = new StreamService({
                   // model,
                   messages: messageHandler.getMessagesForRequest({agentModeEnabled}).filter(m => !!m),
                   parser,
                   diffParser,
                   dataStream,
                   userMessageId,
                   abortSignal: request.signal,
                   agentModeEnabled,
                   isFirstMessage,
                   enableValidation: true,
                   isSupabaseConnected: isSupabaseConnected,
                   enabledTools,
                   tools: {
                       // getFileContents: getFileContents({
                       //     files,
                       //     fileManager,
                       //     dataStream,
                       //     creditUsageTracker,
                       //     initialPromptGuidelines,
                       //     isFirstMessage,
                       //     circuitBreakerState
                       // }),
                       addAiMemory: addAiMemory({
                           creditUsageTracker,
                           dataStream,
                           projectId
                       }),
                       getSupabaseInstructions: getSupabaseInstructions({
                           creditUsageTracker,
                           dataStream,
                           project
                       }),
                       queryCodebase: queryCodebase({
                           projectId,
                           files
                       })
                   },
...

                                   const finalMessages = responseMessagesWithoutIncompleteToolCalls.map(
                                       (message, index) => {
                                           const messageId = generateUUID();

                                           if (message.role === 'assistant') {
                                               dataStream.writeMessageAnnotation({
                                                   messageIdFromServer: messageId,
                                               });

                                               const text: string = (((message.content?.[0] as TextPart)?.text) || message.content || '') as string;

                                               let sqlCount = 0;
                                               if(text && text.match) {
                                                   sqlCount = (text?.match(/<MO_DATABASE_QUERY[^>]*>[\s\S]*?<\/MO_DATABASE_QUERY>/g) || []).length;
                                               }

                                               creditUsageTracker.trackOperation('sql_query', sqlCount);
                                           }
...
Path: src/app/api/chat/v2/route.ts
...

    // Parse request body - this is a critical operation
    let {
        id,
        messages,
        files,
        activeFile,
        dependencies,
        linkSupabaseProjectId,
        linkSupabaseConnection,
        projectId,
        logs,
        agentModeEnabled,
        isReload,
        isInitial
    }: {
        id: string;
        messages: Array<Message>;
        files: any[];
        activeFile: string;
        dependencies?: Record<string, any>;
        linkSupabaseProjectId?: string;
        linkSupabaseConnection?: string;
        projectId: string;
        logs?: any[];
        agentModeEnabled: boolean;
        isReload: boolean;
        isInitial?: boolean;
    } = await request.json();

    // Start with minimal validation
    const apiModelId = DEFAULT_MODEL_NAME;
    const model = models.find(model => model.id === apiModelId);

    if (!model) {
        return new Response("Model not found", { status: 400 });
    }
...

                    // Now enhance the prompt (this would normally block, but we've already started streaming)
                    enhancedRequirements = await enhancerPrompt(userMessage, initialPromptGuidelines, dataStream);

                    // Update the user message with enhanced requirements
                    messageHandler.enhanceUserMessage(
                        `\n<INTERNAL_UX_DESIGNER_GUIDELINES>${enhancedRequirements}</INTERNAL_UX_DESIGNER_GUIDELINES>\n${initialPromptGuidelines}`
                    );
                }

                // If agent mode is enabled and not first message, add agent instructions
                if (agentModeEnabled && !isFirstMessage) {
                    messageHandler.enhanceUserMessage(`
<extended_system_prompt>
You now have access to a powerful context engine tool called 'queryCodebase'. Use this tool FIRST before any other tools to understand the codebase structure and relationships.

Context Engine Usage:
1. Call queryCodebase with a specific question about what you need to understand
2. The tool will return relevant files and their relationships in a single call
3. Use this information to plan your changes without requesting individual files

After using the context engine, make edits using MO_FILE or MO_DIFF or write SQL queries using MO_DATABASE_QUERY.
Please don't get stuck in a tool calling loop. The context engine eliminates the need for multiple getFileContents calls.
Please make sure to make holistic and coherent changes all at once. If fixing bugs, use the context engine to find all relevant files and fix them all at once.
...

                // Start the AI stream
                const startTime = Date.now();
                const result = streamText({
                    model: customModel(model.apiIdentifier),
                    maxSteps: 10,
                    experimental_activeTools: agentModeEnabled && !isFirstMessage ? ['getFileContents'] : [],
                    experimental_toolCallStreaming: false,
                    toolCallStreaming: false,
                    temperature: 0.3,
                    messages: [
                        ...messageHandler.getMessagesForRequest({agentModeEnabled}).filter(m => !!m),
                    ],
                    experimental_transform: smoothStream({ chunking: 'line' }),
                    maxRetries: 3,
                    abortSignal: request.signal,
                    experimental_continueSteps: true,
                    onError: (error) => {
                        console.log('Error', error);
                        throw error;
                    },
...
Path: src/components/base/chat.tsx
...

    const {
        messages,
        setMessages,
        handleSubmit,
        input,
        setInput,
        append,
        isLoading,
        stop,
        reload,
        data,
        status
    } = useChat({
        id,
        keepLastMessageOnError: false,
        experimental_prepareRequestBody: (options: {
            id: string;
            messages: UIMessage[];
            requestData?: JSONValue;
            requestBody?: object;
        }) => {
            options.messages = options.messages.slice(-20);
            return {
                files: session.fileTree.length ? session.fileTree : DEFAULT_CODE,
                activeFile: session.activeFile,
                dependencies: session.dependencies,
                linkSupabaseProjectId: integrationStore.currentSelectedProjectId,
                linkSupabaseConnection: integrationStore.getConnection("supabase")?.id,
                projectId: session.projectId,
                logs: logStore.getLogs(id),
                agentModeEnabled: agentModeEnabled,
                messages: options.messages,
                id,
...
Path: src/lib/chat/handlers/message.handler.ts
import {
    type Message,
    convertToCoreMessages,
    CoreMessage,
    CoreUserMessage,
    ImagePart,
    TextPart, UserContent, CoreSystemMessage
} from 'ai';
import {onboardingPrompt, systemPrompt} from "@/lib/ai/prompts";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {ComponentContext} from "@/types/component-context";
import {COMPLETE_STREAMLINED_PROMPT} from '@/lib/ai/prompt-directory';
import {Extractor} from "@/lib/chat/handlers/file-message/extractor";
import {COMPLETE_AGENT_PROMPT} from "@/lib/ai/prompts/agent-prompt";
...

    /**
     * Initialize the handler with messages
     * This sets up the internal state for processing
     */
    public initialize(messages: Array<Message>, minMessages: number = 5, options: {projectId?: string, backendEnabled?: boolean, componentContexts?: ComponentContext[], isFirstUserMessage?: boolean, agentModeEnabled?: boolean}): void {
        let system;

        if (options?.isFirstUserMessage) {
            system = COMPLETE_STREAMLINED_PROMPT
        } else {
            if (options?.agentModeEnabled) {
                system = COMPLETE_AGENT_PROMPT
            } else {
                system = systemPrompt;
            }
        }

            // .replace(/{{BACKEND_PROMPT}}/g, options.backendEnabled ? backendPrompt : "There is no backend configured. NO MATTER WHAT, don't write backend code even if the user asks for it.");
...

    </reminders>
    </extended_system_prompt>`
            }
        ]
        this.messagesWithTurns = this.getMessagesWithCompleteTurns(messages, minMessages);
        this.coreMessages = convertToCoreMessages(this.messagesWithTurns);
        this.userMessage = this.getMostRecentUserMessage(this.coreMessages);
        this.filteredMessages = this.filterCoreMessages(this.coreMessages);
        if(options.componentContexts) {
            this.componentContexts = options.componentContexts;
        }
        this.appendMessageCountSystemMessage();
    }
...
        messages = messages.concat(clonedFilteredMessages)
        messages = this.applyCaching(messages);
        messages = messages.filter(m => !!m);
        if (agentModeEnabled) {
            // Find all assistant messages with MO_FILE tags
            const assistantMessagesWithMOFILE = messages.map((message, index) => {
                const content: string = (message.content?.[0] as TextPart)?.text || message.content as string;
                return {
                    index,
                    hasMOFILE: message.role === "assistant" && content.includes("MO_FILE")
                };
            }).filter(item => item.hasMOFILE);
...

    /**
     * Filter core messages to optimize context window usage
     * - Keeps all user messages
     * - Keeps assistant messages without tool calls
     * - For assistant messages with tool calls, keeps the last 4 and removes tool-call content
     * - Removes all tool messages
     */
    public filterCoreMessages(messages: CoreMessage[]): CoreMessage[] {
        // Find last 4 assistant messages that have tool-call content (increased from 2)
        const assistantMessagesWithTools = messages
            .filter(msg => msg.role === 'assistant' &&
                Array.isArray(msg.content) &&
                msg.content.some(c => c.type === 'tool-call'))
            .slice(-4); // Increased from 2 to 4 to provide more context
...

    `;
                    }).join('')}${agentModeEnabled ? this.extractor.generateMinimalDependencyGraph(files) : ''}
Answer the user's question only to be able write code and nothing else.
                    `;

        const messageContent: TextPart = {
            type: "text",
            text: textContent
        };

        // // Only add cache_control if this is a base version
        // if (useCache) {
        //     messageContent.cache_control = { type: "ephemeral" };
        // }

        this.fileMessage = {
            role: "system",
            content: textContent
        };
    }

    /**
     * Enhance user message with additional context
     * - Adds Supabase prompt if available
     * - Can be extended to add other context as needed
     */
    public enhanceUserMessage(supabasePrompt?: string) {
        if (!supabasePrompt || !this.userMessage) {
            return;
        }
        this.userMessage = this.appendToUserMessage(this.userMessage, `\n${supabasePrompt}`);

    }
...
Path: src/lib/db/backend-queries.ts
...

    // Combine all backend files into a single code string
    // Each file will be wrapped in a module-like structure
    const combinedCode = backendFiles.map((file: FileItem) => {
      return `
// File: ${file.name}
// Language: ${file.language}
${file.content}
`;
    }).join('\n\n');

    return {
      code: combinedCode,
      files: backendFiles,
      fileStateId
    };
  } catch (error) {
    console.error('Failed to get backend code from file state:', error);
    throw error;
  }
}

/**
 * Get the backend code for a project
 * This combines getting the latest file state and extracting the backend code
 */
export async function getBackendCodeForProject(projectId: string) {
  try {
    const latestFileState = await getLatestFileStateForProject(projectId);

    if (!latestFileState) {
      return null;
    }

    return getBackendCodeFromFileState(latestFileState.id);
  } catch (error) {
    console.error('Failed to get backend code for project:', error);
    throw error;
  }
}
...
```

This is the exact format of the output I received from the `codebase-retrieval` tool. Note how it provides relevant code snippets from multiple files, with each snippet showing only the portions of code that are relevant to the query about the context engine or queryCodebase tool. The tool automatically identifies and extracts the most relevant sections from each file, rather than returning entire file contents.

## 2. Sample Output from `ContextEngine`

Based on the code review, the `ContextEngine.query()` method would return a `ContextResult` object with this structure:

```json
{
  "query": "How do I implement user authentication?",
  "relevantFiles": [
    {
      "name": "src/components/auth/LoginForm.tsx",
      "content": "// Full file content here, potentially hundreds of lines of code...",
      "metadata": {
        "type": "component",
        "exports": ["LoginForm", "LoginFormProps"],
        "imports": ["React", "useState", "useAuth", "Button", "Input"],
        "lineCount": 150,
        "hasJSX": true,
        "hasHooks": true,
        "hasContext": false,
        "hasStyles": true
      },
      "dependencies": ["src/hooks/useAuth.ts", "src/components/ui/Button.tsx"],
      "dependents": ["src/screens/LoginScreen.tsx"]
    },
    // Potentially 9 more complete files with similar structure...
  ],
  "codebaseStructure": {
    "components": ["src/components/auth/LoginForm.tsx", "src/components/ui/Button.tsx", /* ... */],
    "screens": ["src/screens/LoginScreen.tsx", "src/screens/RegisterScreen.tsx", /* ... */],
    "contexts": ["src/contexts/AuthContext.tsx", /* ... */],
    "hooks": ["src/hooks/useAuth.ts", /* ... */],
    "utils": ["src/utils/auth.ts", /* ... */],
    "types": ["src/types/auth.ts", /* ... */],
    "configs": ["src/config/auth.ts", /* ... */]
  },
  "relationships": {
    "imports": {
      "src/components/auth/LoginForm.tsx": ["React", "useState", "useAuth", "Button", "Input", /* ... */],
      // More import relationships for each file...
    },
    "exports": {
      "src/components/auth/LoginForm.tsx": ["LoginForm", "LoginFormProps"],
      // More export relationships for each file...
    },
    "dependencies": {
      "src/components/auth/LoginForm.tsx": ["src/hooks/useAuth.ts", "src/components/ui/Button.tsx"],
      // More dependency relationships for each file...
    },
    "dependents": {
      "src/components/auth/LoginForm.tsx": ["src/screens/LoginScreen.tsx"],
      // More dependent relationships for each file...
    }
  },
  "supabaseSchema": {
    "tables": [
      {
        "table_name": "users",
        "columns": [
          {"column_name": "id", "data_type": "uuid"},
          {"column_name": "email", "data_type": "varchar"},
          {"column_name": "password", "data_type": "varchar"},
          // More columns...
        ]
      },
      // More tables...
    ],
    "functions": [/* ... */],
    "secrets": [/* ... */],
    "dbFunctions": [/* ... */],
    "triggers": [/* ... */],
    "rlsPolicies": [/* ... */],
    "storageBuckets": [/* ... */]
  }
}
```

## 3. Key Differences

| Feature | Codebase Retrieval Tool | Context Engine |
|---------|-------------------------|----------------|
| **Content Scope** | ✅ Relevant code snippets only | ❌ Complete file contents (up to 10 files) |
| **Information Density** | ✅ High (focused on query) | ❌ Low (includes everything) |
| **Context Window Usage** | ✅ Efficient | ❌ Potentially overwhelming |
| **Metadata** | ✅ Minimal (file path only) | ❌ Extensive (type, exports, imports, etc.) |
| **Structure Information** | ❌ Not included | ✅ Complete codebase structure |
| **Relationship Information** | ❌ Not explicitly included | ✅ Detailed imports, exports, dependencies |
| **Database Schema** | ❌ Not included | ✅ Complete Supabase schema if available |
| **Query Specificity** | ✅ Directly answers the query | ❌ Provides broad context that must be parsed |
| **Progressive Loading** | ✅ Can make multiple targeted queries | ❌ Returns everything at once |

## 4. Efficiency Analysis

### Codebase Retrieval Tool
- **Token Efficiency**: Very high - only includes directly relevant code
- **Information Relevance**: High - focused on answering the specific query
- **Processing Overhead**: Low - LLM can immediately use the information
- **Follow-up Capability**: Good - can make additional targeted queries

### Context Engine
- **Token Efficiency**: Very low - includes complete files and extensive metadata
- **Information Relevance**: Mixed - includes relevant information but buried in noise
- **Processing Overhead**: High - LLM must parse and understand large amounts of data
- **Follow-up Capability**: Limited - already provided everything, no progressive loading

## 5. Recommendations for Context Engine Improvement

1. **Snippet-based Retrieval**: Return relevant code snippets rather than entire files
2. **Semantic Search**: Use semantic understanding to find the most relevant code sections
3. **Progressive Loading**: Implement a tiered approach with high-level information first
4. **Summarization**: Provide code summaries instead of raw content
5. **Query-specific Responses**: Tailor the response format based on the query type
6. **Relevance Ranking**: Rank and filter information by relevance to the query
7. **Contextual Formatting**: Format information to maximize LLM understanding

By adopting these improvements, the Context Engine could provide more focused, relevant information while using the context window more efficiently, similar to how the codebase-retrieval tool operates.
