const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

async function analyzeTokenPricing() {
  console.log('Analyzing token-based pricing models...');
  
  // Path to the CSV file
  const csvFilePath = path.resolve(__dirname, 'cost.csv');
  
  // Store entries from the last 7 days
  const entries = [];
  const startDate = new Date('2025-05-19');
  
  // Read the CSV file
  await new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        if (row.created_at) {
          const rowDate = new Date(row.created_at);
          if (rowDate >= startDate) {
            entries.push(row);
          }
        }
      })
      .on('end', () => {
        resolve();
      })
      .on('error', (err) => {
        reject(err);
      });
  });
  
  console.log(`Total entries loaded for pricing analysis: ${entries.length}`);
  
  // Filter for Claude Sonnet 4 entries
  const sonnetEntries = entries.filter(entry => entry.model_permaslug === 'anthropic/claude-4-sonnet-20250522');
  console.log(`Claude Sonnet 4 entries: ${sonnetEntries.length}`);
  
  // Group entries into conversation chains
  const conversationChains = [];
  let currentChain = [];
  let previousTokens = 0;
  
  // Sort entries by creation time
  sonnetEntries.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  for (let i = 0; i < sonnetEntries.length; i++) {
    const entry = sonnetEntries[i];
    const promptTokens = parseInt(entry.tokens_prompt || 0);
    
    // Start a new chain if:
    // 1. This is the first entry
    // 2. Current chain already has 10 entries
    // 3. Prompt tokens decreased (indicating a new conversation)
    // 4. Previous entry had a finish reason of "stop" (conversation ended)
    // 5. Time gap > 5 minutes (300000ms)
    const timeGap = i > 0 ? 
      new Date(entry.created_at) - new Date(sonnetEntries[i-1].created_at) : 0;
    
    if (
      currentChain.length === 0 ||
      currentChain.length >= 10 ||
      promptTokens < previousTokens ||
      (currentChain.length > 0 && 
       (currentChain[currentChain.length - 1].finish_reason_normalized === 'stop' ||
        timeGap > 300000))
    ) {
      if (currentChain.length > 0) {
        conversationChains.push([...currentChain]);
      }
      currentChain = [entry];
    } else {
      currentChain.push(entry);
    }
    
    previousTokens = promptTokens;
  }
  
  // Add the last chain if it exists
  if (currentChain.length > 0) {
    conversationChains.push(currentChain);
  }
  
  console.log(`Identified ${conversationChains.length} conversation chains`);
  
  // Analyze caching opportunities and token usage
  const cachingAnalysis = {
    totalEntries: sonnetEntries.length,
    totalConversations: conversationChains.length,
    totalCost: sonnetEntries.reduce((sum, entry) => sum + parseFloat(entry.cost_total || 0), 0),
    totalPromptTokens: sonnetEntries.reduce((sum, entry) => sum + parseInt(entry.tokens_prompt || 0), 0),
    totalCompletionTokens: sonnetEntries.reduce((sum, entry) => sum + parseInt(entry.tokens_completion || 0), 0),
    totalTokens: 0,
    cacheBenefits: {
      cachingRate: 0,
      potentialSavings: 0,
      optimizedTokens: 0
    },
    userSegments: []
  };
  
  cachingAnalysis.totalTokens = cachingAnalysis.totalPromptTokens + cachingAnalysis.totalCompletionTokens;
  
  // Calculate caching benefits
  let cachablePromptTokens = 0;
  let cachableCompletionTokens = 0;
  
  conversationChains.forEach(chain => {
    if (chain.length > 1) {
      // For each chain with multiple entries, calculate potential cache benefits
      for (let i = 1; i < chain.length; i++) {
        const entry = chain[i];
        const prevEntry = chain[i-1];
        
        const promptTokens = parseInt(entry.tokens_prompt || 0);
        const prevPromptTokens = parseInt(prevEntry.tokens_prompt || 0);
        
        // If prompt tokens increased, the difference could have been cached
        if (promptTokens > prevPromptTokens) {
          cachablePromptTokens += prevPromptTokens;
        }
      }
    }
  });
  
  // Estimate caching rate and potential savings
  cachingAnalysis.cacheBenefits.cachingRate = cachablePromptTokens / cachingAnalysis.totalPromptTokens;
  cachingAnalysis.cacheBenefits.potentialSavings = (cachablePromptTokens / cachingAnalysis.totalTokens) * cachingAnalysis.totalCost;
  cachingAnalysis.cacheBenefits.optimizedTokens = cachingAnalysis.totalTokens - cachablePromptTokens;
  
  // Define user segments based on token usage
  const conversationTokens = conversationChains.map(chain => {
    const promptTokens = chain.reduce((sum, entry) => sum + parseInt(entry.tokens_prompt || 0), 0);
    const completionTokens = chain.reduce((sum, entry) => sum + parseInt(entry.tokens_completion || 0), 0);
    return promptTokens + completionTokens;
  });
  
  // Sort token usage
  conversationTokens.sort((a, b) => a - b);
  
  // Define percentiles
  const percentiles = [10, 25, 50, 75, 90, 95, 99];
  const tokenPercentiles = {};
  
  percentiles.forEach(p => {
    const index = Math.floor((p / 100) * conversationTokens.length);
    tokenPercentiles[`p${p}`] = conversationTokens[index];
  });
  
  // Define user segments
  cachingAnalysis.userSegments = [
    {
      name: "Light Users",
      tokenRange: [0, tokenPercentiles.p25],
      avgTokens: conversationTokens.filter(t => t <= tokenPercentiles.p25).reduce((sum, t) => sum + t, 0) / 
                 conversationTokens.filter(t => t <= tokenPercentiles.p25).length,
      percentOfUsers: 25
    },
    {
      name: "Moderate Users",
      tokenRange: [tokenPercentiles.p25, tokenPercentiles.p75],
      avgTokens: conversationTokens.filter(t => t > tokenPercentiles.p25 && t <= tokenPercentiles.p75).reduce((sum, t) => sum + t, 0) / 
                 conversationTokens.filter(t => t > tokenPercentiles.p25 && t <= tokenPercentiles.p75).length,
      percentOfUsers: 50
    },
    {
      name: "Heavy Users",
      tokenRange: [tokenPercentiles.p75, tokenPercentiles.p95],
      avgTokens: conversationTokens.filter(t => t > tokenPercentiles.p75 && t <= tokenPercentiles.p95).reduce((sum, t) => sum + t, 0) / 
                 conversationTokens.filter(t => t > tokenPercentiles.p75 && t <= tokenPercentiles.p95).length,
      percentOfUsers: 20
    },
    {
      name: "Power Users",
      tokenRange: [tokenPercentiles.p95, Infinity],
      avgTokens: conversationTokens.filter(t => t > tokenPercentiles.p95).reduce((sum, t) => sum + t, 0) / 
                 conversationTokens.filter(t => t > tokenPercentiles.p95).length,
      percentOfUsers: 5
    }
  ];
  
  // Define pricing models
  const pricingModels = [
    {
      name: "Token-Based Pricing",
      description: "$20 for 10 million tokens",
      baseRate: 20 / 10000000, // $20 per 10M tokens
      tiers: [
        { name: "Starter", tokens: 1000000, price: 2.00 },
        { name: "Basic", tokens: 5000000, price: 10.00 },
        { name: "Pro", tokens: 10000000, price: 20.00 },
        { name: "Business", tokens: 50000000, price: 90.00 },
        { name: "Enterprise", tokens: 100000000, price: 170.00 }
      ]
    },
    {
      name: "Credit-Based System",
      description: "Credits for different operations",
      basePrice: 0.08, // per credit
      operations: [
        { name: "Simple Query", credits: 1, avgTokens: tokenPercentiles.p10 },
        { name: "Standard Message", credits: 3, avgTokens: tokenPercentiles.p50 },
        { name: "Complex Query", credits: 5, avgTokens: tokenPercentiles.p75 },
        { name: "Code Generation", credits: 10, avgTokens: tokenPercentiles.p90 }
      ],
      packages: [
        { name: "Starter", credits: 25, price: 1.80, savings: "10%" },
        { name: "Basic", credits: 100, price: 7.00, savings: "12.5%" },
        { name: "Pro", credits: 250, price: 16.00, savings: "20%" },
        { name: "Business", credits: 1000, price: 60.00, savings: "25%" },
        { name: "Enterprise", credits: 5000, price: 280.00, savings: "30%" }
      ]
    },
    {
      name: "Hybrid Model",
      description: "Base subscription + token usage",
      tiers: [
        { 
          name: "Starter", 
          basePrice: 5.00,
          includedTokens: 2500000,
          overageRate: 0.0000025, // $2.50 per million tokens
          estimatedMonthlyCost: {}
        },
        { 
          name: "Pro", 
          basePrice: 20.00,
          includedTokens: 12000000,
          overageRate: 0.0000020, // $2.00 per million tokens
          estimatedMonthlyCost: {}
        },
        { 
          name: "Business", 
          basePrice: 50.00,
          includedTokens: 35000000,
          overageRate: 0.0000015, // $1.50 per million tokens
          estimatedMonthlyCost: {}
        }
      ]
    }
  ];
  
  // Calculate monthly costs for each user segment under each pricing model
  cachingAnalysis.userSegments.forEach(segment => {
    // Calculate monthly token usage (assuming 20 conversations per month)
    const monthlyTokens = segment.avgTokens * 20;
    const optimizedMonthlyTokens = monthlyTokens * (1 - cachingAnalysis.cacheBenefits.cachingRate);
    
    segment.monthlyUsage = {
      conversations: 20,
      rawTokens: monthlyTokens,
      optimizedTokens: optimizedMonthlyTokens
    };
    
    // Token-based pricing
    segment.tokenBasedCost = optimizedMonthlyTokens * pricingModels[0].baseRate;
    
    // Credit-based pricing
    // Find the appropriate operation based on token usage
    let operation;
    if (segment.avgTokens <= tokenPercentiles.p10) {
      operation = pricingModels[1].operations[0];
    } else if (segment.avgTokens <= tokenPercentiles.p50) {
      operation = pricingModels[1].operations[1];
    } else if (segment.avgTokens <= tokenPercentiles.p75) {
      operation = pricingModels[1].operations[2];
    } else {
      operation = pricingModels[1].operations[3];
    }
    
    segment.creditBasedCost = operation.credits * pricingModels[1].basePrice * 20;
    segment.creditsPerMonth = operation.credits * 20;
    
    // Hybrid model
    pricingModels[2].tiers.forEach(tier => {
      if (!tier.estimatedMonthlyCost[segment.name]) {
        tier.estimatedMonthlyCost[segment.name] = 0;
      }
      
      if (optimizedMonthlyTokens <= tier.includedTokens) {
        tier.estimatedMonthlyCost[segment.name] = tier.basePrice;
      } else {
        const overageTokens = optimizedMonthlyTokens - tier.includedTokens;
        tier.estimatedMonthlyCost[segment.name] = tier.basePrice + (overageTokens * tier.overageRate);
      }
    });
  });
  
  // Calculate break-even analysis
  const breakEvenAnalysis = {
    currentCostPerToken: cachingAnalysis.totalCost / cachingAnalysis.totalTokens,
    optimizedCostPerToken: cachingAnalysis.totalCost / cachingAnalysis.cacheBenefits.optimizedTokens,
    tokenPriceForBreakEven: 0,
    creditPriceForBreakEven: 0,
    hybridModelBreakEven: {}
  };
  
  // Calculate token price for break-even
  breakEvenAnalysis.tokenPriceForBreakEven = breakEvenAnalysis.optimizedCostPerToken * 1.2; // 20% margin
  
  // Calculate credit price for break-even
  const avgCreditsPerConversation = 
    (pricingModels[1].operations[0].credits * 0.25) + // 25% simple queries
    (pricingModels[1].operations[1].credits * 0.5) +  // 50% standard messages
    (pricingModels[1].operations[2].credits * 0.2) +  // 20% complex queries
    (pricingModels[1].operations[3].credits * 0.05);  // 5% code generation
  
  breakEvenAnalysis.creditPriceForBreakEven = 
    (cachingAnalysis.totalCost / cachingAnalysis.totalConversations * 1.2) / avgCreditsPerConversation;
  
  // Print analysis results
  console.log('\n===== TOKEN USAGE AND CACHING ANALYSIS =====');
  console.log(`Total cost: $${cachingAnalysis.totalCost.toFixed(2)}`);
  console.log(`Total tokens: ${cachingAnalysis.totalTokens.toLocaleString()}`);
  console.log(`Total conversations: ${cachingAnalysis.totalConversations}`);
  console.log(`Average cost per conversation: $${(cachingAnalysis.totalCost / cachingAnalysis.totalConversations).toFixed(4)}`);
  console.log(`Average tokens per conversation: ${Math.round(cachingAnalysis.totalTokens / cachingAnalysis.totalConversations).toLocaleString()}`);
  
  console.log('\nCaching Benefits:');
  console.log(`Potential caching rate: ${(cachingAnalysis.cacheBenefits.cachingRate * 100).toFixed(2)}%`);
  console.log(`Potential cost savings: $${cachingAnalysis.cacheBenefits.potentialSavings.toFixed(2)}`);
  console.log(`Optimized token count: ${cachingAnalysis.cacheBenefits.optimizedTokens.toLocaleString()}`);
  
  console.log('\n===== USER SEGMENTS =====');
  cachingAnalysis.userSegments.forEach(segment => {
    console.log(`\n${segment.name} (${segment.percentOfUsers}% of users):`);
    console.log(`Token usage range: ${segment.tokenRange[0].toLocaleString()} - ${segment.tokenRange[1] === Infinity ? '∞' : segment.tokenRange[1].toLocaleString()}`);
    console.log(`Average tokens per conversation: ${Math.round(segment.avgTokens).toLocaleString()}`);
    console.log(`Monthly usage (20 conversations):`);
    console.log(`  Raw tokens: ${Math.round(segment.monthlyUsage.rawTokens).toLocaleString()}`);
    console.log(`  Optimized tokens: ${Math.round(segment.monthlyUsage.optimizedTokens).toLocaleString()}`);
  });
  
  console.log('\n===== PRICING MODELS =====');
  
  // Token-based pricing
  console.log(`\n1. ${pricingModels[0].name}`);
  console.log(`Description: ${pricingModels[0].description}`);
  console.log(`Base rate: $${(pricingModels[0].baseRate * 1000000).toFixed(4)} per million tokens`);
  console.log('\nTier options:');
  pricingModels[0].tiers.forEach(tier => {
    console.log(`  ${tier.name}: ${(tier.tokens / 1000000).toFixed(1)}M tokens for $${tier.price.toFixed(2)} (${(tier.price / (tier.tokens / 1000000)).toFixed(2)}/million tokens)`);
  });
  
  console.log('\nEstimated monthly cost by user segment:');
  cachingAnalysis.userSegments.forEach(segment => {
    console.log(`  ${segment.name}: $${segment.tokenBasedCost.toFixed(2)}`);
  });
  
  // Credit-based pricing
  console.log(`\n2. ${pricingModels[1].name}`);
  console.log(`Description: ${pricingModels[1].description}`);
  console.log(`Base credit price: $${pricingModels[1].basePrice.toFixed(2)} per credit`);
  
  console.log('\nOperation costs:');
  pricingModels[1].operations.forEach(op => {
    console.log(`  ${op.name}: ${op.credits} credits ($${(op.credits * pricingModels[1].basePrice).toFixed(2)})`);
  });
  
  console.log('\nCredit packages:');
  pricingModels[1].packages.forEach(pkg => {
    console.log(`  ${pkg.name}: ${pkg.credits} credits for $${pkg.price.toFixed(2)} (${pkg.savings} savings)`);
  });
  
  console.log('\nEstimated monthly cost by user segment:');
  cachingAnalysis.userSegments.forEach(segment => {
    console.log(`  ${segment.name}: $${segment.creditBasedCost.toFixed(2)} (${segment.creditsPerMonth} credits)`);
  });
  
  // Hybrid model
  console.log(`\n3. ${pricingModels[2].name}`);
  console.log(`Description: ${pricingModels[2].description}`);
  
  console.log('\nTier options:');
  pricingModels[2].tiers.forEach(tier => {
    console.log(`  ${tier.name}: $${tier.basePrice.toFixed(2)}/month, includes ${(tier.includedTokens / 1000000).toFixed(1)}M tokens`);
    console.log(`    Overage: $${(tier.overageRate * 1000000).toFixed(2)} per million tokens`);
  });
  
  console.log('\nEstimated monthly cost by user segment:');
  cachingAnalysis.userSegments.forEach(segment => {
    console.log(`  ${segment.name}:`);
    pricingModels[2].tiers.forEach(tier => {
      console.log(`    ${tier.name}: $${tier.estimatedMonthlyCost[segment.name].toFixed(2)}`);
    });
  });
  
  console.log('\n===== BREAK-EVEN ANALYSIS =====');
  console.log(`Current cost per token: $${breakEvenAnalysis.currentCostPerToken.toFixed(8)}`);
  console.log(`Optimized cost per token: $${breakEvenAnalysis.optimizedCostPerToken.toFixed(8)}`);
  console.log(`Token price for break-even (with 20% margin): $${breakEvenAnalysis.tokenPriceForBreakEven.toFixed(8)} per token`);
  console.log(`  Equivalent to: $${(breakEvenAnalysis.tokenPriceForBreakEven * 1000000).toFixed(2)} per million tokens`);
  console.log(`Credit price for break-even (with 20% margin): $${breakEvenAnalysis.creditPriceForBreakEven.toFixed(2)} per credit`);
  
  console.log('\n===== RECOMMENDED PRICING STRUCTURE =====');
  
  // Calculate the recommended pricing based on break-even analysis
  const recommendedTokenPrice = Math.ceil(breakEvenAnalysis.tokenPriceForBreakEven * 100000000) / 100000000;
  const recommendedCreditPrice = Math.ceil(breakEvenAnalysis.creditPriceForBreakEven * 20) / 20;
  
  console.log('Token-Based Pricing:');
  console.log(`  Recommended rate: $${(recommendedTokenPrice * 1000000).toFixed(2)} per million tokens`);
  console.log(`  Tier structure:`);
  console.log(`    Starter: 1M tokens for $${(recommendedTokenPrice * 1000000).toFixed(2)}`);
  console.log(`    Basic: 5M tokens for $${(recommendedTokenPrice * 5000000).toFixed(2)}`);
  console.log(`    Pro: 10M tokens for $${(recommendedTokenPrice * 10000000).toFixed(2)}`);
  console.log(`    Business: 50M tokens for $${(recommendedTokenPrice * 50000000 * 0.9).toFixed(2)} (10% volume discount)`);
  console.log(`    Enterprise: 100M tokens for $${(recommendedTokenPrice * 100000000 * 0.8).toFixed(2)} (20% volume discount)`);
  
  console.log('\nCredit-Based Pricing:');
  console.log(`  Recommended credit price: $${recommendedCreditPrice.toFixed(2)} per credit`);
  console.log(`  Operation costs:`);
  console.log(`    Simple Query: 1 credit ($${(1 * recommendedCreditPrice).toFixed(2)})`);
  console.log(`    Standard Message: 3 credits ($${(3 * recommendedCreditPrice).toFixed(2)})`);
  console.log(`    Complex Query: 5 credits ($${(5 * recommendedCreditPrice).toFixed(2)})`);
  console.log(`    Code Generation: 10 credits ($${(10 * recommendedCreditPrice).toFixed(2)})`);
  
  console.log('\nHybrid Pricing:');
  console.log(`  Starter: $10/month, includes 2.5M tokens, overage at $${(recommendedTokenPrice * 1000000 * 1.1).toFixed(2)}/million`);
  console.log(`  Pro: $30/month, includes 12M tokens, overage at $${(recommendedTokenPrice * 1000000).toFixed(2)}/million`);
  console.log(`  Business: $75/month, includes 40M tokens, overage at $${(recommendedTokenPrice * 1000000 * 0.9).toFixed(2)}/million`);
  
  return {
    cachingAnalysis,
    pricingModels,
    breakEvenAnalysis
  };
}

// Run the analysis
analyzeTokenPricing().catch(console.error);
