const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

async function analyzePricingModels() {
  console.log('Analyzing alternative pricing models...');
  
  // Path to the CSV file
  const csvFilePath = path.resolve(__dirname, 'cost.csv');
  
  // Store entries from the last 7 days
  const entries = [];
  const startDate = new Date('2025-05-19');
  
  // Read the CSV file
  await new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        if (row.created_at) {
          const rowDate = new Date(row.created_at);
          if (rowDate >= startDate) {
            entries.push(row);
          }
        }
      })
      .on('end', () => {
        resolve();
      })
      .on('error', (err) => {
        reject(err);
      });
  });
  
  console.log(`Total entries loaded for pricing analysis: ${entries.length}`);
  
  // Filter for Claude Sonnet 4 entries
  const sonnetEntries = entries.filter(entry => entry.model_permaslug === 'anthropic/claude-4-sonnet-20250522');
  console.log(`Claude Sonnet 4 entries: ${sonnetEntries.length}`);
  
  // Group entries by day
  const entriesByDay = {};
  sonnetEntries.forEach(entry => {
    const date = entry.created_at.split(' ')[0];
    if (!entriesByDay[date]) {
      entriesByDay[date] = [];
    }
    entriesByDay[date].push(entry);
  });
  
  // Group entries into conversation chains
  const conversationChains = [];
  let currentChain = [];
  let previousTokens = 0;
  
  // Sort entries by creation time
  sonnetEntries.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  for (let i = 0; i < sonnetEntries.length; i++) {
    const entry = sonnetEntries[i];
    const promptTokens = parseInt(entry.tokens_prompt || 0);
    
    // Start a new chain if:
    // 1. This is the first entry
    // 2. Current chain already has 10 entries
    // 3. Prompt tokens decreased (indicating a new conversation)
    // 4. Previous entry had a finish reason of "stop" (conversation ended)
    // 5. Time gap > 5 minutes (300000ms)
    const timeGap = i > 0 ? 
      new Date(entry.created_at) - new Date(sonnetEntries[i-1].created_at) : 0;
    
    if (
      currentChain.length === 0 ||
      currentChain.length >= 10 ||
      promptTokens < previousTokens ||
      (currentChain.length > 0 && 
       (currentChain[currentChain.length - 1].finish_reason_normalized === 'stop' ||
        timeGap > 300000))
    ) {
      if (currentChain.length > 0) {
        conversationChains.push([...currentChain]);
      }
      currentChain = [entry];
    } else {
      currentChain.push(entry);
    }
    
    previousTokens = promptTokens;
  }
  
  // Add the last chain if it exists
  if (currentChain.length > 0) {
    conversationChains.push(currentChain);
  }
  
  console.log(`Identified ${conversationChains.length} conversation chains`);
  
  // Calculate metrics for each conversation
  const conversationMetrics = conversationChains.map(chain => {
    const firstEntry = chain[0];
    const lastEntry = chain[chain.length - 1];
    const date = firstEntry.created_at.split(' ')[0];
    
    // Calculate total tokens and costs
    let totalCost = 0;
    let totalPromptTokens = 0;
    let totalCompletionTokens = 0;
    let totalReasoningTokens = 0;
    
    chain.forEach(entry => {
      totalCost += parseFloat(entry.cost_total || 0);
      totalPromptTokens += parseInt(entry.tokens_prompt || 0);
      totalCompletionTokens += parseInt(entry.tokens_completion || 0);
      totalReasoningTokens += parseInt(entry.tokens_reasoning || 0);
    });
    
    // Calculate net new tokens (estimate)
    // This is a simplification - in reality, we'd need to analyze the actual content
    let netNewPromptTokens = 0;
    let previousPromptTokens = 0;
    
    chain.forEach((entry, index) => {
      const promptTokens = parseInt(entry.tokens_prompt || 0);
      if (index === 0) {
        netNewPromptTokens += promptTokens;
      } else {
        // Only count the increase in tokens as "new"
        netNewPromptTokens += Math.max(0, promptTokens - previousPromptTokens);
      }
      previousPromptTokens = promptTokens;
    });
    
    // Calculate final output tokens (from the last message)
    const finalOutputTokens = parseInt(lastEntry.tokens_completion || 0);
    
    // Calculate total tokens
    const totalTokens = totalPromptTokens + totalCompletionTokens + totalReasoningTokens;
    
    return {
      date,
      chainLength: chain.length,
      totalCost,
      totalTokens,
      totalPromptTokens,
      totalCompletionTokens,
      totalReasoningTokens,
      netNewPromptTokens,
      finalOutputTokens,
      costPerToken: totalCost / totalTokens,
      costPerPromptToken: totalCost / totalPromptTokens,
      costPerCompletionToken: totalCompletionTokens > 0 ? totalCost / totalCompletionTokens : 0
    };
  });
  
  // Calculate overall statistics
  const overallStats = {
    totalConversations: conversationMetrics.length,
    totalCost: conversationMetrics.reduce((sum, metrics) => sum + metrics.totalCost, 0),
    totalTokens: conversationMetrics.reduce((sum, metrics) => sum + metrics.totalTokens, 0),
    totalPromptTokens: conversationMetrics.reduce((sum, metrics) => sum + metrics.totalPromptTokens, 0),
    totalCompletionTokens: conversationMetrics.reduce((sum, metrics) => sum + metrics.totalCompletionTokens, 0),
    totalReasoningTokens: conversationMetrics.reduce((sum, metrics) => sum + metrics.totalReasoningTokens, 0),
    totalNetNewPromptTokens: conversationMetrics.reduce((sum, metrics) => sum + metrics.netNewPromptTokens, 0),
    totalFinalOutputTokens: conversationMetrics.reduce((sum, metrics) => sum + metrics.finalOutputTokens, 0),
    avgCostPerConversation: 0,
    avgTokensPerConversation: 0,
    avgPromptTokensPerConversation: 0,
    avgCompletionTokensPerConversation: 0,
    avgNetNewPromptTokensPerConversation: 0,
    avgFinalOutputTokensPerConversation: 0
  };
  
  // Calculate averages
  if (conversationMetrics.length > 0) {
    overallStats.avgCostPerConversation = overallStats.totalCost / overallStats.totalConversations;
    overallStats.avgTokensPerConversation = overallStats.totalTokens / overallStats.totalConversations;
    overallStats.avgPromptTokensPerConversation = overallStats.totalPromptTokens / overallStats.totalConversations;
    overallStats.avgCompletionTokensPerConversation = overallStats.totalCompletionTokens / overallStats.totalConversations;
    overallStats.avgNetNewPromptTokensPerConversation = overallStats.totalNetNewPromptTokens / overallStats.totalConversations;
    overallStats.avgFinalOutputTokensPerConversation = overallStats.totalFinalOutputTokens / overallStats.totalConversations;
  }
  
  // Calculate percentiles for key metrics
  const calculatePercentiles = (values, percentiles = [10, 25, 50, 75, 90, 95, 99]) => {
    const sorted = [...values].sort((a, b) => a - b);
    const result = {};
    
    percentiles.forEach(p => {
      const index = Math.floor((p / 100) * sorted.length);
      result[`p${p}`] = sorted[index];
    });
    
    return result;
  };
  
  const costPercentiles = calculatePercentiles(conversationMetrics.map(m => m.totalCost));
  const promptTokenPercentiles = calculatePercentiles(conversationMetrics.map(m => m.totalPromptTokens));
  const completionTokenPercentiles = calculatePercentiles(conversationMetrics.map(m => m.totalCompletionTokens));
  const netNewPromptTokenPercentiles = calculatePercentiles(conversationMetrics.map(m => m.netNewPromptTokens));
  const finalOutputTokenPercentiles = calculatePercentiles(conversationMetrics.map(m => m.finalOutputTokens));
  
  // Simulate different pricing models
  const pricingModels = [
    {
      name: "Per Message (Current)",
      description: "Flat fee per user message",
      basePrice: 0.20,
      simulatedRevenue: overallStats.totalConversations * 0.20,
      breakEvenPrice: overallStats.totalCost / overallStats.totalConversations,
      simplicity: 5, // 1-5 scale, 5 being simplest
      transparency: 3,
      predictability: 5,
      fairness: 2,
      marginalCostAlignment: 1
    },
    {
      name: "Output Token Pricing",
      description: "Price based on completion tokens (what the user sees)",
      basePrice: 0.001, // per token
      simulatedRevenue: overallStats.totalCompletionTokens * 0.001,
      breakEvenPrice: overallStats.totalCost / overallStats.totalCompletionTokens,
      simplicity: 3,
      transparency: 4,
      predictability: 2,
      fairness: 4,
      marginalCostAlignment: 3
    },
    {
      name: "Net New Input + Output",
      description: "Price based on new user input tokens + completion tokens",
      basePrice: 0.0005, // per token
      simulatedRevenue: (overallStats.totalNetNewPromptTokens + overallStats.totalCompletionTokens) * 0.0005,
      breakEvenPrice: overallStats.totalCost / (overallStats.totalNetNewPromptTokens + overallStats.totalCompletionTokens),
      simplicity: 2,
      transparency: 3,
      predictability: 2,
      fairness: 5,
      marginalCostAlignment: 5
    },
    {
      name: "Tiered Message Pricing",
      description: "Base price + surcharge for complex messages",
      basePrice: 0.15, // base price
      surcharge: 0.10, // surcharge for complex messages
      complexityThreshold: completionTokenPercentiles.p75, // 75th percentile of completion tokens
      simulatedRevenue: conversationMetrics.reduce((sum, m) => 
        sum + 0.15 + (m.totalCompletionTokens > completionTokenPercentiles.p75 ? 0.10 : 0), 0),
      breakEvenPrice: "Variable",
      simplicity: 4,
      transparency: 3,
      predictability: 3,
      fairness: 3,
      marginalCostAlignment: 3
    },
    {
      name: "Code Generation Pricing",
      description: "Base price per message + premium for code generation",
      basePrice: 0.10, // base price
      codePremium: 0.25, // premium for code generation
      simulatedRevenue: conversationMetrics.reduce((sum, m) => {
        // Assume messages with high completion tokens are code generation
        const isCodeGeneration = m.finalOutputTokens > finalOutputTokenPercentiles.p75;
        return sum + 0.10 + (isCodeGeneration ? 0.25 : 0);
      }, 0),
      breakEvenPrice: "Variable",
      simplicity: 4,
      transparency: 4,
      predictability: 3,
      fairness: 4,
      marginalCostAlignment: 4
    },
    {
      name: "Credit-Based System",
      description: "Users purchase credits, different operations cost different credits",
      basePrice: 0.05, // per credit
      creditsPerMessage: 3, // base credits per message
      creditsPerComplexMessage: 5, // credits for complex messages
      complexityThreshold: completionTokenPercentiles.p75,
      simulatedRevenue: conversationMetrics.reduce((sum, m) => {
        const isComplex = m.totalCompletionTokens > completionTokenPercentiles.p75;
        const credits = isComplex ? 5 : 3;
        return sum + (credits * 0.05);
      }, 0),
      breakEvenPrice: "Variable",
      simplicity: 3,
      transparency: 5,
      predictability: 4,
      fairness: 4,
      marginalCostAlignment: 4
    }
  ];
  
  // Calculate profit/loss for each model
  pricingModels.forEach(model => {
    model.profitLoss = model.simulatedRevenue - overallStats.totalCost;
    model.profitMargin = (model.profitLoss / model.simulatedRevenue) * 100;
    model.revenuePerConversation = model.simulatedRevenue / overallStats.totalConversations;
    
    // Calculate overall score (weighted average of metrics)
    model.overallScore = (
      (model.simplicity * 0.3) + 
      (model.transparency * 0.2) + 
      (model.predictability * 0.15) + 
      (model.fairness * 0.2) + 
      (model.marginalCostAlignment * 0.15)
    );
  });
  
  // Sort pricing models by overall score
  pricingModels.sort((a, b) => b.overallScore - a.overallScore);
  
  // Print overall statistics
  console.log('\n===== OVERALL CONVERSATION STATISTICS =====');
  console.log(`Total conversations: ${overallStats.totalConversations}`);
  console.log(`Total cost: $${overallStats.totalCost.toFixed(2)}`);
  console.log(`Average cost per conversation: $${overallStats.avgCostPerConversation.toFixed(4)}`);
  console.log(`Average tokens per conversation: ${Math.round(overallStats.avgTokensPerConversation).toLocaleString()}`);
  console.log(`  Prompt tokens: ${Math.round(overallStats.avgPromptTokensPerConversation).toLocaleString()}`);
  console.log(`  Completion tokens: ${Math.round(overallStats.avgCompletionTokensPerConversation).toLocaleString()}`);
  console.log(`  Net new prompt tokens: ${Math.round(overallStats.avgNetNewPromptTokensPerConversation).toLocaleString()}`);
  console.log(`  Final output tokens: ${Math.round(overallStats.avgFinalOutputTokensPerConversation).toLocaleString()}`);
  
  console.log('\n===== TOKEN PERCENTILES =====');
  console.log('Prompt Tokens:');
  Object.entries(promptTokenPercentiles).forEach(([p, v]) => {
    console.log(`  ${p}: ${Math.round(v).toLocaleString()}`);
  });
  
  console.log('Completion Tokens:');
  Object.entries(completionTokenPercentiles).forEach(([p, v]) => {
    console.log(`  ${p}: ${Math.round(v).toLocaleString()}`);
  });
  
  console.log('Net New Prompt Tokens:');
  Object.entries(netNewPromptTokenPercentiles).forEach(([p, v]) => {
    console.log(`  ${p}: ${Math.round(v).toLocaleString()}`);
  });
  
  console.log('Final Output Tokens:');
  Object.entries(finalOutputTokenPercentiles).forEach(([p, v]) => {
    console.log(`  ${p}: ${Math.round(v).toLocaleString()}`);
  });
  
  console.log('\n===== PRICING MODEL COMPARISON =====');
  pricingModels.forEach((model, index) => {
    console.log(`\n${index + 1}. ${model.name} (Score: ${model.overallScore.toFixed(2)})`);
    console.log(`   Description: ${model.description}`);
    console.log(`   Base Price: $${typeof model.basePrice === 'number' ? model.basePrice.toFixed(4) : model.basePrice}`);
    console.log(`   Simulated Revenue: $${model.simulatedRevenue.toFixed(2)}`);
    console.log(`   Break-Even Price: $${typeof model.breakEvenPrice === 'number' ? model.breakEvenPrice.toFixed(6) : model.breakEvenPrice}`);
    console.log(`   Revenue Per Conversation: $${model.revenuePerConversation.toFixed(4)}`);
    console.log(`   Profit/Loss: $${model.profitLoss.toFixed(2)}`);
    console.log(`   Profit Margin: ${model.profitMargin.toFixed(2)}%`);
    console.log(`   Metrics:`);
    console.log(`     Simplicity: ${model.simplicity}/5`);
    console.log(`     Transparency: ${model.transparency}/5`);
    console.log(`     Predictability: ${model.predictability}/5`);
    console.log(`     Fairness: ${model.fairness}/5`);
    console.log(`     Marginal Cost Alignment: ${model.marginalCostAlignment}/5`);
  });
  
  // Provide detailed recommendations for the top models
  console.log('\n===== PRICING RECOMMENDATIONS =====');
  
  // Find the model with the best balance of profitability and user-friendliness
  const topModels = pricingModels.filter(model => model.profitMargin > -10);
  
  if (topModels.length > 0) {
    const recommendedModel = topModels[0];
    console.log(`Recommended Primary Model: ${recommendedModel.name}`);
    console.log(`Rationale: Best balance of user-friendliness and financial sustainability`);
    
    // Calculate the adjusted price to reach break-even
    if (typeof recommendedModel.breakEvenPrice === 'number') {
      const adjustedPrice = recommendedModel.breakEvenPrice * 1.2; // 20% margin
      console.log(`Recommended Adjusted Price: $${adjustedPrice.toFixed(6)} (includes 20% margin)`);
    }
    
    // Provide implementation guidance
    console.log('\nImplementation Guidance:');
    switch (recommendedModel.name) {
      case "Per Message (Current)":
        console.log(`- Increase per-message price from $0.20 to $${recommendedModel.breakEvenPrice.toFixed(2)}`);
        console.log(`- Maintain simple pricing communication: "$${Math.ceil(recommendedModel.breakEvenPrice * 100) / 100} per message"`);
        break;
      case "Output Token Pricing":
        console.log(`- Price at $${(recommendedModel.breakEvenPrice * 1.2).toFixed(4)} per output token`);
        console.log(`- Communicate as: "Pay only for what you see - $${Math.ceil((recommendedModel.breakEvenPrice * 1.2) * 10000) / 10000} per word generated"`);
        console.log(`- Provide a token estimator: "Average message costs $${(recommendedModel.breakEvenPrice * overallStats.avgCompletionTokensPerConversation * 1.2).toFixed(2)}"`);
        break;
      case "Net New Input + Output":
        console.log(`- Price at $${(recommendedModel.breakEvenPrice * 1.2).toFixed(4)} per token (both input and output)`);
        console.log(`- Communicate as: "Fair usage pricing - pay only for new information processed"`);
        console.log(`- Provide examples: "Short question with detailed answer: ~$${(recommendedModel.breakEvenPrice * (netNewPromptTokenPercentiles.p25 + completionTokenPercentiles.p75) * 1.2).toFixed(2)}"`);
        break;
      case "Tiered Message Pricing":
        console.log(`- Base price: $0.15 per message`);
        console.log(`- Complex message surcharge: $0.10 (for ~25% of messages)`);
        console.log(`- Communicate as: "Simple pricing: $0.15 per message, with a small surcharge for complex requests"`);
        break;
      case "Code Generation Pricing":
        console.log(`- Base price: $0.10 per message`);
        console.log(`- Code generation premium: $0.25`);
        console.log(`- Communicate as: "Basic questions: $0.10, Code generation: $0.35"`);
        break;
      case "Credit-Based System":
        console.log(`- Credit price: $0.05 per credit`);
        console.log(`- Standard messages: 3 credits ($0.15)`);
        console.log(`- Complex messages: 5 credits ($0.25)`);
        console.log(`- Communicate as: "Simple credit system: Buy credits, spend them as you go"`);
        console.log(`- Offer volume discounts: "100 credits for $4.50 (10% off)"`);
        break;
    }
  } else {
    console.log("No pricing model achieves break-even without significant price increases.");
    console.log("Recommendation: Implement cost optimizations before adjusting pricing model.");
  }
  
  return {
    overallStats,
    pricingModels,
    conversationMetrics
  };
}

// Run the analysis
analyzePricingModels().catch(console.error);
