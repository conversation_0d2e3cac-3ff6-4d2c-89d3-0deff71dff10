ounders
Kumar <PERSON><PERSON><PERSON>
how_to_reg
Profile complete
Edit profile
arrow_forward
+ Add a co-founder
  Who writes code, or does other technical work on your product? Was any of it done by a non-founder? Please explain.
  I write all the code as the technical founder. Previously was the Head of Engineering at Liquide, shipping to 4M+ users.
  Built our AI pipeline for real-time stock market analysis and cross-platform UI generation and personalization engine. Currently shipping 3-4 features daily based on user feedback.
  Are you looking for a cofounder?
  Yes, and I am actively looking while building. I have got the product to 3 paying customers and ship improvements daily, handling everything from development to user support. A great cofounder would definitely help us move faster, but I am prioritizing finding the right long-term partner over a quick hire. My experience scaling products (Liquide to 4M+ users) gives me confidence we can grow either way.
  Founder Video
  Please record a one minute video introducing the founder(s).*
  Make sure the file does not exceed 100 MB. Read more about the video here.


Drop here or browse
Company
Company name*
magically
Describe what your company does in 50 characters or less.*
Replit for mobile apps
Company URL, if any
https://magically.life
If you have a demo, attach it below.
Anything that shows us how the product works. Please limit to 3 minutes / 100 MB.


Drop here or browse
Please provide a link to the product, if any.
https://magically.life
If login credentials are required for the link above, enter them here.

username / password
What is your company going to make? Please describe your product and what it does or will do.
Magically helps users build mobile apps by having a conversation, just like chatting with a friend. Instead of learning to code or hiring developers, a user can simply describe what they want and see it being built in real time.

Key features:
1) Natural conversations that turn ideas into working code
2) Instant preview of the app as it's being built without an emulator
3) Production-ready React Native code that works on both iOS and Android without extra work.

I am currently focused on helping indie developers and budding entrepreneurs quickly validate and build their app ideas while developing features that will eventually serve development teams and mobile development agencies. That's where the pain is most acute and higher willingness to pay.
Where do you live now, and where would the company be based after YC?
Use the format City A, Country A / City B, Country B

Bengaluru, India / San Francisco, USA
Explain your decision regarding location.
SF is the natural choice for us. It has the highest concentration of dev agencies and tech companies. They are exactly the customers we need to be close to as we scale. Being there also means direct access to people who have built successful dev tools companies, which is crucial for both advice and future fundraising.
Progress
How far along are you?
We launched our alpha 3 days ago and have been launching new features daily. From 1,200 website visitors, 508 users (42%) actually generated app screens. Most encouraging is user engagement: 14 people spent over 20 minutes building their apps, with 2 power users spending more than 2 hours.
Best validation: 3 users have already converted to paid subscribers despite being in alpha, showing a willingness to pay for the product this early.
How long have each of you been working on this? How much of that has been full-time? Please explain.
I have been working on this full-time for the past month, shipping the prototype and getting to 3 paying customers. The foundation comes from 2 years of building similar internal tools at my last company (Liquide), where I saw firsthand how much time teams waste on cross-platform development. After my previous startups, I have been careful about committing to ideas. But once I saw the opportunity here, I invested $5K of my savings and went all-in. This is now my full-time focus.
What tech stack are you using, or planning to use, to build this product?
Frontend: Next.js for the web interface, React Native for mobile preview, and a forked version of Expo services for real-time communication and cross-platform compliance.

Backend: Custom AI pipeline combining Claude Sonnect for code generation, proprietary evaluations for cross-platform compliance, and self-healing code correction to increase reliability.

Infrastructure: Vercel, Fly.io.

Key innovations:
1. The proprietary eval and self-correcting engine (Tiered architecture) to increase the reliability by over 50% over pure LLM output without the user ever knowing about errors.
2. Custom component library will reduce the LLM costs by over 30%.

Are people using your product?
Yes
No
How many active users or customers do you have? How many are paying? Who is paying you the most, and how much do they pay you?
We have 3 paying customers at $29/month each, all converted during our alpha.


Do you have revenue?
Yes
No
How much revenue?
Not cumulative and not GMV. If none, enter '0'

July 2024
USD$
August 2024
USD$
September 2024
USD$
October 2024
USD$
November 2024
USD$
December 2024
USD$
January 2025
USD$
Where does your revenue come from? If your revenue comes from multiple sources (e.g., multiple products, multiple companies or a mix of consulting and this product), please break down how much is coming from each source.
100% of our revenue comes from product subscriptions at $29/month per user. We're keeping the model simple while we learn from our early customers.
Anything else you would like us to know regarding your revenue or growth rate?
We launched 3 days ago and are focused on understanding what drives our power users to pay. Our early adopters (14 users spending >20 minutes) are giving us valuable insights into use cases and pricing. We are more focused on learning who our ideal customer is and what value they get from the product.

If you are applying with the same idea as a previous batch, did anything change? If you applied with a different idea, why did you pivot and what did you learn from the last idea?
My previous idea was outside my core expertise. This time, I'm solving a problem I've tackled repeatedly while building and scaling mobile apps at Liquide (4M+ users). I've already built internal tools to solve cross-platform development bottlenecks - now I'm making that solution available to everyone, backed by modern AI capabilities.
If you have already participated or committed to participate in an incubator, "accelerator" or "pre-accelerator" program, please tell us about it.
No participation in any accelerator programs yet.
Idea
Why did you pick this idea to work on? Do you have domain expertise in this area? How do you know people need what you're making?
I have been building mobile apps since 2015, starting with Native, then Ionic, and finally React Native. After building 10+ apps both professionally and as side projects, I kept creating internal tools to speed up development. What really clicked was seeing how much time teams waste on cross-platform compliance. I have experienced this pain from every angle: as a developer, as a team lead, and as a freelancer quoting $30K+ for basic apps.

The problem is also personal. I built internal tools at my last company that saved at least $200K in development costs while allowing us to push real-time UI updates with personalization to over 3Mn users, proving the concept works.
Who are your competitors? What do you understand about your business that they don't?
Direct competitors: a0.dev and Appacella are building similar tech, but I am betting on a different market.

1. I have led mobile dev teams and have seen how much time they waste on cross-platform work. Mobile app teams and agencies need this way more than indie developers do. That's where the focus will shift to once the core product is reliable.
2. I have also built UI generators for production apps, so I know exactly where the technical challenges are.
3. And honestly, my failed startups taught me one thing: validate fast and execute focused and faster. We went from idea to real users in 1 month, testing everything weekly.
   How do or will you make money? How much could you make?
   (We realize you can't know precisely, but give your best estimate)

We are charging $29/month right now. Keeping it simple while we learn and adapt. Already have 3 paying customers in alpha, which honestly surprised us this early. While we are starting with founders and indie devs, I know from experience that agencies and teams will pay much more to solve their cross-platform headaches. Planning to introduce team pricing around $299/month once we nail the core product.

On market size: There are 10M mobile developers globally. Even a tiny slice of that market at our current $29/month pricing is significant. The near-term opportunity is the dev agencies in US/EU. If we can get just 500 of them at $299/month, that's $1.8M ARR.  But I am focused on getting to our first 100 paying customers before thinking too big.
Which category best applies to your company?

Developer Tools
If you had any other ideas you considered applying with, please list them. One may be something we've been waiting for. Often when we fund people it's to do something they list here and not in the main application.
None.
Equity
Have you formed ANY legal entity yet?
This may be in the US, in your home country or in another country.

Yes
No
If you have not formed the company yet, describe the planned equity ownership breakdown among the founders, employees and any other proposed stockholders. If there are multiple founders, be sure to give the proposed equity ownership of each founder and founder title (e.g. CEO). (This question is as much for you as us.)
CEO: 50%
Potential future cofounder: 40%
ESOP: 10%
Have you taken any investment yet?
Yes
No
Are you currently fundraising?
Yes
No
Curious
What convinced you to apply to Y Combinator? Did someone encourage you to apply? Have you been to any YC events?
I am building this from India, but our market is global. YC solves two critical challenges for me: speed and access. First, the three-month program will compress a year's worth of learning and connections into a focused sprint. Exactly what we need at this stage. Second, being in the Bay Area opens direct access to both dev agencies (our target market) and technical talent.

We have got early validation with 3 paying customers, but scaling this into a billion-dollar company requires the kind of focused execution YC is known for. The PR boost doesn't hurt either. It'll help establish credibility with larger agencies and teams we want to target.
How did you hear about Y Combinator?
