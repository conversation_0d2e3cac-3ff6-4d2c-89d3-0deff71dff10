import rootStore, { RootStore } from "@/stores/RootStore";
import { action, computed, makeAutoObservable, observable, runInAction } from "mobx";
import ApiClient from "@/services/ApiClient";
import { DesignScreen } from "@/lib/db/schema";
import { toast } from "sonner";
import {orderBy} from "lodash";

export interface ApprovalStep {
  label: string;
  description: string;
  percentage: number;
}

export class DesignStore {
  rootStore: RootStore;

  // Design approval state
  @observable isApproving: boolean = false;
  @observable showApprovalDialog: boolean = false;
  @observable allScreensComplete: boolean = false;
  @observable designPhaseDialogShown: Map<string, boolean> = new Map();
  @observable designApprovalStatus: Map<string, boolean> = new Map();
  @observable screens: Map<string, DesignScreen[]> = new Map();
  @observable activeScreenId: Map<string, string | null> = new Map();
  @observable refreshKeys: Map<string, Record<string, number>> = new Map();
  
  // Store approval progress and current step by chatId
  @observable approvalProgressByChatId: Map<string, number> = new Map();
  @observable currentStepByChatId: Map<string, number> = new Map();

  // Define approval steps - simplified for user understanding
  readonly approvalSteps: ApprovalStep[] = [
    { label: "Analyzing screens", description: "Examining all screens for consistency", percentage: 25 },
    { label: "Generating requirements", description: "Creating detailed requirements document", percentage: 50 },
    { label: "Finalizing approval", description: "Preparing design handoff", percentage: 75 },
    { label: "Complete", description: "Design approved successfully", percentage: 100 }
  ];

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this);
  }

  @action
  setScreens(chatId: string, screens: DesignScreen[]) {
    this.screens.set(chatId, screens);
    
    // Initialize active screen if needed
    if (screens.length > 0 && !this.getActiveScreenId(chatId)) {
      this.setActiveScreenId(chatId, screens[0].id);
    }
    
    // Check if all screens are complete
    this.checkAllScreensComplete(chatId);
  }

  @action
  getScreens(chatId: string): DesignScreen[] {
    return this.screens.get(chatId) || [];
  }


  @action
  setActiveScreenId(chatId: string, screenId: string | null) {
    this.activeScreenId.set(chatId, screenId);
    
    // Force refresh the iframe to ensure it loads immediately
    if (screenId) {
      this.refreshScreen(chatId, screenId);
    }
  }

  @action
  getActiveScreenId(chatId: string): string | null {
    return this.activeScreenId.get(chatId) || null;
  }

  @action
  refreshScreen(chatId: string, screenId: string) {
    const currentKeys = this.refreshKeys.get(chatId) || {};
    this.refreshKeys.set(chatId, {
      ...currentKeys,
      [screenId]: (currentKeys[screenId] || 0) + 1
    });
  }

  @action
  getRefreshKey(chatId: string, screenId: string): number {
    const keys = this.refreshKeys.get(chatId);
    return keys ? keys[screenId] || 0 : 0;
  }

  // This method is now replaced by the more comprehensive version below
  // that handles both updating existing screens and creating new ones

  @action
  checkAllScreensComplete(chatId: string) {
    const screens = this.getScreens(chatId);
    const complete = screens.length > 0 && screens.every(screen => screen.status === 'complete');
    this.allScreensComplete = complete;
  }

  @action
  setIsApproving(isApproving: boolean) {
    this.isApproving = isApproving;
    
    // If we're resetting the approval state, reset all chat progress
    if (!isApproving) {
      // Clear all approval progress for all chats
      this.approvalProgressByChatId.clear();
      this.currentStepByChatId.clear();
    }
  }

  @action
  setShowApprovalDialog(show: boolean) {
    this.showApprovalDialog = show;
  }
  
  @action
  setDesignPhaseDialogShown(chatId: string, shown: boolean) {
    this.designPhaseDialogShown.set(chatId, shown);
  }
  
  @action
  isDesignPhaseDialogShown(chatId: string): boolean {
    return this.designPhaseDialogShown.get(chatId) || false;
  }

  @action
  setApprovalProgress(chatId: string, progress: number) {
    // Store the progress for this chat
    this.approvalProgressByChatId.set(chatId, progress);
    console.log(`Setting progress for chat ${chatId} to ${progress.toFixed(1)}%`);
    
    // Calculate the appropriate step based on progress
    let newStep;
    
    // Map progress to steps with proper sequential indexing
    // 0-25%: Step 0
    // 26-50%: Step 1 (must come after 0)
    // 51-75%: Step 2 (must come after 1)
    // 76-100%: Step 3 (must come after 2)
    if (progress <= 25) {
      newStep = 0;
    } else if (progress <= 50) {
      newStep = 1; // Must be 1 (not 2)
    } else if (progress <= 75) {
      newStep = 2; 
    } else {
      newStep = 3;
    }
    
    // Always update the step - this ensures the UI reflects the current state
    this.currentStepByChatId.set(chatId, newStep);
    console.log(`Updated step for chat ${chatId} to ${newStep} (progress: ${progress.toFixed(1)}%)`);
  }
  
  @action
  getApprovalProgress(chatId: string): number {
    return this.approvalProgressByChatId.get(chatId) || 0;
  }
  
  @action
  getCurrentStep(chatId: string): number {
    return this.currentStepByChatId.get(chatId) || -1;
  }

  @action
  setIsDesignApproved(chatId: string, approved: boolean) {
    this.designApprovalStatus.set(chatId, approved);
  }
  
  @computed
  get isDesignApproved() {
    return this.designApprovalStatus.size > 0 && Array.from(this.designApprovalStatus.values()).some(status => status);
  }
  
  @action
  isDesignApprovedForChat(chatId: string): boolean {
    return this.designApprovalStatus.get(chatId) || false;
  }
  
  @action
  loadScreens(chatId: string) {
    // If we already have screens for this chat, no need to reload
    if (this.screens.has(chatId) && (this.screens.get(chatId)?.length ?? 0) > 0) {
      return;
    }
    
    // In a real implementation, this might fetch screens from an API
    // For now, we'll just ensure the screens map has an entry for this chat
    if (!this.screens.has(chatId)) {
      this.screens.set(chatId, []);
    }
  }
  
  @action
  updateScreen(chatId: string, projectId: string, screenUpdate: {
    name: string,
    screenId: string,
    status: 'starting' | 'generating' | 'complete' | 'error',
    order: number
  }) {

    // Get current screens for this chat
    const currentScreens = this.getScreens(chatId);

    // Find if the screen already exists
    const existingScreenIndex = currentScreens.findIndex(s => s.id === screenUpdate.screenId);
    
    if (existingScreenIndex >= 0) {
      // Update existing screen
      const updatedScreens = [...currentScreens];
      updatedScreens[existingScreenIndex] = {
        ...updatedScreens[existingScreenIndex],
        name: screenUpdate.name,
        status: screenUpdate.status,
        order: screenUpdate.order,
        updatedAt: new Date()
      };
      this.setScreens(chatId, this.sortScreens(updatedScreens));
    } else {
      // Add new screen
      const newScreen = {
        id: screenUpdate.screenId,
        chatId: chatId,
        projectId: projectId,
        name: screenUpdate.name,
        status: screenUpdate.status,
        order: screenUpdate.order,
        html: '', // Initialize with empty HTML content
        createdAt: new Date(),
        updatedAt: new Date()
      };
      this.setScreens(chatId, this.sortScreens([...currentScreens, newScreen]));
      
      // If this is the first screen, set it as active
      if (currentScreens.length === 0) {
        this.setActiveScreenId(chatId, newScreen.id);
      }
    }
    
    console.log(`DesignStore: Updated screen ${screenUpdate.screenId} for chat ${chatId}`);
    
    // Check if all screens are complete after update
    this.checkAllScreensComplete(chatId);
  }


  sortScreens(screens: DesignScreen[]) {
    return orderBy(screens, ['order'], 'asc')
  }

  @action
  async deleteScreen(projectId: string, chatId: string, screenId: string) {
    try {
      // Call the API to delete the screen
      const response = await fetch(`/api/project/${projectId}/design/${chatId}/screens/${screenId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove screen');
      }
      
      // Get current screens from the store
      const currentScreens = this.getScreens(chatId);
      const updatedScreens = currentScreens.filter(s => s.id !== screenId);
      
      // If the active screen was deleted, set a new active screen
      if (this.getActiveScreenId(chatId) === screenId && updatedScreens.length > 0) {
        this.setActiveScreenId(chatId, updatedScreens[0].id);
      }
      
      // Sort by order and update order property to be sequential
      const reorderedScreens = updatedScreens
        .sort((a, b) => a.order - b.order)
        .map((screen, index) => ({
          ...screen,
          order: index
        }));
          
      // Update the store with the filtered screens
      this.setScreens(chatId, reorderedScreens);
      
      return true;
    } catch (error) {
      console.error('Error removing screen:', error);
      throw error;
    }
  }

  @action
  async approveDesign(projectId: string, chatId: string) {
    if (!this.allScreensComplete) return;
    
    this.setIsApproving(true);
    this.setShowApprovalDialog(true);
    
    // Note: Progress animation is now handled in the ApprovalBar component
    // with a 2-minute timer that updates every 500ms
    
    try {
      const response = await ApiClient.post(`/api/project/${projectId}/design/${chatId}/approve`, {});
      
      if (response.ok) {
        // Set to 100% complete - this will be picked up by the ApprovalBar
        this.setApprovalProgress(chatId, 100);
        this.setIsDesignApproved(chatId, true);
        
        // Close dialog after a short delay
        setTimeout(() => {
          this.setShowApprovalDialog(false);
          this.setIsApproving(false);
          toast.success("Design approved successfully!");
        }, 1000);
        const data = await response.json();

        return data.chatId;
      } else {
        throw new Error("Failed to approve design");
      }
    } catch (error) {
      console.error("Error approving design:", error);
      this.setApprovalProgress(chatId, 0);
      this.setIsApproving(false);
      this.setShowApprovalDialog(false);
      toast.error("Failed to approve design. Please try again.");
      return false;
    }
  }
}
