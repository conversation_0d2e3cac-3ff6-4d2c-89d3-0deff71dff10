"use client"
import React, {create<PERSON>ontext, ReactNode} from "react"
import rootStoreInstance, {RootStore} from "@/stores/RootStore";

let rootStore: RootStore

export const StoreContext = createContext<RootStore | undefined>(undefined)

export const StoreWrapper = ({children}: { children: ReactNode }) => {
    if (!rootStore) {
        rootStore = rootStoreInstance
    }
    return (
        <StoreContext.Provider value={rootStore}>{children}</StoreContext.Provider>
    )
}
