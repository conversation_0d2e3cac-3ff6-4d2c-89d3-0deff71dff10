import rootStore, { RootStore } from "@/stores/RootStore";
import { action, computed, makeAutoObservable, observable, runInAction } from "mobx";
import { ProjectSession } from "./ProjectSessionStore";
import { Message, Project } from "@/lib/db/schema";
import { FileItem } from "@/types/file";
import ApiClient from "@/services/ApiClient";

export class GeneratorStore {
    rootStore: RootStore;

    @observable expoUrl: string | null = null;
    @observable playerReady: boolean = false;
    @observable previewOpen: boolean = false;
    @observable sessions = new Map<string, ProjectSession>();
    @observable activeSessionId: string | null = null;
    @observable upgradeDialogOpen: boolean = false;
    @observable loginDialogOpen: boolean = false;
    @observable usageLimit = {
        limit: 0,
        remaining: 0
    };
    @observable projectData = new Map<string, Project>();
    @observable initialLoadComplete = false;

    constructor(rootStore: RootStore) {
        this.rootStore = rootStore;
        makeAutoObservable(this);
    }

    @computed
    get activeSession() {
        return this.activeSessionId ? this.sessions.get(this.activeSessionId) : null;
    }

    @action
    togglePreview(previewOpen: boolean) {
        this.previewOpen = previewOpen;
    }

    @action
    createSession(id: string, initialState?: {
        messages?: Message[];
        fileTree?: FileItem[];
        isInitial: boolean,
        projectId?: string;
    }) {
        if(!this.sessions.has(id)) {
            const session = new ProjectSession(rootStore, id, initialState);
            this.sessions.set(id, session);
            this.activeSessionId = id;
            return session;
        } else {
            return this.sessions.get(id) as ProjectSession;
        }
    }

    @action
    setActiveSession(id: string | null) {
        this.activeSessionId = id;
    }

    @action
    setInitialLoadComplete() {
        this.initialLoadComplete = true;
    }

    @action
    toggleUpgradeDialog(open: boolean) {
        this.upgradeDialogOpen = open;
    }

    @action
    toggleLoginDialog(open: boolean) {
        if (this) {
            this.loginDialogOpen = open;
        }
    }

    @action
    setUsageLimit(limit: number, remaining: number) {
        this.usageLimit = { limit, remaining };
    }

    @action
    getActiveSession(id: string) {
        return this.sessions.get(id);
    }

    @action
    removeSession(id: string) {
        this.sessions.delete(id);
        if (this.activeSessionId === id) {
            this.activeSessionId = null;
        }
    }
    
    @action
    async fetchProjectData(projectId: string) {
        if (!projectId) return null;
        
        // Return cached project data if available
        if (this.projectData.has(projectId)) {
            return this.projectData.get(projectId);
        }
        
        try {
            const response = await ApiClient.get(`/api/project/${projectId}`);
            if (response.ok) {
                const data = await response.json();
                runInAction(() => {
                    this.projectData.set(projectId, data);
                });
                return data;
            }
        } catch (error) {
            console.error('Failed to fetch project data:', error);
        }
        
        return null;
    }
    
    @action
    getProjectData(projectId: string) {
        return this.projectData.get(projectId) || null;
    }

    @action
    setProjectData(projectId: string, project: Project) {
        this.projectData.set(projectId, project);
        console.log('setting', project)

    }
    
    @action
    async getProjectById(projectId: string) {
        // First check if we have it in cache
        const cachedProject = this.projectData.get(projectId);
        if (cachedProject) return cachedProject;
        
        // If not in cache, trigger a fetch but return null for now
        // The component can re-render when the data is available
        await this.fetchProjectData(projectId);
        return null;
    }
}