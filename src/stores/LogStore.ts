import { makeAutoObservable } from 'mobx';
import { RootStore } from './RootStore';
import {LogEntry} from "@/types/logs";

export type LogLevel = 'info' | 'warning' | 'error' | 'debug';


export class LogStore {
  rootStore: RootStore;
  private logs = new Map<string, LogEntry[]>();
  private maxLogsPerChat = 1000; // Limit logs to prevent memory issues

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this, {
      rootStore: false
    });
  }

  addLog = (chatId: string, log: Omit<LogEntry, 'id' | 'timestamp'>) => {
    const chatLogs = this.logs.get(chatId) || [];
    
    // Create a new log entry with id and timestamp
    const newLog: LogEntry = {
      ...log,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      chatId
    };
    
    // Add new log at the beginning for chronological order (newest first)
    const updatedLogs = [newLog, ...chatLogs];
    
    // Trim logs if they exceed the maximum
    if (updatedLogs.length > this.maxLogsPerChat) {
      updatedLogs.length = this.maxLogsPerChat;
    }
    
    this.logs.set(chatId, updatedLogs);
    
    return newLog;
  };

  getLogs = (chatId: string, filter?: { 
    type?: LogLevel | LogLevel[], 
    source?: 'console' | 'network' | 'snack' | ('console' | 'network' | 'snack')[] 
  }) => {
    const logs = this.logs.get(chatId) || [];
    
    if (!filter) return logs;
    
    return logs.filter(log => {
      // Filter by type if specified
      if (filter.type) {
        const types = Array.isArray(filter.type) ? filter.type : [filter.type];
        if (!types.includes(log.type)) return false;
      }
      
      // Filter by source if specified
      if (filter.source) {
        const sources = Array.isArray(filter.source) ? filter.source : [filter.source];
        if (!sources.includes(log.source)) return false;
      }
      
      return true;
    });
  };

  clearLogs = (chatId: string) => {
    this.logs.set(chatId, []);
  };

  clearAllLogs = () => {
    this.logs.clear();
  };
}
