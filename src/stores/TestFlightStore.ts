import { action, makeAutoObservable, observable, runInAction } from 'mobx';
import { RootStore } from './RootStore';
import { FileItem } from '@/types/file';
import { BuildConfig, BuildProgress, BuildStatus, TestFlightInvite } from '@/types/testflight';
import { terminalStore } from './TerminalStore';
import { terminalAutomationService, PromptPattern } from '@/services/TerminalAutomationService';
import axios from 'axios';

export class TestFlightStore {
  rootStore: RootStore;
  
  @observable private buildConfigs = new Map<string, BuildConfig>();
  @observable private buildProgress = new Map<string, BuildProgress>();
  @observable private invites = new Map<string, TestFlightInvite[]>();
  @observable isAppleAccountConnected: boolean = false;
  @observable isExpoAccountConnected: boolean = false;
  @observable isEasInstalled: boolean = false;
  @observable appleUsername: string = '';
  @observable applePassword: string = '';
  @observable appleTeamId: string = '';
  @observable expoUsername: string = '';
  @observable expoPassword: string = '';
  @observable terminalSessionId: string | null = null;
  @observable isTerminalAutomationEnabled: boolean = true;
  @observable terminalAutomationHistory: Array<{
    timestamp: number;
    pattern: PromptPattern;
    response: string;
  }> = [];
  
  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this, {
      rootStore: false
    }, {
      autoBind: true
    });
    
    // Load credentials from localStorage if available
    this.loadCredentialsFromStorage();
  }
  
  @action
  loadCredentials(): void {
    this.loadCredentialsFromStorage();
  }
  
  private loadCredentialsFromStorage(): void {
    // Only run in browser environment
    if (typeof window === 'undefined') return;
    
    try {
      // Load Apple credentials
      const appleCredentials = localStorage.getItem('appleCredentials');
      if (appleCredentials) {
        const { username, password, teamId } = JSON.parse(appleCredentials);
        this.appleUsername = username || '';
        this.applePassword = password || '';
        this.appleTeamId = teamId || '';
        this.isAppleAccountConnected = !!(username && password);
      }
      
      // Load Expo credentials
      const expoCredentials = localStorage.getItem('expoCredentials');
      if (expoCredentials) {
        const { username, password } = JSON.parse(expoCredentials);
        this.expoUsername = username || '';
        this.expoPassword = password || '';
        this.isExpoAccountConnected = !!(username && password);
      }
    } catch (error) {
      console.error('Error loading credentials from localStorage:', error);
    }
  }
  
  private saveCredentialsToStorage(): void {
    // Only run in browser environment
    if (typeof window === 'undefined') return;
    
    try {
      // Save Apple credentials including password
      if (this.appleUsername) {
        localStorage.setItem('appleCredentials', JSON.stringify({
          username: this.appleUsername,
          password: this.applePassword,
          teamId: this.appleTeamId
        }));
      }
      
      // Save Expo credentials including password
      if (this.expoUsername) {
        localStorage.setItem('expoCredentials', JSON.stringify({
          username: this.expoUsername,
          password: this.expoPassword
        }));
      }
    } catch (error) {
      console.error('Error saving credentials to localStorage:', error);
    }
  }
  
  @action
  getBuildConfig(chatId: string): BuildConfig | undefined {
    return this.buildConfigs.get(chatId);
  }
  
  @action
  getBuildProgress(chatId: string): BuildProgress | undefined {
    return this.buildProgress.get(chatId);
  }
  
  @action
  getInvites(chatId: string): TestFlightInvite[] {
    return this.invites.get(chatId) || [];
  }
  
  @action
  setAppleAccountConnected(connected: boolean): void {
    this.isAppleAccountConnected = connected;
  }
  
  @action
  setExpoAccountConnected(connected: boolean): void {
    this.isExpoAccountConnected = connected;
  }
  
  @action
  setAppleCredentials(username: string, password: string, teamId?: string): void {
    this.appleUsername = username;
    this.applePassword = password;
    if (teamId) this.appleTeamId = teamId;
    this.isAppleAccountConnected = !!(username && password);
    
    // Save credentials to localStorage
    this.saveCredentialsToStorage();
  }
  
  @action
  setExpoCredentials(username: string, password: string): void {
    this.expoUsername = username;
    this.expoPassword = password;
    this.isExpoAccountConnected = !!(username && password);
    
    // Save credentials to localStorage
    this.saveCredentialsToStorage();
  }
  
  @action
  setEasInstalled(installed: boolean): void {
    this.isEasInstalled = installed;
  }
  
  @action
  async checkEasInstallation(): Promise<boolean> {
    // In a real implementation, this would check if EAS CLI is installed
    // For now, we'll simulate it
    this.isEasInstalled = true;
    return true;
  }
  
  @action
  initBuildConfig(chatId: string): void {
    // Get the session from generator store
    const session = this.rootStore.generatorStore.getActiveSession(chatId);
    if (!session) return;
    
    // Find app.json or package.json to extract app name and other metadata
    const appJsonFile = session.fileTree.find(file => file.name === 'app.json');
    const packageJsonFile = session.fileTree.find(file => file.name === 'package.json');
    
    let appName = 'My App';
    let version = '1.0.0';
    let buildNumber = '1';
    
    if (appJsonFile) {
      try {
        const appJson = JSON.parse(appJsonFile.content);
        appName = appJson.expo?.name || appJson.name || 'My App';
        version = appJson.expo?.version || appJson.version || '1.0.0';
        buildNumber = appJson.expo?.ios?.buildNumber || '1';
      } catch (e) {
        console.error('Failed to parse app.json', e);
      }
    } else if (packageJsonFile) {
      try {
        const packageJson = JSON.parse(packageJsonFile.content);
        appName = packageJson.name || 'My App';
        version = packageJson.version || '1.0.0';
      } catch (e) {
        console.error('Failed to parse package.json', e);
      }
    }
    
    // Generate a bundle ID based on app name
    const bundleId = `com.magically.${appName.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
    
    // Initialize build config
    this.buildConfigs.set(chatId, {
      appName,
      bundleId,
      version,
      buildNumber
    });
    
    // Initialize build progress
    this.buildProgress.set(chatId, {
      status: 'idle',
      message: 'Ready to start build',
      progress: 0,
      logs: []
    });
    
    // Initialize empty invites list
    this.invites.set(chatId, []);
    
    // Check if EAS is installed
    this.checkEasInstallation();
  }
  
  @action
  updateBuildConfig(chatId: string, config: Partial<BuildConfig>): void {
    const currentConfig = this.buildConfigs.get(chatId);
    if (currentConfig) {
      this.buildConfigs.set(chatId, { ...currentConfig, ...config });
    }
  }
  
  @action
  addBuildLog(chatId: string, message: string, type: 'info' | 'error' | 'warning' | 'success'): void {
    const progress = this.buildProgress.get(chatId);
    if (progress) {
      progress.logs.push({
        timestamp: Date.now(),
        message,
        type
      });
      this.buildProgress.set(chatId, { ...progress });
    }
  }
  
  @action
  updateBuildProgress(chatId: string, update: Partial<BuildProgress>): void {
    const currentProgress = this.buildProgress.get(chatId);
    if (currentProgress) {
      this.buildProgress.set(chatId, { ...currentProgress, ...update });
    }
  }
  
  @action
  async startBuild(chatId: string): Promise<boolean> {
    console.log('TestFlightStore.startBuild called with chatId:', chatId);
    const config = this.buildConfigs.get(chatId);
    if (!config) {
      this.initBuildConfig(chatId);
    }
    
    try {
      // Update build progress to indicate we're starting
      this.updateBuildProgress(chatId, {
        status: 'configuring',
        message: 'Starting build process',
        progress: 5
      });
      this.addBuildLog(chatId, 'Starting build process', 'info');
      
      // Generate a mock build ID
      const buildId = `build-${Date.now()}`;
      this.terminalSessionId = `session-${Date.now()}`;
      
      // Simulate the build process
      this.updateBuildProgress(chatId, {
        buildId,
        status: 'configuring',
        message: 'Build started successfully',
        progress: 10
      });
      this.addBuildLog(chatId, `Build started with ID: ${buildId}`, 'success');
      
      // Simulate the terminal session
      setTimeout(() => {
        this.addBuildLog(chatId, 'Configuring EAS build', 'info');
        this.updateBuildProgress(chatId, {
          status: 'configuring',
          message: 'Configuring EAS build',
          progress: 20
        });
      }, 2000);
      
      setTimeout(() => {
        this.addBuildLog(chatId, 'Preparing build environment', 'info');
        this.updateBuildProgress(chatId, {
          status: 'preparing',
          message: 'Preparing build environment',
          progress: 40
        });
      }, 5000);
      
      setTimeout(() => {
        this.addBuildLog(chatId, 'Building application', 'info');
        this.updateBuildProgress(chatId, {
          status: 'building',
          message: 'Building application',
          progress: 60
        });
      }, 8000);
      
      setTimeout(() => {
        this.addBuildLog(chatId, 'Submitting to TestFlight', 'info');
        this.updateBuildProgress(chatId, {
          status: 'submitting',
          message: 'Submitting to TestFlight',
          progress: 80
        });
      }, 12000);
      
      setTimeout(() => {
        this.addBuildLog(chatId, 'Build completed successfully', 'success');
        this.updateBuildProgress(chatId, {
          status: 'completed',
          message: 'Build completed successfully',
          progress: 100,
          testFlightLink: 'https://appstoreconnect.apple.com/apps/123456789/testflight/ios'
        });
      }, 15000);
      
      return true;
    } catch (error: any) {
      console.error('Error starting build:', error);
      this.addBuildLog(chatId, `Error starting build: ${error.message}`, 'error');
      this.updateBuildProgress(chatId, {
        status: 'failed',
        message: `Failed to start build: ${error.message}`,
        progress: 0,
        error: error.message
      });
      return false;
    }
  }
  
  @action
  private async pollBuildStatus(chatId: string, buildId: string): Promise<void> {
    try {
      const response = await axios.post('/api/testflight/status', {
        buildId,
        expoUsername: this.expoUsername,
        expoPassword: this.expoPassword
      });
      
      if (response.data.success) {
        const { status, progress, logs, testFlightLink, error } = response.data;
        
        // Update build progress
        this.updateBuildProgress(chatId, {
          status,
          progress,
          message: this.getStatusMessage(status),
          testFlightLink,
          error
        });
        
        // Add new logs
        const currentLogs = this.buildProgress.get(chatId)?.logs || [];
        const currentLogMessages = new Set(currentLogs.map(log => log.message));
        
        logs.forEach((log: { message: string; type: 'info' | 'error' | 'warning' | 'success' }) => {
          if (!currentLogMessages.has(log.message)) {
            this.addBuildLog(chatId, log.message, log.type);
          }
        });
        
        // If build is still in progress, continue polling
        if (['configuring', 'preparing', 'building', 'submitting'].includes(status)) {
          setTimeout(() => this.pollBuildStatus(chatId, buildId), 5000);
        }
      } else {
        throw new Error(response.data.message || 'Failed to check build status');
      }
    } catch (error: any) {
      console.error('Error checking build status:', error);
      this.addBuildLog(chatId, `Error checking build status: ${error.message}`, 'error');
      
      // Continue polling even if there's an error, but with a longer delay
      setTimeout(() => this.pollBuildStatus(chatId, buildId), 10000);
    }
  }
  
  private getStatusMessage(status: BuildStatus): string {
    switch (status) {
      case 'configuring': return 'Configuring build environment';
      case 'preparing': return 'Preparing project files';
      case 'building': return 'Building iOS application';
      case 'submitting': return 'Submitting to TestFlight';
      case 'completed': return 'Build completed successfully';
      case 'failed': return 'Build failed';
      default: return 'Build in progress';
    }
  }
  
  @action
  async cancelBuild(chatId: string): Promise<void> {
    const progress = this.buildProgress.get(chatId);
    if (progress && ['configuring', 'preparing', 'building', 'submitting'].includes(progress.status)) {
      // Send SIGINT to the terminal (Ctrl+C)
      if (this.terminalSessionId) {
        terminalStore.sendRawInput('\u0003'); // Ctrl+C character
      }
      
      this.updateBuildProgress(chatId, {
        status: 'idle',
        message: 'Build cancelled',
        progress: 0
      });
      this.addBuildLog(chatId, 'Build cancelled by user', 'warning');
    }
  }
  
  @action
  async resetBuild(chatId: string): Promise<void> {
    // Close the terminal session if it exists
    if (this.terminalSessionId) {
      await terminalStore.closeSession();
      this.terminalSessionId = null;
    }
    
    // Reset the automation history
    this.terminalAutomationHistory = [];
    
    // Reset the build config
    this.initBuildConfig(chatId);
  }
  

  
  // Add a tester invite to the project
  @action
  async addInvite(chatId: string, email: string, name?: string): Promise<void> {
    const currentInvites = this.invites.get(chatId) || [];
    const progress = this.buildProgress.get(chatId);
    
    // Check if email already exists
    if (currentInvites.some(invite => invite.email === email)) {
      return;
    }
    
    // Check if we have a build ID
    if (!progress?.buildId) {
      this.addBuildLog(chatId, 'Cannot send invite: No build available', 'error');
      return;
    }
    
    // Add invite with pending status
    currentInvites.push({
      email,
      name,
      sent: false,
      status: 'pending'
    });
    
    this.invites.set(chatId, currentInvites);
    this.addBuildLog(chatId, `Adding tester: ${email}`, 'info');
    
    try {
      // Make API call to send the invite
      const response = await axios.post('/api/testflight/invite', {
        buildId: progress.buildId,
        email,
        name,
        appleUsername: this.appleUsername,
        applePassword: this.applePassword,
        appleTeamId: this.appleTeamId
      });
      
      if (response.data.success) {
        this.updateInviteStatus(chatId, email, true, 'pending');
        this.addBuildLog(chatId, `Invite sent to ${email}`, 'success');
      } else {
        throw new Error(response.data.message || 'Failed to send invite');
      }
    } catch (error: any) {
      console.error('Error sending invite:', error);
      this.addBuildLog(chatId, `Error sending invite: ${error.message}`, 'error');
      this.updateInviteStatus(chatId, email, false);
    }
  }
  
  // Update the status of an invite
  @action
  updateInviteStatus(chatId: string, email: string, sent: boolean, status?: 'pending' | 'accepted' | 'declined'): void {
    const currentInvites = this.invites.get(chatId) || [];
    const updatedInvites = currentInvites.map(invite => {
      if (invite.email === email) {
        return {
          ...invite,
          sent,
          status: status || invite.status
        };
      }
      return invite;
    });
    
    this.invites.set(chatId, updatedInvites);
  }
  
  // Remove an invite
  @action
  removeInvite(chatId: string, email: string): void {
    const currentInvites = this.invites.get(chatId) || [];
    const updatedInvites = currentInvites.filter(invite => invite.email !== email);
    this.invites.set(chatId, updatedInvites);
  }
  
  // This method would extract app metadata from the project files
  private extractAppMetadata(files: FileItem[]): Partial<BuildConfig> {
    // Implementation would parse app.json, package.json, etc.
    // For now, return default values
    return {
      appName: 'My App',
      bundleId: 'com.magically.myapp',
      version: '1.0.0',
      buildNumber: '1'
    };
  }
  
  /**
   * Initialize a terminal session for the build process
   */
  @action
  async initializeTerminalSession(): Promise<string | null> {
    try {
      // Initialize the terminal session
      await terminalStore.initializeSession();
      
      // Store the session ID
      runInAction(() => {
        this.terminalSessionId = terminalStore.state.sessionId;
      });
      
      return terminalStore.state.sessionId;
    } catch (error) {
      console.error('Failed to initialize terminal session:', error);
      return null;
    }
  }
  
  /**
   * Set up terminal automation patterns based on user credentials
   */
  @action
  setupTerminalAutomationPatterns(): void {
    if (!this.terminalSessionId) return;
    
    // Initialize the automation service with the terminal session
    terminalAutomationService.initialize(this.terminalSessionId, {
      enabled: this.isTerminalAutomationEnabled,
      useDefaultPatterns: true,
      customPatterns: [
        // Add custom patterns for Expo login
        {
          pattern: /Email or username:/i,
          response: `${this.expoUsername}\n`,
          description: 'Expo username input'
        },
        {
          pattern: /Password:/i,
          response: `${this.expoPassword}\n`,
          description: 'Expo password input'
        },
        // Add custom patterns for Apple login
        {
          pattern: /Apple ID:/i,
          response: `${this.appleUsername}\n`,
          description: 'Apple ID input'
        },
        {
          pattern: /Apple ID Password:/i,
          response: `${this.applePassword}\n`,
          description: 'Apple password input'
        },
        // Add custom pattern for Apple Team ID selection
        {
          pattern: new RegExp(`Team ID \\[(.*?)\\].*?${this.appleTeamId}`, 'i'),
          response: (match) => {
            // Extract the number from the prompt and return it
            const teamOptions = match[1].split(',');
            const teamIndex = teamOptions.findIndex(option => 
              option.trim().includes(this.appleTeamId)
            );
            return `${teamIndex + 1}\n`;
          },
          description: 'Apple Team ID selection'
        }
      ],
      onPromptAutomated: (pattern, match, response) => {
        // Add to history
        this.addAutomationHistoryEntry(pattern, response);
      }
    });
  }
  
  /**
   * Add an entry to the automation history
   */
  @action
  addAutomationHistoryEntry(pattern: PromptPattern, response: string): void {
    runInAction(() => {
      this.terminalAutomationHistory = [
        {
          timestamp: Date.now(),
          pattern,
          response
        },
        ...this.terminalAutomationHistory
      ].slice(0, 20); // Keep only the last 20 items
    });
  }
  
  /**
   * Set whether terminal automation is enabled
   */
  @action
  setTerminalAutomationEnabled(enabled: boolean): void {
    this.isTerminalAutomationEnabled = enabled;
    
    // Update the automation service if it's initialized
    if (this.terminalSessionId) {
      terminalAutomationService.setEnabled(enabled);
    }
  }
  
  /**
   * Monitor build progress based on terminal output
   */
  private monitorBuildProgress(chatId: string): void {
    if (!this.terminalSessionId) return;
    
    // Add a direct output listener to monitor the build progress
    terminalStore.addDirectOutputListener((data: string) => {
      // Update build progress based on terminal output
      if (data.includes('Configuring build')) {
        this.updateBuildProgress(chatId, { 
          status: 'configuring',
          message: 'Configuring build environment',
          progress: 20
        });
      } else if (data.includes('Preparing project')) {
        this.updateBuildProgress(chatId, { 
          status: 'preparing',
          message: 'Preparing project files',
          progress: 40
        });
      } else if (data.includes('Building iOS app')) {
        this.updateBuildProgress(chatId, { 
          status: 'building',
          message: 'Building iOS application',
          progress: 60
        });
      } else if (data.includes('Submitting to TestFlight')) {
        this.updateBuildProgress(chatId, { 
          status: 'submitting',
          message: 'Submitting to TestFlight',
          progress: 80
        });
      } else if (data.includes('Build completed successfully')) {
        // Extract TestFlight link if available
        const linkMatch = data.match(/https:\/\/testflight\.apple\.com\/join\/([a-zA-Z0-9]+)/);
        const testFlightLink = linkMatch ? linkMatch[0] : '';
        const buildId = linkMatch ? linkMatch[1] : `build-${Date.now()}`;
        
        this.updateBuildProgress(chatId, { 
          status: 'completed',
          message: 'Build completed successfully',
          progress: 100,
          buildId,
          testFlightLink
        });
        this.addBuildLog(chatId, 'Build completed successfully', 'success');
      } else if (data.includes('Build failed')) {
        this.updateBuildProgress(chatId, { 
          status: 'failed',
          message: 'Build failed',
          progress: 0
        });
        this.addBuildLog(chatId, 'Build failed', 'error');
      }
      
      // Add log entry for important messages
      if (data.includes('error:') || data.includes('Error:')) {
        this.addBuildLog(chatId, data.trim(), 'error');
      } else if (data.includes('warning:') || data.includes('Warning:')) {
        this.addBuildLog(chatId, data.trim(), 'warning');
      } else if (data.includes('success') || data.includes('Success')) {
        this.addBuildLog(chatId, data.trim(), 'success');
      }
    });
  }
}
