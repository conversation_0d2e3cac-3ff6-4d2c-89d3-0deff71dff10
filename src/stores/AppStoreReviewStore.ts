import { action, computed, makeAutoObservable, observable, runInAction } from "mobx";
import { RootStore } from "./RootStore";

export type Platform = 'ios' | 'android';
export type ChecklistItemStatus = 'pending' | 'in-progress' | 'completed' | 'failed';
export type ChecklistItemPriority = 'critical' | 'high' | 'medium' | 'low';
export type ChecklistItemType = 'manual' | 'automated';
export type CheckCategory = 'metadata' | 'content' | 'functionality' | 'design' | 'legal' | 'technical';

export interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  status: ChecklistItemStatus;
  priority: ChecklistItemPriority;
  type: ChecklistItemType;
  category: CheckCategory;
  platform: Platform | 'both';
  steps?: string[];
  detailedGuidance?: string;
  commonIssues?: string[];
  automationAvailable?: boolean;
  automationInProgress?: boolean;
  automationComplete?: boolean;
  automationError?: string;
  relatedDocumentation?: string;
}

export class AppStoreReviewStore {
  rootStore: RootStore;

  @observable selectedPlatform: Platform = 'ios';
  @observable isLoading: boolean = false;
  @observable checklistItems: ChecklistItem[] = [];
  @observable activeItemId: string | null = null;
  @observable isAutomating: boolean = false;
  @observable automationProgress: number = 0;
  @observable automationStep: string = '';
  @observable showAutomationDialog: boolean = false;
  @observable searchQuery: string = '';
  @observable selectedCategories: CheckCategory[] = [];
  @observable selectedPriorities: ChecklistItemPriority[] = [];

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this);
    this.initializeMockData();
  }

  @action
  setSelectedPlatform(platform: Platform) {
    this.selectedPlatform = platform;
  }

  @action
  setActiveItem(itemId: string | null) {
    this.activeItemId = itemId;
  }

  @action
  toggleItemStatus(itemId: string) {
    const item = this.checklistItems.find(item => item.id === itemId);
    if (item) {
      // Cycle through statuses: pending -> in-progress -> completed -> pending
      const statusOrder: ChecklistItemStatus[] = ['pending', 'in-progress', 'completed', 'pending'];
      const currentIndex = statusOrder.indexOf(item.status);
      const nextIndex = (currentIndex + 1) % statusOrder.length;
      item.status = statusOrder[nextIndex];
    }
  }

  @action
  setItemStatus(itemId: string, status: ChecklistItemStatus) {
    const item = this.checklistItems.find(item => item.id === itemId);
    if (item) {
      item.status = status;
    }
  }

  @action
  startAutomation(itemId: string) {
    const item = this.checklistItems.find(item => item.id === itemId);
    if (item && item.automationAvailable) {
      this.isAutomating = true;
      this.showAutomationDialog = true;
      this.automationProgress = 0;
      this.automationStep = 'Initializing...';
      item.automationInProgress = true;
      item.status = 'in-progress';
      
      // Simulate automation process
      this.simulateAutomation(itemId);
    }
  }

  @action
  private simulateAutomation(itemId: string) {
    const steps = [
      { progress: 10, message: 'Analyzing app configuration...' },
      { progress: 25, message: 'Checking metadata requirements...' },
      { progress: 40, message: 'Validating app assets...' },
      { progress: 60, message: 'Applying best practices...' },
      { progress: 80, message: 'Finalizing improvements...' },
      { progress: 100, message: 'Completed!' }
    ];

    let stepIndex = 0;
    
    const interval = setInterval(() => {
      if (stepIndex < steps.length) {
        const step = steps[stepIndex];
        this.setAutomationProgress(step.progress, step.message);
        stepIndex++;
      } else {
        clearInterval(interval);
        this.completeAutomation(itemId, true);
      }
    }, 1500);
  }

  @action
  setAutomationProgress(progress: number, step: string) {
    this.automationProgress = progress;
    this.automationStep = step;
  }

  @action
  completeAutomation(itemId: string, success: boolean = true) {
    const item = this.checklistItems.find(item => item.id === itemId);
    if (item) {
      item.automationInProgress = false;
      item.automationComplete = success;
      item.status = success ? 'completed' : 'failed';
      
      if (!success) {
        item.automationError = 'Failed to complete automation. Please try again or complete manually.';
      }
    }
    
    // Close dialog after a short delay
    setTimeout(() => {
      this.isAutomating = false;
      this.showAutomationDialog = false;
    }, 1000);
  }

  @action
  setSearchQuery(query: string) {
    this.searchQuery = query;
  }

  @action
  toggleCategoryFilter(category: CheckCategory) {
    if (this.selectedCategories.includes(category)) {
      this.selectedCategories = this.selectedCategories.filter(c => c !== category);
    } else {
      this.selectedCategories = [...this.selectedCategories, category];
    }
  }

  @action
  togglePriorityFilter(priority: ChecklistItemPriority) {
    if (this.selectedPriorities.includes(priority)) {
      this.selectedPriorities = this.selectedPriorities.filter(p => p !== priority);
    } else {
      this.selectedPriorities = [...this.selectedPriorities, priority];
    }
  }

  @action
  resetFilters() {
    this.searchQuery = '';
    this.selectedCategories = [];
    this.selectedPriorities = [];
  }

  @computed
  get filteredItems(): ChecklistItem[] {
    return this.checklistItems.filter(item => {
      // Filter by platform
      const platformMatch = item.platform === 'both' || item.platform === this.selectedPlatform;
      
      // Filter by search query
      const searchMatch = this.searchQuery === '' || 
        item.title.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
        item.description.toLowerCase().includes(this.searchQuery.toLowerCase());
      
      // Filter by category
      const categoryMatch = this.selectedCategories.length === 0 || 
        this.selectedCategories.includes(item.category);
      
      // Filter by priority
      const priorityMatch = this.selectedPriorities.length === 0 || 
        this.selectedPriorities.includes(item.priority);
      
      return platformMatch && searchMatch && categoryMatch && priorityMatch;
    });
  }

  @computed
  get completionStats() {
    const platformItems = this.checklistItems.filter(
      item => item.platform === 'both' || item.platform === this.selectedPlatform
    );
    
    const total = platformItems.length;
    const completed = platformItems.filter(item => item.status === 'completed').length;
    const inProgress = platformItems.filter(item => item.status === 'in-progress').length;
    const pending = platformItems.filter(item => item.status === 'pending').length;
    const failed = platformItems.filter(item => item.status === 'failed').length;
    
    const percentComplete = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    return {
      total,
      completed,
      inProgress,
      pending,
      failed,
      percentComplete
    };
  }

  @computed
  get categoryBreakdown() {
    const result: Record<CheckCategory, number> = {
      'metadata': 0,
      'content': 0,
      'functionality': 0,
      'design': 0,
      'legal': 0,
      'technical': 0
    };
    
    this.filteredItems.forEach(item => {
      result[item.category]++;
    });
    
    return result;
  }

  @computed
  get priorityBreakdown() {
    const result: Record<ChecklistItemPriority, number> = {
      'critical': 0,
      'high': 0,
      'medium': 0,
      'low': 0
    };
    
    this.filteredItems.forEach(item => {
      result[item.priority]++;
    });
    
    return result;
  }

  @action
  private initializeMockData() {
    // Mock data for the checklist items
    this.checklistItems = [
      // iOS Specific Items
      {
        id: 'ios-1',
        title: 'App Name & Bundle ID',
        description: 'Ensure your app name and bundle identifier meet Apple guidelines and are consistent across your app.',
        status: 'pending',
        priority: 'critical',
        type: 'manual',
        category: 'metadata',
        platform: 'ios',
        steps: [
          'Verify app name is under 30 characters',
          'Confirm bundle ID follows reverse-domain notation (e.g., com.company.appname)',
          'Ensure bundle ID matches what\'s registered in App Store Connect'
        ],
        detailedGuidance: 'App names should be clear, accurate, and not misleading. Avoid using terms that imply Apple endorsement or special positioning.',
        commonIssues: [
          'App name too long or contains prohibited terms',
          'Bundle ID mismatch between app and App Store Connect',
          'Using reserved Apple terms in app name'
        ],
        automationAvailable: true
      },
      {
        id: 'ios-2',
        title: 'Privacy Policy URL',
        description: 'A valid privacy policy URL is required for all apps on the App Store.',
        status: 'pending',
        priority: 'critical',
        type: 'manual',
        category: 'legal',
        platform: 'ios',
        steps: [
          'Create a comprehensive privacy policy',
          'Host the privacy policy on a secure (HTTPS) website',
          'Add the URL to your App Store Connect listing'
        ],
        detailedGuidance: 'Your privacy policy must clearly explain what data your app collects, how it\'s used, stored, and whether it\'s shared with third parties.',
        commonIssues: [
          'Missing privacy policy',
          'Privacy policy URL is not secure (HTTP instead of HTTPS)',
          'Privacy policy doesn\'t address all required disclosures'
        ],
        automationAvailable: true
      },
      {
        id: 'ios-3',
        title: 'App Icon Requirements',
        description: 'Your app icon must meet Apple\'s size and format requirements.',
        status: 'pending',
        priority: 'high',
        type: 'automated',
        category: 'design',
        platform: 'ios',
        steps: [
          'Create a 1024x1024 pixel icon in PNG format',
          'Ensure icon has no transparency',
          'Verify icon has no rounded corners (Apple will add them automatically)'
        ],
        detailedGuidance: 'Your app icon is the first impression users have of your app. It should be simple, recognizable, and follow Apple\'s Human Interface Guidelines.',
        commonIssues: [
          'Icon resolution too low',
          'Icon contains transparency',
          'Icon includes elements that extend beyond the safe area'
        ],
        automationAvailable: true
      },
      
      // Android Specific Items
      {
        id: 'android-1',
        title: 'App Name & Package Name',
        description: 'Ensure your app name and package name meet Google Play guidelines and are consistent.',
        status: 'pending',
        priority: 'critical',
        type: 'manual',
        category: 'metadata',
        platform: 'android',
        steps: [
          'Verify app name is under 50 characters',
          'Confirm package name follows reverse-domain notation (e.g., com.company.appname)',
          'Ensure package name is unique and not already in use'
        ],
        detailedGuidance: 'Your app name should be descriptive and unique. The package name is permanent for the lifetime of your app and cannot be changed after publishing.',
        commonIssues: [
          'App name too generic or misleading',
          'Package name already in use by another app',
          'Inconsistent naming between app and Play Store listing'
        ],
        automationAvailable: true
      },
      {
        id: 'android-2',
        title: 'Privacy Policy URL',
        description: 'A valid privacy policy URL is required for all apps on Google Play.',
        status: 'pending',
        priority: 'critical',
        type: 'manual',
        category: 'legal',
        platform: 'android',
        steps: [
          'Create a comprehensive privacy policy',
          'Host the privacy policy on a secure (HTTPS) website',
          'Add the URL to your Google Play Console'
        ],
        detailedGuidance: 'Your privacy policy must clearly explain what data your app collects, how it\'s used, stored, and whether it\'s shared with third parties.',
        commonIssues: [
          'Missing privacy policy',
          'Privacy policy URL is not secure (HTTP instead of HTTPS)',
          'Privacy policy doesn\'t address all required disclosures'
        ],
        automationAvailable: true
      },
      {
        id: 'android-3',
        title: 'App Icon Requirements',
        description: 'Your app icon must meet Google Play\'s size and format requirements.',
        status: 'pending',
        priority: 'high',
        type: 'automated',
        category: 'design',
        platform: 'android',
        steps: [
          'Create a 512x512 pixel icon in PNG format',
          'Provide a high-resolution icon (1024x1024 recommended)',
          'Ensure icon follows Material Design guidelines'
        ],
        detailedGuidance: 'Your app icon should be distinctive and memorable. Follow Material Design guidelines for the best results.',
        commonIssues: [
          'Icon resolution too low',
          'Icon doesn\'t follow Material Design guidelines',
          'Icon has poor visibility on different backgrounds'
        ],
        automationAvailable: true
      },
      
      // Common Items for Both Platforms
      {
        id: 'common-1',
        title: 'App Description',
        description: 'Create a compelling and accurate app description that follows store guidelines.',
        status: 'pending',
        priority: 'high',
        type: 'manual',
        category: 'metadata',
        platform: 'both',
        steps: [
          'Write a clear, concise description under 4000 characters',
          'Include key features and benefits',
          'Avoid misleading claims or prohibited content'
        ],
        detailedGuidance: 'Your app description should clearly communicate what your app does, its key features, and why users should download it. Be honest and avoid keyword stuffing.',
        commonIssues: [
          'Description too vague or misleading',
          'Keyword stuffing or spam tactics',
          'Claims of features not actually in the app'
        ],
        automationAvailable: false
      },
      {
        id: 'common-2',
        title: 'Screenshots & Preview Video',
        description: 'Prepare high-quality screenshots and optional preview video that showcase your app.',
        status: 'pending',
        priority: 'high',
        type: 'manual',
        category: 'metadata',
        platform: 'both',
        steps: [
          'Create screenshots for all required device sizes',
          'Ensure screenshots show actual app content (not mockups)',
          'Prepare an optional app preview video (30 seconds max)'
        ],
        detailedGuidance: 'Screenshots and preview videos are your chance to show off your app\'s features and UI. They should accurately represent the user experience.',
        commonIssues: [
          'Low-quality or blurry screenshots',
          'Screenshots don\'t show actual app content',
          'Preview video too long or doesn\'t follow guidelines'
        ],
        automationAvailable: false
      },
      {
        id: 'common-3',
        title: 'Age Rating',
        description: 'Set appropriate age ratings for your app based on its content.',
        status: 'pending',
        priority: 'medium',
        type: 'manual',
        category: 'content',
        platform: 'both',
        steps: [
          'Complete the age rating questionnaire',
          'Be honest about app content and features',
          'Consider regional differences in content standards'
        ],
        detailedGuidance: 'Age ratings help users determine if an app is appropriate. Be accurate and thorough when completing the questionnaire.',
        commonIssues: [
          'Incorrect or inconsistent age ratings',
          'Failure to disclose mature content',
          'Age rating doesn\'t match actual app content'
        ],
        automationAvailable: false
      },
      {
        id: 'common-4',
        title: 'Terms of Service URL',
        description: 'Provide a valid Terms of Service URL if your app requires one.',
        status: 'pending',
        priority: 'medium',
        type: 'manual',
        category: 'legal',
        platform: 'both',
        steps: [
          'Create comprehensive Terms of Service',
          'Host on a secure (HTTPS) website',
          'Add the URL to your app store listing'
        ],
        detailedGuidance: 'Terms of Service should clearly explain user rights and limitations, especially if your app involves user accounts, purchases, or content creation.',
        commonIssues: [
          'Missing Terms of Service when required',
          'Terms not accessible or hosted on insecure site',
          'Terms don\'t address all app features and limitations'
        ],
        automationAvailable: true
      },
      {
        id: 'common-5',
        title: 'In-App Purchases',
        description: 'Ensure all in-app purchases are properly configured and comply with store policies.',
        status: 'pending',
        priority: 'high',
        type: 'manual',
        category: 'functionality',
        platform: 'both',
        steps: [
          'Configure all in-app purchases in app store console',
          'Implement proper purchase restoration',
          'Clearly disclose subscription terms and pricing'
        ],
        detailedGuidance: 'In-app purchases must be transparent to users. Clearly disclose what users are buying, subscription terms, and how to manage or cancel subscriptions.',
        commonIssues: [
          'Missing restore purchases functionality',
          'Unclear subscription terms or pricing',
          'Attempting to circumvent store payment systems'
        ],
        automationAvailable: false
      },
      {
        id: 'common-6',
        title: 'App Performance',
        description: 'Ensure your app performs well, without crashes, bugs, or excessive resource usage.',
        status: 'pending',
        priority: 'critical',
        type: 'automated',
        category: 'technical',
        platform: 'both',
        steps: [
          'Test app on multiple devices and OS versions',
          'Check for memory leaks and performance issues',
          'Verify app doesn\'t crash or freeze during normal use'
        ],
        detailedGuidance: 'Performance issues are a common reason for app rejection. Test thoroughly on multiple devices and fix any crashes, bugs, or UI issues.',
        commonIssues: [
          'App crashes or freezes',
          'Excessive battery or memory usage',
          'Poor performance on older devices'
        ],
        automationAvailable: true
      },
      {
        id: 'common-7',
        title: 'Data Collection Transparency',
        description: 'Clearly disclose all data collection practices in your app and privacy policy.',
        status: 'pending',
        priority: 'critical',
        type: 'manual',
        category: 'legal',
        platform: 'both',
        steps: [
          'Complete App Privacy section in app store console',
          'Ensure privacy policy matches declared data practices',
          'Implement appropriate consent mechanisms for data collection'
        ],
        detailedGuidance: 'Both Apple and Google require transparency about what data your app collects and how it\'s used. Be comprehensive and accurate.',
        commonIssues: [
          'Incomplete or inaccurate privacy declarations',
          'Collecting data not disclosed in privacy policy',
          'Missing required consent mechanisms'
        ],
        automationAvailable: true
      },
      {
        id: 'common-8',
        title: 'Accessibility Support',
        description: 'Ensure your app is accessible to users with disabilities.',
        status: 'pending',
        priority: 'medium',
        type: 'automated',
        category: 'design',
        platform: 'both',
        steps: [
          'Support screen readers (VoiceOver/TalkBack)',
          'Ensure sufficient color contrast',
          'Provide text alternatives for images'
        ],
        detailedGuidance: 'Making your app accessible improves the experience for all users and helps avoid rejection for accessibility issues.',
        commonIssues: [
          'Missing alternative text for images',
          'Poor color contrast making text hard to read',
          'UI elements not properly labeled for screen readers'
        ],
        automationAvailable: true
      }
    ];
  }
}
