import { makeAutoObservable, action, observable, runInAction } from 'mobx';
import { RootStore } from './RootStore';
import { Message } from 'ai';
import { generateUUID } from '@/lib/utils';
import { debounce } from 'lodash';
import { toast } from 'sonner';
import { DesignStreamClient } from '@/lib/design-stream-client';

// Constants

export interface DesignPreviewState {
  projectId: string;
  chatId: string;
  previewHtml: string | null;
  error: string | null;
  streamingContent: string;
  isLoading: boolean;
  sessionId: string;
  messages: Message[];
  input: string;
  isDesignApproved: boolean;
}

const WAIT_DURATION = 1000;

export class DesignPreviewStore {
  rootStore: RootStore;

  @observable state: DesignPreviewState = {
    projectId: '',
    chatId: '',
    previewHtml: null,
    error: null,
    streamingContent: '',
    isLoading: false,
    sessionId: generateUUID(),
    messages: [],
    input: '',
    isDesignApproved: false,
  };

  @observable updateIntervalId: NodeJS.Timeout | null = null;
  @observable currentBlobUrl: string | null = null;

  // Debounced version of parseStreamingContent
  debouncedParseStreamingContent: ReturnType<typeof debounce<(content: string) => string | null>>;

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this, {
      rootStore: false,
      debouncedParseStreamingContent: false, // Don't make the debounced function observable
    });

    // Initialize the debounced function with a 5-second delay
    this.debouncedParseStreamingContent = debounce(this.parseStreamingContent.bind(this), WAIT_DURATION, {
      leading: false,
      trailing: true,
      maxWait: WAIT_DURATION
    });

    // Log that the store has been initialized
    console.log('DesignPreviewStore initialized with debounce duration:', WAIT_DURATION, 'ms');
  }

  @action
  setProjectId(projectId: string) {
    this.state.projectId = projectId;
  }

  @action
  setChatId(chatId: string) {
    this.state.chatId = chatId;
  }

  @action
  setInput(input: string) {
    this.state.input = input;
    console.log('adas')
  }

  @action
  setPreviewHtml(html: string | null) {
    this.state.previewHtml = html;
  }

  @action
  setError(error: string | null) {
    this.state.error = error;
  }

  @action
  setStreamingContent(content: string) {
    this.state.streamingContent = content;
  }

  @action
  setIsLoading(isLoading: boolean) {
    this.state.isLoading = isLoading;
  }

  @action
  setSessionId(sessionId: string) {
    this.state.sessionId = sessionId;
  }

  @action
  setMessages(messages: Message[]) {
    console.log('messages', messages)
    this.state.messages = messages;
  }
  
  @action
  setDesignApproved(isApproved: boolean) {
    this.state.isDesignApproved = isApproved;
  }

  @action
  resetState() {
    this.state.previewHtml = null;
    this.state.error = null;
    this.state.streamingContent = '';
    this.state.sessionId = generateUUID();
    this.state.messages = [];

    // Clean up any existing blob URL
    if (this.currentBlobUrl) {
      URL.revokeObjectURL(this.currentBlobUrl);
      this.currentBlobUrl = null;
    }

    // Clear any existing interval
    this.clearUpdateInterval();
  }

  @action
  startUpdateInterval(callback: () => void) {
    // Clear any existing interval first
    this.clearUpdateInterval();

    // Only start interval if we're loading
    if (this.state.isLoading) {
      console.log('Starting update interval');
      this.updateIntervalId = setInterval(() => {
        if (this.state.streamingContent) {
          // Update the message with the streaming content
          // Pass null for HTML since we're not extracting it here anymore
          this.updateStreamingContentAndMessage(this.state.streamingContent, null);
          
          // Call the callback (which will parse HTML and update preview)
          callback();
        } else if (!this.state.isLoading) {
          // If we're no longer loading, clear the interval
          console.log('No longer loading, clearing interval');
          this.clearUpdateInterval();
        }
      }, WAIT_DURATION);
    } else {
      console.log('Not loading, not starting interval');
    }
  }

  @action
  async createDesignSession(prompt: string, options?: {isFirstLoad: boolean}): Promise<string | null> {
    try {
      // Create a new design session on the server
      const response = await fetch(`/api/project/${this.state.projectId}/chat/${this.state.chatId}/design-sessions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, isFirstLoad: options?.isFirstLoad || false })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create design session');
      }

      const data = await response.json();
      const { sessionId } = data;
      
      // Set loading state
      this.setIsLoading(true);
      
      // Add an initial assistant message that will be updated as content streams in
      const initialAssistantMessage: Message = {
        id: `design-${sessionId}`,
        role: 'assistant',
        content: 'Generating your design...',
      };
      
      this.setMessages([...this.state.messages, initialAssistantMessage]);

      // Update the session ID in the store
      this.setSessionId(sessionId);
      
      // Create a stream client to receive updates
      const streamClient = new DesignStreamClient(
        sessionId,
        this.state.projectId,
        this.state.chatId,
        (data: { content?: string; html?: string; status: string }) => {
          // Handle incoming data from the stream
          if (data.content) {
            // Update the streaming content and message
            this.updateStreamingContentAndMessage(data.content, data.html || null);
          }
          
          // Update loading state based on status
          if (data.status === 'complete' || data.status === 'error') {
            this.setIsLoading(false);
          }
        },
        (error: Error) => {
          console.error('Stream error:', error);
          this.setError(error.message);
          this.setIsLoading(false);
        },
        () => {
          // Stream complete
          this.setIsLoading(false);
          console.log('Design generation complete');
        }
      );
      
      // Connect to the stream
      streamClient.connect();

      return sessionId;
    } catch (error) {
      console.error('Error creating design session:', error);
      toast.error('Failed to create design session');
      return null;
    }
  }

  @action
  clearUpdateInterval() {
    if (this.updateIntervalId) {
      clearInterval(this.updateIntervalId);
      this.updateIntervalId = null;
    }
  }
  
  @action
  updateStreamingContentAndMessage(content: string, html: string | null) {
    // Update the streaming content
    this.setStreamingContent(content);
    
    // If we have HTML, update the preview HTML
    if (html) {
      this.setPreviewHtml(html);
    }
    
    // Find and update the assistant message for this session
    const messageId = `design-${this.state.sessionId}`;
    const updatedMessages = this.state.messages.map(message => {
      if (message.id === messageId) {
        return {
          ...message,
          content: content
        };
      }
      return message;
    });
    
    this.setMessages(updatedMessages);
  }

  @action
  parseStreamingContent(content: string): string | null {
    // This function should only be called through the debounced version
    // Log the timestamp to verify debouncing is working
    console.log('Parsing streaming content at:', new Date().toISOString());
    // Check if we have a complete HTML document with mobile frames
    const htmlMatch = content.match(/<PREVIEW_HTML>([\s\S]*?)<\/PREVIEW_HTML>/);
    if (htmlMatch) {
      let extractedHtml = htmlMatch[1];

      // Ensure the HTML has a proper doctype and HTML structure
      if (!extractedHtml.trim().startsWith('<!DOCTYPE html>')) {
        // Do not add additional mobile frame wrapping - the content already has it
        extractedHtml = `<!DOCTYPE html>\n<html>\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com; img-src 'self' data: blob: https: http:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;">\n<script src="https://cdn.tailwindcss.com"></script>\n</head>\n<body>\n${extractedHtml}\n</body>\n</html>`;
      } else if (!extractedHtml.includes('tailwindcss')) {
        // If it already has DOCTYPE but missing Tailwind, add it
        extractedHtml = extractedHtml.replace('<head>', `<head>\n<meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com; img-src 'self' data: blob: https: http:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;">\n<script src="https://cdn.tailwindcss.com"></script>`);
      }

      // Only update if the content has actually changed
      if (this.state.previewHtml !== extractedHtml) {
        console.log('Updating previewHtml with new content');
        runInAction(() => {
          this.state.previewHtml = extractedHtml;
        });
      } else {
        console.log('HTML content unchanged, skipping update');
      }
      return extractedHtml;
    }

    // If we don't have a complete match yet, try to render partial content
    const partialContent = content;

    // Look for any HTML-like content to display
    if (partialContent.includes('<div') || partialContent.includes('<html') || partialContent.includes('<body')) {
      // Create a complete HTML document with the partial content
      let wrappedContent;

      if (partialContent.includes('<html')) {
        // If it already has HTML structure, use it as is
        wrappedContent = partialContent;
      } else if (partialContent.includes('<body')) {
        // If it has a body tag but no html, wrap it in html
        wrappedContent = `<!DOCTYPE html>\n<html>\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com; img-src 'self' data: blob: https: http:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;">\n<script src="https://cdn.tailwindcss.com"></script>\n</head>\n${partialContent}\n</html>`;
      } else {
        // If it just has divs or other content, wrap it in a complete structure
        // Do not add additional mobile frame wrapping - the content will be wrapped by design-generator.ts
        wrappedContent = `<!DOCTYPE html>\n<html>\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com; img-src 'self' data: blob: https: http:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;">\n<script src="https://cdn.tailwindcss.com"></script>\n</head>\n<body>\n${partialContent}\n</body>\n</html>`;
      }

      // Only update if the content has actually changed
      if (this.state.previewHtml !== wrappedContent) {
        console.log('Updating previewHtml with new wrapped content');
        runInAction(() => {
          this.state.previewHtml = wrappedContent;
        });
      } else {
        console.log('Wrapped HTML content unchanged, skipping update');
      }
      return wrappedContent;
    }

    return null;
  }

  @action
  createBlobUrl(htmlContent: string): string {
    // Clean up previous blob URL if it exists
    if (this.currentBlobUrl) {
      URL.revokeObjectURL(this.currentBlobUrl);
    }

    // Create a new blob URL
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const blobUrl = URL.createObjectURL(blob);
    this.currentBlobUrl = blobUrl;

    return blobUrl;
  }

  @action
  cleanup() {
    // Clean up any blob URLs
    if (this.currentBlobUrl) {
      URL.revokeObjectURL(this.currentBlobUrl);
      this.currentBlobUrl = null;
    }

    // Clear any intervals
    this.clearUpdateInterval();

    // Cancel any pending debounced calls
    if (this.debouncedParseStreamingContent.cancel) {
      this.debouncedParseStreamingContent.cancel();
    }
  }
}
