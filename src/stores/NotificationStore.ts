import { makeAutoObservable, observable, action } from 'mobx';
import { RootStore } from './RootStore';

export type NotificationType = 'info' | 'success' | 'warning' | 'error';

export interface Notification {
  id?: string;
  title: string;
  message: string;
  type: NotificationType;
  duration?: number;
}

export class NotificationStore {
  @observable notifications: Notification[] = [];
  rootStore: RootStore;

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this);
  }

  @action
  showNotification(notification: Notification) {
    const id = Math.random().toString(36).substring(2, 9);
    const newNotification = { ...notification, id };
    
    this.notifications.push(newNotification);
    
    // Auto-dismiss notification after duration (default: 3000ms)
    if (notification.duration !== 0) {
      setTimeout(() => {
        this.dismissNotification(id);
      }, notification.duration || 3000);
    }
    
    return id;
  }

  @action
  dismissNotification(id: string) {
    this.notifications = this.notifications.filter(notification => notification.id !== id);
  }

  @action
  clearAllNotifications() {
    this.notifications = [];
  }
}
