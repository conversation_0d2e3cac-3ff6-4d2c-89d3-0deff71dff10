import { makeAutoObservable, runInAction } from 'mobx';
import { RootStore } from '@/stores/RootStore';
import { Project } from '@/lib/db/schema';

export interface EdgeFunction {
  id: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  version: number;
}

export interface SupabaseResource {
  id: string;
  name: string;
  type: 'function' | 'table' | 'bucket';
  details: any;
  status: string;
}

type SupabaseFunction = {
  id: string;
  name: string;
  status: string;
  version: number;
  created_at: string;
  updated_at: string;
};

type FunctionDetails = {
  logs: any[];
  loading: boolean;
  error: string | null;
};

export class SupabaseStore {
  private rootStore: RootStore;
  
  // State
  functions: Map<string, SupabaseFunction[]> = new Map();
  loadingFunctions: Map<string, boolean> = new Map();
  functionError: Map<string, string | null> = new Map();
  
  // Track which projects have had their data loaded
  loadedProjects: Set<string> = new Set();

  // Store function details (logs, etc.) by function ID
  functionDetails: Map<string, FunctionDetails> = new Map();

  // Track if the tab has been opened
  tabOpened: boolean = false;

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this);
  }
  
  // Mark the tab as opened
  setTabOpened() {
    this.tabOpened = true;
  }

  // Getters
  getFunctions(projectId: string): EdgeFunction[] {
    return this.functions.get(projectId) || [];
  }

  isLoadingFunctions(projectId: string): boolean {
    return this.loadingFunctions.get(projectId) || false;
  }

  getFunctionError(projectId: string): string | null {
    return this.functionError.get(projectId) || null;
  }

  // Check if project data has been loaded
  hasLoadedProject(projectId: string): boolean {
    return this.loadedProjects.has(projectId);
  }
  
  // Get function details by ID
  getFunctionDetails(functionId: string): FunctionDetails | undefined {
    return this.functionDetails.get(functionId);
  }
  
  // Fetch logs for a specific function
  async fetchFunctionLogs(projectId: string, functionId: string) {
    // Initialize function details if not exists
    if (!this.functionDetails.has(functionId)) {
      runInAction(() => {
        this.functionDetails.set(functionId, {
          logs: [],
          loading: true,
          error: null
        });
      });
    } else {
      runInAction(() => {
        const details = this.functionDetails.get(functionId);
        if (details) {
          this.functionDetails.set(functionId, {
            ...details,
            loading: true,
            error: null
          });
        }
      });
    }
    
    try {
      const response = await fetch(`/api/integrations/supabase/logs?projectId=${projectId}&functionId=${functionId}&service=edge-function`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch function logs');
      }
      
      const data = await response.json();
      
      runInAction(() => {
        const details = this.functionDetails.get(functionId);
        if (details) {
          this.functionDetails.set(functionId, {
            ...details,
            logs: data, // Store the entire response which contains logs.result
            loading: false
          });
        }
      });
      
      return data.logs;
    } catch (error) {
      console.error('Error fetching function logs:', error);
      runInAction(() => {
        const details = this.functionDetails.get(functionId);
        if (details) {
          this.functionDetails.set(functionId, {
            ...details,
            loading: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      });
      return [];
    }
  }

  // Actions
  async fetchFunctions(project: Project) {
    if (!project.supabaseProjectId || !project.connectionId) {
      runInAction(() => {
        this.functionError.set(project.id, 'Project is not connected to Supabase');
      });
      return;
    }

    runInAction(() => {
      this.loadingFunctions.set(project.id, true);
      this.functionError.set(project.id, null);
    });

    try {
      const response = await fetch(`/api/integrations/supabase/resources?projectId=${project.id}&type=functions`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch Supabase functions');
      }
      
      const data = await response.json();
      
      runInAction(() => {
        this.functions.set(project.id, data.functions);
        this.loadedProjects.add(project.id);
      });
    } catch (error) {
      console.error('Error fetching Supabase functions:', error);
      runInAction(() => {
        this.functionError.set(project.id, error instanceof Error ? error.message : 'Unknown error');
      });
    } finally {
      runInAction(() => {
        this.loadingFunctions.set(project.id, false);
      });
    }
  }

  // Reset state for a project
  resetProject(projectId: string) {
    this.functions.delete(projectId);
    this.loadingFunctions.delete(projectId);
    this.functionError.delete(projectId);
    this.loadedProjects.delete(projectId);
  }
}
