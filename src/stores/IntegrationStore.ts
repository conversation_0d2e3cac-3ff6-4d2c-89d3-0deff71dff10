import {action, makeAutoObservable, runInAction} from 'mobx';
import {RootStore} from '@/stores/RootStore';
import {Connection} from '@/lib/db/schema';

export interface IntegrationProvider {
    id: string;
    name: string;
    logo: string;
    description: string;
    connected: boolean;
}

export interface Database {
    id: string;
    name: string;
    status: string;
}

export interface ProjectLinkState {
    isLinking: boolean;
    error: string | null;
    success: boolean;
}

export class IntegrationStore {
    private rootStore: RootStore;
    connections: Map<string, Connection> = new Map();
    databases: Map<string, Database[]> = new Map();
    loadingConnections: Set<string> = new Set();
    loadingDatabases: Set<string> = new Set();
    currentSelectedProjectId: string | null = null;
    shouldSendMessage: boolean = false;

    projectLinkState: Map<string, ProjectLinkState> = new Map();

    providers: IntegrationProvider[] = [
        {
            id: 'supabase',
            name: 'Supabase',
            logo: '/icons/integrations/supabase.png',
            description: 'Connect your Supabase project to deploy databases for your mobile apps.',
            connected: false
        },
        // Add more providers here
    ];

    constructor(rootStore: RootStore) {
        this.rootStore = rootStore;
        makeAutoObservable(this);
    }

    getConnection(provider: string) {
        return this.connections.get(provider);
    }

    getDatabases(provider: string) {
        return this.databases.get(provider) || [];
    }

    isLoadingConnection(provider: string) {
        return this.loadingConnections.has(provider);
    }

    isLoadingDatabases(provider: string) {
        return this.loadingDatabases.has(provider);
    }

    async fetchConnection(provider: string) {
        runInAction(() => this.loadingConnections.add(provider));
        try {
            const response = await fetch(`/api/integrations/${provider}?status=check`);
            if (!response.ok) {
                if (response.status === 404) {
                    runInAction(() => this.connections.delete(provider));
                    return null;
                }
                throw new Error('Failed to fetch connection');
            }
            const connection = await response.json();
            runInAction(() => this.connections.set(provider, connection));
            return connection;
        } catch (error) {
            console.error('Failed to fetch connection:', error);
            return null;
        } finally {
            runInAction(() => this.loadingConnections.delete(provider));
        }
    }

    async fetchDatabases(provider: string) {
        if (!this.getConnection(provider)) return;

        runInAction(() => this.loadingDatabases.add(provider));
        try {
            const response = await fetch(`/api/integrations/${provider}/databases`);
            if (!response.ok) throw new Error('Failed to fetch databases');
            const databases = await response.json();
            runInAction(() => this.databases.set(provider, databases));
            return databases;
        } catch (error) {
            console.error('Failed to fetch databases:', error);
            return [];
        } finally {
            runInAction(() => this.loadingDatabases.delete(provider));
        }
    }

    async disconnect(provider: string) {
        try {
            const response = await fetch(`/api/integrations/${provider}`, {
                method: 'DELETE',
            });
            if (!response.ok) throw new Error('Failed to disconnect');
            runInAction(() => {
                this.connections.delete(provider);
                this.databases.delete(provider);
            });
        } catch (error) {
            console.error('Failed to disconnect:', error);
            throw error;
        }
    }

    getAuthUrl(provider: string) {
        return `/api/integrations/${provider}`;
    }

    getProjectLinkState(chatId: string): ProjectLinkState {
        return this.projectLinkState.get(chatId) || {
            isLinking: false,
            error: null,
            success: false,
        };
    }

    @action
    setShouldSendMessage(value: boolean) {
        this.shouldSendMessage = value;
    }

    @action
    resetCurrentSelectedProjectId() {
        this.currentSelectedProjectId = null;
    }

    @action
    setProviderConnectionStatus(provider: string, connected: boolean) {
        if(this.providers[provider]) {
            this.providers[provider].connected = connected;
        } else {
            console.warn(`Provider ${provider} does exist. Trying to set connection status.`)
        }
    }

    @action
    async linkProject(chatId: string, projectId: string) {
        this.currentSelectedProjectId = projectId;
        this.shouldSendMessage = true;
        // Let's figure out a way to basically send a message with the correct params

        // runInAction(() => {
        //     this.projectLinkState.set(chatId, {
        //         isLinking: true,
        //         error: null,
        //         success: false,
        //     });
        // });

        // try {
        //     const response = await fetch(`/api/chat/${chatId}/supabase`, {
        //         method: 'POST',
        //         headers: {'Content-Type': 'application/json'},
        //         body: JSON.stringify({projectRef: projectId}),
        //     });
        //
        //     if (!response.ok) {
        //         throw new Error('Failed to link project');
        //     }
        //
        //     runInAction(() => {
        //         this.projectLinkState.set(chatId, {
        //             isLinking: false,
        //             error: null,
        //             success: true,
        //         });
        //     });
        //
        //     return await response.json();
        // } catch (error) {
        //     const errorMessage = error instanceof Error ? error.message : 'Failed to link project';
        //     runInAction(() => {
        //         this.projectLinkState.set(chatId, {
        //             isLinking: false,
        //             error: errorMessage,
        //             success: false,
        //         });
        //     });
        //     throw error;
        // }
    }
}
