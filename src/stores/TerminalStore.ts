import { makeAutoObservable, runInAction } from 'mobx';
import { terminalService, TerminalOutputListener, TerminalErrorListener, TerminalStatusListener } from '@/services/TerminalService';

export interface TerminalState {
  sessionId: string | null;
  isConnected: boolean;
  isInitialized: boolean;
  output: string[];
  error: string | null;
  isLoading: boolean;
}

class TerminalStore {
  state: TerminalState = {
    sessionId: null,
    isConnected: false,
    isInitialized: false,
    output: [],
    error: null,
    isLoading: false,
  };

  private outputListener: TerminalOutputListener | null = null;
  private errorListener: TerminalErrorListener | null = null;
  private statusListener: TerminalStatusListener | null = null;

  constructor() {
    makeAutoObservable(this);
  }

  /**
   * Initialize a new terminal session
   */
  async initializeSession(existingSessionId?: string): Promise<void> {
    if (this.state.isLoading) {
      return;
    }

    try {
      runInAction(() => {
        this.state.isLoading = true;
        this.state.error = null;
      });

      // Connect to the terminal service
      await terminalService.connect();

      // Create a new session
      const sessionId = await terminalService.createSession(existingSessionId);

      // Set up listeners
      this.setupListeners(sessionId);

      runInAction(() => {
        this.state.sessionId = sessionId;
        this.state.isConnected = true;
        this.state.isInitialized = true;
        this.state.isLoading = false;
      });
    } catch (error) {
      runInAction(() => {
        this.state.error = (error as Error).message;
        this.state.isLoading = false;
      });
      console.error('Failed to initialize terminal session:', error);
    }
  }

  /**
   * Execute a command in the terminal
   */
  async executeCommand(command: string): Promise<void> {
    if (!this.state.sessionId || !this.state.isConnected || !this.state.isInitialized) {
      throw new Error('Terminal session not initialized');
    }

    try {
      // Add command to output with a prefix
      runInAction(() => {
        this.state.output.push(`$ ${command}`);
      });

      // Execute the command
      await terminalService.executeCommand(this.state.sessionId, command);
    } catch (error) {
      runInAction(() => {
        this.state.error = (error as Error).message;
      });
      console.error('Failed to execute command:', error);
    }
  }

  /**
   * Send raw input directly to the terminal
   * This is used for interactive terminal input
   */
  sendRawInput(data: string): void {
    if (!this.state.sessionId || !this.state.isConnected || !this.state.isInitialized) {
      console.error('Terminal session not initialized');
      return;
    }

    try {
      // Send the raw input to the terminal service
      // Note: We don't add this to our local output since it will be echoed back by the PTY
      terminalService.sendRawInput(this.state.sessionId, data);
    } catch (error) {
      console.error('Failed to send raw input:', error);
      runInAction(() => {
        this.state.error = (error as Error).message;
      });
    }
  }

  /**
   * Close the terminal session
   */
  async closeSession(): Promise<void> {
    if (!this.state.sessionId) {
      return;
    }

    try {
      // Remove listeners
      this.removeListeners();

      // Close the session
      await terminalService.closeSession(this.state.sessionId);

      runInAction(() => {
        this.state.sessionId = null;
        this.state.isConnected = false;
        this.state.isInitialized = false;
        this.state.output = [];
        this.state.error = null;
      });
    } catch (error) {
      runInAction(() => {
        this.state.error = (error as Error).message;
      });
      console.error('Failed to close terminal session:', error);
    }
  }

  /**
   * Clear the terminal output
   */
  clearOutput(): void {
    runInAction(() => {
      this.state.output = [];
    });
  }

  /**
   * Clear the error message
   */
  clearError(): void {
    runInAction(() => {
      this.state.error = null;
    });
  }

  /**
   * Add a direct output listener that will receive terminal output directly
   * This is useful for components that need to handle terminal output in real-time
   */
  addDirectOutputListener(listener: TerminalOutputListener): void {
    if (!this.state.sessionId) {
      console.error('Cannot add output listener: no active session');
      return;
    }
    
    terminalService.addOutputListener(this.state.sessionId, listener);
  }
  
  /**
   * Remove a direct output listener
   */
  removeDirectOutputListener(listener: TerminalOutputListener): void {
    if (!this.state.sessionId) {
      return;
    }
    
    terminalService.removeOutputListener(this.state.sessionId, listener);
  }

  /**
   * Set up listeners for terminal events
   */
  private setupListeners(sessionId: string): void {
    // Remove any existing listeners
    this.removeListeners();

    // Set up output listener
    this.outputListener = (data: string) => {
      console.log('TerminalStore received output:', data);
      runInAction(() => {
        // Add the output to the state
        // We're using an array for output history, but we could optimize this
        // to prevent excessive memory usage for long-running sessions
        if (this.state.output.length > 1000) {
          // Limit the number of output entries to prevent memory issues
          this.state.output = this.state.output.slice(-500);
        }
        this.state.output.push(data);
      });
    };
    terminalService.addOutputListener(sessionId, this.outputListener);

    // Set up error listener
    this.errorListener = (error: string) => {
      runInAction(() => {
        this.state.error = error;
      });
    };
    terminalService.addErrorListener(sessionId, this.errorListener);

    // Set up status listener
    this.statusListener = (status: { connected: boolean; initialized: boolean }) => {
      runInAction(() => {
        this.state.isConnected = status.connected;
        this.state.isInitialized = status.initialized;
      });
    };
    terminalService.addStatusListener(sessionId, this.statusListener);
  }

  /**
   * Remove listeners for terminal events
   */
  private removeListeners(): void {
    if (!this.state.sessionId) {
      return;
    }

    if (this.outputListener) {
      terminalService.removeOutputListener(this.state.sessionId, this.outputListener);
      this.outputListener = null;
    }

    if (this.errorListener) {
      terminalService.removeErrorListener(this.state.sessionId, this.errorListener);
      this.errorListener = null;
    }

    if (this.statusListener) {
      terminalService.removeStatusListener(this.state.sessionId, this.statusListener);
      this.statusListener = null;
    }
  }
}

// Export singleton instance
export const terminalStore = new TerminalStore();
