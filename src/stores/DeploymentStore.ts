import { action, makeAutoObservable, observable, runInAction } from "mobx";
import { RootStore } from "./RootStore";
import { incrementVersion } from "@/lib/db/deployment-queries";

export type DeploymentPlatform = 'web' | 'android' | 'ios';

export type PlatformFeatureStatus = 'available' | 'coming_soon' | 'beta';
export type DeploymentStatus = 'idle' | 'queued' | 'processing' | 'deploying' | 'completed' | 'success' | 'failed';

export interface Deployment {
  id: string;
  buildId?: string;
  platform: DeploymentPlatform;
  url?: string;
  slug?: string; // Subdomain for web deployments (slug.web.magically.life)
  status: DeploymentStatus;
  version: string;
  createdAt: Date;
  error?: string;
  fileStateId?: string;
  dbRecordId?: string; // Reference to the database record ID
}

export class DeploymentStore {
  rootStore: RootStore;

  @observable deploymentDialogOpen = false;
  @observable selectedPlatform: DeploymentPlatform = 'android';
  @observable isDeploying = false;

  // Platform features availability
  @observable platformFeatures = {
    web: {
      deploy: 'available' as PlatformFeatureStatus
    },
    android: {
      deploy: 'available' as PlatformFeatureStatus,
      apkDownload: 'available' as PlatformFeatureStatus,
      playStore: 'coming_soon' as PlatformFeatureStatus
    },
    ios: { deploy: 'coming_soon' as PlatformFeatureStatus }
  };
  
  // Map of projectId to list of deployments
  @observable deployments = new Map<string, Deployment[]>();

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    this.deployments = new Map<string, Deployment[]>();
    makeAutoObservable(this);
  }

  @action
  toggleDeploymentDialog(open: boolean) {
    this.deploymentDialogOpen = open;
  }

  @action
  setSelectedTarget(platform: DeploymentPlatform) {
    this.selectedPlatform = platform;
  }

  @action
  async deployProject(projectId: string, platform: DeploymentPlatform) {
    // This method is now used for publishing projects
    // Web deployment is now available, so we don't need to check for coming_soon status
    
    if (platform === 'ios' && this.platformFeatures.ios.deploy === 'coming_soon') {
      // Show notification for coming soon feature
      if (this.rootStore.notificationStore) {
        this.rootStore.notificationStore.showNotification({
          title: 'iOS Publishing',
          message: 'Publishing to iOS is coming soon!',
          type: 'info',
          duration: 3000
        });
      }
      return { status: 'coming_soon' };
    }
    
    if (this.isDeploying) {
      // Show notification for already deploying
      if (this.rootStore.notificationStore) {
        this.rootStore.notificationStore.showNotification({
          title: 'Publishing in Progress',
          message: 'Your app is already being published. Please wait.',
          type: 'warning',
          duration: 3000
        });
      }
      return { status: 'already_deploying' };
    }
    
    this.isDeploying = true;
    
    try {
      // Show notification for starting deployment
      if (this.rootStore.notificationStore) {
        this.rootStore.notificationStore.showNotification({
          title: 'Publishing Started',
          message: `Publishing your app to ${platform}...`,
          type: 'info',
          duration: 3000
        });
      }
      
      // Get the latest version number for this project
      const latestDeployments = this.getDeployments(projectId);
      const latestVersion = latestDeployments.length > 0 
        ? latestDeployments.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0].version
        : '1.0.0';
      
      // Increment the version number (patch by default)
      const newVersion = incrementVersion(latestVersion, 'patch');

      // Create a new deployment record
      const deployment: Deployment = {
        id: crypto.randomUUID(),
        platform: platform,
        status: 'queued',
        version: newVersion,
        createdAt: new Date(),
      };
      
      // Add to deployments map
      const projectDeployments = this.getDeployments(projectId);
      this.deployments.set(projectId, [...projectDeployments, deployment]);
      
      // Call the API to deploy the project
      const response = await fetch(`/api/project/${projectId}/deploy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          platform: platform, // Ensure we're using the correct parameter name (platform instead of platform)
          version: newVersion,
          deploymentId: deployment.id
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Deployment failed');
      }
      
      // Update with database record ID
      if (data.dbRecordId) {
        deployment.dbRecordId = data.dbRecordId;
      }
      
      // If this is a build that needs status polling (android)
      if (data.buildId) {
        // Update the deployment record with the build ID and status
        this.updateDeploymentStatus(
            projectId,
          deployment.id, 
          data.status as DeploymentStatus, 
          data.url,
          undefined,
          data.buildId,
          data.fileStateId
        );
        
        // Start polling for build status
        this.pollBuildStatus(projectId, deployment.id, data.buildId);
        
        return { 
          status: 'queued', 
          deploymentId: deployment.id,
          version: newVersion
        };
      }
      
      // For immediate deployments or coming soon features
      if (data.status === 'coming_soon') {
        this.updateDeploymentStatus(projectId, deployment.id, 'completed', undefined);
        return { status: 'coming_soon' };
      }
      
      // For successful deployments
      this.updateDeploymentStatus(
          projectId,
        deployment.id, 
        'success', 
        data.url,
        undefined,
        undefined,
        data.fileStateId
      );
      
      return { 
        status: 'success', 
        url: data.url,
        version: newVersion
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Deployment error:', error);
      
      return { 
        status: 'error',
        error: errorMessage 
      };
    } finally {
      this.isDeploying = false;
    }
  }

  @action
  private updateDeploymentStatus(
      projectId: string,
    deploymentId: string, 
    status: DeploymentStatus, 
    url?: string, 
    error?: string,
    buildId?: string,
    fileStateId?: string
  ) {
    const projectDeployments = this.deployments.get(projectId) || [];
    const updatedDeployments = projectDeployments.map(deployment => {
      if (deployment.id === deploymentId) {
        const updatedDeployment = {
          ...deployment,
          status,
          error,
          buildId: buildId || deployment.buildId,
          fileStateId: fileStateId || deployment.fileStateId
        };
        
        if (url) updatedDeployment.url = url;
        
        // Update the database record if we have a dbRecordId
        // if (deployment.dbRecordId) {
        //   this.updateDeploymentInDatabase(deployment.dbRecordId, projectId, status, url, error);
        // }
        
        return updatedDeployment;
      }
      return deployment;
    });
    
    this.deployments.set(projectId, updatedDeployments);
  }

  @action
  getDeployments(projectId: string): Deployment[] {
    if (!this.deployments) {
      this.deployments = new Map<string, Deployment[]>();
    }
    return this.deployments.get(projectId) || [];
  }

  @action
  isPlatformAvailable(platform: DeploymentPlatform): boolean {
    return this.platformFeatures[platform]?.deploy === 'available';
  }

  @action
  getLatestDeployment(projectId: string, platform: DeploymentPlatform): Deployment | undefined {
    const deployments = this.getDeployments(projectId);
    return deployments
      .filter(d => d.platform === platform)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];
  }
  
  @action
  getLatestDeploymentUrl(projectId: string, platform: DeploymentPlatform): string | undefined {
    const latest = this.getLatestDeployment(projectId, platform);
    return latest?.url;
  }
  
  @action
  getDeploymentStatus(projectId: string, platform: DeploymentPlatform): DeploymentStatus | undefined {
    const latest = this.getLatestDeployment(projectId, platform);
    return latest?.status;
  }
  
  // Poll for build status updates
  private async pollBuildStatus(projectId: string, deploymentId: string, buildId: string) {
    const maxAttempts = 60; // Maximum number of polling attempts (5 minutes with 5-second intervals)
    const interval = 5000; // Poll every 5 seconds
    let attempts = 0;
    
    // Log polling start
    console.log(`Starting build status polling for buildId: ${buildId}, deploymentId: ${deploymentId}`);
    
    const checkStatus = async () => {
      try {
        // Check if we've reached max attempts
        if (attempts >= maxAttempts) {
          console.log(`Polling timed out after ${maxAttempts} attempts for buildId: ${buildId}`);
          this.updateDeploymentStatus(projectId, deploymentId, 'failed', undefined, 'Build timed out');
          return;
        }
        
        // Get the current deployment
        const projectDeployments = this.deployments.get(projectId) || [];
        const deployment = projectDeployments.find(d => d.id === deploymentId);
        
        // If deployment doesn't exist or is already in a final state, stop polling
        if (!deployment || ['success', 'completed', 'failed'].includes(deployment.status)) {
          console.log(`Stopping polling for buildId: ${buildId} - deployment status: ${deployment?.status || 'not found'}`);
          return;
        }
        
        console.log(`Polling attempt ${attempts + 1} for buildId: ${buildId}`);
        
        // Fetch build status
        const response = await fetch(`/api/project/${projectId}/deploy/status?buildId=${buildId}`);
        
        if (!response.ok) {
          console.error(`Error response from status API: ${response.status} ${response.statusText}`);
          attempts++;
          setTimeout(checkStatus, interval);
          return;
        }
        
        const data = await response.json();
        
        // Update deployment status based on build status
        runInAction(() => {
          if (data.status === 'completed') {
            this.updateDeploymentStatus(
                projectId,
              deploymentId, 
              'completed', // Match the status in the database schema
              data.apkUrl || data.url
            );
            
            // Show success notification
            if (data.apkUrl) {
              // Use the rootStore to show a notification if available
              if (this.rootStore.notificationStore) {
                this.rootStore.notificationStore.showNotification({
                  title: 'Build Completed',
                  message: 'Your APK is ready for download',
                  type: 'success',
                  duration: 5000
                });
              }
            }
          } else if (data.status === 'failed') {
            this.updateDeploymentStatus(
                projectId,
              deploymentId, 
              'failed', 
              undefined, 
              data.error || 'Build failed'
            );
            
            // Show error notification
            if (this.rootStore.notificationStore) {
              this.rootStore.notificationStore.showNotification({
                title: 'Build Failed',
                message: data.error || 'Build process failed',
                type: 'error',
                duration: 5000
              });
            }
          } else {
            // Still in progress, update status and continue polling
            this.updateDeploymentStatus(
                projectId,
              deploymentId, 
              data.status as DeploymentStatus
            );
            
            attempts++;
            setTimeout(checkStatus, interval);
          }
        });
      } catch (error) {
        console.error('Error polling build status:', error);
        attempts++;
        setTimeout(checkStatus, interval);
      }
    };
    
    // Start polling
    checkStatus();
  }
  
  // Update deployment record in the database
  private async updateDeploymentInDatabase(
    dbRecordId: string,
    projectId: string,
    status: string,
    url?: string,
    error?: string
  ) {
    try {
      console.log(`Updating deployment in database: ID=${dbRecordId}, status=${status}`);
      
      const response = await fetch(`/api/project/${projectId}/deploy/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: dbRecordId, // Make sure we're using the correct parameter name
          status,
          url,
          error,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API returned ${response.status}: ${JSON.stringify(errorData)}`);
      }
      
      console.log(`Successfully updated deployment ${dbRecordId} to status: ${status}`);
    } catch (error) {
      console.error('Failed to update deployment in database:', error);
    }
  }
  
  // Load deployments from the database
  @action
  async loadDeploymentsFromDatabase(projectId: string) {
    try {
      console.log(`Loading deployments for projectId: ${projectId}`);
      
      const response = await fetch(`/api/project/${projectId}/deploy/list`);
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`Failed to load deployments: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      
      console.log(`Loaded ${data.deployments?.length || 0} deployments from database`);
      
      // Convert database records to Deployment objects
      const deployments: Deployment[] = (data.deployments || []).map((record: any) => ({
        id: record.id, // Use the database ID as the local ID
        dbRecordId: record.id,
        platform: record.platform as DeploymentPlatform,
        status: record.status as DeploymentStatus,
        version: record.version,
        url: record.url,
        slug: record.slug, // Add slug for web deployments
        buildId: record.buildId,
        fileStateId: record.fileStateId,
        createdAt: new Date(record.createdAt || record.created_at),
        error: record.error,
      }));
      
      // Update the deployments map
      this.deployments.set(projectId, deployments);
      
      // We're no longer automatically polling for in-progress builds
      // This prevents excessive API calls
      
      return deployments;
    } catch (error) {
      console.error('Error loading deployments:', error);
      
      // Show error notification
      if (this.rootStore.notificationStore) {
        this.rootStore.notificationStore.showNotification({
          title: 'Error Loading Deployments',
          message: error instanceof Error ? error.message : 'Failed to load deployments',
          type: 'error',
          duration: 5000
        });
      }
      
      return [];
    }
  }
}
