// import React, { createContext, useContext } from 'react';
// import { RootStore } from './RootStore';
// import rootStore from './RootStore';
//
// // Create a context for the store
// const StoreContext = createContext<RootStore | undefined>(undefined);
//
// // Provider component
// export const StoreProvider: React.FC<{
//   children: React.ReactNode;
//   store?: RootStore;
// }> = ({ children, store = rootStore }) => {
//   return (
//     <StoreContext.Provider value={store}>
//       {children}
//     </StoreContext.Provider>
//   );
// };
//
// // Hook to use the store
// export const useStore = (): RootStore => {
//   const context = useContext(StoreContext);
//   if (context === undefined) {
//     throw new Error('useStore must be used within a StoreProvider');
//   }
//   return context;
// };
