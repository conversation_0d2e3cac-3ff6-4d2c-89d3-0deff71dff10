interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'general' | 'technical' | 'billing' | 'security';
}

export const faqs: FAQ[] = [
  {
    id: 'what-is-credit',
    category: 'billing',
    question: 'What is a message in magically?',
    answer: '1 message represents a single message sent to the magically AI. This includes file creation, editing, deletion, writing SQL queries, fetching file contexts, or any tool call that the AI needs to make to respond to your requests. We offer unlimited auto error fixes.'
  },
  {
    id: 'credit-usage',
    category: 'billing',
    question: 'How are messages consumed?',
    answer: 'Each message sent to the AI is counted as 1 message. For example, when the AI creates a new file, edits existing code, writes a database query,  fetches context from your codebase or chats with you, each of these operations uses 1 message.'
  },
  {
    id: 'free-plan-limits',
    category: 'billing',
    question: 'What are the limits of the free plan?',
    answer: 'The free plan has both daily and monthly limits. You get 35 messages per month, with a maximum of 4 messages per day. The free plan also has limitations including no code download, no deployments, and only basic agent mode functionality.'
  },
  {
    id: 'paid-plan-limits',
    category: 'billing',
    question: 'Do paid plans have daily limits?',
    answer: 'No, paid plans don\'t have daily limits. The Starter plan includes 60 messages per month, Pro plan includes 250 credits per month, and Plus plan includes 500 credits per month.'
  },
  {
    id: 'credit-reset',
    category: 'billing',
    question: 'When do my messages reset?',
    answer: 'Messages reset at the beginning of each billing cycle. For monthly plans, this means you get a fresh allocation of messages on the same day each month. Unused messages do not roll over to the next billing cycle.'
  },
  {
    id: 'unlimited-messages',
    category: 'billing',
    question: 'What does "unlimited auto error fixes" mean for paid plans?',
    answer: 'Paid plans offer unlimited auto error fixes with the AI. This means AI will optimistically try to automatically fix any issues that may have occurred with your project. This operation is free of cost.'
  },
  {
    id: 'getting-started',
    category: 'general',
    question: 'How do I get started with Magically?',
    answer: 'Simply sign up and describe the app you want to build. Our AI will help you create a fully functional web or mobile app in minutes. No coding experience required!'
  },
  {
    id: 'technologies',
    category: 'technical',
    question: 'What technologies do you support?',
    answer: 'We support modern web and mobile technologies including React, Next.js, and Expo for mobile development. Our AI can help you build apps using the latest industry-standard frameworks and libraries.'
  },
  {
    id: 'pricing',
    category: 'billing',
    question: 'How does pricing work?',
    answer: 'We offer flexible subscription plans starting from free tier for hobbyists to enterprise solutions for larger teams. Visit our pricing page for detailed information about plans and features.'
  },
  {
    id: 'code-ownership',
    category: 'general',
    question: 'Who owns the code generated by Magically?',
    answer: 'You retain 100% ownership of all code generated using our platform. You can export, modify, and deploy your code anywhere you want.'
  },
  {
    id: 'deployment',
    category: 'technical',
    question: 'Can I deploy my app to app stores?',
    answer: 'Yes! We provide seamless deployment to both the Apple App Store and Google Play Store. Our platform handles the build process and helps you prepare the necessary assets and configurations.'
  },
  {
    id: 'customization',
    category: 'technical',
    question: 'How customizable are the generated apps?',
    answer: 'Completely customizable! While our AI generates the initial codebase, you have full access to modify any aspect of your app. Use our built-in code editor or export the code to your preferred IDE.'
  },
  {
    id: 'data-security',
    category: 'security',
    question: 'How do you handle data security?',
    answer: 'We take security seriously. Your code and data are encrypted both in transit and at rest. We never store your sensitive information, and you can choose to self-host your application data.'
  },
  {
    id: 'support',
    category: 'general',
    question: 'What kind of support do you offer?',
    answer: 'We offer email support for all users and priority support for paid plans. Our documentation is comprehensive, and we have an active community forum for peer support.'
  },
  {
    id: 'updates',
    category: 'technical',
    question: 'How often do you update the platform?',
    answer: 'We continuously improve our platform with weekly updates. Major features are released monthly, and we maintain backward compatibility to ensure your apps keep working.'
  }
];
