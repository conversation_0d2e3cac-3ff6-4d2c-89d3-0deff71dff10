/* Safe area variables */
:root {
  --safe-area-top: env(safe-area-inset-top);
  --safe-area-right: env(safe-area-inset-right);
  --safe-area-bottom: env(safe-area-inset-bottom);
  --safe-area-left: env(safe-area-inset-left);
}

/* Safe area utilities */
.pb-safe {
  padding-bottom: var(--safe-area-bottom);
}

.pt-safe {
  padding-top: var(--safe-area-top);
}

.pl-safe {
  padding-left: var(--safe-area-left);
}

.pr-safe {
  padding-right: var(--safe-area-right);
}
