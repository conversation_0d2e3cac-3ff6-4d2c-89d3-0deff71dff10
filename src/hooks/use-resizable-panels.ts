import { useState, useCallback, useEffect, useRef } from 'react';

interface ResizablePanelsOptions {
  initialLeftWidth?: number;
  minLeftWidth?: number;
  maxLeftWidth?: number;
  totalWidth?: number;
  persistKey?: string;
}

export function useResizablePanels({
  initialLeftWidth = 240,
  minLeftWidth = 180,
  maxLeftWidth = 400,
  totalWidth,
  persistKey = 'editor',
}: ResizablePanelsOptions = {}) {
  // Try to load saved width from localStorage if persistKey is provided
  const getSavedWidth = useCallback(() => {
    if (!persistKey || typeof window === 'undefined') return initialLeftWidth;
    
    const saved = localStorage.getItem(`resizable-panel-${persistKey}`);
    return saved ? parseInt(saved, 10) : initialLeftWidth;
  }, [initialLeftWidth, persistKey]);

  const [leftWidth, setLeftWidth] = useState<number>(getSavedWidth());
  const [isDragging, setIsDragging] = useState(false);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  // Save width to localStorage when it changes
  useEffect(() => {
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`resizable-panel-${persistKey}`, leftWidth.toString());
    }
  }, [leftWidth, persistKey]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    startXRef.current = e.clientX;
    startWidthRef.current = leftWidth;
    setIsDragging(true);
    
    // Add cursor styling to the entire document during drag
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [leftWidth]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    
    // Reset cursor styling
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;
      
      const deltaX = e.clientX - startXRef.current;
      const newWidth = Math.min(
        Math.max(minLeftWidth, startWidthRef.current + deltaX),
        maxLeftWidth
      );
      
      setLeftWidth(newWidth);
    },
    [isDragging, minLeftWidth, maxLeftWidth]
  );

  // Add and remove event listeners
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
      
      // Also handle mouse leaving the window
      window.addEventListener('mouseleave', handleMouseUp);
    }
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('mouseleave', handleMouseUp);
      
      // Clean up cursor styling on unmount
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return {
    leftWidth,
    isDragging,
    handleMouseDown,
    setLeftWidth,
  };
}
