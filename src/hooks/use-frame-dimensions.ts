'use client';

import { useState, useEffect } from 'react';
import { DeviceType } from '@/components/generator/PhoneFrame';

const deviceDimensions = {
    pixel9pro: {
        aspectRatio: 2856/1280,
        name: 'Google Pixel 9 Pro',
    },
    iphone16promax: {
        aspectRatio: 2868/1320,
        name: 'iPhone 16 Pro Max',
    }
};

interface FrameDimensions {
    width: number;
    height: number;
    ready: boolean;
}

export const useFrameDimensions = (device: DeviceType, containerHeight: number): FrameDimensions => {
    const [dimensions, setDimensions] = useState<FrameDimensions>({ width: 0, height: 0, ready: false });

    useEffect(() => {
        if (containerHeight === 0) return;

        // Get the aspect ratio for the selected device
        const aspectRatio = deviceDimensions[device].aspectRatio;
        
        // Check if we're on mobile
        const isMobile = window.innerWidth < 768;
        
        // Set maximum height based on container height
        const maxHeight = containerHeight;
        
        // Calculate width based on the device's aspect ratio
        const calculatedWidth = maxHeight / aspectRatio;
        
        // Maximum width should be adjusted for mobile vs desktop
        const maxWidth = isMobile ? window.innerWidth * 0.85 : window.innerWidth * 0.9;
        
        let finalWidth: number;
        let finalHeight: number;
        
        if (calculatedWidth > maxWidth) {
            // If calculated width is too large, constrain by width
            finalWidth = maxWidth;
            finalHeight = maxWidth * aspectRatio;
        } else {
            // Otherwise use height-based calculations
            finalWidth = calculatedWidth;
            finalHeight = maxHeight;
        }
        
        // For mobile, ensure the frame fits within the viewport
        if (isMobile && finalHeight > window.innerHeight * 0.7) {
            finalHeight = window.innerHeight * 0.7;
            finalWidth = finalHeight / aspectRatio;
        }
        
        setDimensions({
            width: Math.round(finalWidth),
            height: Math.round(finalHeight),
            ready: true
        });
    }, [device, containerHeight]);

    return dimensions;
};
