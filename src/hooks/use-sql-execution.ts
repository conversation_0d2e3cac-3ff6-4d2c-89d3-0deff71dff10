import { useState, useEffect } from 'react';
import type { SQLStatus } from './use-sql-status';
import type { Message } from '@/lib/db/schema';

interface UseSqlExecutionProps {
  projectId: string;
  sqlStatuses: SQLStatus[];
  messages: Message[];
}

export function useSqlExecution({ projectId, sqlStatuses, messages }: UseSqlExecutionProps) {
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [pendingQueries, setPendingQueries] = useState<SQLStatus[]>([]);
    const [latestMessageId, setLatestMessageId] = useState<string | null>(null);
    const [processedMessageIds, setProcessedMessageIds] = useState<Set<string>>(new Set());

    // Check for new messages with pending SQL queries
    useEffect(() => {
        if (messages.length === 0) return;
        
        // Get the latest message
        const latestMessage = messages[messages.length - 1];
        
        // Skip if it's a user message or we've already processed this message
        if (latestMessage.role === 'user' || processedMessageIds.has(latestMessage.id)) {
            return;
        }
        
        // Check if there are pending SQL queries for this message
        const queriesForLatestMessage = sqlStatuses.filter(
            q => q.state === 'pending'
        );
        
        if (queriesForLatestMessage.length > 0) {
            // Set the latest message ID
            setLatestMessageId(latestMessage.id);
            
            // Set the pending queries
            setPendingQueries(queriesForLatestMessage);
            
            // Open the dialog
            setIsDialogOpen(true);
            
            // Mark this message as processed
            setProcessedMessageIds(prev => new Set([...prev, latestMessage.id]));
        }
    }, [messages, sqlStatuses, processedMessageIds]);

    const executeQuery = async (status: SQLStatus): Promise<string | null> => {
        try {
            const response = await fetch(`/api/project/${projectId}/sql`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: status.query }),
            });

            const data = await response.json();
            if (!response.ok) {
                const errorMessage = data.error || data.message || 'Failed to execute query';
                setError(errorMessage);
                return errorMessage;
            }
            
            setSuccess(true);
            setError(null);
            return null; // No error
        } catch (err: any) {
            const errorMessage = err.message || 'An unexpected error occurred';
            setError(errorMessage);
            return errorMessage;
        }
    };

    const handleDialogComplete = () => {
        setIsDialogOpen(false);
    };

    return {
        success,
        error,
        executeQuery,
        isDialogOpen,
        pendingQueries,
        latestMessageId,
        handleDialogComplete
    };
}
