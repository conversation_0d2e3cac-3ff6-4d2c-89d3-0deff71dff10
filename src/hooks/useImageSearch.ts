import { useState, useEffect } from 'react';
import { imageService } from '@/lib/services/image-service';

interface UseImageSearchOptions {
  perPage?: number;
  autoLoad?: boolean;
}

export function useImageSearch(initialQuery: string = '', options: UseImageSearchOptions = {}) {
  const [query, setQuery] = useState(initialQuery);
  const [images, setImages] = useState<Awaited<ReturnType<typeof imageService.searchImages>>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const searchImages = async (searchQuery: string = query) => {
    if (!searchQuery) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const results = await imageService.searchImages(searchQuery, {
        perPage: options.perPage
      });
      setImages(results);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch images'));
    } finally {
      setLoading(false);
    }
  };

  const getRandomImage = async (searchQuery: string = query) => {
    if (!searchQuery) return null;
    
    setLoading(true);
    setError(null);
    
    try {
      const image = await imageService.getRandomImage(searchQuery);
      return image;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch image'));
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Auto-load images if autoLoad is true
  useEffect(() => {
    if (options.autoLoad && initialQuery) {
      searchImages(initialQuery);
    }
  }, [initialQuery]);

  return {
    images,
    loading,
    error,
    searchImages,
    getRandomImage,
    setQuery
  };
}
