import { useState, useEffect } from 'react';
import { ActionsParser, ActionsStatus, ActionMeta } from '@/lib/parser/ActionsParser';
import type { Message } from 'ai';

export function useActionsParser(message: Message) {
  const [actionsStatus, setActionsStatus] = useState<ActionsStatus>({
    isActive: false,
    isComplete: false,
    content: '',
    actions: []
  });
  const [cleanContent, setCleanContent] = useState(message.content);

  useEffect(() => {
    // Reset actions status when message changes
    setActionsStatus({
      isActive: false,
      isComplete: false,
      content: '',
      actions: []
    });

    const parser = new ActionsParser({
      onActionsStart: () => {
        setActionsStatus(prev => ({
          ...prev,
          isActive: true
        }));
      },
      onActionsDelta: (chunk, meta) => {
        if (meta) {
          setActionsStatus(prev => ({
            ...prev,
            content: meta.content,
            actions: meta.actions
          }));
        }
      },
      onActionsComplete: (meta) => {
        setActionsStatus(prev => ({
          ...prev,
          isComplete: true,
          content: meta.content,
          actions: meta.actions
        }));
      },
      onActionsError: (error) => {
        console.error('Actions parser error:', error);
      }
    });

    // Parse the message content
    const cleaned = parser.parse(message.id, message.content as string);
    setCleanContent(cleaned);
  }, [message.content, message.id]);

  return {
    actionsStatus,
    cleanContent
  };
}
