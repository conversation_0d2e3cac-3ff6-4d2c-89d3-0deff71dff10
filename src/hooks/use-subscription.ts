import useSWR from 'swr';
import { useSession } from 'next-auth/react';
import {fetcher} from "@/lib/utils";
import {PlanTier} from "@/lib/subscription/plans";

interface SubscriptionStatus {
  isActive: boolean;
  isPro: boolean;
  planTier: PlanTier;
  planName: string;
  isAnonymous: boolean;
  messageLimit: number;
  messagesRemaining: number;
  dailyLimit: number;
  dailyRemaining: number;
  credits: {
    total: number;
    used: number;
    remaining: number;
  };
  features: string[];
  allowedFeatures: string[];
}

export function useSubscription() {
  const { data: session } = useSession();
  const { data, error, isLoading } = useSWR<SubscriptionStatus>(
    '/api/subscription/status',
    fetcher
  );

  return {
    subscription: data,
    isLoading,
    error,
    isAuthenticated: !!session?.user,
  };
}
