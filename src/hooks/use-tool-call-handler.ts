// import { useEffect, useState } from 'react';
// import { useChat } from 'ai/react';
// import { ExtractedToolCall } from './use-content-parser';
//
// /**
//  * Custom hook to handle tool calls from the data stream
//  */
// export function useToolCallHandler(chatHook: ReturnType<typeof useChat>) {
//     const [toolCalls, setToolCalls] = useState<ExtractedToolCall[]>([]);
//     const { data } = chatHook;
//
//     useEffect(() => {
//         if (!data) return;
//
//         // Process tool call data from the stream
//         const toolCallData = data.filter(item => item.type === 'tool-call' || item.type === 'tool-result');
//
//         if (toolCallData.length === 0) return;
//
//         // Process tool calls
//         const newToolCalls: ExtractedToolCall[] = [];
//
//         toolCallData.forEach(item => {
//             if (item.type === 'tool-call') {
//                 const content = item.content as any;
//                 newToolCalls.push({
//                     toolCallId: content.toolCallId,
//                     toolName: content.toolName,
//                     args: content.args,
//                     state: 'calling'
//                 });
//             } else if (item.type === 'tool-result') {
//                 const content = item.content as any;
//                 const existingIndex = newToolCalls.findIndex(tc => tc.toolCallId === content.toolCallId);
//
//                 if (existingIndex >= 0) {
//                     newToolCalls[existingIndex] = {
//                         ...newToolCalls[existingIndex],
//                         state: 'result',
//                         result: content.result
//                     };
//                 } else {
//                     newToolCalls.push({
//                         toolCallId: content.toolCallId,
//                         toolName: content.toolName,
//                         args: {},
//                         state: 'result',
//                         result: content.result
//                     });
//                 }
//             }
//         });
//
//         // Update tool calls state
//         if (newToolCalls.length > 0) {
//             setToolCalls(prev => {
//                 const merged = [...prev];
//
//                 for (const toolCall of newToolCalls) {
//                     const existingIndex = merged.findIndex(tc => tc.toolCallId === toolCall.toolCallId);
//                     if (existingIndex >= 0) {
//                         merged[existingIndex] = toolCall;
//                     } else {
//                         merged.push(toolCall);
//                     }
//                 }
//
//                 return merged;
//             });
//         }
//     }, [data]);
//
//     return { toolCalls };
// }
