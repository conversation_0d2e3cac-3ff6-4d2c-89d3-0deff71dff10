import { useState } from 'react';
import type { Message } from 'ai';

export interface HiddenContentStatus {
  isActive: boolean;
  isComplete: boolean;
  hiddenContent: string[];
}

/**
 * Hook to track hidden content status
 */
export function useHiddenContent() {
  const [hiddenContentStatus, setHiddenContentStatus] = useState<HiddenContentStatus>({
    isActive: false,
    isComplete: false,
    hiddenContent: []
  });

  const onHiddenStart = () => {
    setHiddenContentStatus(prev => ({
      ...prev,
      isActive: true
    }));
  };

  const onHiddenDelta = (chunk: string, meta?: { hiddenContent: string[] }) => {
    if (meta) {
      setHiddenContentStatus(prev => ({
        ...prev,
        hiddenContent: meta.hiddenContent
      }));
    }
  };

  const onHiddenComplete = (meta: { hiddenContent: string[] }) => {
    setHiddenContentStatus(prev => ({
      ...prev,
      isComplete: true,
      hiddenContent: meta.hiddenContent
    }));
  };

  const onHiddenError = (error: string) => {
    console.error('Hidden content parser error:', error);
  };

  return {
    hiddenContentStatus,
    callbacks: {
      onHiddenStart,
      onHiddenDelta,
      onHiddenComplete,
      onHiddenError
    }
  };
}
