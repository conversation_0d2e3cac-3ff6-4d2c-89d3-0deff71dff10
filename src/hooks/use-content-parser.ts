import { useState, useEffect } from 'react';
import { MOFileParser } from '@/lib/parser/StreamParser';
import { MODiffParser, DiffMeta } from '@/lib/parser/DiffParser';
import { FileContentManager } from '@/lib/editor/FileContentManager';
import { SQLStreamParser } from '@/lib/parser/SQLStreamParser';
import { ThinkingParser, ThinkingMeta, ThinkingSection } from '@/lib/parser/ThinkingParser';
import { ActionsParser, ActionsStatus } from '@/lib/parser/ActionsParser';
import { HiddenContentParser, HiddenContentMeta } from '@/lib/parser/HiddenContentParser';
import type {ChatRequestOptions, Message} from 'ai';
import type { FileStatus } from './use-file-status';
import type { SQLStatus } from './use-sql-status';
import type { ThinkingStatus } from './use-thinking-parser';
import type { HiddenContentStatus } from './use-hidden-content';

// Track file metrics
const fileMetrics = {
    lines: new Map<string, number>(),
    addChunk(filename: string, chunk: string) {
        const currentLines = this.lines.get(filename) || 0;
        const newLines = chunk.split('\n').length - 1;
        this.lines.set(filename, currentLines + newLines);
    },
    getLineCount(filename: string) {
        return this.lines.get(filename) || 0;
    },
    reset() {
        this.lines.clear();
    }
};

// Track SQL metrics
const sqlMetrics = {
    queries: new Map<string, string>(),
    addChunk(type: string, chunk: string) {
        const current = this.queries.get(type) || '';
        this.queries.set(type, current + chunk);
    },
    getQuery(type: string) {
        return this.queries.get(type) || '';
    },
    reset() {
        this.queries.clear();
    }
};

export function useContentParser(message: Message, projectId: string, isLastMessage: boolean = false) {
    const [cleanContent, setCleanContent] = useState(message.content);
    const [fileStatuses, setFileStatuses] = useState<FileStatus[]>([]);
    const [sqlStatuses, setSqlStatuses] = useState<SQLStatus[]>([]);
    const [diffErrors, setDiffErrors] = useState<{path: string, message: string}[]>([]);
    const [thinkingStatus, setThinkingStatus] = useState<ThinkingStatus>({
        isActive: false,
        isComplete: false,
        content: '',
        sections: []
    });
    const [actionsStatus, setActionsStatus] = useState<ActionsStatus>({
        isActive: false,
        isComplete: false,
        content: '',
        actions: []
    });
    const [hiddenContentStatus, setHiddenContentStatus] = useState<HiddenContentStatus>({
        isActive: false,
        isComplete: false,
        hiddenContent: []
    });

    useEffect(() => {
        // Reset metrics and errors
        fileMetrics.reset();
        sqlMetrics.reset();
        setDiffErrors([]);

        // Initialize actions parser
        const actionsParser = new ActionsParser({
            onActionsStart: () => {
                setActionsStatus(prev => ({
                    ...prev,
                    isActive: true
                }));
            },
            onActionsDelta: (chunk, meta) => {
                if (meta) {
                    setActionsStatus(prev => ({
                        ...prev,
                        content: meta.content,
                        actions: meta.actions
                    }));
                }
            },
            onActionsComplete: (meta) => {
                setActionsStatus(prev => ({
                    ...prev,
                    isComplete: true,
                    content: meta.content,
                    actions: meta.actions
                }));
            },
            onActionsError: (error) => {
                console.error('Actions parser error:', error);
            }
        }, isLastMessage);
        
        // Initialize thinking parser
        const thinkingParser = new ThinkingParser({
            onThinkingStart: () => {
                setThinkingStatus(prev => ({
                    ...prev,
                    isActive: true
                }));
            },
            onThinkingDelta: (chunk, meta) => {
                if (meta) {
                    setThinkingStatus(prev => ({
                        ...prev,
                        content: meta.content,
                        sections: meta.sections
                    }));
                }
            },
            onThinkingComplete: (meta) => {
                setThinkingStatus(prev => ({
                    ...prev,
                    isComplete: true,
                    content: meta.content,
                    sections: meta.sections
                }));
            },
            onThinkingError: (error) => {
                console.error('Thinking parser error:', error);
            }
        });
        
        // Initialize file parser
        const fileParser = new MOFileParser({
            onFileStart: (meta) => {
                setFileStatuses(prev => {
                    const existing = prev.find(f => f.path === meta.path);
                    if (existing) return prev;
                    return [...prev, { path: meta.path, state: 'generating', lineCount: 0, percentage:0, mode: meta.mode }];
                });
            },
            onFileDelta: (filename, chunk, file) => {
                fileMetrics.addChunk(filename, chunk);
                setFileStatuses(prev =>
                    prev.map(f => 
                        f.path === filename
                            ? {...f, lineCount: file?.total_lines || 0, percentage: file?.percent_complete || 0}
                            : f
                    )
                );
            },
            onFileComplete: (meta) => {
                setFileStatuses(prev => 
                    prev.map(f => 
                        f.path === meta.path
                            ? { ...f, state: 'done' } 
                            : f
                    )
                );
                
            }
        });
        
        // Initialize diff parser
        const diffParser = new MODiffParser({
            onDiffStart: (meta) => {
                // For partial edits, add to the list but with a different indicator
                setFileStatuses(prev => {
                    const existing = prev.find(f => f.path === meta.path);
                    // If the file is already being tracked, don't add a new entry
                    if (existing) return prev;
                    return [...prev, { 
                        path: meta.path, 
                        state: 'generating', 
                        lineCount: 0, 
                        percentage: 0, 
                        mode: 'edit' 
                    }];
                });
            },
            onDiffDelta: (path: string, searchBlock: string, replaceBlock: string, meta: DiffMeta) => {
                setFileStatuses(prev =>
                    prev.map(f =>
                        f.path === path
                            ? {...f, lineCount: meta?.lineCount || 0, percentage: 0}
                            : f
                    )
                );
            },
            onDiffComplete: (meta) => {
                // Update file status
                setFileStatuses(prev => 
                    prev.map(f => 
                        f.path === meta.path
                            ? { ...f, state: 'done', lineCount: meta.lineCount || 0 }
                            : f
                    )
                );
            },
            onDiffError: (error, path) => {
                if (path) {
                    setDiffErrors(prev => [...prev, { path, message: error }]);
                } else {
                    setDiffErrors(prev => [...prev, { path: 'unknown', message: error }]);
                }
            }
        });

        const sqlParser = new SQLStreamParser({
            onQueryStart: (meta) => {
                setSqlStatuses(prev => {
                    const existing = prev.find(q => 
                        q.type === meta.type && 
                        q.table === meta.table
                    );
                    if (existing) return prev;
                    return [...prev, { 
                        ...meta, 
                        state: 'pending',
                        query: ''
                    }];
                });
            },
            onQueryDelta: (type, chunk) => {
                sqlMetrics.addChunk(type, chunk);
                setSqlStatuses(prev => 
                    prev.map(q => 
                        q.type === type
                            ? { ...q, query: sqlMetrics.getQuery(type) }
                            : q
                    )
                );
            },
            onQueryComplete: (meta) => {
                setSqlStatuses(prev => 
                    prev.map(q => 
                        q.type === meta.type && q.table === meta.table
                            ? { ...q, state: 'pending', query: meta.query }
                            : q
                    )
                );
            }
        });

        // Initialize hidden content parser
        const hiddenContentParser = new HiddenContentParser({
            onHiddenStart: () => {
                setHiddenContentStatus(prev => ({
                    ...prev,
                    isActive: true
                }));
            },
            onHiddenDelta: (chunk, meta) => {
                if (meta) {
                    setHiddenContentStatus(prev => ({
                        ...prev,
                        hiddenContent: meta.hiddenContent
                    }));
                }
            },
            onHiddenComplete: (meta) => {
                setHiddenContentStatus(prev => ({
                    ...prev,
                    isComplete: true,
                    hiddenContent: meta.hiddenContent
                }));
            },
            onHiddenError: (error) => {
                console.error('Hidden content parser error:', error);
            }
        });

        // console.log('Messa', message.id, message.content)
        // Chain the parsers:
        // 1. Hidden content parser removes hidden tags
        const afterHiddenParser = hiddenContentParser.parse(message.id, message.content as string);
        
        // 2. Thinking parser handles thinking tags
        const afterThinkingParser = thinkingParser.parse(message.id, afterHiddenParser);
        
        // 3. File parser handles MO_FILE tags
        const afterFileParser = fileParser.parse(message.id, afterThinkingParser);
        
        // 4. Diff parser handles MO_DIFF tags
        const afterDiffParser = diffParser.parse(message.id, afterFileParser);
        
        // 5. SQL parser handles SQL tags
        const afterSQLParser = sqlParser.parse(message.id, afterDiffParser);
        
        // 6. Actions parser handles actions tags
        const finalContent = actionsParser.parse(message.id, afterSQLParser);

        // console.log('finalContent', message.id, finalContent)
        setCleanContent(finalContent);
    }, [message.content, message.id]);

    const executeQuery = async (status: SQLStatus) => {
        // Always reset the error state when executing a query
        setSqlStatuses(prev => 
            prev.map(q => 
                q.type === status.type && q.table === status.table
                    ? { ...q, state: 'executing', error: undefined }
                    : q
            )
        );

        try {
            const response = await fetch(`/api/project/${projectId}/sql`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: status.query }),
            });

            const data = await response.json();
            if (!response.ok) {
                const errorMessage = data.error || data.message || 'Failed to execute query';
                throw new Error(errorMessage);
            }
            
            setSqlStatuses(prev => 
                prev.map(q => 
                    q.type === status.type && q.table === status.table
                        ? { ...q, state: 'done' }
                        : q
                )
            );
            return null; // No error
        } catch (error: any) {
            const errorMessage = error.message || 'An unknown error occurred';
            
            // Update the SQL status with the error
            setSqlStatuses(prev => 
                prev.map(q => 
                    q.type === status.type && q.table === status.table
                        ? { ...q, state: 'error', error: errorMessage }
                        : q
                )
            );
            
            // Return the error message so it can be passed to the onError callback
            return errorMessage;
        }
    };

    return {
        fileStatuses,
        sqlStatuses,
        diffErrors,
        thinkingStatus,
        actionsStatus,
        hiddenContentStatus,
        cleanContent,
        executeQuery
    };
}
