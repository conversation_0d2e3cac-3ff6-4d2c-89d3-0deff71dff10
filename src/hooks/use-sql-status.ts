import { useState, useEffect } from 'react';
import { SQLStreamParser } from '@/lib/parser/SQLStreamParser';
import type { Message } from '@/lib/db/schema';

export interface SQLStatus {
    type: 'up' | 'down' | 'rls';
    source: string;
    table: string;
    description: string;
    state: 'pending' | 'executing' | 'done' | 'error';
    query: string;
    error?: string;
    comments?: string[];
}

// Track SQL metrics
const sqlMetrics = {
    queries: new Map<string, string>(),
    addChunk(type: string, chunk: string) {
        const current = this.queries.get(type) || '';
        this.queries.set(type, current + chunk);
    },
    getQuery(type: string) {
        return this.queries.get(type) || '';
    },
    reset() {
        this.queries.clear();
    }
};

export function useSQLStatus(message: Message) {
    const [sqlStatuses, setSqlStatuses] = useState<SQLStatus[]>([]);
    const [cleanContent, setCleanContent] = useState(message.content);

    useEffect(() => {
        sqlMetrics.reset();
        const parser = new SQLStreamParser({
            onQueryStart: (meta) => {
                setSqlStatuses(prev => {
                    const existing = prev.find(q => 
                        q.type === meta.type && 
                        q.table === meta.table
                    );
                    if (existing) return prev;
                    return [...prev, { 
                        ...meta, 
                        state: 'pending',
                        query: '',
                        comments: meta.comments || []
                    }];
                });
            },
            onQueryDelta: (type, chunk) => {
                sqlMetrics.addChunk(type, chunk);
                setSqlStatuses(prev => 
                    prev.map(q => 
                        q.type === type
                            ? { ...q, query: sqlMetrics.getQuery(type) }
                            : q
                    )
                );
            },
            onQueryComplete: (meta) => {
                setSqlStatuses(prev => 
                    prev.map(q => 
                        q.type === meta.type && q.table === meta.table
                            ? { 
                                ...q, 
                                state: 'pending', 
                                query: meta.query,
                                comments: meta.comments || []
                              }
                            : q
                    )
                );
            }
        });

        // Parse the message content
        const cleaned = parser.parse(message.id, message.content as string);
        setCleanContent(cleaned);
    }, [message.content, message.id]);

    const executeQuery = async (status: SQLStatus) => {
        setSqlStatuses(prev => 
            prev.map(q => 
                q.type === status.type && q.table === status.table
                    ? { ...q, state: 'executing' }
                    : q
            )
        );

        try {
            // Here you would actually execute the query
            // For now, just simulate success
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            setSqlStatuses(prev => 
                prev.map(q => 
                    q.type === status.type && q.table === status.table
                        ? { ...q, state: 'done' }
                        : q
                )
            );
        } catch (error: any) {
            setSqlStatuses(prev => 
                prev.map(q => 
                    q.type === status.type && q.table === status.table
                        ? { ...q, state: 'error', error: error.message }
                        : q
                )
            );
        }
    };

    return {
        sqlStatuses,
        cleanContent,
        executeQuery
    };
}
