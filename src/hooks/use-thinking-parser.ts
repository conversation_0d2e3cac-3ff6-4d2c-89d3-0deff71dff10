import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ThinkingMeta, ThinkingSection } from '@/lib/parser/ThinkingParser';
import type { Message } from 'ai';

export interface ThinkingStatus {
  isActive: boolean;
  isComplete: boolean;
  content: string;
  sections: ThinkingSection[];
}

export function useThinkingParser(message: Message) {
  const [thinkingStatus, setThinkingStatus] = useState<ThinkingStatus>({
    isActive: false,
    isComplete: false,
    content: '',
    sections: []
  });
  const [cleanContent, setCleanContent] = useState(message.content);

  useEffect(() => {
    // Reset thinking status when message changes
    setThinkingStatus({
      isActive: false,
      isComplete: false,
      content: '',
      sections: []
    });

    const parser = new ThinkingParser({
      onThinkingStart: () => {
        setThinkingStatus(prev => ({
          ...prev,
          isActive: true
        }));
      },
      onThinkingDelta: (chunk, meta) => {
        if (meta) {
          setThinkingStatus(prev => ({
            ...prev,
            content: meta.content,
            sections: meta.sections
          }));
        }
      },
      onThinkingComplete: (meta) => {
        setThinkingStatus(prev => ({
          ...prev,
          isComplete: true,
          content: meta.content,
          sections: meta.sections
        }));
      },
      onThinkingError: (error) => {
        console.error('Thinking parser error:', error);
      }
    });

    // Parse the message content
    const cleaned = parser.parse(message.id, message.content as string);
    setCleanContent(cleaned);
  }, [message.content, message.id]);

  return {
    thinkingStatus,
    cleanContent
  };
}
