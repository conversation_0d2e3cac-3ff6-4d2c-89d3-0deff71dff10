'use client';


import { VisibilityType } from '@/components/generator/VisibilitySelector';
import { Chat } from '@/lib/db/schema';
import { useMemo } from 'react';
import useSWR, { useSWRConfig } from 'swr';

export function useChatVisibility({
  chatId,
  initialVisibility,
}: {
  chatId: string;
  initialVisibility: VisibilityType;
}) {
  const { mutate, cache } = useSWRConfig();
  const history: Array<Chat> = cache.get('/api/history')?.data;

  const { data: localVisibility, mutate: setLocalVisibility } = useSWR(
    `${chatId}-visibility`,
    null,
    {
      fallbackData: initialVisibility,
    },
  );

  const visibilityType = useMemo(() => {
    if (!history) return localVisibility;
    const chat = history.find((chat) => chat.id === chatId);
    if (!chat) return localVisibility;
    return chat.visibility;
  }, [history, chatId, localVisibility]);

  const setVisibilityType = async (updatedVisibilityType: VisibilityType) => {
    try {
      // Update local state immediately
      setLocalVisibility(updatedVisibilityType);

      // Optimistically update the history cache
      mutate<Array<Chat>>(
        '/api/history',
        (history) => {
          return history
            ? history.map((chat) => {
                if (chat.id === chatId) {
                  return {
                    ...chat,
                    visibility: updatedVisibilityType,
                  };
                }
                return chat;
              })
            : [];
        },
        { revalidate: false },
      );

      // Make the API call
      const response = await fetch('/api/chat/visibility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          chatId, 
          visibility: updatedVisibilityType 
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update visibility');
      }

      // Update both caches after successful API call
      setLocalVisibility(updatedVisibilityType);
      mutate(`${chatId}-visibility`, updatedVisibilityType, false);
      mutate('/api/history');
    } catch (error) {
      console.error('Error updating visibility:', error);
      // Revert both local state and history cache on error
      setLocalVisibility(localVisibility);
      mutate(`${chatId}-visibility`, localVisibility, false);
      mutate('/api/history');
    }
  };

  return { visibilityType, setVisibilityType };
}
