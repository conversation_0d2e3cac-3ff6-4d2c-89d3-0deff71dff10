import { useState, useEffect, useCallback } from 'react';
import { MOFileParser } from '@/lib/parser/StreamParser';
import { fileMetrics } from '@/lib/parser/FileMetrics';
import type { Message } from 'ai';

export interface FileStatus {
  path: string;
  state: 'generating' | 'done';
  lineCount: number;
  mode: 'edit' | 'create' | 'fix' | 'move'
  percentage?: number;
}

export function useFileStatus(message: Message) {
  const [fileStatuses, setFileStatuses] = useState<FileStatus[]>([]);
  const [cleanContent, setCleanContent] = useState(message.content);

  useEffect(() => {
    const parser = new MOFileParser({
      onFileStart: (meta) => {
        setFileStatuses(prev => {
          const existing = prev.find(f => f.path === meta.path);
          if (existing) return prev;
          return [...prev, { path: meta.path, state: 'generating', lineCount: 0, percentage: 0, mode: meta.mode }];
        });
      },
      onFileDelta: (filename, chunk) => {
        fileMetrics.addChunk(filename, chunk);
        setFileStatuses(prev => 
          prev.map(f => 
            f.path === filename
              ? { ...f, lineCount: fileMetrics.getLineCount(filename) }
              : f
          )
        );
      },
      onFileComplete: (meta) => {
        setFileStatuses(prev => 
          prev.map(f => 
            f.path === meta.path
              ? { ...f, state: 'done' } 
              : f
          )
        );
      }
    });

    // Parse the message content
    const cleaned = parser.parse(message.id, message.content);
    setCleanContent(cleaned);
  }, [message.content, message.id]);

  return {
    fileStatuses,
    cleanContent
  };
}
