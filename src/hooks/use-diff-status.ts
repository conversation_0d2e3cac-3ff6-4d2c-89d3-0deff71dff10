import { useState, useCallback } from 'react';
import { DiffMetrics } from '../lib/metrics/DiffMetrics';

interface DiffStatus {
  path: string;
  searchLineCount: number;
  replaceLineCount: number;
  totalLineCount: number;
}

/**
 * Hook to track diff status for files
 */
export function useDiffStatus() {
  const [diffStatuses, setDiffStatuses] = useState<DiffStatus[]>([]);
  const diffMetrics = new DiffMetrics();

  const onDiffDelta = useCallback((path: string, searchBlock: string, replaceBlock: string) => {
    // Add metrics
    diffMetrics.addSearchBlock(path, searchBlock);
    diffMetrics.addReplaceBlock(path, replaceBlock);
    
    // Update statuses
    setDiffStatuses(prev => {
      const existingIndex = prev.findIndex(status => status.path === path);
      
      if (existingIndex >= 0) {
        // Update existing status
        const updated = [...prev];
        updated[existingIndex] = {
          path,
          searchLineCount: diffMetrics.getSearchLineCount(path),
          replaceLineCount: diffMetrics.getReplaceLineCount(path),
          totalLineCount: diffMetrics.getTotalLineCount(path)
        };
        return updated;
      } else {
        // Add new status
        return [
          ...prev,
          {
            path,
            searchLineCount: diffMetrics.getSearchLineCount(path),
            replaceLineCount: diffMetrics.getReplaceLineCount(path),
            totalLineCount: diffMetrics.getTotalLineCount(path)
          }
        ];
      }
    });
  }, []);

  const clearDiffStatus = useCallback((path: string) => {
    diffMetrics.clear(path);
    setDiffStatuses(prev => prev.filter(status => status.path !== path));
  }, []);

  const clearAllDiffStatuses = useCallback(() => {
    diffMetrics.clearAll();
    setDiffStatuses([]);
  }, []);

  return {
    diffStatuses,
    onDiffDelta,
    clearDiffStatus,
    clearAllDiffStatuses
  };
}
