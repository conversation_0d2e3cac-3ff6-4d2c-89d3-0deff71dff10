'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

const DEBOUNCE_DELAY = 150; // ms

export const useContainerHeight = () => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [containerHeight, setContainerHeight] = useState(0);
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

    const updateHeight = useCallback(() => {
        if (containerRef.current) {
            const height = containerRef.current.clientHeight;
            // Only update if height actually changed
            setContainerHeight(prev => {
                const roundedNew = Math.round(height);
                const roundedPrev = Math.round(prev);
                return roundedNew !== roundedPrev ? roundedNew : prev;
            });
        }
    }, []);

    const debouncedUpdateHeight = useCallback(() => {
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
        }
        debounceTimerRef.current = setTimeout(updateHeight, DEBOUNCE_DELAY);
    }, [updateHeight]);

    useEffect(() => {
        // Initial measurement
        updateHeight();

        // Set up resize observer with debounced updates
        const resizeObserver = new ResizeObserver(debouncedUpdateHeight);
        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
        }

        return () => {
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
            }
            resizeObserver.disconnect();
        };
    }, [debouncedUpdateHeight, updateHeight]);

    return { containerRef, containerHeight };
};
