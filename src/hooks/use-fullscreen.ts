import { useState, useEffect } from 'react';
import { useIsMobile } from './use-mobile';

type ElementRef<T extends HTMLElement = HTMLElement> = React.RefObject<T>;

type AnyElementRef = ElementRef<any>;

export function useFullscreen(elements: AnyElementRef | AnyElementRef[] = []) {
    const [isFullscreen, setIsFullscreen] = useState(false);

    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
    }, []);

    const toggleFullscreen = () => {
        if (!document.fullscreenElement) {
            const refs = Array.isArray(elements) ? elements : [elements];
            const elementToFullscreen = refs.find(ref => ref?.current);
            elementToFullscreen?.current?.requestFullscreen();
            setIsFullscreen(true);
        } else {
            document.exitFullscreen();
            setIsFullscreen(false);
        }
    };

    return { isFullscreen, toggleFullscreen };
}
