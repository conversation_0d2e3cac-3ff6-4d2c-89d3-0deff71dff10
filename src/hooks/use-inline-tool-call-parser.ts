// import { useState, useEffect } from 'react';
// import { InlineToolCallParser, ToolCallData } from '@/lib/parser/InlineToolCallParser';
//
// export interface InlineToolCall extends ToolCallData {
//   state: 'calling' | 'result';
// }
//
// export function useInlineToolCallParser(messageId: string, content: string) {
//   const [toolCalls, setToolCalls] = useState<InlineToolCall[]>([]);
//   const [parsedContent, setParsedContent] = useState(content);
//
//   useEffect(() => {
//     const parser = new InlineToolCallParser({
//       callbacks: {
//         onToolCallStart: ({ toolCall }) => {
//           setToolCalls(prev => {
//             const existing = prev.find(tc => tc.id === toolCall.id);
//             if (existing) return prev;
//
//             return [...prev, {
//               ...toolCall,
//               state: 'calling'
//             }];
//           });
//         },
//         onToolCallEnd: ({ toolCall }) => {
//           setToolCalls(prev =>
//             prev.map(tc =>
//               tc.id === toolCall.id
//                 ? { ...tc, state: 'result', result: toolCall.result }
//                 : tc
//             )
//           );
//         }
//       }
//     });
//
//     // Parse the content
//     const parsed = parser.parse(messageId, content);
//     setParsedContent(parsed);
//
//     return () => {
//       parser.reset();
//     };
//   }, [messageId, content]);
//
//   return { toolCalls, parsedContent };
// }
