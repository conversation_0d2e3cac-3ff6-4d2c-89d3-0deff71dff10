'use client';

import { usePostHog } from 'posthog-js/react';
import { useEffect } from 'react';
import { useSession } from 'next-auth/react';

export function usePostHogUser() {
    const posthog = usePostHog();
    const { data: session, status } = useSession();

    useEffect(() => {
        if (status === 'loading') return;

        if (status === 'authenticated' && session?.user) {
            // Identify the user
            posthog?.identify(session.user.id, {
                email: session.user.email,
                name: session.user.name,
                last_login: new Date().toISOString(),
            });
        } else if (status === 'unauthenticated') {
            // Reset user identification when logged out
            posthog?.reset(true);
        }
    }, [posthog, session, status]);

    return {
        captureUserEvent: (eventName: string, properties?: Record<string, any>) => {
            if (status === 'authenticated' && session?.user) {
                posthog?.capture(eventName, {
                    ...properties,
                    user_id: session.user.id,
                    user_email: session.user.email,
                });
            }
        },
    };
}
