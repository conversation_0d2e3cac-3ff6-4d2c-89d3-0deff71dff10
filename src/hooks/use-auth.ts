'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

export function useAuth() {
    const { data: session, status } = useSession();
    const router = useRouter();

    const isAuthenticated = status === 'authenticated';
    const isLoading = status === 'loading';

    const handleRedirect = () => {
        if (isAuthenticated) {
            router.push('/applications');
        } else {
            router.push('/login');
        }
    };

    return {
        isAuthenticated,
        isLoading,
        handleRedirect,
        session,
    };
}
