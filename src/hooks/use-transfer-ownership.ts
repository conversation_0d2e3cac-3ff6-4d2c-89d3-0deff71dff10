'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

export function useTransferOwnership() {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const { update } = useSession();

  const transfer = async (fromUserId: string, retryCount = 0) => {
    try {
      setStatus('loading');
      const response = await fetch('/api/transfer-ownership', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromUserId,
        }),
      });

      if (!response.ok) {
        throw new Error('Transfer failed');
      }
      
      setStatus('success');
      localStorage.removeItem('pendingTransfer');
      
      // Simple solution: reload the page immediately after successful transfer
      window.location.reload();
      
      return true;
    } catch (error) {
      console.error('Transfer error:', error);
      
      // Retry logic
      if (retryCount < MAX_RETRIES) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        return transfer(fromUserId, retryCount + 1);
      }

      setStatus('error');
      toast.error('Failed to transfer your data. Please try again.');
      return false;
    }
  };

  return {
    status,
    transfer,
    setStatus,
  };
}
