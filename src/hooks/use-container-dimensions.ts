'use client';

import { useRef, useCallback, useEffect } from 'react';

const DEBOUNCE_DELAY = 150; // ms

interface ContainerDimensions {
    height: number;
    width: number;
}

export const useContainerDimensions = (onDimensionsChange?: (dimensions: ContainerDimensions) => void) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const dimensionsRef = useRef<ContainerDimensions>({ height: 0, width: 0 });
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

    const updateDimensions = useCallback(() => {
        if (containerRef.current) {
            const height = Math.round(containerRef.current.clientHeight);
            const width = Math.round(containerRef.current.clientWidth);

            // Only notify if dimensions actually changed
            if (height !== dimensionsRef.current.height || width !== dimensionsRef.current.width) {
                dimensionsRef.current = { height, width };
                onDimensionsChange?.(dimensionsRef.current);
            }
        }
    }, [onDimensionsChange]);

    const debouncedUpdateDimensions = useCallback(() => {
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
        }
        debounceTimerRef.current = setTimeout(updateDimensions, DEBOUNCE_DELAY);
    }, [updateDimensions]);

    useEffect(() => {
        // Initial measurement
        updateDimensions();

        // Set up resize observer with debounced updates
        const resizeObserver = new ResizeObserver(debouncedUpdateDimensions);
        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
        }

        return () => {
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
            }
            resizeObserver.disconnect();
        };
    }, [debouncedUpdateDimensions, updateDimensions]);

    return {
        containerRef,
        getDimensions: () => dimensionsRef.current
    };
};
