import { eq, desc, and } from 'drizzle-orm';
import { db } from '@/lib/db';
import { apkBuilds, deployments } from './schema';

// Use the actual type from the database
type DrizzleClient = typeof db;
type DeploymentPlatform = 'web' | 'android' | 'ios';
// Match the exact status values from the schema
type DeploymentStatus = 'queued' | 'processing' | 'completed' | 'failed';

/**
 * Create a new deployment record
 */
export async function createDeployment(db: DrizzleClient, data: {
  projectId: string;
  userId: string;
  platform: DeploymentPlatform;
  version: string;
  fileStateId: string;
  slug: string;
  buildId?: string;
  status?: DeploymentStatus;
  url?: string;
  metadata?: Record<string, any>;
}) {
  return db.insert(deployments).values(data).returning();
}

/**
 * Get all deployments for a project
 */
export async function getDeploymentsByProjectId(db: Dr<PERSON>zleClient, projectId: string) {
  return db.select().from(deployments)
    .where(eq(deployments.projectId, projectId))
    .orderBy(desc(deployments.createdAt));
}

/**
 * Get the latest deployment for a project and platform
 */
export async function getLatestDeployment(db: DrizzleClient, projectId: string, platform: DeploymentPlatform) {
  const results = await db.select().from(deployments)
    .where(and(
      eq(deployments.projectId, projectId),
      eq(deployments.platform, platform)
    ))
    .orderBy(desc(deployments.createdAt))
    .limit(1);
  
  return results[0] || null;
}

/**
 * Update a deployment's status
 */
export async function updateDeploymentStatus(
  db: DrizzleClient, 
  id: string, 
  status: DeploymentStatus, 
  url?: string,
  metadata?: Record<string, any>
) {
  const updateData: any = { 
    status, 
    updatedAt: new Date() 
  };
  
  if (url) updateData.url = url;
  if (metadata) updateData.metadata = metadata;
  
  return db.update(deployments)
    .set(updateData)
    .where(eq(deployments.id, id))
    .returning();
}

/**
 * Get the latest version number for a project
 */
export async function getLatestVersion(db: DrizzleClient, projectId: string) {
  const results = await db.select({ version: deployments.version })
    .from(deployments)
    .where(eq(deployments.projectId, projectId))
    .orderBy(desc(deployments.createdAt))
    .limit(1);
  
  return results[0]?.version || '1.0.0'; // Default to 1.0.0 if no previous deployments
}

/**
 * Get the latest version code for a project
 */
export async function getLatestVersionCode(db: DrizzleClient, projectId: string) {
  const results = await db.select({ versionCode: deployments.versionCode })
    .from(deployments)
    .where(eq(deployments.projectId, projectId))
    .orderBy(desc(deployments.createdAt))
    .limit(1);
  
  return results[0]?.versionCode || 1; // Default to 1 if no previous deployments
}

/**
 * Increment version number based on semantic versioning
 * @param version Current version (e.g., "1.0.0")
 * @param type Type of increment: "major", "minor", or "patch"
 */
export function incrementVersion(version: string, type: 'major' | 'minor' | 'patch' = 'patch') {
  const parts = version.split('.').map(Number);
  
  if (parts.length !== 3) {
    return '1.0.0'; // Reset to default if invalid format
  }
  
  if (type === 'major') {
    parts[0]++;
    parts[1] = 0;
    parts[2] = 0;
  } else if (type === 'minor') {
    parts[1]++;
    parts[2] = 0;
  } else {
    parts[2]++;
  }
  
  return parts.join('.');
}

/**
 * Create a new APK build record
 */
export async function createApkBuild(db: DrizzleClient, data: {
  id: string;
  status: string;
  metadata?: Record<string, any>;
}) {
  return db.insert(apkBuilds).values(data).returning();
}

/**
 * Update an APK build's status
 */
export async function updateApkBuildStatus(
  db: DrizzleClient, 
  id: string, 
  status: DeploymentStatus, 
  apkUrl?: string,
  error?: string,
  metadata?: Record<string, any>
) {
  const updateData: any = { 
    status, 
    updatedAt: new Date() 
  };
  
  if (apkUrl) updateData.apkUrl = apkUrl;
  if (error) updateData.error = error;
  if (metadata) updateData.metadata = metadata;
  
  return db.update(apkBuilds)
    .set(updateData)
    .where(eq(apkBuilds.id, id))
    .returning();
}

/**
 * Get an APK build by ID
 */
export async function getApkBuild(db: DrizzleClient, id: string) {
  const results = await db.select().from(apkBuilds)
    .where(eq(apkBuilds.id, id));
  
  return results[0] || null;
}
