import { db } from '@/lib/db';
import { designScreens } from '@/lib/db/schema';
import { eq, and, count } from 'drizzle-orm';

export async function saveDesignScreen({
  id,
  chatId,
  projectId,
  name,
  html,
  order = 0,
  status = 'complete'
}: {
  id: string;
  chatId: string;
  projectId: string;
  name: string;
  html: string;
  order?: number;
  status?: 'starting' | 'generating' | 'complete' | 'error';
}) {
  try {
    const result = await db.insert(designScreens).values({
      id,
      chatId,
      projectId,
      name,
      html,
      order,
      status,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    
    return result[0];
  } catch (error) {
    console.error('Error saving design screen:', error);
    throw error;
  }
}

export async function updateDesignScreen({
  id,
  html,
  status,
  order
}: {
  id: string;
  html?: string;
  status?: 'starting' | 'generating' | 'complete' | 'error';
  order?: number
}) {
  try {
    const updateValues: any = {
      updatedAt: new Date()
    };
    
    if (html !== undefined) {
      updateValues.html = html;
    }

    if (typeof order !== undefined) {
      updateValues.order = order;
    }
    
    if (status !== undefined) {
      updateValues.status = status;
    }
    
    const result = await db
      .update(designScreens)
      .set(updateValues)
      .where(eq(designScreens.id, id))
      .returning();
    
    return result[0];
  } catch (error) {
    console.error('Error updating design screen:', error);
    throw error;
  }
}

export async function getDesignScreenById({ id }: { id: string }) {
  try {
    const result = await db
      .select()
      .from(designScreens)
      .where(eq(designScreens.id, id));
    
    return result[0] || null;
  } catch (error) {
    console.error('Error getting design screen by ID:', error);
    throw error;
  }
}

export async function getDesignScreensByChatId({ chatId }: { chatId: string }) {
  try {
    const result = await db
      .select()
      .from(designScreens)
      .where(eq(designScreens.chatId, chatId))
      .orderBy(designScreens.order);
    
    return result;
  } catch (error) {
    console.error('Error getting design screens by chat ID:', error);
    throw error;
  }
}

export async function deleteDesignScreen({ id }: { id: string }) {
  try {
    await db
      .delete(designScreens)
      .where(eq(designScreens.id, id));
    
    return { success: true };
  } catch (error) {
    console.error('Error deleting design screen:', error);
    throw error;
  }
}

/**
 * Count the total number of design screens for a project
 */
export async function countDesignScreensByProject({ projectId }: { projectId: string }) {
  try {
    const result = await db
      .select({ count: count() })
      .from(designScreens)
      .where(eq(designScreens.projectId, projectId));
    
    return result[0]?.count || 0;
  } catch (error) {
    console.error('Error counting design screens by project ID:', error);
    throw error;
  }
}

/**
 * Get all design screens for a project
 */
export async function getDesignScreensByProject({ projectId }: { projectId: string }) {
  try {
    const result = await db
      .select()
      .from(designScreens)
      .where(eq(designScreens.projectId, projectId))
      .orderBy(designScreens.order);
    
    return result;
  } catch (error) {
    console.error('Error getting design screens by project ID:', error);
    throw error;
  }
}
