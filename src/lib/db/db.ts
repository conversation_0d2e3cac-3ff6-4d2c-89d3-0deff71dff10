import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// For Next.js, we need to check if we're in development mode
// to avoid creating multiple connections during hot reloading
const globalForPg = globalThis as unknown as {
  pg: postgres.Sql | undefined;
};

// Create the postgres client with proper connection pooling
export const client = globalForPg.pg || 
  postgres(process.env.POSTGRES_URL!, {
    max: 10, // Maximum number of connections
    idle_timeout: 20, // Close idle connections after 20 seconds
    connect_timeout: 10, // Connection timeout in seconds
    max_lifetime: 60 * 30, // Max connection lifetime in seconds (30 minutes)
  });

// Only assign to the global object in development
// This prevents creating multiple connections during hot reloading
if (process.env.NODE_ENV === 'development') {
  globalForPg.pg = client;
}

// Create the drizzle client
export const db = drizzle(client);
