CREATE TABLE "TokenConsumption" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"model" varchar(50) NOT NULL,
	"totalTimeToken" real NOT NULL,
	"promptTokens" integer NOT NULL,
	"completionTokens" integer NOT NULL,
	"totalTokens" integer NOT NULL,
	"chatId" uuid NOT NULL,
	"messageId" uuid NOT NULL,
	"userId" uuid NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"inputCost" real NOT NULL,
	"outputCost" real NOT NULL
);
--> statement-breakpoint
ALTER TABLE "TokenConsumption" ADD CONSTRAINT "TokenConsumption_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;