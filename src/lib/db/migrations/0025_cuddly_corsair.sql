-- Create ProjectChat table if it doesn't exist
CREATE TABLE IF NOT EXISTS "ProjectChat" (
	"projectId" uuid NOT NULL,
	"chatId" uuid NOT NULL,
	"isPrimary" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint

-- Safely drop constraint if it exists
DO $$ 
BEGIN
  BEGIN
    ALTER TABLE "Project" DROP CONSTRAINT "Project_chatId_Chat_id_fk";
  EXCEPTION WHEN undefined_object OR undefined_table THEN
    RAISE NOTICE 'Constraint Project_chatId_Chat_id_fk does not exist or has already been dropped';
  END;
END $$;
--> statement-breakpoint
-- Safely alter columns and add columns if they don't exist
DO $$ 
BEGIN
  -- Drop NOT NULL constraint if it exists
  BEGIN
    ALTER TABLE "Project" ALTER COLUMN "connectionId" DROP NOT NULL;
  EXCEPTION WHEN undefined_column THEN
    RAISE NOTICE 'Column connectionId does not exist in Project table';
  END;
  
  -- Add columns if they don't exist
  BEGIN
    ALTER TABLE "Chat" ADD COLUMN "projectId" uuid;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column projectId already exists in Chat table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "deployments" ADD COLUMN "projectId" uuid;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column projectId already exists in deployments table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "FileState" ADD COLUMN "projectId" uuid;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column projectId already exists in FileState table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Message" ADD COLUMN "projectId" uuid;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column projectId already exists in Message table or table does not exist';
  END;
  
  -- Add Project columns if they don't exist
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "appName" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column appName already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "slug" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column slug already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "scheme" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column scheme already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "bundleIdentifier" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column bundleIdentifier already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "packageName" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column packageName already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "isMigratedv1" boolean DEFAULT false;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column isMigratedv1 already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "icon" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column icon already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "splashImage" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column splashImage already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "primaryColor" text DEFAULT '#000000';
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column primaryColor already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "description" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column description already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "privacyPolicyUrl" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column privacyPolicyUrl already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "termsOfServiceUrl" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column termsOfServiceUrl already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "supabaseProjectId" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column supabaseProjectId already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "supabaseAnonKey" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column supabaseAnonKey already exists in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" ADD COLUMN "supabaseServiceKey" text;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column supabaseServiceKey already exists in Project table or table does not exist';
  END;
  
  -- Handle TokenConsumption projectId column
  BEGIN
    -- First try to add the column
    ALTER TABLE "TokenConsumption" ADD COLUMN "projectId" uuid;
    
    -- If we get here, the column was added, so set default values and make it NOT NULL
    UPDATE "TokenConsumption" SET "projectId" = '00000000-0000-0000-0000-000000000000';
    ALTER TABLE "TokenConsumption" ALTER COLUMN "projectId" SET NOT NULL;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column projectId already exists in TokenConsumption table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Vote" ADD COLUMN "projectId" uuid;
  EXCEPTION WHEN duplicate_column OR undefined_table THEN
    RAISE NOTICE 'Column projectId already exists in Vote table or table does not exist';
  END;
END $$;
--> statement-breakpoint
-- Add foreign key constraints safely
DO $$ 
BEGIN
  -- ProjectChat constraints
  BEGIN
    ALTER TABLE "ProjectChat" ADD CONSTRAINT "ProjectChat_projectId_Project_id_fk" 
      FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") 
      ON DELETE no action ON UPDATE no action;
  EXCEPTION WHEN duplicate_object OR undefined_table THEN
    RAISE NOTICE 'Constraint ProjectChat_projectId_Project_id_fk already exists or tables do not exist';
  END;
  
  BEGIN
    ALTER TABLE "ProjectChat" ADD CONSTRAINT "ProjectChat_chatId_Chat_id_fk" 
      FOREIGN KEY ("chatId") REFERENCES "public"."Chat"("id") 
      ON DELETE no action ON UPDATE no action;
  EXCEPTION WHEN duplicate_object OR undefined_table THEN
    RAISE NOTICE 'Constraint ProjectChat_chatId_Chat_id_fk already exists or tables do not exist';
  END;
  
  -- Chat constraint
  BEGIN
    ALTER TABLE "Chat" ADD CONSTRAINT "Chat_projectId_Project_id_fk" 
      FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") 
      ON DELETE no action ON UPDATE no action;
  EXCEPTION WHEN duplicate_object OR undefined_table THEN
    RAISE NOTICE 'Constraint Chat_projectId_Project_id_fk already exists or tables do not exist';
  END;
  
  -- Deployments constraint
  BEGIN
    ALTER TABLE "deployments" ADD CONSTRAINT "deployments_projectId_Project_id_fk" 
      FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") 
      ON DELETE no action ON UPDATE no action;
  EXCEPTION WHEN duplicate_object OR undefined_table THEN
    RAISE NOTICE 'Constraint deployments_projectId_Project_id_fk already exists or tables do not exist';
  END;
  
  -- FileState constraint
  BEGIN
    ALTER TABLE "FileState" ADD CONSTRAINT "FileState_projectId_Project_id_fk" 
      FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") 
      ON DELETE no action ON UPDATE no action;
  EXCEPTION WHEN duplicate_object OR undefined_table THEN
    RAISE NOTICE 'Constraint FileState_projectId_Project_id_fk already exists or tables do not exist';
  END;
  
  -- Message constraint
  BEGIN
    ALTER TABLE "Message" ADD CONSTRAINT "Message_projectId_Project_id_fk" 
      FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") 
      ON DELETE no action ON UPDATE no action;
  EXCEPTION WHEN duplicate_object OR undefined_table THEN
    RAISE NOTICE 'Constraint Message_projectId_Project_id_fk already exists or tables do not exist';
  END;
  
  -- Vote constraint
  BEGIN
    ALTER TABLE "Vote" ADD CONSTRAINT "Vote_projectId_Project_id_fk" 
      FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") 
      ON DELETE no action ON UPDATE no action;
  EXCEPTION WHEN duplicate_object OR undefined_table THEN
    RAISE NOTICE 'Constraint Vote_projectId_Project_id_fk already exists or tables do not exist';
  END;
  
  -- Drop columns if they exist
  BEGIN
    ALTER TABLE "Project" DROP COLUMN "title";
  EXCEPTION WHEN undefined_column OR undefined_table THEN
    RAISE NOTICE 'Column title does not exist in Project table or table does not exist';
  END;
  
  BEGIN
    ALTER TABLE "Project" DROP COLUMN "chatId";
  EXCEPTION WHEN undefined_column OR undefined_table THEN
    RAISE NOTICE 'Column chatId does not exist in Project table or table does not exist';
  END;
END $$;