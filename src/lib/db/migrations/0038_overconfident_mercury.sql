CREATE INDEX "subscription_userId_idx" ON "Subscription" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "subscription_status_idx" ON "Subscription" USING btree ("status");--> statement-breakpoint
CREATE INDEX "subscription_userId_updatedAt_idx" ON "Subscription" USING btree ("userId","updatedAt");--> statement-breakpoint
CREATE INDEX "subscription_isActive_idx" ON "Subscription" USING btree ("isActive");