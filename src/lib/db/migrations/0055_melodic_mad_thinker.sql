CREATE TABLE "ScreenshotState" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"projectId" uuid,
	"chatId" uuid,
	"messageId" uuid,
	"screenshots" json NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"version" integer DEFAULT 1
);
--> statement-breakpoint
ALTER TABLE "ScreenshotState" ADD CONSTRAINT "ScreenshotState_projectId_Project_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ScreenshotState" ADD CONSTRAINT "ScreenshotState_chatId_Chat_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."Chat"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ScreenshotState" ADD CONSTRAINT "ScreenshotState_messageId_Message_id_fk" FOREI<PERSON><PERSON> ("messageId") REFERENCES "public"."Message"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "screenshotState_projectId_idx" ON "ScreenshotState" USING btree ("projectId");--> statement-breakpoint
CREATE INDEX "screenshotState_chatId_idx" ON "ScreenshotState" USING btree ("chatId");--> statement-breakpoint
CREATE INDEX "screenshotState_messageId_idx" ON "ScreenshotState" USING btree ("messageId");--> statement-breakpoint
CREATE INDEX "screenshotState_createdAt_idx" ON "ScreenshotState" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "screenshotState_version_idx" ON "ScreenshotState" USING btree ("version");