{"id": "90c06fea-27cb-46f3-8eab-7b51b3a4e249", "prevId": "9a8d6aaa-0cf5-4058-9c0a-f5f56cc04f9f", "version": "7", "dialect": "postgresql", "tables": {"public.apk_builds": {"name": "apk_builds", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "apk_url": {"name": "apk_url", "type": "text", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "connectionId": {"name": "connectionId", "type": "uuid", "primaryKey": false, "notNull": false}, "supabaseProjectId": {"name": "supabaseProjectId", "type": "text", "primaryKey": false, "notNull": false}, "supabaseAnonKey": {"name": "supabaseAnonKey", "type": "text", "primaryKey": false, "notNull": false}, "supabaseServiceKey": {"name": "supabaseServiceKey", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Chat_userId_User_id_fk": {"name": "Chat_userId_User_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Chat_connectionId_Connection_id_fk": {"name": "Chat_connectionId_Connection_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "Connection", "columnsFrom": ["connectionId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Connection": {"name": "Connection", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "lastSyncedAt": {"name": "lastSyncedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Connection_userId_User_id_fk": {"name": "Connection_userId_User_id_fk", "tableFrom": "Connection", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Document": {"name": "Document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'text'"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Document_userId_User_id_fk": {"name": "Document_userId_User_id_fk", "tableFrom": "Document", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Document_id_createdAt_pk": {"name": "Document_id_createdAt_pk", "columns": ["id", "createdAt"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.FileState": {"name": "FileState", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "sqlQuery": {"name": "sqlQuery", "type": "text", "primaryKey": false, "notNull": false}, "sqlStatus": {"name": "sqlStatus", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sqlError": {"name": "sqlError", "type": "text", "primaryKey": false, "notNull": false}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": false}, "files": {"name": "files", "type": "json", "primaryKey": false, "notNull": true}, "dependencies": {"name": "dependencies", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "is_base_cache_version": {"name": "is_base_cache_version", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"FileState_chatId_Chat_id_fk": {"name": "FileState_chatId_Chat_id_fk", "tableFrom": "FileState", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "FileState_messageId_Message_id_fk": {"name": "FileState_messageId_Message_id_fk", "tableFrom": "FileState", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "remoteProvider": {"name": "remoteProvider", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remoteProviderId": {"name": "remoteProviderId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Message_chatId_Chat_id_fk": {"name": "Message_chatId_Chat_id_fk", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Message_userId_User_id_fk": {"name": "Message_userId_User_id_fk", "tableFrom": "Message", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.password_reset_tokens": {"name": "password_reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"password_reset_tokens_user_id_User_id_fk": {"name": "password_reset_tokens_user_id_User_id_fk", "tableFrom": "password_reset_tokens", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"password_reset_tokens_token_unique": {"name": "password_reset_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ProjectConnection": {"name": "ProjectConnection", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "projectId": {"name": "projectId", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "providerProjectId": {"name": "providerProjectId", "type": "text", "primaryKey": false, "notNull": true}, "providerProjectData": {"name": "providerProjectData", "type": "json", "primaryKey": false, "notNull": false}, "types": {"name": "types", "type": "json", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ProjectConnection_projectId_Project_id_fk": {"name": "ProjectConnection_projectId_Project_id_fk", "tableFrom": "ProjectConnection", "tableTo": "Project", "columnsFrom": ["projectId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Project": {"name": "Project", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "connectionId": {"name": "connectionId", "type": "uuid", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Project_chatId_Chat_id_fk": {"name": "Project_chatId_Chat_id_fk", "tableFrom": "Project", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Project_userId_User_id_fk": {"name": "Project_userId_User_id_fk", "tableFrom": "Project", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "Project_connectionId_Connection_id_fk": {"name": "Project_connectionId_Connection_id_fk", "tableFrom": "Project", "tableTo": "Connection", "columnsFrom": ["connectionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Subscription": {"name": "Subscription", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "planId": {"name": "planId", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "subscriptionId": {"name": "subscriptionId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Subscription_subscriptionId_unique": {"name": "Subscription_subscriptionId_unique", "nullsNotDistinct": false, "columns": ["subscriptionId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Suggestion": {"name": "Suggestion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "documentId": {"name": "documentId", "type": "uuid", "primaryKey": false, "notNull": true}, "documentCreatedAt": {"name": "documentCreatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "originalText": {"name": "originalText", "type": "text", "primaryKey": false, "notNull": true}, "suggestedText": {"name": "suggestedText", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isResolved": {"name": "isResolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Suggestion_userId_User_id_fk": {"name": "Suggestion_userId_User_id_fk", "tableFrom": "Suggestion", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk": {"name": "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk", "tableFrom": "Suggestion", "tableTo": "Document", "columnsFrom": ["documentId", "documentCreatedAt"], "columnsTo": ["id", "createdAt"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Suggestion_id_pk": {"name": "Suggestion_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.TokenConsumption": {"name": "TokenConsumption", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "totalTimeToken": {"name": "totalTimeToken", "type": "real", "primaryKey": false, "notNull": true}, "promptTokens": {"name": "promptTokens", "type": "integer", "primaryKey": false, "notNull": true}, "completionTokens": {"name": "completionTokens", "type": "integer", "primaryKey": false, "notNull": true}, "totalTokens": {"name": "totalTokens", "type": "integer", "primaryKey": false, "notNull": true}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "inputCost": {"name": "inputCost", "type": "real", "primaryKey": false, "notNull": true}, "outputCost": {"name": "outputCost", "type": "real", "primaryKey": false, "notNull": true}, "cachingDiscount": {"name": "cachingDiscount", "type": "real", "primaryKey": false, "notNull": false}, "cacheDiscountPercent": {"name": "cacheDiscountPercent", "type": "real", "primaryKey": false, "notNull": false}, "subtotal": {"name": "subtotal", "type": "real", "primaryKey": false, "notNull": false}, "totalCost": {"name": "totalCost", "type": "real", "primaryKey": false, "notNull": true}, "isAnonymous": {"name": "isAnonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "remoteProvider": {"name": "remoteProvider", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "remoteProviderId": {"name": "remoteProviderId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"TokenConsumption_userId_User_id_fk": {"name": "TokenConsumption_userId_User_id_fk", "tableFrom": "TokenConsumption", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "firstName": {"name": "firstName", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "linkedin": {"name": "linkedin", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'credentials'"}, "isAnonymous": {"name": "isAnonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Vote": {"name": "Vote", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Vote_chatId_Chat_id_fk": {"name": "Vote_chatId_Chat_id_fk", "tableFrom": "Vote", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Vote_messageId_Message_id_fk": {"name": "Vote_messageId_Message_id_fk", "tableFrom": "Vote", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Vote_chatId_messageId_pk": {"name": "Vote_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}