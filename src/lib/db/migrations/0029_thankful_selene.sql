ALTER TABLE "Subscription" ALTER COLUMN "status" SET DEFAULT 'inactive';--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "planId" SET DEFAULT 'free';--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "subscriptionId" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "credits" SET DEFAULT 50;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "isActive" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "lemonSqueezyCustomerId" varchar;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "lemonSqueezySubscriptionId" varchar;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "lemonSqueezyOrderId" varchar;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "stripeCustomerId" varchar;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "stripeSubscriptionId" varchar;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "stripePriceId" varchar;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "stripeCurrentPeriodEnd" timestamp;--> statement-breakpoint
ALTER TABLE "Subscription" ADD COLUMN "resetDate" timestamp;