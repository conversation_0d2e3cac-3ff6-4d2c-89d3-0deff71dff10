CREATE TABLE "DesignScreen" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatId" uuid NOT NULL,
	"projectId" uuid NOT NULL,
	"name" text NOT NULL,
	"html" text NOT NULL,
	"order" integer DEFAULT 0 NOT NULL,
	"status" varchar DEFAULT 'complete' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "DesignScreen" ADD CONSTRAINT "DesignScreen_chatId_Chat_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."Chat"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "DesignScreen" ADD CONSTRAINT "DesignScreen_projectId_Project_id_fk" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "design_screen_chatId_idx" ON "DesignScreen" USING btree ("chatId");--> statement-breakpoint
CREATE INDEX "design_screen_projectId_idx" ON "DesignScreen" USING btree ("projectId");--> statement-breakpoint
CREATE INDEX "design_screen_order_idx" ON "DesignScreen" USING btree ("order");