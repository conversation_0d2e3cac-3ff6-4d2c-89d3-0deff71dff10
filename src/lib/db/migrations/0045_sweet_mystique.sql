ALTER TABLE "Message" ADD COLUMN "autoFixed" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "Message" ADD COLUMN "hidden" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "TokenConsumption" ADD COLUMN "isAutoFixed" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "Project" ADD CONSTRAINT "Project_designChatId_Chat_id_fk" FOREIGN KEY ("designChatId") REFERENCES "public"."Chat"("id") ON DELETE no action ON UPDATE no action;