import { sql } from 'drizzle-orm';
import { db } from '@/lib/db';

export async function addMetadataToSubscriptions() {
  try {
    // Add metadata JSON column to subscriptions table
    await db.execute(sql`
      ALTER TABLE "Subscription"
      ADD COLUMN IF NOT EXISTS "metadata" JSONB DEFAULT '{}'::jsonb;
    `);
    
    console.log('Successfully added metadata column to Subscription table');
    return { success: true };
  } catch (error) {
    console.error('Error adding metadata column to Subscription table:', error);
    return { success: false, error };
  }
}
