ALTER TABLE "TokenConsumption" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
CREATE INDEX "chat_userId_idx" ON "Chat" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "chat_projectId_idx" ON "Chat" USING btree ("projectId");--> statement-breakpoint
CREATE INDEX "chat_userId_projectId_idx" ON "Chat" USING btree ("userId","projectId");--> statement-breakpoint
CREATE INDEX "chat_updatedAt_idx" ON "Chat" USING btree ("updatedAt");--> statement-breakpoint
CREATE INDEX "fileState_isBaseCacheVersion_idx" ON "FileState" USING btree ("is_base_cache_version");--> statement-breakpoint
CREATE INDEX "fileState_chatId_isBaseCacheVersion_idx" ON "FileState" USING btree ("chatId","is_base_cache_version");--> statement-breakpoint
CREATE INDEX "fileState_version_idx" ON "FileState" USING btree ("version");--> statement-breakpoint
CREATE INDEX "fileState_chatId_messageId_idx" ON "FileState" USING btree ("chatId","messageId");--> statement-breakpoint
CREATE INDEX "fileState_createdAt_idx" ON "FileState" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "message_chatId_idx" ON "Message" USING btree ("chatId");--> statement-breakpoint
CREATE INDEX "message_projectId_idx" ON "Message" USING btree ("projectId");--> statement-breakpoint
CREATE INDEX "message_userId_idx" ON "Message" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "message_chatId_createdAt_idx" ON "Message" USING btree ("chatId","createdAt");--> statement-breakpoint
CREATE INDEX "message_role_idx" ON "Message" USING btree ("role");--> statement-breakpoint
CREATE INDEX "tokenConsumption_userId_idx" ON "TokenConsumption" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "tokenConsumption_chatId_idx" ON "TokenConsumption" USING btree ("chatId");--> statement-breakpoint
CREATE INDEX "tokenConsumption_projectId_idx" ON "TokenConsumption" USING btree ("projectId");--> statement-breakpoint
CREATE INDEX "tokenConsumption_userId_createdAt_idx" ON "TokenConsumption" USING btree ("userId","createdAt");--> statement-breakpoint
CREATE INDEX "tokenConsumption_isAnonymous_idx" ON "TokenConsumption" USING btree ("isAnonymous");