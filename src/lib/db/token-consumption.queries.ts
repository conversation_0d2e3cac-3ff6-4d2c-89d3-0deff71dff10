import 'server-only';
import {eq, and, desc, sql, gte, isNotNull} from 'drizzle-orm';
import { tokenConsumption, type TokenConsumption } from './schema';
import { db } from './db';
import { models } from '../ai/models';
import {round} from "lodash";
import dayjs from "dayjs";
import {calculateCosts, getMessageDetailsFromOpenrouter} from "@/lib/openrouter/get-message-details";




// In src/lib/db/token-consumption.queries.ts
export async function getErrorFixingDiscountsForToday(userId: string): Promise<number> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Count TokenConsumption records with errorId set and discounted=true from today
  const result = await db
      .select({
        count: sql<number>`COUNT(*)`
      })
      .from(tokenConsumption)
      .where(
          and(
              eq(tokenConsumption.userId, userId),
              gte(tokenConsumption.createdAt, today),
              isNotNull(tokenConsumption.errorId),
              eq(tokenConsumption.discounted, true)
          )
      );

  return Number(result[0]?.count || 0);
}

export async function saveTokenConsumption(data: TokenConsumptionInput): Promise<TokenConsumption> {
  const {cacheDiscountPercent, cachingDiscount, inputCost, outputCost, subtotal, totalCost} = await getMessageDetailsFromOpenrouter(data);

  let shouldDiscount = false;
  if (data.errorId) {
    try {
      const count = await getErrorFixingDiscountsForToday(data.userId);
      shouldDiscount = count < 10 || data.isAutoFixed === true; // Auto-fixed errors are always discounted
      console.log('count of free error fixes for today', count);
      console.log('shouldDiscount', shouldDiscount);
      console.log('isAutoFixed', data.isAutoFixed);
    } catch (e: unknown) {
      console.log('Error getting discount credits', e);
    }
  }

  const [consumption] = await db
    .insert(tokenConsumption)
    .values({
      ...data,
      // Store costs as floats directly
      inputCost: inputCost,
      outputCost: outputCost,
      totalCost: totalCost,
      cachingDiscount: cachingDiscount,
      subtotal: subtotal,
      cacheDiscountPercent,
      creditsConsumed: data.creditsConsumed,
      discountedCredits: data.discountedCredits || 0,
      discountReason: data.discountReason || null,
      errorId: data.errorId,
      isAutoFixed: data.isAutoFixed || false,
      discounted: shouldDiscount
    })
    .returning();
  return consumption;
}

export async function getTokenConsumptionByMessageId(messageId: string): Promise<TokenConsumption | undefined> {
  const [consumption] = await db
    .select()
    .from(tokenConsumption)
    .where(eq(tokenConsumption.messageId, messageId));
  return consumption;
}

export async function getTokenConsumptionByChatId(chatId: string): Promise<TokenConsumption[]> {
  return db
    .select()
    .from(tokenConsumption)
    .where(eq(tokenConsumption.chatId, chatId))
    .orderBy(desc(tokenConsumption.createdAt));
}