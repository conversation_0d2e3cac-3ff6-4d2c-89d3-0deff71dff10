import { eq, desc, asc, count, sql, and, isNotNull, gt, lt, between, or, ilike, sum } from 'drizzle-orm';
import { projects, chat, message, user, subscriptions, tokenConsumption } from './schema';
import { db } from './db';

// Types for sorting and pagination
export type ProjectSortField = 'createdAt' | 'totalChats' | 'totalMessages';
export type UserSortField = 'createdAt' | 'name' | 'email' | 'projectCount' | 'chatCount' | 'messageCount';
export type SubscriptionSortField = 'createdAt' | 'status' | 'planId' | 'credits' | 'creditsUsed';
export type TokenConsumptionSortField = 'createdAt' | 'totalTokens' | 'totalCost' | 'model' | 'cacheDiscountPercent';
export type SortField = ProjectSortField | UserSortField | SubscriptionSortField | TokenConsumptionSortField;
export type SortDirection = 'asc' | 'desc';

export interface PaginationParams {
  page: number;
  pageSize: number;
  sortField?: SortField;
  sortDirection?: SortDirection;
  searchTerm?: string;
  filters?: Record<string, string[]>;
}

/**
 * Get all projects with their stats with pagination and sorting
 */
export async function getAllProjectsWithStats({
  page = 1,
  pageSize = 10,
  sortField = 'createdAt',
  sortDirection = 'desc',
  searchTerm = ''
}: PaginationParams) {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * pageSize;
    
    // First get the total count for pagination info
    const totalCountResult = await db
      .select({ count: count() })
      .from(projects);
    
    const totalCount = Number(totalCountResult[0].count);
    
    // Build the base query
    const baseQuery = db
      .select({
        id: projects.id,
        appName: projects.appName,
        slug: projects.slug,
        userId: projects.userId,
        userName: user.name,
        userEmail: user.email,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        primaryColor: projects.primaryColor,
        icon: projects.icon,
      })
      .from(projects)
      .leftJoin(user, eq(projects.userId, user.id));
    
    // Apply search filter if provided
    let filteredQuery = searchTerm 
      ? baseQuery.where(
          sql`LOWER(${projects.appName}) LIKE ${'%' + searchTerm.toLowerCase() + '%'} OR 
              LOWER(${projects.slug}) LIKE ${'%' + searchTerm.toLowerCase() + '%'} OR 
              LOWER(${user.name}) LIKE ${'%' + searchTerm.toLowerCase() + '%'} OR 
              LOWER(${user.email}) LIKE ${'%' + searchTerm.toLowerCase() + '%'}`
        )
      : baseQuery;
    
    // Apply sorting
    const sortedQuery = sortField === 'createdAt'
      ? filteredQuery.orderBy(
          sortDirection === 'desc' ? desc(projects.createdAt) : asc(projects.createdAt)
        )
      : filteredQuery;
    
    // Apply pagination
    const paginatedQuery = sortedQuery
      .limit(pageSize)
      .offset(offset);
    
    // Execute the query
    const paginatedProjects = await paginatedQuery;
    
    // Get chat counts for each project
    const chatCounts = await db
      .select({
        projectId: chat.projectId,
        totalChats: count(),
      })
      .from(chat)
      .where(isNotNull(chat.projectId))
      .groupBy(chat.projectId);
    
    // Get message counts for each project
    const messageCounts = await db
      .select({
        projectId: message.projectId,
        totalMessages: count(),
      })
      .from(message)
      .where(isNotNull(message.projectId))
      .groupBy(message.projectId);
    
    // Combine the results
    let projectsWithStats = paginatedProjects.map(project => {
      const projectChatCount = chatCounts.find(c => c.projectId === project.id)?.totalChats || 0;
      const projectMessageCount = messageCounts.find(m => m.projectId === project.id)?.totalMessages || 0;
      
      return {
        ...project,
        totalChats: Number(projectChatCount),
        totalMessages: Number(projectMessageCount),
        lastActive: project.updatedAt, // Use updatedAt as a proxy for lastActive
      };
    });
    
    // Apply sorting for chat and message counts in memory
    if (sortField === 'totalChats') {
      projectsWithStats = projectsWithStats.sort((a, b) => {
        return sortDirection === 'desc'
          ? b.totalChats - a.totalChats
          : a.totalChats - b.totalChats;
      });
    } else if (sortField === 'totalMessages') {
      projectsWithStats = projectsWithStats.sort((a, b) => {
        return sortDirection === 'desc'
          ? b.totalMessages - a.totalMessages
          : a.totalMessages - b.totalMessages;
      });
    }
    
    return {
      projects: projectsWithStats,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
        currentPage: page,
        pageSize,
      }
    };
  } catch (error) {
    console.error('Failed to get projects with stats:', error);
    throw error;
  }
}

/**
 * Get project details with chat and message stats
 * This is a mock function that returns dummy data
 */
export async function getProjectDetails(projectId: string) {
  // In a real implementation, we would query the database
  // For now, return dummy data for the specified project ID
  const dummyProjects = {
    '1': {
      id: '1',
      appName: 'Fitness Tracker',
      slug: 'fitness-tracker',
      description: 'A comprehensive fitness tracking application',
      userId: 'user-1',
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      createdAt: new Date('2025-01-15'),
      updatedAt: new Date('2025-04-10'),
      totalChats: 12,
      totalMessages: 156,
      lastActive: new Date('2025-04-10'),
      primaryColor: '#4F46E5',
      icon: 'https://via.placeholder.com/50',
      chats: [
        {
          id: 'chat-1-1',
          title: 'Workout Planning',
          createdAt: new Date('2025-03-01'),
          messageCount: 24,
          lastMessageAt: new Date('2025-04-10'),
        },
        {
          id: 'chat-1-2',
          title: 'Nutrition Advice',
          createdAt: new Date('2025-03-05'),
          messageCount: 18,
          lastMessageAt: new Date('2025-04-08'),
        },
        {
          id: 'chat-1-3',
          title: 'Progress Tracking',
          createdAt: new Date('2025-03-10'),
          messageCount: 32,
          lastMessageAt: new Date('2025-04-09'),
        },
      ],
      messageActivity: [
        { date: '2025-04-01', count: 12 },
        { date: '2025-04-02', count: 8 },
        { date: '2025-04-03', count: 15 },
        { date: '2025-04-04', count: 10 },
        { date: '2025-04-05', count: 5 },
        { date: '2025-04-06', count: 7 },
        { date: '2025-04-07', count: 14 },
        { date: '2025-04-08', count: 18 },
        { date: '2025-04-09', count: 22 },
        { date: '2025-04-10', count: 16 },
      ],
    },
    '2': {
      id: '2',
      appName: 'Recipe Manager',
      slug: 'recipe-manager',
      description: 'An app to store and organize your favorite recipes',
      userId: 'user-2',
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
      createdAt: new Date('2025-02-20'),
      updatedAt: new Date('2025-04-12'),
      totalChats: 8,
      totalMessages: 94,
      lastActive: new Date('2025-04-12'),
      primaryColor: '#10B981',
      icon: 'https://via.placeholder.com/50',
      chats: [
        {
          id: 'chat-2-1',
          title: 'Italian Recipes',
          createdAt: new Date('2025-03-15'),
          messageCount: 16,
          lastMessageAt: new Date('2025-04-11'),
        },
        {
          id: 'chat-2-2',
          title: 'Dessert Ideas',
          createdAt: new Date('2025-03-20'),
          messageCount: 22,
          lastMessageAt: new Date('2025-04-12'),
        },
      ],
      messageActivity: [
        { date: '2025-04-03', count: 6 },
        { date: '2025-04-04', count: 4 },
        { date: '2025-04-05', count: 8 },
        { date: '2025-04-06', count: 5 },
        { date: '2025-04-07', count: 10 },
        { date: '2025-04-08', count: 7 },
        { date: '2025-04-09', count: 12 },
        { date: '2025-04-10', count: 9 },
        { date: '2025-04-11', count: 14 },
        { date: '2025-04-12', count: 11 },
      ],
    },
  };

  return dummyProjects[projectId as keyof typeof dummyProjects] || null;
}

/**
 * Get system-wide analytics
 * This is a mock function that returns dummy data
 */
export async function getSystemAnalytics() {
  // In a real implementation, we would query the database
  // For now, return dummy data
  return {
    totalProjects: 42,
    totalUsers: 38,
    totalChats: 256,
    totalMessages: 3842,
    newProjectsLast30Days: 15,
    newUsersLast30Days: 12,
    messagesByDay: [
      { date: '2025-03-15', count: 86 },
      { date: '2025-03-16', count: 92 },
      { date: '2025-03-17', count: 105 },
      { date: '2025-03-18', count: 120 },
      { date: '2025-03-19', count: 98 },
      { date: '2025-03-20', count: 110 },
      { date: '2025-03-21', count: 115 },
      { date: '2025-03-22', count: 125 },
      { date: '2025-03-23', count: 130 },
      { date: '2025-03-24', count: 140 },
      { date: '2025-03-25', count: 135 },
      { date: '2025-03-26', count: 145 },
      { date: '2025-03-27', count: 150 },
      { date: '2025-03-28', count: 142 },
      { date: '2025-03-29', count: 138 },
      { date: '2025-03-30', count: 152 },
      { date: '2025-03-31', count: 160 },
      { date: '2025-04-01', count: 155 },
      { date: '2025-04-02', count: 165 },
      { date: '2025-04-03', count: 170 },
      { date: '2025-04-04', count: 175 },
      { date: '2025-04-05', count: 168 },
      { date: '2025-04-06', count: 172 },
      { date: '2025-04-07', count: 180 },
      { date: '2025-04-08', count: 185 },
      { date: '2025-04-09', count: 190 },
      { date: '2025-04-10', count: 195 },
      { date: '2025-04-11', count: 200 },
      { date: '2025-04-12', count: 205 },
      { date: '2025-04-13', count: 210 },
    ],
    projectsByDay: [
      { date: '2025-03-15', count: 28 },
      { date: '2025-03-20', count: 30 },
      { date: '2025-03-25', count: 32 },
      { date: '2025-03-30', count: 35 },
      { date: '2025-04-05', count: 38 },
      { date: '2025-04-10', count: 40 },
      { date: '2025-04-15', count: 42 },
    ],
    usersByDay: [
      { date: '2025-03-15', count: 26 },
      { date: '2025-03-20', count: 28 },
      { date: '2025-03-25', count: 30 },
      { date: '2025-03-30', count: 32 },
      { date: '2025-04-05', count: 34 },
      { date: '2025-04-10', count: 36 },
      { date: '2025-04-15', count: 38 },
    ],
    topActiveProjects: [
      { id: '1', name: 'Fitness Tracker', messageCount: 156 },
      { id: '3', name: 'Task Planner', messageCount: 203 },
      { id: '5', name: 'Travel Planner', messageCount: 142 },
      { id: '2', name: 'Recipe Manager', messageCount: 94 },
      { id: '4', name: 'Budget Tracker', messageCount: 78 },
    ],
  };
}

/**
 * Search messages across all projects
 * This is a mock function that returns dummy data
 */
export async function searchMessages(query: string) {
  // In a real implementation, we would query the database
  // For now, return dummy data
  return [
    {
      id: 'msg-1',
      content: { text: 'I need help with my workout plan' },
      createdAt: new Date('2025-04-10'),
      role: 'user',
      chatId: 'chat-1-1',
      chatTitle: 'Workout Planning',
      projectId: '1',
      projectName: 'Fitness Tracker',
      userName: 'John Doe',
    },
    {
      id: 'msg-2',
      content: { text: 'Can you suggest a workout plan for beginners?' },
      createdAt: new Date('2025-04-09'),
      role: 'user',
      chatId: 'chat-1-1',
      chatTitle: 'Workout Planning',
      projectId: '1',
      projectName: 'Fitness Tracker',
      userName: 'John Doe',
    },
    {
      id: 'msg-3',
      content: { text: 'I want to track my daily calorie intake' },
      createdAt: new Date('2025-04-08'),
      role: 'user',
      chatId: 'chat-1-2',
      chatTitle: 'Nutrition Advice',
      projectId: '1',
      projectName: 'Fitness Tracker',
      userName: 'John Doe',
    },
  ];
}

/**
 * Get the latest chat for a project
 * This function fetches the most recent chat for a given project
 */
export async function getLatestProjectChat(projectId: string) {
  try {
    // Query to get the latest chat for the project
    const latestChat = await db
      .select({
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt,
      })
      .from(chat)
      .where(eq(chat.projectId, projectId))
      .orderBy(desc(chat.updatedAt))
      .limit(1);
    
    // Return the first result or null if no chats found
    return latestChat.length > 0 ? latestChat[0] : null;
  } catch (error) {
    console.error('Error fetching latest project chat:', error);
    throw new Error('Failed to fetch latest project chat');
  }
}

/**
 * Get all users with their stats with pagination, sorting, and filtering
 */
export async function getAllUsers({
  page = 1,
  pageSize = 10,
  sortField = 'createdAt',
  sortDirection = 'desc',
  searchTerm = '',
  filters = {}
}: PaginationParams) {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * pageSize;
    
    // First get the total count for pagination info
    const totalCountResult = await db
      .select({ count: count() })
      .from(user);
    
    const totalCount = Number(totalCountResult[0].count);
    
    // Build a query with all conditions in one go
    let whereConditions: any[] = [];
    
    // Add search condition if search term is provided
    if (searchTerm) {
      whereConditions.push(sql`(LOWER(${user.name}) LIKE ${'%' + searchTerm.toLowerCase() + '%'} OR 
                              LOWER(${user.email}) LIKE ${'%' + searchTerm.toLowerCase() + '%'})`);
    }
    
    // Add provider filter if provided
    if (filters['provider'] && filters['provider'].length > 0) {
      whereConditions.push(sql`${user.provider} IN (${filters['provider'].join(',')})`);
    }
    
    // Combine all conditions with AND
    let whereClause;
    if (whereConditions.length > 0) {
      if (whereConditions.length === 1) {
        whereClause = whereConditions[0];
      } else {
        whereClause = sql`${whereConditions[0]}`;
        for (let i = 1; i < whereConditions.length; i++) {
          whereClause = sql`${whereClause} AND ${whereConditions[i]}`;
        }
      }
    }
    
    // Determine the sort column and direction
    let orderByClause;
    if (sortField === 'createdAt') {
      orderByClause = sortDirection === 'desc' ? desc(user.createdAt) : asc(user.createdAt);
    } else if (sortField === 'name') {
      orderByClause = sortDirection === 'desc' ? desc(user.name) : asc(user.name);
    } else if (sortField === 'email') {
      orderByClause = sortDirection === 'desc' ? desc(user.email) : asc(user.email);
    } else {
      // Default sort by createdAt
      orderByClause = desc(user.createdAt);
    }
    
    // Execute the query with all conditions
    const paginatedUsers = await db
      .select({
        id: user.id,
        name: user.name,
        email: user.email,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        provider: user.provider,
      })
      .from(user)
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(pageSize)
      .offset(offset);
    
    // Get project counts for each user
    const projectCounts = await db
      .select({
        userId: projects.userId,
        projectCount: count(),
      })
      .from(projects)
      .where(isNotNull(projects.userId))
      .groupBy(projects.userId);
    
    // Get chat counts for each user
    const chatCounts = await db
      .select({
        userId: chat.userId,
        chatCount: count(),
      })
      .from(chat)
      .where(isNotNull(chat.userId))
      .groupBy(chat.userId);
    
    // Get message counts for each user
    const messageCounts = await db
      .select({
        userId: message.userId,
        messageCount: count(),
      })
      .from(message)
      .where(isNotNull(message.userId))
      .groupBy(message.userId);
    
    // Combine the results
    let usersWithStats = paginatedUsers.map(user => {
      const userProjectCount = projectCounts.find(p => p.userId === user.id)?.projectCount || 0;
      const userChatCount = chatCounts.find(c => c.userId === user.id)?.chatCount || 0;
      const userMessageCount = messageCounts.find(m => m.userId === user.id)?.messageCount || 0;
      
      return {
        ...user,
        projectCount: Number(userProjectCount),
        chatCount: Number(userChatCount),
        messageCount: Number(userMessageCount),
        lastActive: user.updatedAt, // Use updatedAt as a proxy for lastActive
      };
    });
    
    // Apply sorting for counts in memory if needed
    if (sortField === 'projectCount') {
      usersWithStats = usersWithStats.sort((a, b) => {
        return sortDirection === 'desc'
          ? b.projectCount - a.projectCount
          : a.projectCount - b.projectCount;
      });
    } else if (sortField === 'chatCount') {
      usersWithStats = usersWithStats.sort((a, b) => {
        return sortDirection === 'desc'
          ? b.chatCount - a.chatCount
          : a.chatCount - b.chatCount;
      });
    } else if (sortField === 'messageCount') {
      usersWithStats = usersWithStats.sort((a, b) => {
        return sortDirection === 'desc'
          ? b.messageCount - a.messageCount
          : a.messageCount - b.messageCount;
      });
    }
    
    return {
      data: usersWithStats,
      pagination: {
        page,
        pageSize,
        pageCount: Math.ceil(totalCount / pageSize),
        totalCount,
      },
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    throw new Error('Failed to fetch users');
  }
}

/**
 * Get all subscriptions with pagination, sorting, and filtering
 */
export async function getAllSubscriptions({
  page = 1,
  pageSize = 10,
  sortField = 'createdAt',
  sortDirection = 'desc',
  searchTerm = '',
  filters = {}
}: PaginationParams) {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * pageSize;
    
    // First get the total count for pagination info
    const totalCountResult = await db
      .select({ count: count() })
      .from(subscriptions);
    
    const totalCount = Number(totalCountResult[0].count);
    
    // Build the base query without join to user table since there's a type mismatch
    // The userId in subscriptions is varchar while id in user is uuid
    const baseQuery = db
      .select({
        id: subscriptions.id,
        userId: subscriptions.userId,
        status: subscriptions.status,
        planId: subscriptions.planId,
        subscriptionId: subscriptions.subscriptionId,
        credits: subscriptions.credits,
        creditsUsed: subscriptions.creditsUsed,
        isActive: subscriptions.isActive,
        provider: subscriptions.provider,
        resetDate: subscriptions.resetDate,
        createdAt: subscriptions.createdAt,
        updatedAt: subscriptions.updatedAt,
      })
      .from(subscriptions);
    
    // Prepare where conditions array
    const whereConditions: any[] = [];
    
    // Apply search filter if provided
    if (searchTerm) {
      whereConditions.push(
        sql`LOWER(${subscriptions.planId}) LIKE ${'%' + searchTerm.toLowerCase() + '%'} OR 
            LOWER(${subscriptions.status}) LIKE ${'%' + searchTerm.toLowerCase() + '%'} OR
            LOWER(${subscriptions.userId}) LIKE ${'%' + searchTerm.toLowerCase() + '%'}`
      );
    }
    
    // Add status filter if provided
    if (filters['status'] && filters['status'].length > 0) {
      whereConditions.push(sql`${subscriptions.status} IN (${filters['status'].join(',')})`);
    }
    
    // Add plan filter if provided
    if (filters['planId'] && filters['planId'].length > 0) {
      whereConditions.push(sql`${subscriptions.planId} IN (${filters['planId'].join(',')})`);
    }
    
    // Add active filter if provided
    if (filters['isActive'] && filters['isActive'].length > 0) {
      const isActiveValue = filters['isActive'][0] === 'true';
      whereConditions.push(sql`${subscriptions.isActive} = ${isActiveValue}`);
    }
    
    // Add provider filter if provided
    if (filters['provider'] && filters['provider'].length > 0) {
      whereConditions.push(sql`${subscriptions.provider} IN (${filters['provider'].join(',')})`);
    }
    
    // Combine all conditions with AND
    let whereClause;
    if (whereConditions.length > 0) {
      if (whereConditions.length === 1) {
        whereClause = whereConditions[0];
      } else {
        whereClause = sql`${whereConditions[0]}`;
        for (let i = 1; i < whereConditions.length; i++) {
          whereClause = sql`${whereClause} AND ${whereConditions[i]}`;
        }
      }
    }
    
    // Apply where clause if exists
    const filteredQuery = whereClause ? baseQuery.where(whereClause) : baseQuery;
    
    // Determine the sort column and direction
    let orderByClause;
    if (sortField === 'createdAt') {
      orderByClause = sortDirection === 'desc' ? desc(subscriptions.createdAt) : asc(subscriptions.createdAt);
    } else if (sortField === 'status') {
      orderByClause = sortDirection === 'desc' ? desc(subscriptions.status) : asc(subscriptions.status);
    } else if (sortField === 'planId') {
      orderByClause = sortDirection === 'desc' ? desc(subscriptions.planId) : asc(subscriptions.planId);
    } else if (sortField === 'credits') {
      orderByClause = sortDirection === 'desc' ? desc(subscriptions.credits) : asc(subscriptions.credits);
    } else if (sortField === 'creditsUsed') {
      orderByClause = sortDirection === 'desc' ? desc(subscriptions.creditsUsed) : asc(subscriptions.creditsUsed);
    } else {
      // Default sort by createdAt
      orderByClause = desc(subscriptions.createdAt);
    }
    
    // Apply sorting and pagination
    const paginatedSubscriptions = await filteredQuery
      .orderBy(orderByClause)
      .limit(pageSize)
      .offset(offset);
    
    // Calculate remaining credits for each subscription
    const subscriptionsWithStats = await Promise.all(paginatedSubscriptions.map(async subscription => {
      const remainingCredits = Number(subscription.credits) - Number(subscription.creditsUsed);
      const percentUsed = subscription.credits > 0 ? (subscription.creditsUsed / subscription.credits) * 100 : 0;
      
      // Fetch actual user information for each subscription
      const userInfo = await db
        .select({
          name: user.name,
          email: user.email
        })
        .from(user)
        .where(sql`CAST(${user.id} AS VARCHAR) = ${subscription.userId}`)
        .limit(1);
      
      return {
        ...subscription,
        remainingCredits,
        percentUsed: Math.round(percentUsed),
        // Add actual user info if found, otherwise use placeholder
        userName: userInfo.length > 0 ? userInfo[0].name : 'User ' + subscription.userId.substring(0, 8),
        userEmail: userInfo.length > 0 ? userInfo[0].email : subscription.userId,
      };
    }));
    
    return {
      data: subscriptionsWithStats,
      pagination: {
        page,
        pageSize,
        pageCount: Math.ceil(totalCount / pageSize),
        totalCount,
      },
    };
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    throw new Error('Failed to fetch subscriptions');
  }
}

/**
 * Get token consumption history for a specific user
 */
export async function getUserTokenConsumption({
  userId,
  page = 1,
  pageSize = 10,
  sortField = 'createdAt',
  sortDirection = 'desc',
  startDate,
  endDate
}: {
  userId: string;
  page: number;
  pageSize: number;
  sortField?: TokenConsumptionSortField;
  sortDirection?: SortDirection;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * pageSize;
    
    // Create where clause with date range filters if provided
    let conditions = [eq(tokenConsumption.userId, userId)];
    
    if (startDate && endDate) {
      conditions.push(between(tokenConsumption.createdAt, startDate, endDate));
    } else if (startDate) {
      conditions.push(gt(tokenConsumption.createdAt, startDate));
    } else if (endDate) {
      conditions.push(lt(tokenConsumption.createdAt, endDate));
    }
    
    // Combine all conditions with AND
    const whereClause = conditions.length === 1 ? conditions[0] : and(...conditions);
    
    // First get the total count for pagination info
    const countQuery = db
      .select({ count: count() })
      .from(tokenConsumption)
      .where(whereClause);
    
    const totalCountResult = await countQuery;
    const totalCount = Number(totalCountResult[0].count);
    
    // Build the base query
    const baseQuery = db
      .select({
        id: tokenConsumption.id,
        model: tokenConsumption.model,
        promptTokens: tokenConsumption.promptTokens,
        completionTokens: tokenConsumption.completionTokens,
        totalTokens: tokenConsumption.totalTokens,
        chatId: tokenConsumption.chatId,
        projectId: tokenConsumption.projectId,
        messageId: tokenConsumption.messageId,
        userId: tokenConsumption.userId,
        createdAt: tokenConsumption.createdAt,
        inputCost: tokenConsumption.inputCost,
        outputCost: tokenConsumption.outputCost,
        cachingDiscount: tokenConsumption.cachingDiscount,
        cacheDiscountPercent: tokenConsumption.cacheDiscountPercent,
        subtotal: tokenConsumption.subtotal,
        totalCost: tokenConsumption.totalCost,
        creditsConsumed: tokenConsumption.creditsConsumed,
        discountedCredits: tokenConsumption.discountedCredits,
        discountReason: tokenConsumption.discountReason,
      })
      .from(tokenConsumption)
      .where(whereClause);
    
    // Determine the sort column and direction
    let orderByClause;
    if (sortField === 'createdAt') {
      orderByClause = sortDirection === 'desc' ? desc(tokenConsumption.createdAt) : asc(tokenConsumption.createdAt);
    } else if (sortField === 'totalTokens') {
      orderByClause = sortDirection === 'desc' ? desc(tokenConsumption.totalTokens) : asc(tokenConsumption.totalTokens);
    } else if (sortField === 'totalCost') {
      orderByClause = sortDirection === 'desc' ? desc(tokenConsumption.totalCost) : asc(tokenConsumption.totalCost);
    } else if (sortField === 'cacheDiscountPercent') {
      // Handle cacheDiscountPercent sorting
      orderByClause = sortDirection === 'desc' ? desc(tokenConsumption.cacheDiscountPercent) : asc(tokenConsumption.cacheDiscountPercent);
    } else if (sortField === 'model') {
      // For backward compatibility, if model is selected, sort by model
      // but we'll actually use cacheDiscountPercent since we've replaced the model column
      orderByClause = sortDirection === 'desc' ? desc(tokenConsumption.cacheDiscountPercent) : asc(tokenConsumption.cacheDiscountPercent);
    } else {
      // Default sort by createdAt
      orderByClause = desc(tokenConsumption.createdAt);
    }
    
    // Apply sorting and pagination
    const paginatedConsumption = await baseQuery
      .orderBy(orderByClause)
      .limit(pageSize)
      .offset(offset);
    
    // Get caching breakdown
    const cachingBreakdown = await db
      .select({
        hasCaching: sql`CASE WHEN COALESCE(${tokenConsumption.cacheDiscountPercent}, 0) > 0 THEN 'Cached' ELSE 'Not Cached' END`,
        totalRequests: count(),
        totalTokens: sql`SUM(${tokenConsumption.totalTokens})`,
        totalCost: sql`SUM(${tokenConsumption.totalCost})`,
        cachingSavings: sql`SUM(COALESCE(${tokenConsumption.cachingDiscount}, 0))`,
      })
      .from(tokenConsumption)
      .where(eq(tokenConsumption.userId, userId))
      .groupBy(sql`CASE WHEN COALESCE(${tokenConsumption.cacheDiscountPercent}, 0) > 0 THEN 'Cached' ELSE 'Not Cached' END`);
    
    // Get summary statistics
    const summaryStats = await db
      .select({
        totalPromptTokens: sql`SUM(${tokenConsumption.promptTokens})`,
        totalCompletionTokens: sql`SUM(${tokenConsumption.completionTokens})`,
        totalTokensSum: sql`SUM(${tokenConsumption.totalTokens})`,
        totalCostSum: sql`SUM(${tokenConsumption.totalCost})`,
        totalCachingDiscount: sql`SUM(COALESCE(${tokenConsumption.cachingDiscount}, 0))`,
        totalCreditsConsumed: sql`SUM(${tokenConsumption.creditsConsumed})`,
        totalDiscountedCredits: sql`SUM(${tokenConsumption.discountedCredits})`,
        requestCount: count(),
      })
      .from(tokenConsumption)
      .where(eq(tokenConsumption.userId, userId));

    return {
      data: paginatedConsumption,
      summary: summaryStats[0],
      cachingBreakdown,
      pagination: {
        page,
        pageSize,
        pageCount: Math.ceil(totalCount / pageSize),
        totalCount,
      },
    };
  } catch (error) {
    console.error('Error fetching token consumption:', error);
    throw new Error('Failed to fetch token consumption');
  }
}

/**
 * Get token consumption for a specific subscription
 */
export async function getSubscriptionTokenConsumption({
  subscriptionId,
  page = 1,
  pageSize = 10,
  sortField = 'createdAt',
  sortDirection = 'desc',
  startDate,
  endDate
}: {
  subscriptionId: string;
  page: number;
  pageSize: number;
  sortField?: TokenConsumptionSortField;
  sortDirection?: SortDirection;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    // First get the subscription to find the associated userId
    const subscription = await db
      .select({
        userId: subscriptions.userId,
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, subscriptionId));
    
    if (!subscription || subscription.length === 0) {
      throw new Error('Subscription not found');
    }
    
    const userId = subscription[0].userId;
    
    // Now use the existing function to get token consumption for this user
    return getUserTokenConsumption({
      userId,
      page,
      pageSize,
      sortField,
      sortDirection,
      startDate,
      endDate
    });
  } catch (error) {
    console.error('Error fetching subscription token consumption:', error);
    throw new Error('Failed to fetch subscription token consumption');
  }
}

/**
 * Error categories for classification - simplified to 5 main buckets
 */
export const ERROR_CATEGORIES = {
  // Database errors - ONLY database-specific errors
  DATABASE_ERROR: [
    'relation', 'table', 'column', 'database', 'postgresql',
    'postgres', 'sql', 'query', 'constraint', 'public.',
    'foreign key', 'unique', 'database connection', 'supabase',
    'code', '42p01', '42703', '42601', '23505', '22p02'
  ],
  
  // Import/Module errors
  MODULE_ERROR: [
    'no default export', 'import', 'export', 'unable to resolve module',
    'cannot find module', 'module not found', 'dependency', 'no exported member',
    'element type is invalid', 'expected a string', 'wrong import'
  ],
  
  // React/UI errors
  UI_ERROR: [
    'invalid hook call', 'hooks can only be called', 'must be used within',
    'navigationcontainer', 'usetheme', 'useauth', 'context provider',
    'property doesn\'t exist', 'datetimepicker', 'component', 'render'
  ],
  
  // JavaScript/TypeScript errors
  CODE_ERROR: [
    'type error', 'typescript error', 'undefined is not', 'null is not',
    'is not a function', 'cannot read property', 'unexpected token',
    'cannot call', 'not callable', 'syntax error'
  ],
  
  // Fallback for other errors
  OTHER_ERROR: [
    'error', 'issue', 'problem', 'bug', 'not working', 'failed',
    'network', 'api', 'request', 'response', 'server', 'client'
  ]
};

/**
 * Get error category statistics for the chart
 */
export async function getErrorCategoryStats(timeRange = 30) {
  try {
    // Calculate the date range
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeRange);
    
    // Build the error patterns for the query
    const errorPatterns = [
      'encountered an error',
      'getting an error',
      'error occurred',
      'facing an issue',
      'not working',
      'failed to',
      'fix this error',
      'fix the error',
      'error message'
    ];
    
    // Create the conditions for the query
    const conditions = errorPatterns.map(pattern => 
      sql`CAST(${message.content} AS TEXT) ILIKE ${'%' + pattern + '%'}`
    );
    
    // Get all error messages for the time period
    const errorMessages = await db
      .select({
        id: message.id,
        content: message.content,
        createdAt: message.createdAt
      })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        gt(message.createdAt, startDate),
        or(...conditions)
      ));
    
    // Process and categorize the error messages
    const categoryCounts: Record<string, number> = {};
    
    // Initialize counts for all categories
    Object.keys(ERROR_CATEGORIES).forEach(category => {
      categoryCounts[category] = 0;
    });
    
    // Count errors by category
    errorMessages.forEach(msg => {
      const content = typeof msg.content === 'string' 
        ? msg.content 
        : JSON.stringify(msg.content);
      
      const category = categorizeError(content);
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });
    
    // Format data for the chart
    const chartData = Object.entries(categoryCounts).map(([category, count]) => ({
      name: formatCategoryName(category),
      value: count as number,
      category
    }));
    
    // Sort by count in descending order
    chartData.sort((a, b) => (b.value as number) - (a.value as number));
    
    return {
      chartData,
      total: errorMessages.length
    };
  } catch (error) {
    console.error('Error fetching error category stats:', error);
    throw new Error('Failed to fetch error category statistics');
  }
}

/**
 * Format category name for display
 */
function formatCategoryName(category: string): string {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Helper function to categorize an error message with priority for database errors
 */
function categorizeError(content: string | any): string {
  // Handle JSON content
  let textContent = '';
  
  try {
    // If content is already a string, use it directly
    if (typeof content === 'string') {
      textContent = content;
    } 
    // If content is an array of objects with text property (common format from the database)
    else if (Array.isArray(content)) {
      textContent = content
        .filter(item => item && typeof item === 'object' && 'text' in item)
        .map(item => item.text)
        .join(' ');
    } 
    // If content is a JSON string, parse it
    else if (typeof content === 'object') {
      textContent = JSON.stringify(content);
    }
  } catch (e) {
    console.error('Error parsing error content:', e);
    textContent = String(content);
  }
  
  // Convert to lowercase for case-insensitive matching
  textContent = textContent.toLowerCase();
  
  // First check for database errors with higher priority
  const dbPatterns = ERROR_CATEGORIES.DATABASE_ERROR;
  if (dbPatterns.some(pattern => textContent.includes(pattern))) {
    return 'DATABASE_ERROR';
  }
  
  // Check for specific module errors
  if (textContent.includes('element type is invalid') || 
      textContent.includes('expected a string') ||
      textContent.includes('no default export') ||
      textContent.includes('cannot find module')) {
    return 'MODULE_ERROR';
  }
  
  // Check for specific UI errors
  if (textContent.includes('invalid hook call') ||
      textContent.includes('must be used within') ||
      textContent.includes('property') && textContent.includes('exist') ||
      textContent.includes('datetimepicker')) {
    return 'UI_ERROR';
  }
  
  // Then check other categories
  for (const [category, patterns] of Object.entries(ERROR_CATEGORIES)) {
    if (category !== 'DATABASE_ERROR' && // Skip DATABASE_ERROR as we already checked it
        patterns.some(pattern => textContent.includes(pattern))) {
      return category;
    }
  }
  
  return 'OTHER_ERROR';
}

/**
 * Get error analytics data from user messages
 */
/**
 * Get user messages with token analytics - shows write and read tokens per conversation turn
 */
export async function getUserMessageTokenAnalytics({
  page = 1,
  pageSize = 10,
  startDate,
  endDate
}: {
  page: number;
  pageSize: number;
  startDate?: Date;
  endDate?: Date;
}) {
  try {
    // Set default date range if not provided (last 7 days)
    if (!startDate) {
      startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
    }
    
    if (!endDate) {
      endDate = new Date();
    }
    
    // Calculate offset for pagination
    const offset = (page - 1) * pageSize;
    
    // Get total count of user messages in the date range
    const totalCountResult = await db
      .select({ count: count() })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        between(message.createdAt, startDate, endDate)
      ));
    
    const totalCount = Number(totalCountResult[0].count);
    
    // Get user messages with their ID and content
    const userMessages = await db
      .select({
        id: message.id,
        content: message.content,
        createdAt: message.createdAt,
        chatId: message.chatId,
        projectId: message.projectId,
        userId: message.userId,
      })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        between(message.createdAt, startDate, endDate)
      ))
      .orderBy(desc(message.createdAt))
      .limit(pageSize)
      .offset(offset);
    
    // For each user message, get the assistant and tool messages that follow
    const messagesWithTokens = await Promise.all(userMessages.map(async (userMsg) => {
      // Get all assistant messages that respond to this user message
      const assistantMessages = await db
        .select({
          id: message.id,
          content: message.content,
          role: message.role,
          createdAt: message.createdAt,
        })
        .from(message)
        .where(and(
          eq(message.role, 'assistant'),
          eq(message.parentUserMessageId, userMsg.id)
        ))
        .orderBy(asc(message.createdAt));
      
      // Get all tool messages that respond to this user message
      const toolMessages = await db
        .select({
          id: message.id,
          content: message.content,
          role: message.role,
          createdAt: message.createdAt,
        })
        .from(message)
        .where(and(
          eq(message.role, 'tool'),
          eq(message.parentUserMessageId, userMsg.id)
        ))
        .orderBy(asc(message.createdAt));
      
      // Calculate token counts
      const assistantTokenCount = assistantMessages.reduce((sum, msg) => {
        // Estimate tokens as characters / 4 (rough approximation)
        const content = typeof msg.content === 'string' 
          ? msg.content 
          : JSON.stringify(msg.content);
        return sum + Math.ceil(content.length / 4);
      }, 0);
      
      const toolTokenCount = toolMessages.reduce((sum, msg) => {
        // Estimate tokens as characters / 4 (rough approximation)
        const content = typeof msg.content === 'string' 
          ? msg.content 
          : JSON.stringify(msg.content);
        return sum + Math.ceil(content.length / 4);
      }, 0);
      
      // Extract tool names from tool messages
      const toolCalls = toolMessages.map(msg => {
        try {
          const content = typeof msg.content === 'string' 
            ? JSON.parse(msg.content) 
            : msg.content;
          
          if (Array.isArray(content)) {
            return content.map(item => item.toolName).filter(Boolean);
          }
          return [];
        } catch (e) {
          return [];
        }
      }).flat();
      
      // Count occurrences of each tool
      const toolCallCounts: Record<string, number> = {};
      toolCalls.forEach(tool => {
        if (tool) {
          toolCallCounts[tool] = (toolCallCounts[tool] || 0) + 1;
        }
      });
      
      // Calculate costs
      const writeTokenCost = (assistantTokenCount * 15) / 1000000; // $15 per million tokens
      const readTokenCost = (toolTokenCount * 3) / 1000000; // $3 per million tokens
      const totalCost = writeTokenCost + readTokenCost;
      
      return {
        id: userMsg.id,
        content: typeof userMsg.content === 'string' 
          ? userMsg.content.substring(0, 100) + (userMsg.content.length > 100 ? '...' : '')
          : JSON.stringify(userMsg.content).substring(0, 100) + '...',
        createdAt: userMsg.createdAt,
        chatId: userMsg.chatId,
        userId: userMsg.userId,
        projectId: userMsg.projectId,
        assistantMessageCount: assistantMessages.length,
        toolMessageCount: toolMessages.length,
        writeTokens: assistantTokenCount,
        readTokens: toolTokenCount,
        totalTokens: assistantTokenCount + toolTokenCount,
        writeTokenCost: writeTokenCost.toFixed(6),
        readTokenCost: readTokenCost.toFixed(6),
        totalCost: totalCost.toFixed(6),
        toolCalls: toolCallCounts
      };
    }));
    
    return {
      messages: messagesWithTokens,
      pagination: {
        total: totalCount,
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    };
  } catch (error) {
    console.error('Error fetching token analytics:', error);
    throw new Error('Failed to fetch token analytics');
  }
}

/**
 * Get detailed message trace for a specific parent user message
 */
export async function getMessageTraceByParentId(parentId: string) {
  try {
    // Get the original user message
    const userMessage = await db
      .select({
        id: message.id,
        content: message.content,
        role: message.role,
        createdAt: message.createdAt,
        chatId: message.chatId,
        projectId: message.projectId,
        userId: message.userId,
      })
      .from(message)
      .where(eq(message.id, parentId))
      .limit(1);
    
    if (!userMessage.length) {
      throw new Error('Message not found');
    }
    
    // Get all messages in the conversation turn (assistant and tool)
    const conversationMessages = await db
      .select({
        id: message.id,
        content: message.content,
        role: message.role,
        createdAt: message.createdAt,
        parentUserMessageId: message.parentUserMessageId,
      })
      .from(message)
      .where(eq(message.parentUserMessageId, parentId))
      .orderBy(asc(message.createdAt));
    
    // Process messages to extract tool calls and results
    const processedMessages = conversationMessages.map(msg => {
      // For assistant messages, extract tool calls
      if (msg.role === 'assistant') {
        try {
          const content = typeof msg.content === 'string' 
            ? JSON.parse(msg.content) 
            : msg.content;
          
          // If content is an array, it might contain tool calls
          if (Array.isArray(content)) {
            const toolCalls = content
              .filter(item => item.type === 'tool-call')
              .map(item => ({
                toolCallId: item.toolCallId,
                toolName: item.toolName,
                args: item.args,
              }));
            
            return {
              ...msg,
              parsedContent: content,
              toolCalls,
              tokenCount: Math.ceil((JSON.stringify(content).length) / 4),
              tokenCost: ((Math.ceil(JSON.stringify(content).length) / 4) * 15) / 1000000, // $15 per million tokens
            };
          }
        } catch (e) {
          // If parsing fails, return the original content
        }
      }
      
      // For tool messages, extract tool results
      if (msg.role === 'tool') {
        try {
          const content = typeof msg.content === 'string' 
            ? JSON.parse(msg.content) 
            : msg.content;
          
          // If content is an array, it might contain tool results
          if (Array.isArray(content)) {
            const toolResults = content
              .filter(item => item.type === 'tool-result')
              .map(item => ({
                toolCallId: item.toolCallId,
                toolName: item.toolName,
                result: item.result,
              }));
            
            return {
              ...msg,
              parsedContent: content,
              toolResults,
              tokenCount: Math.ceil((JSON.stringify(content).length) / 4),
              tokenCost: ((Math.ceil(JSON.stringify(content).length) / 4) * 3) / 1000000, // $3 per million tokens
            };
          }
        } catch (e) {
          // If parsing fails, return the original content
        }
      }
      
      // Default case for messages that couldn't be parsed
      return {
        ...msg,
        parsedContent: msg.content,
        tokenCount: Math.ceil((typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)).length / 4),
        tokenCost: msg.role === 'assistant' 
          ? ((Math.ceil((typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)).length) / 4) * 15) / 1000000 
          : ((Math.ceil((typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)).length) / 4) * 3) / 1000000
      };
    });
    
    // Calculate total tokens and costs
    const totalWriteTokens = processedMessages
      .filter(msg => msg.role === 'assistant')
      .reduce((sum, msg) => sum + msg.tokenCount, 0);
    
    const totalReadTokens = processedMessages
      .filter(msg => msg.role === 'tool')
      .reduce((sum, msg) => sum + msg.tokenCount, 0);
    
    const totalTokens = totalWriteTokens + totalReadTokens;
    
    const totalWriteCost = (totalWriteTokens * 15) / 1000000; // $15 per million tokens
    const totalReadCost = (totalReadTokens * 3) / 1000000; // $3 per million tokens
    const totalCost = totalWriteCost + totalReadCost;
    
    return {
      userMessage: userMessage[0],
      messages: processedMessages,
      stats: {
        messageCount: processedMessages.length,
        assistantMessageCount: processedMessages.filter(msg => msg.role === 'assistant').length,
        toolMessageCount: processedMessages.filter(msg => msg.role === 'tool').length,
        writeTokens: totalWriteTokens,
        readTokens: totalReadTokens,
        totalTokens,
        writeTokenCost: totalWriteCost.toFixed(6),
        readTokenCost: totalReadCost.toFixed(6),
        totalCost: totalCost.toFixed(6),
      }
    };
  } catch (error) {
    console.error('Error fetching message trace:', error);
    throw new Error('Failed to fetch message trace');
  }
}

export async function getErrorAnalytics({
  page = 1,
  pageSize = 10,
  timeRange = 30,
  category = '',
  searchTerm = ''
}: {
  page: number;
  pageSize: number;
  timeRange: number;
  category?: string;
  searchTerm?: string;
}) {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * pageSize;
    
    // Calculate the date range
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeRange);
    
    // Build the error patterns for the query
    const errorPatterns = [
      'encountered an error',
      'getting an error',
      'error occurred',
      'facing an issue',
      'not working',
      'failed to',
      'fix this error',
      'fix the error',
      'error message'
    ];
    
    // Create the conditions for the query
    // We need to cast the content field to text before using ilike
    const conditions = errorPatterns.map(pattern => 
      sql`CAST(${message.content} AS TEXT) ILIKE ${'%' + pattern + '%'}`
    );
    
    // Add search term condition if provided
    if (searchTerm) {
      conditions.push(sql`CAST(${message.content} AS TEXT) ILIKE ${'%' + searchTerm + '%'}`);
    }
    
    // Get total count for pagination
    const countResult = await db
      .select({ count: count() })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        gt(message.createdAt, startDate),
        or(...conditions)
      ));
    
    const totalCount = Number(countResult[0].count);
    
    // Fetch the error messages
    const errorMessages = await db
      .select({
        id: message.id,
        chatId: message.chatId,
        projectId: message.projectId,
        userId: message.userId,
        content: message.content,
        createdAt: message.createdAt
      })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        gt(message.createdAt, startDate),
        or(...conditions)
      ))
      .orderBy(desc(message.createdAt))
      .limit(pageSize)
      .offset(offset);
    
    // Process and categorize the error messages
    const processedErrors = errorMessages.map(msg => {
      const content = typeof msg.content === 'string' 
        ? msg.content 
        : JSON.stringify(msg.content);
      
      const errorCategory = categorizeError(content);
      
      // Skip if category filter is applied and doesn't match
      if (category && errorCategory !== category) {
        return null;
      }
      
      // Parse JSON content if it's in JSON format
      let extractedText = '';
      try {
        if (content.startsWith('[') || content.startsWith('{')) {
          const parsedContent = JSON.parse(content);
          if (Array.isArray(parsedContent)) {
            // Extract text from array of objects with text property
            extractedText = parsedContent
              .filter(item => item && typeof item === 'object' && 'text' in item)
              .map(item => item.text)
              .join(' ');
          } else if (parsedContent && typeof parsedContent === 'object') {
            // Extract text from object with text property
            extractedText = parsedContent.text || JSON.stringify(parsedContent);
          }
        }
      } catch (e) {
        // If parsing fails, use the original content
        extractedText = '';
      }
      
      // Use extracted text if available, otherwise use original content
      const displayText = extractedText || content;
      
      return {
        id: msg.id,
        chatId: msg.chatId,
        projectId: msg.projectId || '',
        userId: msg.userId || '',
        createdAt: msg.createdAt,
        category: errorCategory,
        content: msg.content,
        // Extract a snippet of the error message for display
        snippet: displayText.substring(0, 300) + (displayText.length > 300 ? '...' : '')
      };
    }).filter(Boolean) as any[];
    
    // Get error counts by category
    const categoryCounts: Record<string, number> = {};
    processedErrors.forEach(error => {
      if (!error) return;
      categoryCounts[error.category] = (categoryCounts[error.category] || 0) + 1;
    });
    
    // Get error counts by day for the time range
    const dateRange: string[] = [];
    for (let i = 0; i < timeRange; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);
      dateRange.unshift(date.toISOString().split('T')[0]);
    }
    
    const errorsByDate: Record<string, number> = {};
    dateRange.forEach(date => {
      errorsByDate[date] = 0;
    });
    
    processedErrors.forEach(error => {
      if (!error) return;
      const date = new Date(error.createdAt).toISOString().split('T')[0];
      if (errorsByDate[date] !== undefined) {
        errorsByDate[date]++;
      }
    });
    
    return {
      errors: processedErrors,
      categoryCounts,
      errorsByDate,
      pagination: {
        total: totalCount,
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    };
  } catch (error) {
    console.error('Error fetching error analytics:', error);
    throw new Error('Failed to fetch error analytics');
  }
}
