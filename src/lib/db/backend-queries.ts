import { eq, desc, and } from 'drizzle-orm';
import { db } from '@/lib/db';
import { fileState, projects, deployments } from './schema';
import { FileItem } from '@/types/file';

// Use the actual type from the database
type DrizzleClient = typeof db;

/**
 * Get the latest file state for a project
 */
export async function getLatestFileStateForProject(projectId: string) {
  try {
    const results = await db
      .select()
      .from(fileState)
      .where(eq(fileState.projectId, projectId))
      .orderBy(desc(fileState.createdAt))
      .limit(1);
    
    return results[0] || null;
  } catch (error) {
    console.error('Failed to get latest file state for project:', error);
    throw error;
  }
}

/**
 * Get the backend code from a file state
 * This extracts all files in the 'backend' directory
 */
export async function getBackendCodeFromFileState(fileStateId: string) {
  try {
    const state = await db
      .select()
      .from(fileState)
      .where(eq(fileState.id, fileStateId))
      .limit(1);
    
    if (!state.length) {
      return null;
    }
    
    const files = state[0].files as FileItem[];
    
    // Extract all files in the backend directory
    const backendFiles = files.filter((file: FileItem) => {
      // Check if the file path contains /backend/ directory
      return file.name.includes('backend/') && file.type === 'file';
    });
    
    // Combine all backend files into a single code string
    // Each file will be wrapped in a module-like structure
    const combinedCode = backendFiles.map((file: FileItem) => {
      return `
// File: ${file.name}
// Language: ${file.language}
${file.content}
`;
    }).join('\n\n');
    
    return {
      code: combinedCode,
      files: backendFiles,
      fileStateId
    };
  } catch (error) {
    console.error('Failed to get backend code from file state:', error);
    throw error;
  }
}

/**
 * Get the backend code for a project
 * This combines getting the latest file state and extracting the backend code
 */
export async function getBackendCodeForProject(projectId: string) {
  try {
    const latestFileState = await getLatestFileStateForProject(projectId);

    if (!latestFileState) {
      return null;
    }
    
    return getBackendCodeFromFileState(latestFileState.id);
  } catch (error) {
    console.error('Failed to get backend code for project:', error);
    throw error;
  }
}

/**
 * Get the backend code for a deployment
 * This is used when a specific deployment references a file state
 */
export async function getBackendCodeForDeployment(deploymentId: string) {
  try {
    const [deployment] = await db
      .select()
      .from(deployments)
      .where(eq(deployments.id, deploymentId))
      .limit(1);
    
    if (!deployment || !deployment.fileStateId) {
      return null;
    }
    
    return getBackendCodeFromFileState(deployment.fileStateId);
  } catch (error) {
    console.error('Failed to get backend code for deployment:', error);
    throw error;
  }
}

/**
 * Process backend code to create Fastify-compatible routes
 * This transforms the raw backend code into a format that can be executed by our dynamic backend system
 */
export function processFastifyRoutes(backendCode: string | null) {
  if (!backendCode) {
    return null;
  }
  
  // Extract route definitions from the backend code
  // This is a simplified approach - in a real implementation, you might want to parse the code more carefully
  
  // For our POC, we'll just wrap the code in a Fastify-compatible format
  const processedCode = `
// Processed backend code for Fastify execution
${backendCode}
`;
  
  return processedCode;
}

/**
 * Get ready-to-execute backend code for a project
 */
export async function getExecutableBackendForProject(projectId: string) {
  try {
    const backendCodeData = await getBackendCodeForProject(projectId);
    
    if (!backendCodeData) {
      return null;
    }
    
    const processedCode = processFastifyRoutes(backendCodeData.code);
    
    return {
      code: processedCode,
      files: backendCodeData.files,
      fileStateId: backendCodeData.fileStateId
    };
  } catch (error) {
    console.error('Failed to get executable backend for project:', error);
    throw error;
  }
}
