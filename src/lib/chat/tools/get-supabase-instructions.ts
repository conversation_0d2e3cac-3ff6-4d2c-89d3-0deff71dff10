import { DataStreamWriter, tool } from "ai";
import { z } from "zod";
import { CreditUsageTracker } from "@/lib/credit/CreditUsageTracker";
import { SupabaseIntegrationProvider } from "@/lib/integrations/supabase/SupabaseIntegrationProvider";
import { Project } from "@/lib/db/schema";

/**
 * Tool to fetch Supabase instructions for a chat
 * This provides the AI with context about the Supabase project configuration
 * including schema, functions, and other relevant database information
 */
export const getSupabaseInstructions = ({
  dataStream,
  creditUsageTracker,
  project
}: {
  dataStream: DataStreamWriter;
  creditUsageTracker: CreditUsageTracker;
  project: Project;
}) => {
  return tool({
    description: 'Get Supabase project instructions. This provides you with the supabase specific guides and instructions to understand and plan how to integrate supabase into the project.',
    parameters: z.object({
      reason: z.string().describe("Describe why you need the Supabase instructions")
    }),
    execute: async ({ reason }: { reason: string }) => {
      try {
        console.log(`Fetching Supabase instructions. Reason: ${reason}`);
        
        // Create a new instance of the SupabaseIntegrationProvider
        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();
        
        // Get the latest instructions for the chat
        const result =  supabaseIntegrationProvider.getSupabaseInitialInstructions();
        
        // Track the operation for credit usage
        creditUsageTracker.trackOperation('tool_call');
        
        return JSON.stringify({
          instructions: result,
          comment: 'Use this Supabase instruction to implement supabase features correctly. Call querySupabaseContext tool to fetch schema structure and proper RLS policies, secrets, functions, database functions. triggers.'
        });
      } catch (e: any) {
        console.error('Error while fetching Supabase instructions', e);
        return `Error while fetching Supabase instructions. Please try again. Error: ${e.message}`;
      }
    }
  });
};
