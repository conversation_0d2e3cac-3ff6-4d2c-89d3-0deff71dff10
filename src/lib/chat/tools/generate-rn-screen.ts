import {z} from 'zod';
import {DataStreamWriter, tool, streamText, generateText, smoothStream} from 'ai';
import {generateUUID} from '@/lib/utils';
import {saveDesignScreen, updateDesignScreen, countDesignScreensByProject} from '@/lib/db/design-screens';
import {extractHtmlContent, resetHtmlExtractor} from '@/lib/utils/html-utils';
import {
    COMMON_DESIGN_GUIDELINES,
    SCREEN_GENERATOR_SYSTEM_PROMPT
} from '@/lib/ai/prompt-directory/design-generator-prompt';
import {customModel} from '@/lib/ai';
import {fetchFromOpenRouter} from '@/lib/openrouter/get-message-details';
import {exportData} from '@/lib/server-utils';
import {DEFAULT_DEPENDENCIES} from "@/types/editor";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {camelCase} from "lodash";
import {FIRST_EXAMPLES, TECHNICAL_REQUIREMENTS} from "@/lib/ai/prompt-directory";
import {StreamService} from "@/lib/services/stream-service";
import {MOFileParser} from "@/lib/parser/StreamParser";
import {MODiffParser} from "@/lib/parser/DiffParser";


// Schema for the design generation parameters
export const generateDesignSchema = z.object({
    appIdea: z.string().describe('Overall description of the app'),
    designSystem: z.object({
        colorScheme: z.enum(['light', 'dark', 'auto']).optional(),
        primaryColor: z.string().optional(),
        secondaryColor: z.string().optional()
    }).optional(),
    coherencePrompt: z.string().describe('Include details that should be coherent across the screens like navigation, header styles, logo design. Include sample code if possible. This is the most important prompt that sets up the process to ensure that all the screens are truly part of the same app design language.'),
    screens: z.array(z.object({
        htmlScreenName: z.string().describe('MUST match with the existing html screen name for lookup. If any screen passed does not match, the whole batch will fail. Make sure to pass the html screen name exactly as you see it including spaces and capitalization.'),
        name: z.string().describe('Name of the screen. MUST NOT include .tsx but must contain the format <screen name>Screen'),
        description: z.string().describe('Description of what this screen should contain.'),
    })).describe("Please pass only the screens you want to create/edit. Any screens passed here will be regenerated entirely.")
});

const generateScreen = (screen, appIdea, designSystem, coherencePrompt, parser: MOFileParser, userMessageId: string, dataStream: DataStreamWriter) => {
    return new Promise(async (resolve, reject) => {
        // Use the AI SDK with the model
        try {
            const result = streamText({
                model: customModel("anthropic/claude-sonnet-4"),
                temperature: 0.7,
                experimental_transform: smoothStream({chunking: "line"}),
                messages: [
                    {
                        role: 'system',
                        content:
                            `You are provided a screen. Your job now is generate the corresponding screen code in Expo React Native such that its compatible with Expo React Native on the web.
                                    You MUST convert the design to be a one-on-one replica of the original tailwind css without adding any new dependencies apart from the bundled ${JSON.stringify(DEFAULT_DEPENDENCIES, null, 2)}. 
                                    You can only create one screen code. The design, functionality, styles and the flow must remain intact. In the output, do not add any extra formatting or backticks.
                                    
                                       ${COMMON_DESIGN_GUIDELINES}

  ${TECHNICAL_REQUIREMENTS}
  
                                    `
                    },
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: `
                                        Please convert the following html tailwind in mobile friendly react native code. If the code contains a bottom bar, ignore it. DO NOT position headers/sticky elements via a one to one map, but use react native equivalents. 
                                        Make sure to add navigation.navigate to the corresponding tags.
                                        
                                        App Idea:
                                        ${appIdea}
                                        
                                        Design System (DO NOT DEVIATE from the design system):
                                        ${JSON.stringify(designSystem, null, 2)}
                                        
                                        Coherence Prompt (Important to understand how this screen ties with other screens): 
                                        ${coherencePrompt}
                                        
                                        ScreenName: ${screen.name}
                                        
                                        
                                        Do not write any preamble or explanation. just the code without any formatting. No backticks, no formatting. Match the design exactly and don't change anything. The design has to translate EXACTLY as is otherwise the task will fail.  Don't any comments. Add proper state management and make the screen functional. Make sure to include all imports such that the code compiles.
                                        DO NOT return anything other than the code. DO not add backticks or any special formatting.
                                        DO NOT add the bottom tabs that you see on the screen, it will be added later. Ensure that the screen code is notch friendly and SafeAreProvider is already setup for you to use the SafeAreaView.
                                        
                                        Output in the following format: 
                                        ${FIRST_EXAMPLES}
                                        `
                            }
                        ]
                    }
                ],

                onChunk: ({chunk}) => {
                    if (chunk.type === "text-delta") {
                        try {

                            // First run the file parser
                            const afterFileParser = parser.parse(userMessageId, chunk.textDelta);
                        } catch (e) {
                            console.error('Error in chunk processing', e);
                        }
                    }
                },
                onFinish: ({text}) => {
                    console.log('Done', text)
                    resolve(text)
                },
                onError: (err) => {
                    reject(err)
                }
            });

            let code = "";
            for await (const chunk of result.textStream) {
                code += chunk;
                try {
                    const afterParse = parser.parse(userMessageId, chunk);
                    // dataStream.writeData({ type: 'rn-screen-chunk', content: chunk });
                } catch {
                }
            }

            // once the for-loop completes, you’ve got the full text
            return resolve(code);
        } catch (e) {
            console.log('Error in the screen', e)
            reject(e);
        }

    })
}
/**
 * Tool for generating design screens for an app
 *
 * This tool:
 * 1. Takes app idea, design system, and screen descriptions
 * 2. Generates HTML for each screen
 * 3. Saves the screens to the database
 * 4. Streams updates to the client
 */
// Maximum number of screens that can be generated per project

export const generateReactNativeScreens = ({
                                               fileManager,
                                               dataStream,
                                               userMessageId,
                                               parser
                                           }: {
    fileManager: FileLineManager,
    dataStream: DataStreamWriter,
    userMessageId: string,
    parser: MOFileParser
}) => {
    return tool({
        description: 'Generate designs for the app based on user requirements',
        parameters: generateDesignSchema,
        execute: async ({appIdea, designSystem, screens, coherencePrompt}) => {
            try {

                // Define proper type for results
                interface ScreenResult {
                    name: string,
                    screenCode: string
                }

                console.log('Generating designs for screens:', screens.map(s => s.name).join(', '));

                const results = await Promise.all(screens.map(async screen => {
                    // Generate a stable ID for this screen
                    // Generate the screen HTML
                    return generateScreen(screen, appIdea, designSystem, coherencePrompt, parser, userMessageId, dataStream);
                }));


                console.log('results', results)
                return {
                    success: true,
                    results
                };
            } catch (error) {
                console.error('Error generating design:', error);
                throw error;
            }
        }
    });
};
