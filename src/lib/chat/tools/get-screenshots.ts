import { ScreenshotMetadata } from '@/types/screenshot';
import { z } from 'zod';
import { DataStreamWriter, generateObject, tool } from 'ai';
import { customModel } from '@/lib/ai';
import { CreditUsageTracker } from '@/lib/credit/CreditUsageTracker';
import { getProjectScreenshots } from '@/lib/db/queries';

/**
 * <PERSON><PERSON> for retrieving screenshots for a project
 * Allows the LLM to access design screenshots during app building
 */
export const getScreenshots = ({
  dataStream,
  creditUsageTracker,
  projectId,
  messageId
}: {
  dataStream: DataStreamWriter;
  creditUsageTracker: CreditUsageTracker;
  projectId: string;
  messageId: string
}) => {
  return tool({
    description: 'Get screenshots for the current project filtered by a natural language query.',
    parameters: z.object({
      query: z.string().describe('Natural language query to find relevant screenshots'),
    }),
    execute: async ({ query }: { query: string }) => {
      try {
        // Track credit usage
        creditUsageTracker.trackOperation('tool_call');

        // Fetch the screenshot data using the database query function
        const result = await getProjectScreenshots(projectId);

        if ('error' in result) {
          return { error: result.error };
        }

        // We no longer need to handle legacy data as we're returning empty screenshots array
        // from the database query function when no data is found
        
        // Use the screenshot metadata directly from the screenshotState table
        return await processScreenshotData(result.screenshots as ScreenshotMetadata[], query);
      } catch (error: any) {
        console.error('Error in getScreenshots tool:', error);
        return {
          error: `Error retrieving screenshots: ${error?.message || 'Unknown error'}`,
        };
      }
    }
  });
};

/**
 * Find screenshots relevant to a natural language query
 * Uses a small LLM to match the query to available screenshots
 */
async function findRelevantScreenshots(query: string, screenshots: ScreenshotMetadata[]): Promise<(ScreenshotMetadata & { relevanceScore: number })[]> {
  try {
    // If there are no screenshots, return empty array
    if (!screenshots || screenshots.length === 0) {
      return [];
    }

    // If there are only a few screenshots, return all of them with a default relevance score
    if (screenshots.length <= 3) {
      return screenshots.map(screenshot => ({
        ...screenshot,
        relevanceScore: 1.0
      }));
    }

    // Use the generateObject function with our custom model to find relevant screenshots
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-nano'),
      temperature: 0.1,
      schema: z.array(z.object({
        id: z.string(),
        relevanceScore: z.number().min(0).max(1)
      })),
      system: `You are a helpful assistant that analyzes app screenshots and finds the most relevant ones based on user queries.
      
      Given a list of app screenshots with their metadata and a user query, your task is to:
      1. Analyze each screenshot's name and any other metadata
      2. Determine how relevant each screenshot is to the user's query
      3. Return a list of screenshot IDs with relevance scores (0-1)
      
      Focus on finding screenshots that would be most helpful for answering the user's query.`,
      prompt: `Find screenshots relevant to this query: "${query}"
      
      Available screenshots:
      ${JSON.stringify(screenshots, null, 2)}
      
      Return only the screenshot IDs with their relevance scores (0-1).`
    });

    // Get the scores from the result object
    const scores = result.object;

    // Sort the scores by relevance (highest first)
    const sortedScores = [...scores].sort((a, b) => b.relevanceScore - a.relevanceScore);

    // Get the full screenshot objects for the relevant screenshots
    const relevantScreenshots = sortedScores
      .filter(score => score.relevanceScore > 0.5) // Only include reasonably relevant screenshots
      .map(score => {
        const screenshot = screenshots.find(s => s.id === score.id);
        return screenshot ? { ...screenshot, relevanceScore: score.relevanceScore } : null;
      })
      .filter((item): item is ScreenshotMetadata & { relevanceScore: number } => item !== null);

    return relevantScreenshots;
  } catch (error) {
    console.error('Error finding relevant screenshots:', error);
    return [];
  }
}

/**
 * Process screenshot data and return the appropriate response
 */
async function processScreenshotData(screenshots: ScreenshotMetadata[], query: string) {
  // If no screenshots are available, return an error
  if (!screenshots || screenshots.length === 0) {
    return {
      error: 'No screenshots available for this project',
    };
  }

  // Find relevant screenshots based on the query
  const relevantScreenshots = await findRelevantScreenshots(query, screenshots);
  
  return {
    relevantScreenshots,
    totalScreenshots: screenshots.length,
  };
}
