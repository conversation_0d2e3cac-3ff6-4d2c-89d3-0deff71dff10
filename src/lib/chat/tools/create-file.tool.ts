import {fileCreateSchema} from "@/lib/chat/schemas/file-create.schema";
import {DataStreamWriter, tool} from 'ai';
import {FileLineManager} from "@/lib/editor/FileLineManager";

export const createFileTool = ({
                            fileManager,
                            processImagePlaceholders,
                            processVideoPlaceholders,
                            dataStream
                        }: {
    fileManager: FileLineManager,
    dataStream: DataStreamWriter,
    processImagePlaceholders: (text: string) => Promise<string>,
    processVideoPlaceholders: (text: string) => Promise<string>

}) => {
    return tool({
        description: 'Create a new file',
        parameters: fileCreateSchema,
        execute: async ({absolutePath, content}) => {

            // await updateFile({
            //     files,
            //     snackBaseId,
            //     snackId,
            //     absolutePath,
            //     contents: content
            // })

            fileManager.addFiles(absolutePath, content);

            content = await processImagePlaceholders(content);
            content = await processVideoPlaceholders(content);

            dataStream.writeData({
                type: 'file-operation',
                content: {
                    type: 'create',
                    absolutePath,
                    content
                }
            });
            return `Added file ${absolutePath} successfully!`;
        }
    })
}