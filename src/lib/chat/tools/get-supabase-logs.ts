import { DataStreamWriter, tool } from "ai";
import { z } from "zod";
import { CreditUsageTracker } from "@/lib/credit/CreditUsageTracker";
import { SupabaseIntegrationProvider } from "@/lib/integrations/supabase/SupabaseIntegrationProvider";
import { Project } from "@/lib/db/schema";

// No need for the getLogQuery function as it's now in SupabaseDebuggingTools

/**
 * Tool to fetch Supabase logs for debugging
 * This provides the AI with log data from different Supabase services
 * to help diagnose and debug issues
 */
export const getSupabaseLogs = ({
  dataStream,
  creditUsageTracker,
  project
}: {
  dataStream: DataStreamWriter;
  creditUsageTracker: CreditUsageTracker;
  project: Project;
}) => {
  return tool({
    description: 'Gets logs for a Supabase project by service type. Use this to help debug problems with your app. There are two ways to fetch edge function logs:\n\n1. By function name: Use `functionName: "your-function-name"` to fetch logs by the function name (easier for users to understand)\n2. By function ID: Use `functionId: "ce62b3db-daf3-44ca-b935-957570435829"` if you know the specific ID\n\nThis will return logs from the last hour. If no logs are found, ask the user to test the functionality again to generate new logs, as only the user can trigger new function executions.',
    parameters: z.object({
      service: z.enum([
        'api',
        'branch-action',
        'postgres',
        'edge-function',
        'auth',
        'storage',
        'realtime',
      ]).describe('The service to fetch logs for'),
      limit: z.number().optional().default(100).describe('Maximum number of log entries to return'),
      functionId: z.string().optional().describe('Specific function ID to filter logs for a single edge function (only applicable when service is "edge-function"). If you know the ID, provide it directly.'),
      functionName: z.string().optional().describe('Name of the function to fetch logs for (only applicable when service is "edge-function"). The tool will automatically find the matching function ID.'),
      reason: z.string().describe("Describe why you need these logs for debugging")
    }),
    execute: async ({ service, limit, functionId, functionName, reason }: { 
      service: 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime',
      limit?: number,
      functionId?: string,
      functionName?: string,
      reason: string 
    }) => {
      try {
        console.log(`Fetching Supabase ${service} logs. Reason: ${reason}. Limit: ${limit || 100}. ${functionId ? `Function ID: ${functionId}` : ''}${functionName ? `Function Name: ${functionName}` : ''}`);
        
        if (!project.connectionId || !project.supabaseProjectId) {
          throw new Error('Project is not linked to a Supabase project');
        }
        
        // Create a new instance of the SupabaseIntegrationProvider
        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();
        
        let resolvedFunctionId = functionId;
        
        // If functionName is provided but not functionId, try to find the function ID
        if (service === 'edge-function' && functionName && !functionId) {
          try {
            // Get the functions list
            const functionsResult = await supabaseIntegrationProvider.getProjectResources({
              projectId: project.id,
              resourceType: 'functions'
            });
            
            // Find the function with the matching name
            const functions = functionsResult?.functions || [];
            const matchingFunction = functions.find(
              (func: any) => func.name?.toLowerCase() === functionName.toLowerCase()
            );
            
            if (matchingFunction) {
              resolvedFunctionId = matchingFunction.id;
              console.log(`Found function ID ${resolvedFunctionId} for function name ${functionName}`);
            } else {
              console.log(`Could not find function ID for function name ${functionName}`);
            }
          } catch (error) {
            console.error('Error finding function ID from name:', error);
          }
        }
        
        // Use the getLogs method from SupabaseIntegrationProvider which uses SupabaseDebuggingTools
        const logsData = await supabaseIntegrationProvider.getLogs({
          projectId: project.id,
          service,
          limit: limit || 100,
          functionId: resolvedFunctionId
        });
        
        // Track the operation for credit usage
        creditUsageTracker.trackOperation('tool_call');

        // For debugging
        console.log('service', service);
        console.log('logsData', logsData);
        
        // Format the response using the actual logs data from SupabaseIntegrationProvider
        return JSON.stringify({
          logs: logsData,
          service,
          ...(resolvedFunctionId && { functionId: resolvedFunctionId }),
          ...(functionName && { functionName }),
          timestamp: new Date().toISOString(),
          comment: resolvedFunctionId
            ? `These are the most recent logs for function ${functionName || ''} (ID: ${resolvedFunctionId}) from your Supabase project. Use them to diagnose any issues you're experiencing.`
            : `These are the most recent ${service} logs from your Supabase project. Use them to diagnose any issues you're experiencing.`
        });
      } catch (e: any) {
        console.error(`Error while fetching Supabase ${service} logs`, e);
        return JSON.stringify({
          error: e.message,
          service,
          ...(functionId && { functionId }),
          ...(functionName && { functionName }),
          timestamp: new Date().toISOString(),
          comment: `Error while fetching Supabase ${service} logs: ${e.message}. Please try again or check your Supabase connection.`
        });
      }
    }
  });
};
