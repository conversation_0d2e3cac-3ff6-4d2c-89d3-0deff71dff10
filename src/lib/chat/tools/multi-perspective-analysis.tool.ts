import { z } from 'zod';
import { DataStreamWriter, tool, streamText } from 'ai';
import { customModel } from '@/lib/ai';

// Define the personas that will participate in the multi-perspective analysis
const PERSONAS = {
  ANALYTICAL_THINKER: 'Analytical Thinker',
  CREATIVE_INNOVATOR: 'Creative Innovator',
  PRAGMATIC_IMPLEMENTER: 'Pragmatic Implementer',
  ETHICAL_EVALUATOR: 'Ethical Evaluator',
  STRATEGIC_VISIONARY: 'Strategic Visionary',
} as const;

type PersonaKey = keyof typeof PERSONAS;
type PersonaName = typeof PERSONAS[PersonaKey];

const personaDescriptions: Record<PersonaName, string> = {
  [PERSONAS.ANALYTICAL_THINKER]: 'Focuses on data, logic, and systematic analysis. Breaks down complex issues into components and examines relationships between them. Values evidence-based reasoning and objective evaluation.',
  [PERSONAS.CREATIVE_INNOVATOR]: 'Emphasizes novel approaches, lateral thinking, and imagination. Looks for unexpected connections and possibilities. Values originality, experimentation, and challenging conventional wisdom.',
  [PERSONAS.PRAGMATIC_IMPLEMENTER]: 'Concentrates on practical applications, feasibility, and real-world constraints. Evaluates ideas based on their workability and efficiency. Values actionable solutions and tangible outcomes.',
  [PERSONAS.ETHICAL_EVALUATOR]: 'Considers moral implications, values, and principles. Examines issues through the lens of fairness, justice, and responsibility. Values integrity, compassion, and ethical consistency.',
  [PERSONAS.STRATEGIC_VISIONARY]: 'Focuses on long-term implications, broader context, and systemic impacts. Considers how ideas fit into larger goals and trends. Values foresight, strategic alignment, and sustainable outcomes.',
};

const personaList = Object.values(PERSONAS);

// Schema for the multi-perspective analysis parameters
export const multiPerspectiveAnalysisSchema = z.object({
  topic: z.string().describe('The topic, question, or issue to be analyzed from multiple perspectives'),
  context: z.string().optional().describe('Additional context or background information relevant to the topic'),
  focusAreas: z.array(z.string()).optional().describe('Specific aspects or dimensions of the topic to focus on'),
  requiredPersonas: z.array(
    z.enum([
      PERSONAS.ANALYTICAL_THINKER,
      PERSONAS.CREATIVE_INNOVATOR,
      PERSONAS.PRAGMATIC_IMPLEMENTER,
      PERSONAS.ETHICAL_EVALUATOR,
      PERSONAS.STRATEGIC_VISIONARY
    ])
  ).optional().describe('Specific personas to include in the analysis (if not specified, all available personas will be used)'),
  analysisDepth: z.enum(['brief', 'standard', 'comprehensive']).default('standard').describe('The desired depth of analysis'),
  discussionRounds: z.number().min(1).max(5).default(3).describe('Number of discussion rounds between personas')
});

/**
 * Tool for generating multi-perspective analysis on a topic
 * 
 * This tool:
 * 1. Takes a topic and optional parameters
 * 2. Generates responses from multiple AI personas with different perspectives
 * 3. Facilitates a dialogue between these personas
 * 4. Synthesizes their perspectives into a consensus view
 * 5. Streams the entire process and final synthesis to the client
 */
export const multiPerspectiveAnalysis = ({
  dataStream,
}: {
  dataStream: DataStreamWriter;
}) => {
  return tool({
    description: 'Analyze a topic from multiple perspectives using different AI personas to reach a more comprehensive and balanced understanding',
    parameters: multiPerspectiveAnalysisSchema,
    execute: async ({ topic, context = '', focusAreas = [], requiredPersonas = [], analysisDepth = 'standard', discussionRounds = 3 }) => {
      try {
        // Stream the initial setup information
        dataStream.writeData({
          type: 'multi-perspective-update',
          content: `# Multi-Perspective Analysis: ${topic}\n\nInitiating analysis with multiple perspectives...\n\n`
        });

        // Select which personas to use
        const selectedPersonaNames = requiredPersonas.length > 0 ? requiredPersonas : personaList;
        
        if (selectedPersonaNames.length === 0) {
          throw new Error('No valid personas selected for analysis');
        }

        // Determine token allocation based on analysis depth
        let maxTokensPerPersona;
        switch (analysisDepth) {
          case 'brief':
            maxTokensPerPersona = 500;
            break;
          case 'comprehensive':
            maxTokensPerPersona = 1500;
            break;
          case 'standard':
          default:
            maxTokensPerPersona = 1000;
        }

        // Track all contributions to the discussion
        type Contribution = {
          persona: PersonaName;
          content: string;
          round: number;
          replyTo?: PersonaName[];
        };

        const contributions: Contribution[] = [];
        
        // Initialize the discussion with the topic introduction
        dataStream.writeData({
          type: 'multi-perspective-update',
          content: `## Multi-Perspective Discussion\n\n`
        });

        // PHASE 1: Initial perspectives from each persona
        dataStream.writeData({
          type: 'multi-perspective-update',
          content: `### Round 1: Initial Perspectives\n\n`
        });

        // Generate initial perspectives from each persona
        for (const personaName of selectedPersonaNames) {
          dataStream.writeData({
            type: 'multi-perspective-update',
            content: `#### ${personaName}:\n\n`
          });

          // Create the prompt for this persona
          const personaPrompt = `
You are the "${personaName}" persona with the following characteristics:
${personaDescriptions[personaName]}

Analyze the following topic from your unique perspective:
Topic: ${topic}
${context ? `Context: ${context}` : ''}
${focusAreas.length > 0 ? `Focus Areas: ${focusAreas.join(', ')}` : ''}

Provide your analysis in a clear, concise manner that reflects your perspective. Be insightful but concise.
`;

          // Generate the persona's perspective
          const result = streamText({
            model: customModel("anthropic/claude-sonnet-4"),
            temperature: 0.7,
            messages: [
              {
                role: 'system',
                content: personaPrompt
              }
            ]
          });
          
          let perspective = '';
          for await (const chunk of result.textStream) {
            perspective += chunk;
          }

          // Add this perspective to the contributions
          contributions.push({
            persona: personaName,
            content: perspective,
            round: 1
          });

          dataStream.writeData({
            type: 'multi-perspective-update',
            content: `${perspective}\n\n`
          });
        }

        // PHASE 2: Interactive discussion rounds
        // Each round, personas respond to previous contributions
        for (let round = 2; round <= discussionRounds; round++) {
          dataStream.writeData({
            type: 'multi-perspective-update',
            content: `### Round ${round}: Developing the Discussion\n\n`
          });

          // Get all contributions from the previous round
          const previousRoundContributions = contributions.filter(c => c.round === round - 1);
          
          // Each persona responds to the previous round
          for (const personaName of selectedPersonaNames) {
            // Get all previous contributions except this persona's own contribution
            const relevantContributions = previousRoundContributions
              .filter(c => c.persona !== personaName);
              
            if (relevantContributions.length === 0) continue;
            
            dataStream.writeData({
              type: 'multi-perspective-update',
              content: `#### ${personaName}:\n\n`
            });

            // Format the previous contributions for the prompt
            const previousContributionsText = relevantContributions
              .map(c => `${c.persona}: ${c.content}`)
              .join('\n\n');

            // Create a prompt that encourages building on previous ideas
            const dialoguePrompt = `
You are the "${personaName}" persona with the following characteristics:
${personaDescriptions[personaName]}

You're participating in round ${round} of a discussion on "${topic}".

Here are the most recent contributions from other participants:

${previousContributionsText}

Based on your unique perspective and the discussion so far:
1. Respond to at least 2 specific points made by others
2. Build upon or challenge ideas in a constructive way
3. Introduce a new insight or question that advances the discussion
4. Maintain your distinct perspective while seeking common ground

Be concise but insightful. Your goal is to deepen the collective understanding.
`;

            // Generate the persona's response
            const result = streamText({
              model: customModel("anthropic/claude-sonnet-4"),
              temperature: 0.7,
              messages: [
                {
                  role: 'system',
                  content: dialoguePrompt
                }
              ]
            });
            
            let response = '';
            for await (const chunk of result.textStream) {
              response += chunk;
            }

            // Add this response to the contributions
            contributions.push({
              persona: personaName,
              content: response,
              round: round,
              replyTo: relevantContributions.map(c => c.persona)
            });

            dataStream.writeData({
              type: 'multi-perspective-update',
              content: `${response}\n\n`
            });
          }
        }

        // PHASE 3: Generate synthesis and consensus
        dataStream.writeData({
          type: 'multi-perspective-update',
          content: `## Synthesis and Consensus\n\n`
        });

        // Compile all contributions for synthesis
        const allContent = contributions
          .map(c => `Round ${c.round} - ${c.persona}: ${c.content}`)
          .join('\n\n');

        // Create the synthesis prompt
        const synthesisPrompt = `
You are a neutral synthesizer tasked with finding common ground and key insights from multiple perspectives on the topic: "${topic}"

Here are all the perspectives and dialogue exchanges:

${allContent}

Please provide:
1. A summary of key points of agreement across perspectives
2. Important points of disagreement or tension
3. Unique insights contributed by each perspective
4. A balanced synthesis that incorporates the strongest elements from each viewpoint
5. Remaining questions or areas for further exploration

Your synthesis should be fair to all perspectives while highlighting the most valuable insights from each.
`;

        // Generate the synthesis
        const synthesisResult = streamText({
          model: customModel("anthropic/claude-sonnet-4"),
          temperature: 0.7,
          messages: [
            {
              role: 'system',
              content: synthesisPrompt
            }
          ]
        });
        
        let synthesis = '';
        for await (const chunk of synthesisResult.textStream) {
          synthesis += chunk;
        }

        // Synthesis is built in the streaming loop above

        dataStream.writeData({
          type: 'multi-perspective-update',
          content: synthesis
        });

        // Return the complete analysis results
        return {
          topic,
          contributions,
          synthesis,
          result: 'success'
        };
      } catch (error) {
        console.error('Error in multi-perspective analysis:', error);
        dataStream.writeData({
          type: 'error',
          error: error instanceof Error ? error.message : 'Failed to complete multi-perspective analysis'
        });
        throw error;
      }
    }
  });
};
