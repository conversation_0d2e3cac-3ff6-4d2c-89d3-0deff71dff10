import {fileEditSchema} from "@/lib/chat/schemas/file-edit.schema";
import {DataStreamWriter, tool} from "ai";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {FileItem} from "@/types/file";
import {z} from "zod";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";

// In src/lib/chat/tools/get-file-contents.ts

export const getFileContents = ({
                                    files,
                                    dataStream,
                                    creditUsageTracker,
                                    initialPromptGuidelines,
                                    isFirstMessage,
                                    circuitBreakerState
                                }: {
    fileManager: FileLineManager,
    dataStream: DataStreamWriter,
    files: FileItem[],
    creditUsageTracker: CreditUsageTracker,
    initialPromptGuidelines: string,
    isFirstMessage: boolean,
    circuitBreakerState?: {
        params: {
            fileRequestCounter: number,
            requestedFilesSet: Set<string>,
            consecutiveRequestsWithoutAction: number
        },
        resetCircuitBreaker: () => void
    }
}) => {
    return tool({
        description: 'Use this tool to fetch the exact contents of a file. Note that this will return the complete file and not partial content.',
        parameters: z.object({
            files: z.array(
                z.object({
                    name: z.string().describe("Full name of the file to fetch the contents of the relevant files. The file name has to MATCH EXACTLY the path specified in the <files></files> section and you CANNOT request any files not present in the <files></files> message."),
                    // lineRange: z.object({
                    //     start: z.number().optional().describe("Start line number (inclusive)"),
                    //     end: z.number().optional().describe("End line number (inclusive)")
                    // }).optional().describe("Optional line range to fetch only a portion of the file"),
                    reason: z.string().optional().describe("Describe the reason for requesting this file"),
                    // agreement1: z.boolean().describe("Did you make the file edits using the MO_FILE tag. Remember, this tool only allows view the files contents and not make new edits."),
                    // agreement2: z.string().describe("Answer all these questions: 1. Describe why you did you not use MO_FILE tag to make the changes yet. 2. What are you waiting for? 3. Why are you making so many tool call? 4. Why are you not fetching all the files in a single call getFileContents with the `files` array parameter?")
                })
            ).describe("List of files to retrieve the contents of. Can be between 1 to 6.")
        }),
        execute: async ({files: requestedFiles}: {
            files: Array<{ name: string, lineRange?: { start?: number, end?: number } }>
        }) => {

            // console.log('circuitBreakerParams', circuitBreakerState)
            if(circuitBreakerState) {
                let {params, resetCircuitBreaker} = circuitBreakerState;
                // // Increment the file request counter
                // params.fileRequestCounter += requestedFiles.length;
                // params.consecutiveRequestsWithoutAction++;
                //
                // console.log('fileRequestCounter', params.fileRequestCounter, params.consecutiveRequestsWithoutAction, requestedFiles.length)
                // // Check for duplicate file requests
                // const duplicateRequests = requestedFiles.filter(file => params.requestedFilesSet.has(file.name));
                //
                // // Add current files to the set
                // requestedFiles.forEach(file => params.requestedFilesSet.add(file.name));
                //
                // // Circuit breaker conditions
                // const circuitBreakerTriggered =
                //     params.fileRequestCounter > 6 || // Too many file requests total
                //     params.consecutiveRequestsWithoutAction > 3 || // Too many consecutive requests without action
                //     duplicateRequests.length > 0; // Duplicate file requests
                //
                // if (circuitBreakerTriggered) {
                //     console.log('circuitBreakerTriggered ')
                //     return JSON.stringify({
                //         results: [{
                //             file: "CIRCUIT_BREAKER_TRIGGERED",
                //             exists: true,
                //             content: "CIRCUIT BREAKER ACTIVATED: You are stuck in an analysis loop without taking action. \n\n" +
                //                     "You have requested too many files (" + params.fileRequestCounter + " total) or made too many consecutive requests (" +
                //                 params.consecutiveRequestsWithoutAction + ") without implementing a solution. \n\n" +
                //                     (duplicateRequests.length > 0 ? "You are also requesting files you've already seen: " +
                //                     duplicateRequests.map(f => f.name).join(", ") + "\n\n" : "") +
                //                     "REQUIRED ACTION: You MUST now:\n" +
                //                     "1. STOP requesting more files unless absolutely necessary\n" +
                //                     "2. Consolidate what you've learned so far\n" +
                //                     "3. Implement a solution with the information you have\n" +
                //                     "4. If you're uncertain about some details, make reasonable assumptions\n\n" +
                //                     "Remember: It's better to propose a specific solution that might need refinement than to continue in an analysis loop."
                //         }],
                //         comment: "CIRCUIT BREAKER ACTIVATED: You must implement a solution now with the information you have."
                //     });
                // }

            }
            try {
                const results: any[] = [];

                for (const requestedFile of requestedFiles) {
                    const absolutePath = requestedFile.name;

                    // Find the file in the files array
                    const fileItem = files.find(f => f.name === absolutePath);

                    if (!fileItem) {
                        results.push({
                            file: absolutePath,
                            exists: false,
                            error: `File ${absolutePath} does not exist. Look the only files you have access to are ${files.map(file => file.name)}. Don't divert from the goal. You can to create new files or edit an existing files. Use the correct tags and adhere to the system prompt.`
                        });
                        continue;
                    }

                    // Get file content
                    let content = fileItem.content;

                    // Apply line range if specified
                    if (requestedFile.lineRange) {
                        const {start, end} = requestedFile.lineRange;
                        const lines = content.split('\n');
                        const startLine = start || 0;
                        const endLine = end || lines.length - 1;

                        content = lines.slice(startLine, endLine + 1).join('\n');
                    }

                    const fileLineCount = content.split('\n').length;
                    results.push({
                        file: absolutePath,
                        exists: true,
                        lineCount: fileLineCount,
                        lineRange: requestedFile.lineRange,
                        content: content,
                        warnings: [fileLineCount > 300 ? "This file length is too big to make changes reliably. You need to refactor and break this down into smaller chunks reliably soon": '']
                    });
                }

                creditUsageTracker.trackOperation('tool_call');

                return JSON.stringify({
                    results,
                    comment: isFirstMessage ? initialPromptGuidelines : 'Are you on track to achieving the user goal? Are you fetching the files in batches? Are your fetching duplicates? Snap out of it. You need to complete the goal, not keep fetching files unless you have to.'
                });
            } catch (e: any) {
                console.log('Error while fetching file contents', e);
                return `Error while fetching file contents. Please try again. Error: ${e.message}`;
            }
        }
    });
};