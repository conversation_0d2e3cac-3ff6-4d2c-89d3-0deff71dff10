import { DataStreamWriter, tool } from "ai";
import { z } from "zod";
import { CreditUsageTracker } from "@/lib/credit/CreditUsageTracker";
import { SupabaseIntegrationProvider } from "@/lib/integrations/supabase/SupabaseIntegrationProvider";
import { Project } from "@/lib/db/schema";
import {UpdateProjectAuthConfigRequestBody} from "supabase-management-js";

/**
 * Tool to manage Supabase auth configuration
 * This provides the AI with capabilities to view and update auth settings
 * and retrieve SSO provider information
 */
export const manageSupabaseAuth = ({
  dataStream,
  creditUsageTracker,
  project
}: {
  dataStream: DataStreamWriter;
  creditUsageTracker: CreditUsageTracker;
  project: Project;
}) => {
  return tool({
    description: 'Manages Supabase auth configuration. Use this tool to view current auth settings, update auth configuration, or retrieve SSO provider information. This helps with configuring authentication options like email templates, redirect URLs, and external OAuth providers.',
    parameters: z.object({
      action: z.enum([
        'getAuthConfig',
        'updateAuthConfig',
        'getSSOProviders'
      ]).describe('The action to perform on the auth configuration'),
      authConfig: z.record(z.any()).optional().describe('The auth configuration to update (only required for updateAuthConfig action). Same format as the response from getAuthConfig. Pass only the fields to update.'),
      reason: z.string().describe("Describe why you need to access or modify the auth configuration")
    }),
    execute: async ({ action, authConfig, reason }: { 
      action: 'getAuthConfig' | 'updateAuthConfig' | 'getSSOProviders',
      authConfig?: UpdateProjectAuthConfigRequestBody,
      reason: string 
    }) => {
      try {
        console.log(`Performing Supabase auth action: ${action}. Reason: ${reason}.`);
        if(authConfig) {
          console.log('Updating', authConfig)
        }
        
        if (!project.connectionId || !project.supabaseProjectId) {
          throw new Error('Project is not linked to a Supabase project');
        }
        
        // Create a new instance of the SupabaseIntegrationProvider
        const supabaseIntegrationProvider = new SupabaseIntegrationProvider();
        
        let result;
        
        // Perform the requested action
        switch (action) {
          case 'getAuthConfig':
            result = await supabaseIntegrationProvider.getAuthConfig(project.id);
            break;
          case 'updateAuthConfig':
            if (!authConfig) {
              throw new Error('Auth configuration is required for updateAuthConfig action');
            }
            result = await supabaseIntegrationProvider.updateAuthConfig(project.id, authConfig);
            break;
          case 'getSSOProviders':
            result = await supabaseIntegrationProvider.getSSOProviders(project.id);
            break;
        }
        
        // Track the operation for credit usage
        creditUsageTracker.trackOperation('tool_call');
        
        // Format the response
        return JSON.stringify({
          action,
          result,
          timestamp: new Date().toISOString(),
          comment: getActionComment(action, result)
        });
      } catch (e: any) {
        console.error(`Error while performing Supabase auth action: ${action}`, e);
        return JSON.stringify({
          error: e.message,
          action,
          timestamp: new Date().toISOString(),
          comment: `Error while performing Supabase auth action ${action}: ${e.message}. Please try again or check your Supabase connection.`
        });
      }
    }
  });
};

/**
 * Helper function to generate appropriate comments based on the action
 */
function getActionComment(action: string, result: any): string {
  switch (action) {
    case 'getAuthConfig':
      return 'Here is the current auth configuration for your Supabase project. This includes settings for email templates, redirect URLs, and other auth-related options.';
    case 'updateAuthConfig':
      return 'The auth configuration for your Supabase project has been updated successfully. The changes should take effect immediately.';
    case 'getSSOProviders':
      return 'Here are the SSO providers configured for your Supabase project. You can use this information to manage external authentication options.';
    default:
      return 'Operation completed successfully.';
  }
}
