import { DataStreamWriter, tool } from "ai";
import { z } from "zod";
import { CreditUsageTracker } from "@/lib/credit/CreditUsageTracker";
import { LogEntry } from "@/types/logs";
import dayjs from "dayjs";
import {ToolCountTracker} from "@/lib/chat/tools/tool-count-tracker";

/**
 * Tool to fetch client-side logs for debugging
 * This provides the AI with log data from the client application
 * to help diagnose and debug issues
 */
export const getClientLogs = ({
  dataStream,
  creditUsageTracker,
  logs,
  messageId
}: {
  dataStream: DataStreamWriter;
  creditUsageTracker: CreditUsageTracker;
  logs: LogEntry[];
  messageId: string;
}) => {
  return tool({
    description: 'Gets client-side logs to help debug problems with the application. This will return the most recent logs (maximum 20) from the current session. You can filter by log type and source. [CRITICAL INSTRUCTION]: DO NOT CALL THIS TOOL MULTIPLE TIMES IN A ROW. The logs will be EXACTLY THE SAME each time you call this tool until the user interacts with the app again. Calling this tool multiple times is useless and wasteful. WAIT for the user to tell you they have tried your suggestion before calling this tool again. If you have already called this tool once, DO NOT call it again until the user confirms they have interacted with the app.',
    parameters: z.object({
      type: z.enum(['error', 'warn', 'info', 'log', 'debug', 'all']).default('all')
        .describe('The type of logs to fetch. Use "all" to get logs of all types.'),
      source: z.enum(['console', 'network', 'snack', 'all']).default('all')
        .describe('The source of logs to fetch. Use "all" to get logs from all sources.'),
      limit: z.number().min(1).max(20).default(10)
        .describe('Maximum number of log entries to return (1-20)'),
      reason: z.string()
        .describe("Describe why you need these logs for debugging")
    }),
    execute: async ({ type, source, limit, reason }: { 
      type: 'error' | 'warn' | 'info' | 'log' | 'debug' | 'all',
      source: 'console' | 'network' | 'snack' | 'all',
      limit: number,
      reason: string 
    }) => {
      try {

        const toolTracker = ToolCountTracker.getInstance();
        console.log('Tool call allowed (getClientLogs):', toolTracker.shouldAllowToolCall(messageId, 'getClientLogs'))

        // Check if we should allow this tool call
        if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'getClientLogs')) {
          return {
            result: null,
            message: `⚠️ Tool call limit reached. 
            Client logs do not refresh while you are working on the code. You need to stop and ask the user to interact with the app in the preview to get new logs.
            DO NOT CLAIM THAT YOU HAVE SUCCESSFULLY updated/fixed/created a task until you get a confirmation using logs. ASK the user to test the app and them tell them to confirm if the feature worked or not. 
            THEY are NON technical, they can't see the logs or give you the logs. HELP THEM, DO NOT CONFUSE or MISGUIDE THEM.
            `,
          };
        }
        console.log(`Fetching client logs. Type: ${type}, Source: ${source}, Limit: ${limit}, Reason: ${reason}`);

        // Increment the tool count if we have a chat ID
        if (messageId) {
          toolTracker.incrementToolCount(messageId, 'getClientLogs');
        }
        // Track the operation for credit usage
        creditUsageTracker.trackOperation('tool_call');

        // Filter logs based on parameters
        let filteredLogs = [...logs];
        
        // Filter by type if not 'all'
        if (type !== 'all') {
          filteredLogs = filteredLogs.filter(log => log.type.toLowerCase() === type.toLowerCase());
        }
        
        // Filter by source if not 'all'
        if (source !== 'all') {
          filteredLogs = filteredLogs.filter(log => log.source === source);
        }
        
        // Sort by timestamp (newest first)
        filteredLogs.sort((a, b) => b.timestamp - a.timestamp);
        
        // Limit the number of logs
        filteredLogs = filteredLogs.slice(0, Math.min(limit, 20));
        
        // Format logs for readability and truncate large messages
        const formattedLogs = filteredLogs.map(log => ({
          timestamp: dayjs(log.timestamp).toISOString(),
          type: log.type,
          source: log.source,
          message: truncateLogMessage(log.message)
        }));
        
        // Check for critical issues in the logs
        const criticalIssues = detectCriticalIssues(logs);
        
        return JSON.stringify({
          logs: formattedLogs,
          count: formattedLogs.length,
          totalAvailable: logs.length,
          criticalIssues: criticalIssues.length > 0 ? criticalIssues : undefined,
          timestamp: new Date().toISOString(),
          comment: formattedLogs.length > 0 
            ? `These are the ${formattedLogs.length} most recent logs matching your criteria. Use them to diagnose any issues you're experiencing.`
            : `No logs found matching your criteria. Try broadening your search or check if the issue might be elsewhere.`
        });
      } catch (e: any) {
        console.error(`Error while fetching client logs`, e);
        return JSON.stringify({
          error: e.message,
          timestamp: new Date().toISOString(),
          comment: `Error while fetching client logs: ${e.message}. Please try again with different parameters.`
        });
      }
    }
  });
};

/**
 * Truncate log messages to prevent overwhelming the LLM with too much data
 */
function truncateLogMessage(message: string): string {
  const MAX_MESSAGE_LENGTH = 500; // Characters
  const MAX_JSON_LENGTH = 800;    // Characters for JSON objects
  
  // If message is not too long, return as is
  if (message.length <= MAX_MESSAGE_LENGTH) {
    return message;
  }
  
  // Check if message is JSON
  if ((message.startsWith('{') && message.endsWith('}')) || 
      (message.startsWith('[') && message.endsWith(']'))) {
    try {
      const jsonObj = JSON.parse(message);
      const stringified = JSON.stringify(jsonObj, null, 2);
      
      if (stringified.length <= MAX_JSON_LENGTH) {
        return stringified;
      }
      
      // For large JSON objects, extract key information
      if (Array.isArray(jsonObj)) {
        return `[Array with ${jsonObj.length} items. First few: ${JSON.stringify(jsonObj.slice(0, 2), null, 2)}...]`;
      } else {
        const keys = Object.keys(jsonObj);
        const preview = {};
        // Take first few keys
        keys.slice(0, 5).forEach(key => {
          preview[key] = jsonObj[key];
        });
        return `{Object with keys: ${keys.join(', ')}. Preview: ${JSON.stringify(preview, null, 2)}...}`;
      }
    } catch (e) {
      // Not valid JSON, treat as regular text
    }
  }
  
  // For regular text, keep the beginning and end
  const beginLength = Math.floor(MAX_MESSAGE_LENGTH * 0.7);
  const endLength = MAX_MESSAGE_LENGTH - beginLength - 10; // 10 chars for the ellipsis and spacing
  
  return `${message.substring(0, beginLength)}... [${message.length - MAX_MESSAGE_LENGTH} more characters] ...${message.substring(message.length - endLength)}`;
}

/**
 * Detect critical issues in logs that the LLM should be aware of
 */
function detectCriticalIssues(logs: LogEntry[]): string[] {
  const criticalIssues: string[] = [];
  
  // Get recent logs (last 5 minutes)
  const recentLogs = logs.filter(log => 
    (Date.now() - log.timestamp) < 5 * 60 * 1000
  );
  
  // Check for error patterns
  const errorLogs = recentLogs.filter(log => log.type.toLowerCase() === 'error');
  if (errorLogs.length > 3) {
    criticalIssues.push(`Multiple errors detected (${errorLogs.length} in the last 5 minutes)`);
  }
  
  // Check for network errors
  const networkErrors = recentLogs.filter(log => 
    log.source === 'network' && 
    (log.message.includes('status: 5') || log.message.includes('failed') || log.message.includes('error'))
  );
  if (networkErrors.length > 0) {
    criticalIssues.push(`Network errors detected (${networkErrors.length} in the last 5 minutes)`);
  }
  
  // Check for authentication issues
  const authIssues = recentLogs.filter(log => 
    log.message.toLowerCase().includes('auth') && 
    (log.message.toLowerCase().includes('error') || log.message.toLowerCase().includes('failed') || 
     log.message.toLowerCase().includes('unauthorized') || log.message.toLowerCase().includes('forbidden'))
  );
  if (authIssues.length > 0) {
    criticalIssues.push(`Authentication issues detected (${authIssues.length} in the last 5 minutes)`);
  }
  
  return criticalIssues;
}
