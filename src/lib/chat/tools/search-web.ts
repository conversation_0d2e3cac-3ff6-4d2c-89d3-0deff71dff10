import { DataStreamWriter, tool } from "ai";
import { z } from "zod";
import { CreditUsageTracker } from "@/lib/credit/CreditUsageTracker";
import { TavilyService } from "@/lib/services/tavily";

/**
 * Tool to search the web for relevant information
 * This provides the AI with access to up-to-date information from the internet
 * Uses Tavily API for high-quality search results
 */
export const searchWeb = ({
  dataStream,
  creditUsageTracker
}: {
  dataStream: DataStreamWriter;
  creditUsageTracker: CreditUsageTracker;
}) => {
  return tool({
    description: 'Performs a web search to get a list of relevant web documentation for the given query and optional domain filter. Use this tool ONLY when you need up-to-date information that is not available in the codebase or when the user explicitly asks for external information. IT WILL ALWAYS be used to search for documentation. Do not use this tool for general coding questions or tasks that can be solved with your existing knowledge.',
    parameters: z.object({
      query: z.string().describe("The search query to find relevant information. Make sure to filter for documentation and be as specific as possible"),
      domain: z.string().optional().describe("Optional domain to recommend the search prioritize. Try to include it as the number of results is limited to 2 and truncated heavily to save tokens."),
      reason: z.string().describe("Explain why this information cannot be found in the codebase and why it's essential for completing the user's task")
    }),
    execute: async ({ query, domain, reason }: { 
      query: string;
      domain?: string;
      reason: string;
    }) => {
      try {
        console.log(`Searching web for: "${query}". Reason: ${reason}, Domain: ${domain}`);

        // Validate the search reason to ensure the tool is not being misused
        // const validReasons = [
        //   "current events",
        //   "up-to-date information",
        //   "external documentation",
        //   "third-party API details",
        //   "user explicitly requested",
        //   "package documentation"
        // ];
        //
        // const isValidReason = validReasons.some(validReason =>
        //   reason.toLowerCase().includes(validReason.toLowerCase())
        // );
        
        // if (!isValidReason) {
        //   return JSON.stringify({
        //     error: "Invalid search reason. Web search should only be used for obtaining up-to-date information that cannot be found in the codebase or when explicitly requested by the user.",
        //     suggestedAction: "Use your existing knowledge or codebase search tools instead."
        //   });
        // }
        
        // Create a new instance of the TavilyService
        const tavilyService = new TavilyService();
        
        // Configure search options
        const searchOptions = {
          numberOfTries: 1,
          includeRawContent: true,
          includedDomains: domain ? [domain] : undefined
        };
        
        // Perform the search
        const searchResponse = await tavilyService.search(
          query,
          'advanced',
          2, // Limit to 5 results to conserve tokens
          searchOptions,
        );

        // Track the operation for credit usage
        creditUsageTracker.trackOperation('tool_call');
        
        // Format the results
        const formattedResults = searchResponse.results.map(result => ({
          title: result.title,
          url: result.url,
          content: (result.rawContent || result.content).substring(0, 500) + ((result.rawContent || result.content).length > 500 ? '... // Truncated to save tokens' : ''),
        }));


        return JSON.stringify({
          query,
          results: formattedResults,
          answer: searchResponse.answer || "No direct answer available.",
          comment: 'Use this information to supplement your existing knowledge. Always cite sources when providing information from web searches.'
        });
      } catch (e: any) {
        console.error('Error while searching the web', e);
        return `Error while searching the web. Please try again. Error: ${e.message}`;
      }
    }
  });
};
