import {z} from 'zod';
import {tool} from "ai";
import {getProjectById} from '@/lib/db/project-queries';
import {Project} from "@/lib/db/schema";
import {ToolCountTracker} from './tool-count-tracker';
import {SupabaseContextEngine} from '@/lib/services/supabase-context-engine';
import {SupabaseIntegrationProvider} from '@/lib/integrations/supabase/SupabaseIntegrationProvider';

/**
 * Schema for the query Supabase context tool
 */
export const QuerySupabaseContextSchema = z.object({
    query: z.string().describe('The natural language query about Supabase resources. BEST QUERIES are specific, focused on particular functionality (e.g., "Show me tables related to user authentication" or "Find functions that handle payment processing" or "Show me the users table, the OPENAI secret, RLS Policies related to profile"). WORST QUERIES are vague or overly broad (e.g., "Show me all tables" or "What does this project do?"). Include specific entity names along with resources or functionality when possible.'),
    excludedResources: z.array(z.string()).optional().describe('Array of Supabase resources to exclude from the analysis in the format "resourceType.resourceName" (e.g., "table.users", "function.get_user"). You can use wildcards like "table.*" to exclude all tables.'),
    executeSQL: z.string().optional().describe('Optional SELECT SQL query to execute directly against the database. ONLY SELECT queries are allowed for security reasons. Any other query types will be rejected. Results will be truncated to 3000 characters if too large.'),
    reason: z.string().describe('ANSWER all questions: Explain why you need this Supabase information. Is it absolutely necessary for the current task? Is the cost justified?'),
});

/**
 * Tool for querying Supabase resources using the context engine
 * This replaces the need for the bulky getSupabaseInstructions tool
 */
export const querySupabaseContext = ({
    projectId,
    messageId = '',
}: {
    projectId: string, 
    messageId?: string, 
}) => {
    return tool({
        description: 'Query Supabase resources or execute SQL (either one at a time) to understand the project tables, dbFunctions, triggers, functions, RLS policies, storage buckets, and secrets. ' +
            'Use this instead of requesting full Supabase instructions. Issue queries that are as specific as possible. ' +
            'You can exclude specific resources using the excludedResources parameter in the format "resourceType.resourceName" or use wildcards like "table.*". ' +
            'This tool uses a two-stage approach to efficiently retrieve only the relevant Supabase resources. ' +
            'If the initial query returns incomplete information, the tool may indicate mustRetrieveAdditionalResources=true with guidance on what additional resources to request in a follow-up query. ' +
            'You can also execute SELECT SQL queries directly against the database using the executeSQL parameter. Only SELECT queries are allowed for security reasons. When executing a SQL query, code exploration will not happen.',
        parameters: QuerySupabaseContextSchema,
        execute: async ({query, excludedResources, executeSQL, reason}: z.infer<typeof QuerySupabaseContextSchema>) => {
            try {
                console.log('Supabase Query:', query);
                console.log('Excluded Resources:', excludedResources || []);
                console.log('SQL Query:', executeSQL || 'None');
                console.log('Reason:', reason);

                // Validate inputs
                if (!query && !executeSQL) {
                    return {
                        result: null,
                        message: "Either a natural language query or SQL query is required."
                    };
                }

                // Get the tool tracker instance
                const toolTracker = ToolCountTracker.getInstance();

                console.log('Tool call allowed (querySupabaseContext):', toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext'));

                // Check if we should allow this tool call
                if (messageId && !toolTracker.shouldAllowToolCall(messageId, 'querySupabaseContext')) {
                    return {
                        result: null,
                        message: "⚠️ Tool call limit reached. You've already made multiple Supabase context queries. Please implement or summarize with the information you already have.",
                    };
                }

                // Increment the tool count if we have a chat ID
                if (messageId) {
                    toolTracker.incrementToolCount(messageId, 'querySupabaseContext');
                }

                // Get project information
                let project: Project | null = null;
                try {
                    if (!projectId) {
                        return {
                            result: null,
                            message: "Project ID is required"
                        };
                    }
                    
                    project = await getProjectById({id: projectId});
                    if (!project) {
                        return {
                            result: null,
                            message: `Project not found: ${projectId}`
                        };
                    }
                    
                    // Check if project is connected to Supabase
                    if (!project.connectionId || !project.supabaseProjectId) {
                        return {
                            result: null,
                            message: 'This project is not connected to Supabase'
                        };
                    }
                } catch (error) {
                    console.error('Error fetching project:', error);
                    return {
                        result: null,
                        message: `Failed to fetch project information: ${error instanceof Error ? error.message : 'Unknown error'}`
                    };
                }
                
                // Handle SQL execution if provided
                if (executeSQL) {
                    console.log('Executing SQL query:', executeSQL);
                    
                    // Validate that it's a SELECT query only
                    const sqlLower = executeSQL.trim().toLowerCase();
                    if (!sqlLower.startsWith('select ')) {
                        return {
                            result: null,
                            message: '⚠️ Only SELECT queries are allowed for security reasons. Your query was rejected.'
                        };
                    }
                    
                    // Check for potentially harmful statements
                    const disallowedKeywords = ['insert', 'update', 'delete', 'drop', 'alter', 'create', 'truncate'];
                    if (disallowedKeywords.some(keyword => sqlLower.includes(keyword))) {
                        return {
                            result: null,
                            message: '⚠️ SQL query contains disallowed keywords. Only pure SELECT queries are permitted. Your query was rejected.'
                        };
                    }
                    
                    try {
                        // Execute the SQL query
                        const supabaseProvider = new SupabaseIntegrationProvider();
                        const result = await supabaseProvider.executeSQL({
                            query: executeSQL,
                            projectId
                        });
                        
                        // Truncate the result if it's too large
                        const resultStr = JSON.stringify(result || {}, null, 2);
                        const MAX_LENGTH = 3000;
                        
                        let sqlResult;
                        let message = '📊 SQL query executed successfully.';
                        
                        if (resultStr.length > MAX_LENGTH) {
                            sqlResult = {
                                data: result?.data,
                                truncated: true,
                                originalLength: resultStr.length,
                                message: `SQL result was truncated from ${resultStr.length} to ${MAX_LENGTH} characters`
                            };
                            message += ` Results were truncated (${sqlResult.message}).`;
                        } else {
                            sqlResult = result;
                        }
                        
                        return {
                            result: { sqlResult },
                            message,
                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')
                        };
                    } catch (error) {
                        console.error('SQL execution error:', error);
                        return {
                            result: null,
                            message: `⚠️ Failed to execute SQL query: ${error instanceof Error ? error.message : 'Unknown error'}`
                        };
                    }
                } else {
                    // Only query the context engine if no SQL was provided
                    try {
                        // Initialize the Supabase context engine
                        const contextEngine = new SupabaseContextEngine(project);

                        // Query the Supabase context engine
                        const result = await contextEngine.getRelevantSupabaseResources(query, excludedResources);

                        // Prepare a more informative response
                        const resourceCount = result.resources?.length || 0;
                        let message = `Found ${resourceCount} relevant Supabase resources for your query.`;

                        // Add information about resource types
                        const resourceTypes = result.resources?.map(r => r.resourceType) || [];
                        const uniqueTypes = [...new Set(resourceTypes)];
                        
                        if (uniqueTypes.length > 0) {
                            message += ` Types: ${uniqueTypes.join(', ')}.`;
                        }
                        
                        // Add information about excluded resources if any
                        if (excludedResources && excludedResources.length > 0) {
                            message += `\n${excludedResources.length} resources were excluded from the analysis.`;
                        }
                        
                        // Add information about additional resources if needed
                        if (result.hasAdditional) {
                            message += `\nThere are additional resources that may be relevant but weren't included due to the result limit.`;
                            
                            if (result.mustRetrieveAdditionalResources) {
                                message += `\n\n⚠️ IMPORTANT: You MUST make another query to retrieve critical additional resources. ${result.additionalResourcesSummary}`;
                            } else if (result.additionalResourcesSummary) {
                                message += `\n\nConsider querying for: ${result.additionalResourcesSummary}`;
                            }
                        }
                        
                        return {
                            result,
                            message,
                            toolCount: ToolCountTracker.getInstance().getToolCount(messageId, 'querySupabaseContext')
                        };
                    } catch (error) {
                        console.error('Supabase context engine error:', error);
                        return {
                            result: null,
                            message: `⚠️ Failed to query Supabase context engine: ${error instanceof Error ? error.message : 'Unknown error'}`
                        };
                    }
                }
            } catch (error) {
                // Global error handler - ensures we never throw errors
                console.error('Unexpected error in querySupabaseContext:', error);
                return {
                    result: null,
                    message: `⚠️ An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`
                };
            }
        },
    });
};
