// import {fileEditSchema} from "@/lib/chat/schemas/file-edit.schema";
// import {DataStreamWriter, tool} from "ai";
// import {FileLineManager} from "@/lib/editor/FileLineManager";
//
// export const editFileTool = ({
//                                  fileManager,
//                                  processImagePlaceholders,
//                                  processVideoPlaceholders,
//                                  dataStream
//                              }: {
//     fileManager: FileLineManager,
//     dataStream: DataStreamWriter,
//     processImagePlaceholders: (text: string) => Promise<string>,
//     processVideoPlaceholders: (text: string) => Promise<string>
//
// }) => {
//     return tool({
//         description: 'Edit an existing file using line numbers',
//         parameters: fileEditSchema,
//         execute: async ({
//                             absolutePath,
//                             edits
//                         }: any) => {
//             try {
//                 // console.log('Result', {
//                 //     completeFileCheckedValidation,
//                 //     lineRange,
//                 //     append,
//                 //     needDeletion,
//                 //     checkList1,
//                 //     rationaleForReplacingEntireFile,
//                 //     rationaleForChecklist1
//                 // })
//                 // Apply all edits using the file manager
//                 fileManager.applyFileEdits({absolutePath, edits});
//
//                 // Get the final content
//                 let updatedContent = fileManager.getFinalContent(absolutePath);
//
//                 // Process placeholders
//                 updatedContent = await processImagePlaceholders(updatedContent);
//                 updatedContent = await processVideoPlaceholders(updatedContent);
//
//
//                 dataStream.writeData({
//                     type: 'file-operation',
//                     content: {
//                         type: 'edit',
//                         absolutePath,
//                         content: updatedContent
//                     }
//                 });
//                 return `Updated file ${absolutePath} successfully!`;
//             } catch (e: any) {
//                 console.log('Error while apply edits', e);
//                 return `Error while applying changes. Please rethink and make the changes again. Error: ${e.message}`
//             }
//         }
//     })
// }