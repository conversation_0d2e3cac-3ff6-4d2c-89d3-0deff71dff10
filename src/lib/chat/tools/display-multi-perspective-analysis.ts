import { DataStreamWriter, tool } from "ai";
import { z } from "zod";

/**
 * Tool to display multi-perspective analysis results in the UI
 * This provides a structured way to visualize the multi-perspective analysis
 * with persona contributions and synthesis
 */
export const displayMultiPerspectiveAnalysis = ({
  dataStream
}: {
  dataStream: DataStreamWriter;
}) => {
  return tool({
    description: 'Displays the results of a multi-perspective analysis in the UI. Use this to show the output of the multi-perspective analysis tool in a structured, interactive format. This tool helps visualize the different perspectives, dialogue rounds, and final synthesis.',
    parameters: z.object({
      topic: z.string().describe('The topic that was analyzed'),
      contributions: z.array(z.object({
        persona: z.string().describe('The name of the persona who contributed'),
        content: z.string().describe('The content of the contribution'),
        round: z.number().describe('The round number of the contribution'),
        replyTo: z.array(z.string()).optional().describe('The personas this contribution is replying to')
      })).describe('The contributions from different personas across discussion rounds'),
      synthesis: z.string().describe('The final synthesis of all perspectives'),
      reason: z.string().describe('Why you are displaying these results')
    }),
    execute: async ({ topic, contributions, synthesis, reason }) => {
      try {
        console.log(`Displaying multi-perspective analysis for topic: ${topic}. Reason: ${reason}`);
        
        // Format the data for the UI component
        const result = {
          topic,
          contributions,
          synthesis,
          timestamp: new Date().toISOString()
        };
        
        // Return the formatted result
        return JSON.stringify({
          result,
          reason,
          state: 'complete',
          type: 'multi-perspective-analysis',
          timestamp: new Date().toISOString()
        });
      } catch (e: any) {
        console.error(`Error while displaying multi-perspective analysis`, e);
        return JSON.stringify({
          error: e.message,
          reason,
          state: 'error',
          type: 'multi-perspective-analysis',
          timestamp: new Date().toISOString()
        });
      }
    }
  });
};
