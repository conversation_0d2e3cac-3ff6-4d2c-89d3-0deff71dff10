import {DataStreamWriter, tool} from "ai";
import {z} from "zod";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";

/**
 * Tool for human-in-the-loop client testing
 * This tool requires user confirmation before proceeding
 * No execute function is provided to enable human-in-the-loop pattern
 */
export const clientTestingTool = ({
                                      dataStream,
                                      creditUsageTracker
                                  }: {
    dataStream: DataStreamWriter;
    creditUsageTracker: CreditUsageTracker;
}) => {
    return tool({
        description: 'After every complex step, ask the user to test the functionality.',
        parameters: z.object({
            featuresToTest: z.string().describe("What should the user test? Be concise, noon-technical and focussed"),
            expectations: z.string().describe("What is the expected outcome?"),
            reason: z.string().describe("Why do you need the user to test the app right now?")
        })
        // No execute function - this enables human-in-the-loop pattern
        // The tool result will be added via addToolResult when user completes testing
    });
};
