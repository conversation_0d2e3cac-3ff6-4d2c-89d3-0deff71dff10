import {fileEditSchema} from "@/lib/chat/schemas/file-edit.schema";
import {DataStreamWriter, tool} from "ai";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {FileItem} from "@/types/file";
import {z} from "zod";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";
import {getProjectById, updateProject} from "@/lib/db/queries";
import dayjs from "dayjs";

// In src/lib/chat/tools/add-ai-memory.ts

export const addAiMemory = ({
                                dataStream,
                                creditUsageTracker,
                                projectId
                            }: {
    dataStream: DataStreamWriter,
    creditUsageTracker: CreditUsageTracker,
    projectId: string
}) => {
    return tool({
        description: `Use the addAiMemory tool to store critical information for future interactions.
        WHEN TO USE:
        - After completing part of a multi-step task to track remaining steps
        - To record user preferences, project requirements, or design decisions
        - To capture technical constraints or architectural decisions
        - To maintain context across multiple interactions

        HOW TO USE:
        - Use clear prefixes for different types of memories:
          * USER PREFERENCE: For user style/feature preferences
          * REQUIREMENT: For project requirements
          * COMPLETED/NEXT: For task progress tracking
          * DECISION: For design/technical decisions
          * CONSTRAINT: For technical limitations
        - Be specific and concise (1-2 sentences per memory)
        - Always use at the END of your response
        `,
        parameters: z.object({
            knowledge: z.string().describe(`Concise, specific information to remember for future interactions. Use prefixes like USER PREFERENCE:, REQUIREMENT:, COMPLETED:/NEXT:, DECISION:, or CONSTRAINT: to categorize the memory. Be specific and actionable - this information will be available in future interactions.`)
        }),
        execute: async ({knowledge}: { knowledge: string }) => {
            try {
                const project = await getProjectById({id: projectId})
                if (!project) {
                    return "Project not found"
                }

                // Constants for memory management
                const MAX_MEMORY_CHARS = 8000; // About 2000 tokens
                const MAX_CATEGORY_ENTRIES = 10; // Maximum entries per category before summarization

                // Process the new knowledge entry
                const timestamp = dayjs().toISOString();
                const newEntry = {
                    timestamp,
                    content: knowledge.trim(),
                    category: extractCategory(knowledge)
                };

                // Parse existing memory into structured format
                const existingMemories = parseMemories(project.aiGeneratedMemory || '');

                // Add new entry
                existingMemories.push(newEntry);

                // Manage memory size through categorization and summarization
                const managedMemories = manageMemorySize(existingMemories, MAX_MEMORY_CHARS, MAX_CATEGORY_ENTRIES);

                // Convert back to string format
                const updatedMemory = memoriesToString(managedMemories);

                // Update the project with the new memory
                creditUsageTracker.trackOperation("add_ai_memory", 1);
                await updateProject({id: projectId, aiGeneratedMemory: updatedMemory});
                return "Memory saved and optimized"
            } catch (e: any) {
                console.log('Error while saving memory', e);
                return `Failed to save memory: ${e.message}`
            }
        }
    });
};

// Helper function to extract category from knowledge string
function extractCategory(knowledge: string): string {
    const knowledgeLower = knowledge.toLowerCase();
    if (knowledgeLower.startsWith('user preference:')) return 'USER_PREFERENCE';
    if (knowledgeLower.startsWith('requirement:')) return 'REQUIREMENT';
    if (knowledgeLower.startsWith('completed:')) return 'TASK_PROGRESS';
    if (knowledgeLower.startsWith('next:')) return 'TASK_PROGRESS';
    if (knowledgeLower.startsWith('decision:')) return 'DECISION';
    if (knowledgeLower.startsWith('constraint:')) return 'CONSTRAINT';
    return 'GENERAL'; // Default category
}

// Parse memory string into structured format
function parseMemories(memoryString: string): Array<{timestamp: string, content: string, category: string}> {
    if (!memoryString) return [];

    const entries = memoryString.split('-------').filter(entry => entry.trim());
    return entries.map(entry => {
        const parts = entry.split('\n').filter(part => part.trim());
        const timestamp = parts[0]?.trim() || dayjs().toISOString();
        const content = parts.slice(1).join('\n').replace('---------------------', '').trim();
        return {
            timestamp,
            content,
            category: extractCategory(content)
        };
    });
}

// Manage memory size through categorization and prioritization
function manageMemorySize(
    memories: Array<{timestamp: string, content: string, category: string}>,
    maxChars: number,
    maxCategoryEntries: number
): Array<{timestamp: string, content: string, category: string}> {
    // Group memories by category
    const categorized: Record<string, Array<{timestamp: string, content: string, category: string}>> = {};

    memories.forEach(memory => {
        if (!categorized[memory.category]) {
            categorized[memory.category] = [];
        }
        categorized[memory.category].push(memory);
    });

    // Sort each category by timestamp (newest first)
    Object.keys(categorized).forEach(category => {
        categorized[category].sort((a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
    });

    // Summarize categories with too many entries
    Object.keys(categorized).forEach(category => {
        if (categorized[category].length > maxCategoryEntries) {
            const recentEntries = categorized[category].slice(0, maxCategoryEntries);
            const olderEntries = categorized[category].slice(maxCategoryEntries);

            // Create a summary entry for older entries
            if (olderEntries.length > 0) {
                const oldestDate = new Date(olderEntries[olderEntries.length - 1].timestamp);
                const newestDate = new Date(olderEntries[0].timestamp);

                const summaryEntry = {
                    timestamp: dayjs().toISOString(),
                    content: `SUMMARY (${olderEntries.length} older entries from ${oldestDate.toLocaleDateString()} to ${newestDate.toLocaleDateString()}): ${summarizeEntries(olderEntries)}`,
                    category: category
                };

                // Replace older entries with summary
                categorized[category] = [summaryEntry, ...recentEntries];
            }
        }
    });

    // Flatten and prioritize entries if still too large
    let result: any[] = [];

    // Priority order: TASK_PROGRESS, REQUIREMENT, CONSTRAINT, DECISION, USER_PREFERENCE, GENERAL
    const priorityOrder = ['TASK_PROGRESS', 'REQUIREMENT', 'CONSTRAINT', 'DECISION', 'USER_PREFERENCE', 'GENERAL'];

    // Add entries in priority order until we reach the size limit
    let currentSize = 0;

    for (const category of priorityOrder) {
        if (categorized[category]) {
            for (const entry of categorized[category]) {
                const entrySize = JSON.stringify(entry).length;
                if (currentSize + entrySize <= maxChars) {
                    result.push(entry);
                    currentSize += entrySize;
                } else {
                    // If we can't add more entries, break out
                    break;
                }
            }
        }
    }

    return result;
}

// Simple summarization function
function summarizeEntries(entries: Array<{timestamp: string, content: string, category: string}>): string {
    // For now, just take the first sentence of each entry, up to 5 entries
    return entries.slice(0, 5).map(entry => {
        const firstSentence = entry.content.split('.')[0];
        return firstSentence + (firstSentence.endsWith('.') ? '' : '.');
    }).join(' ');
}

// Convert memories back to string format
function memoriesToString(memories: Array<{timestamp: string, content: string, category: string}>): string {
    return memories.map(memory =>
        `-------${memory.timestamp} ------- \n ${memory.content} \n ---------------------`
    ).join('');
}