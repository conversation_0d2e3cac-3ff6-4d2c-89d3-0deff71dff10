/**
 * A simple singleton class to track tool usage counts per conversation
 * to prevent excessive tool calls and implement circuit breakers
 */
export class ToolCountTracker {
  private static instance: ToolCountTracker;

  // Map of chatId -> toolName -> count
  private toolCounts: Map<string, Map<string, number>> = new Map();

  // Tool limits
  private readonly QUERY_CODEBASE_LIMIT = 6;
  private readonly CLIENT_LOGS_LIMIT = 1;
  private readonly TOTAL_TOOLS_LIMIT = 10;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): ToolCountTracker {
    if (!ToolCountTracker.instance) {
      ToolCountTracker.instance = new ToolCountTracker();
    }
    return ToolCountTracker.instance;
  }

  /**
   * Increment the count for a specific tool in a conversation
   * @param chatId The conversation ID
   * @param toolName The name of the tool being used
   * @returns The new count for this tool
   */
  public incrementToolCount(chatId: string, toolName: string): number {
    if (!this.toolCounts.has(chatId)) {
      this.toolCounts.set(chatId, new Map<string, number>());
    }

    const chatTools = this.toolCounts.get(chatId)!;
    const currentCount = chatTools.get(toolName) || 0;
    const newCount = currentCount + 1;
    chatTools.set(toolName, newCount);

    return newCount;
  }

  /**
   * Get the current count for a specific tool in a conversation
   * @param messageId The conversation ID
   * @param toolName The name of the tool
   * @returns The current count for this tool
   */
  public getToolCount(messageId: string, toolName: string): number {
    if (!this.toolCounts.has(messageId)) {
      return 0;
    }

    return this.toolCounts.get(messageId)?.get(toolName) || 0;
  }

  /**
   * Get the total count of all tools used in a conversation
   * @param messageId The conversation ID
   * @returns The total count of all tools
   */
  public getTotalToolCount(messageId: string): number {
    if (!this.toolCounts.has(messageId)) {
      return 0;
    }

    let total = 0;
    this.toolCounts.get(messageId)?.forEach(count => {
      total += count;
    });

    return total;
  }

  /**
   * Check if a tool call should be allowed based on limits
   * @param messageId The conversation ID
   * @param toolName The name of the tool
   * @returns Whether the tool call should be allowed
   */
  public shouldAllowToolCall(messageId: string, toolName: string): boolean {
    // If the user message contains "continue", block all tool calls
    // if (isContinueMessage) {
    //   return false;
    // }

    // Check specific tool limits
    if (toolName === 'queryCodebase' && this.getToolCount(messageId, toolName) >= this.QUERY_CODEBASE_LIMIT) {
      return false;
    }

    if (toolName === 'getClientLogs' && this.getToolCount(messageId, toolName) >= this.CLIENT_LOGS_LIMIT) {
      return false;
    }

    // Check total tool limit
    return this.getTotalToolCount(messageId) < this.TOTAL_TOOLS_LIMIT;
  }

  /**
   * Reset the counts for a specific conversation
   * @param messageId The conversation ID to reset
   */
  public resetCounts(messageId: string): void {
    this.toolCounts.delete(messageId);
  }

  /**
   * Clean up old conversation data to prevent memory leaks
   * @param maxAgeMs Maximum age in milliseconds before a conversation's data is removed
   */
  public cleanup(olderThanMs: number = 24 * 60 * 60 * 1000): void {
    // In a real implementation, you would track timestamps and clean up old entries
    // This is a simplified version
  }
}
