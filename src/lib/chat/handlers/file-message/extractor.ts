import {FileItem} from "@/types/file";

export class Extractor {
    public extractImportsFromContent(content: string): string[] {
        const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+[^;]+|[^;{]*)\s+from\s+['""][^'"]+['""];?|import\s+['""][^'"]+['""];?/g;

        const matches = content.match(importRegex) || [];

        // Remove duplicates and return
        return [...new Set(matches)];
    }

    /**
     * Determine file type based on name and extension
     */
    public getFileType(fileName: string): string {
        if (fileName.includes('navigation') || fileName.includes('Navigator')) {
            return 'navigation';
        } else if (fileName.includes('context') || fileName.includes('provider')) {
            return 'context';
        } else if (fileName.includes('components') || /\.[jt]sx$/.test(fileName)) {
            return 'component';
        } else if (fileName.includes('utils') || fileName.includes('helpers')) {
            return 'utility';
        } else if (fileName.includes('api') || fileName.includes('service')) {
            return 'service';
        } else if (fileName.includes('types') || fileName.includes('interfaces')) {
            return 'types';
        } else {
            return 'other';
        }
    }

    public getFileLineCount(file: string): {count: number, warning: string} {
        const fileLines = file.split('\n').length - 1;
        return {
            count: fileLines,
            warning: fileLines > 300 ? 'This file is becoming too big. Breakdown into smaller manageable and maintainable chunks': ''
        }
    }

    /**
     * Extract minimal file structure with key information and minimal tokens
     */
    public extractMinimalFileStructure(content: string): string {
        // 1. Extract all imports
        const imports = this.extractImportsFromContent(content);

        // // 2. Extract component/function signatures
        // const signatures = this.extractSignatures(content);
        //
        // // 3. Extract state declarations and hooks
        // const stateHooks = this.extractStateHooks(content);
        //
        // // 4. Extract key prop types and interfaces
        // const types = this.extractKeyTypes(content);
        //
        // // 5. Extract route definitions for navigation files
        // const routes = this.extractRouteDefinitions(content);

        return `
        ${imports.map(imp => `${imp}`).join('\n')}
       
    `;
        // ${types.length > 0 ? `<types>${types.join('\n')}</types>` : ''}
        // ${signatures.map(sig => `<signature>${sig}</signature>`).join('\n')}
        // ${stateHooks.length > 0 ? `<state>${stateHooks.join('\n')}</state>` : ''}
        // ${routes.length > 0 ? `<routes>${routes.join('\n')}</routes>` : ''}
    }

    /**
     * Extract function and component signatures
     */
    private extractSignatures(content: string): string[] {
        // Extract function/component signatures without bodies
        const signatureRegex = /(?:function|const)\s+(\w+)\s*(?:\([^)]*\))(?:\s*:\s*[^{]+)?/g;
        const matches = content.match(signatureRegex) || [];
        return matches.map(match => match.trim());
    }

    /**
     * Extract state hooks like useState, useContext, etc.
     */
    private extractStateHooks(content: string): string[] {
        // Extract useState, useContext, useReducer declarations
        const hookRegex = /(?:const|let|var)\s+\[\w\s,]+\]\s*=\s*use\w+\([^;]*\)/g;
        const matches = content.match(hookRegex) || [];
        return matches.map(match => match.trim());
    }

    /**
     * Extract key type definitions
     */
    private extractKeyTypes(content: string): string[] {
        // Extract interfaces and types that are likely to be important
        const typeRegex = /(?:interface|type)\s+(\w+)(?:Props|State|Config|Options)?\s*(?:extends[^{]+)?\s*\{[^}]*\}/g;
        const matches = content.match(typeRegex) || [];
        return matches.map(match => match.trim());
    }

    /**
     * Extract route definitions from navigation files
     */
    private extractRouteDefinitions(content: string): string[] {
        // Extract route definitions from navigation files
        const routeRegex = /(?:<\w+\.(?:Screen|Navigator)[^>]*>|createStackNavigator|createBottomTabNavigator|createDrawerNavigator)/g;
        const matches = content.match(routeRegex) || [];
        return matches.map(match => match.trim());
    }

    /**
     * Generate a minimal dependency graph showing file relationships
     */
    public generateMinimalDependencyGraph(files: any[]): string {
        // Create a minimal representation of file dependencies
        const graph: Record<string, string[]> = {};

        files.forEach(file => {
            const imports = this.extractImportsFromContent(file.content);
            const importedFiles = imports
                .map(imp => this.extractFilePathFromImport(imp))
                .filter(Boolean) as string[];

            graph[file.name] = importedFiles;
        });

        // Convert to a compact string representation
        return `<file_relationships>
        ${Object.entries(graph)
            .map(([file, deps]) => `${file} → ${deps.join(', ')}`)
            .join('\n')}
    </file_relationships>`;
    }

    /**
     * Extract file path from import statement
     */
    private extractFilePathFromImport(importStatement: string): string | null {
        // Extract the file path from import statements
        const pathMatch = importStatement.match(/from\s+['"]([^'"]+)['"];?/);
        if (!pathMatch) return null;

        const path = pathMatch[1];
        // Convert relative path to filename
        if (path.startsWith('./') || path.startsWith('../')) {
            const parts = path.split('/');
            return parts[parts.length - 1];
        }

        return path;
    }

}