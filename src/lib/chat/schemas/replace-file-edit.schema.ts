import { z } from "zod";

export const replaceFileSchema = z.object({
    absolutePath: z.string().describe(
        "ABSOLUTE file path to replace. MUST match exactly with project structure. " +
        "This operation will replace the entire file content."
    ),
    content: z.string().describe(
        "COMPLETE file contents with these CRITICAL requirements:\n\n" +
        "1. IMPORTS AND DEPENDENCIES:\n" +
        "   - Include ALL necessary imports\n" +
        "   - Check imports from ALL related files being edited\n" +
        "   - Maintain existing import order conventions\n" +
        "   - Add new imports for any new functionality\n\n" +
        "2. CONTEXT PRESERVATION:\n" +
        "   - Preserve ALL existing functionality unless explicitly meant to change\n" +
        "   - Maintain type definitions and interfaces\n" +
        "   - Keep existing exports and module structure\n" +
        "   - Preserve comments that explain complex logic\n\n" +
        "3. CROSS-FILE CONSISTENCY:\n" +
        "   - Ensure types match across all related files\n" +
        "   - Maintain consistent naming with other files\n" +
        "   - Check for shared interfaces and types\n" +
        "   - Verify component prop types match usage\n\n" +
        "4. CODE STYLE:\n" +
        "   - Follow project's existing style conventions\n" +
        "   - Maintain consistent indentation\n" +
        "   - Use appropriate line breaks and spacing\n" +
        "   - Keep code organization patterns\n\n" +
        "5. VALIDATION CHECKLIST:\n" +
        "   - Verified all imports are complete and necessary\n" +
        "   - Checked for circular dependencies\n" +
        "   - Validated all type definitions\n" +
        "   - Confirmed no missing context from original file\n" +
        "   - Ensured compatibility with other file changes"
    )
});
