import { z } from "zod";

export const fileEditSchema = z.object({
    absolutePath: z.string().describe(
        "ABSOLUTE file path to edit. MUST match exactly with project structure. " +
        "Only one edit operation allowed per file per user request."
    ),
    edits: z.array(z.object({
        searchPattern: z.string().describe(
            "The exact code snippet to search for in the file. This is the text that will be replaced. " +
            "Be as specific as possible to avoid ambiguity. Include enough context to ensure a unique match. " +
            "For new files, leave this empty."
        ),
        replacementContent: z.string().describe(
            "The new code that will replace the search pattern. " +
            "Make sure to maintain proper indentation and formatting. " +
            "For appending content, include both the new content and the original content."
        ),
        isAppend: z.boolean().optional().describe(
            "If true, the replacement will be inserted before the search pattern instead of replacing it. " +
            "Use this when adding new imports or when you need to add content without removing existing code."
        ),
        description: z.string().optional().describe(
            "A brief description of what this edit does. This helps with documentation and debugging."
        )
    })).describe(
        "ALL edits for this file in ONE array. Edits are applied atomically in order.\n" +
        "MUST bundle multiple changes for the same file to avoid conflicts.\n" +
        "DO NOT use for creating new files, use MO_FILE instead "
    )
});
