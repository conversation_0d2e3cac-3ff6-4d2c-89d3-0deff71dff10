import {z} from "zod";

export const fileCreateSchema = z.object({
    absolutePath: z.string().describe("File path from the root without starting with a /"),
    content: z.string()
        .describe('CRITICAL: Return the file contents only in this format <MO_FILE lang="typescript|javascript|markdown|json|xml|txt|yaml|sql" path="full absolute path of the file" mode="edit|create|fix"> ....contents go here ...</MO_FILE>')
})