import { Sandbox } from '@e2b/code-interpreter';
import { createPty, handleTerminalCommand, PROJECT_DIR } from './helpers';
import { 
  buildApkFast, 
  buildApkFull, 
  installApk, 
  buildAndInstallApk,
  fixRenderingIssues 
} from './apk-commands';

/**
 * Terminal commands for the Magically.life platform
 * This module exports all terminal-related functionality
 */

// Re-export helper functions
export { 
  createPty, 
  handleTerminalCommand, 
  PROJECT_DIR,
  // APK build commands
  buildApkFast,
  buildApkFull,
  installApk,
  buildAndInstallApk,
  fixRenderingIssues
};

/**
 * Initialize the APK build environment
 * This sets up all necessary scripts and configurations
 * 
 * @param session - The sandbox session
 * @returns Promise<boolean> - Whether the initialization was successful
 */
export async function initApkBuildEnvironment(
  session: { sandbox: Sandbox; terminalId: number | null }
) {
  try {
    // Copy the unified-apk-build.js script to the project directory
    const copyScriptCommand = `cp /tmp/unified-apk-build.js ${PROJECT_DIR}/scripts/`;
    await handleTerminalCommand(session, copyScriptCommand);
    
    // Make the script executable
    const chmodCommand = `chmod +x ${PROJECT_DIR}/scripts/unified-apk-build.js`;
    await handleTerminalCommand(session, chmodCommand);
    
    // Create a simple wrapper script for easy access
    const createWrapperCommand = `
cat > ${PROJECT_DIR}/build-apk << 'EOL'
#!/bin/bash

# Simple wrapper for the unified-apk-build.js script
# Usage: ./build-apk [fast|full|install] [options]

NODE_PATH=${PROJECT_DIR}/node_modules node ${PROJECT_DIR}/scripts/unified-apk-build.js "$@"
EOL

chmod +x ${PROJECT_DIR}/build-apk
`;
    await handleTerminalCommand(session, createWrapperCommand);
    
    console.log('APK build environment initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing APK build environment:', error);
    return false;
  }
}

/**
 * Run a complete APK build workflow
 * This handles the entire process from fixing rendering issues to building and installing the APK
 * 
 * @param session - The sandbox session
 * @param options - Build options
 * @returns Promise<boolean> - Whether the workflow was successful
 */
export async function runApkBuildWorkflow(
  session: { sandbox: Sandbox; terminalId: number | null },
  options: {
    method?: 'fast' | 'full';
    dev?: boolean;
    useHermes?: boolean;
    fixRendering?: boolean;
    install?: boolean;
    outputPath?: string;
  } = {}
) {
  try {
    const { 
      method = 'fast',
      dev = false,
      useHermes = true,
      fixRendering = true,
      install = true,
      outputPath = `${PROJECT_DIR}/build`
    } = options;
    
    // Initialize the build environment
    await initApkBuildEnvironment(session);
    
    // Fix rendering issues if requested
    if (fixRendering) {
      console.log('Fixing rendering issues...');
      await fixRenderingIssues(session);
    }
    
    // Build and optionally install the APK
    if (install) {
      return await buildAndInstallApk(session, {
        method,
        dev,
        useHermes,
        outputPath
      });
    } else {
      return method === 'fast'
        ? await buildApkFast(session, { dev, useHermes, outputPath })
        : await buildApkFull(session, { dev, outputPath });
    }
  } catch (error) {
    console.error('Error running APK build workflow:', error);
    return false;
  }
}

// Export a simple command map for easy access
export const commands = {
  buildFast: buildApkFast,
  buildFull: buildApkFull,
  install: installApk,
  buildAndInstall: buildAndInstallApk,
  fixRendering: fixRenderingIssues,
  initEnvironment: initApkBuildEnvironment,
  runWorkflow: runApkBuildWorkflow
};
