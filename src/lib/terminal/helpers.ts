import {CommandHandle, Sandbox} from '@e2b/code-interpreter';
import {v4 as uuidv4} from 'uuid';
import {getLatestFileState} from '@/lib/db/queries';
import {FileItem} from '@/types/file';
import archiver from "archiver";
import {Chat, chat, FileState} from "@/lib/db/schema";
import crypto from 'crypto';

const script = "#!/usr/bin/env node\n" +
    "\n" +
    "/**\n" +
    " * Unified APK Build Script\n" +
    " * \n" +
    " * This script provides a comprehensive solution for building Android APKs\n" +
    " * with various options:\n" +
    " * \n" +
    " * 1. Fast APK build - Uses a pre-built template APK and only replaces the JS bundle\n" +
    " * 2. Full APK build - Runs the complete build process including native code compilation\n" +
    " * \n" +
    " * The script is designed to work in the Docker environment specified in e2b.Dockerfile\n" +
    " * and can be integrated with the terminal helpers for a seamless experience.\n" +
    " */\n" +
    "\n" +
    "const fs = require('fs');\n" +
    "const path = require('path');\n" +
    "const { execSync } = require('child_process');\n" +
    "\n" +
    "// Constants for Android SDK paths based on e2b.Dockerfile\n" +
    "const ANDROID_SDK_ROOT = '/android-sdk';\n" +
    "const BUILD_TOOLS_PATH = `${ANDROID_SDK_ROOT}/build-tools/33.0.0`; // Use the version installed in Dockerfile\n" +
    "const ZIPALIGN_PATH = `${BUILD_TOOLS_PATH}/zipalign`;\n" +
    "const APKSIGNER_PATH = `${BUILD_TOOLS_PATH}/apksigner`;\n" +
    "\n" +
    "// Keystore credentials - should be stored securely in production\n" +
    "const KEYSTORE_PASSWORD = '76c2b9e4f8387318c562ac48a876a056';\n" +
    "const KEY_ALIAS = 'upload';\n" +
    "const KEY_PASSWORD = '76c2b9e4f8387318c562ac48a876a056';\n" +
    "\n" +
    "// Project paths\n" +
    "const PROJECT_DIR = process.cwd();\n" +
    "const TEMPLATE_APK_PATH = path.join(PROJECT_DIR, 'universal.apk');\n" +
    "const KEYSTORE_PATH = path.join(PROJECT_DIR, 'ks.jks');\n" +
    "\n" +
    "/**\n" +
    " * Builds an APK using the fast method (replacing only the JS bundle)\n" +
    " * \n" +
    " * @param {Object} options - Build options\n" +
    " * @param {boolean} options.dev - Whether to build in development mode\n" +
    " * @param {string} options.outputPath - Path to save the final APK\n" +
    " * @param {boolean} options.useHermes - Whether to use Hermes bytecode format\n" +
    " * @returns {string} - Path to the built APK\n" +
    " */\n" +
    "async function buildFastApk(options = {}) {\n" +
    "  const {\n" +
    "    dev = false,\n" +
    "    outputPath = path.join(PROJECT_DIR, 'build'),\n" +
    "    useHermes = true,\n" +
    "  } = options;\n" +
    "  \n" +
    "  console.log('🚀 Starting fast APK build...');\n" +
    "  const startTime = Date.now();\n" +
    "  \n" +
    "  // Create temp directory for build artifacts\n" +
    "  const tempDir = path.join(PROJECT_DIR, '.android-tmp');\n" +
    "  if (fs.existsSync(tempDir)) {\n" +
    "    fs.rmSync(tempDir, { recursive: true, force: true });\n" +
    "  }\n" +
    "  fs.mkdirSync(tempDir, { recursive: true });\n" +
    "  \n" +
    "  // Ensure output directory exists\n" +
    "  if (!fs.existsSync(outputPath)) {\n" +
    "    fs.mkdirSync(outputPath, { recursive: true });\n" +
    "  }\n" +
    "  \n" +
    "  try {\n" +
    "    // 1. Run prebuild to ensure native code is generated\n" +
    "    console.log('🔧 Running prebuild...');\n" +
    "    execSync('npx expo prebuild --platform android --clean', { cwd: PROJECT_DIR, stdio: 'inherit' });\n" +
    "    \n" +
    "    // 2. Bundle JavaScript code\n" +
    "    console.log('📦 Bundling JavaScript...');\n" +
    "    const bundleCommand = useHermes\n" +
    "      ? `npx expo export:embed --platform android --bundle-output ${path.join(tempDir, 'index.android.bundle')} --assets-dest ${path.join(tempDir, 'asset')} --dev ${dev} --bytecode`\n" +
    "      : `npx react-native bundle --platform android --dev ${dev} --entry-file index.js --bundle-output ${path.join(tempDir, 'index.android.bundle')} --assets-dest ${path.join(tempDir, 'asset')}`;\n" +
    "    \n" +
    "    execSync(bundleCommand, { cwd: PROJECT_DIR, stdio: 'inherit' });\n" +
    "    \n" +
    "    // 3. Copy the template APK\n" +
    "    console.log('📂 Using template APK...');\n" +
    "    if (!fs.existsSync(TEMPLATE_APK_PATH)) {\n" +
    "      throw new Error(`Template APK not found at ${TEMPLATE_APK_PATH}`);\n" +
    "    }\n" +
    "    \n" +
    "    const workingApkPath = path.join(tempDir, 'working.apk');\n" +
    "    fs.copyFileSync(TEMPLATE_APK_PATH, workingApkPath);\n" +
    "    \n" +
    "    // 4. Update only the JavaScript bundle in the APK\n" +
    "    console.log('🔄 Updating JavaScript bundle in APK...');\n" +
    "    const tempBundlePath = path.join(tempDir, 'index.android.bundle');\n" +
    "    const tempAssetsDir = path.join(tempDir, 'assets');\n" +
    "    fs.mkdirSync(tempAssetsDir, { recursive: true });\n" +
    "    fs.copyFileSync(tempBundlePath, path.join(tempAssetsDir, 'index.android.bundle'));\n" +
    "    \n" +
    "    // Use jar command to update only the bundle file\n" +
    "    execSync(`jar uf \"${workingApkPath}\" -C \"${tempDir}\" assets/index.android.bundle`, { stdio: 'inherit' });\n" +
    "    \n" +
    "    // 5. Copy assets if they exist\n" +
    "    const sourceAssetsPath = path.join(tempDir, 'asset');\n" +
    "    if (fs.existsSync(sourceAssetsPath) && fs.readdirSync(sourceAssetsPath).length > 0) {\n" +
    "      console.log('📁 Copying assets...');\n" +
    "      // Create a zip of assets\n" +
    "      execSync(`cd \"${sourceAssetsPath}\" && zip -r \"${tempDir}/assets.zip\" .`, { stdio: 'inherit' });\n" +
    "      // Add assets to APK\n" +
    "      execSync(`jar uf \"${workingApkPath}\" -C \"${sourceAssetsPath}\" .`, { stdio: 'inherit' });\n" +
    "    }\n" +
    "    \n" +
    "    // 6. Optimize the APK\n" +
    "    console.log('⚡ Optimizing APK...');\n" +
    "    const alignedApkPath = path.join(tempDir, 'app-aligned.apk');\n" +
    "    execSync(`${ZIPALIGN_PATH} -v -p 4 ${workingApkPath} ${alignedApkPath}`);\n" +
    "    \n" +
    "    // 7. Sign the APK\n" +
    "    console.log('🔐 Signing APK...');\n" +
    "    const finalApkPath = path.join(outputPath, dev ? 'app-debug.apk' : 'app-release.apk');\n" +
    "    \n" +
    "    execSync(`\n" +
    "      ${APKSIGNER_PATH} sign --ks ${KEYSTORE_PATH} \\\\\n" +
    "      --ks-pass pass:${KEYSTORE_PASSWORD} \\\\\n" +
    "      --ks-key-alias ${KEY_ALIAS} \\\\\n" +
    "      --key-pass pass:${KEY_PASSWORD} \\\\\n" +
    "      --out ${finalApkPath} \\\\\n" +
    "      ${alignedApkPath}\n" +
    "    `);\n" +
    "    \n" +
    "    const endTime = Date.now();\n" +
    "    console.log(`✅ Fast build completed in ${(endTime - startTime) / 1000} seconds!`);\n" +
    "    console.log(`📱 APK available at: ${finalApkPath}`);\n" +
    "    \n" +
    "    return finalApkPath;\n" +
    "  } catch (error) {\n" +
    "    console.error('❌ Build failed:', error.message);\n" +
    "    throw error;\n" +
    "  }\n" +
    "}\n" +
    "\n" +
    "/**\n" +
    " * Builds an APK using the full method (complete build process)\n" +
    " * \n" +
    " * @param {Object} options - Build options\n" +
    " * @param {boolean} options.dev - Whether to build in development mode\n" +
    " * @param {string} options.outputPath - Path to save the final APK\n" +
    " * @returns {string} - Path to the built APK\n" +
    " */\n" +
    "async function buildFullApk(options = {}) {\n" +
    "  const {\n" +
    "    dev = false,\n" +
    "    outputPath = path.join(PROJECT_DIR, 'build'),\n" +
    "  } = options;\n" +
    "  \n" +
    "  console.log('🚀 Starting full APK build...');\n" +
    "  const startTime = Date.now();\n" +
    "  \n" +
    "  // Ensure output directory exists\n" +
    "  if (!fs.existsSync(outputPath)) {\n" +
    "    fs.mkdirSync(outputPath, { recursive: true });\n" +
    "  }\n" +
    "  \n" +
    "  try {\n" +
    "    // 1. Run prebuild to ensure native code is generated\n" +
    "    console.log('🔧 Running prebuild...');\n" +
    "    execSync('npx expo prebuild --platform android --clean', { cwd: PROJECT_DIR, stdio: 'inherit' });\n" +
    "    \n" +
    "    // 2. Build the APK using Gradle\n" +
    "    console.log('🏗️ Building APK with Gradle...');\n" +
    "    const gradleCommand = dev\n" +
    "      ? './gradlew assembleDebug'\n" +
    "      : './gradlew assembleRelease';\n" +
    "    \n" +
    "    execSync(gradleCommand, { cwd: path.join(PROJECT_DIR, 'android'), stdio: 'inherit' });\n" +
    "    \n" +
    "    // 3. Copy the built APK to the output directory\n" +
    "    const gradleApkPath = dev\n" +
    "      ? path.join(PROJECT_DIR, 'android/app/build/outputs/apk/debug/app-debug.apk')\n" +
    "      : path.join(PROJECT_DIR, 'android/app/build/outputs/apk/release/app-release.apk');\n" +
    "    \n" +
    "    const finalApkPath = path.join(outputPath, dev ? 'app-debug.apk' : 'app-release.apk');\n" +
    "    fs.copyFileSync(gradleApkPath, finalApkPath);\n" +
    "    \n" +
    "    const endTime = Date.now();\n" +
    "    console.log(`✅ Full build completed in ${(endTime - startTime) / 1000} seconds!`);\n" +
    "    console.log(`📱 APK available at: ${finalApkPath}`);\n" +
    "    \n" +
    "    return finalApkPath;\n" +
    "  } catch (error) {\n" +
    "    console.error('❌ Build failed:', error.message);\n" +
    "    throw error;\n" +
    "  }\n" +
    "}\n" +
    "\n" +
    "/**\n" +
    " * Installs the APK on a connected device or emulator\n" +
    " * \n" +
    " * @param {string} apkPath - Path to the APK to install\n" +
    " * @returns {boolean} - Whether the installation was successful\n" +
    " */\n" +
    "async function installApk(apkPath) {\n" +
    "  try {\n" +
    "    console.log('📲 Installing APK on device...');\n" +
    "    execSync(`adb install -r -d -t \"${apkPath}\"`, { stdio: 'inherit' });\n" +
    "    console.log('✅ APK installed successfully!');\n" +
    "    return true;\n" +
    "  } catch (error) {\n" +
    "    console.error('❌ Installation failed:', error.message);\n" +
    "    return false;\n" +
    "  }\n" +
    "}\n" +
    "\n" +
    "// Command-line interface\n" +
    "if (require.main === module) {\n" +
    "  const args = process.argv.slice(2);\n" +
    "  const command = args[0];\n" +
    "  \n" +
    "  // Parse options\n" +
    "  const options = {\n" +
    "    dev: args.includes('--dev'),\n" +
    "    useHermes: !args.includes('--no-hermes'),\n" +
    "    outputPath: args.find(arg => arg.startsWith('--output='))?.split('=')[1] || path.join(PROJECT_DIR, 'build'),\n" +
    "  };\n" +
    "  \n" +
    "  // Run the appropriate command\n" +
    "  (async () => {\n" +
    "    try {\n" +
    "      let apkPath;\n" +
    "      \n" +
    "      switch (command) {\n" +
    "        case 'fast':\n" +
    "          apkPath = await buildFastApk(options);\n" +
    "          break;\n" +
    "        case 'full':\n" +
    "          apkPath = await buildFullApk(options);\n" +
    "          break;\n" +
    "        case 'install':\n" +
    "          const targetApk = args[1] || path.join(options.outputPath, options.dev ? 'app-debug.apk' : 'app-release.apk');\n" +
    "          await installApk(targetApk);\n" +
    "          break;\n" +
    "        default:\n" +
    "          console.log(`\n" +
    "Usage: node unified-apk-build.js [command] [options]\n" +
    "\n" +
    "Commands:\n" +
    "  fast     Build APK using fast method (template + JS bundle)\n" +
    "  full     Build APK using full method (complete build)\n" +
    "  install  Install APK on connected device\n" +
    "\n" +
    "Options:\n" +
    "  --dev            Build in development mode\n" +
    "  --no-hermes      Disable Hermes bytecode format\n" +
    "  --output=PATH    Specify output directory for APK\n" +
    "          `);\n" +
    "      }\n" +
    "    } catch (error) {\n" +
    "      process.exit(1);\n" +
    "    }\n" +
    "  })();\n" +
    "}\n" +
    "\n" +
    "// Export functions for use in other scripts\n" +
    "module.exports = {\n" +
    "  buildFastApk,\n" +
    "  buildFullApk,\n" +
    "  installApk,\n" +
    "};\n"

// Project directory constant
export const PROJECT_DIR = '/project';

export const prepareProject = async (fileState: FileState, options: {
    projectId: string,
    title: string,
    slug: string,
    bundleIdentifier: string,
    packageName: string,
    scheme: string,
    versionCode: number,
    versionName: string
}) => {
    const {files, dependencies} = fileState;
    if (!process.env.GITHUB_PAT) {
        throw new Error('GitHub Personal Access Token not configured');
    }

    // Fetch all template files
    console.log('Fetching all template files...');
    const templateFiles = await fetchAllRepoContents('base-v1');
    console.log('Found files:', Object.keys(templateFiles).join(', '));

    // Create files map starting with template files
    const filesMap: Record<string, string | Buffer<ArrayBufferLike>> = {};

    // Add template files with correct paths
    for (const [path, content] of Object.entries(templateFiles)) {
        filesMap[path] = content;
    }

    // Add user files
    (files as FileItem[]).filter(file => !!file.name).forEach(file => {
        filesMap[`src/${file.name}`] = file.content;
    });

    // Update package.json with dependencies
    if (!filesMap['package.json']) {
        throw new Error('package.json not found in template');
    }

    const {title, slug, bundleIdentifier, packageName, scheme, versionName, versionCode} = options;
    try {
        const appJSON = JSON.parse(filesMap['app.json'] as string);
        console.log('appJSON', appJSON)
        if(appJSON?.expo) {
            appJSON.expo.name = title;
            appJSON.expo.slug = slug;
            appJSON.expo.version = versionName || "1.0.0";
            appJSON.expo.scheme = scheme || slug;
            if(!appJSON.expo.ios) {
                appJSON.expo.ios = {};
            }
            appJSON.expo.ios.bundleIdentifier = bundleIdentifier;
            appJSON.expo.ios.buildNumber = versionName || "1.0.0";
            if(!appJSON.expo.android) {
                appJSON.expo.android = {};
            }
            appJSON.expo.android.package = packageName;
        }
        filesMap['app.json'] = JSON.stringify(appJSON, null, 2);
    } catch (error) {
        console.error('Error parsing app.json:', filesMap['app.json']);
        throw new Error('Invalid app.json in template');
    }

    try {
        const packageJson = JSON.parse(filesMap['package.json'] as string);
        packageJson.name = options.title.toLowerCase().replace(/\s/g, '-');
        packageJson.version = versionName || "1.0.0";
        packageJson.dependencies = {
            ...packageJson.dependencies,
            ...Object.entries(dependencies as Record<string, { version: "*" }>)
                .reduce((acc, [dependency, version]) => {
                    // Remove the partial packages to ensure it doesn't cause build issues
                    if(['zustand/middleware', 'date-fns/locale'].includes(dependency)) {
                        return acc;
                    }
                    acc[dependency] = version.version || "*";
                    return acc;
                }, {} as { [key: string]: string })
        };
        filesMap['package.json'] = JSON.stringify(packageJson, null, 2);
    } catch (error) {
        console.error('Error parsing package.json:', filesMap['package.json']);
        throw new Error('Invalid package.json in template');
    }

    // Create manifest
    const magicallyManifest = {
        version: versionName || "1.0.0",
        projectId: options.projectId,
        projectName: options.title,
        fileManifest: (files as FileItem[]).map(file => {
            if (file.type === "file") {
                const hash = crypto
                    .createHash('sha256')
                    .update(file.content)
                    .digest('hex');

                return {
                    path: file.name,
                    language: file.language,
                    hash: hash
                };
            }
            return null;
        }).filter(f => !!f)
    };

    // Add manifest to files
    filesMap['magically.json'] = JSON.stringify(magicallyManifest, null, 2);

    return filesMap;
}



// Helper function to create a zip buffer from files
export async function createZipFromFiles(files: Record<string, string | Buffer>): Promise<Buffer> {
    return new Promise((resolve, reject) => {
        const archive = archiver('zip', {zlib: {level: 9}});
        const chunks: any[] = [];

        archive.on('error', (err) => reject(err));
        archive.on('data', (chunk) => chunks.push(chunk));
        archive.on('end', () => resolve(Buffer.concat(chunks)));

        // Add each file to the archive
        for (const [filePath, content] of Object.entries(files)) {
            archive.append(content, {name: filePath});
        }

        archive.finalize();
    });
}

// Helper function to recursively list and fetch all repository contents
export async function fetchAllRepoContents(path: string = ''): Promise<Record<string, string | Buffer<ArrayBufferLike>>> {
    if (!process.env.GITHUB_PAT) {
        throw new Error('GitHub Personal Access Token not configured');
    }

    const response = await fetch(
        `https://api.github.com/repos/magically-life/magically-templates/contents/${path}`,
        {
            headers: {
                'Accept': 'application/vnd.github.v3+json',
                'Authorization': `token ${process.env.GITHUB_PAT}`,
                'X-GitHub-Api-Version': '2022-11-28'
            }
        }
    );

    if (!response.ok) {
        throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
    }

    const contents = await response.json();
    const files: Record<string, string | Buffer<ArrayBufferLike>> = {};

    for (const item of contents) {
        if (item.type === 'file') {
            const fileResponse = await fetch(item.download_url, {
                headers: {
                    'Authorization': `token ${process.env.GITHUB_PAT}`,
                }
            });
            if (!fileResponse.ok) {
                throw new Error(`Failed to fetch file ${item.path}: ${fileResponse.status} ${fileResponse.statusText}`);
            }
            // Remove the base-v1/ prefix from paths
            const relativePath = item.path.replace('base-v1/', '');
            // Check if file is binary (images, etc.)
            const isBinary = /\.(png|jpg|jpeg|gif|ico|webp|bmp|tiff|svg)$/i.test(item.path);

            if (isBinary) {
                // Handle binary files as ArrayBuffer
                const arrayBuffer = await fileResponse.arrayBuffer();
                files[relativePath] = Buffer.from(arrayBuffer);
            } else {
                // Handle text files as before
                files[relativePath] = await fileResponse.text();
            }
        } else if (item.type === 'dir') {
            const subFiles = await fetchAllRepoContents(item.path);
            Object.assign(files, subFiles);
        }
    }

    return files;
}

/**
 * Helper function to create app.json for EAS build
 */
export function createAppJson(appName: string = 'Magically App', bundleId: string = 'life.magically.app', version: string = '1.0.0', buildNumber: string = '1') {
    return JSON.stringify({
        expo: {
            name: appName,
            slug: appName.toLowerCase().replace(/\s+/g, '-'),
            version: version,
            orientation: 'portrait',
            icon: './assets/icon.png',
            userInterfaceStyle: 'light',
            splash: {
                image: './assets/splash.png',
                resizeMode: 'contain',
                backgroundColor: '#ffffff'
            },
            assetBundlePatterns: [
                '**/*'
            ],
            ios: {
                supportsTablet: true,
                bundleIdentifier: bundleId,
                buildNumber: buildNumber
            },
            android: {
                adaptiveIcon: {
                    foregroundImage: './assets/adaptive-icon.png',
                    backgroundColor: '#ffffff'
                },
                package: bundleId
            },
            web: {
                favicon: './assets/favicon.png'
            },
            runtimeVersion: {
                policy: 'appVersion'
            }
        }
    }, null, 2);
}

/**
 * Helper function to create eas.json for EAS Build configuration
 */
export function createEasJson() {
    return JSON.stringify({
        cli: {
            version: '>= 5.4.0'
        },
        build: {
            "base": {
                "android": {
                    "image": "latest",
                    resourceClass: 'large',
                    "buildType": "apk"
                },
            },
            development: {
                developmentClient: true,
                distribution: 'internal',
                ios: {
                    resourceClass: 'large'
                }
            },
            preview: {
                distribution: 'internal',
                ios: {
                    resourceClass: 'large',
                    simulator: true
                }
            },
            production: {
                ios: {
                    resourceClass: 'large'
                }
            }
        },
        submit: {
            production: {
                ios: {}
            }
        }
    }, null, 2);
}

/**
 * Helper function to prepare project for build
 */
export async function prepareProjectForBuild(sandbox: Sandbox, options: {
    files: FileItem[];
    appJsonContent: string;
    easJsonContent: string;
    dependencies: Record<string, { version: string }>;
    projectDir: string;
    onFileUploaded?: () => void;
}) {
    const { files, appJsonContent, easJsonContent, dependencies, projectDir } = options;

    try {
        // Create project directory
        await sandbox.files.makeDir(projectDir);

        // Prepare files for bulk upload
        const filesToUpload: any[] = [];

        // Add all project files
        for (const file of files) {
            // Add the file to the bulk upload list
            filesToUpload.push({
                path: `${projectDir}/src/${file.name}`,
                data: file.content
            });
            
            // Notify about file upload if callback provided
            if (options.onFileUploaded) {
                options.onFileUploaded();
            }

            // Track directories that need to be created
            const dirPath = `${projectDir}/${file.name}`.substring(0, `${projectDir}/${file.name}`.lastIndexOf('/'));
            if (dirPath !== projectDir) {
                // Create directory structure as needed
                // Create each directory in the path if needed
                const pathParts = dirPath.split('/');
                let currentPath = '';

                for (const part of pathParts) {
                    if (!part) continue; // Skip empty parts
                    currentPath += '/' + 'src/' + part;
                    try {
                        await sandbox.files.makeDir(`${currentPath}`);
                    } catch (error) {
                        // Directory might already exist, continue
                        console.log(`Directory ${currentPath} might already exist, continuing...`);
                    }
                }
            }
        }

        // // Add configuration files
        // filesToUpload.push({
        //     path: `${projectDir}/app.json`,
        //     data: appJsonContent
        // });
        //
        filesToUpload.push({
            path: `${projectDir}/eas.json`,
            data: easJsonContent
        });

        filesToUpload.push({
            path: `${projectDir}/scripts/fast-apk-build.js`,
            data: script
        })

        console.log('filesToUpload', filesToUpload)

        // Bulk upload all files at once
        console.log(`Bulk uploading ${filesToUpload.length} files to sandbox...`);
        await sandbox.files.write(filesToUpload);
        console.log('Bulk file upload completed successfully');

        // Create or update package.json with dependencies
        let packageJson: any = {};

        try {
            // Check if package.json already exists from uploaded files
            const packageJsonCheck = await sandbox.commands.run(`test -f ${projectDir}/package.json && cat ${projectDir}/package.json || echo '{}'`);
            if (packageJsonCheck.stdout.trim() !== '{}') {
                packageJson = JSON.parse(packageJsonCheck.stdout);
            } else {
                // Create basic package.json if it doesn't exist
                packageJson = {
                    name: 'magically-app',
                    version: '1.0.0',
                    main: 'index.js',
                    scripts: {
                        start: 'expo start',
                        android: 'expo start --android',
                        ios: 'expo start --ios',
                        web: 'expo start --web',
                        build: 'eas build --platform ios --profile preview'
                    }
                };
            }

            // Add dependencies
            packageJson.dependencies = packageJson.dependencies || {};
            packageJson.devDependencies = packageJson.devDependencies || {};

            // Merge dependencies from the provided dependencies object
            for (const [name, { version }] of Object.entries(dependencies)) {
                packageJson.dependencies[name] = version;
            }

            // Ensure essential dependencies are included
            const essentialDeps = {
                'react': '^18.2.0',
                'react-native': '0.6.9',
                'expo': 'latest',
                "react-native-screens": "^4.4.0"
            };

            for (const [name, version] of Object.entries(essentialDeps)) {
                if (!packageJson.dependencies[name]) {
                    packageJson.dependencies[name] = version;
                }
            }

            // Ensure development dependencies
            const devDeps = {
                '@types/react': '^18.2.14',
                'typescript': '^5.1.6',
                'eas-cli': '^5.4.0'
            };

            for (const [name, version] of Object.entries(devDeps)) {
                if (!packageJson.devDependencies[name]) {
                    packageJson.devDependencies[name] = version;
                }
            }

            // Write updated package.json
            await sandbox.files.write([
                {
                    path: `${projectDir}/package.json`,
                    data: JSON.stringify(packageJson),
                }
            ]);
            console.log('Updated package.json with dependencies');

        } catch (error) {
            console.error('Error processing package.json:', error);
            throw error;
        }

        return true;
    } catch (error) {
        console.error('Error preparing project for build:', error);
        throw error;
    }
}

/**
 * Helper function to upload files to sandbox
 * @param sandbox - The sandbox instance
 * @param chatId - The chat ID to get files from
 * @param progressCallback - Optional callback for progress updates
 */
export async function uploadFilesToSandbox(
    sandbox: Sandbox, 
    chatId: string,
    progressCallback?: (progress: number, status: string) => void
) {
    try {
        // Report progress
        progressCallback?.(5, 'Creating project directories...');
        
        // Create project directory
        await sandbox.files.makeDir(`${PROJECT_DIR}/assets`);

        // Fetch the latest file state from the database
        progressCallback?.(10, 'Fetching project files...');
        console.log(`Fetching latest file state for chat ID: ${chatId}`);
        const fileState = await getLatestFileState(chatId);

        if (!fileState) {
            console.error(`No file state found for chat ID: ${chatId}`);
            progressCallback?.(100, 'Error: No files found');
            return false;
        }

        console.log(`Found file state with ID: ${fileState.id}`);
        progressCallback?.(20, 'Files found, preparing configuration...');

        // Create configuration files
        const appJsonContent = createAppJson();
        const easJsonContent = createEasJson();

        // Parse files and dependencies from file state
        const files = (fileState.files as FileItem[]).filter(f => !!f.name);
        const dependencies = fileState.dependencies as Record<string, { version: string }>;

        console.log(`Uploading ${files.length} files to sandbox`);
        progressCallback?.(30, `Preparing to upload ${files.length} files...`);

        // Track progress for file uploads
        let uploadedFiles = 0;
        const totalFiles = files.length;
        
        // Use the prepareProjectForBuild function to set up the project
        await prepareProjectForBuild(sandbox, {
            files,
            appJsonContent,
            easJsonContent,
            dependencies,
            projectDir: PROJECT_DIR,
            onFileUploaded: () => {
                uploadedFiles++;
                const fileProgress = Math.min(30 + Math.floor((uploadedFiles / totalFiles) * 50), 80);
                progressCallback?.(fileProgress, `Uploaded ${uploadedFiles}/${totalFiles} files...`);
            }
        });

        // Install required tools
        // console.log('Installing EAS CLI and yarn...');
        // await sandbox.commands.run('npm install -g eas-cli yarn', { timeoutMs: 120000 });

        // Initialize git repository
        // console.log('Initializing git repository...');
        // await sandbox.commands.run(`git init && git config --global user.email "<EMAIL>" && git config --global user.name "Example User" && git add . && git commit -m "Initial commit"`, { timeoutMs: 30000, cwd: PROJECT_DIR });

        progressCallback?.(90, 'Finalizing project setup...');
        console.log('Project setup completed successfully');
        progressCallback?.(100, 'Project setup completed successfully!');
        return true;
    } catch (error) {
        console.error('Error setting up project:', JSON.stringify(error));
        return false;
    }
}

/**
 * Helper function to handle terminal commands for existing sessions
 */
export async function handleTerminalCommand(
    session: { sandbox: Sandbox; terminalId: number | null },
    command: string
) {
    try {
        if(!session.terminalId) {
            throw new Error("No terminalId provided to handleTerminalCommand");
        }
        console.log(`Executing command in terminal ${session.terminalId}: ${command}`);

        // Send the command to the terminal
        await session.sandbox.pty.sendInput(
            session.terminalId,
            new TextEncoder().encode(command + '\n'),
            {
                requestTimeoutMs: 0
            }
        );

        return true;
    } catch (error) {
        console.error(`Error executing command in terminal ${session.terminalId}:`, error);
        return false;
    }
}

/**
 *
 * @param session
 * @param options
 * Helper function to create a new pty terminal for the sandbox
 */
export async function createPty(session: { sandbox: Sandbox; },
                                options: { cwd?: string, onData?: (data: string) => any }): Promise<CommandHandle> {
    try {
        // Log that we're creating a PTY
        console.log('Creating PTY in sandbox...');
        
        // Create the PTY with a shell that's likely to be available in the sandbox
        // The e2b sandbox should automatically use an available shell
        return session.sandbox.pty.create({
            cols: 80,  // Set reasonable default column width
            rows: 24,  // Set reasonable default row height
            cwd: options?.cwd,
            requestTimeoutMs: 1000 * 60 * 10,
            timeoutMs: 1000 * 60 * 10,
            user: "root",
            onData: (data: Uint8Array) => {
                if (options?.onData) {
                    options?.onData(new TextDecoder().decode(data));
                }
            }
        });
    
    } catch (e) {
        console.log('Error creating pty:', JSON.stringify(e));
        throw e;
    }
}
