import { Sandbox } from '@e2b/code-interpreter';
import { handleTerminalCommand } from './helpers';

/**
 * APK build commands for the terminal
 * These commands integrate with the unified-apk-build.js script
 */

/**
 * Build an APK using the fast method (template + JS bundle)
 * 
 * @param session - The sandbox session
 * @param options - Build options
 * @returns Promise<boolean> - Whether the build was successful
 */
export async function buildApkFast(
  session: { sandbox: Sandbox; terminalId: number | null },
  options: {
    dev?: boolean;
    useHermes?: boolean;
    outputPath?: string;
  } = {}
) {
  try {
    const optionsString = [
      options.dev ? '--dev' : '',
      options.useHermes === false ? '--no-hermes' : '',
      options.outputPath ? `--output=${options.outputPath}` : '',
    ].filter(Boolean).join(' ');

    const command = `node /project/scripts/unified-apk-build.js fast ${optionsString}`;
    return await handleTerminalCommand(session, command);
  } catch (error) {
    console.error('Error building APK:', error);
    return false;
  }
}

/**
 * Build an APK using the full method (complete build)
 * 
 * @param session - The sandbox session
 * @param options - Build options
 * @returns Promise<boolean> - Whether the build was successful
 */
export async function buildApkFull(
  session: { sandbox: Sandbox; terminalId: number | null },
  options: {
    dev?: boolean;
    outputPath?: string;
  } = {}
) {
  try {
    const optionsString = [
      options.dev ? '--dev' : '',
      options.outputPath ? `--output=${options.outputPath}` : '',
    ].filter(Boolean).join(' ');

    const command = `node /project/scripts/unified-apk-build.js full ${optionsString}`;
    return await handleTerminalCommand(session, command);
  } catch (error) {
    console.error('Error building APK:', error);
    return false;
  }
}

/**
 * Install an APK on a connected device or emulator
 * 
 * @param session - The sandbox session
 * @param apkPath - Path to the APK to install
 * @returns Promise<boolean> - Whether the installation was successful
 */
export async function installApk(
  session: { sandbox: Sandbox; terminalId: number | null },
  apkPath?: string
) {
  try {
    const command = apkPath 
      ? `node /project/scripts/unified-apk-build.js install ${apkPath}`
      : `node /project/scripts/unified-apk-build.js install`;
    
    return await handleTerminalCommand(session, command);
  } catch (error) {
    console.error('Error installing APK:', error);
    return false;
  }
}

/**
 * Run a complete APK build and install process
 * 
 * @param session - The sandbox session
 * @param options - Build options
 * @returns Promise<boolean> - Whether the process was successful
 */
export async function buildAndInstallApk(
  session: { sandbox: Sandbox; terminalId: number | null },
  options: {
    method?: 'fast' | 'full';
    dev?: boolean;
    useHermes?: boolean;
    outputPath?: string;
  } = {}
) {
  try {
    const { method = 'fast' } = options;
    
    // Build the APK
    const buildSuccess = method === 'fast'
      ? await buildApkFast(session, options)
      : await buildApkFull(session, options);
    
    if (!buildSuccess) {
      return false;
    }
    
    // Install the APK
    const outputPath = options.outputPath || '/project/build';
    const apkName = options.dev ? 'app-debug.apk' : 'app-release.apk';
    const apkPath = `${outputPath}/${apkName}`;
    
    return await installApk(session, apkPath);
  } catch (error) {
    console.error('Error building and installing APK:', error);
    return false;
  }
}

/**
 * Fix FlatList/ScrollView rendering issues in the APK
 * This adds removeClippedSubviews={false} to all FlatList and ScrollView components
 * 
 * @param session - The sandbox session
 * @returns Promise<boolean> - Whether the fix was successful
 */
export async function fixRenderingIssues(
  session: { sandbox: Sandbox; terminalId: number | null }
) {
  try {
    // Find all files with FlatList or ScrollView components
    const findCommand = `find /project/src -type f -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "FlatList\\|ScrollView"`;
    await handleTerminalCommand(session, findCommand);
    
    // Add a script to fix the rendering issues
    const fixScript = `
cat > /project/scripts/fix-rendering.js << 'EOL'
#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Find all files with FlatList or ScrollView
const files = execSync('find /project/src -type f -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs grep -l "FlatList\\\\|ScrollView"')
  .toString()
  .trim()
  .split('\\n')
  .filter(Boolean);

console.log(\`Found \${files.length} files with FlatList or ScrollView components\`);

let modifiedFiles = 0;

files.forEach(file => {
  let content = fs.readFileSync(file, 'utf8');
  
  // Fix FlatList components
  const flatListRegex = /<FlatList([^>]*)>/g;
  let modified = false;
  
  if (flatListRegex.test(content)) {
    content = content.replace(flatListRegex, (match, props) => {
      if (!/removeClippedSubviews/.test(props)) {
        return match.replace(/<FlatList/, '<FlatList removeClippedSubviews={false}');
      }
      return match.replace(/removeClippedSubviews=\\{[^}]+\\}/, 'removeClippedSubviews={false}');
    });
    modified = true;
  }
  
  // Fix ScrollView components
  const scrollViewRegex = /<ScrollView([^>]*)>/g;
  
  if (scrollViewRegex.test(content)) {
    content = content.replace(scrollViewRegex, (match, props) => {
      if (!/removeClippedSubviews/.test(props)) {
        return match.replace(/<ScrollView/, '<ScrollView removeClippedSubviews={false}');
      }
      return match.replace(/removeClippedSubviews=\\{[^}]+\\}/, 'removeClippedSubviews={false}');
    });
    modified = true;
  }
  
  if (modified) {
    fs.writeFileSync(file, content);
    modifiedFiles++;
    console.log(\`Modified \${file}\`);
  }
});

console.log(\`Modified \${modifiedFiles} files to fix rendering issues\`);
EOL

chmod +x /project/scripts/fix-rendering.js
    `;
    
    await handleTerminalCommand(session, fixScript);
    
    // Run the fix script
    return await handleTerminalCommand(session, 'node /project/scripts/fix-rendering.js');
  } catch (error) {
    console.error('Error fixing rendering issues:', error);
    return false;
  }
}
