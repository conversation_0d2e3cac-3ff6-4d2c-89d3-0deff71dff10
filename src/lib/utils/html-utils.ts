/**
 * Utility functions for handling HTML content in design screens
 */

// State variables for tracking screen content across calls
let partialScreenContent = '';
let isCapturingScreen = false;
let screenName = '';

// Note: Use resetHtmlExtractor() instead of this function
// Keeping this for backward compatibility
export function resetHtmlExtractionState() {
  return resetHtmlExtractor();
}

/**
 * Extracts clean HTML content from an AI response
 * Handles various formats including markdown code blocks and screen tags
 * Supports partial/chunked content by tracking state between calls
 */
export function extractHtmlContent(content: string): string | null {
  // Check for screen tag opening
  if (!isCapturingScreen) {
    const screenStartMatch = content.match(/<screen([^>]*)>/i);
    if (screenStartMatch) {
      isCapturingScreen = true;
      
      // Try to extract screen name if available
      const nameMatch = screenStartMatch[1]?.match(/name=["']([^"']*)["']/i);
      if (nameMatch && nameMatch[1]) {
        screenName = nameMatch[1];
      }
      
      // Get content after the opening tag
      const startIndex = content.indexOf(screenStartMatch[0]) + screenStartMatch[0].length;
      partialScreenContent = content.substring(startIndex);
    }
  } else {
    // Already capturing, append new content
    partialScreenContent += content;
  }
  
  // Check if we have a closing tag while capturing
  if (isCapturingScreen) {
    const closeTagIndex = partialScreenContent.indexOf('</screen>');
    if (closeTagIndex !== -1) {
      // We found the closing tag, extract the complete content
      const extractedContent = partialScreenContent.substring(0, closeTagIndex).trim();
      
      // Reset state
      isCapturingScreen = false;
      partialScreenContent = '';
      screenName = '';
      
      return extractedContent;
    }
    
    // Still capturing but no closing tag yet
    return partialScreenContent;
  }
  
  // Fallback to traditional extraction methods for non-chunked content
  
  // Try to extract content from <screen> tags first
  const screenTagMatch = content.match(/<screen[^>]*>([\s\S]*?)<\/screen>/i);
  if (screenTagMatch && screenTagMatch[1]) {
    return screenTagMatch[1].trim();
  }
  
  // Try to extract content from <screens> tags
  const screensTagMatch = content.match(/<screens>([\s\S]*?)<\/screens>/i);
  if (screensTagMatch && screensTagMatch[1]) {
    return screensTagMatch[1].trim();
  }

  // Try to extract from markdown code blocks
  const markdownMatch = content.match(/```html\s*([\s\S]*?)\s*```/);
  if (markdownMatch && markdownMatch[1]) {
    return markdownMatch[1].trim();
  }
  
  // If we have a complete HTML document, return it
  if (content.trim().startsWith('<') && content.trim().endsWith('>')) {
    return content.trim();
  }
  
  // If no specific format is detected but content contains HTML tags, return it
  if (content.includes('<div') || content.includes('<span') || content.includes('<p')) {
    return content;
  }
  
  // No valid HTML content found
  return null;
}

/**
 * Resets the state of the chunked parser
 * Call this when you want to start fresh with a new screen extraction
 */
export function resetHtmlExtractor(): void {
  partialScreenContent = '';
  isCapturingScreen = false;
  screenName = '';
}


export function wrapScreenInCode(screenCode: string) {
  return `<\!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport"       content="width=device-width, initial-scale=1.0, maximum-scale=1.0, viewport-fit=cover">
<meta http-equiv="Content-Security-Policy" content="default-src 'self' https: http:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:; style-src 'self' 'unsafe-inline' https: http:; img-src 'self' data: blob: https: http:; font-src 'self' data: https: http:; media-src 'self' data: blob: https: http:; connect-src 'self' https: http:;">
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
<script>
  window.onload = function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
      lucide.createIcons();
      console.log('Lucide icons initialized');
    } else {
      console.error('Lucide library not loaded properly');
    }
    
    // Simple debounce function to prevent multiple rapid clicks
    let lastClickTime = 0;
    const CLICK_DEBOUNCE_MS = 500; // Minimum time between clicks (500ms)
    
    // Track if we've already sent a click event
    let clickEventSent = false;
    
    // Add click event listener to capture clicks and forward to parent
    document.addEventListener('click', function(event) {
      const now = Date.now();
      
      // If we've already sent a click or if it's too soon after the last click, ignore
      if (clickEventSent || (now - lastClickTime < CLICK_DEBOUNCE_MS)) {
        return;
      }
      
      // Update last click time
      lastClickTime = now;
      
      // Mark that we've sent a click event (only send one per page load)
      clickEventSent = true;
      
      // Get information about the clicked element
      const target = event.target;
      const tagName = target.tagName.toLowerCase();
      const className = target.className;
      const id = target.id;
      const text = target.innerText || target.textContent;
      const rect = target.getBoundingClientRect();
      
      // Create a simplified representation of the clicked element
      const clickData = {
        type: 'screen-click',
        element: {
          tagName,
          className,
          id,
          text: text ? text.substring(0, 50) : '',
          position: {
            x: rect.left + window.scrollX,
            y: rect.top + window.scrollY,
            width: rect.width,
            height: rect.height
          }
        },
        screenId: window.location.pathname.split('/').pop(), // Try to extract screen ID from URL
        timestamp: now
      };
      
      // Send message to parent window
      try {
        window.parent.postMessage(clickData, '*');
        console.log('Click forwarded to parent:', clickData);
      } catch (err) {
        console.error('Failed to forward click to parent:', err);
      }
    });
  };
</script>

<script>

document.addEventListener('DOMContentLoaded', function() {
  const buttons = document.querySelectorAll('*');
  
  buttons.forEach(button => {
    button.addEventListener('click', function() {
      this.style.opacity = '0.6';
      setTimeout(() => {
        this.style.opacity = '1';
        showNotification();
      }, 150);
    });
  });

  function showNotification() {
    const notification = document.createElement('div');
    notification.innerHTML = 'This is a design preview. You can chat to change the designs. Review the design and the flow and then click <b style="color:#16a34a;">"Start building app"</b> to build your actual mobile app.';
    notification.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.6);color:white;padding:16px;border-radius:8px;z-index:1000;font-size:18px;width:75%;';
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 2000);
  }
});
</script>
</head>
<body class="overflow-x-hidden">
${screenCode}
</body>
</html>`
}