import { TargetedValidator } from '../services/targeted-validator';
import { ExpoValidator } from '../services/expo-validator';
import { FileItem } from '@/types/file';

/**
 * Validates all AI-generated files for Expo compatibility
 * @param files Array of file objects to validate
 * @param dataStream Optional data stream to send validation results to the client
 * @param useExactRuntimeValidator If true, uses the exact Expo Snack runtime validator that only reports the first error
 * @returns Validation results
 */
export async function validateExpoFiles(
  files: FileItem[],
  dataStream?: { writeData: (data: any) => void },
  useExactRuntimeValidator: boolean = false
) {
  // Convert FileItems to the format expected by validators
  const filesToValidate = files.map(file => ({
    name: file.name,
    content: file.content
  }));

  try {
    let validationResults;
    
    if (useExactRuntimeValidator) {
      // Use the exact Expo Snack runtime validator that only reports the first error
      validationResults = ExpoValidator.validateFiles(filesToValidate);
      
      // Convert to a format compatible with the existing API
      if (!validationResults.isValid && validationResults.error) {
        const fileResults: Record<string, any> = {};
        const errorFile = validationResults.error.sourceFile || 'unknown';
        
        fileResults[errorFile] = {
          isValid: false,
          errors: [{
            line: validationResults.error.line || 1,
            column: validationResults.error.column || 1,
            message: validationResults.error.message,
            severity: 'error'
          }]
        };
        
        // validationResults.fileResults = fileResults;
      } else {
        // validationResults.fileResults = {};
      }
    } else {
      // Use the comprehensive validator that reports all errors
      validationResults = TargetedValidator.validateFiles(filesToValidate);
    }
    
    // If a data stream is provided, send the validation results to the client
    if (dataStream && !validationResults.isValid) {
      dataStream.writeData({
        type: 'validation-results',
        content: {
          isValid: validationResults.isValid,
          summary: validationResults.summary,
          details: {} as any
        }
      });
    }

    // Log validation issues for debugging
    if (!validationResults.isValid) {
      console.log('Expo validation issues found:');
      console.log(validationResults.summary);
    }

    return validationResults;
  } catch (error: any) {
    console.error('Error validating Expo files:', error);
    
    // If a data stream is provided, send the error to the client
    if (dataStream) {
      dataStream.writeData({
        type: 'validation-error',
        content: {
          message: `Failed to validate files: ${error.message}`
        }
      });
    }
    
    return {
      isValid: false,
      summary: `Error during validation: ${error.message}`,
      fileResults: {}
    };
  }
}

/**
 * Attempts to fix common issues in Expo files
 * @param files Array of file objects to fix
 * @returns Fixed files or original files if fixing failed
 */
export async function attemptToFixExpoFiles(files: FileItem[]): Promise<FileItem[]> {
  // For now, just return the original files
  // In a real implementation, you could use AI to fix the detected issues
  return files;
}
