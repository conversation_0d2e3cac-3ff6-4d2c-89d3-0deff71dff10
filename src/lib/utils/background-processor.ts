import { v4 as uuidv4 } from 'uuid';

/**
 * Job status types
 */
export type JobStatus = 'pending' | 'running' | 'completed' | 'failed';

/**
 * Job storage interface
 */
export interface JobStorage<T = any> {
  set(jobId: string, data: T): Promise<void>;
  get(jobId: string): Promise<T | null>;
  delete(jobId: string): Promise<void>;
  update(jobId: string, data: Partial<T>): Promise<void>;
}

/**
 * Job data structure
 */
export interface Job<T = any, R = any> {
  id: string;
  status: JobStatus;
  data: T;
  result?: R;
  error?: Error;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

/**
 * In-memory job storage implementation
 */
class InMemoryJobStorage implements JobStorage {
  private storage: Map<string, any> = new Map();

  async set(jobId: string, data: any): Promise<void> {
    this.storage.set(jobId, data);
  }

  async get(jobId: string): Promise<any | null> {
    return this.storage.get(jobId) || null;
  }

  async delete(jobId: string): Promise<void> {
    this.storage.delete(jobId);
  }

  async update(jobId: string, data: Partial<any>): Promise<void> {
    const existingData = this.storage.get(jobId);
    if (existingData) {
      this.storage.set(jobId, { ...existingData, ...data });
    }
  }
}

/**
 * Default job storage instance
 */
const defaultStorage = new InMemoryJobStorage();

/**
 * Background processor options
 */
export interface BackgroundProcessorOptions<T = any> {
  storage?: JobStorage<Job<T>>;
  retainCompletedJobs?: boolean;
}

/**
 * Background processor class
 * Handles running jobs in the background while allowing immediate response
 */
export class BackgroundProcessor<T = any, R = any> {
  private storage: JobStorage<Job<T>>;
  private retainCompletedJobs: boolean;

  constructor(options: BackgroundProcessorOptions<T> = {}) {
    this.storage = options.storage || defaultStorage;
    this.retainCompletedJobs = options.retainCompletedJobs || false;
  }

  /**
   * Run a job in the background
   * @param jobFn Function that returns a promise to be executed
   * @param data Optional data to associate with the job
   * @returns Job ID that can be used to check status and get results
   */
  async run(jobFn: (data: T) => Promise<R>, data: T): Promise<string> {
    const jobId = uuidv4();
    const job: Job<T> = {
      id: jobId,
      status: 'pending',
      data,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store the initial job
    await this.storage.set(jobId, job);

    // Run the job in the background
    this.executeJob(jobId, jobFn, data).catch(console.error);

    return jobId;
  }

  /**
   * Execute a job and update its status
   */
  private async executeJob(jobId: string, jobFn: (data: T) => Promise<R>, data: T): Promise<void> {
    try {
      // Update job status to running
      await this.storage.update(jobId, {
        status: 'running',
        updatedAt: new Date(),
      });

      // Execute the job
      const result = await jobFn(data);

      // Update job status to completed
      await this.storage.update(jobId, {
        status: 'completed',
        result,
        updatedAt: new Date(),
        completedAt: new Date(),
      });

      // Clean up if not retaining completed jobs
      if (!this.retainCompletedJobs) {
        setTimeout(() => {
          this.storage.delete(jobId).catch(console.error);
        }, 5000); // Delete after 5 seconds to allow for result retrieval
      }
    } catch (error) {
      // Update job status to failed
      await this.storage.update(jobId, {
        status: 'failed',
        error: error instanceof Error ? error : new Error(String(error)),
        updatedAt: new Date(),
      });
    }
  }

  /**
   * Get the current status of a job
   * @param jobId Job ID to check
   * @returns Job status and data
   */
  async getStatus(jobId: string): Promise<Job<T, R> | null> {
    return this.storage.get(jobId);
  }

  /**
   * Wait for a job to complete
   * @param jobId Job ID to wait for
   * @param timeoutMs Optional timeout in milliseconds
   * @returns Job result or throws an error if job failed or timed out
   */
  async waitForCompletion(jobId: string, timeoutMs = 30000): Promise<R> {
    return new Promise(async (resolve, reject) => {
      const startTime = Date.now();
      
      const checkStatus = async () => {
        const job = await this.storage.get(jobId);
        
        if (!job) {
          reject(new Error(`Job ${jobId} not found`));
          return;
        }
        
        if (job.status === 'completed') {
          resolve(job.result as R);
          return;
        }
        
        if (job.status === 'failed') {
          reject(job.error || new Error('Job failed'));
          return;
        }
        
        // Check for timeout
        if (Date.now() - startTime > timeoutMs) {
          reject(new Error(`Job ${jobId} timed out after ${timeoutMs}ms`));
          return;
        }
        
        // Check again after a short delay
        setTimeout(checkStatus, 100);
      };
      
      checkStatus();
    });
  }
}

/**
 * Create a new background processor with optimistic execution
 * This allows immediate streaming while background tasks complete
 */
export function createOptimisticProcessor<T = any, R = any>(
  options?: BackgroundProcessorOptions<T>
): BackgroundProcessor<T, R> {
  return new BackgroundProcessor<T, R>(options);
}

/**
 * Helper function to run a job and get its ID for later checking
 */
export async function runInBackground<T = any, R = any>(
  jobFn: (data: T) => Promise<R>,
  data: T,
  options?: BackgroundProcessorOptions<T>
): Promise<string> {
  const processor = new BackgroundProcessor<T, R>(options);
  return processor.run(jobFn, data);
}

/**
 * Helper function to run multiple jobs in parallel and wait for all to complete
 */
export async function runAllInBackground<T = any, R = any>(
  jobFns: Array<(data: T) => Promise<R>>,
  data: T,
  options?: BackgroundProcessorOptions<T>
): Promise<string[]> {
  const processor = new BackgroundProcessor<T, R>(options);
  const jobIds = await Promise.all(jobFns.map(fn => processor.run(fn, data)));
  return jobIds;
}
