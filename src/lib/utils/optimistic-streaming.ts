import { BackgroundProcessor, createOptimisticProcessor } from './background-processor';
import {DataStreamWriter} from "ai";

/**
 * Interface for optimistic streaming context
 */
export interface OptimisticContext<T = any> {
  // Background processor instance
  processor: BackgroundProcessor<T>;
  // Map of job IDs to track running jobs
  jobIds: Map<string, string>;
  // Data that can be accessed by streaming functions
  data: Record<string, any>;
  // Flag to indicate if all required checks have passed
  checksCompleted: boolean;
  // Flag to indicate if streaming should continue
  shouldContinue: boolean;
}

/**
 * Creates an optimistic streaming context that allows immediate streaming
 * while background tasks complete
 */
export function createOptimisticContext<T = any>(): OptimisticContext<T> {
  return {
    processor: createOptimisticProcessor<T>(),
    jobIds: new Map(),
    data: {},
    checksCompleted: false,
    shouldContinue: true
  };
}

/**
 * Runs a job in the background and stores its ID in the context
 */
export async function runOptimisticJob<T = any, R = any>(
  context: OptimisticContext<T>,
  key: string,
  jobFn: (data: T) => Promise<R>,
  data: T
): Promise<string> {
  const jobId = await context.processor.run(jobFn, data);
  context.jobIds.set(key, jobId);
  return jobId;
}

/**
 * Waits for a specific job to complete
 */
export async function waitForJob<R = any>(
  context: OptimisticContext,
  key: string,
  timeoutMs = 30000
): Promise<R | null> {
  const jobId = context.jobIds.get(key);
  if (!jobId) return null;
  
  try {
    return await context.processor.waitForCompletion(jobId, timeoutMs);
  } catch (error) {
    console.error(`Error waiting for job ${key}:`, error);
    return null;
  }
}

/**
 * Waits for all jobs to complete
 */
export async function waitForAllJobs(
  context: OptimisticContext,
  timeoutMs = 30000
): Promise<boolean> {
  const jobIds = Array.from(context.jobIds.values());
  
  try {
    await Promise.all(
      jobIds.map(jobId => 
        context.processor.waitForCompletion(jobId, timeoutMs)
      )
    );
    return true;
  } catch (error) {
    console.error('Error waiting for all jobs:', error);
    return false;
  }
}

/**
 * Creates a streaming function that uses optimistic background processing
 */
export function createOptimisticStream<T = any>({
  dataStream,
  context = createOptimisticContext<T>(),
  backgroundJobs = [],
  streamingFn,
  onComplete,
  onError
}: {
  dataStream: DataStreamWriter;
  context?: OptimisticContext<T>;
  backgroundJobs?: Array<{
    key: string;
    fn: (data: T) => Promise<any>;
    data: T;
  }>;
  streamingFn: (context: OptimisticContext<T>) => Promise<void>;
  onComplete?: (context: OptimisticContext<T>) => Promise<void>;
  onError?: (error: Error, context: OptimisticContext<T>) => Promise<void>;
}) {
  return async () => {
    try {
      // Start all background jobs
      await Promise.all(
        backgroundJobs.map(job => 
          runOptimisticJob(context, job.key, job.fn, job.data)
        )
      );
      
      // Start streaming immediately
      await streamingFn(context);
      
      // Wait for all jobs to complete
      await waitForAllJobs(context);
      
      // Call onComplete callback if provided
      if (onComplete) {
        await onComplete(context);
      }
    } catch (error) {
      console.error('Error in optimistic stream:', error);
      
      // Call onError callback if provided
      if (onError && error instanceof Error) {
        await onError(error, context);
      }
      
      // Send error to client
      dataStream.writeData({
        type: 'error',
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      // Always close the stream
      // dataStream.close();
    }
  };
}

/**
 * Example usage with user limits check
 */
export function createOptimisticStreamWithLimits<T = any>({
  dataStream,
  userId,
  checkUserLimits,
  streamingFn,
  onComplete
}: {
  dataStream: DataStreamWriter;
  userId: string;
  checkUserLimits: (userId: string) => Promise<boolean>;
  streamingFn: (context: OptimisticContext<T>) => Promise<void>;
  onComplete?: (context: OptimisticContext<T>) => Promise<void>;
}) {
  const context = createOptimisticContext<T>();
  
  return createOptimisticStream({
    dataStream,
    context,
    backgroundJobs: [
      {
        key: 'userLimits',
        fn: async () => {
          const hasLimits = await checkUserLimits(userId);
          context.data.hasLimits = hasLimits;
          context.shouldContinue = hasLimits;
          context.checksCompleted = true;
          return hasLimits;
        },
        data: {} as T
      }
    ],
    streamingFn: async (ctx) => {
      // Start streaming immediately
      // If limits check fails later, we'll handle it
      await streamingFn(ctx);
      
      // Check if limits were exceeded after streaming starts
      if (ctx.checksCompleted && !ctx.shouldContinue) {
        dataStream.writeData({
          type: 'error',
          error: 'User limits exceeded'
        });
      }
    },
    onComplete
  });
}
