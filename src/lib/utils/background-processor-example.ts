import { createOptimisticStream, createOptimisticContext } from './optimistic-streaming';
import { getUserById } from '@/lib/db/queries';
import {CoreMessage, createDataStreamResponse, DataStreamWriter, streamText} from 'ai';
import {customModel} from "@/lib/ai";
import {DEFAULT_MODEL_NAME} from "@/lib/ai/models";

/**
 * Example of using optimistic background processing with the chat API
 */
export async function optimisticChatExample(
  userId: string,
  messages: CoreMessage[],
  dataStream: DataStreamWriter
) {
  // Create optimistic context
  const context = createOptimisticContext();
  
  // Define the streaming function
  const streamingFunction = async (ctx: any) => {
    // Start streaming immediately while background checks complete
    dataStream.writeData({
      type: 'start',
      message: 'Processing your request...'
    });
    
    // Generate AI response and stream it
    const { text } = streamText({
      messages,
      model: customModel("anthropic/claude-3.7-sonnet"),
      onChunk: (chunk) => {
        if (ctx.checksCompleted && !ctx.shouldContinue) {
          // If user doesn't have enough credits, we could stop here
          // But for this example, we'll continue streaming
          dataStream.writeData({
            type: 'warning',
            message: 'You have limited credits remaining'
          });
        }

        // Stream the chunk to the client
        dataStream.writeData({
          type: 'chunk',
          chunk
        });
      }
    })
    
    // Store the complete response in context for later use
    ctx.data.completeResponse = text;
  };
  
  // Create the optimistic stream with background jobs
  const streamExecutor = createOptimisticStream({
    dataStream,
    context,
    backgroundJobs: [
      // Check user limits
      {
        key: 'userCheck',
        fn: async () => {
          const user: any = await getUserById(userId);
          
          // Store user data in context
          context.data.user = user;
          
          // Check if user has enough credits
          const hasCredits = user && user.credits > 0;
          context.shouldContinue = hasCredits;
          
          return user;
        },
        data: {}
      },
      // Prepare any other background tasks
      {
        key: 'messagePreprocessing',
        fn: async () => {
          // Any preprocessing of messages could happen here
          // For example, checking for harmful content, etc.
          return true;
        },
        data: {}
      }
    ],
    streamingFn: streamingFunction,
    onComplete: async (ctx) => {
      // After streaming is complete and all background jobs are done
      // We can perform final actions like updating user credits
      
      if (ctx.data.user && ctx.shouldContinue) {
        // In a real implementation, this would update the database
        console.log(`Would update user ${userId} credits`);
        
        // Send completion event to client
        dataStream.writeData({
          type: 'complete',
          message: 'Request completed successfully'
        });
      }
    },
    onError: async (error, ctx) => {
      console.error('Error in chat processing:', error);
      
      // Send detailed error to client
      dataStream.writeData({
        type: 'error',
        error: error.message
      });
    }
  });
  
  // Execute the stream
  return streamExecutor();
}

/**
 * Example of how to use the background processor directly
 */
export async function directBackgroundProcessorExample() {
  // Import the processor
  const { createOptimisticProcessor, runInBackground } = await import('./background-processor');
  
  // Create a processor instance
  const processor = createOptimisticProcessor();
  
  // Run a job in the background
  const jobId = await processor.run(
    async (data) => {
      // Simulate a time-consuming task
      await new Promise(resolve => setTimeout(resolve, 2000));
      return `Processed data: ${data.value}`;
    },
    { value: 'test-data' }
  );
  
  console.log(`Started background job: ${jobId}`);
  
  // Check job status
  const status = await processor.getStatus(jobId);
  console.log(`Job status: ${status?.status}`);
  
  // Wait for job completion
  try {
    const result = await processor.waitForCompletion(jobId);
    console.log(`Job completed with result: ${result}`);
    return result;
  } catch (error) {
    console.error(`Job failed: ${error}`);
    throw error;
  }
}

/**
 * Example of how this could be integrated with the chat API route
 */
export function chatApiRouteExample() {
  // This would be in your API route handler
  return async (req: any, res: any) => {
    const { userId, messages } = req.body;
    
    // Create a data stream for the response

    return createDataStreamResponse({
      execute: async (dataStream) => {
        // Use the optimistic chat example
        await optimisticChatExample(userId, messages, dataStream);
      }
    });
  };
}
