/**
 * Utility for detecting error-related content in user messages
 * Used to determine if a user is fixing errors (in which case we don't charge credits)
 */

// Strong error indicators - these terms almost always indicate an error fixing scenario
const STRONG_ERROR_INDICATORS = [
  // 'error',
  'exception',
  // 'crash',
  // 'broken',
  // 'failed',
  // 'failure',
  // 'undefined',
  'cannot read property',
  'null is not an object',
  'is not a function',
  'maximum call stack',
  'invariant violation',
  'cannot find variable',
  'unhandled promise rejection',
  'unhandled rejection',
  'syntax error',
  'type error',
  'reference error',
  'runtime error',
  'uncaught exception',
  'unable to resolve module',
  'minified react'
];

// Error context phrases - these need to appear with other terms to indicate an error
const ERROR_CONTEXT_PHRASES: string[] = [];

// Technical terms that need context to be considered error-related
const TECHNICAL_TERMS_NEEDING_CONTEXT: { term: string, contextTerms: string[] }[] = [
  // These terms need to be paired with error context to indicate a problem
  // { term: 'component', contextTerms: ['missing', 'broken', 'error', 'issue', 'problem', 'not rendering', 'failed'] },
  // { term: 'props', contextTerms: ['invalid', 'missing', 'undefined', 'null', 'error', 'issue', 'problem'] },
  // { term: 'state', contextTerms: ['invalid', 'not updating', 'error', 'issue', 'problem'] },
  // { term: 'style', contextTerms: ['not applying', 'wrong', 'incorrect', 'error', 'issue', 'problem'] },
  // { term: 'navigation', contextTerms: ['broken', 'not working', 'error', 'issue', 'problem', 'failed'] },
  // { term: 'screen', contextTerms: ['blank', 'white', 'black', 'empty', 'not loading', 'error', 'issue', 'problem'] },
  // { term: 'layout', contextTerms: ['broken', 'wrong', 'incorrect', 'not working', 'error', 'issue', 'problem'] },
  // { term: 'render', contextTerms: ['failed', 'error', 'issue', 'problem', 'not working'] },
  // { term: 'expo', contextTerms: ['error', 'issue', 'problem', 'failed', 'not working'] },
];

/**
 * Checks if the content contains strong error indicators
 * @param content The content to check
 * @returns True if the content contains strong error indicators
 */
function containsStrongErrorIndicators(content: string): boolean {
  const lowerContent = content.toLowerCase();
  return STRONG_ERROR_INDICATORS.some(term => lowerContent.includes(term));
}

/**
 * Checks if the content contains error context phrases
 * @param content The content to check
 * @returns True if the content contains error context phrases
 */
function containsErrorContextPhrases(content: string): boolean {
  const lowerContent = content.toLowerCase();
  return ERROR_CONTEXT_PHRASES.some(phrase => lowerContent.includes(phrase));
}

/**
 * Checks if the content contains technical terms with error context
 * @param content The content to check
 * @returns True if the content contains technical terms with error context
 */
function containsTechnicalTermsWithContext(content: string): boolean {
  const lowerContent = content.toLowerCase();
  
  return TECHNICAL_TERMS_NEEDING_CONTEXT.some(({ term, contextTerms }) => {
    if (lowerContent.includes(term)) {
      // If the term is found, check if any context terms are also present
      return contextTerms.some(contextTerm => lowerContent.includes(contextTerm));
    }
    return false;
  });
}

/**
 * Checks if the content contains any error-related terms or patterns
 * Uses a multi-layered approach to reduce false positives
 * @param content The content to check
 * @returns True if the content contains error-related terms or patterns
 */
export function containsErrorTerms(content: string): boolean {
  // If content is empty or too short, it's unlikely to be an error message
  if (!content || content.length < 5) return false;
  
  // First check for strong error indicators
  if (containsStrongErrorIndicators(content)) {
    return true;
  }
  
  // Then check for error context phrases
  if (containsErrorContextPhrases(content)) {
    return true;
  }
  
  // Finally check for technical terms with error context
  return containsTechnicalTermsWithContext(content);
}

/**
 * Determines if a message is related to error fixing based on its content
 * Works with both string and array content formats
 * @param messageContent The content of the message (string or array of parts)
 * @returns Object with isErrorFixing flag and errorUuid if found
 */
export function isErrorFixingMessage(messageContent: any): { isErrorFixing: boolean; errorUuid?: string } {
  const errorCodeRegex = /%%Error code: ([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})%%/i;
  let errorUuid: string | undefined;
  let isErrorFixing = false;
  
  // Handle array content format (where content is an array of parts)
  if (Array.isArray(messageContent)) {
    for (const part of messageContent) {
      if (part.type === 'text') {
        // Extract UUID if present - isErrorFixing is true ONLY if this regex is found
        const match = part.text.match(errorCodeRegex);
        if (match && match[1]) {
          errorUuid = match[1];
          isErrorFixing = true;
        }
      }
    }
    
    return { isErrorFixing, errorUuid };
  }
  
  // Handle string content format
  if (typeof messageContent === 'string') {
    // Extract UUID if present - isErrorFixing is true ONLY if this regex is found
    const match = messageContent.match(errorCodeRegex);
    if (match && match[1]) {
      errorUuid = match[1];
      isErrorFixing = true;
    }
    
    return { isErrorFixing, errorUuid };
  }
  
  return { isErrorFixing: false };
}
