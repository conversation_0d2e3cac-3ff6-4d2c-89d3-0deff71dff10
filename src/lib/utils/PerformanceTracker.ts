/**
 * PerformanceTracker - A simple utility for tracking performance metrics
 * 
 * This class provides methods to track execution time of various operations
 * with Sentry integration for performance monitoring.
 */

import * as Sentry from '@sentry/nextjs';

type TimerData = {
  startTime: number;
  endTime?: number;
  label: string;
  metadata?: Record<string, any>;
  parentId?: string; // For nested timers
  transaction?: any; // Sentry transaction
  span?: any; // Sentry span
};

export class PerformanceTracker {
  private static instance: PerformanceTracker;
  private timers: Map<string, TimerData> = new Map();
  private enabled: boolean;
  private completedTraces: Map<string, Array<{id: string, label: string, elapsedTime: number, timestamp: number, metadata?: Record<string, any>}>> = new Map();

  constructor() {
    // Check if performance tracking is enabled via environment variable
    this.enabled = process.env.ENABLE_PERFORMANCE_TRACKING === 'true';
  }

  /**
   * Get the singleton instance of PerformanceTracker
   */
  public static getInstance(): PerformanceTracker {
    if (!PerformanceTracker.instance) {
      PerformanceTracker.instance = new PerformanceTracker();
    }
    return PerformanceTracker.instance;
  }

  /**
   * Start a timer with the given ID and label
   * 
   * @param id Unique identifier for this timer
   * @param label Human-readable label for the operation being timed
   * @param metadata Optional metadata to associate with this timing
   * @param parentId Optional parent timer ID for nested operations
   * @returns The current timestamp
   */
  public startTimer(id: string, label: string, metadata?: Record<string, any>, parentId?: string): number {
    if (!this.enabled) return Date.now();
    
    const startTime = Date.now();
    
    // Create Sentry transaction or span based on whether this is a root or child timer
    let transaction: any | undefined;
    let span: any | undefined;
    
    // Create a span using Sentry.withActiveSpan for proper context
    if (parentId) {
      // For child operations, we'll just use tags to track the relationship
      Sentry.setTag('parent_id', parentId);
      Sentry.setTag('child_id', id);
      Sentry.setTag('operation', label);
      
      if (metadata) {
        Object.entries(metadata).forEach(([key, value]) => {
          Sentry.setTag(`${id}_${key}`, String(value));
        });
      }
    } else {
      // For root operations, set request-level tags
      Sentry.setTag('request_id', id);
      Sentry.setTag('operation', label);
      
      if (metadata) {
        Object.entries(metadata).forEach(([key, value]) => {
          Sentry.setTag(key, String(value));
        });
      }
    }
    
    this.timers.set(id, {
      startTime,
      label,
      metadata,
      parentId,
      transaction,
      span
    });
    
    // Initialize trace collection for request IDs (typically the root timer)
    if (!parentId && !this.completedTraces.has(id)) {
      this.completedTraces.set(id, []);
    }
    
    return startTime;
  }

  /**
   * Stop a timer with the given ID
   * 
   * @param id Unique identifier for the timer to stop
   * @param additionalMetadata Optional additional metadata to merge with existing metadata
   * @returns The elapsed time in milliseconds, or undefined if the timer doesn't exist
   */
  public stopTimer(id: string, additionalMetadata?: Record<string, any>): number | undefined {
    if (!this.enabled) return undefined;
    
    const timer = this.timers.get(id);
    if (!timer) {
      console.warn(`Timer with ID ${id} not found`);
      return undefined;
    }

    const endTime = Date.now();
    timer.endTime = endTime;
    
    if (additionalMetadata) {
      timer.metadata = { ...timer.metadata, ...additionalMetadata };
    }

    const elapsedTime = endTime - timer.startTime;
    
    // Add data to Sentry if there's additional metadata
    if (additionalMetadata) {
      Object.entries(additionalMetadata).forEach(([key, value]) => {
        Sentry.setTag(`${id}_${key}`, String(value));
      });
    }
    
    // For Sentry, we don't need to explicitly finish spans as they'll be auto-finished
    // when the request completes
    
    // Store the completed trace for later printing
    const requestId = timer.parentId || id;
    if (this.completedTraces.has(requestId)) {
      const traces = this.completedTraces.get(requestId);
      traces?.push({
        id,
        label: timer.label,
        elapsedTime,
        timestamp: Date.now(),
        metadata: timer.metadata
      });
      
      // For streaming contexts, we don't want to wait for the root timer to complete
      // before logging individual operations
      this.logIndividualTrace(id, timer.label, elapsedTime, timer.metadata);
      
      // If this is the root timer (request), print all traces and clean up
      if (!timer.parentId && id === requestId) {
        this.printAllTraces(requestId);
        this.cleanupTraces(requestId);
      }
    }

    // Log the timing information
    this.logTiming(id, timer, elapsedTime);
    
    return elapsedTime;
  }

  /**
   * Log timing information
   * 
   * @param id Timer ID
   * @param timer Timer data
   * @param elapsedTime Elapsed time in milliseconds
   */
  private async logTiming(id: string, timer: TimerData, elapsedTime: number): Promise<void> {
    if (!this.enabled) return;
    
    // We'll only log individual timings in debug mode
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PERFORMANCE] ${timer.label} (${id}): ${elapsedTime}ms`, timer.metadata || {});
    }
  }
  
  /**
   * Log an individual trace immediately
   * 
   * @param id Timer ID
   * @param label Operation label
   * @param elapsedTime Elapsed time in milliseconds
   * @param metadata Optional metadata
   */
  private logIndividualTrace(id: string, label: string, elapsedTime: number, metadata?: Record<string, any>): void {
    if (!this.enabled || process.env.NODE_ENV !== 'development') return;
    
    console.log(`[PERFORMANCE] ${label} completed: ${elapsedTime}ms`, metadata || {});
  }
  
  /**
   * Print all traces for a request ID
   * 
   * @param requestId The request ID to print traces for
   */
  private printAllTraces(requestId: string): void {
    const traces = this.completedTraces.get(requestId);
    if (!traces || traces.length === 0) return;
    
    // Sort traces by timestamp to show them in execution order
    traces.sort((a, b) => a.timestamp - b.timestamp);
    
    // Calculate total time
    const totalTime = traces.find(t => t.id === requestId)?.elapsedTime || 0;
    
    console.log('\n=== PERFORMANCE TRACES ===');
    console.log(`Request ID: ${requestId}`);
    console.log(`Total Time: ${totalTime}ms`);
    console.log(`Operations: ${traces.length}`);
    console.log('---------------------------');
    
    // Print each trace
    traces.forEach(trace => {
      const percentage = totalTime > 0 ? Math.round((trace.elapsedTime / totalTime) * 100) : 0;
      console.log(`${trace.label}: ${trace.elapsedTime}ms (${percentage}%)`, trace.metadata || {});
    });
    
    console.log('===========================\n');
    
    // Send to Sentry as a custom measurement
    Sentry.setMeasurement('request_total_time', totalTime, 'millisecond');
    traces.forEach(trace => {
      if (trace.id !== requestId) { // Skip the root timer as it's already captured
        Sentry.setMeasurement(`op_${trace.label.replace(/\s+/g, '_').toLowerCase()}`, trace.elapsedTime, 'millisecond');
      }
    });
  }
  
  /**
   * Clean up traces for a request ID to prevent memory leaks
   * 
   * @param requestId The request ID to clean up traces for
   */
  private cleanupTraces(requestId: string): void {
    // Clear traces for this request to avoid memory leaks
    this.completedTraces.delete(requestId);
    
    // Also clean up any timers associated with this request
    for (const [id, timer] of this.timers.entries()) {
      if (timer.parentId === requestId || id === requestId) {
        this.timers.delete(id);
      }
    }
  }

  /**
   * Check if a timer is currently running
   * 
   * @param id Timer ID to check
   * @returns True if the timer is running, false otherwise
   */
  public isTimerRunning(id: string): boolean {
    if (!this.enabled) return false;
    
    const timer = this.timers.get(id);
    return !!timer && timer.endTime === undefined;
  }

  /**
   * Get all active timers
   * 
   * @returns Array of timer IDs that are currently running
   */
  public getActiveTimers(): string[] {
    if (!this.enabled) return [];
    
    return Array.from(this.timers.entries())
      .filter(([_, timer]) => timer.endTime === undefined)
      .map(([id]) => id);
  }

  /**
   * Clear all timers
   */
  public clearTimers(): void {
    if (!this.enabled) return;
    
    this.timers.clear();
  }

  /**
   * Enable or disable performance tracking
   * 
   * @param enabled Whether tracking should be enabled
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * Check if performance tracking is enabled
   * 
   * @returns True if enabled, false otherwise
   */
  public isEnabled(): boolean {
    return this.enabled;
  }
}

// Export a singleton instance for easy import
export const performanceTracker = PerformanceTracker.getInstance();
