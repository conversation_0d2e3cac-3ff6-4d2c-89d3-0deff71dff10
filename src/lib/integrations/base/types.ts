import { Connection, ConnectionStatus } from '../../db/schema';

export type IntegrationProvider = 'supabase' | 'onesignal';

export type OAuthConfig = {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
  authorizationUrl: string;
  tokenUrl: string;
};

export type OAuthToken = {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: string;
  tokenType?: string;
};

export type IntegrationConfig = {
  oauth?: OAuthConfig;
  apiKey?: {
    required: boolean;
    fields: Array<{
      key: string;
      label: string;
      type: 'text' | 'password';
      required: boolean;
    }>;
  };
};

export type IntegrationMetadata = {
  credentials?: Record<string, string>;
  oauth?: OAuthToken;
  config?: Record<string, any>;
};

export type CreateConnectionInput = Omit<
  Connection,
  'id' | 'createdAt' | 'updatedAt' | 'lastSyncedAt' | 'status'
> & {
  metadata: IntegrationMetadata;
};

export type UpdateConnectionInput = Partial<CreateConnectionInput> & {
  id: string;
};

export interface IIntegrationProvider {
  getConfig(): IntegrationConfig;
  getAuthUrl(): string;
  connect(input: CreateConnectionInput): Promise<Connection>;
  disconnect({ connectionId }: { connectionId: string }): Promise<Connection>;
  update(input: UpdateConnectionInput): Promise<Connection>;
  getConnection({ connectionId }: { connectionId: string }): Promise<Connection>;
  validateCredentials(metadata: IntegrationMetadata): Promise<boolean>;
  handleOAuthCallback(code: string): Promise<OAuthToken>;
}
