import 'server-only';
import {Connection, user, connection, projectConnections, ConnectionStatus} from '../../db/schema';
import { and, eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { getUserIntegrations } from "../queries";
import { db } from '@/lib/db/db';

import {
  CreateConnectionInput,
  IIntegrationProvider,
  IntegrationConfig,
  IntegrationMetadata,
  OAuthToken,
  UpdateConnectionInput,
} from './types';

// biome-ignore lint: Forbidden non-null assertion.

export abstract class BaseIntegrationProvider implements IIntegrationProvider {
  constructor(
    protected readonly provider: string,
    public readonly config: IntegrationConfig
  ) {}

  abstract validateCredentials(metadata: IntegrationMetadata): Promise<boolean>;
  abstract handleOAuthCallback(code: string): Promise<OAuthToken>;

  getConfig(): IntegrationConfig {
    return this.config;
  }

  getAuthUrl(): string {
    if (!this.config.oauth) {
      throw new Error('OAuth is not configured for this provider');
    }

    const { authorizationUrl, clientId, redirectUri, scope } = this.config.oauth;
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: scope.join(' '),
    });

    return `${authorizationUrl}?${params.toString()}`;
  }

  async connect(input: CreateConnectionInput): Promise<Connection> {
    try {
      const isValid = await this.validateCredentials(input.metadata);
      console.log('isValid', isValid)
      if (!isValid) {
        throw new Error('Invalid credentials');
      }

      const [newConnection] = await db
        .insert(connection)
        .values({
          ...input,
          status: ConnectionStatus.CONNECTED,
        })
        .returning();

      console.log('newConnection', newConnection)
      return newConnection;
    } catch (error) {
      console.error('Failed to connect integration');
      throw error;
    }
  }

  async disconnect({ connectionId }: { connectionId: string }): Promise<Connection> {
    try {
      const [updatedConnection] = await db
        .update(connection)
        .set({
          status: ConnectionStatus.DISCONNECTED,
          updatedAt: new Date(),
        })
        .where(eq(connection.id, connectionId))
        .returning();

      if (!updatedConnection) {
        throw new Error('Connection not found');
      }

      return updatedConnection;
    } catch (error) {
      console.error('Failed to disconnect integration');
      throw error;
    }
  }

  async update(input: UpdateConnectionInput): Promise<Connection> {
    try {
      const { id, ...updateData } = input;

      if (updateData.metadata) {
        const isValid = await this.validateCredentials(updateData.metadata);
        if (!isValid) {
          throw new Error('Invalid credentials');
        }
      }

      const [updatedConnection] = await db
        .update(connection)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(connection.id, id))
        .returning();

      if (!updatedConnection) {
        throw new Error('Connection not found');
      }

      return updatedConnection;
    } catch (error) {
      console.error('Failed to update integration');
      throw error;
    }
  }

  async getConnection({ connectionId }: { connectionId: string }): Promise<Connection> {
    try {
      const connections = await db
        .select()
        .from(connection)
        .where(eq(connection.id, connectionId));

      const foundConnection = connections[0];
      if (!foundConnection) {
        throw new Error('Connection not found');
      }

      return foundConnection as Connection;
    } catch (error) {
      console.error('Failed to get integration');
      throw error;
    }
  }


  public async getConnectionByUserId({ userId }: { userId: string }): Promise<Connection | undefined> {
    try {
      const connections = await db
        .select()
        .from(connection)
        .where(
          and(
            eq(connection.userId, userId),
            eq(connection.provider, this.provider),
            eq(connection.status, "connected")
          ),
        );
      return connections[0];
    } catch (error) {
      console.error('Failed to get integration by user ID');
      throw error;
    }
  }
}

