import 'server-only';
import { and, eq } from 'drizzle-orm';
import {connection, ConnectionStatus} from '../db/schema';
import type { Connection } from '../db/schema';
import { db } from '../db/db';

// Optionally, if not using email/pass login, you can
// use the Drizzle adapter for Auth.js / NextAuth
// https://authjs.dev/reference/adapter/drizzle

// biome-ignore lint: Forbidden non-null assertion.
const dbClient = db;

export async function getUserIntegrations({ userId }: { userId: string }): Promise<Connection[]> {
  try {
    return await dbClient.select().from(connection).where(eq(connection.userId, userId));
  } catch (error) {
    console.error('Failed to get user integrations from database');
    throw error;
  }
}

export async function getUserIntegrationByProvider({
  userId,
  provider,
}: {
  userId: string;
  provider: string;
}): Promise<Connection | undefined> {
  try {
    const connections = await db
      .select()
      .from(connection)
      .where(
        and(
          eq(connection.userId, userId),
          eq(connection.provider, provider),
          eq(connection.status, ConnectionStatus.CONNECTED),
        ),
      );
    return connections[0];
  } catch (error) {
    console.error('Failed to get user integration by provider from database');
    throw error;
  }
}

export async function deleteUserIntegration({ connectionId }: { connectionId: string }): Promise<void> {
  try {
    await db
      .delete(connection)
      .where(eq(connection.id, connectionId));
  } catch (error) {
    console.error('Failed to delete user integration from database');
    throw error;
  }
}
