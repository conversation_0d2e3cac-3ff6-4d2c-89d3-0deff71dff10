import { IntegrationMetadata } from '../base/types';

export type SupabaseCredentials = {
  projectUrl: string;
  apiKey: string;
  projectRef?: string;
};

export type SupabaseMetadata = IntegrationMetadata & {
  credentials: SupabaseCredentials;
};

export type SupabaseDatabase = {
  id: string;
  name: string;
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
};

export type SupabaseProjectData = {
  id: string;
  name: string;
  organization_id: string;
  database: {
    host: string;
    port: number;
    user: string;
    password: string;
    database: string;
  };
  anon_key: string;
  service_key: string;
};

export type SupabaseTypes = {
  database: string;
  client: string;
  helper: string;
  projectRef: string;
  host: string;
  anonKey: string;
};
