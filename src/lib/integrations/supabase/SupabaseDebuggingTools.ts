import { stripIndent } from 'common-tags';

/**
 * Class that provides debugging tools for Supabase integrations
 */
export class SupabaseDebuggingTools {
  /**
   * Helper function to generate SQL queries for different Supabase service logs
   */
  getLogQuery(
    service: 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime',
    limit: number = 100,
    functionId?: string
  ) {
    switch (service) {
      case 'api':
        return stripIndent`
          select id, identifier, timestamp, event_message, request.method, request.path, response.status_code
          from edge_logs
          cross join unnest(metadata) as m
          cross join unnest(m.request) as request
          cross join unnest(m.response) as response
          order by timestamp desc
          limit ${limit}
        `;
      case 'branch-action':
        return stripIndent`
          select workflow_run, workflow_run_logs.timestamp, id, event_message from workflow_run_logs
          order by timestamp desc
          limit ${limit}
        `;
      case 'postgres':
        return stripIndent`
          select identifier, postgres_logs.timestamp, id, event_message, parsed.error_severity from postgres_logs
          cross join unnest(metadata) as m
          cross join unnest(m.parsed) as parsed
          order by timestamp desc
          limit ${limit}
        `;
      case 'edge-function':
        if (functionId) {
          return stripIndent`
            select id, function_logs.timestamp, event_message, metadata.event_type, metadata.function_id, metadata.level 
            from function_logs
            cross join unnest(metadata) as metadata
            where (metadata.function_id = '${functionId}')
            order by timestamp desc
            limit ${limit}
          `;
        } else {
          return stripIndent`
            select id, function_edge_logs.timestamp, event_message, response.status_code, request.method, m.function_id, m.execution_time_ms, m.deployment_id, m.version from function_edge_logs
            cross join unnest(metadata) as m
            cross join unnest(m.response) as response
            cross join unnest(m.request) as request
            order by timestamp desc
            limit ${limit}
          `;
        }
      case 'auth':
        return stripIndent`
          select id, auth_logs.timestamp, event_message, metadata.level, metadata.status, metadata.path, metadata.msg as msg, metadata.error from auth_logs
          cross join unnest(metadata) as metadata
          order by timestamp desc
          limit ${limit}
        `;
      case 'storage':
        return stripIndent`
          select id, storage_logs.timestamp, event_message from storage_logs
          order by timestamp desc
          limit ${limit}
        `;
      case 'realtime':
        return stripIndent`
          select id, realtime_logs.timestamp, event_message from realtime_logs
          order by timestamp desc
          limit ${limit}
        `;
      default:
        throw new Error(`unsupported log service type: ${service}`);
    }
  }

  /**
   * Fetch logs for a Supabase service
   * @param params Parameters for fetching logs
   * @returns The logs data
   */
  async getLogs({
    connectionId,
    projectId,
    service,
    limit = 100,
    functionId,
    callSupabaseAPI
  }: {
    connectionId: string;
    projectId: string;
    service: 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime';
    limit?: number;
    functionId?: string;
    callSupabaseAPI: (connectionId: string, callback: (token: string) => Promise<Response>) => Promise<any>;
  }) {
    try {
      // Get the SQL query for the requested service
      const sql = this.getLogQuery(service, limit, functionId);
      
      // Calculate timestamp for logs (24 hours ago to ensure we get logs)
      const timestamp = new Date(Date.now() - 1 * 60 * 60 * 1000);

      console.log('sql', sql)
      console.log('projectId', projectId)
      console.log('iso_timestamp_start', timestamp.toISOString())

      // Fetch logs using the provided API call function
      const logs = await callSupabaseAPI(
        connectionId,
        async (token) => {
          const url = new URL(`https://api.supabase.com/v1/projects/${projectId}/analytics/endpoints/logs.all`);
          url.searchParams.append('iso_timestamp_start', timestamp.toISOString());
          url.searchParams.append('iso_timestamp_end', new Date().toISOString());
          url.searchParams.append('sql', sql);

          console.log('token', token)
          return fetch(url.toString(), {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
        }
      );
      
      return {
        logs,
        service,
        ...(functionId && { functionId }),
        timestamp: timestamp.toISOString(),
        comment: functionId
          ? `These are the most recent logs for function ID ${functionId} from your Supabase project.`
          : `These are the most recent ${service} logs from your Supabase project.`
      };
    } catch (e: any) {
      console.error(`Error while fetching Supabase ${service} logs`, e);
      throw new Error(`Error while fetching Supabase ${service} logs: ${e.message}`);
    }
  }
}
