import { createHash } from 'crypto';

const MO_DIFF_OPEN = '<MO_DIFF';
const MO_DIFF_CLOSE = '</MO_DIFF>';
const SEARCH_OPEN = '<SEARCH>';
const SEARCH_CLOSE = '</SEARCH>';
const REPLACE_OPEN = '<REPLACE>';
const REPLACE_CLOSE = '</REPLACE>';
const HEADER_BUFFER_LIMIT = 2048; // Prevent memory attacks

// Interface for diff metadata
export interface DiffMeta {
    path: string;
    lang: string;
    searches: string[];
    replacements: string[];
    currentPair: number;
    lineCount: number;
}

// Interface for tracking the current state of a search/replace block
interface BlockState {
    type: 'search' | 'replace' | null;
    content: string;
}

// Interface for callbacks
export interface DiffParserCallbacks {
    onDiffStart?: (meta: Omit<DiffMeta, 'currentPair'>) => void;
    onDiffComplete?: (meta: DiffMeta) => void;
    onDiffError?: (error: string, path?: string) => void;
    onDiffDelta?: (path: string, searchBlock: string, replaceBlock: string, meta: DiffMeta) => void;
}

export class MODiffParser {
    private states = new Map<string, {
        position: number;
        currentDiff?: DiffMeta;
        currentBlock: BlockState;
        headerBuffer: string;
        blockBuffer: string;
    }>();

    constructor(private callbacks?: DiffParserCallbacks) {}

    parse(messageId: string, input: string): string {
        const state = this.getState(messageId);
        let output = '';
        let i = 0;

        while (i < input.length) {
            // If we're not already processing a diff, look for the start of one
            if (!state.currentDiff) {
                const diffStart = input.indexOf(MO_DIFF_OPEN, i);
                if (diffStart === -1) {
                    // No diff tag found, pass through the rest of the input
                    output += input.slice(i);
                    break;
                }

                // Add text before the diff tag to output
                output += input.slice(i, diffStart);
                i = diffStart;
                state.headerBuffer = '';
            }

            // We're inside a diff
            if (state.currentDiff) {
                // If we're inside a search/replace block
                if (state.currentBlock.type) {
                    i = this.processBlockContent(state, input, i);
                    continue;
                }

                // Check for diff end
                const diffEnd = input.indexOf(MO_DIFF_CLOSE, i);

                // Check for search or replace blocks
                const searchStart = input.indexOf(SEARCH_OPEN, i);
                const replaceStart = input.indexOf(REPLACE_OPEN, i);

                // Determine which comes first
                if (diffEnd !== -1 && (searchStart === -1 || diffEnd < searchStart) && (replaceStart === -1 || diffEnd < replaceStart)) {
                    // Diff is ending
                    this.finalizeDiff(state.currentDiff);
                    i = diffEnd + MO_DIFF_CLOSE.length;
                    state.currentDiff = undefined;
                    state.currentBlock = { type: null, content: '' };
                    continue;
                }

                // Start of search block
                if (searchStart !== -1 && (diffEnd === -1 || searchStart < diffEnd) && (replaceStart === -1 || searchStart < replaceStart)) {
                    state.currentBlock = { type: 'search', content: '' };
                    i = searchStart + SEARCH_OPEN.length;
                    continue;
                }

                // Start of replace block
                if (replaceStart !== -1 && (diffEnd === -1 || replaceStart < diffEnd) && (searchStart === -1 || replaceStart < searchStart)) {
                    state.currentBlock = { type: 'replace', content: '' };
                    i = replaceStart + REPLACE_OPEN.length;
                    continue;
                }

                // If none of the above, consume the chunk
                const chunkEnd = Math.min(
                    diffEnd === -1 ? Infinity : diffEnd,
                    searchStart === -1 ? Infinity : searchStart,
                    replaceStart === -1 ? Infinity : replaceStart
                );

                if (chunkEnd === Infinity) {
                    // No tags found, consume the rest of the input
                    i = input.length;
                } else {
                    // Consume up to the next tag
                    i = chunkEnd;
                }
            } else {
                // We're at the start of a diff, parse the header
                const headerEnd = input.indexOf('>', i);
                if (headerEnd === -1) {
                    // Header is incomplete, buffer and wait for more
                    state.headerBuffer += input.slice(i);
                    if (state.headerBuffer.length > HEADER_BUFFER_LIMIT) {
                        this.resetState(messageId);
                        this.callbacks?.onDiffError?.('Header buffer limit exceeded');
                    }
                    break;
                }

                const fullHeader = state.headerBuffer + input.slice(i, headerEnd + 1);
                const diffMeta = this.parseDiffHeader(fullHeader);
                if (diffMeta) {
                    state.currentDiff = diffMeta;
                    state.currentBlock = { type: null, content: '' };
                    this.callbacks?.onDiffStart?.(diffMeta);
                }

                i = headerEnd + 1;
                state.headerBuffer = '';
            }
        }

        return output;
    }

    private processBlockContent(state: any, input: string, startPos: number): number {
        const blockType = state.currentBlock.type;
        const searchEnd = blockType === 'search' ? input.indexOf(SEARCH_CLOSE, startPos) : -1;
        const replaceEnd = blockType === 'replace' ? input.indexOf(REPLACE_CLOSE, startPos) : -1;
        const blockEnd = blockType === 'search' ? searchEnd : replaceEnd;

        if (blockEnd === -1) {
            // Block is not ending in this chunk, accumulate content
            state.currentBlock.content += input.slice(startPos);
            return input.length;
        }

        // Block is ending
        state.currentBlock.content += input.slice(startPos, blockEnd);

        // Process the completed block
        if (blockType === 'search') {
            // Add to searches array
            state.currentDiff.searches.push(state.currentBlock.content);
        } else if (blockType === 'replace') {
            // Add to replacements array
            state.currentDiff.replacements.push(state.currentBlock.content);

            // Check if we've completed a search/replace pair
            if (state.currentDiff.searches.length === state.currentDiff.replacements.length) {
                // We've completed a pair, check if we're at the end of the diff
                const diffEnd = input.indexOf(MO_DIFF_CLOSE, blockEnd + REPLACE_CLOSE.length);
                if (diffEnd !== -1) {
                    this.finalizeDiff(state.currentDiff);
                    state.currentDiff = undefined;
                    state.currentBlock = { type: null, content: '' };
                    return diffEnd + MO_DIFF_CLOSE.length;
                }

                // Emit onDiffDelta callback
                this.callbacks?.onDiffDelta?.(state.currentDiff.path, state.currentDiff.searches[state.currentDiff.currentPair], state.currentDiff.replacements[state.currentDiff.currentPair], state.currentDiff);
                state.currentDiff.currentPair++;
                state.currentDiff.lineCount += Math.max(state.currentDiff.searches[state.currentDiff.currentPair - 1].split('\n').length, state.currentDiff.replacements[state.currentDiff.currentPair - 1].split('\n').length);
            }
        }

        // Reset current block
        state.currentBlock = { type: null, content: '' };
        return blockEnd + (blockType === 'search' ? SEARCH_CLOSE.length : REPLACE_CLOSE.length);
    }

    private parseDiffHeader(header: string): DiffMeta | undefined {
        const attrRegex = /(\w+)=["']((?:\\"|[^"'])*)["']/g;
        const attrs: Record<string, string> = {};

        let match: RegExpExecArray | null;
        while ((match = attrRegex.exec(header)) !== null) {
            attrs[match[1].toLowerCase()] = match[2].replace(/\\"/g, '"');
        }

        if (!attrs.path) {
            this.callbacks?.onDiffError?.('MO_DIFF missing path attribute');
            return undefined;
        }

        // Remove leading slash from path if present
        const sanitizedPath = attrs.path.startsWith('/') ? attrs.path.substring(1) : attrs.path;

        // Log when path sanitization occurs
        if (attrs.path.startsWith('/')) {
            console.log(`DiffParser: Sanitized file path from '${attrs.path}' to '${sanitizedPath}'`);
        }

        return {
            path: sanitizedPath,
            lang: attrs.lang || 'text',
            searches: [],
            replacements: [],
            currentPair: 0,
            lineCount: 0
        };
    }

    private finalizeDiff(diff: DiffMeta) {
        // Always require search/replace pairs
        if (diff.searches.length !== diff.replacements.length) {
            this.callbacks?.onDiffError?.(`Mismatched search/replace pairs (${diff.searches.length} searches, ${diff.replacements.length} replacements)`, diff.path);
            return;
        }

        this.callbacks?.onDiffComplete?.(diff);
    }

    private getState(messageId: string) {
        if (!this.states.has(messageId)) {
            this.states.set(messageId, {
                position: 0,
                headerBuffer: '',
                blockBuffer: '',
                currentBlock: { type: null, content: '' }
            });
        }
        return this.states.get(messageId)!;
    }

    public resetState(messageId: string) {
        this.states.delete(messageId);
    }
}
