import { createHash } from 'crypto';

const HIDDEN_OPEN = '<hidden>';
const HIDDEN_CLOSE = '</hidden>';

export interface HiddenContentMeta {
    content: string;
    hiddenContent: string[];
}

interface ParserCallbacks {
    onHiddenStart?: () => void;
    onHiddenDelta?: (chunk: string, meta?: HiddenContentMeta) => void;
    onHiddenComplete?: (meta: HiddenContentMeta) => void;
    onHiddenError?: (error: string) => void;
}

/**
 * Parser for hidden content in streaming responses
 * Removes content within <hidden></hidden> tags from the output
 * and provides callbacks to handle the hidden content
 */
export class HiddenContentParser {
    private states = new Map<string, {
        position: number;
        isInHidden: boolean;
        currentHiddenContent: string;
        allHiddenContent: string[];
        cleanContent: string;
    }>();

    constructor(private callbacks?: ParserCallbacks) {}

    /**
     * Parse a chunk of text, removing hidden content and calling callbacks
     * @param messageId Unique ID for the message being parsed
     * @param input Input text to parse
     * @returns Cleaned text with hidden content removed
     */
    parse(messageId: string, input: string): string {
        const state = this.getState(messageId);
        let output = '';
        let i = 0;

        while (i < input.length) {
            if (state.isInHidden) {
                // We're inside a hidden tag, look for the closing tag
                const hiddenEnd = input.indexOf(HIDDEN_CLOSE, i);
                
                if (hiddenEnd === -1) {
                    // No closing tag found in this chunk, accumulate hidden content
                    state.currentHiddenContent += input.slice(i);
                    i = input.length;
                } else {
                    // Found closing tag, accumulate final part of hidden content
                    state.currentHiddenContent += input.slice(i, hiddenEnd);
                    
                    // Add to the list of hidden content blocks
                    state.allHiddenContent.push(state.currentHiddenContent);
                    
                    // Call callback with the hidden content
                    this.callbacks?.onHiddenDelta?.(state.currentHiddenContent, {
                        content: state.cleanContent,
                        hiddenContent: state.allHiddenContent
                    });
                    
                    // Reset current hidden content
                    state.currentHiddenContent = '';
                    state.isInHidden = false;
                    
                    // Move past the closing tag
                    i = hiddenEnd + HIDDEN_CLOSE.length;
                }
            } else {
                // We're outside a hidden tag, look for an opening tag
                const hiddenStart = input.indexOf(HIDDEN_OPEN, i);
                
                if (hiddenStart === -1) {
                    // No opening tag found, add everything to output
                    output += input.slice(i);
                    state.cleanContent += input.slice(i);
                    i = input.length;
                } else {
                    // Found opening tag, add content before it to output
                    output += input.slice(i, hiddenStart);
                    state.cleanContent += input.slice(i, hiddenStart);
                    
                    // Call callback when we find a hidden tag
                    if (!state.isInHidden) {
                        this.callbacks?.onHiddenStart?.();
                    }
                    
                    // Move past the opening tag
                    i = hiddenStart + HIDDEN_OPEN.length;
                    state.isInHidden = true;
                }
            }
        }
        
        // If we've processed the entire input and have hidden content,
        // call the completion callback
        if (state.allHiddenContent.length > 0 && !state.isInHidden) {
            this.callbacks?.onHiddenComplete?.({
                content: state.cleanContent,
                hiddenContent: state.allHiddenContent
            });
        }
        
        return output;
    }

    /**
     * Get the current state for a message, creating it if it doesn't exist
     */
    private getState(messageId: string) {
        if (!this.states.has(messageId)) {
            this.states.set(messageId, {
                position: 0,
                isInHidden: false,
                currentHiddenContent: '',
                allHiddenContent: [],
                cleanContent: ''
            });
        }
        return this.states.get(messageId)!;
    }

    /**
     * Reset the state for a message
     */
    public resetState(messageId: string) {
        this.states.delete(messageId);
    }
}
