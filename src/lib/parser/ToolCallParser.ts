/**
 * Parser for extracting tool calls from streaming content
 */

interface ToolCall {
    toolCallId: string;
    toolName: string;
    args: any;
    result?: any;
}

interface ToolCallParserCallbacks {
    onToolCallStart?: (toolCall: ToolCall) => void;
    onToolCallResult?: (toolCall: ToolCall) => void;
}

export class ToolCallParser {
    private callbacks: ToolCallParserCallbacks;
    
    constructor(callbacks: ToolCallParserCallbacks = {}) {
        this.callbacks = callbacks;
    }
    
    /**
     * Parse content for tool calls
     * @param messageId The ID of the message being parsed
     * @param content The content to parse
     * @returns The content with tool calls extracted
     */
    public parse(messageId: string, content: string): string {
        // Extract tool calls using regex patterns
        const toolCallPattern = /```tool-call\s*\n([\s\S]*?)\n```/g;
        const toolResultPattern = /```tool-result\s*\n([\s\S]*?)\n```/g;
        
        // Process tool calls
        let match;
        while ((match = toolCallPattern.exec(content)) !== null) {
            try {
                const toolCallJson = match[1].trim();
                const toolCall = JSON.parse(toolCallJson);
                
                if (this.callbacks.onToolCallStart) {
                    this.callbacks.onToolCallStart({
                        toolCallId: toolCall.id || `tool-${Date.now()}`,
                        toolName: toolCall.name,
                        args: toolCall.arguments ? JSON.parse(toolCall.arguments) : {}
                    });
                }
            } catch (error) {
                console.error('Error parsing tool call:', error);
            }
        }
        
        // Process tool results
        while ((match = toolResultPattern.exec(content)) !== null) {
            try {
                const toolResultJson = match[1].trim();
                const toolResult = JSON.parse(toolResultJson);
                
                if (this.callbacks.onToolCallResult) {
                    this.callbacks.onToolCallResult({
                        toolCallId: toolResult.id || `tool-${Date.now()}`,
                        toolName: toolResult.name,
                        args: {},
                        result: toolResult.result
                    });
                }
            } catch (error) {
                console.error('Error parsing tool result:', error);
            }
        }
        
        // Remove tool call and result blocks from content
        content = content.replace(toolCallPattern, '');
        content = content.replace(toolResultPattern, '');
        
        return content;
    }
}
