const ACTIONS_OPEN = '<actions>';
const ACTIONS_CLOSE = '</actions>';
const ACTION_OPEN = '<action';
const ACTION_CLOSE = '</action>';
const HEADER_BUFFER_LIMIT = 2048; // Prevent memory attacks

export interface ActionMeta {
  type: string;
  link?: string;
  tool?: string;
  content: string;
  secretName?: string;
  allSecretNames?: string[];
}

export interface ActionsStatus {
  isActive: boolean;
  isComplete: boolean;
  content: string;
  actions: ActionMeta[];
}

interface ParserCallbacks {
  onActionsStart?: () => void;
  onActionsDelta?: (chunk: string, meta?: ActionsStatus) => void;
  onActionsComplete?: (meta: ActionsStatus) => void;
  onActionsError?: (error: string) => void;
}

export class ActionsParser {
  private states = new Map<string, {
    position: number;
    currentActions?: ActionsStatus;
    buffer: string;
  }>();

  constructor(private callbacks?: ParserCallbacks, private isLastMessage: boolean = false) {}

  parse(messageId: string, input: string): string {
    const state = this.getState(messageId);
    let output = '';
    let i = 0;

    // No logging in production code

    // Check for orphaned <action> tags without enclosing <actions> tags
    const hasActionsOpen = input.includes(ACTIONS_OPEN);
    const hasActionOpen = input.includes(ACTION_OPEN);
    const hasActionsClose = input.includes(ACTIONS_CLOSE);
    
    // No logging in production code
    
    // If we have <action> tags but no <actions> wrapper, add the wrapper
    if (!hasActionsOpen && hasActionOpen) {
      // Find the first <action tag
      const firstActionIndex = input.indexOf(ACTION_OPEN);
      if (firstActionIndex > -1) {
        // Only wrap the action tags, not the entire message
        const beforeActions = input.substring(0, firstActionIndex);
        const actionsContent = input.substring(firstActionIndex);
        input = beforeActions + ACTIONS_OPEN + actionsContent + (hasActionsClose ? '' : ACTIONS_CLOSE);
        // No logging in production code
      }
    }

    while (i < input.length) {
      if (!state.currentActions) {
        const actionsStart = input.indexOf(ACTIONS_OPEN, i);
        if (actionsStart === -1) {
          output += input.slice(i);
          break;
        }

        output += input.slice(i, actionsStart);
        i = actionsStart + ACTIONS_OPEN.length;
        
        // Initialize actions metadata
        state.currentActions = {
          isActive: true,
          isComplete: false,
          content: '',
          actions: []
        };
        
        // Notify that actions parsing has started
        this.callbacks?.onActionsStart?.();
      } else {
        const actionsEnd = input.indexOf(ACTIONS_CLOSE, i);

        // Process the current chunk
        const chunk = actionsEnd === -1 ? input.slice(i) : input.slice(i, actionsEnd);
        
        // Add chunk to content
        state.currentActions.content += chunk;
        
        // Parse actions from the content
        state.currentActions.actions = this.parseActions(state.currentActions.content);
        
        // Call onActionsDelta with the chunk and current actions metadata
        this.callbacks?.onActionsDelta?.(chunk, state.currentActions);

        if (actionsEnd === -1) {
          i = input.length;
          break;
        }

        // Actions block is complete
        state.currentActions.isComplete = true;
        this.finalizeActions(state.currentActions);

        i = actionsEnd + ACTIONS_CLOSE.length;
        state.currentActions = undefined;
      }
    }
    return output;
  }

  private parseActions(content: string): ActionMeta[] {
    const actions: ActionMeta[] = [];
    let position = 0;
    
    // No logging in production code
    
    while (position < content.length) {
      const actionStart = content.indexOf(ACTION_OPEN, position);
      if (actionStart === -1) {
        // No logging in production code
        break;
      }
      
      // No logging in production code
      
      const actionEndTag = content.indexOf('>', actionStart);
      if (actionEndTag === -1) {
        // No logging in production code
        break;
      }
      
      const actionClose = content.indexOf(ACTION_CLOSE, actionEndTag);
      if (actionClose === -1) {
        // No logging in production code
        break;
      }
      
      // Extract the action tag attributes
      const actionTag = content.substring(actionStart + ACTION_OPEN.length, actionEndTag).trim();
      
      // Extract the action content
      const actionContent = content.substring(actionEndTag + 1, actionClose).trim();
      
      // Parse attributes
      const type = this.extractAttribute(actionTag, 'type') || 'feature';
      const link = this.extractAttribute(actionTag, 'link');
      const tool = this.extractAttribute(actionTag, 'tool');
      const secretName = this.extractAttribute(actionTag, 'secretName');

      // No logging in production code
      
      actions.push({
        type,
        link,
        tool,
        content: actionContent,
        secretName
      });
      
      position = actionClose + ACTION_CLOSE.length;
    }
    
    // No logging in production code
    
    return actions;
  }
  
  private extractAttribute(tag: string, attrName: string): string | undefined {
    const regex = new RegExp(`${attrName}=["']([^"']*)["']`);
    const match = tag.match(regex);
    return match ? match[1] : undefined;
  }

  private finalizeActions(actions: ActionsStatus) {
    // Validate that the actions content is not empty
    if (!actions.content || actions.content.trim() === '') {
      // No logging in production code
      this.callbacks?.onActionsError?.('Empty or incomplete actions content');
      return;
    }

    // No logging in production code

    // Call onActionsComplete with the final actions metadata
    this.callbacks?.onActionsComplete?.(actions);
  }

  private getState(messageId: string) {
    if (!this.states.has(messageId)) {
      this.states.set(messageId, {
        position: 0,
        buffer: '',
      });
    }
    return this.states.get(messageId)!;
  }

  public resetState(messageId: string) {
    this.states.delete(messageId);
  }
}
