import { createHash } from 'crypto';

const MO_FILE_OPEN = '<MO_FILE';
const MO_FILE_CLOSE = '</MO_FILE>';
const HEADER_BUFFER_LIMIT = 2048; // Prevent memory attacks

export interface FileMeta {
    path: string;
    lang: string;
    content: string;
    buffer: string;
    mode: 'edit' | 'create' | 'fix' | 'move';
    type?: 'file' | 'function' | 'secret'; // Type of artifact
    approx_lines: number;
    total_lines?: number;
    percent_complete?: number; // Added percentage completion field

    // Move operation specific attribute
    source_path?: string; // Original path for move operations

    // Function-specific attributes
    entrypoint?: string; // Function entry point (e.g., 'handler')
    runtime?: string; // Runtime environment (e.g., 'edge', 'node')
    trigger?: string; // Event that triggers the function (e.g., 'auth.signup')
    verifyJwt?: string; // Whether to verify JWT ('true' or 'false')

    // Secret-specific attributes
    scope?: string; // Scope of the secret (e.g., 'project', 'function')
    environment?: string; // Environment for the secret (e.g., 'all', 'development', 'production')
}

interface ParserCallbacks {
    onFileStart?: (meta: Omit<FileMeta, 'buffer'>) => void;
    onFileDelta?: (path: string, chunk: string, meta?: FileMeta) => void;
    onFileComplete?: (meta: FileMeta) => void;
    onFileChunk?: (meta: FileMeta) => void;
    onDiffError?: (error: string) => void;
}

export class MOFileParser {
    private states = new Map<string, {
        position: number;
        currentFile?: FileMeta;
        headerBuffer: string;
    }>();

    constructor(private callbacks?: ParserCallbacks) {}

    parse(messageId: string, input: string): string {
        const state = this.getState(messageId);
        let output = '';
        let i = 0;

        while (i < input.length) {
            if (!state.currentFile) {
                const moStart = input.indexOf(MO_FILE_OPEN, i);
                if (moStart === -1) {
                    output += input.slice(i);
                    break;
                }

                output += input.slice(i, moStart);
                i = moStart;
                state.headerBuffer = '';
            }

            if (state.currentFile) {
                const moEnd = input.indexOf(MO_FILE_CLOSE, i);

                // Process the current chunk
                const chunk = moEnd === -1 ? input.slice(i) : input.slice(i, moEnd);

                // Count newlines properly using regex with global flag
                const newLines = (chunk.match(/\n/g) || []).length;
                state.currentFile.total_lines = (state.currentFile.total_lines || 0) + newLines;

                // Calculate percentage complete (capped at 100%)
                if (state.currentFile.approx_lines > 0) {
                    state.currentFile.percent_complete = Math.min(
                        100,
                        Math.round((state.currentFile.total_lines / state.currentFile.approx_lines) * 100)
                    );
                } else {
                    state.currentFile.percent_complete = 0;
                }

                // Add chunk to content
                state.currentFile.content += chunk;

                // Call onFileDelta with just this chunk for the current file
                this.callbacks?.onFileDelta?.(state.currentFile.path, chunk, state.currentFile);

                if (moEnd === -1) {
                    i = input.length;
                    break;
                }

                this.finalizeFile(state.currentFile);

                i = moEnd + MO_FILE_CLOSE.length;
                state.currentFile = undefined;
            } else {
                const headerEnd = input.indexOf('>', i);
                if (headerEnd === -1) {
                    state.headerBuffer += input.slice(i);
                    if (state.headerBuffer.length > HEADER_BUFFER_LIMIT) {
                        this.resetState(messageId);
                    }
                    break;
                }

                const fullHeader = state.headerBuffer + input.slice(i, headerEnd + 1);
                const fileMeta = this.parseHeader(fullHeader);
                if (fileMeta) {
                    // Initialize percent_complete to 0
                    fileMeta.percent_complete = 0;

                    state.currentFile = fileMeta;
                    this.callbacks?.onFileStart?.({
                        path: fileMeta.path,
                        lang: fileMeta.lang,
                        total_lines: fileMeta.total_lines || 0,
                        content: '',
                        mode: fileMeta.mode,
                        type: fileMeta.type || 'file',
                        approx_lines: fileMeta.approx_lines,
                        percent_complete: fileMeta.percent_complete,
                        // Function-specific attributes
                        entrypoint: fileMeta.entrypoint,
                        runtime: fileMeta.runtime,
                        trigger: fileMeta.trigger,
                        verifyJwt: fileMeta.verifyJwt,
                        // Secret-specific attributes
                        scope: fileMeta.scope,
                        environment: fileMeta.environment
                    });
                }

                i = headerEnd + 1;
                state.headerBuffer = '';
            }
        }
        return output;
    }

    private parseHeader(header: string): FileMeta | undefined {
        const attrRegex = /(\w+)=["']((?:\\"|[^"])*)["']/g;
        const attrs: Record<string, string> = {};

        let match: RegExpExecArray | null;
        while ((match = attrRegex.exec(header)) !== null) {
            attrs[match[1].toLowerCase()] = match[2].replace(/\\"/g, '"');
        }

        if (!attrs.path) {
            console.warn('MO_FILE missing path attribute');
            return undefined;
        }

        // Validate source_path for move operations
        if (attrs.mode === 'move' && !attrs.source_path) {
            console.warn('MO_FILE with mode="move" missing source_path attribute');
            return undefined;
        }

        // Remove leading slash from path if present
        const sanitizedPath = attrs.path.startsWith('/') ? attrs.path.substring(1) : attrs.path;

        // Log when path sanitization occurs
        if (attrs.path.startsWith('/')) {
            console.log(`Sanitized file path from '${attrs.path}' to '${sanitizedPath}'`);
        }

        // Also sanitize source_path if it exists and has a leading slash
        const sanitizedSourcePath = attrs.source_path && attrs.source_path.startsWith('/')
            ? attrs.source_path.substring(1)
            : attrs.source_path;

        // Log when source_path sanitization occurs
        if (attrs.source_path && attrs.source_path.startsWith('/')) {
            console.log(`Sanitized source path from '${attrs.source_path}' to '${sanitizedSourcePath}'`);
        }

        return {
            path: sanitizedPath,
            lang: attrs.lang || 'text',
            content: '',
            buffer: '',
            total_lines: 0,
            mode: attrs.mode as any || 'edit',
            type: attrs.type as 'file' | 'function' | 'secret' || 'file',
            approx_lines: parseInt(attrs.approx_lines || "0"),
            percent_complete: 0,
            // Move operation specific attribute
            source_path: sanitizedSourcePath,
            // Function-specific attributes
            entrypoint: attrs.entrypoint,
            runtime: attrs.runtime,
            trigger: attrs.trigger,
            verifyJwt: attrs.verifyjwt,
            // Secret-specific attributes
            scope: attrs.scope,
            environment: attrs.environment
        };
    }

    private finalizeFile(file: FileMeta) {
        // const actualHash = createHash('sha1').update(file.content).digest('hex');
        // if (file.expectedHash && actualHash !== file.expectedHash) {
        //     console.error(`Hash mismatch for ${file.path}`);
        //     return;
        // }

        // For move operations, we don't need to validate content
        if (file.mode === 'move') {
            // Ensure file is marked as 100% complete when finalized
            file.percent_complete = 100;

            this.callbacks?.onFileComplete?.({
                ...file,
            });
            return;
        }

        // Validate that the file content is not empty or incomplete
        if (!file.content || file.content.trim() === '') {
            console.error(`Empty or incomplete file content for ${file.path}`);
            this.callbacks?.onDiffError?.(`Empty or incomplete file content for ${file.path}`);
            return;
        }

        // Ensure file is marked as 100% complete when finalized
        file.percent_complete = 100;

        // Don't call onFileDelta here - it's now called incrementally in the parse method
        this.callbacks?.onFileComplete?.({
            ...file,
        });
    }

    private getState(messageId: string) {
        if (!this.states.has(messageId)) {
            this.states.set(messageId, {
                position: 0,
                headerBuffer: '',
            });
        }
        return this.states.get(messageId)!;
    }

    public resetState(messageId: string) {
        this.states.delete(messageId);
    }
}