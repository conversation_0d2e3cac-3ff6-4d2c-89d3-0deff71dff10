import { createHash } from 'crypto';

const MO_SQL_OPEN = '<MO_DATABASE_QUERY';
const MO_SQL_CLOSE = '</MO_DATABASE_QUERY>';
const HEADER_BUFFER_LIMIT = 2048;

interface SQLMeta {
    type: 'up' | 'down' | 'rls';
    source: string;
    table: string;
    description: string;
    query: string;
    buffer: string;
    dependencies: string[];
    comments: string[];
}

interface SQLParserCallbacks {
    onQueryStart?: (meta: Omit<SQLMeta, 'buffer' | 'query'>) => void;
    onQueryDelta?: (type: string, chunk: string) => void;
    onQueryComplete?: (meta: SQLMeta) => void;
}

export class SQLStreamParser {
    private states = new Map<string, {
        position: number;
        currentQuery?: SQLMeta;
        headerBuffer: string;
    }>();

    constructor(private callbacks?: SQLParserCallbacks) {}

    parse(messageId: string, input: string): string {
        const state = this.getState(messageId);
        let output = '';
        let i = 0;

        while (i < input.length) {
            if (!state.currentQuery) {
                const moStart = input.indexOf(MO_SQL_OPEN, i);
                if (moStart === -1) {
                    output += input.slice(i);
                    break;
                }

                output += input.slice(i, moStart);
                i = moStart;
                state.headerBuffer = '';
            }

            if (state.currentQuery) {
                const moEnd = input.indexOf(MO_SQL_CLOSE, i);
                if (moEnd === -1) {
                    state.currentQuery.query += input.slice(i);
                    i = input.length;
                    break;
                }

                state.currentQuery.query += input.slice(i, moEnd);
                this.finalizeQuery(state.currentQuery);

                i = moEnd + MO_SQL_CLOSE.length;
                state.currentQuery = undefined;
            } else {
                const headerEnd = input.indexOf('>', i);
                if (headerEnd === -1) {
                    state.headerBuffer += input.slice(i);
                    if (state.headerBuffer.length > HEADER_BUFFER_LIMIT) {
                        this.resetState(messageId);
                    }
                    break;
                }

                const fullHeader = state.headerBuffer + input.slice(i, headerEnd + 1);
                const sqlMeta = this.parseHeader(fullHeader);
                if (sqlMeta) {
                    state.currentQuery = sqlMeta;
                    this.callbacks?.onQueryStart?.({
                        type: sqlMeta.type,
                        source: sqlMeta.source,
                        table: sqlMeta.table,
                        description: sqlMeta.description,
                        dependencies: sqlMeta.dependencies,
                        comments: sqlMeta.comments
                    });
                }

                i = headerEnd + 1;
                state.headerBuffer = '';
            }
        }
        return output;
    }

    private parseHeader(header: string): SQLMeta | undefined {
        const attrRegex = /(\w+)=["']((?:\\"|[^"'])*)["']/g;
        const attrs: Record<string, string> = {};

        let match: RegExpExecArray | null;
        while ((match = attrRegex.exec(header)) !== null) {
            attrs[match[1].toLowerCase()] = match[2].replace(/\\"/g, '"');
        }

        if (!attrs.source) {
            console.warn('MO_DATABASE_QUERY missing source attribute');
            return undefined;
        }

        return {
            type: (attrs.type as 'up' | 'down' | 'rls') || 'up',
            source: attrs.source,
            table: attrs.table || '',
            description: attrs.description || '',
            query: '',
            buffer: '',
            dependencies: attrs.dependencies ? JSON.parse(attrs.dependencies) : [],
            comments: []
        };
    }

    private finalizeQuery(query: SQLMeta) {
        // Extract comments from the query
        const comments = this.extractComments(query.query);
        const queryWithComments = {
            ...query,
            comments
        };
        
        this.callbacks?.onQueryDelta?.(query.type, query.query);
        this.callbacks?.onQueryComplete?.(queryWithComments);
    }
    
    private extractComments(query: string): string[] {
        const comments: string[] = [];
        
        // Match SQL comments that start with -- Purpose: or -- This
        const commentRegex = /--\s*(Purpose|[Tt]his)[^\n]*(\n\s*--[^\n]*)*\n?/g;
        let match;
        
        while ((match = commentRegex.exec(query)) !== null) {
            // Clean up the comment by removing leading -- and trimming
            const commentBlock = match[0];
            const cleanedComment = commentBlock
                .split('\n')
                .map(line => line.replace(/^\s*--\s*/, '').trim())
                .filter(line => line.length > 0)
                .join('\n');
                
            if (cleanedComment) {
                comments.push(cleanedComment);
            }
        }
        
        return comments;
    }

    private getState(messageId: string) {
        if (!this.states.has(messageId)) {
            this.states.set(messageId, {
                position: 0,
                headerBuffer: '',
            });
        }
        return this.states.get(messageId)!;
    }

    public resetState(messageId: string) {
        this.states.delete(messageId);
    }
}
