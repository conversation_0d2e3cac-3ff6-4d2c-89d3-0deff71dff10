// Separate utility to track file metrics without modifying StreamParser
export class FileMetrics {
  private lineCountMap = new Map<string, number>();

  addChunk(filename: string, chunk: string) {
    const currentCount = this.lineCountMap.get(filename) || 0;
    const newLines = (chunk.match(/\n/g) || []).length;
    this.lineCountMap.set(filename, currentCount + newLines);
  }

  getLineCount(filename: string): number {
    return this.lineCountMap.get(filename) || 0;
  }

  reset(filename: string) {
    this.lineCountMap.delete(filename);
  }
}

// Singleton instance for global use
export const fileMetrics = new FileMetrics();
