import { createHash } from 'crypto';

const THINKING_OPEN = '<thinking>';
const THINKING_CLOSE = '</thinking>';
const HEADER_BUFFER_LIMIT = 2048; // Prevent memory attacks

export interface ThinkingMeta {
    content: string;
    isComplete: boolean;
    sections: ThinkingSection[];
}

export interface ThinkingSection {
    title: string;
    content: string;
    items?: ThinkingItem[];
}

export interface ThinkingItem {
    key: string;
    value: string;
}

interface ParserCallbacks {
    onThinkingStart?: () => void;
    onThinkingDelta?: (chunk: string, meta?: ThinkingMeta) => void;
    onThinkingComplete?: (meta: ThinkingMeta) => void;
    onThinkingError?: (error: string) => void;
}

export class ThinkingParser {
    private states = new Map<string, {
        position: number;
        currentThinking?: ThinkingMeta;
        buffer: string;
    }>();

    constructor(private callbacks?: ParserCallbacks) {}

    parse(messageId: string, input: string): string {
        const state = this.getState(messageId);
        let output = '';
        let i = 0;

        while (i < input.length) {
            if (!state.currentThinking) {
                const thinkingStart = input.indexOf(THINKING_OPEN, i);
                if (thinkingStart === -1) {
                    output += input.slice(i);
                    break;
                }

                output += input.slice(i, thinkingStart);
                i = thinkingStart + THINKING_OPEN.length;
                
                // Initialize thinking metadata
                state.currentThinking = {
                    content: '',
                    isComplete: false,
                    sections: []
                };
                
                // Notify that thinking has started
                this.callbacks?.onThinkingStart?.();
            } else {
                const thinkingEnd = input.indexOf(THINKING_CLOSE, i);

                // Process the current chunk
                const chunk = thinkingEnd === -1 ? input.slice(i) : input.slice(i, thinkingEnd);
                
                // Add chunk to content
                state.currentThinking.content += chunk;
                
                // Parse sections from the content
                state.currentThinking.sections = this.parseSections(state.currentThinking.content);
                
                // Call onThinkingDelta with the chunk and current thinking metadata
                this.callbacks?.onThinkingDelta?.(chunk, state.currentThinking);

                if (thinkingEnd === -1) {
                    i = input.length;
                    break;
                }

                // Thinking block is complete
                state.currentThinking.isComplete = true;
                this.finalizeThinking(state.currentThinking);

                i = thinkingEnd + THINKING_CLOSE.length;
                state.currentThinking = undefined;
            }
        }
        return output;
    }

    private parseSections(content: string): ThinkingSection[] {
        const sections: ThinkingSection[] = [];
        
        // Split content by numbered sections (e.g., "1. Problem Analysis")
        const sectionRegex = /(\d+)\.\s+([^\n]+)([\s\S]*?)(?=\d+\.\s+|$)/g;
        let match;
        
        while ((match = sectionRegex.exec(content)) !== null) {
            const [, number, title, sectionContent] = match;
            
            // Parse items in the section (e.g., "- **Core Problem:** [What is the user trying to solve?]")
            const items: ThinkingItem[] = [];
            const itemRegex = /-\s+\*\*([^:]+):\*\*\s+\[([^\]]+)\]/g;
            let itemMatch;
            
            while ((itemMatch = itemRegex.exec(sectionContent)) !== null) {
                const [, key, value] = itemMatch;
                items.push({ key: key.trim(), value: value.trim() });
            }
            
            sections.push({
                title: title.trim(),
                content: sectionContent.trim(),
                items: items.length > 0 ? items : undefined
            });
        }
        
        return sections;
    }

    private finalizeThinking(thinking: ThinkingMeta) {
        // Validate that the thinking content is not empty
        if (!thinking.content || thinking.content.trim() === '') {
            console.error('Empty or incomplete thinking content');
            this.callbacks?.onThinkingError?.('Empty or incomplete thinking content');
            return;
        }

        // Call onThinkingComplete with the final thinking metadata
        this.callbacks?.onThinkingComplete?.(thinking);
    }

    private getState(messageId: string) {
        if (!this.states.has(messageId)) {
            this.states.set(messageId, {
                position: 0,
                buffer: '',
            });
        }
        return this.states.get(messageId)!;
    }

    public resetState(messageId: string) {
        this.states.delete(messageId);
    }
}
