/**
 * Parser for extracting tool calls from streaming content and displaying them inline
 */

export interface ToolCallData {
  id: string;
  name: string;
  args: any;
  result?: any;
}

export interface ToolCallCallbackData {
  messageId: string;
  toolCall: ToolCallData;
}

export type ToolCallCallback = (data: ToolCallCallbackData) => void;

export interface ParserCallbacks {
  onToolCallStart?: ToolCallCallback;
  onToolCallStream?: ToolCallCallback;
  onToolCallEnd?: ToolCallCallback;
}

interface MessageState {
  position: number;
  insideToolCall: boolean;
  insideToolResult: boolean;
  currentToolCall?: ToolCallData;
  toolCallId: number;
}

const TOOL_CALL_TAG_OPEN = '```tool-call';
const TOOL_CALL_TAG_CLOSE = '```';
const TOOL_RESULT_TAG_OPEN = '```tool-result';
const TOOL_RESULT_TAG_CLOSE = '```';

export class InlineToolCallParser {
  #messages = new Map<string, MessageState>();

  constructor(private _options: { callbacks?: ParserCallbacks } = {}) {}

  parse(messageId: string, input: string): string {
    let state = this.#messages.get(messageId);

    if (!state) {
      state = {
        position: 0,
        insideToolCall: false,
        insideToolResult: false,
        toolCallId: 0,
      };

      this.#messages.set(messageId, state);
    }

    let output = input;
    let toolCallMatches: { start: number; end: number; content: string }[] = [];
    let toolResultMatches: { start: number; end: number; content: string; id: string }[] = [];

    // Find all tool call blocks
    let toolCallMatch;
    const toolCallRegex = new RegExp(`${TOOL_CALL_TAG_OPEN}\\s*\\n([\\s\\S]*?)\\n\\s*${TOOL_CALL_TAG_CLOSE}`, 'g');
    
    while ((toolCallMatch = toolCallRegex.exec(input)) !== null) {
      try {
        const content = toolCallMatch[1].trim();
        const toolCallData = JSON.parse(content);
        
        const toolCall: ToolCallData = {
          id: toolCallData.id || `tool-${Date.now()}-${state.toolCallId++}`,
          name: toolCallData.name,
          args: toolCallData.arguments ? JSON.parse(toolCallData.arguments) : {}
        };
        
        this._options.callbacks?.onToolCallStart?.({
          messageId,
          toolCall
        });
        
        toolCallMatches.push({
          start: toolCallMatch.index,
          end: toolCallMatch.index + toolCallMatch[0].length,
          content
        });
      } catch (error) {
        console.error('Error parsing tool call:', error);
      }
    }
    
    // Find all tool result blocks
    let toolResultMatch;
    const toolResultRegex = new RegExp(`${TOOL_RESULT_TAG_OPEN}\\s*\\n([\\s\\S]*?)\\n\\s*${TOOL_RESULT_TAG_CLOSE}`, 'g');
    
    while ((toolResultMatch = toolResultRegex.exec(input)) !== null) {
      try {
        const content = toolResultMatch[1].trim();
        const toolResultData = JSON.parse(content);
        
        const toolCallId = toolResultData.id || '';
        
        toolResultMatches.push({
          start: toolResultMatch.index,
          end: toolResultMatch.index + toolResultMatch[0].length,
          content,
          id: toolCallId
        });
        
        // Find the corresponding tool call
        const toolCall = {
          id: toolCallId,
          name: toolResultData.name,
          args: {},
          result: toolResultData.result
        };
        
        this._options.callbacks?.onToolCallEnd?.({
          messageId,
          toolCall
        });
      } catch (error) {
        console.error('Error parsing tool result:', error);
      }
    }
    
    // Replace tool calls and results with custom HTML
    // Process in reverse order to avoid index shifting
    const allMatches = [...toolCallMatches, ...toolResultMatches].sort((a, b) => b.start - a.start);
    
    for (const match of allMatches) {
      const isToolCall = !('id' in match);
      const replacement = isToolCall 
        ? `<span class="inline-tool-call" data-tool-call="${encodeURIComponent(match.content)}"></span>`
        : `<span class="inline-tool-result" data-tool-result="${encodeURIComponent(match.content)}"></span>`;
      
      output = output.substring(0, match.start) + replacement + output.substring(match.end);
    }
    
    state.position = input.length;
    return output;
  }

  reset() {
    this.#messages.clear();
  }
}
