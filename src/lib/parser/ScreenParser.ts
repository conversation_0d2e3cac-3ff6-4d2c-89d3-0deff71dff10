import { createHash } from 'crypto';

const SCREENS_OPEN = '<screens>';
const SCREENS_CLOSE = '</screens>';
const SCREEN_OPEN = '<screen';
const SCREEN_CLOSE = '</screen>';
const HEADER_BUFFER_LIMIT = 2048; // Prevent memory attacks

export interface ScreenMeta {
  name: string;
  mode: 'append' | 'replace' | 'delete';
  content: string;
  styles?: string;
}

interface ScreenParserCallbacks {
  onScreenStart?: (meta: Omit<ScreenMeta, 'content' | 'styles'>) => void;
  onScreenDelta?: (name: string, chunk: string) => void;
  onScreenComplete?: (meta: ScreenMeta) => void;
  onParseError?: (error: string) => void;
}

export class ScreenParser {
  private states = new Map<string, {
    position: number;
    inScreensTag: boolean;
    currentScreen?: {
      name: string;
      mode: 'append' | 'replace' | 'delete';
      content: string;
      inStyleTag: boolean;
      styles: string;
    };
    headerBuffer: string;
  }>();

  constructor(private callbacks?: ScreenParserCallbacks) {}

  parse(messageId: string, input: string): string {
    const state = this.getState(messageId);
    let output = '';
    let i = 0;

    while (i < input.length) {
      // Looking for <screens> tag
      if (!state.inScreensTag) {
        const screensStart = input.indexOf(SCREENS_OPEN, i);
        if (screensStart === -1) {
          output += input.slice(i);
          break;
        }

        output += input.slice(i, screensStart);
        i = screensStart + SCREENS_OPEN.length;
        state.inScreensTag = true;
        continue;
      }

      // Inside <screens> tag, looking for <screen> or </screens>
      if (state.inScreensTag && !state.currentScreen) {
        const screenStart = input.indexOf(SCREEN_OPEN, i);
        const screensEnd = input.indexOf(SCREENS_CLOSE, i);

        // </screens> comes first or no <screen> found
        if ((screensEnd !== -1 && (screenStart === -1 || screensEnd < screenStart))) {
          output += input.slice(i, screensEnd);
          i = screensEnd + SCREENS_CLOSE.length;
          state.inScreensTag = false;
          continue;
        }

        // <screen> tag found
        if (screenStart !== -1) {
          output += input.slice(i, screenStart);
          i = screenStart + SCREEN_OPEN.length;
          state.headerBuffer = SCREEN_OPEN;
          
          // Look for the closing > of the screen tag
          const headerEnd = input.indexOf('>', i);
          if (headerEnd === -1) {
            // Header is incomplete, buffer it
            state.headerBuffer += input.slice(i);
            if (state.headerBuffer.length > HEADER_BUFFER_LIMIT) {
              this.callbacks?.onParseError?.('Screen header too large');
              this.resetState(messageId);
            }
            break;
          }

          // Complete header found
          state.headerBuffer += input.slice(i, headerEnd + 1);
          const screenMeta = this.parseScreenHeader(state.headerBuffer);
          
          if (!screenMeta) {
            this.callbacks?.onParseError?.('Invalid screen header: ' + state.headerBuffer);
            i = headerEnd + 1;
            state.headerBuffer = '';
            continue;
          }

          // Initialize the current screen
          state.currentScreen = {
            name: screenMeta.name,
            mode: screenMeta.mode,
            content: '',
            inStyleTag: false,
            styles: ''
          };

          // Notify about screen start
          this.callbacks?.onScreenStart?.({
            name: screenMeta.name,
            mode: screenMeta.mode
          });

          i = headerEnd + 1;
          state.headerBuffer = '';
          continue;
        }

        // No complete tag found, move to the end
        output += input.slice(i);
        break;
      }

      // Inside a <screen> tag, collecting content
      if (state.currentScreen) {
        // Look for </screen> tag
        const screenEnd = input.indexOf(SCREEN_CLOSE, i);
        
        if (screenEnd === -1) {
          // Process the current chunk
          this.processScreenContent(state, input.slice(i), messageId);
          i = input.length;
          break;
        }

        // Process content up to the </screen> tag
        this.processScreenContent(state, input.slice(i, screenEnd), messageId);
        
        // Finalize the screen
        this.finalizeScreen(state.currentScreen, messageId);
        
        i = screenEnd + SCREEN_CLOSE.length;
        state.currentScreen = undefined;
      }
    }

    return output;
  }

  private processScreenContent(state: any, chunk: string, messageId: string) {
    if (!state.currentScreen) return;

    // Check for <style> and </style> tags
    if (!state.currentScreen.inStyleTag) {
      const styleStart = chunk.indexOf('<style>');
      if (styleStart !== -1) {
        // Add content before <style> to content
        state.currentScreen.content += chunk.substring(0, styleStart);
        
        // Notify about content update before style tag
        if (styleStart > 0) {
          this.callbacks?.onScreenDelta?.(state.currentScreen.name, chunk.substring(0, styleStart));
        }
        
        // Check if </style> is also in this chunk
        const styleEnd = chunk.indexOf('</style>', styleStart + 7);
        if (styleEnd !== -1) {
          // Both tags in same chunk
          state.currentScreen.styles += chunk.substring(styleStart + 7, styleEnd);
          
          // Add anything after </style> to content
          const afterStyle = chunk.substring(styleEnd + 8);
          state.currentScreen.content += afterStyle;
          
          // Notify about content update after style tag
          if (afterStyle.length > 0) {
            this.callbacks?.onScreenDelta?.(state.currentScreen.name, afterStyle);
          }
        } else {
          // Only opening tag, set flag and add style content
          state.currentScreen.inStyleTag = true;
          state.currentScreen.styles += chunk.substring(styleStart + 7);
        }
      } else {
        // No style tag, add to content
        state.currentScreen.content += chunk;
        this.callbacks?.onScreenDelta?.(state.currentScreen.name, chunk);
      }
    } else {
      // Already in a style tag, look for closing tag
      const styleEnd = chunk.indexOf('</style>');
      if (styleEnd !== -1) {
        // Add up to the closing tag to styles
        state.currentScreen.styles += chunk.substring(0, styleEnd);
        
        // Add anything after </style> to content
        const afterStyle = chunk.substring(styleEnd + 8);
        state.currentScreen.content += afterStyle;
        
        // Notify about content update after style tag
        if (afterStyle.length > 0) {
          this.callbacks?.onScreenDelta?.(state.currentScreen.name, afterStyle);
        }
        
        // Reset style flag
        state.currentScreen.inStyleTag = false;
      } else {
        // Still in style tag
        state.currentScreen.styles += chunk;
      }
    }
    
    // Trigger a delta update every time we process content, even if it's just style changes
    // This ensures we get frequent updates
    this.callbacks?.onScreenDelta?.(state.currentScreen.name, '');
  }

  private parseScreenHeader(header: string): { name: string; mode: 'append' | 'replace' | 'delete' } | undefined {
    const nameMatch = header.match(/name=["']([^"']*)["']/);
    const modeMatch = header.match(/mode=["']([^"']*)["']/);
    
    if (!nameMatch) {
      return undefined;
    }
    
    return {
      name: nameMatch[1],
      mode: (modeMatch?.[1] as 'append' | 'replace' | 'delete') || 'append'
    };
  }

  private finalizeScreen(screen: {
    name: string;
    mode: 'append' | 'replace' | 'delete';
    content: string;
    styles: string;
  }, messageId: string) {
    // Clean up content - preserve the content as is, don't remove wrappers
    let cleanContent = screen.content.trim();
    
    // Check if the content is a complete HTML document
    const isCompleteHtml = cleanContent.includes('<!DOCTYPE html>') || 
                          cleanContent.includes('<html') ||
                          cleanContent.includes('<body');
    
    // If it's not a complete HTML document, ensure it has proper layout structure
    if (!isCompleteHtml) {
      // Check if content is already a complete flex layout
      const hasFlex = cleanContent.includes('flex flex-col') || 
                      cleanContent.includes('flex-col') || 
                      cleanContent.includes('flex-1');
                      
      // If not a flex layout and doesn't have a mobile frame, wrap it
      if (!hasFlex && !cleanContent.includes('class="mobile-frame"') && !cleanContent.includes('class=\'mobile-frame\'')) {
        cleanContent = `<div class="flex flex-col h-full">${cleanContent}</div>`;
      }
    }
    
    this.callbacks?.onScreenComplete?.({
      name: screen.name,
      mode: screen.mode,
      content: cleanContent,
      styles: screen.styles.trim() || undefined
    });
  }

  public getState(messageId: string) {
    if (!this.states.has(messageId)) {
      this.states.set(messageId, {
        position: 0,
        inScreensTag: false,
        headerBuffer: '',
      });
    }
    return this.states.get(messageId)!;
  }

  public resetState(messageId: string) {
    this.states.delete(messageId);
  }
}
