/**
 * This file contains examples of using React Context for in-memory data persistence
 * to create feature-complete applications.
 */

export const CONTEXT_EXAMPLES = `
<context_examples>
  <todo_app_context_example>
    <MO_FILE lang="typescript" path="contexts/TodoContext.tsx" mode="create" approx_lines="80">
/**
 * @magic_component: TodoContext
 * @magic_platform: all
 * @magic_purpose: Provides state management for todo items with full CRUD operations
 * @magic_connects: TodoList,TodoForm,TodoItem
 * @magic_category: State/Todo
 * @magic_keywords: todo,context,state,crud,provider
 */
 // Notice all the imports are present. Any variable referenced from another file/modules/dependency MUST always be imported here.
       // Notice no duplicate imports are present. Any import can happen once and only once in the file.

import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the Todo item type
export interface Todo {
  id: string;
  text: string;
  completed: boolean;
  createdAt: number;
}

// Define the context value type
interface TodoContextType {
  // State
  todos: Todo[];
  loading: boolean;
  error: string | null;
  
  // CRUD operations
  addTodo: (text: string) => Promise<void>;
  toggleTodo: (id: string) => Promise<void>;
  updateTodo: (id: string, text: string) => Promise<void>;
  deleteTodo: (id: string) => Promise<void>;
  
  // Bulk operations
  clearCompleted: () => Promise<void>;
}

// Create the context with a default value
const TodoContext = createContext<TodoContextType | undefined>(undefined);

// Storage key
const STORAGE_KEY = 'todos';

// Provider component
export const TodoProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load todos from storage on mount
  useEffect(() => {
    const loadTodos = async () => {
      try {
        setLoading(true);
        const storedTodos = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedTodos) {
          setTodos(JSON.parse(storedTodos));
        }
      } catch (e) {
        setError('Failed to load todos');
        console.error(e);
      } finally {
        setLoading(false);
      }
    };

    loadTodos();
  }, []);

  // Save todos to storage whenever they change
  useEffect(() => {
    const saveTodos = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(todos));
      } catch (e) {
        setError('Failed to save todos');
        console.error(e);
      }
    };

    // Skip initial save when todos are empty and we're still loading
    if (!loading) {
      saveTodos();
    }
  }, [todos, loading]);

  // Add a new todo
  const addTodo = async (text: string) => {
    const newTodo: Todo = {
      id: Date.now().toString(),
      text,
      completed: false,
      createdAt: Date.now(),
    };
    
    setTodos((prevTodos) => [...prevTodos, newTodo]);
  };

  // Toggle a todo's completed status
  const toggleTodo = async (id: string) => {
    setTodos((prevTodos) =>
      prevTodos.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  };

  // Update a todo's text
  const updateTodo = async (id: string, text: string) => {
    setTodos((prevTodos) =>
      prevTodos.map((todo) =>
        todo.id === id ? { ...todo, text } : todo
      )
    );
  };

  // Delete a todo
  const deleteTodo = async (id: string) => {
    setTodos((prevTodos) => prevTodos.filter((todo) => todo.id !== id));
  };

  // Clear completed todos
  const clearCompleted = async () => {
    setTodos((prevTodos) => prevTodos.filter((todo) => !todo.completed));
  };

  // Context value
  const value: TodoContextType = {
    todos,
    loading,
    error,
    addTodo,
    toggleTodo,
    updateTodo,
    deleteTodo,
    clearCompleted,
  };

  return <TodoContext.Provider value={value}>{children}</TodoContext.Provider>;
};

// Custom hook for using the todo context
export const useTodos = (): TodoContextType => {
  const context = useContext(TodoContext);
  if (context === undefined) {
    throw new Error('useTodos must be used within a TodoProvider');
  }
  return context;
};
</MO_FILE>

    <MO_FILE lang="typescript" path="components/TodoItem.tsx" mode="create" approx_lines="60">
/**
 * @magic_component: TodoItem
 * @magic_platform: all
 * @magic_purpose: Displays a single todo item with complete and delete functionality
 * @magic_connects: TodoContext,TodoList
 * @magic_category: UI/Todo
 * @magic_keywords: todo,item,component,list,item
 */
 // Notice all the imports are present. Any variable referenced from another file/modules/dependency MUST always be imported here.
       // Notice no duplicate imports are present. Any import can happen once and only once in the file.

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTodos, Todo } from '../contexts/TodoContext';
import { COLORS, SPACING } from '../constants/theme';

interface TodoItemProps {
  todo: Todo;
}

const TodoItem: React.FC<TodoItemProps> = ({ todo }) => {
  const { toggleTodo, updateTodo, deleteTodo } = useTodos();
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(todo.text);

  const handleToggle = () => {
    toggleTodo(todo.id);
  };

  const handleDelete = () => {
    deleteTodo(todo.id);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (editText.trim()) {
      updateTodo(todo.id, editText.trim());
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditText(todo.text);
    setIsEditing(false);
  };

  return (
    <View style={styles.container}>
      {isEditing ? (
        <View style={styles.editContainer}>
          <TextInput
            style={styles.input}
            value={editText}
            onChangeText={setEditText}
            autoFocus
          />
          <TouchableOpacity onPress={handleSave} style={styles.iconButton}>
            <Feather name="check" size={20} color={COLORS.success} />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleCancel} style={styles.iconButton}>
            <Feather name="x" size={20} color={COLORS.error} />
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <TouchableOpacity onPress={handleToggle} style={styles.checkbox}>
            {todo.completed ? (
              <Feather name="check-square" size={20} color={COLORS.primary} />
            ) : (
              <Feather name="square" size={20} color={COLORS.textSecondary} />
            )}
          </TouchableOpacity>
          
          <Text
            style={[
              styles.text,
              todo.completed && styles.completedText,
            ]}
          >
            {todo.text}
          </Text>
          
          <TouchableOpacity onPress={handleEdit} style={styles.iconButton}>
            <Feather name="edit-2" size={18} color={COLORS.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity onPress={handleDelete} style={styles.iconButton}>
            <Feather name="trash-2" size={18} color={COLORS.error} />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.small,
    paddingHorizontal: SPACING.medium,
    backgroundColor: COLORS.cardBackground,
    borderRadius: 8,
    marginBottom: SPACING.small,
  },
  checkbox: {
    marginRight: SPACING.small,
  },
  text: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
  },
  completedText: {
    textDecorationLine: 'line-through',
    color: COLORS.textSecondary,
  },
  iconButton: {
    padding: SPACING.tiny,
    marginLeft: SPACING.small,
  },
  editContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.primary,
    paddingVertical: SPACING.tiny,
  },
});

export default TodoItem;
</MO_FILE>

    <MO_FILE lang="typescript" path="components/TodoForm.tsx" mode="create" approx_lines="50">
/**
 * @magic_component: TodoForm
 * @magic_platform: all
 * @magic_purpose: Form for adding new todo items
 * @magic_connects: TodoContext,TodoScreen
 * @magic_category: UI/Todo
 * @magic_keywords: todo,form,add,create,input
 */
 
 // Notice all the imports are present. Any variable referenced from another file/modules/dependency MUST always be imported here.
       // Notice no duplicate imports are present. Any import can happen once and only once in the file.

import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, StyleSheet } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTodos } from '../contexts/TodoContext';
import { COLORS, SPACING } from '../constants/theme';

const TodoForm: React.FC = () => {
  const [text, setText] = useState('');
  const { addTodo } = useTodos();

  const handleSubmit = () => {
    if (text.trim()) {
      addTodo(text.trim());
      setText('');
    }
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        placeholder="Add a new task..."
        placeholderTextColor={COLORS.textTertiary}
        value={text}
        onChangeText={setText}
        onSubmitEditing={handleSubmit}
        returnKeyType="done"
      />
      <TouchableOpacity
        style={[styles.button, !text.trim() && styles.buttonDisabled]}
        onPress={handleSubmit}
        disabled={!text.trim()}
      >
        <Feather name="plus" size={24} color={COLORS.white} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: SPACING.medium,
  },
  input: {
    flex: 1,
    height: 50,
    backgroundColor: COLORS.cardBackground,
    borderRadius: 8,
    paddingHorizontal: SPACING.medium,
    fontSize: 16,
    color: COLORS.text,
    marginRight: SPACING.small,
  },
  button: {
    width: 50,
    height: 50,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: COLORS.textTertiary,
  },
});

export default TodoForm;
</MO_FILE>

    <MO_FILE lang="typescript" path="screens/TodoScreen.tsx" mode="create" approx_lines="80">
/**
 * @magic_component: TodoScreen
 * @magic_platform: all
 * @magic_purpose: Main screen for todo app with list and form
 * @magic_connects: TodoContext,TodoForm,TodoList
 * @magic_category: Screens/Todo
 * @magic_keywords: todo,screen,main,list,app
 */
 // Notice all the imports are present. Any variable referenced from another file/modules/dependency MUST always be imported here.
       // Notice no duplicate imports are present. Any import can happen once and only once in the file.

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { TodoProvider, useTodos, Todo } from '../contexts/TodoContext';
import TodoItem from '../components/TodoItem';
import TodoForm from '../components/TodoForm';
import { COLORS, SPACING } from '../constants/theme';
import { KeyboardAvoidingView, Platform } from 'react-native';

// Inner component that uses the context
const TodoListContent: React.FC = () => {
  const { todos, loading, error, clearCompleted } = useTodos();

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  const completedCount = todos.filter(todo => todo.completed).length;
  const hasCompleted = completedCount > 0;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Todo List</Text>
        {hasCompleted && (
          <TouchableOpacity onPress={clearCompleted} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>Clear Completed</Text>
          </TouchableOpacity>
        )}
      </View>

      <TodoForm />

      {todos.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Feather name="clipboard" size={48} color={COLORS.textTertiary} />
          <Text style={styles.emptyText}>No tasks yet</Text>
          <Text style={styles.emptySubtext}>Add a task to get started</Text>
        </View>
      ) : (
        <FlatList
          data={todos}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => <TodoItem todo={item} />}
          style={styles.list}
        />
      )}
    </View>
  );
};

// Main component that provides the context
const TodoScreen: React.FC = () => {
  return (
    <TodoProvider>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <TodoListContent />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </TodoProvider>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: SPACING.medium,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.large,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  clearButton: {
    paddingVertical: SPACING.tiny,
    paddingHorizontal: SPACING.small,
  },
  clearButtonText: {
    color: COLORS.primary,
    fontWeight: '500',
  },
  list: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: COLORS.error,
    fontSize: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.textSecondary,
    marginTop: SPACING.medium,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textTertiary,
    marginTop: SPACING.tiny,
  },
});

export default TodoScreen;
</MO_FILE>
  </todo_app_context_example>

  <shopping_cart_context_example>
    <MO_FILE lang="typescript" path="contexts/CartContext.tsx" mode="create" approx_lines="80">
/**
 * @magic_component: CartContext
 * @magic_platform: all
 * @magic_purpose: Provides state management for shopping cart with persistence
 * @magic_connects: ProductCard,CartScreen,CartItem
 * @magic_category: State/Commerce
 * @magic_keywords: cart,shopping,context,state,commerce
 */
 // Notice all the imports are present. Any variable referenced from another file/modules/dependency MUST always be imported here.
       // Notice no duplicate imports are present. Any import can happen once and only once in the file.

import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define product type
export interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
}

// Define cart item type
export interface CartItem {
  product: Product;
  quantity: number;
}

// Define context value type
interface CartContextType {
  // State
  items: CartItem[];
  loading: boolean;
  error: string | null;
  
  // Cart operations
  addToCart: (product: Product, quantity?: number) => Promise<void>;
  removeFromCart: (productId: string) => Promise<void>;
  updateQuantity: (productId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  
  // Derived values
  totalItems: number;
  totalPrice: number;
}

// Create the context with a default value
const CartContext = createContext<CartContextType | undefined>(undefined);

// Storage key
const STORAGE_KEY = 'cart_items';

// Provider component
export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State
  const [items, setItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load cart from storage on mount
  useEffect(() => {
    const loadCart = async () => {
      try {
        setLoading(true);
        const storedCart = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedCart) {
          setItems(JSON.parse(storedCart));
        }
      } catch (e) {
        setError('Failed to load cart');
        console.error(e);
      } finally {
        setLoading(false);
      }
    };

    loadCart();
  }, []);

  // Save cart to storage whenever it changes
  useEffect(() => {
    const saveCart = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(items));
      } catch (e) {
        setError('Failed to save cart');
        console.error(e);
      }
    };

    // Skip initial save when cart is empty and we're still loading
    if (!loading) {
      saveCart();
    }
  }, [items, loading]);

  // Add a product to the cart
  const addToCart = async (product: Product, quantity: number = 1) => {
    setItems((prevItems) => {
      const existingItem = prevItems.find(
        (item) => item.product.id === product.id
      );

      if (existingItem) {
        // Update quantity if item already exists
        return prevItems.map((item) =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        // Add new item
        return [...prevItems, { product, quantity }];
      }
    });
  };

  // Remove a product from the cart
  const removeFromCart = async (productId: string) => {
    setItems((prevItems) =>
      prevItems.filter((item) => item.product.id !== productId)
    );
  };

  // Update the quantity of a cart item
  const updateQuantity = async (productId: string, quantity: number) => {
    if (quantity <= 0) {
      // Remove item if quantity is 0 or negative
      await removeFromCart(productId);
      return;
    }

    setItems((prevItems) =>
      prevItems.map((item) =>
        item.product.id === productId ? { ...item, quantity } : item
      )
    );
  };

  // Clear the cart
  const clearCart = async () => {
    setItems([]);
  };

  // Calculate total items
  const totalItems = items.reduce((total, item) => total + item.quantity, 0);

  // Calculate total price
  const totalPrice = items.reduce(
    (total, item) => total + item.product.price * item.quantity,
    0
  );

  // Context value
  const value: CartContextType = {
    items,
    loading,
    error,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    totalItems,
    totalPrice,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

// Custom hook for using the cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
</MO_FILE>
  </shopping_cart_context_example>

  <user_preferences_context_example>
    <MO_FILE lang="typescript" path="contexts/PreferencesContext.tsx" mode="create" approx_lines="60">
/**
 * @magic_component: PreferencesContext
 * @magic_platform: all
 * @magic_purpose: Manages user preferences like theme and notifications with persistence
 * @magic_connects: SettingsScreen,ThemeProvider,AppWrapper
 * @magic_category: State/Preferences
 * @magic_keywords: preferences,settings,theme,notifications,dark,mode
 */
 
import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ColorSchemeName, useColorScheme } from 'react-native';

// Define preferences type
interface Preferences {
  theme: 'light' | 'dark' | 'system';
  notificationsEnabled: boolean;
  soundsEnabled: boolean;
}

// Define context value type
interface PreferencesContextType {
  // State
  preferences: Preferences;
  loading: boolean;
  error: string | null;
  
  // Theme
  currentTheme: 'light' | 'dark';
  
  // Update methods
  setTheme: (theme: 'light' | 'dark' | 'system') => Promise<void>;
  toggleNotifications: () => Promise<void>;
  toggleSounds: () => Promise<void>;
  resetPreferences: () => Promise<void>;
}

// Default preferences
const DEFAULT_PREFERENCES: Preferences = {
  theme: 'system',
  notificationsEnabled: true,
  soundsEnabled: true,
};

// Create the context with a default value
const PreferencesContext = createContext<PreferencesContextType | undefined>(undefined);

// Storage key
const STORAGE_KEY = 'user_preferences';

// Provider component
export const PreferencesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State
  const [preferences, setPreferences] = useState<Preferences>(DEFAULT_PREFERENCES);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get system theme
  const systemTheme = useColorScheme() as 'light' | 'dark';
  
  // Determine current theme based on preferences
  const currentTheme = preferences.theme === 'system' ? systemTheme : preferences.theme;

  // Load preferences from storage on mount
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        setLoading(true);
        const storedPreferences = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedPreferences) {
          setPreferences(JSON.parse(storedPreferences));
        }
      } catch (e) {
        setError('Failed to load preferences');
        console.error(e);
      } finally {
        setLoading(false);
      }
    };

    loadPreferences();
  }, []);

  // Save preferences to storage whenever they change
  useEffect(() => {
    const savePreferences = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
      } catch (e) {
        setError('Failed to save preferences');
        console.error(e);
      }
    };

    // Skip initial save when we're still loading
    if (!loading) {
      savePreferences();
    }
  }, [preferences, loading]);

  // Update theme
  const setTheme = async (theme: 'light' | 'dark' | 'system') => {
    setPreferences((prev) => ({ ...prev, theme }));
  };

  // Toggle notifications
  const toggleNotifications = async () => {
    setPreferences((prev) => ({
      ...prev,
      notificationsEnabled: !prev.notificationsEnabled,
    }));
  };

  // Toggle sounds
  const toggleSounds = async () => {
    setPreferences((prev) => ({
      ...prev,
      soundsEnabled: !prev.soundsEnabled,
    }));
  };

  // Reset preferences to defaults
  const resetPreferences = async () => {
    setPreferences(DEFAULT_PREFERENCES);
  };

  // Context value
  const value: PreferencesContextType = {
    preferences,
    loading,
    error,
    currentTheme,
    setTheme,
    toggleNotifications,
    toggleSounds,
    resetPreferences,
  };

  return <PreferencesContext.Provider value={value}>{children}</PreferencesContext.Provider>;
};

// Custom hook for using the preferences context
export const usePreferences = (): PreferencesContextType => {
  const context = useContext(PreferencesContext);
  if (context === undefined) {
    throw new Error('usePreferences must be used within a PreferencesProvider');
  }
  return context;
};
</MO_FILE>
  </user_preferences_context_example>
</context_examples>
`;

export default CONTEXT_EXAMPLES;
