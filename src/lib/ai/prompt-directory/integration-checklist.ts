export const INTEGRATION_CHECKLIST = `
<MANDATORY_INTEGRATION_CHECKLIST>
  Before submitting ANY code, verify that:

  <theme_imports>
    1. EVERY file that uses styling imports ALL required theme constants:
       - import { COLORS, SPACING, T<PERSON><PERSON><PERSON><PERSON>HY, B<PERSON><PERSON>R_RADIUS, SHADOWS } from '../constants/theme';
    2. EVERY style property uses theme constants, NEVER hardcoded values
    3. Theme constants are imported from the correct relative path
  </theme_imports>

  <context_providers>
    1. Context providers are implemented BEFORE components that use them
    2. ALL functions used by components are exported from context providers
    3. Context provider values include ALL functions referenced by components
    4. Example: If a component uses getFilmById, the context MUST export this function
  </context_providers>
    
   <zustand_stores>
    1. Zustand stores are implemented BEFORE components that use them
    2. ALL functions used by components are exported from zustand stores
    3. Store values include ALL functions referenced by components
    4. Example: If a component uses getFilmById, the zustand store MUST export this function
  </zustand_stores>

  <component_integration>
    1. ALL props used in a component are passed from its parent
    2. ALL required props have default values or are properly validated
    3. ALL onPress handlers are properly defined and bound
    4. Navigation is properly connected between screens
  </component_integration>

  <data_flow>
    1. Data access patterns are CONSISTENT throughout the app
    2. State updates properly trigger re-renders where needed
    3. Data persistence is properly implemented where required
  </data_flow>

  <file_structure>
    1. MINIMIZE the number of files - fewer files = fewer integration errors
    2. Keep related functionality in the SAME file when possible
    3. Use CONSISTENT naming patterns across the codebase
  </file_structure>

  <error_prevention>
    MENTALLY walk through each user interaction in the main flow:
    1. Does each screen render correctly?
    2. Does each button have a proper onPress handler?
    3. Does each navigation link go to the correct screen?
    4. Are all required functions defined and exported?
    5. Are all theme constants imported and used correctly?
  </error_prevention>
</MANDATORY_INTEGRATION_CHECKLIST>
`;

export default INTEGRATION_CHECKLIST;
