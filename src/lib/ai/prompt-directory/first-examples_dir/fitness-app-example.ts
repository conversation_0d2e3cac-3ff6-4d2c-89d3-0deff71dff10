import { COMMON_DEFINITIONS } from './common-definitions';

export const FITNESS_APP_EXAMPLE = `
${COMMON_DEFINITIONS}

<fitness_app_example>
  <thinking>
    I need to create a fitness tracking app with a distinctive design. The app should:
    1. Track workouts with a timer
    2. Show workout history and progress
    3. Allow users to create custom workouts
    
    For the design, I'll use a bold, energetic aesthetic with:
    - A dark background with vibrant accent colors
    - Strong typography with dramatic size contrasts
    - Card-based UI with subtle shadows and rounded corners
    
    The primary flow will include:
    1. Home screen with workout options and progress summary
    2. Active workout screen with timer and exercise details
    3. Workout completion screen with results and sharing options
    
    I'll ensure all theme constants are properly imported in every file.
  </thinking>

  <MO_FILE lang="typescript" path="constants/theme.ts" mode="create" approx_lines="120">
// Theme constants for the fitness app
// This file contains all design tokens used throughout the app

export const COLORS = {
  // Base colors
  background: "#121212",
  backgroundAlt: "#1E1E1E",
  text: "#FFFFFF",
  textSecondary: "#B3B3B3",
  
  // Primary palette
  primary: "#FF4757",
  primaryDark: "#CC3A46",
  primaryLight: "#FF6B7A",
  
  // Secondary palette
  secondary: "#3498DB",
  secondaryLight: "#5DADE2",
  accent: "#2ECC71",
  
  // Semantic colors
  success: "#2ECC71",
  warning: "#F39C12",
  error: "#E74C3C",
  info: "#3498DB",
  
  // UI elements
  card: "#1E1E1E",
  cardAlt: "#252525",
  border: "#333333",
  divider: "#333333",
  overlay: "rgba(0, 0, 0, 0.7)",
};

export const SPACING = {
  base: 4,
  xxs: 2,
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const TYPOGRAPHY = {
  display: {
    fontSize: 40,
    fontWeight: "700",
    letterSpacing: -1,
    lineHeight: 48,
  },
  h1: {
    fontSize: 32,
    fontWeight: "700",
    letterSpacing: -0.5,
    lineHeight: 38,
  },
  h2: {
    fontSize: 24,
    fontWeight: "600",
    letterSpacing: -0.3,
    lineHeight: 30,
  },
  h3: {
    fontSize: 20,
    fontWeight: "600",
    letterSpacing: -0.2,
    lineHeight: 26,
  },
  body: {
    fontSize: 16,
    fontWeight: "400",
    letterSpacing: 0.1,
    lineHeight: 24,
  },
  bodySmall: {
    fontSize: 14,
    fontWeight: "400",
    letterSpacing: 0.1,
    lineHeight: 20,
  },
  button: {
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: 0.2,
    lineHeight: 24,
  },
  caption: {
    fontSize: 12,
    fontWeight: "500",
    letterSpacing: 0.3,
    lineHeight: 16,
  },
};

export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 16,
  xl: 24,
  pill: 9999,
};

export const SHADOWS = {
  none: {
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  subtle: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  medium: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  strong: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
};
</MO_FILE>

  <MO_FILE lang="typescript" path="components/Button.tsx" mode="create" approx_lines="80">
import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  StyleProp, 
  ViewStyle, 
  TextStyle 
} from 'react-native';
import { COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '../constants/theme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  icon?: React.ReactNode;
}

export const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
}: ButtonProps) => {
  
  const getBackgroundColor = () => {
    if (disabled) return COLORS.backgroundAlt;
    
    switch (variant) {
      case 'primary': return COLORS.primary;
      case 'secondary': return COLORS.secondary;
      case 'outline': return 'transparent';
      case 'text': return 'transparent';
      default: return COLORS.primary;
    }
  };
  
  const getTextColor = () => {
    if (disabled) return COLORS.textSecondary;
    
    switch (variant) {
      case 'primary': return COLORS.text;
      case 'secondary': return COLORS.text;
      case 'outline': return COLORS.primary;
      case 'text': return COLORS.primary;
      default: return COLORS.text;
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        styles[size],
        { backgroundColor: getBackgroundColor() },
        variant === 'outline' && styles.outlineButton,
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      {loading ? (
        <ActivityIndicator color={getTextColor()} size="small" />
      ) : (
        <>
          {icon}
          <Text style={[
            styles.text, 
            styles[\`\${size}Text\`],
            { color: getTextColor() },
            textStyle
          ]}>
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.subtle,
  },
  outlineButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  small: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.md,
  },
  medium: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
  },
  large: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.xl,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.6,
  },
  text: {
    ...TYPOGRAPHY.button,
    textAlign: 'center',
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
});
</MO_FILE>

  <MO_FILE lang="typescript" path="mocks/workouts.ts" mode="create" approx_lines="70">
// Mock data for workouts

export interface Exercise {
  id: string;
  name: string;
  duration: number; // in seconds
  sets?: number;
  reps?: number;
  restTime?: number; // in seconds
  imageUrl?: string;
}

export interface Workout {
  id: string;
  title: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // total duration in minutes
  caloriesBurn: number; // estimated calories
  category: string;
  exercises: Exercise[];
  imageUrl: string;
  isFeatured?: boolean;
}

export const mockExercises: Exercise[] = [
  {
    id: 'ex1',
    name: 'Push-ups',
    duration: 60,
    sets: 3,
    reps: 12,
    restTime: 30,
    imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500&q=80',
  },
  {
    id: 'ex2',
    name: 'Squats',
    duration: 60,
    sets: 3,
    reps: 15,
    restTime: 30,
    imageUrl: 'https://images.unsplash.com/photo-1574680096145-d05b474e2155?w=500&q=80',
  },
  {
    id: 'ex3',
    name: 'Plank',
    duration: 45,
    sets: 3,
    restTime: 30,
    imageUrl: 'https://images.unsplash.com/photo-1566241142559-40a9552c8a76?w=500&q=80',
  },
  {
    id: 'ex4',
    name: 'Lunges',
    duration: 60,
    sets: 3,
    reps: 10,
    restTime: 30,
    imageUrl: 'https://images.unsplash.com/photo-1434608519344-49d01f1591b0?w=500&q=80',
  },
  {
    id: 'ex5',
    name: 'Mountain Climbers',
    duration: 45,
    sets: 3,
    restTime: 30,
    imageUrl: 'https://images.unsplash.com/photo-1552674605-db6ffd4facb5?w=500&q=80',
  },
];

export const mockWorkouts: Workout[] = [
  {
    id: 'w1',
    title: 'Full Body Burn',
    description: 'A complete workout targeting all major muscle groups',
    level: 'intermediate',
    duration: 30,
    caloriesBurn: 350,
    category: 'strength',
    exercises: [mockExercises[0], mockExercises[1], mockExercises[2], mockExercises[4]],
    imageUrl: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=500&q=80',
    isFeatured: true,
  },
  {
    id: 'w2',
    title: 'HIIT Cardio',
    description: 'High intensity interval training to boost your metabolism',
    level: 'advanced',
    duration: 20,
    caloriesBurn: 400,
    category: 'cardio',
    exercises: [mockExercises[4], mockExercises[3], mockExercises[0]],
    imageUrl: 'https://images.unsplash.com/photo-1549060279-7e168fcee0c2?w=500&q=80',
    isFeatured: true,
  },
  {
    id: 'w3',
    title: 'Beginner Basics',
    description: 'Perfect for those just starting their fitness journey',
    level: 'beginner',
    duration: 25,
    caloriesBurn: 200,
    category: 'strength',
    exercises: [mockExercises[0], mockExercises[1], mockExercises[2]],
    imageUrl: 'https://images.unsplash.com/photo-1571731956672-f2b94d7dd0cb?w=500&q=80',
  },
];
</MO_FILE>

  <MO_FILE lang="typescript" path="screens/HomeScreen.tsx" mode="create" approx_lines="150">
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Feather } from '@expo/vector-icons';
import { Button } from '../components/Button';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../constants/theme';
import { mockWorkouts, Workout } from '../mocks/workouts';

const { width } = Dimensions.get('window');

const HomeScreen = () => {
  const navigation = useNavigation();
  const [featuredWorkouts, setFeaturedWorkouts] = useState<Workout[]>([]);
  const [recentWorkouts, setRecentWorkouts] = useState<Workout[]>([]);
  
  useEffect(() => {
    // Load featured workouts
    setFeaturedWorkouts(mockWorkouts.filter(workout => workout.isFeatured));
    
    // Load recent workouts (would come from storage in a real app)
    setRecentWorkouts(mockWorkouts.slice(0, 2));
  }, []);
  
  const handleWorkoutPress = (workout: Workout) => {
    // @ts-ignore - Navigation typing would be properly set up in a real app
    navigation.navigate('WorkoutDetail', { workoutId: workout.id });
  };
  
  const handleStartQuickWorkout = () => {
    // @ts-ignore - Navigation typing would be properly set up in a real app
    navigation.navigate('ActiveWorkout', { workoutId: mockWorkouts[0].id });
  };
  
  const renderWorkoutCard = (workout: Workout) => (
    <TouchableOpacity
      key={workout.id}
      style={styles.workoutCard}
      onPress={() => handleWorkoutPress(workout)}
    >
      <Image 
        source={{ uri: workout.imageUrl }} 
        style={styles.workoutImage}
      />
      <View style={styles.workoutInfo}>
        <View style={styles.workoutHeader}>
          <Text style={styles.workoutTitle}>{workout.title}</Text>
          <View style={styles.levelBadge}>
            <Text style={styles.levelText}>{workout.level}</Text>
          </View>
        </View>
        <View style={styles.workoutStats}>
          <View style={styles.statItem}>
            <Feather name="clock" size={14} color={COLORS.textSecondary} />
            <Text style={styles.statText}>{workout.duration} min</Text>
          </View>
          <View style={styles.statItem}>
            <Feather name="zap" size={14} color={COLORS.textSecondary} />
            <Text style={styles.statText}>{workout.caloriesBurn} cal</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello, Fitness Enthusiast!</Text>
            <Text style={styles.subtitle}>Ready for your workout?</Text>
          </View>
          <TouchableOpacity style={styles.profileButton}>
            <Feather name="user" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>
        
        {/* Quick Start */}
        <View style={styles.quickStartSection}>
          <Button 
            title="Start Quick Workout" 
            onPress={handleStartQuickWorkout}
            variant="primary"
            fullWidth
            icon={<Feather name="play" size={18} color={COLORS.text} style={{ marginRight: SPACING.sm }} />}
          />
        </View>
        
        {/* Featured Workouts */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Workouts</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredContainer}
          >
            {featuredWorkouts.map(renderWorkoutCard)}
          </ScrollView>
        </View>
        
        {/* Recent Workouts */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Workouts</Text>
          </View>
          {recentWorkouts.length > 0 ? (
            recentWorkouts.map(renderWorkoutCard)
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No recent workouts</Text>
              <Button 
                title="Browse Workouts" 
                onPress={() => {}}
                variant="outline"
                size="small"
                style={{ marginTop: SPACING.md }}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  greeting: {
    ...TYPOGRAPHY.h2,
    color: COLORS.text,
  },
  subtitle: {
    ...TYPOGRAPHY.bodySmall,
    color: COLORS.textSecondary,
    marginTop: SPACING.xxs,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.pill,
    backgroundColor: COLORS.cardAlt,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickStartSection: {
    marginBottom: SPACING.xl,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
  },
  seeAllText: {
    ...TYPOGRAPHY.bodySmall,
    color: COLORS.primary,
  },
  featuredContainer: {
    paddingRight: SPACING.md,
  },
  workoutCard: {
    width: width * 0.7,
    backgroundColor: COLORS.card,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    marginRight: SPACING.md,
    ...SHADOWS.medium,
  },
  workoutImage: {
    width: '100%',
    height: 120,
  },
  workoutInfo: {
    padding: SPACING.md,
  },
  workoutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  workoutTitle: {
    ...TYPOGRAPHY.body,
    fontWeight: '600',
    color: COLORS.text,
    flex: 1,
  },
  levelBadge: {
    backgroundColor: COLORS.primaryDark,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xxs,
    borderRadius: BORDER_RADIUS.pill,
  },
  levelText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.text,
    textTransform: 'capitalize',
  },
  workoutStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  statText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  emptyState: {
    backgroundColor: COLORS.card,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
  },
});

export default HomeScreen;
</MO_FILE>

  <MO_FILE lang="typescript" path="App.tsx" mode="create" approx_lines="25">
import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import HomeScreen from './screens/HomeScreen';
import { COLORS } from './constants/theme';

// In a real app, these would be implemented
// import WorkoutDetailScreen from './screens/WorkoutDetailScreen';
// import ActiveWorkoutScreen from './screens/ActiveWorkoutScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <SafeAreaProvider>
      <StatusBar style="light" />
      <NavigationContainer>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            contentStyle: { backgroundColor: COLORS.background }
          }}
        >
          <Stack.Screen name="Home" component={HomeScreen} />
          {/* Additional screens would be added here */}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
</MO_FILE>
</fitness_app_example>
`;
