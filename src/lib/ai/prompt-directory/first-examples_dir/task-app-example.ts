import { COMMON_DEFINITIONS } from './common-definitions';

export const TASK_APP_EXAMPLE = `
${COMMON_DEFINITIONS}

<task_app_example>
  <thinking>
    I need to create a task management app with a minimalist design. The app should:
    1. Allow users to create, edit, and complete tasks
    2. Organize tasks by projects or categories
    3. Track task completion and show progress
    
    For the design, I'll use a minimalist approach with:
    - Clean interface with ample white space
    - Monochromatic color scheme with one accent color
    - Typography-focused UI with minimal decorative elements
    
    The primary flow will include:
    1. Home screen with task lists and progress overview
    2. Task detail screen for viewing and editing tasks
    3. New task creation screen
    
    I'll ensure all theme constants are properly imported in every file and focus on data persistence.
  </thinking>

  <MO_FILE lang="typescript" path="constants/theme.ts" mode="create" approx_lines="120">
// Theme constants for the task management app
// This file contains all design tokens used throughout the app

export const COLORS = {
  // Base colors - monochromatic palette
  background: "#FFFFFF",
  backgroundAlt: "#F8F9FA",
  text: "#212529",
  textSecondary: "#6C757D",
  textTertiary: "#ADB5BD",
  
  // Primary accent color with variations
  primary: "#3B82F6", // Blue
  primaryDark: "#2563EB",
  primaryLight: "#60A5FA",
  
  // Semantic colors
  success: "#10B981", // Green
  warning: "#F59E0B", // Amber
  error: "#EF4444", // Red
  info: "#3B82F6", // Blue
  
  // UI elements
  card: "#FFFFFF",
  border: "#E9ECEF",
  divider: "#E9ECEF",
  overlay: "rgba(0, 0, 0, 0.5)",
  
  // Task priority colors
  priorityHigh: "#EF4444",
  priorityMedium: "#F59E0B",
  priorityLow: "#10B981",
};

export const SPACING = {
  base: 4,
  xxs: 2,
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const TYPOGRAPHY = {
  display: {
    fontSize: 32,
    fontWeight: "700",
    letterSpacing: -0.5,
    lineHeight: 40,
  },
  h1: {
    fontSize: 28,
    fontWeight: "700",
    letterSpacing: -0.5,
    lineHeight: 36,
  },
  h2: {
    fontSize: 24,
    fontWeight: "600",
    letterSpacing: -0.3,
    lineHeight: 32,
  },
  h3: {
    fontSize: 20,
    fontWeight: "600",
    letterSpacing: -0.2,
    lineHeight: 28,
  },
  body: {
    fontSize: 16,
    fontWeight: "400",
    letterSpacing: 0,
    lineHeight: 24,
  },
  bodySmall: {
    fontSize: 14,
    fontWeight: "400",
    letterSpacing: 0,
    lineHeight: 20,
  },
  button: {
    fontSize: 16,
    fontWeight: "500",
    letterSpacing: 0,
    lineHeight: 24,
  },
  caption: {
    fontSize: 12,
    fontWeight: "400",
    letterSpacing: 0,
    lineHeight: 16,
  },
};

export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  pill: 9999,
};

export const SHADOWS = {
  none: {
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  subtle: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  medium: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 4,
    elevation: 2,
  },
  strong: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
};
</MO_FILE>

  <MO_FILE lang="typescript" path="components/Button.tsx" mode="create" approx_lines="80">
import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  StyleProp, 
  ViewStyle, 
  TextStyle 
} from 'react-native';
import { COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '../constants/theme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  icon?: React.ReactNode;
}

export const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
}: ButtonProps) => {
  
  const getBackgroundColor = () => {
    if (disabled) return COLORS.backgroundAlt;
    
    switch (variant) {
      case 'primary': return COLORS.primary;
      case 'secondary': return COLORS.backgroundAlt;
      case 'outline': return 'transparent';
      case 'text': return 'transparent';
      default: return COLORS.primary;
    }
  };
  
  const getTextColor = () => {
    if (disabled) return COLORS.textTertiary;
    
    switch (variant) {
      case 'primary': return COLORS.background;
      case 'secondary': return COLORS.text;
      case 'outline': return COLORS.primary;
      case 'text': return COLORS.primary;
      default: return COLORS.background;
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        styles[size],
        { backgroundColor: getBackgroundColor() },
        variant === 'outline' && styles.outlineButton,
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      {loading ? (
        <ActivityIndicator color={getTextColor()} size="small" />
      ) : (
        <>
          {icon}
          <Text style={[
            styles.text, 
            styles[\`\${size}Text\`],
            { color: getTextColor() },
            textStyle
          ]}>
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.subtle,
  },
  outlineButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  small: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.md,
  },
  medium: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
  },
  large: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.xl,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.6,
  },
  text: {
    ...TYPOGRAPHY.button,
    textAlign: 'center',
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
});
</MO_FILE>

  <MO_FILE lang="typescript" path="mocks/tasks.ts" mode="create" approx_lines="80">
// Mock data for tasks with persistence capabilities

import AsyncStorage from '@react-native-async-storage/async-storage';

export type TaskPriority = 'high' | 'medium' | 'low';
export type TaskStatus = 'todo' | 'in_progress' | 'completed';

export interface Task {
  id: string;
  title: string;
  description?: string;
  dueDate?: string; // ISO date string
  priority: TaskPriority;
  status: TaskStatus;
  projectId?: string;
  tags?: string[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface Project {
  id: string;
  name: string;
  color: string;
  taskCount?: number;
}

// Initial mock data
export const mockProjects: Project[] = [
  {
    id: 'p1',
    name: 'Personal',
    color: '#3B82F6', // Blue
  },
  {
    id: 'p2',
    name: 'Work',
    color: '#10B981', // Green
  },
  {
    id: 'p3',
    name: 'Shopping',
    color: '#F59E0B', // Amber
  },
];

export const mockTasks: Task[] = [
  {
    id: 't1',
    title: 'Complete project proposal',
    description: 'Finish the draft and send it to the team for review',
    dueDate: new Date(Date.now() + 86400000 * 2).toISOString(), // 2 days from now
    priority: 'high',
    status: 'in_progress',
    projectId: 'p2',
    tags: ['work', 'urgent'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 't2',
    title: 'Buy groceries',
    description: 'Milk, eggs, bread, fruits, vegetables',
    dueDate: new Date(Date.now() + 86400000).toISOString(), // 1 day from now
    priority: 'medium',
    status: 'todo',
    projectId: 'p3',
    tags: ['shopping', 'personal'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 't3',
    title: 'Morning run',
    description: '5km run in the park',
    dueDate: new Date().toISOString(), // Today
    priority: 'low',
    status: 'todo',
    projectId: 'p1',
    tags: ['health', 'exercise'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 't4',
    title: 'Read book chapter',
    description: 'Chapter 5 of "Atomic Habits"',
    priority: 'medium',
    status: 'completed',
    projectId: 'p1',
    tags: ['personal', 'learning'],
    createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    updatedAt: new Date().toISOString(),
  },
];

// Storage keys
const TASKS_STORAGE_KEY = 'tasks';
const PROJECTS_STORAGE_KEY = 'projects';

// Data persistence functions
export const saveTasks = async (tasks: Task[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(TASKS_STORAGE_KEY, JSON.stringify(tasks));
  } catch (error) {
    console.error('Error saving tasks:', error);
  }
};

export const loadTasks = async (): Promise<Task[]> => {
  try {
    const tasksJson = await AsyncStorage.getItem(TASKS_STORAGE_KEY);
    return tasksJson ? JSON.parse(tasksJson) : mockTasks;
  } catch (error) {
    console.error('Error loading tasks:', error);
    return mockTasks;
  }
};

export const saveProjects = async (projects: Project[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(PROJECTS_STORAGE_KEY, JSON.stringify(projects));
  } catch (error) {
    console.error('Error saving projects:', error);
  }
};

export const loadProjects = async (): Promise<Project[]> => {
  try {
    const projectsJson = await AsyncStorage.getItem(PROJECTS_STORAGE_KEY);
    return projectsJson ? JSON.parse(projectsJson) : mockProjects;
  } catch (error) {
    console.error('Error loading projects:', error);
    return mockProjects;
  }
};
</MO_FILE>

  <MO_FILE lang="typescript" path="screens/HomeScreen.tsx" mode="create" approx_lines="200">
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  FlatList,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Feather } from '@expo/vector-icons';
import { Button } from '../components/Button';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../constants/theme';
import { 
  Task, 
  TaskStatus, 
  TaskPriority, 
  Project, 
  mockTasks, 
  mockProjects,
  loadTasks,
  loadProjects,
  saveTasks
} from '../mocks/tasks';

const { width } = Dimensions.get('window');

const HomeScreen = () => {
  const navigation = useNavigation();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<TaskStatus>('todo');
  
  useEffect(() => {
    // Load data on component mount
    const loadData = async () => {
      const loadedTasks = await loadTasks();
      const loadedProjects = await loadProjects();
      setTasks(loadedTasks);
      setProjects(loadedProjects);
    };
    
    loadData();
  }, []);
  
  // Calculate task statistics
  const taskStats = {
    todo: tasks.filter(task => task.status === 'todo').length,
    in_progress: tasks.filter(task => task.status === 'in_progress').length,
    completed: tasks.filter(task => task.status === 'completed').length,
    total: tasks.length,
  };
  
  // Get filtered tasks based on selected status
  const filteredTasks = tasks.filter(task => task.status === selectedStatus);
  
  const getProjectById = (projectId?: string): Project | undefined => {
    return projectId ? projects.find(project => project.id === projectId) : undefined;
  };
  
  const handleTaskPress = (task: Task) => {
    // @ts-ignore - Navigation typing would be properly set up in a real app
    navigation.navigate('TaskDetail', { taskId: task.id });
  };
  
  const handleAddTask = () => {
    // @ts-ignore - Navigation typing would be properly set up in a real app
    navigation.navigate('NewTask');
  };
  
  const handleStatusChange = (status: TaskStatus) => {
    setSelectedStatus(status);
  };
  
  const handleTaskStatusToggle = (taskId: string) => {
    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        const newStatus: TaskStatus = task.status === 'completed' ? 'todo' : 'completed';
        return { ...task, status: newStatus, updatedAt: new Date().toISOString() };
      }
      return task;
    });
    
    setTasks(updatedTasks);
    saveTasks(updatedTasks);
  };
  
  const getPriorityColor = (priority: TaskPriority): string => {
    switch (priority) {
      case 'high': return COLORS.priorityHigh;
      case 'medium': return COLORS.priorityMedium;
      case 'low': return COLORS.priorityLow;
      default: return COLORS.textSecondary;
    }
  };
  
  const renderTaskItem = ({ item }: { item: Task }) => {
    const project = getProjectById(item.projectId);
    
    return (
      <TouchableOpacity 
        style={styles.taskItem}
        onPress={() => handleTaskPress(item)}
      >
        <TouchableOpacity 
          style={[
            styles.checkbox, 
            item.status === 'completed' && styles.checkboxChecked
          ]}
          onPress={() => handleTaskStatusToggle(item.id)}
        >
          {item.status === 'completed' && (
            <Feather name="check" size={14} color={COLORS.background} />
          )}
        </TouchableOpacity>
        
        <View style={styles.taskContent}>
          <Text 
            style={[
              styles.taskTitle, 
              item.status === 'completed' && styles.taskTitleCompleted
            ]}
            numberOfLines={1}
          >
            {item.title}
          </Text>
          
          <View style={styles.taskMeta}>
            {item.dueDate && (
              <View style={styles.metaItem}>
                <Feather name="calendar" size={12} color={COLORS.textSecondary} />
                <Text style={styles.metaText}>
                  {new Date(item.dueDate).toLocaleDateString()}
                </Text>
              </View>
            )}
            
            {project && (
              <View style={styles.projectTag}>
                <View 
                  style={[
                    styles.projectDot, 
                    { backgroundColor: project.color }
                  ]} 
                />
                <Text style={styles.projectText}>{project.name}</Text>
              </View>
            )}
          </View>
        </View>
        
        <View 
          style={[
            styles.priorityIndicator, 
            { backgroundColor: getPriorityColor(item.priority) }
          ]} 
        />
      </TouchableOpacity>
    );
  };
  
  const renderStatusTab = (status: TaskStatus, label: string, count: number) => (
    <TouchableOpacity
      style={[
        styles.statusTab,
        selectedStatus === status && styles.statusTabActive
      ]}
      onPress={() => handleStatusChange(status)}
    >
      <Text 
        style={[
          styles.statusTabText,
          selectedStatus === status && styles.statusTabTextActive
        ]}
      >
        {label}
      </Text>
      <View style={styles.statusCount}>
        <Text style={styles.statusCountText}>{count}</Text>
      </View>
    </TouchableOpacity>
  );
  
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>My Tasks</Text>
          <Text style={styles.subtitle}>
            {taskStats.total} tasks, {taskStats.completed} completed
          </Text>
        </View>
        <TouchableOpacity style={styles.profileButton}>
          <Feather name="user" size={24} color={COLORS.text} />
        </TouchableOpacity>
      </View>
      
      {/* Status Tabs */}
      <View style={styles.statusTabs}>
        {renderStatusTab('todo', 'To Do', taskStats.todo)}
        {renderStatusTab('in_progress', 'In Progress', taskStats.in_progress)}
        {renderStatusTab('completed', 'Completed', taskStats.completed)}
      </View>
      
      {/* Task List */}
      <FlatList
        data={filteredTasks}
        renderItem={renderTaskItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.taskList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>
              No {selectedStatus.replace('_', ' ')} tasks
            </Text>
          </View>
        }
      />
      
      {/* Add Task Button */}
      <View style={styles.addButtonContainer}>
        <Button
          title="Add New Task"
          onPress={handleAddTask}
          icon={<Feather name="plus" size={18} color={COLORS.background} style={{ marginRight: SPACING.sm }} />}
          fullWidth
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    ...TYPOGRAPHY.h1,
    color: COLORS.text,
  },
  subtitle: {
    ...TYPOGRAPHY.bodySmall,
    color: COLORS.textSecondary,
    marginTop: SPACING.xxs,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.pill,
    backgroundColor: COLORS.backgroundAlt,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.subtle,
  },
  statusTabs: {
    flexDirection: 'row',
    marginBottom: SPACING.lg,
  },
  statusTab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  statusTabActive: {
    borderBottomColor: COLORS.primary,
  },
  statusTabText: {
    ...TYPOGRAPHY.button,
    color: COLORS.textSecondary,
  },
  statusTabTextActive: {
    color: COLORS.primary,
  },
  statusCount: {
    backgroundColor: COLORS.backgroundAlt,
    borderRadius: BORDER_RADIUS.pill,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xxs,
    marginTop: SPACING.xs,
  },
  statusCountText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
  },
  taskList: {
    paddingBottom: SPACING.xxl,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.card,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    ...SHADOWS.subtle,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 2,
    borderColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  checkboxChecked: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    ...TYPOGRAPHY.body,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  taskTitleCompleted: {
    textDecorationLine: 'line-through',
    color: COLORS.textSecondary,
  },
  taskMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  metaText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  projectTag: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  projectDot: {
    width: 8,
    height: 8,
    borderRadius: BORDER_RADIUS.pill,
    marginRight: SPACING.xs,
  },
  projectText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
  },
  priorityIndicator: {
    width: 4,
    height: '100%',
    borderRadius: BORDER_RADIUS.pill,
    marginLeft: SPACING.md,
  },
  emptyState: {
    padding: SPACING.xl,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
  },
  addButtonContainer: {
    position: 'absolute',
    bottom: SPACING.lg,
    left: SPACING.md,
    right: SPACING.md,
  },
});

export default HomeScreen;
</MO_FILE>

  <MO_FILE lang="typescript" path="App.tsx" mode="create" approx_lines="25">
import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import HomeScreen from './screens/HomeScreen';
import { COLORS } from './constants/theme';

// In a real app, these would be implemented
// import TaskDetailScreen from './screens/TaskDetailScreen';
// import NewTaskScreen from './screens/NewTaskScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <SafeAreaProvider>
      <StatusBar style="dark" />
      <NavigationContainer>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            contentStyle: { backgroundColor: COLORS.background }
          }}
        >
          <Stack.Screen name="Home" component={HomeScreen} />
          {/* Additional screens would be added here */}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
</MO_FILE>
</task_app_example>
`;
