import { COMMON_DEFINITIONS } from './common-definitions';

export const RECIPE_APP_EXAMPLE = `
${COMMON_DEFINITIONS}

<recipe_app_example>
  <thinking>
    I need to create a recipe/cooking app with a clean, elegant design. The app should:
    1. Display recipes with beautiful food photography
    2. Allow users to browse by category and search for recipes
    3. Show detailed cooking instructions and ingredients
    
    For the design, I'll use a light, airy aesthetic with:
    - Clean white background with subtle accent colors
    - Elegant typography with serif headings and sans-serif body text
    - Minimal UI elements with focus on food imagery
    
    The primary flow will include:
    1. Home screen with featured recipes and categories
    2. Recipe detail screen with ingredients and instructions
    3. Cooking mode with step-by-step instructions
    
    I'll ensure all theme constants are properly imported in every file.
  </thinking>

  <MO_FILE lang="typescript" path="constants/theme.ts" mode="create" approx_lines="120">
// Theme constants for the recipe app
// This file contains all design tokens used throughout the app

export const COLORS = {
  // Base colors
  background: "#FFFFFF",
  backgroundAlt: "#F9F9F9",
  text: "#333333",
  textSecondary: "#666666",
  
  // Primary palette
  primary: "#FF6B6B",
  primaryDark: "#E05A5A",
  primaryLight: "#FF9B9B",
  
  // Secondary palette
  secondary: "#4ECDC4",
  secondaryLight: "#7EDCD7",
  accent: "#FFD166",
  
  // Semantic colors
  success: "#6BCB77",
  warning: "#FFD166",
  error: "#FF6B6B",
  info: "#4ECDC4",
  
  // UI elements
  card: "#FFFFFF",
  cardAlt: "#F9F9F9",
  border: "#EEEEEE",
  divider: "#EEEEEE",
  overlay: "rgba(0, 0, 0, 0.5)",
};

export const SPACING = {
  base: 4,
  xxs: 2,
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const TYPOGRAPHY = {
  display: {
    fontSize: 36,
    fontWeight: "700",
    letterSpacing: -0.5,
    lineHeight: 44,
    fontFamily: "serif", // Would use a specific font in a real app
  },
  h1: {
    fontSize: 30,
    fontWeight: "700",
    letterSpacing: -0.3,
    lineHeight: 38,
    fontFamily: "serif",
  },
  h2: {
    fontSize: 24,
    fontWeight: "600",
    letterSpacing: -0.2,
    lineHeight: 32,
    fontFamily: "serif",
  },
  h3: {
    fontSize: 20,
    fontWeight: "600",
    letterSpacing: -0.1,
    lineHeight: 28,
    fontFamily: "serif",
  },
  body: {
    fontSize: 16,
    fontWeight: "400",
    letterSpacing: 0.1,
    lineHeight: 24,
  },
  bodySmall: {
    fontSize: 14,
    fontWeight: "400",
    letterSpacing: 0.1,
    lineHeight: 20,
  },
  button: {
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: 0.2,
    lineHeight: 24,
  },
  caption: {
    fontSize: 12,
    fontWeight: "500",
    letterSpacing: 0.3,
    lineHeight: 16,
  },
};

export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 16,
  xl: 24,
  pill: 9999,
};

export const SHADOWS = {
  none: {
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  subtle: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  medium: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  strong: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  },
};
</MO_FILE>

  <MO_FILE lang="typescript" path="components/Button.tsx" mode="create" approx_lines="80">
import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  StyleProp, 
  ViewStyle, 
  TextStyle 
} from 'react-native';
import { COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '../constants/theme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  icon?: React.ReactNode;
}

export const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
}: ButtonProps) => {
  
  const getBackgroundColor = () => {
    if (disabled) return COLORS.backgroundAlt;
    
    switch (variant) {
      case 'primary': return COLORS.primary;
      case 'secondary': return COLORS.secondary;
      case 'outline': return 'transparent';
      case 'text': return 'transparent';
      default: return COLORS.primary;
    }
  };
  
  const getTextColor = () => {
    if (disabled) return COLORS.textSecondary;
    
    switch (variant) {
      case 'primary': return COLORS.background;
      case 'secondary': return COLORS.background;
      case 'outline': return COLORS.primary;
      case 'text': return COLORS.primary;
      default: return COLORS.background;
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        styles[size],
        { backgroundColor: getBackgroundColor() },
        variant === 'outline' && styles.outlineButton,
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      {loading ? (
        <ActivityIndicator color={getTextColor()} size="small" />
      ) : (
        <>
          {icon}
          <Text style={[
            styles.text, 
            styles[\`\${size}Text\`],
            { color: getTextColor() },
            textStyle
          ]}>
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.subtle,
  },
  outlineButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  small: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.md,
  },
  medium: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
  },
  large: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.xl,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.6,
  },
  text: {
    ...TYPOGRAPHY.button,
    textAlign: 'center',
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
});
</MO_FILE>

  <MO_FILE lang="typescript" path="mocks/recipes.ts" mode="create" approx_lines="80">
// Mock data for recipes

export interface Ingredient {
  id: string;
  name: string;
  amount: string;
  unit: string;
}

export interface Step {
  id: string;
  order: number;
  description: string;
  imageUrl?: string;
  duration?: number; // in minutes
}

export interface Recipe {
  id: string;
  title: string;
  description: string;
  prepTime: number; // in minutes
  cookTime: number; // in minutes
  servings: number;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string[];
  ingredients: Ingredient[];
  steps: Step[];
  imageUrl: string;
  isFeatured?: boolean;
  calories?: number;
  rating?: number;
  reviewCount?: number;
}

export const mockRecipes: Recipe[] = [
  {
    id: 'r1',
    title: 'Avocado & Egg Toast',
    description: 'A simple, nutritious breakfast that combines creamy avocado with perfectly cooked eggs on toasted bread.',
    prepTime: 5,
    cookTime: 10,
    servings: 2,
    difficulty: 'easy',
    category: ['breakfast', 'vegetarian', 'quick'],
    ingredients: [
      { id: 'i1', name: 'Avocado', amount: '1', unit: 'whole' },
      { id: 'i2', name: 'Eggs', amount: '2', unit: 'large' },
      { id: 'i3', name: 'Bread', amount: '2', unit: 'slices' },
      { id: 'i4', name: 'Salt', amount: '1/4', unit: 'tsp' },
      { id: 'i5', name: 'Pepper', amount: '1/4', unit: 'tsp' },
      { id: 'i6', name: 'Red pepper flakes', amount: '1/8', unit: 'tsp' },
    ],
    steps: [
      { id: 's1', order: 1, description: 'Toast the bread until golden brown.' },
      { id: 's2', order: 2, description: 'Mash the avocado and spread it on the toast.' },
      { id: 's3', order: 3, description: 'Fry the eggs to your preference (sunny side up recommended).' },
      { id: 's4', order: 4, description: 'Place the eggs on top of the avocado toast.' },
      { id: 's5', order: 5, description: 'Season with salt, pepper, and red pepper flakes.' },
    ],
    imageUrl: 'https://images.unsplash.com/photo-1525351484163-7529414344d8?w=500&q=80',
    isFeatured: true,
    calories: 350,
    rating: 4.8,
    reviewCount: 124,
  },
  {
    id: 'r2',
    title: 'Vegetable Stir Fry',
    description: 'A colorful and nutritious stir fry loaded with fresh vegetables and a savory sauce.',
    prepTime: 15,
    cookTime: 10,
    servings: 4,
    difficulty: 'medium',
    category: ['dinner', 'vegetarian', 'quick'],
    ingredients: [
      { id: 'i1', name: 'Broccoli', amount: '2', unit: 'cups' },
      { id: 'i2', name: 'Bell peppers', amount: '2', unit: 'medium' },
      { id: 'i3', name: 'Carrots', amount: '2', unit: 'medium' },
      { id: 'i4', name: 'Soy sauce', amount: '3', unit: 'tbsp' },
      { id: 'i5', name: 'Garlic', amount: '3', unit: 'cloves' },
      { id: 'i6', name: 'Ginger', amount: '1', unit: 'tbsp' },
      { id: 'i7', name: 'Vegetable oil', amount: '2', unit: 'tbsp' },
    ],
    steps: [
      { id: 's1', order: 1, description: 'Chop all vegetables into bite-sized pieces.' },
      { id: 's2', order: 2, description: 'Heat oil in a wok or large pan over high heat.' },
      { id: 's3', order: 3, description: 'Add garlic and ginger, stir for 30 seconds.' },
      { id: 's4', order: 4, description: 'Add vegetables and stir fry for 5-7 minutes.' },
      { id: 's5', order: 5, description: 'Add soy sauce and continue cooking for 2 minutes.' },
      { id: 's6', order: 6, description: 'Serve hot, optionally over rice.' },
    ],
    imageUrl: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=500&q=80',
    isFeatured: true,
    calories: 220,
    rating: 4.6,
    reviewCount: 89,
  },
];
</MO_FILE>

  <MO_FILE lang="typescript" path="screens/HomeScreen.tsx" mode="create" approx_lines="150">
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions,
  TextInput
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Feather } from '@expo/vector-icons';
import { Button } from '../components/Button';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from '../constants/theme';
import { mockRecipes, Recipe } from '../mocks/recipes';

const { width } = Dimensions.get('window');

const HomeScreen = () => {
  const navigation = useNavigation();
  const [featuredRecipes, setFeaturedRecipes] = useState<Recipe[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  
  useEffect(() => {
    // Load featured recipes
    setFeaturedRecipes(mockRecipes.filter(recipe => recipe.isFeatured));
    
    // Extract unique categories
    const allCategories = mockRecipes.flatMap(recipe => recipe.category);
    const uniqueCategories = [...new Set(allCategories)];
    setCategories(uniqueCategories);
  }, []);
  
  const handleRecipePress = (recipe: Recipe) => {
    // @ts-ignore - Navigation typing would be properly set up in a real app
    navigation.navigate('RecipeDetail', { recipeId: recipe.id });
  };
  
  const handleCategoryPress = (category: string) => {
    // @ts-ignore - Navigation typing would be properly set up in a real app
    navigation.navigate('CategoryRecipes', { category });
  };
  
  const handleSearch = () => {
    if (searchQuery.trim()) {
      // @ts-ignore - Navigation typing would be properly set up in a real app
      navigation.navigate('SearchResults', { query: searchQuery });
    }
  };
  
  const renderRecipeCard = (recipe: Recipe) => (
    <TouchableOpacity
      key={recipe.id}
      style={styles.recipeCard}
      onPress={() => handleRecipePress(recipe)}
    >
      <Image 
        source={{ uri: recipe.imageUrl }} 
        style={styles.recipeImage}
      />
      <View style={styles.recipeInfo}>
        <Text style={styles.recipeTitle}>{recipe.title}</Text>
        <View style={styles.recipeStats}>
          <View style={styles.statItem}>
            <Feather name="clock" size={14} color={COLORS.textSecondary} />
            <Text style={styles.statText}>{recipe.prepTime + recipe.cookTime} min</Text>
          </View>
          <View style={styles.statItem}>
            <Feather name="star" size={14} color={COLORS.textSecondary} />
            <Text style={styles.statText}>{recipe.rating}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
  
  const renderCategoryItem = (category: string) => (
    <TouchableOpacity
      key={category}
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(category)}
    >
      <Text style={styles.categoryText}>{category}</Text>
    </TouchableOpacity>
  );
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Culinary Delights</Text>
          <TouchableOpacity style={styles.profileButton}>
            <Feather name="user" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>
        
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Feather name="search" size={20} color={COLORS.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search recipes..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
          </View>
        </View>
        
        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {categories.map(renderCategoryItem)}
          </ScrollView>
        </View>
        
        {/* Featured Recipes */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Recipes</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.featuredContainer}>
            {featuredRecipes.map(renderRecipeCard)}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    ...TYPOGRAPHY.h1,
    color: COLORS.text,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.pill,
    backgroundColor: COLORS.backgroundAlt,
    alignItems: 'center',
    justifyContent: 'center',
    ...SHADOWS.subtle,
  },
  searchContainer: {
    marginBottom: SPACING.lg,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundAlt,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    marginLeft: SPACING.sm,
    ...TYPOGRAPHY.body,
    color: COLORS.text,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    ...TYPOGRAPHY.h2,
    color: COLORS.text,
  },
  seeAllText: {
    ...TYPOGRAPHY.bodySmall,
    color: COLORS.primary,
  },
  categoriesContainer: {
    paddingRight: SPACING.md,
  },
  categoryItem: {
    backgroundColor: COLORS.backgroundAlt,
    borderRadius: BORDER_RADIUS.pill,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginRight: SPACING.sm,
    ...SHADOWS.subtle,
  },
  categoryText: {
    ...TYPOGRAPHY.button,
    color: COLORS.text,
    textTransform: 'capitalize',
  },
  featuredContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  recipeCard: {
    width: (width - SPACING.md * 3) / 2,
    backgroundColor: COLORS.card,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    marginBottom: SPACING.md,
    ...SHADOWS.medium,
  },
  recipeImage: {
    width: '100%',
    height: 120,
  },
  recipeInfo: {
    padding: SPACING.sm,
  },
  recipeTitle: {
    ...TYPOGRAPHY.body,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  recipeStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  statText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
});

export default HomeScreen;
</MO_FILE>

  <MO_FILE lang="typescript" path="App.tsx" mode="create" approx_lines="25">
import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import HomeScreen from './screens/HomeScreen';
import { COLORS } from './constants/theme';

// In a real app, these would be implemented
// import RecipeDetailScreen from './screens/RecipeDetailScreen';
// import CategoryRecipesScreen from './screens/CategoryRecipesScreen';
// import SearchResultsScreen from './screens/SearchResultsScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <SafeAreaProvider>
      <StatusBar style="dark" />
      <NavigationContainer>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            contentStyle: { backgroundColor: COLORS.background }
          }}
        >
          <Stack.Screen name="Home" component={HomeScreen} />
          {/* Additional screens would be added here */}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
</MO_FILE>
</recipe_app_example>
`;
