import { COMMON_DEFINITIONS } from './common-definitions';
import { FITNESS_APP_EXAMPLE } from './fitness-app-example';
import { RECIPE_APP_EXAMPLE } from './recipe-app-example';
import { TASK_APP_EXAMPLE } from './task-app-example';

export const DIVERSE_FIRST_EXAMPLES = `
${COMMON_DEFINITIONS}

<diverse_examples_guide>
  <purpose>
    These examples demonstrate how to implement different types of applications
    with distinct design approaches while following best practices for imports,
    error prevention, and code organization.
    
    Each example includes:
    1. A thinking/planning section showing the design approach
    2. A complete theme.ts file with all necessary design tokens
    3. A reusable Button component
    4. Mock data with persistence capabilities
    5. A fully implemented HomeScreen
    6. A minimal App.tsx setup
    
    All examples follow these critical principles:
    - Every file imports ALL necessary theme constants
    - Data persistence is implemented for all features
    - Consistent styling using theme constants
    - Clear file organization and import structure
    - Error prevention through proper imports
  </purpose>
  
  <how_to_use>
    1. Study the example most relevant to your app type
    2. Follow the same import patterns for theme constants
    3. Implement the same data persistence approach
    4. Use the same file structure and organization
    5. Verify all imports before implementation
  </how_to_use>
</diverse_examples_guide>

${FITNESS_APP_EXAMPLE}

${RECIPE_APP_EXAMPLE}

${TASK_APP_EXAMPLE}
`;

export default DIVERSE_FIRST_EXAMPLES;
