export const COMMON_DEFINITIONS = `
<file_operation_guidelines>
  <when_to_use_mo_file>
    - ALWAYS use MO_FILE
    - Use MO_FILE when creating a new file that doesn't exist yet (mode="create")
    - Use MO_FILE when completely replacing an existing file's complete content (mode="edit" or mode="fix")
    - Always include the complete file content between the MO_FILE tags
    - CRITICAL: Never submit incomplete MO_FILE content - it must contain a complete implementation
    - CRITICAL: MO_FILE must include all necessary imports, component definitions, and exports
  </when_to_use_mo_file>
  
  <import_verification>
    - ALWAYS include ALL necessary imports at the top of EVERY file
    - NEVER reference any variable, type, or component without importing it first
    - ALWAYS import ALL theme constants used in the file (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>HY, etc.)
    - VERIFY that every import resolves to an actual file or package
    - CHECK for duplicate imports - each import should appear only once
    - CRITICAL: Run through a mental checklist for each file to ensure all imports are present
  </import_verification>
  
  <theme_usage>
    - NEVER use hardcoded values for colors, spacing, typography, etc.
    - ALWAYS import and use theme constants from constants/theme.ts
    - VERIFY that all style properties use appropriate theme constants
  </theme_usage>

  <happy_path_enforcement>
    - ALWAYS implement the core functionality FIRST before any auxiliary features
    - CRITICAL: Ensure the PRIMARY USER FLOW works end-to-end with no broken links
    - Test the happy path mentally by tracing through user interactions step by step
    - Verify that all screens in the main flow are connected properly
    - Ensure all buttons in the main flow have proper onPress handlers
    - Confirm that navigation between screens works correctly
    - Validate that all context providers needed for the main flow are properly implemented
  </happy_path_enforcement>

  <simplified_integration>
    - ALWAYS implement context providers BEFORE the components that use them
    - CRITICAL: Ensure all functions referenced in components are actually defined and exported
    - Keep all related functionality in the same file when possible to avoid integration errors
    - When functions must be separated, create explicit integration checklists
    - Use consistent patterns for data access across the entire application
    - Verify that all props passed to components are actually used and defined
    - Double-check that all required props are passed to components
  </simplified_integration>

  <error_prevention_checklist>
    - CRITICAL: Before submitting ANY code, run through this checklist:
    - Are all theme constants imported in every file that uses them?
    - Are all context providers implemented and exporting all required functions?
    - Are all navigation routes properly defined and connected?
    - Are all button onPress handlers properly defined and bound?
    - Are all required props passed to every component?
    - Is the primary user flow fully implemented and connected?
    - Do all imports point to files that actually exist?
  </error_prevention_checklist>
</file_operation_guidelines>

MO_FILE format:
1. lang represents the language of the code
2. path: Absolute full path
3. approx_lines: Approximate number of lines of code in the final output
4. mode: The operation being performed ('create', 'edit', or 'fix')

<integration_verification>
  Before submitting code for ANY component that uses external functions or data:
  1. VERIFY that all imported functions are actually exported from their source files
  2. VERIFY that all context providers export ALL functions used by components
  3. VERIFY that all props used in a component are passed from its parent
  4. VERIFY that all navigation routes referenced actually exist
  5. VERIFY that all state management is properly initialized and connected
</integration_verification>
`;
