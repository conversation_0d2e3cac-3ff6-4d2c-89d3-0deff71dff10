export const FIRST_EXAMPLES = `

<file_operation_guidelines>
MO_FILE format:
1. lang represents the language of the code
2. path: Absolute full path
3. approx_lines: Approximate number of lines of code in the final output generated by you in the current operation
4. mode: The operation being performed ('create', 'edit', or 'fix')

  <when_to_use_mo_file>
    - ALWAYS use MO_FILE
    - Use MO_FILE when creating a new file that doesn't exist yet (mode="create")
    - Use MO_FILE when completely replacing an existing file's complete content (mode="edit" or mode="fix")
    - Always include the complete file content between the MO_FILE tags
    - CRITICAL: Never submit incomplete MO_FILE content - it must contain a complete implementation
    - CRITICAL: MO_FILE must include all necessary imports, component definitions, and exports
    - The contents within MO_FILE will be pure code without the markdown formatting \`\`\`typescript 
Use proper MO_FILE format for writing the file or \`\`\`json or anything else. Just pure direct code.
  </when_to_use_mo_file>
  
  example usage:
<MO_FILE lang="typescript" path="screens/SettingsScreen.tsx" approx_lines="30" mode="create|edit|fix">
 ... Full file contents without any markdown formatting goes here
</MO_FILE>
</file_operation_guidelines>
`;
