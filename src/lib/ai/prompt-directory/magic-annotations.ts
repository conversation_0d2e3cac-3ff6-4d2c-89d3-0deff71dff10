/**
 * Magic Annotations System
 * 
 * This file defines the structure and usage of magic annotations used throughout the codebase.
 * Magic annotations serve as semantic markers that can be used by both humans and AI to
 * discover, understand, and modify code more effectively.
 */

export const MAGIC_ANNOTATIONS = `
<magic_annotations_system>
  <purpose>
    Magic annotations create a semantic layer over your codebase that serves as a bridge between
    human developers and AI assistants. They enable:
    
    1. Feature discovery through autocomplete (type @ to see available components/features)
    2. Contextual understanding for AI agents to make holistic changes
    3. Semantic search across the codebase without requiring exact file paths
    4. Relationship mapping between components and features
  </purpose>

  <annotation_types>
    <magic_component>
      Purpose: Identifies a reusable component or feature by name
      Format: @magic_component: ComponentName
      Example: @magic_component: AuthProvider
    </magic_component>

    <magic_platform>
      Purpose: Specifies which platform(s) the code supports
      Format: @magic_platform: platform1,platform2
      Example: @magic_platform: web,ios,android
    </magic_platform>

    <magic_purpose>
      Purpose: Briefly describes what the component/function does
      Format: @magic_purpose: Short description of functionality
      Example: @magic_purpose: Handles user authentication with email and social providers
    </magic_purpose>

    <magic_connects>
      Purpose: Lists other components this one interacts with
      Format: @magic_connects: Component1,Component2
      Example: @magic_connects: UserProfile,SettingsScreen,AuthAPI
    </magic_connects>

    <magic_category>
      Purpose: Categorizes the component for organization
      Format: @magic_category: Category/Subcategory
      Example: @magic_category: UI/Forms
    </magic_category>

    <magic_keywords>
      Purpose: Additional searchable terms for discovery
      Format: @magic_keywords: keyword1,keyword2,keyword3
      Example: @magic_keywords: authentication,login,security,user
    </magic_keywords>
  </annotation_types>

  <usage_guidelines>
    1. Place annotations in JSDoc comments above component/function definitions
    2. Keep annotations concise and focused on searchability
    3. Use comma-separated values without spaces for multi-value fields
    4. Update annotations when component functionality changes
    5. Ensure component names match actual exports for autocomplete accuracy
    6. Include at least @magic_component and @magic_purpose for all significant components
    7. No matter what, the EVERY code including the @magic annotations must be inside a <MO_FILE></MO_FILE> block
  </usage_guidelines>

  <example_usage>
    /**
     * @magic_component: ProductCard
     * @magic_platform: all
     * @magic_purpose: Displays product information in a card format with image, title, price
     * @magic_connects: ProductList,ProductDetail,ShoppingCart
     * @magic_category: UI/Commerce
     * @magic_keywords: product,card,display,commerce,item
     */
    const ProductCard: React.FC<ProductCardProps> = ({ product, onPress }) => {
      // Component implementation
    };
  </example_usage>
</magic_annotations_system>
`;

/**
 * Export the magic annotations system for use in prompts
 */
export default MAGIC_ANNOTATIONS;
