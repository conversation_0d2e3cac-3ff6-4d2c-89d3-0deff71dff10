// // Create a new file: src/lib/ai/stream-handlers.ts
//
// import {LanguageModelV1StreamPart, LanguageModelV1, LanguageModelV1CallOptions, streamText} from 'ai';
//
// // Track the state of a stream
// interface StreamState {
//     generatedText: string;
//     isComplete: boolean;
//     isRetry: boolean;
//     retryCount: number;
// }
//
// // Handle stream interruptions
// export async function handleStreamInterruption(
//     chunk: LanguageModelV1StreamPart,
//     controller: TransformStreamDefaultController<LanguageModelV1StreamPart>,
//     state: StreamState,
//     model: LanguageModelV1,
//     params: LanguageModelV1CallOptions
// ) {
//     console.log('[Stream] Detected interruption, attempting to continue...');
//
//     if (state.retryCount >= 3) {
//         console.error('[Stream] Maximum retries reached, giving up');
//         controller.enqueue(chunk);
//         return;
//     }
//
//     try {
//         await continueStreamFromInterruption(
//             controller,
//             state,
//             model,
//             params
//         );
//     } catch (error) {
//         console.error('[Stream] Failed to continue stream:', error);
//         controller.enqueue(chunk);
//     }
// }
//
// // Continue a stream from where it was interrupted
// async function continueStreamFromInterruption(
//     controller: TransformStreamDefaultController<LanguageModelV1StreamPart>,
//     state: StreamState,
//     model: LanguageModelV1,
//     params: LanguageModelV1CallOptions
// ) {
//     // Get the last part of generated text
//     const lastChunk = state.generatedText.slice(-500);
//     console.log('[Stream] Last generated content:', lastChunk.slice(-100));
//
//     // Create continuation params
//     const continuationParams = createContinuationParams(params, lastChunk);
//
//     // Request continuation
//     console.log('[Stream] Requesting continuation...');
//     const {fullStream} = streamText(continuationParams);
//
//     // Process the continuation stream
//     await processContinuationStream(
//         fullStream as any,
//         controller,
//         state
//     );
//
//     console.log('[Stream] Continuation completed');
//     state.retryCount++;
// }
//
// // Create parameters for continuation request
// function createContinuationParams(
//     params: LanguageModelV1CallOptions,
//     lastChunk: string
// ): LanguageModelV1CallOptions {
//     return {
//         ...params,
//         messages: [
//             ...(params.messages || []),
//             {
//                 role: 'system',
//                 content: `The previous response was interrupted. Continue exactly from where you left off. The last part of your response was: "${lastChunk}"`
//             }
//         ]
//     };
// }
//
// // Process the continuation stream
// async function processContinuationStream(
//     continuationStream: ReadableStream<LanguageModelV1StreamPart>,
//     controller: TransformStreamDefaultController<LanguageModelV1StreamPart>,
//     state: StreamState
// ) {
//     const reader = continuationStream.getReader();
//
//     // Skip the first part that might repeat content we already have
//     let skipInitialContent = true;
//
//     while (true) {
//         const {done, value} = await reader.read();
//         if (done) break;
//
//         // For text chunks, we need to be careful about duplicated content
//         if (value.type === 'text-delta') {
//             if (skipInitialContent) {
//                 // Check if we've reached new content
//                 if (!state.generatedText.endsWith(value.textDelta)) {
//                     skipInitialContent = false;
//                     controller.enqueue(value);
//                     state.generatedText += value.textDelta;
//                 }
//             } else {
//                 controller.enqueue(value);
//                 state.generatedText += value.textDelta;
//             }
//         }
//         // For non-text chunks, just pass them through
//         else if (value.type !== 'finish') {
//             controller.enqueue(value);
//         }
//     }
// }
//
// // Handle text delta chunks
// export function handleTextDelta(
//     chunk: LanguageModelV1StreamPart & { type: 'text-delta' },
//     controller: TransformStreamDefaultController<LanguageModelV1StreamPart>,
//     state: StreamState
// ) {
//     state.generatedText += chunk.textDelta;
//     controller.enqueue(chunk);
// }
//
// // Handle finish chunks
// export function handleFinish(
//     chunk: LanguageModelV1StreamPart & { type: 'finish' },
//     controller: TransformStreamDefaultController<LanguageModelV1StreamPart>,
//     state: StreamState,
//     model: LanguageModelV1,
//     params: LanguageModelV1CallOptions
// ) {
//     state.isComplete = true;
//
//     if (chunk.finishReason === 'unknown') {
//         return handleStreamInterruption(chunk, controller, state, model, params);
//     }
//
//     controller.enqueue(chunk);
// }