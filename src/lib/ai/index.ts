import {TextPart, wrapLanguageModel} from 'ai';
import {createOpenRouter} from "@openrouter/ai-sdk-provider";
import {customMiddleware} from './custom-middleware';
import fs from "fs/promises";
import {exportData} from "@/lib/server-utils";

const updateMessageWithCaching = (message:
                                  {
                                      role: string,
                                      content: string | { type: string, text: string, [index: string]: any }[]
                                  }) => {
    if (typeof message.content === "string") {
        message.content = [
            {
                type: "text",
                text: message.content,
                cache_control: {
                    "type": "ephemeral"
                }
            }
        ]
    } else {
        message = {
            ...message,
            // @ts-ignore
            cache_control: {
                "type": "ephemeral"
            }
        }
        // @ts-ignore
        message.content[message.content.length - 1]["cache_control"] = {
            "type": "ephemeral"
        }
    }
}

const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    // Log request details
    // console.log('\n=== OpenRouter Request ===');
    // @ts-ignore
    // console.log('URL:', typeof input === 'string' ? input : input.url);
    // console.log('Method:', init?.method);
    // console.log('Headers:', JSON.stringify(init?.headers, null, 2));

    let modifiedInit = {...init};

    if (init?.body) {
        const bodyObj = JSON.parse(init.body as string);

        bodyObj.parallel_tool_calls = false;
        if (bodyObj.model.includes("claude") || bodyObj.model.includes("gemini") ) {
            // Add cache control to system messages
            if (bodyObj.messages && Array.isArray(bodyObj.messages)) {
                if (bodyObj.messages[0].role === 'system') {
                    updateMessageWithCaching(bodyObj.messages[0]);
                }
                
                // For Claude Sonnet 4, merge consecutive assistant messages if the second one has tool calls
                if (bodyObj.model.includes("claude-sonnet-4")) {
                    const mergedMessages: any[] = [];
                    
                    for (let i = 0; i < bodyObj.messages.length; i++) {
                        const currentMessage = bodyObj.messages[i];
                        
                        // Check if this is an assistant message and the next one is also an assistant with tool calls
                        if (
                            currentMessage.role === 'assistant' && 
                            i + 1 < bodyObj.messages.length && 
                            bodyObj.messages[i + 1].role === 'assistant' && 
                            bodyObj.messages[i + 1].tool_calls && 
                            bodyObj.messages[i + 1].tool_calls.length > 0
                        ) {
                            // Create a merged message
                            const nextMessage = bodyObj.messages[i + 1];
                            
                            // Handle content merging with proper type handling
                            let mergedContent;
                            if (Array.isArray(currentMessage.content) && Array.isArray(nextMessage.content)) {
                                // Both are arrays, concatenate them with proper type handling
                                // Use type assertion to help TypeScript understand the structure
                                const currentContent = currentMessage.content as {type: string, text: string}[];
                                const nextContent = nextMessage.content as {type: string, text: string}[];
                                mergedContent = [...currentContent, ...nextContent];
                            } else if (Array.isArray(currentMessage.content)) {
                                // Current is array, next is string or other
                                mergedContent = currentMessage.content as {type: string, text: string}[];
                            } else if (Array.isArray(nextMessage.content)) {
                                // Next is array, current is string or other
                                mergedContent = nextMessage.content as {type: string, text: string}[];
                            } else {
                                // Neither is array, use current or fallback to next
                                mergedContent = currentMessage.content || nextMessage.content;
                            }
                            
                            const mergedMessage = {
                                ...currentMessage,
                                tool_calls: nextMessage.tool_calls,
                                content: mergedContent
                            };
                            
                            mergedMessages.push(mergedMessage);
                            // Skip the next message since we've merged it
                            i++;
                        } else {
                            mergedMessages.push(currentMessage);
                        }
                    }
                    
                    bodyObj.messages = mergedMessages;
                }

                // Find the last system message and mark is cached
                // const lastSystemMessage = bodyObj.messages.findLast((message: any) => message.role === "system");
                // if (lastSystemMessage) {
                //     updateMessageWithCaching(lastSystemMessage)
                // }

                const fileMessage = bodyObj.messages.find((message: any) => {
                    if (message.role === "user") {
                        return Array.isArray(message.content) ?
                            message.content.some(
                                (text: any) => {
                                    // console.log('text.text', text.text)
                                    return text.text?.includes("<FILE_MESSAGE>")
                                }
                            ) : message.content.includes("<FILE_MESSAGE>")
                    }
                    return false;
                });

                // console.log('Found fileMessage')
                if (fileMessage) {
                    updateMessageWithCaching(fileMessage)
                }


                // Find first user message
                // const firstAssistantResponse = bodyObj.messages.find((message: any) => message.role === "assistant");
                // if (firstAssistantResponse) {
                //     updateMessageWithCaching(firstAssistantResponse);
                // }
                // Detect if we are tool calling loop




                // Find the last message that happened before the current turn
                // const lastCompleteMessageIndex = bodyObj.messages.findLastIndex((message: any) => {
                //     return Array.isArray(message.content) && message.content.some((c: any) => c.text.includes("Today's date"))
                // })

                // bodyObj['provider'] = {
                //     'order': [
                //         'Google',
                //         'Amazon Bedrock',
                //         'Anthropic',
                //     ]
                // };

                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {
                    if (["user", "system", "assistant"].includes(message.role)) {
                        if(message.tool_calls && message.tool_calls.length > 0) {
                            message.content = ((message.content?.[0])?.text || message.content);
                            if(!message.content) {
                                message.content = ' <hidden></hidden> '
                            }
                        } else if (typeof message.content === "string") {
                            if(!bodyObj.model.includes("claude-sonnet-4")) {
                                message.content = [
                                    {
                                        type: "text",
                                        text: message.content || ' <hidden></hidden> '
                                    }
                                ]
                            }

                        }
                    }
                    return message;
                })

                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {
                    if(message.role === "tool") {
                        message.content = [
                            {
                                type: 'text',
                                text: (message.content?.[0] as TextPart).text || message.content
                            }
                        ]
                    }
                    return message;
                });

                    // this is to ensure we don't get the empty content error
                bodyObj.messages = bodyObj.messages.map((message: any, index: number) => {
                    if (Array.isArray(message.content)) {
                        if(!message.content.length) {
                            message.content = [
                                {
                                    type: 'text'
                                }
                            ]
                        }
                        // message.content[0].text = 'Empty content';
                        if(!message.content?.[0].text) {
                            message.content = 'Empty content';
                        }
                    }
                    return message;
                })

                const isToolCallingLoop = bodyObj.messages.filter((message: any) => message.role === "tool").length > 0;

                if(isToolCallingLoop) {
                    // We need to prioritize caching these
                    // find second last tool call
                    const secondLastToolCall = bodyObj.messages.filter((message: any) => message.role === "tool").slice(-2)[0];
                    if (secondLastToolCall) {
                        updateMessageWithCaching(secondLastToolCall);
                    }
                    const lastToolCallResponse = bodyObj.messages.findLast((message: any) => message.role === "tool");
                    if (lastToolCallResponse) {
                        updateMessageWithCaching(lastToolCallResponse);
                    }
                } else {
                    // find second last user message
                    const secondLastUserMessage = bodyObj.messages.filter((message: any) => message.role === "user").slice(-2)[0];
                    if (secondLastUserMessage) {
                        updateMessageWithCaching(secondLastUserMessage);
                    }
                    const lastUserMessage = bodyObj.messages.findLast((message: any) => message.role === "user");
                    if (lastUserMessage) {
                        updateMessageWithCaching(lastUserMessage);
                    }
                }

                // if(lastCompleteMessageIndex !== -1) {
                //     console.log(`Found message at index ${lastCompleteMessageIndex}`)
                // Update the previous message as the one with Today's date is the latest user message
                // updateMessageWithCaching(bodyObj.messages[lastCompleteMessageIndex]);
                // }
            }


            // Add cache control to tools and function calls
            if (bodyObj.tools && Array.isArray(bodyObj.tools)) {
                bodyObj.tools = bodyObj.tools.map((tool: any) => {
                    // Handle function objects
                    if (tool.function) {
                        // tool["cache_control"] = {
                        //     "type": "ephemeral"
                        // };
                        return {
                            ...tool,
                            function: {
                                ...tool.function,
                                // "cache_control": {
                                //     "type": "ephemeral"
                                // }
                            },
                            // "cache_control": {
                            //     "type": "ephemeral"
                            // }
                        };
                    }
                    // Handle direct tool objects
                    return {
                        ...tool,
                        // "cache_control": {
                        //     "type": "ephemeral"
                        // }
                    };
                });
            }
        }
        // Only apply the message truncation logic if not using Claude Sonnet 4
        // if (!odyObj.model.includes("claude-sonnet-4")) {
        //     let m: any[] = bodyObj.messages
        //         // .slice(0, 18);
        //     m.splice(20, 1)
        //     bodyObj.messages = [...m]
        // }
        // await exportData(bodyObj.messages, 'request-messages')

        modifiedInit.body = JSON.stringify(bodyObj);
        // console.log('Modified Body:', JSON.stringify(bodyObj));

        // // Request tracker - summarize the request body
        if (bodyObj.messages && Array.isArray(bodyObj.messages)) {
            const messageCount = bodyObj.messages.length;
            const roleCounts: Record<string, number> = {};

            // console.log('\n=== Request Summary ===');
            // console.log(`Model: ${bodyObj.model}`);
            // console.log(`Total Messages: ${messageCount}`);

            let totalTokens = 0;

            // Track message details
            bodyObj.messages.forEach((message: any, index: number) => {
                // Count roles
                roleCounts[message.role] = (roleCounts[message.role] || 0) + 1;

                // Extract content for summary
                let contentSummary = '';
                let hasCaching = false;

                if (typeof message.content === 'string') {
                    contentSummary = message.content.substring(0, 500) + (message.content.length > 500 ? '...' : '');
                } else if (Array.isArray(message.content)) {
                    const firstContent = message.content[0];
                    if (firstContent && firstContent.text) {
                        contentSummary = firstContent.text.substring(0, 500) + (firstContent.text.length > 500 ? '...' : '');
                    }
                    // Check for caching in content array
                    hasCaching = message.content.some((item: any) => item.cache_control && item.cache_control.type === 'ephemeral');
                }

                // Check for caching at message level
                if (message.cache_control && message.cache_control.type === 'ephemeral') {
                    hasCaching = true;
                }

                // Estimate token count (very rough estimate: ~4 chars per token)
                let tokenEstimate = 0;
                if (typeof message.content === 'string') {
                    tokenEstimate = Math.ceil(message.content.length / 4);
                } else if (Array.isArray(message.content)) {
                    tokenEstimate = Math.ceil(message.content.reduce((sum: number, item: any) => {
                        return sum + (item.text ? item.text.length : 0);
                    }, 0) / 4);
                }

                totalTokens += tokenEstimate;
                // console.log(`\n----\nMessage ${index + 1}:`);
                // console.log(`  Role: ${message.role}`);
                // console.log(`  Content: ${contentSummary}`);
                // console.log(`  Caching: ${hasCaching ? 'Enabled' : 'Not enabled'}`);
                // console.log(`  Est. Tokens: ~${tokenEstimate}\n----\n`);

                // Log tool calls if present
                if (message.tool_calls && message.tool_calls.length > 0) {
                    // console.log(`  Tool Calls: ${message.tool_calls.length}`);
                }
            });

            // Summary of role distribution
            // console.log('\nTotal Tokens:', totalTokens);
            // console.log('\nRole Distribution:');
            // Object.entries(roleCounts).forEach(([role, count]) => {
            //     console.log(`  ${role}: ${count}`);
            // });

            // console.log('=== End Request Summary ===\n');
        }



    }

    // Make the actual request
    const response = await fetch(input, modifiedInit);

    // Clone the response to read it twice
    // const responseClone = response.clone();

    // Log the entire stream
    // if (responseClone.body) {
    //     const reader = responseClone.body.getReader();
    //     console.log('\n=== Start of Stream ===');
    //
    //     try {
    //         while (true) {
    //             const { value, done } = await reader.read();
    //
    //             if (done) {
    //                 console.log('=== End of Stream ===\n');
    //                 break;
    //             }
    //
    //             const chunk = new TextDecoder().decode(value);
    //             // Split by lines to handle SSE format
    //             const lines = chunk.split('\n');
    //             lines.forEach(line => {
    //                 if (line.trim()) {
    //                     if (line.startsWith('data: ')) {
    //                         try {
    //                             // Try to parse as JSON if possible
    //                             const jsonData = JSON.parse(line.slice(6));
    //                             console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));
    //                         } catch {
    //                             // If not JSON, log as is
    //                             console.log('Raw data:', line.slice(6));
    //                         }
    //                     } else {
    //                         console.log('Non-data line:', line);
    //                     }
    //                 }
    //             });
    //         }
    //     } catch (error) {
    //         console.error('Error reading stream:', error);
    //     }
    // }

    return response;
};

const openrouter = createOpenRouter({
    apiKey: process.env.OPENROUTER_API_KEY!,
    fetch: customFetch,
    baseURL: "https://openrouter.helicone.ai/api/v1",
    headers: {
        Authorization: `Bearer ${process.env.OPENROUTER_API_KEY!}`,
        "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY!}`,
        "X-Title": `magically-${process.env.NODE_ENV}`,
        'Helicone-Property-App': `magically-${process.env.NODE_ENV}`
    },
    extraBody: {
        transforms: ["middle-out"],
        providers: {
            anthropic: {
                "cache_control": {
                    "type": "ephemeral"
                },
            },
        },
        // "usage": {
        //     "include": true
        // },
        'provider': {
            'order': [
                'Google',
                'Anthropic',
                'Amazon Bedrock'
            ]
        }
    }
});

export const customModel = (apiIdentifier: string) => {
    return wrapLanguageModel({
        model: openrouter(apiIdentifier, {
            parallelToolCalls: true,
            includeReasoning: true,
        }),
        middleware: customMiddleware,
    });
};
