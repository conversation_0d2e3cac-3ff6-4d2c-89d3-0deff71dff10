import {generateUUID} from '@/lib/utils';
import {CoreMessage, CoreSystemMessage, CoreUserMessage, DataStreamWriter, smoothStream, streamText, tool} from 'ai';
import {z} from 'zod';
import {Session} from 'next-auth';
import {FileItem} from "@/types/file";
import {customModel} from "@/lib/ai";
import {systemPrompt} from "@/lib/ai/prompts";
import {PlaceholderProcessingService} from "@/lib/services/PlaceholderProcessingService";
import {llmMediaService} from "@/lib/services/llm-media-service";
import {FileLineManager} from "@/lib/editor/FileLineManager";

const fileEditSchema = z.object({
    absolutePath: z.string().describe(
        "ABSOLUTE file path to edit. MUST match exactly with project structure. " +
        "Only one edit operation allowed per file per user request."
    ),
    edits: z.array(z.object({
    //     completeFileCheckedValidation: z.boolean().describe(`
    //                    CONFIRMATION OF 5 CHECKS:
    // 1. Scanned entire file for duplicate function/component names
    // 2. Verified all JSX tags/braces/methods are properly closed
    // 3. Checked 5 lines before/after edit context
    // 4. Validated imports match changed functionality
    // 5. Compared against latest file version`),
    //     rationaleForReplacingEntireFile: z.string().describe("If you are replacing the entire file, clearly articulate why? REPLACING file is expensive and should be reserved for situations when there are not suitable alternatives or continuous errors."),
    //     rationaleForChecklist1: z.string().describe("Explain your decision for checklist 1. How did you think about the previous 5 lines and the next 5 lines of the edit ranges you provided? Explain your rationales about considering imports, jsx closing, stylesheet opening closing. DETAIL out your thought process."),
    //     checkList1: z.boolean().describe(`
    //                    - Have you considered that the line range you are providing is inclusive of the lines being edited. MEANING, L131-L146 will replace the contents on line 131 as well as on line 146 and everything in between?
    //                    - Do you understand that you have provide the line numbers of the old file as you are provided and NEVER the new line numbers after editing?
    //                    - Answer true if you have, answer false if you haven't considered it.
    //                    `),
    //     needDeletion: z.string().describe("Explain if you need to delete some lines from the code for this edit to work. If yes, tell the exact line numbers that need to be deleted for the file to syntactically work. NOTE: This does not actually delete the line"),
    //     append: z.boolean().describe(
    //         'If true, content will be inserted BEFORE the target line instead of replacing it. ' +
    //         'Use this when adding new imports or when you need to add content without removing existing code. ' +
    //         'Example: When adding a new import statement, set append: true to avoid removing existing imports.'
    //     ),
        existingSimplifiedCodeSnipper: z.string().describe("A simplified code snippet highlighting the function, method, class, variable, styles etc to target in the existing code"),
        lineRange: z.string().regex(/L\d+-L\d+/).describe(
            "INCLUSIVE line range using SHIFTED line numbers after accounting for the previous edits in this batch for the same life. Format: Lstart-Lend\n" +
            "Example: L5-L7 edits lines 5,6,7\n" +
            "Verify line numbers exist before editing!"
        ),
        instructions: z.string().describe(`
        This instruction will be passed a instruction prompt to another AI to make the changes.
        Add detailed instruction on what to change and why to change. 
        Where else to check for references and how not to make mistakes in editing? 
        Treat the instruction as your self reasoning and memory to make the edits. 
        `)
    })).describe(
        "ALL edits for this file in ONE array. Edits are applied atomically in order.\n" +
        "MUST bundle multiple changes to avoid line shift conflicts."
    )
});


interface StreamEditProps {
    session: Session;
    dataStream: DataStreamWriter;
    systemPrompts: CoreSystemMessage[];
    fileMessage: CoreUserMessage
    userMessage: CoreMessage,
    fileManager: FileLineManager
}

const placeholderService = new PlaceholderProcessingService(llmMediaService);

const processImagePlaceholders = async (text: string) => {
    const {processedText, metrics} = await placeholderService.processImagePlaceholders(text);
    return processedText;
};

const processVideoPlaceholders = async (text: string) => {
    const {processedText, metrics} = await placeholderService.processVideoPlaceholders(text);
    return processedText;
};


export const streamEdit = ({
                               session,
                               dataStream,
                               systemPrompts,
                               fileMessage,
                               userMessage,
                               fileManager
                           }: StreamEditProps) =>
    tool({
        description:
            'Use this tool to edit/add new files',
        parameters: fileEditSchema,
        execute: async ({absolutePath, edits}) => {
            const id = generateUUID();

            dataStream.writeData({
                type: 'kind',
                content: 'code',
            });

            dataStream.writeData({
                type: 'id',
                content: id,
            });

            dataStream.writeData({
                type: 'file-name',
                content: '',
            });

            dataStream.writeData({
                type: 'clear',
                content: '',
            });

            let draftContent = '';

            console.log('writing data', JSON.stringify(edits, null, 2));


            const {fullStream} = streamText({
                model: customModel("anthropic/claude-3.5-sonnet"),
                experimental_transform: smoothStream({chunking: 'line'}),
                temperature: 0.3,
                messages: [
                    ...systemPrompts,
                    fileMessage,
                    {
                        role: 'user',
                        content: `Since the last previous message, the following files have been updated and this is the latest version of those files. Ignore the earlier version of these files.
                        ${ fileManager.getDirtyFiles().map((file) => {
                            return `
<file name="${file.path}">
${file.contents}
</file>
`
                        })       
                        }
                        `

                    },
                    {
                        role: 'user',
                        content: `
User sent this message: ${userMessage.content}

You are making edit to a code file that has been generated by a different LLM. The LLM will give you the exact instructions 
on what to edit and your job is to think and precisely make the exact edits in order to create a coherent file. The final output must be 
syntactically and operationally correct. 

File to edit: ${absolutePath}

Instructions from the parent LLM to edit the file: 
${JSON.stringify(edits, null, 2)}

DO NOT add any additional formatting. Just return the file. Not even \`\`\`typescript or any preabmle before or after any file edit.`
                    }
                ],
            });

            for await (const delta of fullStream) {
                const {type} = delta;

                if (type === 'text-delta') {
                    const {textDelta} = delta;

                    draftContent += textDelta;

                    dataStream.writeData({
                        type: 'text-delta',
                        content: textDelta,
                    });
                }
            }

            draftContent = await processImagePlaceholders(draftContent);
            draftContent = await processVideoPlaceholders(draftContent);

            fileManager.replaceFile(absolutePath, draftContent)

            dataStream.writeData({type: 'finish', content: ''});

            return {
                id,
                content: 'A document was created and is now visible to the user.',
            };
        },
    });
