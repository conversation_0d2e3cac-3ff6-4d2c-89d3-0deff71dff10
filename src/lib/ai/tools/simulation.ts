import { z } from 'zod';
import { generateObject, generateText } from 'ai';
import {customModel} from "@/lib/ai";

// Define our schemas
const AgentSchema = z.object({
  role: z.string().describe("Role/perspective of the agent"),
  focus: z.array(z.string()).describe("Key areas this agent focuses on"),
  bias: z.array(z.string()).describe("Known biases of this agent"),
  expertise: z.array(z.string()).describe("Areas of expertise"),
  decision_weight: z.number().min(0).max(1).describe("Weight given to this agent's decisions")
});

const SimulationResultSchema = z.object({
  agent_id: z.string(),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
  decision: z.string(),
  key_factors: z.array(z.string()),
  suggestions: z.array(z.string())
});

const SimulationOutputSchema = z.object({
  overall_sentiment: z.number().min(0).max(1),
  confidence_score: z.number().min(0).max(1),
  agent_responses: z.array(SimulationResultSchema),
  key_insights: z.array(z.string()),
  improvement_suggestions: z.array(z.string()),
  consensus_areas: z.array(z.string()),
  disagreement_areas: z.array(z.string())
});

export interface SimulationConfig {
  num_agents: number;
  agent_types: string[];
  simulation_context: string;
  target_content: string;
}

const DEFAULT_AGENT_SYSTEM_PROMPT = `You are an expert agent with specific expertise and biases. 
Analyze the content from your unique perspective while staying true to your role.
Focus on providing concrete, actionable feedback based on your expertise.
Be direct and honest in your assessment.`;

const SIMULATION_SYSTEM_PROMPT = `You are a simulation coordinator responsible for:
1. Analyzing responses from multiple expert agents
2. Finding patterns and consensus
3. Identifying key areas of agreement and disagreement
4. Generating actionable insights
5. Maintaining objectivity in synthesis

Focus on practical, implementable insights while acknowledging uncertainty.`;

export async function runSimulation(config: SimulationConfig) {
  try {
    // 1. Generate Agents
    console.log('[Simulation] Generating agents');
    const agentsResult = await generateObject({
      schema: z.object({results: z.array(AgentSchema)}),
      model: customModel("anthropic/claude-3.5-haiku"),
      temperature: 0.7,
      system: DEFAULT_AGENT_SYSTEM_PROMPT,
      prompt: `Generate ${config.num_agents} unique agents with different perspectives for analyzing: ${config.simulation_context}
      Agent types needed: ${config.agent_types.join(", ")}
      Make each agent unique with specific expertise and biases.`
    });

    console.log('agentsResult', agentsResult)

    const agents = agentsResult.object.results;
    console.log('[Simulation] Generated agents:', {
      count: agents.length,
      roles: agents.map(a => a.role)
    });

    // 2. Run Individual Agent Analyses
    console.log('[Simulation] Starting parallel agent analyses');
    const agentPromises = agents.map(async (agent, index) => {
      console.log(`[Simulation] Agent ${index + 1}/${agents.length} (${agent.role}) starting analysis`);
      const result = await generateObject({
        schema: SimulationResultSchema,
        model: customModel("openai/gpt-4o-mini"),
        temperature: 0.5,
        system: `${DEFAULT_AGENT_SYSTEM_PROMPT}\nYou are a ${agent.role} with expertise in ${agent.expertise.join(", ")}.
        Your known biases are: ${agent.bias.join(", ")}. Focus on: ${agent.focus.join(", ")}`,
        prompt: `Analyze the following content from your perspective:\n${config.target_content}`
      });
      console.log(`[Simulation] Agent ${index + 1}/${agents.length} (${agent.role}) completed analysis`);
      return result.object;
    });

    const agentResponses = await Promise.all(agentPromises);
    console.log('[Simulation] All agent analyses completed:', {
      responses: agentResponses.length,
      average_confidence: agentResponses.reduce((acc, r) => acc + r.confidence, 0) / agentResponses.length
    });

    // 3. Synthesize Results
    console.log('[Simulation] Starting synthesis of agent responses');
    const finalResult = await generateObject({
      schema: SimulationOutputSchema,
      model: customModel("anthropic/claude-3.5-haiku"),
      temperature: 0.3,
      system: SIMULATION_SYSTEM_PROMPT,
      prompt: `Synthesize the following agent responses into a coherent analysis:
      ${JSON.stringify(agentResponses, null, 2)}`
    });

    console.log('[Simulation] Synthesis completed:', {
      overall_sentiment: finalResult.object.overall_sentiment,
      confidence_score: finalResult.object.confidence_score,
      num_insights: finalResult.object.key_insights.length,
      num_suggestions: finalResult.object.improvement_suggestions.length
    });

    return {
      success: true,
      simulation_id: Date.now().toString(),
      agents: agents,
      results: finalResult.object
    };

  } catch (error) {
    console.error('[Simulation] Error:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error in simulation'
    };
  }
}

// Example usage:
/*
const config: SimulationConfig = {
  num_agents: 5,
  agent_types: ['investor', 'technical_expert', 'market_analyst', 'customer', 'competitor'],
  simulation_context: "YC pitch analysis",
  target_content: "Your pitch content here..."
};

const result = await runSimulation(config);
*/
