import {LanguageModelV1Middleware, LanguageModelV1StreamPart, TypeValidationError} from 'ai';

export const customMiddleware: LanguageModelV1Middleware = {
    // In custom-middleware.ts
    wrapStream: async ({doStream, params, model}) => {
        const {stream, rawResponse, ...rest} = await doStream();
        let generatedText = '';
        const startTime = Date.now();


        const transformStream = new TransformStream<
            LanguageModelV1StreamPart,
            LanguageModelV1StreamPart
        >({
            async transform(chunk, controller) {
                // Handle text chunks
                if (chunk.type === 'text-delta') {
                    generatedText += chunk.textDelta;
                    controller.enqueue(chunk);
                }
                // Handle finish chunks - detect unknown finish reason
                else if (chunk.type === 'finish') {
                    console.log(`[Stream] Finished with reason: ${chunk.finishReason} after ${Date.now() - startTime}ms`);

                    if (chunk.finishReason === 'unknown') {
                        console.warn('[Stream] Detected unknown finish reason - stream was interrupted');

                        // Attempt to restart the stream
                        const maxRetries = 3;
                        const baseDelay = 1000; // 1 second
                        let retryCount = 0;

                        while (retryCount < maxRetries) {
                            console.log(`[Stream] Attempting to restart stream after interruption (${retryCount + 1}/${maxRetries})...`);
                            try {
                                // Exponential backoff: 1s, 2s, 4s
                                const delay = baseDelay * Math.pow(2, retryCount);
                                await new Promise(resolve => setTimeout(resolve, delay));

                                const retryStartTime = Date.now();
                                console.log(`[Stream] Starting retry ${retryCount + 1}`);

                                params.prompt.push({
                                    role: "system",
                                    content: "<continuation_of_stream priority='critical'>ULTRA IMPORTANT: We are reattempting to continue the partial stream after interruption. Notice your previous partial response and continue generating from there. Don't re-evaluate. Do not add a preamble again as the last file you were working on is currently open and is being edited. Just </continuation_of_stream>",
                                });

                                console.log('params before retrying', JSON.stringify(params))
                                const retryResult = await doStream();
                                const reader = retryResult.stream.getReader();

                                console.log(`[Stream] Retry ${retryCount + 1} stream obtained after ${Date.now() - retryStartTime}ms`);

                                // Read from the new stream and forward chunks
                                while (true) {
                                    const {done, value} = await reader.read();
                                    if (done) break;
                                    controller.enqueue(value);
                                }

                                console.log(`[Stream] Retry ${retryCount + 1} completed successfully`);
                                return; // Exit if successful
                            } catch (retryError) {
                                const errorMessage = retryError instanceof Error ? retryError.message : String(retryError);
                                console.error(`[Stream] Retry ${retryCount + 1} failed: ${errorMessage}`);
                                retryCount++;
                            }
                        }

                        // If we get here, all retries failed
                        console.error(`[Stream] All ${maxRetries} retries failed after stream interruption`);
                    }

                    // Pass through the finish chunk
                    controller.enqueue(chunk);
                }
                // Handle error chunks - keep your existing 429 handler
                else if (chunk.type === 'error') {
                    console.log('[Stream] Error chunk received:', chunk.error);

                    if (typeof chunk.error === "object") {
                        if ((JSON.stringify(chunk.error))?.includes('429')) {
                            const maxRetries = 3;
                            const baseDelay = 2000; // 2 seconds
                            let retryCount = 0;

                            while (retryCount < maxRetries) {
                                console.log(`[Stream] Got 429, attempt ${retryCount + 1} of ${maxRetries}...`);
                                try {
                                    // Exponential backoff: 2s, 4s, 8s
                                    const delay = baseDelay * Math.pow(2, retryCount);
                                    await new Promise(resolve => setTimeout(resolve, delay));

                                    const retryStartTime = Date.now();
                                    const retryResult = await doStream();
                                    const reader = retryResult.stream.getReader();

                                    console.log(`[Stream] 429 retry ${retryCount + 1} stream obtained after ${Date.now() - retryStartTime}ms`);

                                    while (true) {
                                        const {done, value} = await reader.read();
                                        if (done) break;
                                        controller.enqueue(value);
                                    }

                                    console.log(`[Stream] 429 retry ${retryCount + 1} completed successfully`);
                                    return; // Exit if successful
                                } catch (retryError) {
                                    const errorMessage = retryError instanceof Error ? retryError.message : String(retryError);
                                    console.error(`[Stream] 429 retry ${retryCount + 1} failed: ${errorMessage}`);
                                    retryCount++;
                                }
                            }

                            // If we get here, all retries failed
                            console.error(`[Stream] All ${maxRetries} retries failed after 429 error`);
                        }
                    }

                    // Pass through the error chunk
                    controller.enqueue(chunk);
                }
                // Pass through all other chunks
                else {
                    controller.enqueue(chunk);
                }
            },

            flush() {
                console.log(`[Stream] Stream completed after ${Date.now() - startTime}ms`);
                console.log(`[Stream] Total generated text length: ${generatedText.length} characters`);
            },
        });

        return {
            stream: stream.pipeThrough(transformStream),
            ...rest,
        };
    }
};
