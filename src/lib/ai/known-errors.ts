export const KNOWN_ERRORS = `
<known_errors>
  <error id="navigation-theme">
    ⚠️ "Cannot read properties of undefined (reading 'medium')" in NavigationContainer
    ✅ ALWAYS extend from base theme: \`{...DefaultTheme, colors: {...}}\`
    🔍 Root cause: Missing font properties in custom theme object
    🚫 NEVER create navigation themes from scratch
  </error>

  <error id="typescript-variable-type">
    ⚠️ Using variable references as types: \`typeof array[0]\`
    ✅ ALWAYS use explicit types: \`{item}: {item: UserType}\` NOT \`{item}: {item: typeof users[0]}\`
    🔍 Root cause: TypeScript can't properly infer types from runtime variables
    🚫 CRITICAL: This breaks type checking and causes runtime errors
  </error>

  <error id="union-types-expo">
    ⚠️ Using literal union types in Expo: \`type Mode = 'light' | 'dark'\`
    ✅ Use base types with comments: \`type Mode = string; // 'light' or 'dark'\`
    🔍 Root cause: Expo's JavaScript runtime has limitations with union types
    🚫 Affects: Navigation params, state management, component props
  </error>

  <error id="auth-loading-indefinitely">
    ⚠️ Profile/Auth loading indefinitely, especially in multiple tabs
    ✅ Use UNIQUE storage keys: \`const STORAGE_KEY = 'app-name-project-id-auth-storage'\`
    🔍 Root cause: Parallel tabs interfering with shared storage keys
    🚫 ALWAYS prefix AsyncStorage/SecureStore keys with unique app identifier
  </error>
  
  <error id="missing-navigation">
    ⚠️ Buttons/components without navigation implementation
    ✅ ALWAYS implement navigation.navigate() for ALL interactive elements
    🔍 Root cause: Forgetting to connect UI elements to navigation
    🚫 NEVER leave a button without navigation if its screen exists
  </error>
  
  <error id="file-size-limit">
    ⚠️ Files exceeding 250-300 lines
    ✅ Break into smaller modules following single responsibility principle
    🔍 Root cause: Monolithic components with too many responsibilities
    🚫 Extract: Utilities, sub-components, hooks, and context providers
  </error>
    
  <error id="unable-to-resolve-module">
    ⚠️ Getting unable-to-resolve-module when importing a package in the format <package>/<inner_import>
    ✅ This is not supported in the expo snack runtime and will not recover. Find alternatives.
    🔍 Root cause: Expo snack runtime limitations. 
    🚫 NEVER try to import from <package>/<inner_import>. Find alternative implementations that work. Supported imports: zustand/middleware,date-fns/locale. NOTHING else is supported.
  </error>
  
  <error id="expo-camera-usage">
    Camera usage example:
      import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
      import { useState } from 'react';
import { Button, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function App() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();

  if (!permission) {
    // Camera permissions are still loading.
    return <View />;
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet.
    return (
      <View style={styles.container}>
        <Text style={styles.message}>We need your permission to show the camera</Text>
        <Button onPress={requestPermission} title="grant permission" />
      </View>
    );
  }

  function toggleCameraFacing() {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  }

  return (
    <View style={styles.container}>
      <CameraView style={styles.camera} facing={facing}>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={toggleCameraFacing}>
            <Text style={styles.text}>Flip Camera</Text>
          </TouchableOpacity>
        </View>
      </CameraView>
    </View>
  );
}

const styles = StyleSheet.create({
 // Add all the styles carefully wihtout causing errors
});

  </error>

</known_errors>
`;
