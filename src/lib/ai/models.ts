// Define your models here.

export interface Model {
    id: string;
    label: string;
    apiIdentifier: string;
    description: string;
    inputCost?: number;
    outputCost?: number;
}

// const MODELS = {
//   CODE: "anthropic/claude-3-opus",
//   CREATIVE: "anthropic/claude-3-sonnet",
//   MATH: "google/gemini-pro",
//   GENERAL: "openai/gpt-4",
// } as const;

// export function selectModel(input: string): string {
//   const input_lower = input.toLowerCase();
//
//   // Code-related tasks
//   if (input_lower.includes("code") ||
//       input_lower.includes("programming") ||
//       input_lower.includes("function") ||
//       input_lower.includes("debug")) {
//     return MODELS.CODE;
//   }
//
//   // Creative writing or storytelling
//   if (input_lower.includes("story") ||
//       input_lower.includes("creative") ||
//       input_lower.includes("write") ||
//       input_lower.includes("describe")) {
//     return MODELS.CREATIVE;
//   }
//
//   // Mathematical or analytical tasks
//   if (input_lower.includes("math") ||
//       input_lower.includes("calculate") ||
//       input_lower.includes("solve") ||
//       input_lower.includes("analysis")) {
//     return MODELS.MATH;
//   }
//
//   return MODELS.GENERAL;
// }

export const models: Array<Model> = [
    {
        id: 'gpt-4o-mini',
        label: 'GPT 4o mini ($0.15/$0.6)',
        apiIdentifier: 'openai/gpt-4o-mini',
        description: 'Small model for fast, lightweight tasks',
    },
    {
        id: 'gpt-4o',
        label: 'GPT 4o ($2.5/$4)',
        apiIdentifier: 'openai/gpt-4o',
        description: 'For complex, multi-step tasks',
    },
    {
        id: 'business-t1-fx334-2025.0332',
        label: 'Claude 3.7-sonnet ($3/$15)',
        inputCost: 3,
        outputCost: 15,
        apiIdentifier: 'anthropic/claude-3.7-sonnet',
        description: 'For writing or storytelling',
    },
    {
        id: 'business-t1-fx356-2025.0332',
        label: 'Claude 4-sonnet ($3/$15)',
        inputCost: 3,
        outputCost: 15,
        apiIdentifier: 'anthropic/claude-sonnet-4',
        description: 'For writing or storytelling',
    },
    {
        id: 'business-t1-fx337-2025.0332',
        label: 'Claude 3.5-haiku ($0.8/$4)',
        apiIdentifier: 'anthropic/claude-3.5-haiku',
        description: 'For writing or storytelling',
    },
    {
        id: 'business-t1-fx336-2025.0332',
        label: 'Mistral Large (2411) ($2/$6)',
        apiIdentifier: 'mistralai/mistral-large-2411',
        description: 'Reasoning',
    },
    {
        id: 'business-t1-fx335-2025.0332',
        label: 'Deepseek Chat ($0.14/$0.28)',
        apiIdentifier: 'deepseek/deepseek-chat',
        description: 'Reasoning',
    },
    {
        id: 'business-t1-fx339-2025.0332',
        label: 'ChatGPT 4o ($2.5/$10)',
        apiIdentifier: 'openai/gpt-4o-2024-11-20',
        description: 'Reasoning',
    },
    // {
    //     id: 'command-r',
    //     label: 'Command R ($0.475/$1.425)',
    //     apiIdentifier: 'cohere/command-r',
    //     description: 'Reasoning',
    // },
    {
        id: 'business-t1-fx340-2025.0332',
        label: 'Gemini Pro 1.5 ($1.25/$5)',
        apiIdentifier: 'google/gemini-pro-1.5',
        description: 'Reasoning',
    },
    {
        id: 'qwen/qwen-max',
        label: 'LLama 3.3-70b-instruct ($0.12/$0.3)',
        apiIdentifier: 'qwen/qwen-max',
        description: 'Small talk',
    },
    {
        id: 'business-t1-fx338-2025.0332',
        label: 'Deepseek R1 ($0.55/$2.19)',
        apiIdentifier: 'openai/o3-mini-high',
        description: 'Advanced reasoning',
    },
    {
        id: 'business-t1-fx340-2025.0332',
        label: 'OpenAI GPT 4.1 ($2/$8)',
        apiIdentifier: 'openai/gpt-4.1',
        description: 'Advanced reasoning',
    },
] as const;

export const DEFAULT_MODEL_NAME: string = 'business-t1-fx334-2025.0332';
