export const EDIT_FILE_EXAMPLES = `# Smart File Editing Guide

## Key Concepts:
1. append: boolean (optional)
   - Use when ADDING new content without replacing existing code
   - Perfect for imports, new functions, or additional JSX elements
   - Content will be inserted BEFORE the target line

2. lineRange: string
   - Format: "L<start>-L<end>"
   - For single line: use same start and end (e.g., "L1-L1")
   - Both ends of the range are INCLUDED (e.g., "L1-L21") meaning L1-L21 will alter lines 1 to 21.
   - ALWAYS include complete code blocks

3. completeFileCheckedValidation: boolean
   - MUST verify:
     1. No duplicate functions/components
     2. All brackets properly closed
     3. Checked ±5 lines context
     4. Imports match changes
     5. Latest file version used

## Examples:

Example 1: Adding New Import
File: HomeScreen.tsx
1| import { View } from 'react-native';
2| import { SafeAreaView } from 'react-native-safe-area-context';
3| 
4| export function HomeScreen() {

✅ CORRECT:
edits: [{
  completeFileCheckedValidation: true,
  lineRange: "L1-L1",
  append: true,  // Key point: Add without replacing
  editedContent: "import { Header } from './Header';\\n"
}]

❌ INCORRECT:
edits: [{
  lineRange: "L1-L2",  // Wrong: Don't replace existing imports
  editedContent: "import { View } from 'react-native';\\nimport { SafeAreaView } from 'react-native-safe-area-context';\\nimport { Header } from './Header';"
}]

Example 2: Adding New Method
File: HomeScreen.tsx
8| const MOCK_DATA = {
9|   title: 'Example'
10| };
11|

✅ CORRECT:
edits: [{
  completeFileCheckedValidation: true,
  lineRange: "L11-L11",
  append: true,  // Key point: Add after existing code
  editedContent: "\\nfunction getData() {\\n  return MOCK_DATA;\\n}\\n"
}]

❌ INCORRECT:
edits: [{
  lineRange: "L8-L11",  // Wrong: Don't modify existing code
  editedContent: "const MOCK_DATA = {\\n  title: 'Example'\\n};\\n\\nfunction getData() {\\n  return MOCK_DATA;\\n}"
}]

Example 3: Modifying JSX Structure
File: HomeScreen.tsx
60|   return (
61|     <SafeAreaView>
62|       <ScrollView>
63|         <Text>Content</Text>
64|       </ScrollView>
65|     </SafeAreaView>
66|   );

✅ CORRECT:
edits: [{
  completeFileCheckedValidation: true,
  lineRange: "L62-L62",
  append: true,  // Key point: Insert without breaking structure
  editedContent: "        <Header />\\n"
}]

❌ INCORRECT:
edits: [{
  lineRange: "L61-L65",  // Wrong: Don't replace entire JSX block
  editedContent: "    <SafeAreaView>\\n      <Header />\\n      <ScrollView>\\n        <Text>Content</Text>\\n      </ScrollView>\\n    </SafeAreaView>"
}]

Example 4: Adding useEffect Hook
File: components/DataFetcher.tsx
BEFORE EDIT:
1| import { useState } from 'react';
2|
3| export function DataFetcher({ url }) {
4|   const [data, setData] = useState(null);
5|   return <div>{data}</div>;
6| }

DESIRED OUTCOME:
1| import { useState, useEffect } from 'react';
2|
3| export function DataFetcher({ url }) {
4|   const [data, setData] = useState(null);
5|   
6|   useEffect(() => {
7|     fetch(url)
8|       .then(res => res.json())
9|       .then(setData);
10|   }, [url]);
11|
12|   return <div>{data}</div>;
13| }

✅ CORRECT EDIT:
edits: [
  {
    lineRange: "L1-L1",
    append: true,
    editedContent: "import { useState, useEffect } from 'react';"
  },
  {
    lineRange: "L4-L4",
    append: true,
    editedContent: "
useEffect(() => {
    fetch(url)
        .then(res => res.json())
        .then(setData);
}, [url]);
"
  }
]

❌ INCORRECT EDIT (missing import):
edits: [{
  lineRange: "L4-L4",
  editedContent: "
useEffect(() => {
    fetch(url) // ERROR: useEffect not imported
}, [url]);
"
}]

---

Example 3: Complex JSX Modification 
File: components/UserProfile.tsx
BEFORE EDIT:
23| return (
24|   <View>
25|     <Text>Welcome back</Text>
26|   </View>
27| );

DESIRED OUTCOME:
23| return (
24|   <View style={styles.container}>
25|     <Header />
26|     <Text style={styles.title}>Welcome back</Text>
27|     <UserStats />
28|   </View>
29| );

✅ CORRECT EDIT:
edits: [
  {
    lineRange: "L24-L24",
    editedContent: " < View
style = {styles.container} >"
  },
  {
    lineRange: "L25-L25",
    append: true,
    editedContent: "
< Header / >
<Text style = {styles.title} > Welcome
back < /Text>
< UserStats / >"
  }
]

❌ INCORRECT EDIT (unclosed JSX):
edits: [{
  lineRange: "L24-L24",
  editedContent: " < View
style = {styles.container} >" // Missing closing tag
}]

Example 4: Updating Function Logic
File: HomeScreen.tsx
20| function processData(input) {
21|   return input;
22| }

✅ CORRECT:
edits: [{
  completeFileCheckedValidation: true,
  lineRange: "L20-L22",  // Key point: Include entire function
  editedContent: "function processData(input) {\\n  const processed = input * 2;\\n  return processed;\\n}"
}]

❌ INCORRECT:
edits: [{
  lineRange: "L21-L21",  // Wrong: Partial function modification
  editedContent: "  const processed = input * 2;\\n  return processed;"
}]

REMEMBER:
1. Use append: true when:
   - Adding new imports
   - Adding new functions/methods
   - Adding JSX elements
   - Any addition that shouldn't replace existing code

2. Don't use append when:
   - Modifying existing function logic
   - Updating variable values
   - Replacing entire code blocks

3. Always:
   - Include complete code blocks in lineRange
   - Verify proper closing of brackets/braces
   - Check surrounding context (±5 lines)
   - Validate imports match the changes
`