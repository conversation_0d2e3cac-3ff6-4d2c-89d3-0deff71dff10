/**
 * This example demonstrates autonomous recursive planning
 * where the AI adapts its approach based on what it discovers
 * in the codebase rather than following a fixed structure.
 */

const autonomousPlanningExample = `
<example id="autonomous_recursive_planning">
  <user_query>Add a dark mode toggle to my app</user_query>
  
  <assistant>
    <thinking>
    I need to understand what this app is and how theming currently works.
    
    From the file outline I can see:
    - App.tsx (likely the entry point)
    - Some navigation files
    - Theme-related files might exist in constants/ or styles/
    - There might be a settings screen where a toggle would make sense
    
    Let me first check for any theme-related files and the app structure to understand the current implementation.
    </thinking>
    
    I'll help you add a dark mode toggle to your app. First, I need to understand your current app structure and theme implementation.
    
    [Using getFileContents to fetch App.tsx and look for any theme-related files]
    
    <thinking>
    Now I have App.tsx and can see:
    - It's a React Native app using React Navigation
    - There's no theme context or provider yet
    - It imports from a constants/theme.ts file
    - The StatusBar is hardcoded to "light" style
    
    I should examine the theme constants file next to understand the current color system. I also need to check the navigation setup since that will need theme integration.
    
    I don't yet know where the settings UI is located, so I'll need to explore that after understanding the theme structure.
    </thinking>
    
    After examining App.tsx, I can see you're using a theme constants file. Let me check that file and your navigation setup to better understand how to implement dark mode.
    
    [Using getFileContents to fetch constants/theme.ts and navigation/index.tsx]
    
    <thinking>
    Now I have more context:
    
    1. The theme.ts file defines:
    - A COLORS object with primary, secondary, text, and UI colors
    - TYPOGRAPHY, SPACING, and other style constants
    - No dark theme variants exist yet
    
    2. The navigation/index.tsx shows:
    - NavigationContainer with a custom theme
    - Theme colors are imported from constants/theme.ts
    - Multiple screens including what looks like a SettingsScreen
    
    I now understand the theme structure, but I need to see the SettingsScreen to know where to add the toggle. I should also check a few UI components to understand how they consume theme values.
    </thinking>
    
    I've examined your theme structure and navigation. Now I need to see your SettingsScreen and a few UI components to understand how to best implement dark mode.
    
    [Using getFileContents to fetch screens/SettingsScreen.tsx and components/Button.tsx]
    
    <thinking>
    With these additional files, I now have a comprehensive understanding:
    
    1. SettingsScreen:
    - Has a list of settings options
    - Uses direct color imports from theme.ts
    - Has no theme toggle yet
    - Has a good section where we can add our toggle
    
    2. Button component:
    - Uses colors directly from theme.ts
    - Will need updating to support both themes
    
    Based on what I've found, here's my implementation plan:
    
    1. Create a ThemeContext to manage theme state (light/dark)
    2. Add dark variants to the theme constants
    3. Update App.tsx to use ThemeProvider
    4. Add toggle to SettingsScreen
    5. Update navigation theme to respond to theme changes
    
    I'll need to check if there are any other key components that might need special handling for dark mode.
    </thinking>
    
    After examining your code, I now have a clear picture of how to implement dark mode. I've noticed that your UI components directly import colors from theme.ts and your SettingsScreen already has a structure for toggles.
    
    [Using getFileContents to fetch components/Card.tsx and any chart components]
    
    <thinking>
    After examining more components, I've discovered:
    
    1. Card.tsx uses shadow styles that will need dark mode variants
    2. There's a LineChart component that uses hardcoded colors for the chart
    3. Several screens use background colors that will need theme awareness
    
    This confirms my approach, but I'll need to be more comprehensive:
    
    1. Create ThemeContext with light/dark state and persistence
    2. Add complete dark variants to all colors in theme.ts
    3. Create special handling for charts and shadows
    4. Update all components to use the current theme
    5. Add a toggle with icon in SettingsScreen
    
    I now have enough information to implement a complete solution.
    </thinking>
    
    Based on my analysis of your codebase, I'll implement a complete dark mode system:
    
    1. First, I'll create a ThemeContext to manage theme state:
    [Implementation details for ThemeContext]
    
    2. Next, I'll update your theme constants with dark variants:
    [Implementation details for theme constants]
    
    3. I'll integrate the ThemeProvider in App.tsx:
    [Implementation details for App.tsx]
    
    4. I'll add a toggle to your SettingsScreen:
    [Implementation details for SettingsScreen]
    
    5. I'll update navigation to use the current theme:
    [Implementation details for navigation]
    
    6. Finally, I'll update your components to be theme-aware:
    [Implementation details for components]
    
    This implementation gives you a complete dark mode system that:
    1. Persists user preference using AsyncStorage
    2. Provides a toggle in settings with a sun/moon icon
    3. Automatically updates all UI components
    4. Handles special cases like charts and shadows
    5. Updates navigation and status bar styles
    
    Would you like me to implement these changes?
  </assistant>
</example>
`;

export default autonomousPlanningExample;
