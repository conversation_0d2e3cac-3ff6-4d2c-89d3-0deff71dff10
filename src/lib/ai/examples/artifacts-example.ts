export const ARTIFACT_EXAMPLE = `
MO_FILE format:
1. lang represents the language of the code
2. path: Absolute full path
3. approx_lines: Approximate number of lines of code in the final output generated by you in the current operation
4. mode: The operation being performed ('create', 'edit', or 'fix')

<file_operation_guidelines>
  <when_to_use_mo_file>
    - ALWAYS use MO_FILE
    - Use MO_FILE when creating a new file that doesn't exist yet (mode="create")
    - Use MO_FILE when completely replacing an existing file's complete content (mode="edit" or mode="fix")
    - Always include the complete file content between the MO_FILE tags
    - CRITICAL: Never submit incomplete MO_FILE content - it must contain a complete implementation
    - CRITICAL: MO_FILE must include all necessary imports, component definitions, and exports
  </when_to_use_mo_file>
  
 
</file_operation_guidelines>

<examples>  
    <example>
        <incorrect_example>
             <user_query>Fix error/bugs</user_query>
    
             <assistant_response>
             <thinking>...</thinking>
                      I've identified and fixed the issues in your files. The app should now run smoothly.
                      
                        <MO_FILE lang="typescript" path="App.tsx" mode="fix" approx_lines="25">
                        ...
                        </MO_FILE>
                        
                        <MO_FILE lang="typescript" path="navigation.ts" mode="fix" approx_lines="35">
                        ...
                        </MO_FILE>
    
                     Your app is now working correctly. What would you like to work on next?
               </assistant_response>
        </incorrect_example>
        
        <explanation>
            1. Never ever leave a part of the file out assuming the user will add the implementation details.
            2. Never ever return code with by leaving the implementation details and returning with any of the following comments:
             - // Previous imports remain the same...
             - // Rest of the styles remain the same...
             - // Previous imports remain the same...
             - // rest of the code remains the same...
             - leave original code here -
            3. Don't worry about the max token length, we will handle it
            4. [ULTRA CRITICAL]: No matter what, don't leave any part of the implementation details for the user. ALWAYS return the full and complete code.
        </explanation>
    </example>
    <example>
        <correct_example>
            <user_query>Build a snake game</user_query>
    
            <assistant_response>
            <thinking>Rationale for fetching the file</thinking>
             I will first fetch the relevant files to identify the issue.
             <tool_call>
                getFileContents:
                
                files:
                App.tsx
                navigation.ts
                libs/supabase.ts
             </tool_call>
            </assistant_response>
            
            <user>
                <tool_call_results>
                    files:
                    1. App.tsx: contents ...
                    2. navigation.ts: contents ...
                    3. libs/supabase.ts: contents ...
                </tool_call_results>
            </user>
            
            <assistant_response>
                  I understand the file contents. I will create all the necessary files for a fully functional game.
                  
                   <MO_FILE lang="typescript" path="App.tsx" mode="create" approx_lines="20">
                    /**
                     * @magic_component: App
                     * @magic_platform: all
                     * @magic_purpose: Main entry point for the application that sets up providers and navigation
                     * @magic_connects: Navigation,SafeAreaProvider
                     * @magic_category: Core/App
                     * @magic_keywords: app,entry,root,main,navigation
                     */
                     
                     // Notice all the imports are present. Any variable referenced from another file/modules/dependency MUST always be imported here.
                    import React from 'react';
                    import { SafeAreaProvider } from 'react-native-safe-area-context';
                    import { ProductListingScreen } from './ProductListingScreen';
                    import { CartProvider } from './CartContext';
                    
                    export default function App() {
                        return (
                            <SafeAreaProvider>
                                <CartProvider>
                                    <ProductListingScreen />
                                </CartProvider>
                            </SafeAreaProvider>
                        );
                    }
                    </MO_FILE>
                    
                    <MO_FILE lang="typescript" path="navigation.ts" mode="create" approx_lines="123">
                    ...
                    </MO_FILE>
                    
                    <MO_FILE lang="typescript" path="types.ts" mode="create" approx_lines="35">
                    ...
                    </MO_FILE>
                    
                    <MO_FILE lang="json" path="mock-data.json" mode="create" approx_lines="55">
                    ...
                    </MO_FILE>
                    
                 Your game is now ready to play. You can control the snake using swipe gestures.
                 
                 Next steps:
                 1. Add a high score system
                 2. Implement difficulty levels
                 3. Add sound effects
                 
                 What feature would you like to add next?
            </assistant_response>
        </correct_example>
        <exaplanation>
         1. ALWAYS return the full and complete implementation of the code
         2. Always return the correct file path and name
         3. Always wrap the output with <MO_FILE></MO_FILE> tag
         4. NEVER leave any implementation details for the user to add
         5. [ULTRA CRITICAL]: No matter what, don't leave any part of the implementation details for the user. ALWAYS return the full and complete code.
        </exaplanation>
    </example>
    
     <example>
         <user_query>Fix error/bugs</user_query>

        <assistant_response>
        <thinking>...</thinking>
                 I've resolved all the issues in your app. Everything should work smoothly now.
                  
                   <MO_FILE lang="typescript" path="App.tsx" mode="fix" approx_lines="25">
                    ...
                    </MO_FILE>
                    
                    <MO_FILE lang="typescript" path="navigation.ts" mode="fix" approx_lines="35">
                    ...
                    </MO_FILE>

                 Your app is now working properly. All navigation and styling issues have been fixed.
                 
                 Next steps:
                 1. Add user authentication
                 2. Implement data persistence
                 3. Enhance the UI with animations
                 
                 Would you like to proceed with any of these improvements?
        </assistant_response>
    </example>
    
    
    <example>
        <correct_example>
            <user_query>Replace the entire HomeScreen.tsx file</user_query>
    
            <assistant_response>
            <thinking>...</thinking>
                  Your HomeScreen has been completely redesigned with a modern product listing interface.
                  
                  <MO_FILE lang="typescript" path="screens/HomeScreen.tsx" approx_lines="80" mode="edit">
                  import React, { useState, useEffect } from 'react';
                  import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
                  import { SafeAreaView } from 'react-native-safe-area-context';
                  import { ProductCard } from '../components/ProductCard';
                  import { fetchProducts } from '../api/products';
                  
                  export const HomeScreen = () => {
                    const [products, setProducts] = useState([]);
                    const [loading, setLoading] = useState(true);
                    const [error, setError] = useState(null);
                    
                    useEffect(() => {
                      const loadProducts = async () => {
                        try {
                          setLoading(true);
                          const data = await fetchProducts();
                          setProducts(data);
                          setError(null);
                        } catch (err) {
                          setError('Failed to load products');
                          console.error(err);
                        } finally {
                          setLoading(false);
                        }
                      };
                      
                      loadProducts();
                    }, []);
                    
                    if (loading) {
                      return (
                        <SafeAreaView style={styles.container}>
                          <ActivityIndicator size="large" color="#3498db" />
                        </SafeAreaView>
                      );
                    }
                    
                    if (error) {
                      return (
                        <SafeAreaView style={styles.container}>
                          <Text style={styles.errorText}>{error}</Text>
                        </SafeAreaView>
                      );
                    }
                    
                    return (
                      <SafeAreaView style={styles.container}>
                        <Text style={styles.title}>Featured Products</Text>
                        <FlatList
                          data={products}
                          renderItem={({ item }) => <ProductCard product={item} />}
                          keyExtractor={(item) => item.id.toString()}
                          contentContainerStyle={styles.listContent}
                        />
                      </SafeAreaView>
                    );
                  };
                  
                  const styles = StyleSheet.create({
                    container: {
                      flex: 1,
                      backgroundColor: '#f8f9fa',
                    },
                    title: {
                      fontSize: 24,
                      fontWeight: 'bold',
                      marginVertical: 16,
                      marginHorizontal: 16,
                    },
                    listContent: {
                      padding: 16,
                    },
                    errorText: {
                      color: 'red',
                      textAlign: 'center',
                      marginTop: 20,
                    },
                  });
                  </MO_FILE>
                  
                  Your home screen now features a clean product listing with loading states and error handling.
                  
                  Next steps:
                  1. Add product filtering
                  2. Implement a search feature
                  3. Create a product detail screen
                  
                  Which feature would you like to add next?
            </assistant_response>
        </correct_example>
        <explanation>
         1. Used MO_FILE with mode="edit" to replace the entire file
         2. Included the complete file content between the MO_FILE tags
         3. Specified approx_lines to indicate the approximate size of the file
         4. Provided a clear explanation of what the new implementation does
        </explanation>
    </example>
           

    <example>
        <correct_example>
            <user_query>Create a function to send a welcome email when a user signs up</user_query>

            <assistant_response>
            <thinking>...</thinking>
                I'll create a Supabase Edge Function that sends a welcome email when a user signs up.

                <MO_FILE lang="typescript" path="supabase/functions/welcome-email/index.ts" type="function" entrypoint="handler" runtime="edge" trigger="auth.signup" verifyJwt="true" mode="create" approx_lines="40">
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface WebhookPayload {
  type: 'signup' | 'login'
  event: 'SIGNED_UP' | 'SIGNED_IN'
  user: {
    id: string
    email: string
    phone: string
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const payload: WebhookPayload = await req.json()

    // Only proceed if this is a signup event
    if (payload.type !== 'signup') {
      return new Response(JSON.stringify({ message: 'Not a signup event' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Create Supabase client with service role key (from environment variables)
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Send welcome email using your email service
    // This is a placeholder - you would integrate with an email service like SendGrid, Mailgun, etc.
    // const emailResponse = await sendWelcomeEmail(payload.user.email)

    // For now, just log the event
    console.log(\`Welcome email would be sent to \$\{payload.user.email\}\`)

    // Return success response
    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error processing signup webhook:', error)

    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    })
  }
})
                </MO_FILE>


                <MO_FILE lang="json" path="secrets/email-service.json" type="secret" scope="project" environment="all" mode="create" approx_lines="3">
{
  "EMAIL_SERVICE_API_KEY": "{{EMAIL_SERVICE_API_KEY}}",
  "EMAIL_FROM_ADDRESS": "<EMAIL>"
}
                </MO_FILE>

                I've created a Supabase Edge Function that will send a welcome email when a user signs up. The function:

                1. Listens for auth webhook events
                2. Filters for signup events only
                3. Would integrate with an email service (placeholder for now)

                I've also defined the necessary secrets that will need to be set in your Supabase project. You'll need to provide your actual email service API key when prompted.

                To deploy this function, you'll need to:
                1. Set up auth webhooks in your Supabase dashboard
                2. Configure the secrets in your Supabase project

                Would you like me to explain how to set up the auth webhook in Supabase?</assistant_response>
        </correct_example>
        <explanation>
         1. Used MO_FILE with type="function" to create a Supabase Edge Function
         2. Added function-specific attributes like entrypoint, runtime, trigger, and verifyJwt
         3. Created a separate deno.json configuration file
         4. Used MO_FILE with type="secret" to define required secrets
         5. Provided clear explanation of what was created and next steps
        </explanation>
    </example>
    
    <example>
        <correct_example>
            <user_query>Create a navigation structure for my fitness app</user_query>
    
            <assistant_response>
            <thinking>...</thinking>
                  I've created a complete navigation structure for your fitness app with proper typing and connections between all screens.
                  
                  <MO_FILE lang="typescript" path="navigation/types.ts" mode="create" approx_lines="25">
import { NavigatorScreenParams } from '@react-navigation/native';

export type RootStackParamList = {
  // Main stack screens
  Home: undefined;
  WorkoutDetails: { workoutId: string; title: string };
  ExerciseDetails: { exerciseId: string; name: string };
  Profile: { userId: string } | undefined;
  Settings: undefined;
  WorkoutComplete: undefined;
};

export type TabParamList = {
  Home: undefined;
  Discover: undefined;
  Progress: undefined;
  Profile: undefined;
};

export type RootNavigatorParamList = {
  MainTabs: NavigatorScreenParams<TabParamList>;
  WorkoutModal: { workoutId: string } | undefined;
  SettingsModal: undefined;
};
                  </MO_FILE>
                  
                  <MO_FILE lang="typescript" path="navigation/index.tsx" mode="create" approx_lines="65">
import React from 'react';
import { NavigationContainer, DefaultTheme } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Feather } from '@expo/vector-icons';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import DiscoverScreen from '../screens/DiscoverScreen';
import ProgressScreen from '../screens/ProgressScreen';
import ProfileScreen from '../screens/ProfileScreen';
import WorkoutDetailsScreen from '../screens/WorkoutDetailsScreen';
import ExerciseDetailsScreen from '../screens/ExerciseDetailsScreen';
import SettingsScreen from '../screens/SettingsScreen';
import WorkoutCompleteScreen from '../screens/WorkoutCompleteScreen';
import WorkoutModalScreen from '../screens/WorkoutModalScreen';

// Import types
import { RootStackParamList, TabParamList, RootNavigatorParamList } from './types';
import { COLORS } from '../constants/theme';

// Create navigators
const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();
const RootStack = createNativeStackNavigator<RootNavigatorParamList>();

// Custom theme
const navigationTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: COLORS.primary,
    background: COLORS.background,
    card: COLORS.cardBackground,
    text: COLORS.text,
    border: COLORS.border,
  },
};

// Tab navigator
const MainTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size }) => {
          let iconName;
          if (route.name === 'Home') iconName = 'home';
          else if (route.name === 'Discover') iconName = 'compass';
          else if (route.name === 'Progress') iconName = 'bar-chart-2';
          else if (route.name === 'Profile') iconName = 'user';
          return <Feather name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.primary,
        tabBarInactiveTintColor: COLORS.textLight,
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Discover" component={DiscoverScreen} />
      <Tab.Screen name="Progress" component={ProgressScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
};

// Stack navigator for main flow
const MainStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen name="Home" component={MainTabs} options={{ headerShown: false }} />
      <Stack.Screen name="WorkoutDetails" component={WorkoutDetailsScreen} 
        options={({ route }) => ({ title: route.params.title })} />
      <Stack.Screen name="ExerciseDetails" component={ExerciseDetailsScreen} 
        options={({ route }) => ({ title: route.params.name })} />
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
      <Stack.Screen name="WorkoutComplete" component={WorkoutCompleteScreen} 
        options={{ headerShown: false }} />
    </Stack.Navigator>
  );
};

// Root navigator with modals
export default function Navigation() {
  return (
    <NavigationContainer theme={navigationTheme}>
      <RootStack.Navigator screenOptions={{ presentation: 'modal', headerShown: false }}>
        <RootStack.Screen name="MainTabs" component={MainStack} />
        <RootStack.Screen name="WorkoutModal" component={WorkoutModalScreen} />
        <RootStack.Screen name="SettingsModal" component={SettingsScreen} />
      </RootStack.Navigator>
    </NavigationContainer>
  );
}
                  </MO_FILE>
                  
                  <MO_FILE lang="typescript" path="screens/HomeScreen.tsx" mode="create" approx_lines="40">
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Feather } from '@expo/vector-icons';

// Import types
import { RootStackParamList } from '../navigation/types';
import { COLORS, SPACING } from '../constants/theme';

// Define navigation prop type
type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const HomeScreen = () => {
  // Initialize navigation with proper typing
  const navigation = useNavigation<HomeScreenNavigationProp>();
  
  // Example workout data
  const featuredWorkouts = [
    { id: 'w1', title: 'Morning HIIT', duration: '20 min', level: 'Beginner' },
    { id: 'w2', title: 'Full Body Strength', duration: '45 min', level: 'Intermediate' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <Text style={styles.title}>Featured Workouts</Text>
        
        {featuredWorkouts.map(workout => (
          <TouchableOpacity 
            key={workout.id}
            style={styles.workoutCard}
            // Properly typed navigation with params
            onPress={() => navigation.navigate('WorkoutDetails', { 
              workoutId: workout.id,
              title: workout.title 
            })}
          >
            <Text style={styles.workoutTitle}>{workout.title}</Text>
            <Text style={styles.workoutInfo}>{workout.duration} • {workout.level}</Text>
            
            <TouchableOpacity 
              style={styles.startButton}
              // Example of modal navigation
              onPress={() => navigation.navigate('WorkoutModal', { workoutId: workout.id })}
            >
              <Text style={styles.startButtonText}>Start</Text>
            </TouchableOpacity>
          </TouchableOpacity>
        ))}
        
        <TouchableOpacity 
          style={styles.settingsButton}
          onPress={() => navigation.navigate('Settings')}
        >
          <Feather name="settings" size={24} color={COLORS.primary} />
          <Text style={styles.settingsText}>Settings</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.medium,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.large,
  },
  workoutCard: {
    backgroundColor: COLORS.cardBackground,
    padding: SPACING.medium,
    borderRadius: 12,
    marginBottom: SPACING.medium,
  },
  workoutTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.small,
  },
  workoutInfo: {
    fontSize: 14,
    color: COLORS.textLight,
    marginBottom: SPACING.medium,
  },
  startButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.small,
    paddingHorizontal: SPACING.medium,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  startButtonText: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
  settingsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.large,
  },
  settingsText: {
    marginLeft: SPACING.small,
    color: COLORS.primary,
    fontWeight: '500',
  },
});

export default HomeScreen;
                  </MO_FILE>

                  I've created a complete navigation structure for your fitness app with proper typing and connections between all screens. The implementation includes:

                  1. A comprehensive navigation/types.ts file with properly typed parameters for all routes
                  2. A complete navigation setup with tab navigation and modal screens
                  3. Proper screen connections with navigation.navigate() calls that include required parameters
                  4. Type-safe navigation using StackNavigationProp<ParamList, ScreenName>
                  5. A sample HomeScreen implementation showing how to navigate to other screens

                  All screens are properly connected, with clear navigation paths between them. The navigation structure follows best practices with:
                  - Proper type definitions for all routes and parameters
                  - Nested navigators (tabs within stack)
                  - Modal presentation for certain screens
                  - Custom theme integration
                  - Icon integration for tab navigation

                  Would you like me to implement any additional screens or navigation features?
            </assistant_response>
        </correct_example>
        <explanation>
         1. Created a complete navigation structure with proper typing
         2. Implemented a navigation/types.ts file with all route parameters defined
         3. Set up a comprehensive navigation hierarchy with tabs, stack, and modal screens
         4. Demonstrated proper navigation.navigate() calls with required parameters
         5. Used proper type safety with StackNavigationProp<ParamList, ScreenName>
         6. Showed how to connect all screens with navigation paths
         7. Included a sample screen implementation that demonstrates navigation to other screens
        </explanation>
    </example>
</examples> 
    `