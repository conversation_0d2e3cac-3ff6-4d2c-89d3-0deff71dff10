export const STREAMLINED_ARTIFACT_EXAMPLE = `
<file_operations>
  ⚠️ MO_FILE FORMAT:
  • Format: <MO_FILE lang="language" path="path/to/file" approx_lines="number" mode="operation">code</MO_FILE>
  • Purpose: New files or complete replacements
  • Modes: "create" (new), "edit"/"fix" (replace existing)
  • CRITICAL: Include ALL imports, complete implementation, proper exports
  • NEVER use comments like "// rest of code remains the same" or ellipses (...)

  ⚠️ MO_DIFF FORMAT:
  • Format: <MO_DIFF lang="language" path="path/to/file"><SEARCH>old</SEARCH><REPLACE>new</REPLACE></MO_DIFF>
  • Purpose: Targeted edits (<10% of file)
  • NEVER use for JSX, styles, or complex patterns
  • SEARCH must match EXACTLY what's in the file
  • One file = ONE MO_DIFF block per message

  ⚠️ MO_DATABASE_QUERY FORMAT:
  • Format: <MO_DATABASE_QUERY>SQL query</MO_DATABASE_QUERY>
  • Purpose: Database schema changes when Supabase is connected
  • Include both migration file AND immediate query
  • Use IF EXISTS/IF NOT EXISTS for safety
</file_operations>

<usage_rules>
  ⚠️ WHEN TO USE MO_FILE:
  • ALWAYS for new files (mode="create")
  • ALWAYS for full replacements (mode="edit"/"fix")
  • When changes affect >10% of file
  • For ANY JSX structures or style objects
  • When in doubt, use MO_FILE with complete implementation

  ⚠️ WHEN TO USE MO_DIFF:
  • ONLY for tiny changes (<10% of file)
  • ONLY for simple text replacements
  • NEVER for complex structures
  • NEVER for multiple parts of a file
  • NEVER when unsure about exact file content

  ⚠️ WHEN TO USE MO_DATABASE_QUERY:
  • ONLY when Supabase is connected
  • For schema changes, migrations, triggers, functions
  • Always check <SUPABASE_INSTRUCTIONS> first
  • Always include proper error handling
</usage_rules>
`;
