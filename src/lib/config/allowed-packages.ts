/**
 * Allowed packages for dynamic backend execution
 * 
 * This file defines the list of npm packages that are safe to use in the dynamic backend environment.
 * The same list is used both in the sandbox execution environment and in the AI prompt.
 */

export interface PackageInfo {
  name: string;
  description: string;
  version?: string;
}

/**
 * List of allowed packages with descriptions
 */
export const ALLOWED_PACKAGES: PackageInfo[] = [
  // Utility libraries
  { 
    name: 'lodash', 
    description: 'A modern JavaScript utility library delivering modularity, performance & extras',
    version: '4.17.21'
  },
  { 
    name: 'date-fns', 
    description: 'Modern JavaScript date utility library',
    version: '2.30.0'
  },
  { 
    name: 'uuid', 
    description: 'For the creation of RFC4122 UUIDs',
    version: '9.0.0'
  },
  
  // HTTP clients
  { 
    name: 'axios', 
    description: 'Promise based HTTP client for the browser and node.js',
    version: '1.4.0'
  },
  { 
    name: 'node-fetch', 
    description: 'A light-weight module that brings Fetch API to Node.js',
    version: '3.3.1'
  },
  
  // Validation
  { 
    name: 'joi', 
    description: 'Object schema validation',
    version: '17.9.2'
  },
  { 
    name: 'validator', 
    description: 'String validation and sanitization',
    version: '13.9.0'
  },
  
  // Parsing and formatting
  { 
    name: 'marked', 
    description: 'A markdown parser and compiler',
    version: '5.0.2'
  },
  { 
    name: 'json5', 
    description: 'JSON for humans',
    version: '2.2.3'
  },
  
  // Cryptography
  { 
    name: 'crypto-js', 
    description: 'JavaScript library of crypto standards',
    version: '4.1.1'
  },
  { 
    name: 'jsonwebtoken', 
    description: 'JSON Web Token implementation for authentication',
    version: '9.0.2'
  },
  
  // Data manipulation
  { 
    name: 'csv-parser', 
    description: 'Streaming CSV parser',
    version: '3.0.0'
  },
  { 
    name: 'papaparse', 
    description: 'Fast and powerful CSV parser for the browser',
    version: '5.4.1'
  },
  
  // Environment utilities
  { 
    name: 'dotenv', 
    description: 'Loads environment variables from .env file',
    version: '16.0.3'
  }
];

/**
 * Get a map of allowed package names for quick lookup
 */
export function getAllowedPackageMap(): Record<string, PackageInfo> {
  return ALLOWED_PACKAGES.reduce((acc, pkg) => {
    acc[pkg.name] = pkg;
    return acc;
  }, {} as Record<string, PackageInfo>);
}

/**
 * Get a formatted string of allowed packages for documentation
 */
export function getFormattedPackageList(): string {
  return ALLOWED_PACKAGES.map(pkg => 
    `- ${pkg.name}${pkg.version ? ` (v${pkg.version})` : ''}: ${pkg.description}`
  ).join('\n');
}
