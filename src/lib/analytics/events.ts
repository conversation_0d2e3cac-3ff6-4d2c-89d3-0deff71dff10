export const ANALYTICS_EVENTS = {
  USER: {
    SIGNED_UP: 'user_signed_up',
    SIGNED_IN: 'user_signed_in',
    SIGNED_OUT: 'user_signed_out',
    SESSION_EXPIRED: 'user_session_expired',
    ANONYMOUS_ID_GENERATED: 'anonymous_id_generated',
    FIRST_TIME_USER: 'first_time_user_identified'
  },
  SUBSCRIPTION: {
    UPGRADE_DIALOG_VIEWED: 'upgrade_dialog_viewed',
    UPGRADE_INITIATED: 'upgrade_initiated',
    UPGRADE_COMPLETED: 'upgrade_completed',
    UPGRADE_FAILED: 'upgrade_failed',
    UPGRADE_DISMISSED: 'upgrade_dismissed',
    PRICING_VIEWED: 'pricing_comparison_viewed',
    SUBSCRIPTION_STARTED: 'subscription_started',
    SUBSCRIPTION_COMPLETED: 'subscription_completed',
    SUBSCRIPTION_FAILED: 'subscription_failed',
    CREDIT_LIMIT_REACHED: 'credit_limit_reached'
  },
  MESSAGES: {
    GENERATION_STARTED: 'message_generation_started',
    GENERATION_COMPLETED: 'message_generation_completed',
    GENERATION_FAILED: 'message_generation_failed',
    LIMIT_REACHED: 'message_limit_reached',
    SENT: 'message_sent',
    RECEIVED: 'message_received',
    REGENERATED: 'message_regenerated',
    STOPPED: 'message_stopped',
    ERROR: 'message_error',
    EDITED: 'message_edited',
    CHAT_STARTED: 'chat_started',
    CHAT_RESUMED: 'chat_resumed',
    AI_RESPONSE_STREAMING: 'ai_response_streaming',
    AI_RESPONSE_RECEIVED: 'ai_response_received',
    AI_RESPONSE_INTERRUPTED: 'ai_response_interrupted',
    CONVERSATION_ABANDONED: 'conversation_abandoned'
  },
  IDEA_FLOW: {
    FORM_VIEWED: 'idea_form_viewed',
    FORM_FOCUSED: 'idea_form_focused',
    IMAGE_ATTACHED: 'image_attached',
    IMAGE_REMOVED: 'image_removed',
    IDEA_SUBMITTED: 'idea_submitted',
    PROJECT_CREATED: 'project_created',
    PROJECT_CREATION_FAILED: 'project_creation_failed',
    FIRST_PREVIEW_LOADED: 'first_preview_loaded',
    AUTH_GUARD_TRIGGERRED: 'auth_guard_triggered',
    AUTH_GUARD_CLEARED: 'auth_guard_cleared'
  },
  FEATURES: {
    AI_ASSIST_TOGGLED: 'ai_assist_mode_toggled',
    CUSTOM_PROMPT_CREATED: 'custom_prompt_created',
    CUSTOM_PROMPT_USED: 'custom_prompt_used',
    AGENT_MODE_TOGGLED: 'agent_mode_toggled',
    FILE_TREE_NAVIGATED: 'file_tree_navigated',
    FILE_SELECTED: 'file_selected',
    CODE_EDITED: 'code_edited',
    CODE_SNIPPET_COPIED: 'code_snippet_copied',
    PREVIEW_REFRESHED: 'preview_refreshed',
    SUPABASE_CONNECTION_INITIATED: 'supabase_connection_initiated',
    SUPABASE_CONNECTION_COMPLETED: 'supabase_connection_completed',
    DEPLOYMENT_INITIATED: 'deployment_initiated',
    DEPLOYMENT_COMPLETED: 'deployment_completed',
    DEPLOYMENT_FAILED: 'deployment_failed',
    QR_CODE_SCANNED: 'qr_code_scanned',
    FULL_SCREEN_TOGGLED: 'full_screen_toggled',
    DISCORD_HELP_TRIGGERRED: 'discord_help_triggered',
    MESSAGE_VOTED: 'message_voted',
    CHECKPOINT_RESTORED: 'message_checkpoint_restored'
  },
  ONBOARDING: {
    STARTED: 'onboarding_started',
    STEP_COMPLETED: 'onboarding_step_completed',
    COMPLETED: 'onboarding_completed',
    SKIPPED: 'onboarding_skipped',
    HELP_ACCESSED: 'help_resource_accessed',
    TOOLTIP_SHOWN: 'tooltip_shown',
    TOOLTIP_DISMISSED: 'tooltip_dismissed'
  },
  ERROR: {
    OCCURRED: 'error_occurred',
    RENDERING_ERROR: 'rendering_error',
    RESOURCE_LOADING_FAILED: 'resource_loading_failed'
  },
  PERFORMANCE: {
    PAGE_LOAD_COMPLETE: 'page_load_complete',
    API_REQUEST_INITIATED: 'api_request_initiated',
    API_REQUEST_COMPLETE: 'api_request_complete',
    API_REQUEST_FAILED: 'api_request_failed',
    CREDIT_CALCULATION_PERFORMED: 'credit_calculation_performed'
  }
} as const;
