/**
 * Tracks metrics for diffs, such as line counts
 */
export class DiffMetrics {
    private diffStats: Record<string, { 
        searchLines: number; 
        replaceLines: number;
        totalLines: number;
    }> = {};

    /**
     * Add search block to metrics
     */
    public addSearchBlock(path: string, content: string): void {
        if (!this.diffStats[path]) {
            this.diffStats[path] = { searchLines: 0, replaceLines: 0, totalLines: 0 };
        }
        
        const lineCount = this.countLines(content);
        this.diffStats[path].searchLines += lineCount;
        this.diffStats[path].totalLines += lineCount;
    }

    /**
     * Add replace block to metrics
     */
    public addReplaceBlock(path: string, content: string): void {
        if (!this.diffStats[path]) {
            this.diffStats[path] = { searchLines: 0, replaceLines: 0, totalLines: 0 };
        }
        
        const lineCount = this.countLines(content);
        this.diffStats[path].replaceLines += lineCount;
        this.diffStats[path].totalLines += lineCount;
    }

    /**
     * Get search line count for a file
     */
    public getSearchLineCount(path: string): number {
        return this.diffStats[path]?.searchLines || 0;
    }

    /**
     * Get replace line count for a file
     */
    public getReplaceLineCount(path: string): number {
        return this.diffStats[path]?.replaceLines || 0;
    }

    /**
     * Get total line count for a file
     */
    public getTotalLineCount(path: string): number {
        return this.diffStats[path]?.totalLines || 0;
    }

    /**
     * Clear metrics for a file
     */
    public clear(path: string): void {
        delete this.diffStats[path];
    }

    /**
     * Clear all metrics
     */
    public clearAll(): void {
        this.diffStats = {};
    }

    /**
     * Count lines in a string
     */
    private countLines(text: string): number {
        return text ? (text.match(/\n/g) || []).length + 1 : 0;
    }
}
