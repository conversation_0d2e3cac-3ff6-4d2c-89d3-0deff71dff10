import { DiffMeta } from "../parser/DiffParser";
import { embed } from 'ai';
import { openai } from '@ai-sdk/openai';

/**
 * FileContentManager handles applying diffs to files
 * It keeps track of the current content of files and applies
 * search/replace operations or full file replacements
 */
export class FileContentManager {
    private fileContents: Record<string, string> = {};
    
    /**
     * Set the content of a file
     */
    public setFileContent(path: string, content: string): void {
        this.fileContents[path] = content;
    }
    
    /**
     * Get the current content of a file
     */
    public getFileContent(path: string): string | null {
        return this.fileContents[path] || null;
    }
    
    /**
     * Apply a diff to a file
     * @returns Result object with success flag and any error message
     */
    public async applyDiff(diff: DiffMeta, options: { bestEffort?: boolean } = {}): Promise<{ success: boolean; message?: string; content?: string }> {
        const { path, searches, replacements } = diff;
        const { bestEffort = false } = options;
        
        // Get current file content
        const currentContent = this.getFileContent(path);
        
        // Check for empty search string, which indicates a full file replacement
        if (searches.length === 1 && searches[0].trim() === '') {
            if (replacements.length !== 1) {
                return { 
                    success: false, 
                    message: 'Full replacement requires exactly one replacement content block' 
                };
            }
            
            const newContent = replacements[0];
            this.setFileContent(path, newContent);
            return { 
                success: true, 
                content: newContent 
            };
        }
        
        // Otherwise, handle search/replace pairs
        if (!currentContent) {
            return { 
                success: false, 
                message: `File ${path} not found. Cannot apply partial edits to a non-existent file.` 
            };
        }
        
        if (searches.length !== replacements.length) {
            return { 
                success: false, 
                message: `Mismatched search/replace pairs (${searches.length} searches, ${replacements.length} replacements)` 
            };
        }
        
        let updatedContent = currentContent;
        const failedSearches: number[] = [];
        const successfulSearches: number[] = [];
        
        // Apply each search/replace pair
        for (let i = 0; i < searches.length; i++) {
            const search = searches[i];
            const replace = replacements[i];
            
            // Try exact matching first (original method)
            if (updatedContent.includes(search)) {
                // console.log(`[FileContentManager] Exact match found for search pattern #${i+1}`);
                
                // If there's more than one match, we have an ambiguity problem
                const matches = this.countMatches(updatedContent, search);
                if (matches > 1) {
                    // console.log(`[FileContentManager] Ambiguous match (${matches} occurrences) for search pattern #${i+1}`);
                    failedSearches.push(i + 1);
                    continue;
                }
                
                // Use replace() with string, not regex, to avoid special character issues
                updatedContent = updatedContent.replace(search, replace);
                successfulSearches.push(i + 1);
            } else {
                // console.log(`[FileContentManager] Exact match failed for search pattern #${i+1}, trying normalized comparison...`);
                
                // Try normalized comparison (second fallback)
                try {
                    const normalizedResult = this.applyWithNormalizedComparison(updatedContent, search, replace);
                    if (normalizedResult.success) {
                        // console.log(`[FileContentManager] Normalized comparison succeeded for search pattern #${i+1}`);
                        updatedContent = normalizedResult.content;
                        successfulSearches.push(i + 1);
                    } else {
                        // console.log(`[FileContentManager] Normalized comparison failed for search pattern #${i+1}, trying enhanced fuzzy matching...`);
                        
                        // Try enhanced fuzzy matching (third fallback)
                        try {
                            const fuzzyResult = this.applyWithEnhancedFuzzyMatching(updatedContent, search, replace);
                            if (fuzzyResult.success) {
                                // console.log(`[FileContentManager] Enhanced fuzzy matching succeeded for search pattern #${i+1}`);
                                updatedContent = fuzzyResult.content;
                                successfulSearches.push(i + 1);
                            } else {
                                // console.log(`[FileContentManager] All fallback methods failed for search pattern #${i+1}`);
                                failedSearches.push(i + 1);
                            }
                        } catch (error) {
                            console.error(`[FileContentManager] Fuzzy matching error for search pattern #${i+1}:`, error);
                            failedSearches.push(i + 1);
                        }
                    }
                } catch (error) {
                    console.error(`[FileContentManager] Normalized comparison error for search pattern #${i+1}:`, error);
                    failedSearches.push(i + 1);
                }
            }
        }
        
        // If any searches failed and we're not in best effort mode, report failure
        if (failedSearches.length > 0 && !bestEffort) {
            return {
                success: false,
                message: `Failed to find matches for search patterns: ${failedSearches.join(', ')}`
            };
        }
        
        // Update the file content even if some searches failed in best effort mode
        this.setFileContent(path, updatedContent);
        
        // Return success with a warning if some patterns failed in best effort mode
        if (failedSearches.length > 0 && bestEffort) {
            return {
                success: true,
                content: updatedContent,
                message: `Applied ${successfulSearches.length} patterns successfully. Failed patterns: ${failedSearches.join(', ')}`
            };
        }
        
        return {
            success: true,
            content: updatedContent
        };
    }
    
    /**
     * Apply diff using normalized comparison
     * This approach is more tolerant of whitespace and indentation changes
     */
    private applyWithNormalizedComparison(
        content: string, 
        search: string, 
        replace: string
    ): { success: boolean; content: string } {
        try {
            // Normalize content and search by removing whitespace variations
            const normalizedContent = this.normalizeCode(content);
            const normalizedSearch = this.normalizeCode(search);
            
            // If normalized search is too short, it's too ambiguous
            if (normalizedSearch.length < 10) {
                // console.log('[FileContentManager] Normalized search too short, skipping');
                return { success: false, content };
            }
            
            // Check if normalized search exists in normalized content
            if (!normalizedContent.includes(normalizedSearch)) {
                // console.log('[FileContentManager] No match found after normalization');
                return { success: false, content };
            }
            
            // Find all occurrences of the normalized search
            const matches = this.findAllMatches(normalizedContent, normalizedSearch);
            // console.log(`[FileContentManager] Found ${matches.length} matches after normalization`);
            
            // If there's more than one match, we have an ambiguity problem
            if (matches.length > 1) {
                // console.log('[FileContentManager] Ambiguous match after normalization');
                return { success: false, content };
            }
            
            // Find the actual text in the original content
            const matchIndex = matches[0];
            const originalMatchStart = this.findOriginalIndex(content, normalizedContent, matchIndex);
            // @ts-ignore
            const originalMatchEnd = this.findOriginalEndIndex(content, normalizedContent, matchIndex + normalizedSearch.length);
            
            if (originalMatchStart === -1 || originalMatchEnd === -1) {
                // console.log('[FileContentManager] Failed to map normalized indices back to original content');
                return { success: false, content };
            }
            
            // Replace the content
            const updatedContent = 
                content.substring(0, originalMatchStart) + 
                replace + 
                content.substring(originalMatchEnd);
            
            return { success: true, content: updatedContent };
        } catch (error) {
            console.error('[FileContentManager] Normalized comparison error:', error);
            return { success: false, content };
        }
    }
    
    /**
     * Normalize code by removing extra whitespace and standardizing indentation
     */
    private normalizeCode(code: string): string {
        // Remove comments
        let normalized = code.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '');
        
        // Normalize whitespace
        normalized = normalized
            // Replace multiple spaces with a single space
            .replace(/\s+/g, ' ')
            // Remove spaces after opening and before closing brackets
            .replace(/\{\s+/g, '{').replace(/\s+\}/g, '}')
            .replace(/\(\s+/g, '(').replace(/\s+\)/g, ')')
            .replace(/\[\s+/g, '[').replace(/\s+\]/g, ']')
            // Remove spaces before and after operators
            .replace(/\s*([=+\-*/%&|^!<>:;,.])\s*/g, '$1')
            // Normalize line breaks
            .replace(/[\r\n]+/g, '\n');
        
        return normalized;
    }
    
    /**
     * Find all matches of a pattern in a string
     */
    private findAllMatches(text: string, pattern: string): number[] {
        const matches: number[] = [];
        let index = 0;
        
        while ((index = text.indexOf(pattern, index)) !== -1) {
            matches.push(index);
            index += pattern.length;
        }
        
        return matches;
    }
    
    /**
     * Apply diff using enhanced fuzzy matching
     * This approach combines multiple strategies for maximum reliability
     */
    private applyWithEnhancedFuzzyMatching(
        content: string, 
        search: string, 
        replace: string
    ): { success: boolean; content: string } {
        try {
            // 1. First try line-based context matching
            const lineBasedResult = this.applyWithLineContextMatching(content, search, replace);
            if (lineBasedResult.success) {
                // console.log('[FileContentManager] Line-based context matching succeeded');
                return lineBasedResult;
            }
            
            // 2. Try token-based fuzzy matching with multiple similarity metrics
            const tokenBasedResult = this.applyWithTokenMatching(content, search, replace);
            if (tokenBasedResult.success) {
                // console.log('[FileContentManager] Token-based fuzzy matching succeeded');
                return tokenBasedResult;
            }
            
            // 3. Try n-gram similarity as last resort
            const ngramResult = this.applyWithNGramMatching(content, search, replace);
            if (ngramResult.success) {
                // console.log('[FileContentManager] N-gram matching succeeded');
                return ngramResult;
            }
            
            return { success: false, content };
        } catch (error) {
            console.error('[FileContentManager] Enhanced fuzzy matching error:', error);
            return { success: false, content };
        }
    }
    
    /**
     * Apply diff using line-based context matching
     * This approach is good for code that has been reformatted but structure remains similar
     */
    private applyWithLineContextMatching(
        content: string, 
        search: string, 
        replace: string
    ): { success: boolean; content: string } {
        try {
            // Split content and search into lines
            const contentLines = content.split('\n');
            const searchLines = search.split('\n');
            
            // If search is too short, it's too ambiguous
            if (searchLines.length < 2) {
                return { success: false, content };
            }
            
            // Create a signature from the first and last few lines of the search
            const createSignature = (lines: string[]) => {
                const signatureLines: string[] = [];
                const numLines = Math.min(3, Math.floor(lines.length / 2));
                
                // Add first few lines
                for (let i = 0; i < numLines; i++) {
                    signatureLines.push(this.getSignificantTokens(lines[i]));
                }
                
                // Add last few lines
                for (let i = lines.length - numLines; i < lines.length; i++) {
                    signatureLines.push(this.getSignificantTokens(lines[i]));
                }
                
                return signatureLines.join('|');
            };
            
            const searchSignature = createSignature(searchLines);
            
            // Slide through content lines looking for matching signature
            let bestMatchIndex = -1;
            let bestMatchScore = 0;
            
            for (let i = 0; i <= contentLines.length - searchLines.length; i++) {
                const windowLines = contentLines.slice(i, i + searchLines.length);
                const windowSignature = createSignature(windowLines);
                
                const score = this.calculateStringSimilarity(searchSignature, windowSignature);
                if (score > bestMatchScore && score > 0.8) {
                    bestMatchScore = score;
                    bestMatchIndex = i;
                }
            }
            
            if (bestMatchIndex !== -1) {
                // console.log(`[FileContentManager] Line context match found with score: ${bestMatchScore.toFixed(4)}`);
                
                // Replace the content
                const updatedContentLines = [...contentLines];
                updatedContentLines.splice(bestMatchIndex, searchLines.length, replace);
                
                return { 
                    success: true, 
                    content: updatedContentLines.join('\n') 
                };
            }
            
            return { success: false, content };
        } catch (error) {
            console.error('[FileContentManager] Line context matching error:', error);
            return { success: false, content };
        }
    }
    
    /**
     * Apply diff using token-based fuzzy matching
     */
    private applyWithTokenMatching(
        content: string, 
        search: string, 
        replace: string
    ): { success: boolean; content: string } {
        try {
            // Tokenize the content and search pattern
            const contentTokens = this.tokenizeCode(content);
            const searchTokens = this.tokenizeCode(search);
            
            // If search is too short after tokenization, it's too ambiguous
            if (searchTokens.length < 3) {
                return { success: false, content };
            }
            
            // Find the best matching position using sliding window with multiple window sizes
            let bestMatchPosition = -1;
            let bestMatchScore = 0;
            let bestMatchLength = 0;
            
            // Try with exact length
            this.findBestMatch(contentTokens, searchTokens, searchTokens.length, (position, score, length) => {
                if (score > bestMatchScore) {
                    bestMatchScore = score;
                    bestMatchPosition = position;
                    bestMatchLength = length;
                }
            });
            
            // Try with slightly larger window to account for added tokens
            this.findBestMatch(contentTokens, searchTokens, Math.floor(searchTokens.length * 1.2), (position, score, length) => {
                if (score > bestMatchScore) {
                    bestMatchScore = score;
                    bestMatchPosition = position;
                    bestMatchLength = length;
                }
            });
            
            // Try with slightly smaller window to account for removed tokens
            this.findBestMatch(contentTokens, searchTokens, Math.floor(searchTokens.length * 0.8), (position, score, length) => {
                if (score > bestMatchScore) {
                    bestMatchScore = score;
                    bestMatchPosition = position;
                    bestMatchLength = length;
                }
            });
            
            // If we found a good match
            if (bestMatchScore > 0.8 && bestMatchPosition !== -1) {
                // console.log(`[FileContentManager] Token match score: ${bestMatchScore.toFixed(4)}`);
                
                // Find the actual text positions in the original content
                const startPos = this.findPositionOfToken(content, contentTokens[bestMatchPosition]);
                const endPos = this.findEndPositionOfToken(content, contentTokens[bestMatchPosition + bestMatchLength - 1]);
                
                if (startPos !== -1 && endPos !== -1) {
                    // Replace the content
                    const updatedContent = 
                        content.substring(0, startPos) + 
                        replace + 
                        content.substring(endPos);
                    
                    return { success: true, content: updatedContent };
                }
            }
            
            return { success: false, content };
        } catch (error) {
            console.error('[FileContentManager] Token matching error:', error);
            return { success: false, content };
        }
    }
    
    /**
     * Find the best matching position in content tokens for search tokens
     */
    private findBestMatch(
        contentTokens: string[], 
        searchTokens: string[], 
        windowSize: number,
        callback: (position: number, score: number, length: number) => void
    ): void {
        for (let i = 0; i <= contentTokens.length - windowSize; i++) {
            const windowTokens = contentTokens.slice(i, i + windowSize);
            const score = this.calculateTokenSimilarity(windowTokens, searchTokens);
            callback(i, score, windowSize);
        }
    }
    
    /**
     * Apply diff using n-gram matching
     */
    private applyWithNGramMatching(
        content: string, 
        search: string, 
        replace: string
    ): { success: boolean; content: string } {
        try {
            // Generate n-grams for content and search
            const contentNGrams = this.generateNGrams(content, 3);
            const searchNGrams = this.generateNGrams(search, 3);
            
            // Find the position with the highest n-gram overlap
            let bestStartPos = -1;
            let bestEndPos = -1;
            let bestScore = 0;
            
            // Slide through content in chunks
            const chunkSize = 100;
            const overlap = 50;
            
            for (let i = 0; i < content.length; i += (chunkSize - overlap)) {
                const end = Math.min(i + chunkSize, content.length);
                const chunk = content.substring(i, end);
                const chunkNGrams = this.generateNGrams(chunk, 3);
                
                const overlapScore = this.calculateNGramOverlap(chunkNGrams, searchNGrams);
                
                if (overlapScore > bestScore) {
                    bestScore = overlapScore;
                    bestStartPos = i;
                    bestEndPos = end;
                }
                
                // If we've reached the end of content, break
                if (end === content.length) break;
            }
            
            // If we found a good match
            if (bestScore > 0.7 && bestStartPos !== -1) {
                // console.log(`[FileContentManager] N-gram match score: ${bestScore.toFixed(4)}`);
                
                // Replace the content
                const updatedContent = 
                    content.substring(0, bestStartPos) + 
                    replace + 
                    content.substring(bestEndPos);
                
                return { success: true, content: updatedContent };
            }
            
            return { success: false, content };
        } catch (error) {
            console.error('[FileContentManager] N-gram matching error:', error);
            return { success: false, content };
        }
    }
    
    /**
     * Generate n-grams from text
     */
    private generateNGrams(text: string, n: number): Set<string> {
        const ngrams = new Set<string>();
        for (let i = 0; i <= text.length - n; i++) {
            ngrams.add(text.substring(i, i + n));
        }
        return ngrams;
    }
    
    /**
     * Calculate overlap between two sets of n-grams
     */
    private calculateNGramOverlap(setA: Set<string>, setB: Set<string>): number {
        let intersection = 0;
        for (const ngram of setA) {
            if (setB.has(ngram)) {
                intersection++;
            }
        }
        return intersection / Math.max(setA.size, setB.size);
    }
    
    /**
     * Extract significant tokens from a line (removing noise like whitespace)
     */
    private getSignificantTokens(line: string): string {
        return line
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[;,.(){}[\]<>]/g, '')
            .trim();
    }
    
    /**
     * Calculate similarity between two strings
     */
    private calculateStringSimilarity(a: string, b: string): number {
        const longer = a.length > b.length ? a : b;
        const shorter = a.length > b.length ? b : a;
        
        if (longer.length === 0) {
            return 1.0;
        }
        
        // Calculate Levenshtein distance
        const costs = new Array(shorter.length + 1);
        for (let i = 0; i <= shorter.length; i++) {
            costs[i] = i;
        }
        
        for (let i = 1; i <= longer.length; i++) {
            costs[0] = i;
            let nw = i - 1;
            for (let j = 1; j <= shorter.length; j++) {
                const cj = Math.min(
                    costs[j] + 1,
                    costs[j - 1] + 1,
                    nw + (longer.charAt(i - 1) === shorter.charAt(j - 1) ? 0 : 1)
                );
                nw = costs[j];
                costs[j] = cj;
            }
        }
        
        return (longer.length - costs[shorter.length]) / longer.length;
    }
    
    /**
     * Find the original index in the content based on the normalized index
     */
    private findOriginalIndex(
        originalContent: string, 
        normalizedContent: string, 
        normalizedIndex: number
    ): number {
        let originalIndex = 0;
        let normalizedPos = 0;
        
        while (normalizedPos < normalizedIndex && originalIndex < originalContent.length) {
            // Skip whitespace in original content
            while (originalIndex < originalContent.length && /\s/.test(originalContent[originalIndex])) {
                originalIndex++;
            }
            
            // Move forward in both strings
            if (originalIndex < originalContent.length) {
                originalIndex++;
                normalizedPos++;
            }
        }
        
        return originalIndex;
    }
    
    /**
     * Find the end index in the original content based on the start index and search pattern
     */
    private findOriginalEndIndex(
        originalContent: string, 
        startIndex: number, 
        searchPattern: string
    ): number {
        // Count non-whitespace characters in search pattern
        const nonWhitespaceCount = searchPattern.replace(/\s+/g, '').length;
        
        let count = 0;
        let endIndex = startIndex;
        
        while (count < nonWhitespaceCount && endIndex < originalContent.length) {
            if (!/\s/.test(originalContent[endIndex])) {
                count++;
            }
            endIndex++;
        }
        
        return endIndex;
    }
    
    /**
     * Count the number of occurrences of a substring in a string
     * Uses a non-regex approach to avoid escape character issues
     */
    private countMatches(text: string, search: string): number {
        let count = 0;
        let index = 0;
        
        while ((index = text.indexOf(search, index)) !== -1) {
            count++;
            index += search.length;
        }
        
        return count;
    }
    
    /**
     * Tokenize code into meaningful parts
     */
    private tokenizeCode(code: string): string[] {
        // Remove comments
        const noComments = code.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '');
        
        // Split by common delimiters while preserving them
        const tokens = noComments.split(/([{}()[\];,.<>:=+\-*/%&|^!~]|\s+)/g)
            .filter(token => token.trim() !== '');
        
        return tokens;
    }
    
    /**
     * Calculate similarity between two token arrays
     */
    private calculateTokenSimilarity(tokensA: string[], tokensB: string[]): number {
        const normalizedA = tokensA.map(t => t.trim()).filter(t => t !== '');
        const normalizedB = tokensB.map(t => t.trim()).filter(t => t !== '');
        
        let matches = 0;
        const minLength = Math.min(normalizedA.length, normalizedB.length);
        
        for (let i = 0; i < minLength; i++) {
            if (normalizedA[i] === normalizedB[i]) {
                matches++;
            }
        }
        
        return matches / Math.max(normalizedA.length, normalizedB.length);
    }
    
    /**
     * Find the position of a token in the original content
     */
    private findPositionOfToken(content: string, token: string): number {
        // Simple implementation - can be improved for better accuracy
        return content.indexOf(token);
    }
    
    /**
     * Find the end position of a token in the original content
     */
    private findEndPositionOfToken(content: string, token: string): number {
        const pos = content.indexOf(token);
        if (pos === -1) return -1;
        return pos + token.length;
    }
    
    /**
     * Clear all file contents and caches
     */
    public clear(): void {
        this.fileContents = {};
    }
}
