import { MatchingStrategy, MatchResult } from './matching/MatchingStrategy';
import { ExactMatchingStrategy } from './matching/ExactMatchingStrategy';
import { NormalizedMatchingStrategy } from './matching/NormalizedMatchingStrategy';
import { LineContextMatchingStrategy } from './matching/LineContextMatchingStrategy';
import { TokenMatchingStrategy } from './matching/TokenMatchingStrategy';
import { NGramMatchingStrategy } from './matching/NGramMatchingStrategy';
import { AICompletionStrategy } from './matching/AICompletionStrategy';

/**
 * Service for applying code diffs using various matching strategies
 */
export class DiffApplicationService {
    private strategies: MatchingStrategy[] = [];

    constructor() {
        // Register strategies in order of preference (fastest/most reliable first)
        this.strategies = [
            new ExactMatchingStrategy(),
            // new NormalizedMatchingStrategy(),
            // new LineContextMatchingStrategy(),
            // new TokenMatchingStrategy(),
            // new NGramMatchingStrategy(),
            // new AICompletionStrategy() // AI-based correction as last resort
        ];
    }

    /**
     * Apply a diff to content using all available strategies
     * @param content The content to modify
     * @param search The search pattern
     * @param replace The replacement text
     * @param options Optional configuration
     */
    public async applyDiff(
        content: string,
        search: string,
        replace: string,
        options?: {
            forceAICompletion?: boolean;
            allSearches?: string[];
            allReplacements?: string[];
            filePath?: string;
        }
    ): Promise<MatchResult> {
        // Log search and replace patterns with some context
        this.logPatterns(search, replace);

        if (options?.forceAICompletion) {
            // If AI completion is forced, only use the AI strategy
            const aiStrategy = this.strategies.find(s => s.name === 'AI Completion');
            if (aiStrategy) {
                // console.log('[DiffApplicationService] Forcing AI completion strategy');
                const result = await aiStrategy.apply(content, search, replace, {
                    allSearches: options.allSearches,
                    allReplacements: options.allReplacements,
                    filePath: options.filePath
                });
                if (result.success) {
                    this.logChanges(content, result.content);
                }
                return result;
            }
        }

        // Try each strategy in order
        for (const strategy of this.strategies) {
            console.log(`[DiffApplicationService] Trying strategy: ${strategy.name}`);

            const result = await strategy.apply(content, search, replace, {
                filePath: options?.filePath
            });

            if (result.success) {
                console.log(`[DiffApplicationService] Success with strategy: ${strategy.name}, score: ${result.score || 'N/A'}`);

                // Log the changes that were made
                this.logChanges(content, result.content);

                return result;
            } else {
                console.log(`[DiffApplicationService] Failed with strategy: ${strategy.name}, reason: ${result.message || 'Unknown'}`);
            }
        }

        // If all strategies fail, return failure
        return {
            success: false,
            content,
            message: 'All matching strategies failed'
        };
    }

    /**
     * Add a custom strategy
     */
    public addStrategy(strategy: MatchingStrategy): void {
        this.strategies.push(strategy);
    }

    /**
     * Clear all strategies
     */
    public clearStrategies(): void {
        this.strategies = [];
    }

    /**
     * Log search and replace patterns with details
     */
    private logPatterns(search: string, replace: string): void {
        // // console.log('[DiffApplicationService] Search pattern:');
        // // console.log('----------------------------------------');
        // // console.log(search);
        // // console.log('----------------------------------------');
        //
        // // console.log('[DiffApplicationService] Replace pattern:');
        // // console.log('----------------------------------------');
        // // console.log(replace);
        // // console.log('----------------------------------------');

        // Log tag counts to check for structural issues
        const searchOpenTags = (search.match(/<[^\/][^>]*>/g) || []).length;
        const searchCloseTags = (search.match(/<\/[^>]*>/g) || []).length;
        const replaceOpenTags = (replace.match(/<[^\/][^>]*>/g) || []).length;
        const replaceCloseTags = (replace.match(/<\/[^>]*>/g) || []).length;

        // console.log(`[DiffApplicationService] Search tags: ${searchOpenTags} open, ${searchCloseTags} close`);
        // console.log(`[DiffApplicationService] Replace tags: ${replaceOpenTags} open, ${replaceCloseTags} close`);

        if (searchOpenTags !== searchCloseTags || replaceOpenTags !== replaceCloseTags) {
            console.warn('[DiffApplicationService] WARNING: Unbalanced tags detected!');
        }

        if (searchOpenTags - searchCloseTags !== replaceOpenTags - replaceCloseTags) {
            console.warn('[DiffApplicationService] WARNING: Tag balance differs between search and replace!');
        }
    }

    /**
     * Log the changes that were made
     */
    private logChanges(originalContent: string, newContent: string): void {
        if (originalContent === newContent) {
            // console.log('[DiffApplicationService] No changes were made to the content');
            return;
        }

        // Find where content differs
        let firstDiffIndex = 0;
        while (firstDiffIndex < originalContent.length &&
               firstDiffIndex < newContent.length &&
               originalContent[firstDiffIndex] === newContent[firstDiffIndex]) {
            firstDiffIndex++;
        }

        let lastOriginalIndex = originalContent.length - 1;
        let lastNewIndex = newContent.length - 1;
        while (lastOriginalIndex >= 0 &&
               lastNewIndex >= 0 &&
               originalContent[lastOriginalIndex] === newContent[lastNewIndex]) {
            lastOriginalIndex--;
            lastNewIndex--;
        }

        // Show context around the change
        const contextBefore = originalContent.substring(
            Math.max(0, firstDiffIndex - 100),
            firstDiffIndex
        );
        const contextAfter = originalContent.substring(
            lastOriginalIndex + 1,
            Math.min(originalContent.length, lastOriginalIndex + 100)
        );

        const removed = originalContent.substring(firstDiffIndex, lastOriginalIndex + 1);
        const added = newContent.substring(firstDiffIndex, lastNewIndex + 1);

        // // console.log('[DiffApplicationService] Change details:');
        // // console.log('--- Context before ---');
        // // console.log(contextBefore);
        // // console.log('--- Removed ---');
        // // console.log(removed);
        // // console.log('--- Added ---');
        // // console.log(added);
        // // console.log('--- Context after ---');
        // // console.log(contextAfter);
    }
}
