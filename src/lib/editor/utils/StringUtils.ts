/**
 * Utility class for string operations
 */
export class StringUtils {
    /**
     * Calculate similarity between two strings (0-1)
     */
    public calculateStringSimilarity(a: string, b: string): number {
        if (a === b) return 1.0;
        if (a.length === 0 || b.length === 0) return 0.0;
        
        // Calculate Levenshtein distance
        const distance = this.levenshteinDistance(a, b);
        const maxLength = Math.max(a.length, b.length);
        
        // Convert distance to similarity score (0-1)
        return 1 - (distance / maxLength);
    }
    
    /**
     * Calculate Levenshtein distance between two strings
     */
    private levenshteinDistance(a: string, b: string): number {
        const matrix: number[][] = [];
        
        // Initialize matrix
        for (let i = 0; i <= a.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= b.length; j++) {
            matrix[0][j] = j;
        }
        
        // Fill matrix
        for (let i = 1; i <= a.length; i++) {
            for (let j = 1; j <= b.length; j++) {
                const cost = a[i - 1] === b[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // deletion
                    matrix[i][j - 1] + 1,      // insertion
                    matrix[i - 1][j - 1] + cost // substitution
                );
            }
        }
        
        return matrix[a.length][b.length];
    }
    
    /**
     * Normalize whitespace in a string
     */
    public normalizeWhitespace(text: string): string {
        return text
            .replace(/\s+/g, ' ')
            .trim();
    }
}
