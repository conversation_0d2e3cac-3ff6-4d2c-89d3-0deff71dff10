/**
 * Utility class for tokenizing code
 */
export class CodeTokenizer {
    /**
     * Tokenize code into meaningful parts
     */
    public tokenizeCode(code: string): string[] {
        // Remove comments
        let cleanCode = code
            .replace(/\/\/.*$/gm, '')
            .replace(/\/\*[\s\S]*?\*\//g, '');
        
        // Split code into tokens
        const tokens: string[] = [];
        let currentToken = '';
        let inString = false;
        let stringDelimiter = '';
        
        for (let i = 0; i < cleanCode.length; i++) {
            const char = cleanCode[i];
            const nextChar = i < cleanCode.length - 1 ? cleanCode[i + 1] : '';
            
            // Handle strings
            if ((char === '"' || char === "'" || char === '`') && (i === 0 || cleanCode[i - 1] !== '\\')) {
                if (!inString) {
                    // Starting a string
                    if (currentToken) {
                        tokens.push(currentToken);
                        currentToken = '';
                    }
                    inString = true;
                    stringDelimiter = char;
                    currentToken += char;
                } else if (char === stringDelimiter) {
                    // Ending a string
                    currentToken += char;
                    tokens.push(currentToken);
                    currentToken = '';
                    inString = false;
                } else {
                    // Character inside a string
                    currentToken += char;
                }
                continue;
            }
            
            if (inString) {
                currentToken += char;
                continue;
            }
            
            // Handle operators and delimiters
            if (/[=+\-*/%&|^!<>:;,.(){}[\]]/.test(char)) {
                if (currentToken) {
                    tokens.push(currentToken);
                    currentToken = '';
                }
                
                // Handle multi-character operators
                if ((char === '=' && nextChar === '=') || 
                    (char === '!' && nextChar === '=') ||
                    (char === '<' && nextChar === '=') ||
                    (char === '>' && nextChar === '=') ||
                    (char === '&' && nextChar === '&') ||
                    (char === '|' && nextChar === '|') ||
                    (char === '+' && nextChar === '+') ||
                    (char === '-' && nextChar === '-') ||
                    (char === '+' && nextChar === '=') ||
                    (char === '-' && nextChar === '=') ||
                    (char === '*' && nextChar === '=') ||
                    (char === '/' && nextChar === '=') ||
                    (char === '%' && nextChar === '=')) {
                    tokens.push(char + nextChar);
                    i++; // Skip next character
                } else {
                    tokens.push(char);
                }
                continue;
            }
            
            // Handle whitespace
            if (/\s/.test(char)) {
                if (currentToken) {
                    tokens.push(currentToken);
                    currentToken = '';
                }
                continue;
            }
            
            // Add to current token
            currentToken += char;
        }
        
        // Add the last token if there is one
        if (currentToken) {
            tokens.push(currentToken);
        }
        
        return tokens;
    }
}
