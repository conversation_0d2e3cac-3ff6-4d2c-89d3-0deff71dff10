import { FileItem } from "@/types/file";

interface LineEdit {
    lineRange: string;  // Format: "L10-L15"
    editedContent: string;
    append?: boolean;  // If true, content will be inserted before the target line instead of replacing it
}

interface FileEdit {
    absolutePath: string;
    edits: LineEdit[];
}

interface LineShift {
    afterLine: number;  // Line number after which shift occurs
    shiftAmount: number;  // Number of lines added (positive) or removed (negative)
}

export class FileLineManager {
    private filesMap: Record<string, string> = {};
    private originalFiles: Record<string, string> = {};
    private lineShifts: Record<string, LineShift[]> = {};
    private dirtyFiles: string[] = [];

    /**
     * Initialize with file tree and convert to line-numbered format
     */
    public initializeFiles(files: FileItem[]) {
        files.forEach(file => {
            // Store original content
            this.originalFiles[file.name] = file.content;
            // Store line-numbered content
            this.filesMap[file.name] = this.addLineNumbers(file.content);
            // Initialize empty line shifts for this file
            this.lineShifts[file.name] = [];
        });
    }

    /**
     * Add line numbers to content
     */
    private addLineNumbers(content: string): string {
        return content.split('\n')
            .map((line, idx) => `${String(idx + 1).padStart(3, ' ')}| ${line}`)
            .join('\n');
    }

    /**
     * Get line-numbered content for a file
     */
    public getNumberedContent(filePath: string): string {
        return this.filesMap[filePath] || '';
    }

    /**
     * Remove line numbers from content
     */
    private removeLineNumbers(content: string): string {
        if (!content) return '';
        return content.split('\n')
            .map(line => line.replace(/^\s*\d+\|\s/, ''))
            .join('\n');
    }

    /**
     * Calculate actual line number after all shifts
     */
    private getAdjustedLineNumber(filePath: string, originalLine: number): number {
        let adjustedLine = originalLine;
        const shifts = this.lineShifts[filePath] || [];
        
        // Apply shifts in order
        let cumulativeShift = 0;
        for (const shift of shifts) {
            if (originalLine > shift.afterLine) {
                cumulativeShift += shift.shiftAmount;
            }
        }
        return adjustedLine + cumulativeShift;
    }

    /**
     * @description Add files to the tree
     * @param filePath
     * @param content
     * @private
     */
    addFiles(filePath: string, content: string) {
        this.filesMap[filePath] = content;
    }

    replaceFile(filePath: string, content: string, markDirty?: boolean) {
        this.filesMap[filePath] = content;
        this.dirtyFiles.push(filePath);
    }
    
    /**
     * Rollback a file to its original state or to a specified content
     * @param filePath Path to the file to rollback
     * @param content Optional content to rollback to. If not provided, rolls back to original content
     */
    rollbackFile(filePath: string, content?: string) {
        if (content) {
            // Rollback to specified content
            this.filesMap[filePath] = this.addLineNumbers(content);
        } else if (this.originalFiles[filePath]) {
            // Rollback to original content
            this.filesMap[filePath] = this.addLineNumbers(this.originalFiles[filePath]);
        } else {
            // If no original content, remove the file
            delete this.filesMap[filePath];
        }
        
        // Reset line shifts for this file
        this.lineShifts[filePath] = [];
        
        // Remove from dirty files if it was marked as dirty
        const dirtyIndex = this.dirtyFiles.indexOf(filePath);
        if (dirtyIndex !== -1) {
            this.dirtyFiles.splice(dirtyIndex, 1);
        }
    }

    getDirtyFiles(): {path: string, contents: string}[] {
        return Object.entries(this.filesMap).map(([key, contents]) => {
            if(this.dirtyFiles.includes(key)) {
                return {
                    path: key,
                    contents: this.getNumberedContent(key)
                };
            }
            return null;
        }).filter(f => !!f);
    }


    private getFileType(extension: string) {
        let fileExt = "text";
        if(['tsx','ts'].includes(extension)) {
            fileExt = "typescript"
        } else if(['jsx','js'].includes(extension)) {
            fileExt = "javascript"
        } else if(['json'].includes(extension)) {
            fileExt = "json"
        } else if(['md'].includes(extension)) {
            fileExt = "markdown"
        } else if(['css','scss','less'].includes(extension)) {
            fileExt = "css"
        } else if(['html'].includes(extension)) {
            fileExt = "html"
        } else if(['xml'].includes(extension)) {
            fileExt = "xml"
        } else if(['yaml','yml'].includes(extension)) {
            fileExt = "yaml"
        } else if(['sh','bash'].includes(extension)) {
            fileExt = "shell"
        } else if(['gradle'].includes(extension)) {
            fileExt = "groovy"
        } else if(['swift'].includes(extension)) {
            fileExt = "swift"
        } else if(['java'].includes(extension)) {
            fileExt = "java"
        } else if(['kt'].includes(extension)) {
            fileExt = "kotlin"
        } else if(['m','h'].includes(extension)) {
            fileExt = "objectivec"
        }
        return fileExt;
    }

    /**
     * Get all files in FileItem[] format
     */
    getFileItems(): FileItem[] {
        return Object.entries(this.filesMap).map(([path, content]) => ({
            name: path,
            type: 'file' as const,
            language: this.getFileType(path.split('.').pop() || 'text'),
            content: this.getFinalContent(path),
            changes: 0
        }));
    }

    /**
     * Apply a single edit to a file
     */
    private applyEdit(filePath: string, edit: LineEdit): void {
        // Parse line range (e.g., "L10-L15")
        const matches = edit.lineRange.match(/L(\d+)-L(\d+)/);
        if (!matches) {
            throw new Error(`Invalid line range format: ${edit.lineRange}`);
        }

        const originalStart = parseInt(matches[1]);
        const originalEnd = parseInt(matches[2]);

        // Get adjusted line numbers based on previous edits
        const adjustedStart = this.getAdjustedLineNumber(filePath, originalStart);
        const adjustedEnd = this.getAdjustedLineNumber(filePath, originalEnd);

        // Get current content and split into lines
        const currentContent = this.removeLineNumbers(this.filesMap[filePath]);
        const lines = currentContent.split('\n');

        // Validate line range
        if (adjustedStart < 1 || adjustedStart > lines.length || adjustedEnd < 1 || adjustedEnd > lines.length) {
            throw new Error(`Invalid line range (1-${lines.length}): ${edit.lineRange}`);
        }

        // Remove line numbers from edited content if they exist
        const cleanedContent = this.removeLineNumbers(edit.editedContent);
        const newLines = cleanedContent.split('\n');

        // Calculate lines being added/removed
        const lineChange = newLines.length - (adjustedEnd - adjustedStart + 1);

        // Apply the edit - either append before or replace
        if (edit.append) {
            lines.splice(adjustedStart - 1, 0, ...newLines); // Insert before target line
        } else {
            lines.splice(adjustedStart - 1, adjustedEnd - adjustedStart + 1, ...newLines); // Replace target lines
        }

        // Handle empty lines
        let lastNonEmptyIndex = lines.length - 1;
        while (lastNonEmptyIndex >= 0 && lines[lastNonEmptyIndex] === '') {
            lastNonEmptyIndex--;
        }

        // Keep only one empty line at the end if needed
        if (lastNonEmptyIndex < lines.length - 1) {
            lines.splice(lastNonEmptyIndex + 2);
        }

        // Update line shifts
        if (lineChange !== 0) {
            // Update existing shifts
            const updatedShifts = this.lineShifts[filePath].filter(shift => shift.afterLine < originalStart);
            
            // Add the new shift
            updatedShifts.push({
                afterLine: originalEnd,
                shiftAmount: lineChange
            });
            
            // Sort shifts by line number
            updatedShifts.sort((a, b) => a.afterLine - b.afterLine);
            
            this.lineShifts[filePath] = updatedShifts;
        }

        // Update file content with new line numbers
        this.filesMap[filePath] = this.addLineNumbers(lines.join('\n'));
    }

    /**
     * Apply multiple edits to a file
     */
    public applyFileEdits(fileEdit: FileEdit): void {
        const { absolutePath, edits } = fileEdit;
        
        if (!this.filesMap[absolutePath]) {
            throw new Error(`File ${absolutePath} not found`);
        }

        // Sort edits - appends in ascending order, replacements in descending order
        const sortedEdits = [...edits].sort((a, b) => {
            const aMatch = a.lineRange.match(/L(\d+)/);
            const bMatch = b.lineRange.match(/L(\d+)/);
            if (!aMatch || !bMatch) {
                throw new Error(`Invalid line range format in one of the edits`);
            }
            // If both are appends or both are not appends, sort by line number
            if (a.append === b.append) {
                // For appends, use ascending order
                if (a.append) {
                    return parseInt(aMatch[1]) - parseInt(bMatch[1]);
                }
                // For replacements, use descending order
                return parseInt(bMatch[1]) - parseInt(aMatch[1]);
            }
            // If only one is append, put it first
            return a.append ? -1 : 1;
        });

        // Apply each edit
        sortedEdits.forEach(edit => {
            this.applyEdit(absolutePath, edit);
        });
    }

    /**
     * Get final content of a file without line numbers
     * @returns The entire file content after all edits have been applied
     */
    public getFinalContent(filePath: string): string {
        if (!this.filesMap[filePath]) {
            throw new Error(`File ${filePath} not found`);
        }
        return this.removeLineNumbers(this.filesMap[filePath]);
    }

    public hasFile(filePath: string): boolean {
        return !!this.filesMap[filePath]
    }
}
