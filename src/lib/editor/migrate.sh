#!/bin/bash

# Script to migrate from the old FileContentManager to the new refactored version

# Make backup of original file
echo "Creating backup of original FileContentManager.ts..."
cp FileContentManager.ts FileContentManager.original.ts

# Replace original with new version
echo "Replacing with new refactored version..."
mv FileContentManager.new.ts FileContentManager.ts

# Make the script executable
chmod +x migrate.sh

echo "Migration complete!"
echo "Original file has been backed up to FileContentManager.original.ts"
echo "New refactored version is now in place as FileContentManager.ts"
echo ""
echo "To revert the changes, run: mv FileContentManager.original.ts FileContentManager.ts"
