import { DiffMeta } from "../parser/DiffParser";
import { FileContentStorage } from "./content/FileContentStorage";
import { DiffApplicationService } from "./DiffApplicationService";

/**
 * FileContentManager handles applying diffs to files
 * It orchestrates the storage and diff application services
 */
export class FileContentManager {
    private contentStorage: FileContentStorage;
    private diffService: DiffApplicationService;
    // Track diffs by their path to avoid double-counting
    private pendingDiffPaths: Set<string> = new Set();

    constructor() {
        this.contentStorage = new FileContentStorage();
        this.diffService = new DiffApplicationService();
    }

    /**
     * Set the content of a file
     */
    public setFileContent(path: string, content: string): void {
        this.contentStorage.setFileContent(path, content);
    }

    /**
     * Get the current content of a file
     */
    public getFileContent(path: string): string | null {
        return this.contentStorage.getFileContent(path) || null;
    }

    /**
     * Get all file items
     */
    public getFileItems(): Array<{name: string, content: string}> {
        return this.contentStorage.getAllFilePaths().map(path => ({
            name: path,
            content: this.contentStorage.getFileContent(path) || ''
        }));
    }

    /**
     * Check if all diff operations have been completed
     * @returns True if all started diffs have completed, false otherwise
     */
    public areDiffsComplete(): boolean {
        return this.pendingDiffPaths.size === 0;
    }

    /**
     * Wait until all diff operations have completed
     * @param maxWaitMs Maximum time to wait in milliseconds
     * @returns Promise that resolves when all diffs are complete or timeout is reached
     */
    public async waitForDiffsToComplete(maxWaitMs: number = 5000): Promise<boolean> {
        const startTime = Date.now();

        // Helper function to wait a short time
        const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

        // Poll until diffs are complete or timeout
        while (!this.areDiffsComplete()) {
            if (Date.now() - startTime > maxWaitMs) {
                console.warn(`[FileContentManager] Timed out waiting for diffs to complete after ${maxWaitMs}ms. Pending paths: ${Array.from(this.pendingDiffPaths).join(', ')}`);
                return false;
            }
            await wait(100); // Poll every 100ms
        }

        console.log(`[FileContentManager] All diffs completed successfully.`);
        return true;
    }

    /**
     * Apply a diff to a file
     * @param diff The diff metadata
     * @param options Options for diff application
     * @returns Result object with success flag, any error message, and optional credit usage information
     */
    public async applyDiff(diff: DiffMeta, options: { bestEffort?: boolean; forceAICompletion?: boolean, filePath?: string } = {}): Promise<{ success: boolean; message?: string; content?: string; usedAICorrection?: boolean; creditUsage?: any }> {
        // Track this diff by its path
        const diffPath = options.filePath || diff.path;
        this.pendingDiffPaths.add(diffPath);

        // Create a cleanup function to mark this diff as complete
        const markComplete = () => {
            this.pendingDiffPaths.delete(diffPath);
            console.log(`[FileContentManager] Completed diff for ${diffPath}. Remaining: ${this.pendingDiffPaths.size}`);
        };
        const { path, searches, replacements } = diff;
        const { bestEffort = false, forceAICompletion = false } = options;
        // Use the provided filePath or fall back to the diff path
        const filePath = options.filePath || path;

        // Get current file content
        const currentContent = this.getFileContent(path);

        // Check for empty search string, which indicates a full file replacement
        if (searches.length === 1 && searches[0].trim() === '') {
            if (replacements.length !== 1) {
                return {
                    success: false,
                    message: 'Full replacement requires exactly one replacement content block'
                };
            }

            const newContent = replacements[0];
            this.setFileContent(path, newContent);
            // Mark this diff as complete
            markComplete();
            return {
                success: true,
                content: newContent
            };
        }

        // Otherwise, handle search/replace pairs
        if (!currentContent) {
            return {
                success: false,
                message: `File ${path} not found. Cannot apply partial edits to a non-existent file.`
            };
        }

        if (searches.length !== replacements.length) {
            return {
                success: false,
                message: `Mismatched search/replace pairs (${searches.length} searches, ${replacements.length} replacements)`
            };
        }

        let updatedContent = currentContent;
        const failedSearches: number[] = [];
        const successfulSearches: number[] = [];

        // Apply each search/replace pair
        for (let i = 0; i < searches.length; i++) {
            const search = searches[i];
            const replace = replacements[i];

            try {
                // Use the DiffApplicationService to apply the diff
                const result = await this.diffService.applyDiff(updatedContent, search, replace, { forceAICompletion, filePath: filePath });

                if (result.success) {
                    // console.log(`[FileContentManager] Successfully applied search pattern #${i+1}`);
                    updatedContent = result.content;
                    successfulSearches.push(i + 1);
                } else {
                    // console.log(`[FileContentManager] Failed to apply search pattern #${i+1}: ${result.message}`);
                    failedSearches.push(i + 1);
                }
            } catch (error) {
                console.error(`[FileContentManager] Error applying search pattern #${i+1}:`, error);
                failedSearches.push(i + 1);
            }
        }

        // If any searches failed and we're not in best effort mode, try AI correction if not already forced
        if (failedSearches.length > 0) {
            if (!forceAICompletion) {
                console.log('[FileContentManager] Attempting AI correction after standard strategies failed');

                // Collect all failed search/replace pairs
                const failedSearches: string[] = [];
                const failedReplacements: string[] = [];

                for (let i = 0; i < searches.length; i++) {
                    if (!successfulSearches.includes(i + 1)) {
                        failedSearches.push(searches[i]);
                        failedReplacements.push(replacements[i]);
                    }
                }

                // Try AI correction with all failed patterns at once
                try {
                    // We'll use the first failed search/replace pair as the main one
                    // but provide all pairs to the AI strategy
                    const result = await this.diffService.applyDiff(
                        currentContent,
                        failedSearches[0],
                        failedReplacements[0],
                        {
                            forceAICompletion: true,
                            allSearches: failedSearches,
                            allReplacements: failedReplacements,
                            filePath: filePath // Pass the full path for proper context
                        }
                    );

                    if (result.success) {
                        this.setFileContent(filePath, result.content);
                        // Mark this diff as complete
                        markComplete();
                        return {
                            success: true,
                            content: result.content,
                            message: 'Applied changes using AI correction',
                            usedAICorrection: true,
                            creditUsage: (result as any).creditUsage // Pass along any credit usage information
                        };
                    }
                } catch (error) {
                    console.error(`[FileContentManager] Error in AI correction:`, error);
                }
            }

            // Mark this diff as complete even for failures
            markComplete();
            return {
                success: false,
                message: 'Failed to apply diff: ' + failedSearches.map((s, i) => `Search #${i+1}`).join(', ')
            };
        }

        // Update the file content even if some searches failed in best effort mode
        this.setFileContent(path, updatedContent);

        // Mark this diff as complete
        markComplete();

        // Return success with a warning if some patterns failed in best effort mode
        if (failedSearches.length > 0 && bestEffort) {
            return {
                success: true,
                content: updatedContent,
                message: `Applied ${successfulSearches.length} patterns successfully. Failed patterns: ${failedSearches.join(', ')}`
            };
        }

        return {
            success: true,
            content: updatedContent,
            usedAICorrection: forceAICompletion
        };
    }

    /**
     * Count the number of files in storage
     */
    public countFiles(): number {
        return this.contentStorage.countFiles();
    }

    /**
     * Get all file paths
     */
    public getAllFilePaths(): string[] {
        return this.contentStorage.getAllFilePaths();
    }

    /**
     * Clear all file contents
     */
    public clear(): void {
        this.contentStorage.clear();
    }
}
