import { DiffApplicationService } from './DiffApplicationService';

/**
 * Simple test script for the DiffApplicationService
 * Run with: npx ts-node src/lib/editor/test-diff-application.ts
 */
async function testDiffApplication() {
    const diffService = new DiffApplicationService();
    
    // Test case 1: Exact matching
    const content1 = `function hello() {
    // console.log("Hello, world!");
}`;
    const search1 = `// console.log("Hello, world!");`;
    const replace1 = `// console.log("Hello, universe!");`;
    
    const result1 = await diffService.applyDiff(content1, search1, replace1);
    // console.log('Test case 1 (Exact matching):');
    // console.log('Success:', result1.success);
    // console.log('Result:', result1.content);
    // console.log('---');
    
    // Test case 2: Normalized matching (whitespace differences)
    const content2 = `function calculate(a, b) {
    const result = a + b;
    return result;
}`;
    const search2 = `const result=a+b;`;
    const replace2 = `const result = a + b + c;`;
    
    const result2 = await diffService.applyDiff(content2, search2, replace2);
    // console.log('Test case 2 (Normalized matching):');
    // console.log('Success:', result2.success);
    // console.log('Result:', result2.content);
    // console.log('---');
    
    // Test case 3: Line context matching
    const content3 = `class Example {
    constructor() {
        this.value = 10;
    }
    
    getValue() {
        return this.value;
    }
}`;
    const search3 = `getValue() {
        return this.value;
    }`;
    const replace3 = `getValue() {
        // console.log("Getting value");
        return this.value;
    }`;
    
    const result3 = await diffService.applyDiff(content3, search3, replace3);
    // console.log('Test case 3 (Line context matching):');
    // console.log('Success:', result3.success);
    // console.log('Result:', result3.content);
    // console.log('---');
    
    // Test case 4: Token matching
    const content4 = `function processData(data) {
    if (!data) return null;
    const processed = transform(data);
    return processed;
}`;
    const search4 = `const processed = transform(data);`;
    const replace4 = `const processed = transform(data);
    validateResult(processed);`;
    
    const result4 = await diffService.applyDiff(content4, search4, replace4);
    // console.log('Test case 4 (Token matching):');
    // console.log('Success:', result4.success);
    // console.log('Result:', result4.content);
    // console.log('---');
    
    // Test case 5: N-gram matching (heavily modified code)
    const content5 = `async function fetchUserData(userId) {
    try {
        const response = await api.get('/users/' + userId);
        return response.data;
    } catch (error) {
        console.error("Failed to fetch user data");
        return null;
    }
}`;
    const search5 = `const response = await api.get('/users/' + userId);`;
    const replace5 = `const response = await api.get(\`/users/\${userId}\`);`;
    
    const result5 = await diffService.applyDiff(content5, search5, replace5);
    // console.log('Test case 5 (N-gram matching):');
    // console.log('Success:', result5.success);
    // console.log('Result:', result5.content);
}

testDiffApplication().catch(console.error);
