import { FileLineManager } from '../FileLineManager';
import { FileItem } from '@/types/file';

describe('FileLineManager', () => {
    let manager: FileLineManager;

    const sampleFiles: FileItem[] = [
        {
            name: '/test/sample.ts',
            type: 'file',
            language: 'typescript',
            content: 'import React from "react";\nconst x = 5;\nconst y = 10;\n// console.log(x + y);'
        },
        {
            name: '/test/complex.ts',
            type: 'file',
            language: 'typescript',
            content: 'function add(a: number, b: number) {\n  return a + b;\n}\n\nfunction subtract(a: number, b: number) {\n  return a - b;\n}'
        }
    ];

    beforeEach(() => {
        manager = new FileLineManager();
        manager.initializeFiles(sampleFiles);
    });

    describe('File Initialization', () => {
        it('should correctly initialize files with line numbers', () => {
            const numberedContent = manager.getNumberedContent('/test/sample.ts');
            expect(numberedContent).toContain('  1| import React from "react";');
            expect(numberedContent).toContain('  2| const x = 5;');
        });

        it('should handle empty files', () => {
            const emptyFile: FileItem = {
                name: '/test/empty.ts',
                type: 'file',
                language: 'typescript',
                content: ''
            };
            manager.initializeFiles([emptyFile]);
            const content = manager.getNumberedContent('/test/empty.ts');
            expect(content).toBe('  1| ');
        });

        it('should ignore non-file items', () => {
            const mixedFiles: FileItem[] = [
                ...sampleFiles,
                { name: '/test/dir', type: 'directory', children: [] }
            ];
            manager.initializeFiles(mixedFiles);
            expect(manager.getNumberedContent('/test/dir')).toBe('');
        });
    });

    describe('Single Line Edits', () => {
        it('should edit a single line correctly', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L2-L2',
                    editedContent: 'const x = 10;'
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('import React from "react";\nconst x = 10;\nconst y = 10;\n// console.log(x + y);');
        });

        it('should handle edits at start of file', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L1-L1',
                    editedContent: '// First line'
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('// First line\nconst x = 5;\nconst y = 10;\n// console.log(x + y);');
        });

        it('should handle edits at end of file', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L4-L4',
                    editedContent: '// Last line'
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('import React from "react";\nconst x = 5;\nconst y = 10;\n// Last line');
        });
    });

    describe('Multi-line Edits', () => {
        it('should replace multiple lines with single line', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L2-L3',
                    editedContent: 'const sum = 15;'
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('import React from "react";\nconst sum = 15;\n// console.log(x + y);');
        });

        it('should replace single line with multiple lines', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L2-L2',
                    editedContent: 'const a = 5;\nconst b = 10;\nconst c = 15;'
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('import React from "react";\nconst a = 5;\nconst b = 10;\nconst c = 15;\nconst y = 10;\n// console.log(x + y);');
        });
    });

    describe('Multiple Sequential Edits', () => {
        it('should handle multiple edits in order', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [
                    {
                        lineRange: 'L1-L1',
                        editedContent: '// Header comment'
                    },
                    {
                        lineRange: 'L3-L3',
                        editedContent: 'const y = 20;'
                    }
                ]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('// Header comment\nconst x = 5;\nconst y = 20;\n// console.log(x + y);');
        });

        it('should handle overlapping line ranges correctly', () => {
            manager.applyFileEdits({
                absolutePath: '/test/complex.ts',
                edits: [
                    {
                        lineRange: 'L1-L3',
                        editedContent: 'function multiply(a: number, b: number) {\n  return a * b;\n}'
                    },
                    {
                        lineRange: 'L5-L7',
                        editedContent: 'function divide(a: number, b: number) {\n  return a / b;\n}'
                    }
                ]
            });
            const result = manager.getFinalContent('/test/complex.ts');
            expect(result).toBe('function multiply(a: number, b: number) {\n  return a * b;\n}\n\nfunction divide(a: number, b: number) {\n  return a / b;\n}');
        });
    });

    describe('Line Shift Handling', () => {
        it('should handle line shifts after adding lines', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [
                    {
                        lineRange: 'L2-L2',
                        editedContent: 'const x = 5;\nconst z = 15;' // Adding a line
                    }
                ]
            });
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [
                    {
                        lineRange: 'L3-L3', // Using original line numbers
                        editedContent: 'const y = 25;'
                    }
                ]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('import React from "react";\nconst x = 5;\nconst z = 15;\nconst y = 25;\n// console.log(x + y);');
        });

        it('should handle line shifts after removing lines', () => {
            manager.applyFileEdits({
                absolutePath: '/test/complex.ts',
                edits: [
                    {
                        lineRange: 'L1-L3',
                        editedContent: 'function add(x: number, y: number) { return x + y; }' // Removing 2 lines
                    }
                ]
            });
            manager.applyFileEdits({
                absolutePath: '/test/complex.ts',
                edits: [
                    {
                        lineRange: 'L5-L7', // Using original line numbers
                        editedContent: 'function subtract(x: number, y: number) { return x - y; }'
                    }
                ]
            });
            const result = manager.getFinalContent('/test/complex.ts');
            expect(result).toBe('function add(x: number, y: number) { return x + y; }\n\nfunction subtract(x: number, y: number) { return x - y; }');
        });
    });

    describe('Error Handling', () => {
        it('should throw error for non-existent file', () => {
            expect(() => {
                manager.applyFileEdits({
                    absolutePath: '/test/nonexistent.ts',
                    edits: [{
                        lineRange: 'L1-L1',
                        editedContent: 'test'
                    }]
                });
            }).toThrow('File /test/nonexistent.ts not found');
        });

        it('should throw error for invalid line range', () => {
            expect(() => {
                manager.applyFileEdits({
                    absolutePath: '/test/sample.ts',
                    edits: [{
                        lineRange: 'L10-L20', // File only has 4 lines
                        editedContent: 'test'
                    }]
                });
            }).toThrow('Invalid line range');
        });

        it('should throw error for invalid line range format', () => {
            expect(() => {
                manager.applyFileEdits({
                    absolutePath: '/test/sample.ts',
                    edits: [{
                        lineRange: 'Line1-Line2', // Invalid format
                        editedContent: 'test'
                    }]
                });
            }).toThrow();
        });
    });

    describe('Append Operations', () => {
        it('should append content before target line', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L1-L1',
                    editedContent: 'import React from "react";',
                    append: true
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result.split('\n').join('')).toBe('import React from "react";import React from "react";const x = 5;const y = 10;// console.log(x + y);');
        });

        it('should handle append and replace on same line', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import React from "react";',
                        append: true
                    },
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import { View } from "react-native";'
                    }
                ]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result.split('\n').join('')).toBe('import { View } from "react-native";import React from "react";const x = 5;const y = 10;// console.log(x + y);');
        });

        it('should handle multiple appends to same line', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import React from "react";',
                        append: true
                    },
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import { useState } from "react";',
                        append: true
                    }
                ]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result.split('\n').join('')).toBe('import { useState } from "react";import React from "react";import React from "react";const x = 5;const y = 10;// console.log(x + y);');
        });

        it('should append multiple lines before target line', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L1-L1',
                    editedContent: 'import React from "react";\
import { View, Text } from "react-native";\
import { useState, useEffect } from "react";',
                    append: true
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result.split('\n').join('')).toBe('import React from "react";import { View, Text } from "react-native";import { useState, useEffect } from "react";import React from "react";const x = 5;const y = 10;// console.log(x + y);');
        });

        it('should handle multi-line append with subsequent replace', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import React from "react";\
import { View, Text } from "react-native";',
                        append: true
                    },
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import { useState } from "react";'
                    }
                ]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result.split('\n').join('')).toBe('import { useState } from "react";import React from "react";const x = 5;const y = 10;// console.log(x + y);');
        });

        it('should handle multiple multi-line appends to same line', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import React from "react";\
import { View } from "react-native";',
                        append: true
                    },
                    {
                        lineRange: 'L1-L1',
                        editedContent: 'import { useState } from "react";\
import { Text } from "react-native";',
                        append: true
                    }
                ]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result.split('\n').join('')).toBe('import { useState } from "react";import { Text } from "react-native";import React from "react";import { View } from "react-native";import React from "react";const x = 5;const y = 10;// console.log(x + y);');
        });
    });

    describe('Content Cleaning', () => {
        it('should remove line numbers from edited content', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L1-L1',
                    editedContent: '  1| const newLine = true;' // Content with line number
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('const newLine = true;\nconst x = 5;\nconst y = 10;\n// console.log(x + y);');
            expect(result).not.toContain('1|');
        });

        it('should handle content with multiple line number patterns', () => {
            manager.applyFileEdits({
                absolutePath: '/test/sample.ts',
                edits: [{
                    lineRange: 'L1-L2',
                    editedContent: '  1| const a = 1;\n  2| const b = 2;\n  3| const c = 3;'
                }]
            });
            const result = manager.getFinalContent('/test/sample.ts');
            expect(result).toBe('const a = 1;\nconst b = 2;\nconst c = 3;\nconst y = 10;\n// console.log(x + y);');
        });
    });

    describe('Complex Data Structure Edits', () => {
        it('should handle complex movie data structure edit', () => {
            // Setup initial movie data file
            const movieFile: FileItem = {
                name: '/src/screens/HomeScreen.tsx',
                type: 'file',
                language: 'typescript',
                content: `import {View, ScrollView, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import {MovieCard} from './MovieCard';
import {CategoryRow} from './CategoryRow';
import {StatusBar} from 'expo-status-bar';

const MOCK_DATA = {
    featured: {
        id: 'featured1',
        title: 'Stranger Things',
        type: 'TV Series',
    },
    categories: [
        {
            id: 'cat1',
            title: 'Trending Now',
            movies: [
                {id: '1', title: 'The Crown', type: 'TV Series'},
                {id: '2', title: 'Dark', type: 'TV Series'},
                {id: '3', title: 'Ozark', type: 'TV Series'},
                {id: '4', title: 'Narcos', type: 'TV Series'},
            ],
        },
        {
            id: 'cat2',
            title: 'Popular Movies',
            movies: [
                {id: '5', title: 'The Irishman', type: 'Movie'},
                {id: '6', title: 'Extraction', type: 'Movie'},
                {id: '7', title: 'Bird Box', type: 'Movie'},
                {id: '8', title: 'The Gray Man', type: 'Movie'},
            ],
        },
        {
            id: 'cat3',
            title: 'Watch Again',
            movies: [
                {id: '9', title: 'Breaking Bad', type: 'TV Series'},
                {id: '10', title: 'Money Heist', type: 'TV Series'},
                {id: '11', title: 'The Witcher', type: 'TV Series'},
                {id: '12', title: 'Black Mirror', type: 'TV Series'},
            ],
        },
    ],
};

export function HomeScreen() {
    const navigation = useNavigation();

    const handleMoviePress = (movie: any) => {
        navigation.navigate('Movie', {
            title: movie.title,
            type: movie.type
        });
    };

    return (
        <SafeAreaView style={styles.container}>
            <StatusBar style="light"/>
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.featured}>
                    <MovieCard
                        title={MOCK_DATA.featured.title}
                        type={MOCK_DATA.featured.type}
                        onPress={() => handleMoviePress(MOCK_DATA.featured)}
                        featured
                    />
                </View>

                {MOCK_DATA.categories.map((category) => (
                    <CategoryRow
                        key={category.id}
                        title={category.title}
                        movies={category.movies}
                        onMoviePress={handleMoviePress}
                    />
                ))}
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#000',
    },
    featured: {
        paddingHorizontal: 16,
        marginTop: 16,
    },
});`
            };

            // Initialize manager with movie file
            const movieManager = new FileLineManager();
            movieManager.initializeFiles([movieFile]);

            // Apply the edit with image placeholders
            movieManager.applyFileEdits({
                absolutePath: '/src/screens/HomeScreen.tsx',
                edits: [{
                    lineRange: 'L8-L47',
                    editedContent: `const MOCK_DATA = {
  featured: {
    id: 'featured1',
    title: 'Stranger Things',
    type: 'TV Series',
    image: '{{IMAGE:dark mysterious scene with supernatural elements}}'
  },
  categories: [
    {
      id: 'cat1',
      title: 'Trending Now',
      movies: [
        { id: '1', title: 'The Crown', type: 'TV Series', image: '{{IMAGE:elegant period drama scene with royal setting}}' },
        { id: '2', title: 'Dark', type: 'TV Series', image: '{{IMAGE:mysterious time travel scene in dark forest}}' },
        { id: '3', title: 'Ozark', type: 'TV Series', image: '{{IMAGE:tense crime drama scene at night}}' },
        { id: '4', title: 'Narcos', type: 'TV Series', image: '{{IMAGE:gritty crime scene in urban setting}}' },
      ],
    },
    {
      id: 'cat2',
      title: 'Popular Movies',
      movies: [
        { id: '5', title: 'The Irishman', type: 'Movie', image: '{{IMAGE:vintage gangster scene in dark lighting}}' },
        { id: '6', title: 'Extraction', type: 'Movie', image: '{{IMAGE:intense action scene with explosions}}' },
        { id: '7', title: 'Bird Box', type: 'Movie', image: '{{IMAGE:post-apocalyptic scene with blindfolded people}}' },
        { id: '8', title: 'The Gray Man', type: 'Movie', image: '{{IMAGE:high stakes action scene in urban setting}}' },
      ],
    },
    {
      id: 'cat3',
      title: 'Watch Again',
      movies: [
        { id: '9', title: 'Breaking Bad', type: 'TV Series', image: '{{IMAGE:desert scene with dramatic lighting}}' },
        { id: '10', title: 'Money Heist', type: 'TV Series', image: '{{IMAGE:bank heist scene with masked figures}}' },
        { id: '11', title: 'The Witcher', type: 'TV Series', image: '{{IMAGE:fantasy medieval scene with monsters}}' },
        { id: '12', title: 'Black Mirror', type: 'TV Series', image: '{{IMAGE:futuristic dystopian technology scene}}' },
      ],
    },
  ],
};`
                }]
            });

            // Get the final content
            const finalContent = movieManager.getFinalContent('/src/screens/HomeScreen.tsx');

            // // Test for duplicate closing braces
            // const closingBraceCount = (finalContent.match(/};/g) || []).length;
            // expect(closingBraceCount).toBe(1);
            //
            // // Parse the content to ensure it's valid JavaScript
            // expect(() => {
            //     // This will throw if there's a syntax error
            //     Function(`return ${finalContent}`)();
            // }).not.toThrow();

            // Now check the exact content
            expect(finalContent).toEqual(`import {View, ScrollView, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import {MovieCard} from './MovieCard';
import {CategoryRow} from './CategoryRow';
import {StatusBar} from 'expo-status-bar';

const MOCK_DATA = {
  featured: {
    id: 'featured1',
    title: 'Stranger Things',
    type: 'TV Series',
    image: '{{IMAGE:dark mysterious scene with supernatural elements}}'
  },
  categories: [
    {
      id: 'cat1',
      title: 'Trending Now',
      movies: [
        { id: '1', title: 'The Crown', type: 'TV Series', image: '{{IMAGE:elegant period drama scene with royal setting}}' },
        { id: '2', title: 'Dark', type: 'TV Series', image: '{{IMAGE:mysterious time travel scene in dark forest}}' },
        { id: '3', title: 'Ozark', type: 'TV Series', image: '{{IMAGE:tense crime drama scene at night}}' },
        { id: '4', title: 'Narcos', type: 'TV Series', image: '{{IMAGE:gritty crime scene in urban setting}}' },
      ],
    },
    {
      id: 'cat2',
      title: 'Popular Movies',
      movies: [
        { id: '5', title: 'The Irishman', type: 'Movie', image: '{{IMAGE:vintage gangster scene in dark lighting}}' },
        { id: '6', title: 'Extraction', type: 'Movie', image: '{{IMAGE:intense action scene with explosions}}' },
        { id: '7', title: 'Bird Box', type: 'Movie', image: '{{IMAGE:post-apocalyptic scene with blindfolded people}}' },
        { id: '8', title: 'The Gray Man', type: 'Movie', image: '{{IMAGE:high stakes action scene in urban setting}}' },
      ],
    },
    {
      id: 'cat3',
      title: 'Watch Again',
      movies: [
        { id: '9', title: 'Breaking Bad', type: 'TV Series', image: '{{IMAGE:desert scene with dramatic lighting}}' },
        { id: '10', title: 'Money Heist', type: 'TV Series', image: '{{IMAGE:bank heist scene with masked figures}}' },
        { id: '11', title: 'The Witcher', type: 'TV Series', image: '{{IMAGE:fantasy medieval scene with monsters}}' },
        { id: '12', title: 'Black Mirror', type: 'TV Series', image: '{{IMAGE:futuristic dystopian technology scene}}' },
      ],
    },
  ],
};

export function HomeScreen() {
    const navigation = useNavigation();

    const handleMoviePress = (movie: any) => {
        navigation.navigate('Movie', {
            title: movie.title,
            type: movie.type
        });
    };

    return (
        <SafeAreaView style={styles.container}>
            <StatusBar style="light"/>
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.featured}>
                    <MovieCard
                        title={MOCK_DATA.featured.title}
                        type={MOCK_DATA.featured.type}
                        onPress={() => handleMoviePress(MOCK_DATA.featured)}
                        featured
                    />
                </View>

                {MOCK_DATA.categories.map((category) => (
                    <CategoryRow
                        key={category.id}
                        title={category.title}
                        movies={category.movies}
                        onMoviePress={handleMoviePress}
                    />
                ))}
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#000',
    },
    featured: {
        paddingHorizontal: 16,
        marginTop: 16,
    },
});`)
            // Verify the changes
            expect(finalContent).toContain('{{IMAGE:dark mysterious scene with supernatural elements}}');
            expect(finalContent).toContain('{{IMAGE:elegant period drama scene with royal setting}}');
            expect(finalContent).toContain('{{IMAGE:mysterious time travel scene in dark forest}}');
            
            // Verify structure is maintained
            expect(finalContent).toContain('featured: {');
            expect(finalContent).toContain('categories: [');
            
            // Verify original content outside edit range is preserved
            expect(finalContent).toContain("import {View, ScrollView, StyleSheet} from 'react-native';");
            expect(finalContent).toContain("import {StatusBar} from 'expo-status-bar';");
        });
    });
});
