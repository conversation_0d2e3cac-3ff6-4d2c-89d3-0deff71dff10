import { MatchingStrategy, MatchResult } from './MatchingStrategy';
import { generateText } from 'ai';
import { customModel } from '@/lib/ai';
import { TargetedValidator } from '@/lib/services/targeted-validator';
import { CreditUsageTracker } from '@/lib/credit/CreditUsageTracker';
import { isErrorFixingMessage } from '@/lib/utils/error-detection';

/**
 * Strategy that uses AI to generate a complete file when syntax errors occur
 * This is used as a last resort when all other strategies fail
 */
export class AICompletionStrategy implements MatchingStrategy {
    public readonly name = 'AI Completion';

    /**
     * Apply AI-based correction to generate a complete file
     * This strategy can handle multiple search/replace pairs at once
     */
    public async apply(
        content: string,
        search: string,
        replace: string,
        options?: { allSearches?: string[], allReplacements?: string[], filePath?: string }
    ): Promise<MatchResult> {
        try {
            // Extract the actual filename from the path if provided
            const originalFilePath = options?.filePath || 'file.ts';
            // Ensure we get just the filename without the path for validation
            const fileName = originalFilePath.split('/').pop() || 'file.ts';

            console.log(`[AICompletionStrategy] Processing file: ${originalFilePath} (from path: ${options?.filePath || 'unknown'})`);

            // Determine if we're handling a single pair or multiple pairs
            const isMultiPair = options?.allSearches && options?.allReplacements &&
                                options.allSearches.length > 0 &&
                                options.allSearches.length === options.allReplacements.length;

            // Build a comprehensive and explicit prompt
            let prompt = `Fix this code file that has syntax errors. I need to modify the file but maintain its structure.\n\n`;
            prompt += `ORIGINAL FILE (${originalFilePath}):\n\n${content}\n\n`;

            if (isMultiPair && options?.allSearches && options?.allReplacements) {
                const searches = options.allSearches;
                const replacements = options.allReplacements;

                prompt += `I need to make ${searches.length} changes to fix the syntax errors:\n\n`;

                for (let i = 0; i < searches.length; i++) {
                    prompt += `CHANGE #${i+1}:\nReplace this code:\n\n${searches[i]}\n\nWith this code:\n\n${replacements[i]}\n\n`;
                }
            } else {
                prompt += `I need to make this change to fix the syntax error:\n\nReplace this code:\n\n${search}\n\nWith this code:\n\n${replace}\n\n`;
            }

            prompt += `IMPORTANT INSTRUCTIONS:\n`;
            prompt += `1. ONLY modify the specific parts mentioned above. Do not change any other code.\n`;
            prompt += `2. Focus ONLY on fixing syntax errors related to the changes.\n`;
            prompt += `3. Return the COMPLETE file with the changes applied.\n`;
            prompt += `4. Do NOT include any markdown formatting, code blocks, or explanations.\n`;
            prompt += `5. Do NOT add any comments or additional code.\n`;
            prompt += `6. Return ONLY the raw code file content, nothing else.\n`;

            // Check if this is an error fixing operation
            const isErrorFixing = true; // We're always fixing errors in AICompletionStrategy

            // Create a credit tracker for this operation
            const creditTracker = new CreditUsageTracker();

            // Use a single, comprehensive attempt with clear instructions
            const result = await generateText({
                model: customModel('openai/gpt-4.1-mini'),
                messages: [
                    {
                        role: 'system',
                        content: `You are a precise code correction assistant.
                        Your only job is to fix syntax errors in code files by applying specific changes.
                        Return ONLY the complete corrected code file with NO markdown formatting, NO code blocks, NO explanations, and NO additional text.
                        The output must be valid code that can be directly executed.

                        FOR content not mentioned in the SEARCH/REPLACE chunks:
                        DO NOT alter anything else apart from the specified search replace chunks.
                        DO NOT even alter the indentation, formatting or even a single character.
                        DO NOT try to be proactive to fix any other issues than what's been asked.
                        THE ONLY job you have it to apply the search/replace without altering anything. Adjust the code to ensure that there are no syntax issue.
                        RESPECTING the codebase is the most important aspect of your job. DO not change anything apart from what has been asked.
                        DO NOT remove comments.
                        DO NOT remove unused code.
                        DO NOT add better fixes to code not asked to be changed
                        DO NOT ado anything that what is being asked

                        FOR content mentioned in the SEARCH/REPLACE chunks:
                        MAKE sure to apply the replacement properly
                        FOR ambiguous changes, use best understanding of where the changes need to be appplied
                        MAKE the changes syntactically correct
                        AUTO correct any issues within the replacement including missing syntax cues
                        APPLY JSX carefully so as to not cause Syntax issues
                        `
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0 // Very low temperature for deterministic output
            });

            // Get the generated code and clean it
            let generatedCode = result.text.trim();

            // Remove any potential markdown code block markers
            generatedCode = generatedCode.replace(/^```[\w]*\n|```$/g, '');

            // Validate the generated code
            console.log(`[AICompletionStrategy] Validating generated code for ${originalFilePath}`);
            const validationResult = TargetedValidator.validateFiles([{
                name: fileName, // Use just the filename for validation
                content: generatedCode
            }]);

            // Get the validation key - the validator might normalize the filename
            const validationKey = Object.keys(validationResult.fileResults)[0] || fileName;

            console.log(`[AICompletionStrategy] Validation result for ${fileName}:`,
                validationResult.isValid ? 'VALID' : 'INVALID');

            if (!validationResult.isValid) {
                // Use the key returned by the validator to access errors
                const errors = validationResult.fileResults[validationKey]?.errors;
                console.error(`[AICompletionStrategy] Validation failed for ${fileName}:`, errors);

                return {
                    success: false,
                    content,
                    message: `AI generated invalid code: ${errors?.[0]?.message || 'Unknown error'}`
                };
            }

            // Return the validated AI-generated content
            console.log(`[AICompletionStrategy] Successfully generated valid code for ${fileName}`);

            // Track the AI completion as a discounted operation if it's for error fixing
            if (isErrorFixing) {
                creditTracker.trackDiscountedOperation('file_change', 'error_fixing', 1);
                console.log(`[AICompletionStrategy] Tracked discounted AI completion for error fixing. Discounted credits: ${creditTracker.getDiscountedCreditCount()}`);
            } else {
                creditTracker.trackOperation('file_change', 1);
            }

            return {
                success: true,
                content: generatedCode,
                message: `Applied changes using AI completion for ${originalFilePath}`,
                score: 1.0,
                creditUsage: creditTracker // Pass the credit tracker with the result
            } as MatchResult & { creditUsage: CreditUsageTracker };
        } catch (error) {
            console.error('[AICompletionStrategy] Error:', error);
            return {
                success: false,
                content,
                message: `AI completion error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
}
