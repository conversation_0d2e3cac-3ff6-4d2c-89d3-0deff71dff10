import { MatchingStrategy, MatchResult } from './MatchingStrategy';
import { CodeTokenizer } from '../utils/CodeTokenizer';

/**
 * Strategy for token-based fuzzy matching
 */
export class TokenMatchingStrategy implements MatchingStrategy {
    public readonly name = 'Token Matching';
    private tokenizer = new CodeTokenizer();
    
    /**
     * Apply token-based matching
     */
    public async apply(content: string, search: string, replace: string, _options?: any): Promise<MatchResult> {
        try {
            // Tokenize the content and search pattern
            const contentTokens = this.tokenizer.tokenizeCode(content);
            const searchTokens = this.tokenizer.tokenizeCode(search);
            
            // If search is too short after tokenization, it's too ambiguous
            if (searchTokens.length < 3) {
                return { 
                    success: false, 
                    content,
                    message: 'Search pattern too short after tokenization'
                };
            }
            
            // Find the best matching position using sliding window with multiple window sizes
            let bestMatchPosition = -1;
            let bestMatchScore = 0;
            let bestMatchLength = 0;
            
            // Try with exact length
            this.findBestMatch(contentTokens, searchTokens, searchTokens.length, (position, score, length) => {
                if (score > bestMatchScore) {
                    bestMatchScore = score;
                    bestMatchPosition = position;
                    bestMatchLength = length;
                }
            });
            
            // Try with slightly larger window to account for added tokens
            this.findBestMatch(contentTokens, searchTokens, Math.floor(searchTokens.length * 1.2), (position, score, length) => {
                if (score > bestMatchScore) {
                    bestMatchScore = score;
                    bestMatchPosition = position;
                    bestMatchLength = length;
                }
            });
            
            // Try with slightly smaller window to account for removed tokens
            this.findBestMatch(contentTokens, searchTokens, Math.floor(searchTokens.length * 0.8), (position, score, length) => {
                if (score > bestMatchScore) {
                    bestMatchScore = score;
                    bestMatchPosition = position;
                    bestMatchLength = length;
                }
            });
            
            // If we found a good match
            if (bestMatchScore > 0.8 && bestMatchPosition !== -1) {
                // Find the actual text positions in the original content
                const startPos = this.findPositionOfToken(content, contentTokens[bestMatchPosition]);
                const endPos = this.findEndPositionOfToken(content, contentTokens[bestMatchPosition + bestMatchLength - 1]);
                
                if (startPos !== -1 && endPos !== -1) {
                    // Replace the content
                    const updatedContent = 
                        content.substring(0, startPos) + 
                        replace + 
                        content.substring(endPos);
                    
                    return {
                        success: true,
                        content: updatedContent,
                        score: bestMatchScore
                    };
                }
            }
            
            return { 
                success: false, 
                content,
                message: `No good token match found (best score: ${bestMatchScore.toFixed(2)})`
            };
        } catch (error) {
            console.error('[TokenMatchingStrategy] Error:', error);
            return { 
                success: false, 
                content,
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
    
    /**
     * Find the best matching position in content tokens for search tokens
     */
    private findBestMatch(
        contentTokens: string[], 
        searchTokens: string[], 
        windowSize: number,
        callback: (position: number, score: number, length: number) => void
    ): void {
        for (let i = 0; i <= contentTokens.length - windowSize; i++) {
            const windowTokens = contentTokens.slice(i, i + windowSize);
            const score = this.calculateTokenSimilarity(windowTokens, searchTokens);
            callback(i, score, windowSize);
        }
    }
    
    /**
     * Calculate similarity between two token arrays
     */
    private calculateTokenSimilarity(tokensA: string[], tokensB: string[]): number {
        const normalizedA = tokensA.map(t => t.trim()).filter(t => t !== '');
        const normalizedB = tokensB.map(t => t.trim()).filter(t => t !== '');
        
        let matches = 0;
        const minLength = Math.min(normalizedA.length, normalizedB.length);
        
        for (let i = 0; i < minLength; i++) {
            if (normalizedA[i] === normalizedB[i]) {
                matches++;
            }
        }
        
        return matches / Math.max(normalizedA.length, normalizedB.length);
    }
    
    /**
     * Find the position of a token in the original content
     */
    private findPositionOfToken(content: string, token: string): number {
        return content.indexOf(token);
    }
    
    /**
     * Find the end position of a token in the original content
     */
    private findEndPositionOfToken(content: string, token: string): number {
        const pos = content.indexOf(token);
        if (pos === -1) return -1;
        return pos + token.length;
    }
}
