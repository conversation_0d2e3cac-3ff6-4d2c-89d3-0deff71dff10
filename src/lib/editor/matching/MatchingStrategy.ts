/**
 * Interface for all matching strategies
 */
export interface MatchingStrategy {
    /**
     * Name of the strategy for logging
     */
    readonly name: string;
    
    /**
     * Apply the matching strategy to find and replace content
     * @param content The content to search in
     * @param search The search pattern
     * @param replace The replacement text
     * @param options Optional configuration parameters
     * @returns Result object with success flag and updated content
     */
    apply(content: string, search: string, replace: string, options?: any): Promise<MatchResult>;
}

/**
 * Result of a matching operation
 */
export interface MatchResult {
    success: boolean;
    content: string;
    score?: number;
    message?: string;
}
