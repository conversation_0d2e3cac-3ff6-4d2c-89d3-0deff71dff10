import { MatchingStrategy, MatchResult } from './MatchingStrategy';

/**
 * Strategy for normalized code matching (ignoring whitespace and formatting)
 */
export class NormalizedMatchingStrategy implements MatchingStrategy {
    public readonly name = 'Normalized Matching';
    
    /**
     * Apply normalized string matching
     */
    public async apply(content: string, search: string, replace: string, _options?: any): Promise<MatchResult> {
        try {
            // Normalize content and search by removing whitespace variations
            const normalizedContent = this.normalizeCode(content);
            const normalizedSearch = this.normalizeCode(search);
            
            // If normalized search is too short, it's too ambiguous
            if (normalizedSearch.length < 10) {
                return { 
                    success: false, 
                    content,
                    message: 'Normalized search too short'
                };
            }
            
            // Check if normalized search exists in normalized content
            if (!normalizedContent.includes(normalizedSearch)) {
                return { 
                    success: false, 
                    content,
                    message: 'No match found after normalization'
                };
            }
            
            // Find all occurrences of the normalized search
            const matches = this.findAllMatches(normalizedContent, normalizedSearch);
            
            // If there's more than one match, we have an ambiguity problem
            if (matches.length > 1) {
                return { 
                    success: false, 
                    content,
                    message: `Ambiguous match after normalization (${matches.length} occurrences)`
                };
            }
            
            // Find the actual text in the original content
            const matchIndex = matches[0];
            const originalMatchStart = this.findOriginalIndex(content, normalizedContent, matchIndex);
            const originalMatchEnd = this.findOriginalEndIndex(content, normalizedContent, matchIndex + normalizedSearch.length);
            
            if (originalMatchStart === -1 || originalMatchEnd === -1) {
                return { 
                    success: false, 
                    content,
                    message: 'Failed to map normalized indices back to original content'
                };
            }
            
            // Replace the content
            const updatedContent = 
                content.substring(0, originalMatchStart) + 
                replace + 
                content.substring(originalMatchEnd);
            
            return { 
                success: true, 
                content: updatedContent,
                score: 1.0
            };
        } catch (error) {
            console.error('[NormalizedMatchingStrategy] Error:', error);
            return { 
                success: false, 
                content,
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
    
    /**
     * Normalize code by removing extra whitespace and standardizing indentation
     */
    private normalizeCode(code: string): string {
        // Remove comments
        let normalized = code.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '');
        
        // Normalize whitespace
        normalized = normalized
            // Replace multiple spaces with a single space
            .replace(/\s+/g, ' ')
            // Remove spaces after opening and before closing brackets
            .replace(/\{\s+/g, '{').replace(/\s+\}/g, '}')
            .replace(/\(\s+/g, '(').replace(/\s+\)/g, ')')
            .replace(/\[\s+/g, '[').replace(/\s+\]/g, ']')
            // Remove spaces before and after operators
            .replace(/\s*([=+\-*/%&|^!<>:;,.])\s*/g, '$1')
            // Normalize line breaks
            .replace(/[\r\n]+/g, '\n');
        
        return normalized;
    }
    
    /**
     * Find all matches of a pattern in a string
     */
    private findAllMatches(text: string, pattern: string): number[] {
        const matches: number[] = [];
        let index = 0;
        
        while ((index = text.indexOf(pattern, index)) !== -1) {
            matches.push(index);
            index += pattern.length;
        }
        
        return matches;
    }
    
    /**
     * Find the original index in the content corresponding to the normalized index
     */
    private findOriginalIndex(original: string, normalized: string, normalizedIndex: number): number {
        if (normalizedIndex === 0) return 0;
        
        let originalIndex = 0;
        let normalizedPos = 0;
        
        for (let i = 0; i < original.length; i++) {
            const char = original[i];
            
            // Skip whitespace and comments in counting normalized position
            if (/\s/.test(char)) continue;
            
            if (normalizedPos === normalizedIndex) {
                originalIndex = i;
                break;
            }
            
            normalizedPos++;
            originalIndex++;
        }
        
        return originalIndex;
    }
    
    /**
     * Find the original end index in the content corresponding to the normalized end index
     */
    private findOriginalEndIndex(original: string, normalized: string, normalizedEndIndex: number): number {
        if (normalizedEndIndex >= normalized.length) return original.length;
        
        let originalIndex = 0;
        let normalizedPos = 0;
        
        for (let i = 0; i < original.length; i++) {
            if (normalizedPos === normalizedEndIndex) {
                originalIndex = i;
                break;
            }
            
            const char = original[i];
            
            // Skip whitespace and comments in counting normalized position
            if (/\s/.test(char)) continue;
            
            normalizedPos++;
            originalIndex++;
        }
        
        return originalIndex;
    }
}
