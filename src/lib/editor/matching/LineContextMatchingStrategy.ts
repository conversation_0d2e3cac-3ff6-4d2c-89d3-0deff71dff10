import { MatchingStrategy, MatchResult } from './MatchingStrategy';
import { StringUtils } from '../utils/StringUtils';

/**
 * Strategy for line-based context matching
 * This approach is good for code that has been reformatted but structure remains similar
 */
export class LineContextMatchingStrategy implements MatchingStrategy {
    public readonly name = 'Line Context Matching';
    private stringUtils = new StringUtils();
    
    /**
     * Apply line-based context matching
     */
    public async apply(content: string, search: string, replace: string, _options?: any): Promise<MatchResult> {
        try {
            // Split content and search into lines
            const contentLines = content.split('\n');
            const searchLines = search.split('\n');
            const replaceLines = replace.split('\n');
            
            // console.log(`[LineContextMatchingStrategy] Content has ${contentLines.length} lines`);
            // console.log(`[LineContextMatchingStrategy] Search has ${searchLines.length} lines`);
            // console.log(`[LineContextMatchingStrategy] Replace has ${replaceLines.length} lines`);
            
            // If search is too short, it's too ambiguous
            if (searchLines.length < 2) {
                return { 
                    success: false, 
                    content,
                    message: 'Search pattern too short (needs at least 2 lines)'
                };
            }
            
            // Create a signature from the first and last few lines of the search
            const searchSignature = this.createSignature(searchLines);
            // console.log(`[LineContextMatchingStrategy] Search signature: ${searchSignature}`);
            
            // Slide through content lines looking for matching signature
            let bestMatchIndex = -1;
            let bestMatchScore = 0;
            let bestWindowSignature = '';
            let bestMatchLength = searchLines.length;
            
            for (let i = 0; i <= contentLines.length - searchLines.length; i++) {
                const windowLines = contentLines.slice(i, i + searchLines.length);
                const windowSignature = this.createSignature(windowLines);
                
                const score = this.stringUtils.calculateStringSimilarity(searchSignature, windowSignature);
                if (score > bestMatchScore && score > 0.8) {
                    bestMatchScore = score;
                    bestMatchIndex = i;
                    bestWindowSignature = windowSignature;
                }
            }
            
            if (bestMatchIndex !== -1) {
                // console.log(`[LineContextMatchingStrategy] Best match found at line ${bestMatchIndex+1} with score ${bestMatchScore}`);
                // console.log(`[LineContextMatchingStrategy] Best match signature: ${bestWindowSignature}`);
                
                // Get the matched content
                const matchedLines = contentLines.slice(bestMatchIndex, bestMatchIndex + searchLines.length);
                const matchedContent = matchedLines.join('\n');
                
                // // console.log('[LineContextMatchingStrategy] Matched content:');
                // // console.log('----------------------------------------');
                // // console.log(matchedContent);
                // // console.log('----------------------------------------');
                //
                // Check for structural integrity
                const structuralAnalysis = this.analyzeStructuralIntegrity(matchedContent, replace);
                
                // Determine if we need to include additional lines to maintain structural integrity
                let additionalLinesNeeded = 0;
                if (structuralAnalysis.needsExtraClosingTags) {
                    // Look ahead for closing tags that balance the structure
                    additionalLinesNeeded = this.findAdditionalClosingLines(contentLines, bestMatchIndex + searchLines.length, structuralAnalysis.missingClosingTags);
                    
                    if (additionalLinesNeeded > 0) {
                        // console.log(`[LineContextMatchingStrategy] Need to include ${additionalLinesNeeded} additional lines for structural integrity`);
                        
                        // Update the matched content to include these additional lines
                        const extendedMatchedLines = contentLines.slice(bestMatchIndex, bestMatchIndex + searchLines.length + additionalLinesNeeded);
                        const extendedMatchedContent = extendedMatchedLines.join('\n');
                        
                        // console.log('[LineContextMatchingStrategy] Extended matched content:');
                        // console.log('----------------------------------------');
                        // console.log(extendedMatchedContent);
                        // console.log('----------------------------------------');
                        
                        // Update the best match length
                        bestMatchLength = searchLines.length + additionalLinesNeeded;
                    }
                }
                
                // Prepare the replacement
                let finalReplacement = replace;
                
                // If we found additional closing tags, append them to the replacement
                if (additionalLinesNeeded > 0) {
                    const closingLines = contentLines.slice(
                        bestMatchIndex + searchLines.length,
                        bestMatchIndex + searchLines.length + additionalLinesNeeded
                    );
                    
                    // Append the closing lines to the replacement
                    finalReplacement = replace + '\n' + closingLines.join('\n');
                    
                    // console.log('[LineContextMatchingStrategy] Final replacement with preserved closing tags:');
                    // console.log('----------------------------------------');
                    // console.log(finalReplacement);
                    // console.log('----------------------------------------');
                }
                
                // Replace the content
                const updatedContentLines = [...contentLines];
                const finalReplaceLines = finalReplacement.split('\n');
                updatedContentLines.splice(bestMatchIndex, bestMatchLength, ...finalReplaceLines);
                
                // Log the result
                // // console.log('[LineContextMatchingStrategy] Content after replacement:');
                // // console.log('----------------------------------------');
                const contextBefore = updatedContentLines.slice(Math.max(0, bestMatchIndex - 5), bestMatchIndex).join('\n');
                const replacement = finalReplaceLines.join('\n');
                const contextAfter = updatedContentLines.slice(bestMatchIndex + finalReplaceLines.length, Math.min(updatedContentLines.length, bestMatchIndex + finalReplaceLines.length + 5)).join('\n');
                
                // // console.log('--- Context before ---');
                // // console.log(contextBefore);
                // // console.log('--- Replacement ---');
                // // console.log(replacement);
                // // console.log('--- Context after ---');
                // // console.log(contextAfter);
                // // console.log('----------------------------------------');
                
                return { 
                    success: true, 
                    content: updatedContentLines.join('\n'),
                    score: bestMatchScore
                };
            }
            
            return { 
                success: false, 
                content,
                message: 'No matching line context found'
            };
        } catch (error) {
            console.error('[LineContextMatchingStrategy] Error:', error);
            return { 
                success: false, 
                content,
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
    
    /**
     * Create a signature from lines of code
     */
    private createSignature(lines: string[]): string {
        const signatureLines: string[] = [];
        const numLines = Math.min(3, Math.floor(lines.length / 2));
        
        // Add first few lines
        for (let i = 0; i < numLines; i++) {
            signatureLines.push(this.getSignificantTokens(lines[i]));
        }
        
        // Add last few lines
        for (let i = lines.length - numLines; i < lines.length; i++) {
            signatureLines.push(this.getSignificantTokens(lines[i]));
        }
        
        return signatureLines.join('|');
    }
    
    /**
     * Extract significant tokens from a line (removing noise like whitespace)
     */
    private getSignificantTokens(line: string): string {
        return line
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[;,.(){}[\]<>]/g, '')
            .trim();
    }
    
    /**
     * Analyze structural integrity issues between matched content and replacement
     */
    private analyzeStructuralIntegrity(matchedContent: string, replacement: string): {
        matchedOpenTags: number;
        matchedCloseTags: number;
        replaceOpenTags: number;
        replaceCloseTags: number;
        needsExtraClosingTags: boolean;
        missingClosingTags: number;
        tagTypes: string[];
    } {
        // Extract all tags
        const matchedOpenTagMatches = matchedContent.match(/<([a-zA-Z][a-zA-Z0-9]*)[^>]*?(?:\/)?>/g) || [];
        const matchedCloseTagMatches = matchedContent.match(/<\/([a-zA-Z][a-zA-Z0-9]*)[^>]*?>/g) || [];
        const replaceOpenTagMatches = replacement.match(/<([a-zA-Z][a-zA-Z0-9]*)[^>]*?(?:\/)?>/g) || [];
        const replaceCloseTagMatches = replacement.match(/<\/([a-zA-Z][a-zA-Z0-9]*)[^>]*?>/g) || [];
        
        // Count tags
        const matchedOpenTags = matchedOpenTagMatches.length;
        const matchedCloseTags = matchedCloseTagMatches.length;
        const replaceOpenTags = replaceOpenTagMatches.length;
        const replaceCloseTags = replaceCloseTagMatches.length;
        
        // Extract tag types
        const tagTypeRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)/;
        const matchedTagTypes = new Set<string>();
        
        matchedOpenTagMatches.forEach(tag => {
            const match = tag.match(tagTypeRegex);
            if (match && match[1]) {
                matchedTagTypes.add(match[1]);
            }
        });
        
        // console.log(`[LineContextMatchingStrategy] Matched content tags: ${matchedOpenTags} open, ${matchedCloseTags} close`);
        // console.log(`[LineContextMatchingStrategy] Replacement tags: ${replaceOpenTags} open, ${replaceCloseTags} close`);
        
        if (matchedOpenTags !== matchedCloseTags) {
            console.warn('[LineContextMatchingStrategy] WARNING: Matched content has unbalanced tags!');
        }
        
        if (replaceOpenTags !== replaceCloseTags) {
            console.warn('[LineContextMatchingStrategy] WARNING: Replacement has unbalanced tags!');
        }
        
        const needsExtraClosingTags = matchedOpenTags - matchedCloseTags < replaceOpenTags - replaceCloseTags;
        const missingClosingTags = (replaceOpenTags - replaceCloseTags) - (matchedOpenTags - matchedCloseTags);
        
        if (matchedOpenTags - matchedCloseTags !== replaceOpenTags - replaceCloseTags) {
            console.warn('[LineContextMatchingStrategy] WARNING: Tag balance differs between matched content and replacement!');
            console.warn(`[LineContextMatchingStrategy] This may cause structural issues in the resulting code.`);
            
            if (needsExtraClosingTags) {
                console.warn(`[LineContextMatchingStrategy] Need to preserve ${missingClosingTags} closing tags from original content`);
            }
        }
        
        return {
            matchedOpenTags,
            matchedCloseTags,
            replaceOpenTags,
            replaceCloseTags,
            needsExtraClosingTags,
            missingClosingTags,
            tagTypes: Array.from(matchedTagTypes)
        };
    }
    
    /**
     * Find additional lines containing closing tags that need to be preserved
     */
    private findAdditionalClosingLines(contentLines: string[], startIndex: number, neededTags: number): number {
        let additionalLines = 0;
        let foundClosingTags = 0;
        
        // Look ahead for closing tags
        for (let i = startIndex; i < contentLines.length && foundClosingTags < neededTags; i++) {
            const line = contentLines[i];
            const closeTagsInLine = (line.match(/<\/[a-zA-Z][a-zA-Z0-9]*>/g) || []).length;
            
            if (closeTagsInLine > 0) {
                foundClosingTags += closeTagsInLine;
                additionalLines++;
                
                // console.log(`[LineContextMatchingStrategy] Found ${closeTagsInLine} closing tags in line: ${line.trim()}`);
            }
        }
        
        return additionalLines;
    }
}
