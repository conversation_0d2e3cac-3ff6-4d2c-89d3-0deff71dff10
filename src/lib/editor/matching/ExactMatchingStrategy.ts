import { MatchingStrategy, MatchResult } from './MatchingStrategy';

/**
 * Strategy for exact string matching
 */
export class ExactMatchingStrategy implements MatchingStrategy {
    public readonly name = 'Exact Matching';
    
    /**
     * Apply exact string matching
     */
    public async apply(content: string, search: string, replace: string, _options?: any): Promise<MatchResult> {
        // Check if the search string exists in the content
        if (!content.includes(search)) {
            return { success: false, content };
        }
        
        // Check for ambiguity (multiple matches)
        const matches = this.countMatches(content, search);
        if (matches > 1) {
            return { 
                success: false, 
                content,
                message: `Ambiguous match (${matches} occurrences)`
            };
        }
        
        // Replace the content
        const updatedContent = content.replace(search, replace);
        
        return {
            success: true,
            content: updatedContent
        };
    }
    
    /**
     * Count the number of occurrences of a string in content
     */
    private countMatches(content: string, search: string): number {
        let count = 0;
        let pos = content.indexOf(search);
        
        while (pos !== -1) {
            count++;
            pos = content.indexOf(search, pos + 1);
        }
        
        return count;
    }
}
