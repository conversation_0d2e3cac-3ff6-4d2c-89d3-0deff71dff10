import { MatchingStrategy, MatchResult } from './MatchingStrategy';

/**
 * Strategy for n-gram based matching
 */
export class NGramMatchingStrategy implements MatchingStrategy {
    public readonly name = 'N-Gram Matching';
    
    /**
     * Apply n-gram matching
     */
    public async apply(content: string, search: string, replace: string, _options?: any): Promise<MatchResult> {
        try {
            // Generate n-grams for content and search
            const contentNGrams = this.generateNGrams(content, 3);
            const searchNGrams = this.generateNGrams(search, 3);
            
            // Find the position with the highest n-gram overlap
            let bestStartPos = -1;
            let bestEndPos = -1;
            let bestScore = 0;
            
            // Slide through content in chunks
            const chunkSize = 100;
            const overlap = 50;
            
            for (let i = 0; i < content.length; i += (chunkSize - overlap)) {
                const end = Math.min(i + chunkSize, content.length);
                const chunk = content.substring(i, end);
                const chunkNGrams = this.generateNGrams(chunk, 3);
                
                const overlapScore = this.calculateNGramOverlap(chunkNGrams, searchNGrams);
                
                if (overlapScore > bestScore) {
                    bestScore = overlapScore;
                    bestStartPos = i;
                    bestEndPos = end;
                }
                
                // If we've reached the end of content, break
                if (end === content.length) break;
            }
            
            // If we found a good match
            if (bestScore > 0.7 && bestStartPos !== -1) {
                // Replace the content
                const updatedContent = 
                    content.substring(0, bestStartPos) + 
                    replace + 
                    content.substring(bestEndPos);
                
                return {
                    success: true,
                    content: updatedContent,
                    score: bestScore
                };
            }
            
            return { 
                success: false, 
                content,
                message: `No good n-gram match found (best score: ${bestScore.toFixed(2)})`
            };
        } catch (error) {
            console.error('[NGramMatchingStrategy] Error:', error);
            return { 
                success: false, 
                content,
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
    
    /**
     * Generate n-grams from text
     */
    private generateNGrams(text: string, n: number): Set<string> {
        const ngrams = new Set<string>();
        for (let i = 0; i <= text.length - n; i++) {
            ngrams.add(text.substring(i, i + n));
        }
        return ngrams;
    }
    
    /**
     * Calculate overlap between two sets of n-grams
     */
    private calculateNGramOverlap(setA: Set<string>, setB: Set<string>): number {
        let intersection = 0;
        for (const ngram of setA) {
            if (setB.has(ngram)) {
                intersection++;
            }
        }
        return intersection / Math.max(setA.size, setB.size);
    }
}
