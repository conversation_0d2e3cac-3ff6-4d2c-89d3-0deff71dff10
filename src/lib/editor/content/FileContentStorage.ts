/**
 * FileContentStorage is responsible for storing and retrieving file contents
 */
export class FileContentStorage {
    private fileContents: Record<string, string> = {};
    
    /**
     * Set the content of a file
     */
    public setFileContent(path: string, content: string): void {
        this.fileContents[path] = content;
    }
    
    /**
     * Get the content of a file
     */
    public getFileContent(path: string): string | undefined {
        return this.fileContents[path];
    }
    
    /**
     * Check if a file exists in storage
     */
    public hasFile(path: string): boolean {
        return path in this.fileContents;
    }
    
    /**
     * Get all file paths
     */
    public getAllFilePaths(): string[] {
        return Object.keys(this.fileContents);
    }
    
    /**
     * Count the number of files
     */
    public countFiles(): number {
        return Object.keys(this.fileContents).length;
    }
    
    /**
     * Clear all file contents
     */
    public clear(): void {
        this.fileContents = {};
    }
}
