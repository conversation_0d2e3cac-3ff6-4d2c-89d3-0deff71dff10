import {embed, embedMany} from 'ai';
import { openai } from '@ai-sdk/openai';

/**
 * Service for performing vector similarity operations with caching
 */
export class VectorSimilarityService {
    // Cache embeddings to avoid redundant API calls
    private embeddingCache: Map<string, number[]> = new Map();
    
    // Maximum number of chunks to process at once
    private readonly MAX_CHUNKS = 20;
    
    // Maximum chunk size to consider
    private readonly MAX_CHUNK_SIZE = 1000;
    
    // Chunk size for vector similarity search
    private readonly CHUNK_SIZE = 200;
    
    // Chunk overlap for vector similarity search
    private readonly CHUNK_OVERLAP = 50;
    
    /**
     * Get embedding for text with caching
     */
    public async getEmbedding(text: string): Promise<number[]> {
        const startTime = performance.now();
        
        // Check cache first
        const cacheKey = this.getCacheKey(text);
        if (this.embeddingCache.has(cacheKey)) {
            const cachedEmbedding = this.embeddingCache.get(cacheKey)!;
            const endTime = performance.now();
            console.log(`[VectorSimilarity] Retrieved embedding from cache in ${(endTime - startTime).toFixed(2)}ms`);
            return cachedEmbedding;
        }
        
        // Generate new embedding
        console.log(`[VectorSimilarity] Generating embedding for text (${text.length} chars)`);
        const { embedding } = await embed({
            model: openai.embedding('text-embedding-3-small'),
            value: text,  // Changed from 'value' to 'input' to match OpenAI API requirements
        });
        
        // Cache the result
        this.embeddingCache.set(cacheKey, embedding);
        
        const endTime = performance.now();
        console.log(`[VectorSimilarity] Generated embedding in ${(endTime - startTime).toFixed(2)}ms`);
        
        return embedding;
    }
    
    /**
     * Get embeddings for multiple texts in a single batch request
     */
    public async getBatchEmbeddings(texts: string[]): Promise<number[][]> {
        const startTime = performance.now();
        
        // Check cache first and collect texts that need embeddings
        const uncachedTexts: string[] = [];
        const cacheKeys: string[] = [];
        const results: (number[] | null)[] = new Array(texts.length).fill(null);
        
        texts.forEach((text, index) => {
            const cacheKey = this.getCacheKey(text);
            if (this.embeddingCache.has(cacheKey)) {
                results[index] = this.embeddingCache.get(cacheKey)!;
            } else {
                uncachedTexts.push(text);
                cacheKeys.push(cacheKey);
            }
        });
        
        if (uncachedTexts.length === 0) {
            const endTime = performance.now();
            console.log(`[VectorSimilarity] Retrieved all ${texts.length} embeddings from cache in ${(endTime - startTime).toFixed(2)}ms`);
            return results as number[][];
        }
        
        // Generate embeddings for uncached texts
        console.log(`[VectorSimilarity] Generating batch embeddings for ${uncachedTexts.length} texts`);
        
        // Process in smaller batches to avoid API limits
        const batchSize = 20; // OpenAI has a limit on batch size
        const embeddings: number[][] = [];
        
        for (let i = 0; i < uncachedTexts.length; i += batchSize) {
            const batch = uncachedTexts.slice(i, i + batchSize);
            const batchStartTime = performance.now();
            
            const response = await embedMany({
                model: openai.embedding('text-embedding-3-small'),
                values: batch,  // Changed from 'value' to 'input' to match OpenAI API requirements
            });
            
            const batchEndTime = performance.now();
            console.log(`[VectorSimilarity] Generated batch of ${batch.length} embeddings in ${(batchEndTime - batchStartTime).toFixed(2)}ms`);
            
            // Add to results and cache
            response.embeddings.forEach((embedding, j) => {
                const originalIndex = texts.indexOf(uncachedTexts[i + j]);
                if (originalIndex !== -1) {
                    results[originalIndex] = embedding;
                }
                this.embeddingCache.set(cacheKeys[i + j], embedding);
            });
            
            embeddings.push(...response.embeddings);
        }
        
        const endTime = performance.now();
        console.log(`[VectorSimilarity] Generated ${uncachedTexts.length} embeddings in ${(endTime - startTime).toFixed(2)}ms`);
        
        return results as number[][];
    }
    
    /**
     * Find the most similar chunk to the query in the content
     * @param content The content to search in
     * @param query The query to search for
     * @returns The most similar chunk and its position in the content
     */
    public async findMostSimilarChunk(
        content: string,
        query: string
    ): Promise<{ chunk: string; startIndex: number; endIndex: number; similarity: number } | null> {
        if (!content || !query) {
            console.log('[VectorSimilarityService] Empty content or query');
            return null;
        }

        // If query is very short, vector similarity is not reliable
        if (query.length < 20) {
            console.log('[VectorSimilarityService] Query too short for reliable vector similarity');
            return null;
        }

        try {
            console.log('[VectorSimilarityService] Starting vector similarity search');
            console.time('vectorSimilaritySearch');

            // Split content into chunks with overlap
            const chunks = this.splitContentIntoChunks(content, this.CHUNK_SIZE, this.CHUNK_OVERLAP);
            
            // Limit the number of chunks to process to avoid excessive API calls
            const processedChunks = this.selectRepresentativeChunks(chunks, this.MAX_CHUNKS);
            
            console.log(`[VectorSimilarityService] Processing ${processedChunks.length} chunks out of ${chunks.length} total chunks`);
            
            // Get embeddings for all chunks and the query
            const textsToEmbed = [...processedChunks.map(chunk => chunk.text), query];
            const embeddings = await this.getBatchEmbeddings(textsToEmbed);
            
            if (!embeddings || embeddings.length !== textsToEmbed.length) {
                console.log('[VectorSimilarityService] Failed to get embeddings');
                return null;
            }
            
            // The query embedding is the last one
            const queryEmbedding = embeddings[embeddings.length - 1];
            
            // Calculate similarities between query and each chunk
            let bestSimilarity = -1;
            let bestChunkIndex = -1;
            
            for (let i = 0; i < processedChunks.length; i++) {
                const similarity = this.cosineSimilarity(queryEmbedding, embeddings[i]);
                
                if (similarity > bestSimilarity) {
                    bestSimilarity = similarity;
                    bestChunkIndex = i;
                }
            }
            
            // Use a lower threshold for code (0.80 instead of 0.90)
            // Code often has minor variations that shouldn't affect matching
            const SIMILARITY_THRESHOLD = 0.80;
            
            if (bestSimilarity >= SIMILARITY_THRESHOLD && bestChunkIndex !== -1) {
                console.log(`[VectorSimilarityService] Found match with similarity: ${bestSimilarity.toFixed(4)}`);
                
                // Find the actual position of the chunk in the original content
                const chunk = processedChunks[bestChunkIndex];
                const startIndex = content.indexOf(chunk.text);
                
                if (startIndex === -1) {
                    console.log('[VectorSimilarityService] Could not find chunk in original content');
                    return null;
                }
                
                const endIndex = startIndex + chunk.text.length;
                
                console.timeEnd('vectorSimilaritySearch');
                return {
                    chunk: chunk.text,
                    startIndex,
                    endIndex,
                    similarity: bestSimilarity
                };
            } else {
                // If best match is below threshold, try with a more aggressive chunking strategy
                if (bestSimilarity > 0.70) {
                    console.log(`[VectorSimilarityService] Best match (${bestSimilarity.toFixed(4)}) below threshold, trying with smaller chunks`);
                    
                    // Try with smaller chunks and more overlap
                    const smallerChunks = this.splitContentIntoChunks(content, Math.floor(this.CHUNK_SIZE / 2), Math.floor(this.CHUNK_OVERLAP * 1.5));
                    const processedSmallerChunks = this.selectRepresentativeChunks(smallerChunks, this.MAX_CHUNKS * 2);
                    
                    console.log(`[VectorSimilarityService] Processing ${processedSmallerChunks.length} smaller chunks`);
                    
                    // Get embeddings for all smaller chunks and the query
                    const textsToEmbedSmaller = [...processedSmallerChunks.map(chunk => chunk.text), query];
                    const embeddingsSmaller = await this.getBatchEmbeddings(textsToEmbedSmaller);
                    
                    if (!embeddingsSmaller || embeddingsSmaller.length !== textsToEmbedSmaller.length) {
                        console.log('[VectorSimilarityService] Failed to get embeddings for smaller chunks');
                        return null;
                    }
                    
                    // The query embedding is the last one
                    const queryEmbeddingSmaller = embeddingsSmaller[embeddingsSmaller.length - 1];
                    
                    // Calculate similarities between query and each smaller chunk
                    let bestSimilaritySmaller = -1;
                    let bestChunkIndexSmaller = -1;
                    
                    for (let i = 0; i < processedSmallerChunks.length; i++) {
                        const similarity = this.cosineSimilarity(queryEmbeddingSmaller, embeddingsSmaller[i]);
                        
                        if (similarity > bestSimilaritySmaller) {
                            bestSimilaritySmaller = similarity;
                            bestChunkIndexSmaller = i;
                        }
                    }
                    
                    if (bestSimilaritySmaller >= SIMILARITY_THRESHOLD && bestChunkIndexSmaller !== -1) {
                        console.log(`[VectorSimilarityService] Found match with smaller chunks, similarity: ${bestSimilaritySmaller.toFixed(4)}`);
                        
                        // Find the actual position of the chunk in the original content
                        const chunk = processedSmallerChunks[bestChunkIndexSmaller];
                        const startIndex = content.indexOf(chunk.text);
                        
                        if (startIndex === -1) {
                            console.log('[VectorSimilarityService] Could not find smaller chunk in original content');
                            return null;
                        }
                        
                        const endIndex = startIndex + chunk.text.length;
                        
                        console.timeEnd('vectorSimilaritySearch');
                        return {
                            chunk: chunk.text,
                            startIndex,
                            endIndex,
                            similarity: bestSimilaritySmaller
                        };
                    }
                }
                
                console.log(`[VectorSimilarityService] No match found above threshold (best: ${bestSimilarity.toFixed(4)})`);
                console.timeEnd('vectorSimilaritySearch');
                return null;
            }
        } catch (error) {
            console.error('[VectorSimilarityService] Error in vector similarity search:', error);
            console.timeEnd('vectorSimilaritySearch');
            return null;
        }
    }
    
    /**
     * Select a representative subset of chunks to avoid processing too many
     */
    private selectRepresentativeChunks(
        chunks: Array<{ text: string; start: number; end: number }>,
        maxChunks: number
    ): Array<{ text: string; start: number; end: number }> {
        // If we have fewer chunks than the max, return all of them
        if (chunks.length <= maxChunks) {
            return chunks;
        }
        
        // Select chunks at regular intervals to cover the whole content
        const step = chunks.length / maxChunks;
        const selectedChunks: Array<{ text: string; start: number; end: number }> = [];
        
        for (let i = 0; i < maxChunks; i++) {
            const index = Math.min(Math.floor(i * step), chunks.length - 1);
            selectedChunks.push(chunks[index]);
        }
        
        return selectedChunks;
    }
    
    /**
     * Split content into overlapping chunks for vector similarity search
     */
    private splitContentIntoChunks(
        content: string, 
        chunkSize: number,
        overlap: number
    ): Array<{ text: string; start: number; end: number }> {
        const chunks: Array<{ text: string; start: number; end: number }> = [];
        
        for (let i = 0; i < content.length; i += (chunkSize - overlap)) {
            const end = Math.min(i + chunkSize, content.length);
            chunks.push({
                text: content.substring(i, end),
                start: i,
                end
            });
            
            if (end === content.length) break;
        }
        
        return chunks;
    }
    
    /**
     * Calculate cosine similarity between two vectors
     */
    private cosineSimilarity(a: number[], b: number[]): number {
        let dotProduct = 0;
        let normA = 0;
        let normB = 0;
        
        for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }
        
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    
    /**
     * Generate a cache key for text
     * Uses a simple hash to avoid storing the full text as keys
     */
    private getCacheKey(text: string): string {
        // Simple hash function
        let hash = 0;
        for (let i = 0; i < text.length; i++) {
            const char = text.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return `${hash}_${text.length}`;
    }
    
    /**
     * Clear the embedding cache
     */
    public clearCache(): void {
        this.embeddingCache.clear();
        console.log('[VectorSimilarity] Embedding cache cleared');
    }
}
