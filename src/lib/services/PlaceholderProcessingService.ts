import { performance } from 'perf_hooks';

interface ProcessingResult {
    processedText: string;
    metrics: {
        totalTime: number;
        uniqueCount: number;
        totalMatches: number;
        searchTime: number;
        replacementTime: number;
    };
}

interface CacheEntry {
    url: string;
    timestamp: number;
}

export class PlaceholderProcessingService {
    private imageCache: Map<string, CacheEntry> = new Map();
    private videoCache: Map<string, CacheEntry> = new Map();
    private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

    constructor(private llmMediaService: any) {}

    private getCachedUrl(cache: Map<string, CacheEntry>, key: string): string | null {
        const entry = cache.get(key);
        if (!entry) return null;

        // Check if cache entry has expired
        if (Date.now() - entry.timestamp > this.CACHE_TTL) {
            cache.delete(key);
            return null;
        }

        return entry.url;
    }

    private setCacheEntry(cache: Map<string, CacheEntry>, key: string, url: string) {
        cache.set(key, {
            url,
            timestamp: Date.now()
        });
    }

    private async processPlaceholders(
        text: string,
        type: 'IMAGE' | 'VIDEO',
        searchFn: (desc: string) => Promise<string>
    ): Promise<ProcessingResult> {
        const startTime = performance.now();
        
        const regex = new RegExp(`{{${type}:([^}]+)}}`, 'g');
        const matches = text.match(regex);

        if (!matches) {
            return {
                processedText: text,
                metrics: {
                    totalTime: 0,
                    uniqueCount: 0,
                    totalMatches: 0,
                    searchTime: 0,
                    replacementTime: 0
                }
            };
        }

        // Create a map of unique descriptions
        const uniqueDescriptions = new Map<string, string[]>();
        matches.forEach(match => {
            const description = match.match(new RegExp(`{{${type}:([^}]+)}}`))?.[1];
            if (description) {
                const matchesForDesc = uniqueDescriptions.get(description) || [];
                matchesForDesc.push(match);
                uniqueDescriptions.set(description, matchesForDesc);
            }
        });

        console.log(`[${type}] Found ${uniqueDescriptions.size} unique descriptions from ${matches.length} total matches`);

        // Fetch all unique media in parallel
        const searchStartTime = performance.now();
        const searchResults = await Promise.all(
            Array.from(uniqueDescriptions.keys()).map(async description => {
                // Try to get from cache first
                const cache = type === 'IMAGE' ? this.imageCache : this.videoCache;
                const cachedUrl = this.getCachedUrl(cache, description);
                
                if (cachedUrl) {
                    console.log(`[${type}] Using cached result for "${description}", url:`, cachedUrl);
                    return [description, cachedUrl] as const;
                }

                // If not in cache, fetch and cache the result
                const url = await searchFn(description);
                console.log(`[${type}] Fetched new result for "${description}", url:`, url);
                this.setCacheEntry(cache, description, url);
                return [description, url] as const;
            })
        );
        const searchTime = performance.now() - searchStartTime;

        // Create a map of description to URL for easy lookup
        const urlMap = new Map(searchResults);

        // Replace all matches with their corresponding URLs
        const replacementStartTime = performance.now();
        let processedText = text;
        for (const [description, matches] of uniqueDescriptions) {
            const url = urlMap.get(description);
            if (url) {
                matches.forEach(match => {
                    processedText = processedText.replace(match, url);
                });
            }
        }
        const replacementTime = performance.now() - replacementStartTime;
        const totalTime = performance.now() - startTime;

        const metrics = {
            totalTime,
            uniqueCount: uniqueDescriptions.size,
            totalMatches: matches.length,
            searchTime,
            replacementTime
        };

        console.log(`[${type}] Processing metrics:`, metrics);
        return { processedText, metrics };
    }

    async processImagePlaceholders(text: string): Promise<ProcessingResult> {
        return this.processPlaceholders(text, 'IMAGE', 
            (desc) => this.llmMediaService.searchImage(desc));
    }

    async processVideoPlaceholders(text: string): Promise<ProcessingResult> {
        return this.processPlaceholders(text, 'VIDEO', 
            (desc) => this.llmMediaService.searchVideo(desc));
    }
}
