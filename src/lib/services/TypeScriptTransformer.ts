/**
 * TypeScriptTransformer.ts
 * A service that uses Sucrase to reliably transform TypeScript code for Expo Snack compatibility.
 * 
 * This service handles stripping TypeScript-specific syntax that isn't compatible with
 * Expo Snack's SystemJS runtime, such as union types and typeof references.
 */

import { FileNode } from '@/types/file';
import { CodeFile } from '@/types/editor';

// This will be dynamically imported on the server side
let sucrase: any = null;

/**
 * Transforms TypeScript code to be compatible with Expo Snack's SystemJS runtime
 * by properly handling type annotations, union types, and typeof references.
 * 
 * @param files Array of file items to transform
 * @returns Transformed file items
 */
export async function transformTypeScript<T extends FileNode | CodeFile>(files: T[]): Promise<T[]> {
  // Dynamically import sucrase only on the server side
  if (!sucrase) {
    try {
      sucrase = await import('sucrase');
    } catch (error) {
      console.error('Failed to import sucrase:', error);
      // Fallback to returning the original files if sucrase can't be imported
      return files;
    }
  }

  return files.map(file => {
    // Only process TypeScript files
    if (needsTransformation(file.name)) {
      try {
        // Use sucrase to transform TypeScript to JavaScript
        // The 'typescript' transform removes type annotations
        // The 'jsx' transform handles JSX syntax if present
        const transforms = file.name.endsWith('.tsx') 
          ? ['typescript', 'jsx'] 
          : ['typescript'];
        
        const result = sucrase.transform(file.content, {
          transforms,
          production: true, // Removes development-only code
          filePath: file.name, // Helps with source maps and error messages
        });

        // Return the transformed content
        return {
          ...file,
          content: result.code,
        };
      } catch (error) {
        console.error(`Error transforming file ${file.name}:`, error);
        // Return the original file if transformation fails
        return file;
      }
    }

    // Return non-TypeScript files as-is
    return file;
  });
}

/**
 * Checks if a file needs TypeScript transformation
 * 
 * @param fileName Name of the file to check
 * @returns Boolean indicating if the file needs transformation
 */
export function needsTransformation(fileName: string): boolean {
  return fileName.endsWith('.ts') || fileName.endsWith('.tsx');
}
