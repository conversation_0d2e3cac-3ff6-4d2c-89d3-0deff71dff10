import * as ts from 'typescript';
import { customModel } from '@/lib/ai';
import { generateText } from 'ai';
import { DEFAULT_MODEL_NAME, models } from '@/lib/ai/models';

export interface ValidationResult {
  isValid: boolean;
  errors?: Array<{
    line: number;
    message: string;
    severity: 'error' | 'warning';
  }>;
  fixedCode?: string;
}

export class CodeValidator {
  static async quickValidate(code: string): Promise<ValidationResult> {
    try {
      // Create compiler options
      const compilerOptions = {
        jsx: ts.JsxEmit.React,
        target: ts.ScriptTarget.ESNext,
        module: ts.ModuleKind.ESNext,
        moduleResolution: ts.ModuleResolutionKind.NodeJs,
        allowJs: true,
        allowSyntheticDefaultImports: true,
        esModuleInterop: true,
        isolatedModules: true,
        lib: ['dom', 'dom.iterable', 'esnext'],
        strict: true,
        skipLibCheck: true,
        noEmit: true,
        incremental: true
      };

      // Create the program
      const fileName = 'temp.tsx';
      const sourceFile = ts.createSourceFile(
        fileName,
        code,
        ts.ScriptTarget.Latest,
        true,
        ts.ScriptKind.TSX
      );

      const host = ts.createCompilerHost(compilerOptions);
      const originalGetSourceFile = host.getSourceFile;
      host.getSourceFile = (name, languageVersion) => {
        if (name === fileName) {
          return sourceFile;
        }
        return originalGetSourceFile(name, languageVersion);
      };

      const program = ts.createProgram([fileName], compilerOptions, host);
      const syntaxErrors = ts.getPreEmitDiagnostics(program, sourceFile);

      if (syntaxErrors.length > 0) {
        return {
          isValid: false,
          errors: syntaxErrors.map(error => ({
            // @ts-ignore
            line: error?.file?.getLineAndCharacterOfPosition(error.start || 0)?.line + 1 || 1,
            message: ts.flattenDiagnosticMessageText(error.messageText, '\n'),
            severity: 'error'
          }))
        };
      }
      return { isValid: true };
    } catch (e: any) {
      return {
        isValid: false,
        errors: [{
          line: (e as any).loc?.line || 0,
          message: e.message,
          severity: 'error'
        }]
      };
    }
  }

  static async fixWithGPT35(code: string, error: any): Promise<string> {
    const result = await generateText({
      model: customModel('qwen/qwen-max'),
      messages: [
        {
          role: 'system',
          content: 'You are a React Native code fixing assistant. Fix the syntax error in the provided code. Only return the fixed code, nothing else. Not even the file formatting or backticks. Make sure the code follows React Native best practices.'
        },
        {
          role: 'user',
          content: `Fix this React Native code that has the following error on line ${error.line}: ${error.message}\n\nCode:\n${code}`
        }
      ],
      temperature: 0.3
    });

    return result.text.trim();
  }

  static async validateAndFix(content: string, maxAttempts = 3): Promise<string> {
    let attempts = 0;
    let currentContent = content;
    
    while (attempts < maxAttempts) {
      const validationResult = await this.quickValidate(currentContent);
      
      if (validationResult.isValid) {
        return currentContent;
      }
      
      // Try to fix with GPT-3.5
      currentContent = await this.fixWithGPT35(
        currentContent,
        validationResult.errors![0]
      );
      
      attempts++;
    }
    
    throw new Error('Failed to fix code after multiple attempts');
  }

  /**
   * Validates a collection of files for Expo compatibility and syntax errors
   * @param files Array of file objects with name and content
   * @returns Validation results with errors grouped by file
   */
  static async validateExpoFiles(files: Array<{name: string, content: string}>): Promise<{
    isValid: boolean;
    fileResults: Record<string, ValidationResult>;
    summary: string;
  }> {
    // Track validation results for each file
    const fileResults: Record<string, ValidationResult> = {};
    let hasErrors = false;
    
    // Process each file in parallel for better performance
    await Promise.all(files.map(async (file) => {
      // Skip non-code files
      if (!file.name.match(/\.(tsx?|jsx?)$/)) {
        fileResults[file.name] = { isValid: true };
        return;
      }
      
      try {
        // Validate the file content
        const validationResult = await this.quickValidate(file.content);
        fileResults[file.name] = validationResult;
        
        if (!validationResult.isValid) {
          hasErrors = true;
        }
        
        // Check for Expo-specific issues
        const expoIssues = this.checkExpoSpecificIssues(file.name, file.content);
        
        // Merge Expo-specific issues with TypeScript validation results
        if (expoIssues.length > 0) {
          hasErrors = true;
          fileResults[file.name] = {
            isValid: false,
            errors: [
              ...(fileResults[file.name].errors || []),
              ...expoIssues
            ]
          };
        }
      } catch (error: any) {
        hasErrors = true;
        fileResults[file.name] = {
          isValid: false,
          errors: [{
            line: 0,
            message: `Failed to validate: ${error.message}`,
            severity: 'error'
          }]
        };
      }
    }));
    
    // Generate a summary of all validation issues
    const summary = this.generateValidationSummary(fileResults);
    
    return {
      isValid: !hasErrors,
      fileResults,
      summary
    };
  }
  
  /**
   * Checks for common Expo-specific issues in code
   * @param fileName Name of the file being checked
   * @param content Content of the file
   * @returns Array of validation errors specific to Expo
   */
  private static checkExpoSpecificIssues(fileName: string, content: string): Array<{
    line: number;
    message: string;
    severity: 'error' | 'warning';
  }> {
    const issues: Array<{
      line: number;
      message: string;
      severity: 'error' | 'warning';
    }> = [];
    
    // Check for common Expo issues by looking for patterns in the code
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // Check for direct DOM manipulation (not supported in React Native)
      if (line.includes('document.') || line.includes('window.')) {
        issues.push({
          line: index + 1,
          message: 'Direct DOM manipulation is not supported in React Native/Expo',
          severity: 'error'
        });
      }
      
      // Check for non-Expo navigation libraries
      if (line.includes('react-router') && !line.includes('//')) {
        issues.push({
          line: index + 1,
          message: 'react-router is not recommended for Expo apps, use react-navigation instead',
          severity: 'warning'
        });
      }
      
      // Check for web-specific APIs without platform checks
      if ((line.includes('localStorage') || line.includes('sessionStorage')) && 
          !line.includes('Platform.OS') && !line.includes('//')) {
        issues.push({
          line: index + 1,
          message: 'Web storage APIs require platform checks or alternatives like AsyncStorage',
          severity: 'error'
        });
      }
      
      // Check for incorrect style properties
      if (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
        // Check for web-only CSS properties in style objects
        const webOnlyProps = ['float', 'cursor', 'pointerEvents', 'userSelect'];
        webOnlyProps.forEach(prop => {
          if (line.includes(`${prop}:`) && !line.includes('Platform.OS') && !line.includes('//')) {
            issues.push({
              line: index + 1,
              message: `'${prop}' is a web-only style property and may not work on all platforms`,
              severity: 'warning'
            });
          }
        });
      }
    });
    
    // Check for missing Expo imports in app entry points
    if (fileName === 'App.tsx' || fileName === 'App.jsx') {
      if (!content.includes('expo') && !content.includes('Expo')) {
        issues.push({
          line: 1,
          message: 'App entry point may be missing Expo imports or configuration',
          severity: 'warning'
        });
      }
    }
    
    return issues;
  }
  
  /**
   * Generates a human-readable summary of validation issues
   * @param fileResults Validation results for each file
   * @returns Summary string
   */
  private static generateValidationSummary(fileResults: Record<string, ValidationResult>): string {
    const fileNames = Object.keys(fileResults);
    const errorFiles = fileNames.filter(name => !fileResults[name].isValid);
    
    if (errorFiles.length === 0) {
      return 'All files passed validation.';
    }
    
    let summary = `Found issues in ${errorFiles.length} file(s):\n\n`;
    
    errorFiles.forEach(fileName => {
      const result = fileResults[fileName];
      summary += `${fileName}:\n`;
      
      if (result.errors && result.errors.length > 0) {
        result.errors.forEach(error => {
          summary += `  - Line ${error.line}: ${error.severity.toUpperCase()}: ${error.message}\n`;
        });
      }
      
      summary += '\n';
    });
    
    return summary;
  }
}
