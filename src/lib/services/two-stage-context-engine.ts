import { z } from "zod";
import { generateObject } from "ai";
import { customModel } from "@/lib/ai";
import { FileItem } from "@/types/file";
import { CodeSnippet } from "./context-engine";
import * as path from "path";
import {orderBy} from "lodash";

/**
 * Interface for snippet identification result from the second LLM
 */
interface SnippetIdentification {
  fileName: string;
  startLine: number;
  endLine: number;
  snippetType: string;
  snippetName: string;
  relevanceScore: number;
  reasoning: string;
}

/**
 * Two-stage LLM approach for context retrieval
 * 1. First LLM identifies relevant files
 * 2. Second LLM identifies relevant snippets within those files
 */
export class TwoStageLLMContextEngine {
  private files: FileItem[] = [];
  private fileIndex: Map<string, FileItem> = new Map();

  /**
   * Initialize with project files
   */
  constructor(files: FileItem[]) {
    this.files = files;
    this.buildIndex();
  }

  /**
   * Build an index of files for quick lookup
   */
  private buildIndex(): void {
    for (const file of this.files) {
      this.fileIndex.set(file.name, file);
    }
  }

  /**
   * Get a minimal structural representation of the codebase
   * This provides enough context for the first LLM while keeping token usage low
   */
  private getCodebaseStructure(): string {
    // Build a map of imports for quick lookup
    const importMap = new Map<string, Set<string>>();

    // First pass: extract imports from all files
    this.files
      .filter(file => this.isCodeFile(file.name))
      .forEach(file => {
        const imports = this.extractImports(file.content || "");
        imports.forEach(importPath => {
          // Normalize import path
          if (importPath.startsWith('@/')) {
            importPath = importPath.substring(2);
          }

          // Add to the import map
          if (!importMap.has(importPath)) {
            importMap.set(importPath, new Set());
          }
          importMap.get(importPath)?.add(file.name);
        });
      });

    // Second pass: create the structure with import relationships
    return this.files
      .filter(file => this.isCodeFile(file.name))
      .map(file => {
        const fileType = this.determineFileType(file.name, file.content || "");
        const exports = this.extractExports(file.content || "");

        // Find files that import this file (limited to 3 for brevity)
        const importedBy = Array.from(importMap.get(file.name) || []).slice(0, 3);

        let result = `${file.name} - ${fileType}`;

        if (exports.length > 0) {
          result += ` - Exports: ${exports.join(", ")}`;
        }

        if (importedBy.length > 0) {
          result += ` - Used by: ${importedBy.join(", ")}`;
        }

        return result;
      })
      .join("\n");
  }

  /**
   * First stage: Find relevant files for a query
   */
  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{files: string[], reasoning: string }> {
    console.time('find-relevant-files');

    // Get a compact representation of the codebase structure
    const codebaseStructure = this.getCodebaseStructure();

    // Use LLM to identify relevant files
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-nano'), // Using a smaller model to reduce costs
      temperature: 0.1,
      schema: z.object({
        files: z.array(z.string()),
        reasoning: z.string().describe("Explanation of why these files were selected")
      }),
      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.

You will be given:
1. A query about the codebase
2. A structured representation of files in the project with their types and exports

NOTES:
1. REQUIREMENTS.md is like the README.md for the system, include it in instances even if it is not directly asked for but may contain the relevant details about the query
2. MINI_MISSION.md: A single session mission file similar to a EPIC/GROUP of user stories. Include if relevant and not even asked for.
3. LEARNINGS.md: Direct personalized learnings of the current project, specifically the user's preferences etc.

Return a JSON object with:
1. An array of the most relevant file paths (maximum 10)
2. Your reasoning for selecting these files

Choose files that would be most helpful for understanding or implementing the query.`,
      prompt: `Query: ${query}
      Reason: ${reason}

Files in the project:
${codebaseStructure}

${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\n\n` : ''}
Return the most relevant file paths (maximum 10) and your reasoning:`,
    });

    console.timeEnd('find-relevant-files');
    console.log(`Selected files reasoning: ${result.object.reasoning}`);

    // Filter out any files that don't exist in our index
    return {
      files: result.object.files.filter(file => this.fileIndex.has(file)),
      reasoning: result.object.reasoning
    };
  }

  /**
   * Second stage: Identify relevant snippets within files
   */
  async identifyRelevantSnippets(query: string, reason: string, relevantFiles: string[], reasoning: string): Promise<SnippetIdentification[]> {
    console.time('identify-snippets');


    // Prepare file contents with line numbers
    const fileContents = relevantFiles.map(fileName => {
      const file = this.fileIndex.get(fileName);
      const content = file?.content || "";

      // Add line numbers to help the LLM identify specific ranges
      // Format with consistent padding to make line numbers stand out
      const lines = content.split("\n");
      const maxLineNumberWidth = String(lines.length).length;
      const numberedContent = lines.map((line, i) => {
        const lineNumber = i + 1;
        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');
        return `${paddedLineNumber}: ${line}`;
      }).join("\n");

      return {
        name: fileName,
        content: numberedContent,
        lineCount: lines.length
      };
    });

    // Use LLM to identify relevant snippets within these files
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-mini'), // Using a more capable model for this task
      temperature: 0.1,
      schema: z.object({
        snippets: z.array(z.object({
          fileName: z.string(),
          startLine: z.number(),
          endLine: z.number(),
          snippetType: z.string(),
          snippetName: z.string(),
          relevanceScore: z.number().min(0).max(1),
          reasoning: z.string()
        }))
      }),
      system: `You are a code analysis expert. Your task is to identify the most relevant code snippets within files for a specific query.

For each file, identify specific code blocks (functions, components, types, etc.) that are most relevant to the query.
Return the exact line numbers for each snippet, along with metadata about the snippet.
When asked for the contents of a complete file, please return the entire file and not broken down by snippets. 

Guidelines:
1. Each snippet should be a complete logical unit (function, component, class, type definition) but two snippets should NEVER overlap. ALWAYS please send the least comprehensive snippet.
2. Include enough context to understand the snippet (don't cut off in the middle of a block)
3. Assign a relevance score from 0.0 to 1.0 based on how directly the snippet addresses the query
4. Provide a brief reasoning for why each snippet is relevant
5. Prefer larger, more complete snippets over tiny fragments
6. Include related code that provides necessary context
7. Pay close attention to the line numbers at the beginning of each line (formatted as "NUMBER: code")
8. Make sure your startLine and endLine values correspond exactly to these line numbers
9. When identifying a code block, include its entire definition from start to end
10. For functions/methods, include the entire function body including closing braces
11. When asked for the contents of a complete file, please return the entire file and not broken down by snippets
12. When asked for multiple complete files, please send only 2-3 of the files and never all of them
13. Try to send a maximum of 1000 lines across all the files to reduce overall context stuffing. But override if its relevant and the query is specific.

For reliable code editing:
14. Always include import statements at the top of the file when they're relevant to the snippet
15. Include style definitions when they're referenced by the component
16. For React components, include the entire component definition including props interface/type
17. For hooks, include both the hook definition and any related types/interfaces
18. When a file has multiple related functions/components, include all of them as separate snippets
19. For context providers, include both the context creation and the provider component

1. REQUIREMENTS.md is like the README.md for the system, include it in instances even if it is not directly asked for but may contain the relevant details about the query
2. MINI_MISSION.md: A single session mission file similar to a EPIC/GROUP of user stories. Include if relevant and not even asked for
3. LEARNINGS.md: Direct personalized learnings of the current project, specifically the user's preferences etc

These markdown files are large and hence should be filtered to return what is relevant even if not explicitly asked for. Its a judgement call. Balance between context stuffing and focussing on the right areas/code/feature.
`,
      prompt: `Query: ${query}

I need to identify the most relevant code snippets in these files. For each relevant code block, provide:
1. The file name
2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line
3. Type of snippet (function, component, hook, type, etc.)
4. Name of the snippet (function name, component name, etc.)
5. Relevance score (0.0 to 1.0)
6. Brief reasoning for why this snippet is relevant
7. Check from REQUIREMENTS.md, MINI_MISSION.md and LEARNINGS.md if there's anything relevant. Include surrounding requirements that are not complete to help concentrate

IMPORTANT GUIDELINES FOR RELIABLE CODE EDITING:
- Each line in the files below is prefixed with its line number (e.g., "42: const foo = bar;"). Use these exact line numbers in your response.
- Always include import statements at the top of files when they're relevant to the code
- Include style definitions and type definitions that are referenced by components
- For components, include the entire component including props interface/type
- When a file has related functions/components, include all of them as separate snippets
- Include both context creation and provider components for context files
- When asked for the contents of a complete file, please return the entire file and not broken down by snippets

Reasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:
${reason}

Reasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:
${reasoning}

Files to analyze:
${fileContents.map(file => `
=== ${file.name} (${file.lineCount} lines) ===
${file.content}
`).join("\n\n")}

Return an array of the most relevant code snippets with their exact line numbers and metadata:`,
    });

    console.timeEnd('identify-snippets');
    return result.object.snippets;
  }

  /**
   * Extract actual code snippets based on line numbers
   */
  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {
    // Group snippets by file to avoid duplicate processing
    const snippetsByFile = new Map<string, SnippetIdentification[]>();

    for (const snippet of snippetIdentifications) {
      if (!snippetsByFile.has(snippet.fileName)) {
        snippetsByFile.set(snippet.fileName, []);
      }
      snippetsByFile.get(snippet.fileName)?.push(snippet);
    }

    const results: CodeSnippet[] = [];

    // Process each file's snippets
    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {
      const file = this.fileIndex.get(fileName);
      if (!file || !file.content) continue;

      const lines = file.content.split("\n");

      // Find import statements (usually at the top of the file)
      const importEndLine = this.findImportEndLine(lines);
      const hasImports = importEndLine > 0;

      // Process each snippet in the file
      for (const identification of fileSnippets) {
        // Ensure line numbers are within bounds
        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));
        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));

        // Removing as this is causing the llm issues to understand
        const shouldIncludeImports = false;

        // Determine if we should include imports
        // const shouldIncludeImports = hasImports &&
        //     identification.snippetType.toLowerCase() !== 'import' &&
        //     startLine > importEndLine;

        // Extract the snippet content with imports if needed
        let snippetLines;
        let actualStartLine;

        if (shouldIncludeImports) {
          // Include imports and the actual snippet
          const importLines = lines.slice(0, importEndLine);
          const codeLines = lines.slice(startLine - 1, endLine);

          // Add a separator between imports and code
          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];
          actualStartLine = 1; // Starting from the beginning of the file
        } else {
          // Just include the snippet itself
          snippetLines = lines.slice(startLine - 1, endLine);
          actualStartLine = startLine;
        }

        const content = snippetLines.join("\n");

        // Log the extraction for debugging
        console.log(`Extracting snippet from ${identification.fileName}:`);
        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);
        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);
        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);
        if (shouldIncludeImports) {
          console.log(`  Including imports from lines 1-${importEndLine}`);
        }

        results.push({
          filePath: identification.fileName,
          content,
          startLine: actualStartLine,
          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines
          type: this.mapSnippetType(identification.snippetType),
          symbols: [identification.snippetName],
          score: identification.relevanceScore,
          context: identification.reasoning,
          includesImports: shouldIncludeImports
        } as CodeSnippet);
      }
    }

    return orderBy(results, ['score'], ['desc']);
  }

  /**
   * Find the line where imports end in a file
   */
  private findImportEndLine(lines: string[]): number {
    let lastImportLine = 0;

    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines
      const line = lines[i].trim();
      if (line.startsWith('import ')) {
        lastImportLine = i + 1; // +1 because line numbers are 1-based
      }
    }

    return lastImportLine;
  }

  /**
   * Main method to get relevant snippets for a query
   */
  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<CodeSnippet[]> {
    // Stage 1: Find relevant files
    const {files: relevantFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);

    if (relevantFiles.length === 0) {
      console.log("No relevant files found");
      return [];
    }

    // Stage 2: Identify relevant snippets within those files
    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, relevantFiles, reasoning);

    // Stage 3: Extract the actual snippets
    return this.extractSnippets(snippetIdentifications);
  }

  /**
   * Helper methods
   */
  private isCodeFile(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase();
    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);
  }

  private determineFileType(fileName: string, content: string): string {
    const name = fileName.toLowerCase();

    if (name.includes('screen') || name.includes('page')) {
      return 'screen';
    }

    if (name.includes('context')) {
      return 'context';
    }

    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {
      return 'hook';
    }

    if (name.includes('util') || name.includes('helper')) {
      return 'util';
    }

    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {
      return 'type';
    }

    if (name.includes('config') || name.includes('setup')) {
      return 'config';
    }

    // Default to component for TSX/JSX files
    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {
      return 'component';
    }

    return 'unknown';
  }

  private extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(const|function|class|interface|type|default)\s+(\w+)/g;

    let match;
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[2]);
    }

    return exports;
  }

  private extractImports(content: string): string[] {
    const imports: string[] = [];
    const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([@\w\/.\-]+)['"];?/g;

    let match;
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      imports.push(importPath);
    }

    return imports;
  }

  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {
    const normalizedType = type.toLowerCase();

    if (normalizedType.includes('component')) return 'component';
    if (normalizedType.includes('hook')) return 'hook';
    if (normalizedType.includes('screen')) return 'screen';
    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';
    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';
    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';
    if (normalizedType.includes('config')) return 'config';

    return 'unknown';
  }
}
