import { createClient, PhotosWithTotalResults, Videos } from 'pexels';

const PEXELS_API_KEY = process.env.PEXELS_API_KEY || 'YOUR_API_KEY';

export class LLMMediaService {
  private client: ReturnType<typeof createClient>;
  private static instance: LLMMediaService;

  private constructor() {
    this.client = createClient(PEXELS_API_KEY);
  }

  static getInstance(): LLMMediaService {
    if (!LLMMediaService.instance) {
      LLMMediaService.instance = new LLMMediaService();
    }
    return LLMMediaService.instance;
  }

  async searchImage(query: string): Promise<string> {
    try {
      const response = await this.client.photos.search({
        query,
        per_page: 1,
        orientation: 'portrait',
      }) as PhotosWithTotalResults;

      if (response.photos.length === 0) {
        throw new Error('No images found');
      }

      return response.photos[0].src.large2x;
    } catch (error) {
      console.error('Error fetching image:', error);
      // Return a placeholder URL that represents where an image should go
      return 'https://placehold.co/600x400/e2e8f0/475569?text=Image+Placeholder';
    }
  }

  async searchVideo(query: string): Promise<string> {
    try {
      const response = await this.client.videos.search({
        query,
        per_page: 1,
        orientation: 'portrait'
      }) as Videos;

      if (response.videos.length === 0) {
        throw new Error('No videos found');
      }

      // Get the video file with closest resolution to 720p
      const video = response.videos[0];
      const videoFile = video.video_files
        .sort((a , b) => Math.abs(720 - (a?.height || 0)) - Math.abs(720 - (b?.height || 0)))[0];

      return videoFile.link;
    } catch (error) {
      console.error('Error fetching video:', error);
      // Return a placeholder URL
      return 'https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/720/Big_Buck_Bunny_720_10s_1MB.mp4';
    }
  }

  // Format for LLM to understand how to use the service
  static getLLMInstructions(): string {
    const baseUrl = 'https://magically.life';
    
    return `
To add media to your React Native components, use our Media API:

1. For Images:
   Import the Image component:
   \`\`\`typescript
   import { Image } from 'react-native';
   \`\`\`

   Use the Media API directly:
   \`\`\`typescript
   <Image 
     source={{ uri: '${baseUrl}/api/media/image?query=' + encodeURIComponent('description of the image you want') }}
     style={{ width: 200, height: 200 }}
   />
   \`\`\`

2. For Videos:
   Import the Video component:
   \`\`\`typescript
   import { Video } from 'expo-av';
   \`\`\`

   Use the Media API directly:
   \`\`\`typescript
   <Video
     source={{ uri: '${baseUrl}/api/media/video?query=' + encodeURIComponent('description of the video you want') }}
     useNativeControls
     resizeMode="contain"
     style={{ width: '100%', height: 200 }}
     videoStyle={{flex: 1}}
   />
   \`\`\`
   
Example:
\`\`\`typescript
<Image 
  source={{ uri: '${baseUrl}/api/media/image?query=' + encodeURIComponent('modern office workspace with laptop') }}
  style={{ width: '100%', height: 200 }}
/>
\`\`\`

3. For Audio:
   Import the Audio component:
   \`\`\`typescript
   import { Audio } from 'expo-av';
   \`\`\`
   
   Use the Audio example from Soundhelix:
   \`\`\`typescript
    const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3' },
        { shouldPlay: true },
        onPlaybackStatusUpdate
      );
   \`\`\`
   
   Available urls (Helix 1 to 17):
      \`\`\`text
      ${Array.from({ length: 17 }, (_, i) => `https://www.soundhelix.com/examples/mp3/SoundHelix-Song-${i + 1}.mp3`).join('\n')}
      \`\`\`

The API will return a real image URL from Pexels that matches your description.

You can also prefetch multiple media items at once to improve performance:
\`\`\`typescript
// In your component's useEffect or initialization
fetch('${baseUrl}/api/media/prefetch', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    images: ['modern office workspace', 'person using smartphone'],
    videos: ['typing on keyboard']
  })
});
\`\`\`

Guidelines:
1. Provide clear, specific descriptions
2. Use appropriate dimensions
3. Add proper error handling in your components
4. Consider adding loading states
5. DO NOT use images where logos should be present
6. Use prefetching for better performance
   `;
  }

  static generateInstructionsForDesignPreview() {
    const baseUrl = 'https://magically.life';

    return `
    To add media to your html tags, use our Media API:
    1. For Images:
    
    URL: ${baseUrl}/api/media/image?query=encodeURIComponent('description of the image you want')
    
    2. For Videos:
    URL: ${baseUrl}/api/media/video?query=encodeURIComponent('description of the video you want')
    When using video, ensure you ALWAYS use videoStyle={{flex: 1}}.
    
    3. For Audio:
    Use one of these urls:
    ${
      Array.from({ length: 17 }, (_, i) => `https://www.soundhelix.com/examples/mp3/SoundHelix-Song-${i + 1}.mp3`).join('\n')
    }
`
  }
}

export const llmMediaService = LLMMediaService.getInstance();
