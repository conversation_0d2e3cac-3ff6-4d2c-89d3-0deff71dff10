import { z } from "zod";
import { generateObject } from "ai";
import { customModel } from "@/lib/ai";
import { Message } from "ai";
import { FileItem } from "@/types/file";
import { CodeSnippet } from "./context-engine";

/**
 * Interface for code change analysis result
 */
export interface CodeChangeAnalysis {
  summary: string;
  changedFiles: CodeFileChange[];
  potentialIssues: PotentialIssue[];
  userFriendlyDescription: string;
  recommendedAction: 'continue' | 'redo' | 'fix';
  confidence: 'high' | 'medium' | 'low';
}

/**
 * Interface for individual file changes
 */
export interface CodeFileChange {
  filePath: string;
  changeType: 'created' | 'modified' | 'deleted';
  description: string;
  functionalityImpact: 'none' | 'minor' | 'significant' | 'breaking';
  linesChanged: number;
}

/**
 * Interface for potential issues detected in the changes
 */
export interface PotentialIssue {
  description: string;
  severity: 'low' | 'medium' | 'high';
  filesPaths: string[];
  suggestedFix?: string;
}

/**
 * Schema for the code change analysis
 */
const CodeChangeAnalysisSchema = z.object({
  summary: z.string().describe("A concise technical summary of the changes made"),
  changedFiles: z.array(z.object({
    filePath: z.string().describe("Path to the changed file"),
    changeType: z.enum(['created', 'modified', 'deleted']).describe("Type of change made to the file"),
    description: z.string().describe("Description of what changed in the file"),
    functionalityImpact: z.enum(['none', 'minor', 'significant', 'breaking']).describe("Impact on existing functionality"),
    linesChanged: z.number().describe("Approximate number of lines changed")
  })).describe("List of files that were changed"),
  potentialIssues: z.array(z.object({
    description: z.string().describe("Description of the potential issue"),
    severity: z.enum(['low', 'medium', 'high']).describe("Severity of the issue"),
    filesPaths: z.array(z.string()).describe("Files affected by this issue"),
    suggestedFix: z.string().optional().describe("Suggested fix for the issue")
  })).describe("Potential issues detected in the changes"),
  userFriendlyDescription: z.string().describe("A non-technical description of the changes for the user"),
  recommendedAction: z.enum(['continue', 'redo', 'fix']).describe("Recommended action for the user"),
  confidence: z.enum(['high', 'medium', 'low']).describe("Confidence in the analysis")
});

/**
 * Post-stream analysis service for analyzing code changes and providing user-friendly feedback
 */
export class PostStreamAnalysisService {
  private previousFiles: Map<string, FileItem> = new Map();
  private currentFiles: Map<string, FileItem> = new Map();
  
  /**
   * Initialize with the current state of files
   */
  constructor(files: FileItem[]) {
    this.updateCurrentFiles(files);
  }
  
  /**
   * Update the current files
   * This should be called after each stream completion to update the baseline
   */
  public updateCurrentFiles(files: FileItem[]): void {
    // Store current files as previous
    this.previousFiles = new Map(this.currentFiles);
    
    // Update current files
    this.currentFiles = new Map();
    for (const file of files) {
      this.currentFiles.set(file.name, file);
    }
  }
  
  /**
   * Analyze changes between previous and current file states
   */
  public async analyzeChanges(
    latestMessage: Message, 
    conversationContext: Message[]
  ): Promise<CodeChangeAnalysis> {
    console.time('analyze-changes');
    
    // Generate diff information
    const diffs = this.generateDiffs();
    
    // Extract the user's original request from conversation context
    const userRequest = this.extractUserRequest(conversationContext);
    
    // Use LLM to analyze the changes
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-turbo'), // Using a more capable model for analysis
      temperature: 0.1,
      schema: CodeChangeAnalysisSchema,
      system: `You are a code analysis expert specializing in React Native Expo applications. 
Your task is to analyze code changes made by an AI assistant in response to a user request.

You will be given:
1. The original user request
2. The AI's response message
3. A list of file changes (created, modified, deleted files with diffs)

Your goal is to:
1. Summarize the changes in both technical and non-technical terms
2. Identify any potential issues or unintended modifications
3. Recommend whether the user should continue with these changes, redo them, or fix specific issues
4. Provide this information in a structured format that can be presented to non-technical users

Be especially vigilant about:
- Changes that don't align with the user's request
- Modifications to core functionality that weren't requested
- Breaking changes to existing features
- Security vulnerabilities or bad practices
- Incomplete implementations

For non-technical users, focus on explaining what the changes accomplish in business terms,
not how they work technically. Use simple language and avoid jargon.`,
      prompt: `User Request: ${userRequest}

AI Response: ${latestMessage.content}

File Changes:
${diffs}

Based on the above information, analyze the changes and provide a structured assessment.
Focus on whether the changes correctly implement what the user requested without breaking existing functionality.
Your analysis should help a non-technical user decide whether to accept the changes, redo them, or fix specific issues.`,
    });
    
    console.timeEnd('analyze-changes');
    return result.object;
  }
  
  /**
   * Generate diff information between previous and current files
   */
  private generateDiffs(): string {
    const diffs: string[] = [];
    
    // Check for created files
    for (const [path, file] of this.currentFiles.entries()) {
      if (!this.previousFiles.has(path)) {
        diffs.push(`NEW FILE: ${path}\nContent:\n${this.truncateContent(file.content || '')}`);
      }
    }
    
    // Check for modified files
    for (const [path, currentFile] of this.currentFiles.entries()) {
      const previousFile = this.previousFiles.get(path);
      if (previousFile && previousFile.content !== currentFile.content) {
        diffs.push(`MODIFIED: ${path}\nDiff:\n${this.generateSimpleDiff(
          previousFile.content || '', 
          currentFile.content || ''
        )}`);
      }
    }
    
    // Check for deleted files
    for (const [path, file] of this.previousFiles.entries()) {
      if (!this.currentFiles.has(path)) {
        diffs.push(`DELETED: ${path}\nPrevious content:\n${this.truncateContent(file.content || '')}`);
      }
    }
    
    return diffs.join('\n\n');
  }
  
  /**
   * Generate a simple diff between two file contents
   */
  private generateSimpleDiff(oldContent: string, newContent: string): string {
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    
    // For simplicity, we'll just show a few lines before and after with indicators
    // A more sophisticated diff algorithm could be implemented here
    const maxLines = 10; // Maximum number of lines to show in the diff
    
    if (oldLines.length <= maxLines && newLines.length <= maxLines) {
      return `OLD:\n${oldContent}\n\nNEW:\n${newContent}`;
    }
    
    // Find the first different line
    let firstDiffIndex = 0;
    const minLength = Math.min(oldLines.length, newLines.length);
    
    while (firstDiffIndex < minLength && oldLines[firstDiffIndex] === newLines[firstDiffIndex]) {
      firstDiffIndex++;
    }
    
    // Find the last different line, working backwards
    let oldLastDiffIndex = oldLines.length - 1;
    let newLastDiffIndex = newLines.length - 1;
    
    while (
      oldLastDiffIndex > firstDiffIndex && 
      newLastDiffIndex > firstDiffIndex && 
      oldLines[oldLastDiffIndex] === newLines[newLastDiffIndex]
    ) {
      oldLastDiffIndex--;
      newLastDiffIndex--;
    }
    
    // Create context for the diff
    const contextBefore = Math.max(0, firstDiffIndex - 3); // 3 lines of context before
    const oldContextAfter = Math.min(oldLines.length, oldLastDiffIndex + 3); // 3 lines of context after
    const newContextAfter = Math.min(newLines.length, newLastDiffIndex + 3);
    
    const oldDiff = oldLines.slice(contextBefore, oldContextAfter).join('\n');
    const newDiff = newLines.slice(contextBefore, newContextAfter).join('\n');
    
    return `OLD (lines ${contextBefore+1}-${oldContextAfter}):\n${oldDiff}\n\nNEW (lines ${contextBefore+1}-${newContextAfter}):\n${newDiff}`;
  }
  
  /**
   * Truncate content if it's too long
   */
  private truncateContent(content: string, maxLines = 20): string {
    const lines = content.split('\n');
    if (lines.length <= maxLines) {
      return content;
    }
    
    return lines.slice(0, maxLines).join('\n') + `\n... (${lines.length - maxLines} more lines)`;
  }
  
  /**
   * Extract the user's original request from conversation context
   */
  private extractUserRequest(conversationContext: Message[]): string {
    // Find the most recent user message before the assistant's response
    for (let i = conversationContext.length - 1; i >= 0; i--) {
      if (conversationContext[i].role === 'user') {
        return conversationContext[i].content || '';
      }
    }
    
    return 'Unknown request';
  }
}

/**
 * Create a user-friendly UI message from the analysis
 */
export function createUserFeedbackMessage(analysis: CodeChangeAnalysis): string {
  const { userFriendlyDescription, recommendedAction, potentialIssues } = analysis;
  
  let message = `## Changes Summary\n${userFriendlyDescription}\n\n`;
  
  if (potentialIssues.length > 0) {
    const highSeverityIssues = potentialIssues.filter(issue => issue.severity === 'high');
    const otherIssues = potentialIssues.filter(issue => issue.severity !== 'high');
    
    if (highSeverityIssues.length > 0) {
      message += `## ⚠️ Important Issues\n`;
      highSeverityIssues.forEach(issue => {
        message += `- ${issue.description}\n`;
      });
      message += '\n';
    }
    
    if (otherIssues.length > 0) {
      message += `## Other Considerations\n`;
      otherIssues.forEach(issue => {
        message += `- ${issue.description}\n`;
      });
      message += '\n';
    }
  }
  
  message += `## Recommendation\n`;
  switch (recommendedAction) {
    case 'continue':
      message += `✅ These changes look good! You can continue building your app.`;
      break;
    case 'fix':
      message += `🔧 These changes need some adjustments, but the overall direction is good.`;
      break;
    case 'redo':
      message += `🚫 These changes have significant issues. Consider trying a different approach.`;
      break;
  }
  
  return message;
}
