/**
 * HTML Template Service
 * Centralizes all HTML template generation and styling for design previews
 */

/**
 * Default styles for design previews
 * These styles ensure proper display of mobile frames and form elements
 */
const DEFAULT_STYLES = `
  body {
    display: flex;
    flex-direction: column;
    overflow-x: auto;
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
    align-items: center;
    justify-content: flex-start;
  }

  /* Screens container */
  .screens-container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    gap: 40px;
    margin-bottom: 20px;
    overflow-x: auto;
    padding: 20px;
    width: 100%;
  }

  /* Screen container */
  .screen-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    margin: 0;
  }

  /* Ensure mobile frames maintain aspect ratio and fit in viewport */
  .mobile-frame {
    border-radius: 36px;
    min-width: 433px;
    min-height: 882px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 6px solid #5d5d5d;
    /* Fixed aspect ratio of 433:882 (0.49093:1) */
    aspect-ratio: 433/882;
    /* Ensure the frame is 80% of viewport height */
    height: 80vh !important;
    max-height: 80vh !important;
    width: auto !important;
    margin: 0 auto !important;
    display: block !important;
  }

  .screen {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
  }

  /* Navigation between screens */
  .screen-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
  }

  .screen-nav button {
    padding: 8px 16px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
  }

  .screen-nav button:hover {
    background-color: #e0e0e0;
  }

  .screen-nav button.active {
    background-color: #007bff;
    color: white;
    border-color: #0069d9;
  }

  /* Add script to preserve form state */
  input, textarea, select {
    transition: none !important;
  }
`;

/**
 * Content Security Policy that allows necessary external resources
 */
const CONTENT_SECURITY_POLICY = `default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://unpkg.com; img-src 'self' data: blob: https: http:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://unpkg.com;`;

/**
 * Wraps HTML content in a complete HTML document with proper head elements and styles
 */
export function wrapHtmlContent(content: string): string {
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="${CONTENT_SECURITY_POLICY}">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>${DEFAULT_STYLES}</style>
</head>
<body>
  ${content}
</body>
</html>`;
}

/**
 * Adds necessary styles and scripts to an existing HTML document
 */
export function enhanceExistingHtml(html: string): string {
  // If it already has a head tag, add our styles and CSP to it
  if (html.includes('<head>')) {
    // Add CSP if not present
    if (!html.includes('Content-Security-Policy')) {
      html = html.replace('<head>', `<head>
  <meta http-equiv="Content-Security-Policy" content="${CONTENT_SECURITY_POLICY}">`);
    }
    
    // Add Tailwind if not present
    if (!html.includes('tailwindcss')) {
      html = html.replace('<head>', `<head>
  <script src="https://cdn.tailwindcss.com"></script>`);
    }
    
    // Add our styles
    html = html.replace('<head>', `<head>
  <style>${DEFAULT_STYLES}</style>`);
    
    return html;
  }
  
  // If it doesn't have a head tag, wrap it completely
  return wrapHtmlContent(html);
}

/**
 * Creates a loading HTML template
 */
export function getLoadingHtml(): string {
  return wrapHtmlContent(`
  <div class="flex flex-col items-center justify-center w-full h-full">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
    <p class="text-lg font-medium text-gray-700">Generating your design...</p>
    <p class="text-sm text-gray-500 mt-2">This may take a few moments</p>
  </div>
  `);
}

/**
 * Clean HTML content by removing any text before the first HTML tag
 * and after the last HTML tag
 */
export function cleanHtmlContent(html: string): string {
  // Trim whitespace
  let cleanedHtml = html.trim();
  
  // If the content doesn't start with an HTML tag, find the first one
  if (!cleanedHtml.startsWith('<')) {
    const firstTagIndex = cleanedHtml.indexOf('<');
    if (firstTagIndex > 0) {
      cleanedHtml = cleanedHtml.substring(firstTagIndex);
    }
  }
  
  // Try to find a proper HTML structure
  // Look for the first root element (like <div>, <section>, etc.)
  const rootElementMatch = cleanedHtml.match(/<(div|section|main|body|html|article|aside|nav|header|footer)([^>]*)>([\s\S]*?)(<\/\1>)/i);
  
  if (rootElementMatch) {
    // Extract just the root element and its contents
    const [fullMatch] = rootElementMatch;
    const startIndex = cleanedHtml.indexOf(fullMatch);
    const endIndex = startIndex + fullMatch.length;
    
    // Check if we're not already at the beginning
    if (startIndex > 0) {
      cleanedHtml = cleanedHtml.substring(startIndex);
    }
    
    // Check if there's content after the last closing tag
    if (endIndex < cleanedHtml.length) {
      // Look for any additional HTML elements after the first root
      const remainingHtml = cleanedHtml.substring(endIndex);
      const hasMoreTags = /<\/?[a-z][^>]*>/i.test(remainingHtml);
      
      if (!hasMoreTags) {
        // If no more HTML tags, truncate at the end of the first root element
        cleanedHtml = cleanedHtml.substring(0, endIndex);
      }
    }
  }
  
  return cleanedHtml;
}

/**
 * Wraps multiple screens in a complete HTML document with navigation
 */
export function wrapScreens(screens: Array<{name: string, content: string, styles?: string}>, status: string = 'complete'): string {
  const screensHtml = screens.map((screen, index) => {
    console.log('screen', screen)
    
    // First, check if the content is already wrapped in a complete HTML document
    // If so, extract just the body content
    let processedContent = screen.content;
    if (processedContent.includes('<!DOCTYPE html>') || processedContent.includes('<html')) {
      const bodyMatch = processedContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
      if (bodyMatch && bodyMatch[1]) {
        processedContent = bodyMatch[1].trim();
      }
    }
    
    // Check if content already has a mobile-frame wrapper
    const hasFrame = processedContent.includes('class="mobile-frame"') || processedContent.includes('class=\'mobile-frame\'');
    
    // Wrap content in mobile frame if it doesn't already have one
    const frameWrappedContent = hasFrame ? processedContent : `
      <div class="mobile-frame">
        <div class="screen">
          ${processedContent}
        </div>
      </div>
    `;
    
    return `
    <div class="screen-container" data-screen="${screen.name}">
      ${frameWrappedContent}
      ${screen.styles ? `<style>${screen.styles}</style>` : ''}
    </div>
    `;
  }).join('\n');

  return `<!DOCTYPE html>
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="session-status" content="${status}">
      ${status === 'generating' ? '<meta http-equiv="refresh" content="1">' : ''}
      <meta http-equiv="Content-Security-Policy" content="${CONTENT_SECURITY_POLICY}">
      <script src="https://cdn.tailwindcss.com"></script>
      <style>${DEFAULT_STYLES}</style>
      </script>
    </head>
    <body>
      <div class="screens-container">
        ${screensHtml}
      </div>
    </body>
  </html>`;
}

/**
 * Extract HTML content from a string that might contain PREVIEW_HTML tags
 */
export function extractHtmlFromContent(content: string): string | null {
  // Check if we have a complete HTML document with PREVIEW_HTML tags
  const htmlMatch = content.match(/<PREVIEW_HTML>([\s\S]*?)<\/PREVIEW_HTML>/);
  if (htmlMatch) {
    return cleanHtmlContent(htmlMatch[1]);
  }
  
  // If no PREVIEW_HTML tags, check if there's any HTML-like content
  if (content.includes('<div') || content.includes('<html') || content.includes('<body')) {
    return cleanHtmlContent(content);
  }
  
  return null;
}
