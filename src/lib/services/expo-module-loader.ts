import * as parser from '@babel/parser';
import traverse from '@babel/traverse';
import * as babel from '@babel/standalone';
import { SourceMapConsumer } from 'source-map';

/**
 * Replicates the Expo Snack runtime's module loading and error handling behavior
 * This implementation follows the exact approach used in ExpoModules.tsx
 */
export interface ModuleLoadResult {
  success: boolean;
  error?: ModuleError;
  exports?: any;
}

interface ModuleError extends Error {
  line?: number;
  column?: number;
  sourceFile?: string;
}

export interface Module {
  id: string;
  code: string;
  dependencies: string[];
}

export class ExpoModuleLoader {
  private modules: Map<string, Module> = new Map();
  private loadedModules: Map<string, any> = new Map();
  private sourceMapConsumers: Map<string, SourceMapConsumer> = new Map();
  private transformCache: Map<string, { source: string; result: any }> = new Map();
  
  /**
   * Register a module with the loader
   * @param id Module identifier
   * @param code Module source code
   */
  registerModule(id: string, code: string): void {
    // Parse dependencies
    const dependencies = this.extractDependencies(code);
    this.modules.set(id, { id, code, dependencies });
  }
  
  /**
   * Extract module dependencies from code
   * Simplified version that looks for import/require statements
   */
  private extractDependencies(code: string): string[] {
    const dependencies: string[] = [];
    
    try {
      const ast = parser.parse(code, {
        sourceType: 'module',
        plugins: ['jsx', 'typescript', 'classProperties', 'decorators-legacy'],
      });
      
      // traverse(ast, {
      //   ImportDeclaration(path) {
      //     dependencies.push(path.node.source.value);
      //   },
      //   CallExpression(path) {
      //     if (
      //       path.node.callee.type === 'Identifier' &&
      //       path.node.callee.name === 'require' &&
      //       path.node.arguments.length > 0 &&
      //       path.node.arguments[0].type === 'StringLiteral'
      //     ) {
      //       dependencies.push(path.node.arguments[0].value);
      //     }
      //   }
      // });
    } catch (e) {
      // If parsing fails, we'll catch syntax errors during transpilation
    }
    
    return dependencies;
  }
  
  /**
   * Load a module and all its dependencies
   * Replicates the exact behavior of Expo Snack runtime
   * @param entryPoint The main module to load
   */
  async loadModule(entryPoint: string): Promise<ModuleLoadResult> {
    // Clear previously loaded modules
    this.loadedModules.clear();
    
    try {
      // This follows the Expo Snack approach of loading modules
      return await this.importModule(entryPoint);
    } catch (e: any) {
      return {
        success: false,
        error: {
          message: e.message,
          line: e.line,
          column: e.column,
          sourceFile: e.sourceFile
        } as ModuleError
      };
    }
  }
  
  /**
   * Import a module and its dependencies
   * @param id Module identifier
   */
  private async importModule(id: string): Promise<ModuleLoadResult> {
    // Check if already loaded
    if (this.loadedModules.has(id)) {
      return { success: true, exports: this.loadedModules.get(id) };
    }
    
    const module = this.modules.get(id);
    if (!module) {
      throw new Error(`Unable to resolve module '${id}'`);
    }
    
    // First load dependencies (depth-first, just like in Expo Snack)
    for (const depId of module.dependencies) {
      try {
        await this.importModule(depId);
      } catch (e) {
        // Propagate the first error encountered, just like in Expo Snack
        throw e;
      }
    }
    
    // Then transpile and evaluate this module
    return await this.transpileAndEvaluate(module);
  }
  
  /**
   * Transpile and evaluate a module
   * Follows the exact pattern from ExpoModules.tsx
   */
  private async transpileAndEvaluate(module: Module): Promise<ModuleLoadResult> {
    try {
      // 1. Transpile (similar to translatePipeline in ExpoModules.tsx)
      let transformedCode: string;
      let sourceMap: any;
      
      // Check cache first
      const cached = this.transformCache.get(module.id);
      if (cached && cached.source === module.code) {
        transformedCode = cached.result.code;
        sourceMap = cached.result.map;
      } else {
        // Transpile with Babel (similar to ExpoModules.tsx)
        const result = babel.transform(module.code, {
          presets: ['react', 'typescript'],
          plugins: [
            ['@babel/plugin-transform-modules-commonjs'],
            ['@babel/plugin-transform-react-jsx'],
          ],
          sourceType: 'module',
          filename: module.id,
          sourceMaps: true,
        });
        
        if (!result || !result.code) {
          throw new Error(`Failed to transpile module '${module.id}'`);
        }
        
        transformedCode = result.code;
        sourceMap = result.map;
        
        // Cache the result
        this.transformCache.set(module.id, { 
          source: module.code, 
          result: { code: transformedCode, map: sourceMap } 
        });
        
        // Store source map consumer for error mapping
        if (sourceMap) {
          this.sourceMapConsumers.set(module.id, await new SourceMapConsumer(sourceMap));
        }
      }
      
      // 2. Evaluate (similar to evaluate in ExpoModules.tsx)
      // This is where we replicate the exact error handling behavior
      const exports = {};
      const moduleObj = { exports };
      
      // Wrap in try/catch just like in ExpoModules.tsx
      let evalException = null;
      const wrappedCode = `
        (function(module, exports, require) { 
          try { 
            ${transformedCode}
          } catch (e) { 
            evalException = e; 
          }
        })(moduleObj, exports, requireFn);
      `;
      
      // Create a require function for this module
      const requireFn = (depId: string) => {
        const result = this.loadedModules.get(depId);
        if (!result) {
          throw new Error(`Module '${depId}' not loaded but required by '${module.id}'`);
        }
        return result;
      };
      
      // Evaluate the code
      const context = {
        moduleObj,
        exports,
        requireFn,
        evalException: null
      };
      
      // Execute the code (similar to globalEvalWithSourceUrl in ExpoModules.tsx)
      // eslint-disable-next-line no-new-func
      new Function(...Object.keys(context), wrappedCode)(...Object.values(context));
      
      // Check if an exception was thrown during evaluation
      if (context.evalException) {
        const error = context.evalException as ModuleError;
        
        // Map error location using source maps if available
        if (error.line && error.column && this.sourceMapConsumers.has(module.id)) {
          const consumer = this.sourceMapConsumers.get(module.id)!;
          const original = consumer.originalPositionFor({
            line: error.line,
            column: error.column
          });
          
          if (original && original.line) {
            error.line = original.line;
            error.column = original.column;
          }
        }
        
        error.sourceFile = module.id;
        throw error;
      }
      
      // Store the exports in loaded modules
      this.loadedModules.set(module.id, moduleObj.exports);
      
      return {
        success: true,
        exports: moduleObj.exports
      };
    } catch (e: any) {
      // Exactly like in ExpoModules.tsx - propagate the first error
      throw e;
    }
  }
  
  /**
   * Validate multiple files, stopping at the first error
   * This replicates the exact behavior of Expo Snack runtime
   */
  validateFiles(files: Array<{name: string, content: string}>): {
    isValid: boolean;
    error?: ModuleError;
  } {
    // Clear previous state
    this.modules.clear();
    this.loadedModules.clear();
    this.transformCache.clear();
    
    // Register all modules
    for (const file of files) {
      this.registerModule(file.name, file.content);
    }
    
    // Find entry point (typically App.js or index.js)
    const entryPoint = files.find(f => 
      f.name === 'App.js' || f.name === 'App.tsx' || 
      f.name === 'index.js' || f.name === 'index.tsx'
    );
    
    if (!entryPoint) {
      const error = new Error('No entry point found (App.js, App.tsx, index.js, or index.tsx)') as ModuleError;
      error.sourceFile = 'unknown';
      return {
        isValid: false,
        error
      };
    }
    
    // Try to load the entry point, which will load all dependencies
    try {
      // This is synchronous for simplicity, but the real implementation is async
      this.loadModule(entryPoint.name);
      return { isValid: true };
    } catch (e: any) {
      // Return the first error encountered, exactly like Expo Snack
      const error = e as ModuleError;
      return {
        isValid: false,
        error
      };
    }
  }
}
