// import * as parser from '@babel/parser';
// import traverse from '@babel/traverse';
//
// export interface SyntaxValidationResult {
//   isValid: boolean;
//   errors?: Array<{
//     line: number;
//     column: number;
//     message: string;
//     severity: 'error' | 'warning';
//   }>;
// }
//
// export interface ExpoSpecificIssue {
//   line: number;
//   column: number;
//   message: string;
//   severity: 'error' | 'warning';
// }
//
// /**
//  * Validates syntax of JavaScript/TypeScript code without type checking
//  */
// export class SyntaxValidator {
//   /**
//    * Validates the syntax of a single file
//    * @param content File content
//    * @param fileName File name (used to determine parser plugins)
//    * @returns Validation result
//    */
//   static validateSyntax(content: string, fileName: string): SyntaxValidationResult {
//     try {
//       // Determine if this is a TypeScript file
//       const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');
//       const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');
//
//       // Configure parser plugins based on file type
//       const plugins: parser.ParserPlugin[] = [
//         'jsx',
//         'classProperties',
//         'objectRestSpread',
//         'dynamicImport',
//         'optionalChaining',
//         'nullishCoalescingOperator',
//         'decorators-legacy',
//         'classPrivateProperties',
//         'classPrivateMethods',
//         'exportDefaultFrom',
//         'doExpressions',
//         'functionBind',
//       ];
//
//       if (isTypeScript) {
//         plugins.push('typescript');
//         if (isJSX) {
//           plugins.push('jsx');
//         }
//       }
//
//       // Parse the code
//       const ast = parser.parse(content, {
//         sourceType: 'module',
//         plugins,
//         errorRecovery: true, // Try to continue parsing even after errors
//         ranges: true,
//         tokens: true,
//       });
//
//       const errors: Array<{
//         line: number;
//         column: number;
//         message: string;
//         severity: 'error' | 'warning';
//       }> = [];
//
//       // If there are syntax errors, they'll be in ast.errors
//       if (ast.errors && ast.errors.length > 0) {
//         console.log(`Found ${ast.errors.length} syntax errors in ${fileName}`);
//
//         // Add all syntax errors to our errors array
//         errors.push(...ast.errors.map(error => ({
//           line: error?.loc?.line || 0,
//           column: error?.loc?.column || 0,
//           message: error?.message,
//           severity: 'error'
//         })));
//       }
//
//       // Check for React/Expo-specific issues using AST traversal
//       try {
//         const expoIssues = this.checkExpoSpecificIssues(ast, content);
//         if (expoIssues.length > 0) {
//           console.log(`Found ${expoIssues.length} Expo-specific issues in ${fileName}`);
//           errors.push(...expoIssues);
//         }
//       } catch (traverseError: any) {
//         console.error(`Error during AST traversal for ${fileName}:`, traverseError);
//         errors.push({
//           line: 1,
//           column: 0,
//           message: `Error analyzing code: ${traverseError.message}`,
//           severity: 'error'
//         });
//       }
//
//       // Check for additional pattern-based issues
//       const patternIssues = this.checkPatternBasedIssues(content, fileName);
//       if (patternIssues.length > 0) {
//         console.log(`Found ${patternIssues.length} pattern-based issues in ${fileName}`);
//         errors.push(...patternIssues);
//       }
//
//       return {
//         isValid: errors.length === 0,
//         errors: errors.length > 0 ? errors : undefined
//       };
//     } catch (error: any) {
//       // If Babel parser throws an exception, it's definitely a syntax error
//       console.error(`Parser error in ${fileName}:`, error);
//       return {
//         isValid: false,
//         errors: [{
//           line: error.loc?.line || 0,
//           column: error.loc?.column || 0,
//           message: error.message || 'Unknown syntax error',
//           severity: 'error'
//         }]
//       };
//     }
//   }
//
//   /**
//    * Validates multiple files
//    * @param files Array of files to validate
//    * @returns Validation results for each file
//    */
//   static validateFiles(files: Array<{name: string, content: string}>): {
//     isValid: boolean;
//     fileResults: Record<string, SyntaxValidationResult>;
//     summary: string;
//   } {
//     const fileResults: Record<string, SyntaxValidationResult> = {};
//     let hasErrors = false;
//     let errorCount = 0;
//
//     console.log(`Starting validation of ${files.length} files`);
//
//     // Validate each file
//     files.forEach(file => {
//       // Skip non-code files
//       if (!file.name.match(/\.(tsx?|jsx?)$/)) {
//         fileResults[file.name] = { isValid: true };
//         console.log(`Skipping non-code file: ${file.name}`);
//         return;
//       }
//
//       try {
//         console.log(`Validating file: ${file.name}`);
//         const result = this.validateSyntax(file.content, file.name);
//         fileResults[file.name] = result;
//
//         if (!result.isValid) {
//           hasErrors = true;
//           errorCount += result.errors?.length || 0;
//           console.log(`Found ${result.errors?.length || 0} errors in ${file.name}`);
//         }
//       } catch (error: any) {
//         console.error(`Error validating ${file.name}:`, error);
//         hasErrors = true;
//         errorCount++;
//         fileResults[file.name] = {
//           isValid: false,
//           errors: [{
//             line: 0,
//             column: 0,
//             message: `Failed to validate: ${error.message || 'Unknown error'}`,
//             severity: 'error'
//           }]
//         };
//       }
//     });
//
//     // Generate a summary
//     const summary = this.generateValidationSummary(fileResults);
//
//     console.log(`Validation complete. Found errors in ${Object.keys(fileResults).filter(name => !fileResults[name].isValid).length} files with a total of ${errorCount} issues.`);
//
//     return {
//       isValid: !hasErrors,
//       fileResults,
//       summary
//     };
//   }
//
//   /**
//    * Generates a human-readable summary of validation issues
//    * @param fileResults Validation results for each file
//    * @returns Summary string
//    */
//   private static generateValidationSummary(fileResults: Record<string, SyntaxValidationResult>): string {
//     const fileNames = Object.keys(fileResults);
//     const errorFiles = fileNames.filter(name => !fileResults[name].isValid);
//
//     if (errorFiles.length === 0) {
//       return 'All files passed syntax validation.';
//     }
//
//     let summary = `Found syntax issues in ${errorFiles.length} file(s):\n\n`;
//     let totalIssues = 0;
//
//     errorFiles.forEach(fileName => {
//       const result = fileResults[fileName];
//       summary += `${fileName}:\n`;
//
//       if (result.errors && result.errors.length > 0) {
//         totalIssues += result.errors.length;
//         result.errors.forEach(error => {
//           summary += `  - Line ${error.line}: ${error.severity.toUpperCase()}: ${error.message}\n`;
//         });
//       } else {
//         summary += `  - Unknown error (no details available)\n`;
//       }
//
//       summary += '\n';
//     });
//
//     summary = `Found ${totalIssues} issues across ${errorFiles.length} file(s):\n\n` + summary;
//
//     return summary;
//   }
//
//   /**
//    * Checks for common React Native/Expo issues using AST traversal
//    * @param ast Babel AST
//    * @param sourceCode Original source code
//    * @returns Array of issues
//    */
//   private static checkExpoSpecificIssues(ast: parser.ParseResult<parser.File>, sourceCode: string): ExpoSpecificIssue[] {
//     const issues: ExpoSpecificIssue[] = [];
//
//     // Track declared variables to check for uninitialized access
//     const declaredVars = new Set<string>();
//     const initializedVars = new Set<string>();
//
//     // Track defined functions to check for undefined function calls
//     const definedFunctions = new Set<string>();
//     const calledFunctions = new Set<string>();
//
//     try {
//       traverse(ast, {
//         // Track variable declarations
//         VariableDeclarator(path) {
//           if (path.node.id.type === 'Identifier') {
//             declaredVars.add(path.node.id.name);
//
//             // Check if it's initialized
//             if (path.node.init) {
//               initializedVars.add(path.node.id.name);
//             }
//           }
//         },
//
//         // Track function declarations and expressions
//         FunctionDeclaration(path) {
//           if (path.node.id && path.node.id.type === 'Identifier') {
//             definedFunctions.add(path.node.id.name);
//           }
//         },
//
//         ArrowFunctionExpression(path) {
//           if (path.parent.type === 'VariableDeclarator' &&
//               path.parent.id.type === 'Identifier') {
//             definedFunctions.add(path.parent.id.name);
//           }
//         },
//
//         FunctionExpression(path) {
//           if (path.parent.type === 'VariableDeclarator' &&
//               path.parent.id.type === 'Identifier') {
//             definedFunctions.add(path.parent.id.name);
//           }
//         },
//
//         // Check for direct DOM API usage
//         MemberExpression(path) {
//           if (
//             path.node.object.type === 'Identifier' &&
//             (path.node.object.name === 'document' || path.node.object.name === 'window')
//           ) {
//             issues.push({
//               line: path.node.loc?.start.line || 0,
//               column: path.node.loc?.start.column || 0,
//               message: `Direct DOM API usage (${path.node.object.name}) is not supported in React Native/Expo`,
//               severity: 'error'
//             });
//           }
//
//           // Check for accessing properties on potentially uninitialized variables
//           if (path.node.object.type === 'Identifier' &&
//               declaredVars.has(path.node.object.name) &&
//               !initializedVars.has(path.node.object.name)) {
//
//             issues.push({
//               line: path.node.loc?.start.line || 0,
//               column: path.node.loc?.start.column || 0,
//               message: `Accessing property on potentially uninitialized variable '${path.node.object.name}'`,
//               severity: 'error'
//             });
//           }
//         },
//
//         // Track function calls
//         CallExpression(path) {
//           // Check for React hooks called conditionally
//           if (
//             path.node.callee.type === 'Identifier' &&
//             path.node.callee.name.startsWith('use') &&
//             path.node.callee.name !== 'useMemo' &&
//             path.node.callee.name !== 'useCallback'
//           ) {
//             let parent = path.parent;
//             while (parent) {
//               // Check if hook is inside a condition, loop, or nested function
//               if (
//                 parent.type === 'IfStatement' ||
//                 parent.type === 'WhileStatement' ||
//                 parent.type === 'ForStatement' ||
//                 parent.type === 'ForInStatement' ||
//                 parent.type === 'ForOfStatement' ||
//                 parent.type === 'ConditionalExpression'
//               ) {
//                 issues.push({
//                   line: path.node.loc?.start.line || 0,
//                   column: path.node.loc?.start.column || 0,
//                   message: `React Hook "${path.node.callee.name}" may be called conditionally. React Hooks must be called at the top level of your component`,
//                   severity: 'error'
//                 });
//                 break;
//               }
//               parent = parent.parent;
//             }
//
//             // Check for React hooks called without required arguments
//             if (path.node.callee.name === 'useContext' && (!path.node.arguments || path.node.arguments.length === 0)) {
//               issues.push({
//                 line: path.node.loc?.start.line || 0,
//                 column: path.node.loc?.start.column || 0,
//                 message: 'React.useContext() called without required context parameter',
//                 severity: 'error'
//               });
//             }
//           }
//
//           // Track function calls to check for undefined functions
//           if (path.node.callee.type === 'Identifier') {
//             calledFunctions.add(path.node.callee.name);
//           }
//         },
//
//         // Check for incorrect style properties
//         ObjectProperty(path) {
//           if (
//             path.parent.type === 'ObjectExpression' &&
//             path.node.key.type === 'Identifier'
//           ) {
//             // Check for web-only CSS properties in style objects
//             const webOnlyProps = ['float', 'cursor', 'pointerEvents', 'userSelect'];
//             const propName = path.node.key.name;
//
//             if (webOnlyProps.includes(propName)) {
//               // Check if this is likely a style object
//               let isStyleObject = false;
//               let current = path.parentPath;
//
//               while (current) {
//                 if (
//                   current.node.type === 'ObjectExpression' &&
//                   current.parent.type === 'VariableDeclarator' &&
//                   current.parent.id.type === 'Identifier' &&
//                   current.parent.id.name.toLowerCase().includes('style')
//                 ) {
//                   isStyleObject = true;
//                   break;
//                 }
//
//                 if (
//                   current.node.type === 'ObjectExpression' &&
//                   current.parent.type === 'JSXAttribute' &&
//                   current.parent.name.type === 'JSXIdentifier' &&
//                   current.parent.name.name === 'style'
//                 ) {
//                   isStyleObject = true;
//                   break;
//                 }
//
//                 current = current.parentPath;
//               }
//
//               if (isStyleObject) {
//                 issues.push({
//                   line: path.node.loc?.start.line || 0,
//                   column: path.node.loc?.start.column || 0,
//                   message: `'${propName}' is a web-only style property and may not work on all platforms`,
//                   severity: 'warning'
//                 });
//               }
//             }
//           }
//         }
//       });
//
//       // After traversal, check for undefined function calls
//       calledFunctions.forEach(funcName => {
//         // Skip built-in functions and React hooks
//         if (!definedFunctions.has(funcName) &&
//             !funcName.startsWith('use') &&
//             !['setTimeout', 'setInterval', 'fetch', 'console', 'JSON', 'parseInt', 'parseFloat', 'alert'].includes(funcName)) {
//
//           // Find the line where this function is called
//           const lines = sourceCode.split('\n');
//           let lineNumber = 0;
//
//           for (let i = 0; i < lines.length; i++) {
//             if (lines[i].includes(`${funcName}(`)) {
//               lineNumber = i + 1;
//               break;
//             }
//           }
//
//           issues.push({
//             line: lineNumber,
//             column: 0,
//             message: `Reference to potentially undefined function '${funcName}'`,
//             severity: 'error'
//           });
//         }
//       });
//
//     } catch (error) {
//       // If traverse throws an error, we'll just return the issues we've found so far
//       console.error('Error during AST traversal:', error);
//     }
//
//     return issues;
//   }
//
//   /**
//    * Checks for issues using pattern matching (regex)
//    * @param content File content
//    * @param fileName File name
//    * @returns Array of issues
//    */
//   private static checkPatternBasedIssues(content: string, fileName: string): ExpoSpecificIssue[] {
//     const issues: ExpoSpecificIssue[] = [];
//     const lines = content.split('\n');
//
//     // Check for common React Native/Expo issues line by line
//     lines.forEach((line, index) => {
//       // Check for localStorage/sessionStorage without platform checks
//       if ((line.includes('localStorage') || line.includes('sessionStorage')) &&
//           !line.includes('Platform.OS') && !line.includes('//')) {
//         issues.push({
//           line: index + 1,
//           column: 0,
//           message: 'Web storage APIs require platform checks or alternatives like AsyncStorage',
//           severity: 'error'
//         });
//       }
//
//       // Check for direct DOM manipulation
//       if ((line.includes('document.') || line.includes('window.')) &&
//           !line.includes('//') && !line.includes('/*')) {
//         issues.push({
//           line: index + 1,
//           column: 0,
//           message: 'Direct DOM manipulation is not supported in React Native/Expo',
//           severity: 'error'
//         });
//       }
//
//       // Check for web-only APIs
//       const webOnlyAPIs = ['navigator.', 'history.', 'location.href'];
//       webOnlyAPIs.forEach(api => {
//         if (line.includes(api) && !line.includes('Platform.OS') &&
//             !line.includes('//') && !line.includes('/*')) {
//           issues.push({
//             line: index + 1,
//             column: 0,
//             message: `Web-only API '${api.replace('.', '')}' is not supported in React Native/Expo`,
//             severity: 'error'
//           });
//         }
//       });
//
//       // Check for incorrect style properties in style objects
//       if (line.includes('style') && line.includes('{') &&
//           (fileName.endsWith('.tsx') || fileName.endsWith('.jsx'))) {
//         const webOnlyProps = ['float:', 'cursor:', 'pointerEvents:', 'userSelect:'];
//         webOnlyProps.forEach(prop => {
//           if (line.includes(prop) && !line.includes('Platform.OS') &&
//               !line.includes('//') && !line.includes('/*')) {
//             issues.push({
//               line: index + 1,
//               column: 0,
//               message: `'${prop.replace(':', '')}' is a web-only style property and may not work on all platforms`,
//               severity: 'warning'
//             });
//           }
//         });
//       }
//     });
//
//     // Check for missing imports in app entry points
//     if (fileName === 'App.tsx' || fileName === 'App.jsx') {
//       const requiredImports = ['react-native', 'expo'];
//       requiredImports.forEach(imp => {
//         if (!content.includes(`from '${imp}'`) && !content.includes(`from "${imp}"`)) {
//           issues.push({
//             line: 1,
//             column: 0,
//             message: `Missing import from '${imp}' in App component`,
//             severity: 'warning'
//           });
//         }
//       });
//     }
//
//     return issues;
//   }
//
//   /**
//    * Attempts to fix syntax errors in code using a simple approach
//    * @param content Code content to fix
//    * @param fileName Optional file name to determine parser plugins
//    * @returns Fixed code or original code if no errors
//    */
//   static async validateAndFix(content: string, fileName: string = 'file.tsx'): Promise<string> {
//     const validationResult = this.validateSyntax(content, fileName);
//
//     // If no errors, return the original content
//     if (validationResult.isValid) {
//       return content;
//     }
//
//     // For simple syntax errors, try to fix them
//     let fixedContent = content;
//
//     if (validationResult.errors) {
//       // Sort errors by line number in descending order to avoid offset issues
//       const sortedErrors = [...validationResult.errors].sort((a, b) => b.line - a.line);
//
//       for (const error of sortedErrors) {
//         // Try to fix common syntax errors
//         fixedContent = this.applySyntaxFix(fixedContent, error);
//       }
//     }
//
//     // Validate the fixed content
//     const fixedValidation = this.validateSyntax(fixedContent, fileName);
//
//     // If we successfully fixed the errors, return the fixed content
//     if (fixedValidation.isValid) {
//       return fixedContent;
//     }
//
//     // If we couldn't fix all errors, return the original content
//     // In a real implementation, you might want to use AI to fix more complex errors
//     return content;
//   }
//
//   /**
//    * Applies a fix for a specific syntax error
//    * @param content Original code content
//    * @param error Error to fix
//    * @returns Fixed code
//    */
//   private static applySyntaxFix(content: string, error: { line: number; column: number; message: string; severity: string }): string {
//     const lines = content.split('\n');
//     const line = lines[error.line - 1] || '';
//
//     // Try to fix common syntax errors based on error message
//     if (error.message.includes('Unexpected token')) {
//       // Missing semicolon
//       if (error.message.includes("';'")) {
//         lines[error.line - 1] = line + ';';
//         return lines.join('\n');
//       }
//
//       // Missing closing parenthesis
//       if (error.message.includes("')'")) {
//         lines[error.line - 1] = line + ')';
//         return lines.join('\n');
//       }
//
//       // Missing closing brace
//       if (error.message.includes("'}'")) {
//         lines[error.line - 1] = line + '}';
//         return lines.join('\n');
//       }
//
//       // Missing closing bracket
//       if (error.message.includes("']'")) {
//         lines[error.line - 1] = line + ']';
//         return lines.join('\n');
//       }
//     }
//
//     // Missing import for React
//     if (error.message.includes('React') && error.line === 1) {
//       return 'import React from "react";\n' + content;
//     }
//
//     // Fix for DOM API usage
//     if (error.message.includes('Direct DOM API usage')) {
//       if (error.message.includes('document')) {
//         // Replace document with a comment explaining the issue
//         return content.replace(/document\./g, '/* DOM API not supported in React Native */ // document.');
//       }
//       if (error.message.includes('window')) {
//         // Replace window with a comment explaining the issue
//         return content.replace(/window\./g, '/* DOM API not supported in React Native */ // window.');
//       }
//     }
//
//     // Fix for localStorage/sessionStorage
//     if (error.message.includes('Web storage APIs')) {
//       return content
//         .replace(/localStorage\./g, '/* Use AsyncStorage instead */ // localStorage.')
//         .replace(/sessionStorage\./g, '/* Use AsyncStorage instead */ // sessionStorage.');
//     }
//
//     // If we don't know how to fix this error, return the original content
//     return content;
//   }
// }
