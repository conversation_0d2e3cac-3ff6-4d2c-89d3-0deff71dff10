import { createClient, PhotosWithTotalResults, ErrorResponse } from 'pexels';

// We should move this to env
const PEXELS_API_KEY = 'YOUR_PEXELS_API_KEY';

interface ImageSearchResult {
  url: string;
  width: number;
  height: number;
  photographer: string;
  photographerUrl: string;
  alt: string;
}

class ImageService {
  private client: ReturnType<typeof createClient>;
  private cache: Map<string, ImageSearchResult[]>;
  private static instance: ImageService;

  private constructor() {
    this.client = createClient(PEXELS_API_KEY);
    this.cache = new Map();
  }

  static getInstance(): ImageService {
    if (!ImageService.instance) {
      ImageService.instance = new ImageService();
    }
    return ImageService.instance;
  }

  private getCacheKey(query: string, options: { perPage: number }): string {
    return `${query}-${options.perPage}`;
  }

  async searchImages(
    query: string,
    options: { perPage?: number } = {}
  ): Promise<ImageSearchResult[]> {
    const perPage = options.perPage || 10;
    const cacheKey = this.getCacheKey(query, { perPage });

    // Check cache first
    const cachedResults = this.cache.get(cacheKey);
    if (cachedResults) {
      return cachedResults;
    }

    try {
      const response = await this.client.photos.search({
        query,
        per_page: perPage,
        orientation: 'portrait' // Better for mobile apps
      }) as PhotosWithTotalResults;

      const results: ImageSearchResult[] = response.photos.map(photo => ({
        url: photo.src.large, // or photo.src.medium for smaller size
        width: photo.width,
        height: photo.height,
        photographer: photo.photographer,
        photographerUrl: photo.photographer_url,
        alt: photo.alt || query
      }));

      // Cache the results
      this.cache.set(cacheKey, results);

      return results;
    } catch (error) {
      console.error('Error fetching images:', error);
      // if ((error as ErrorResponse).status === 429) {
      //   throw new Error('Rate limit exceeded. Please try again later.');
      // }
      throw new Error('Failed to fetch images');
    }
  }

  // Get a random image for a query
  async getRandomImage(query: string): Promise<ImageSearchResult> {
    const images = await this.searchImages(query, { perPage: 5 });
    if (images.length === 0) {
      throw new Error('No images found');
    }
    return images[Math.floor(Math.random() * images.length)];
  }

  // Clear cache for a specific query or all cache
  clearCache(query?: string) {
    if (query) {
      // Clear specific query cache
      for (const key of this.cache.keys()) {
        if (key.startsWith(query)) {
          this.cache.delete(key);
        }
      }
    } else {
      // Clear all cache
      this.cache.clear();
    }
  }
}

export const imageService = ImageService.getInstance();
