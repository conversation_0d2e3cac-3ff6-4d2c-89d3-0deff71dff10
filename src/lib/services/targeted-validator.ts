import * as parser from '@babel/parser';
import traverse from '@babel/traverse';
import { types as t } from '@babel/core';

export interface ValidationResult {
  isValid: boolean;
  errors?: Array<{
    line: number;
    column: number;
    message: string;
    severity: 'error' | 'warning';
  }>;
}

/**
 * A targeted validator that focuses only on specific common errors in React Native/Expo code
 */
export class TargetedValidator {
  /**
   * Validates a collection of files for specific common errors
   * @param files Array of file objects with name and content
   * @returns Validation results with errors grouped by file
   */
  static validateFiles(files: Array<{name: string, content: string}>): {
    isValid: boolean;
    fileResults: Record<string, ValidationResult>;
    summary: string;
  } {
    const fileResults: Record<string, ValidationResult> = {};
    let hasErrors = false;
    
    console.log(`Starting targeted validation of ${files.length} files`);
    
    // Process each file
    files.forEach(file => {
      // Skip non-code files
      if (!file.name.match(/\.(tsx?|jsx?)$/)) {
        fileResults[file.name] = { isValid: true };
        return;
      }
      
      try {
        // console.log(`Validating file: ${file.name}`);
        const result = this.validateFile(file.content, file.name);
        fileResults[file.name] = result;
        
        if (!result.isValid) {
          hasErrors = true;
          console.log(`Found ${result.errors?.length || 0} errors in ${file.name}`);
        }
      } catch (error: any) {
        console.error(`Error validating ${file.name}:`, error);
        hasErrors = true;
        fileResults[file.name] = {
          isValid: false,
          errors: [{
            line: 0,
            column: 0,
            message: `Failed to validate: ${error.message || 'Unknown error'}`,
            severity: 'error'
          }]
        };
      }
    });
    
    // Generate a summary
    const summary = this.generateValidationSummary(fileResults);
    
    return {
      isValid: !hasErrors,
      fileResults,
      summary
    };
  }
  
  /**
   * Validates a single file for specific common errors
   * @param content File content
   * @param fileName File name
   * @returns Validation result
   */
  private static validateFile(content: string, fileName: string): ValidationResult {
    const errors: Array<{
      line: number;
      column: number;
      message: string;
      severity: 'error' | 'warning';
    }> = [];
    
    try {
      // 1. Check for syntax errors using Babel parser
      const syntaxErrors = this.checkSyntaxErrors(content, fileName);
      errors.push(...syntaxErrors);
      
      // 2. Check for specific runtime errors
      if (syntaxErrors.length === 0) {
        // // 2. Check for import issues (only if syntax is valid)
        // const importErrors = this.checkImportIssues(content, fileName);
        // errors.push(...importErrors);
        
        // 3. Check for specific runtime errors
        const runtimeErrors = this.checkRuntimeErrors(content, fileName);
        errors.push(...runtimeErrors);
      }
      
      return {
        isValid: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error: any) {
      console.error(`Error in validateFile for ${fileName}:`, error);
      return {
        isValid: false,
        errors: [{
          line: 0,
          column: 0,
          message: `Validation error: ${error.message || 'Unknown error'}`,
          severity: 'error'
        }]
      };
    }
  }
  
  /**
   * Checks for syntax errors using Babel parser
   * @param content File content
   * @param fileName File name
   * @returns Array of syntax errors
   */
  private static checkSyntaxErrors(content: string, fileName: string): Array<{
    line: number;
    column: number;
    message: string;
    severity: 'error' | 'warning';
  }> {
    try {
      // Determine if this is a TypeScript file
      const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');
      const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');
      
      // Configure parser plugins based on file type
      const plugins: parser.ParserPlugin[] = [
        'jsx',
        'classProperties',
        'objectRestSpread',
        'dynamicImport',
        'optionalChaining',
        'nullishCoalescingOperator',
      ];
      
      if (isTypeScript) {
        plugins.push('typescript');
        if (isJSX) {
          plugins.push('jsx');
        }
      }
      
      // Parse the code
      const ast = parser.parse(content, {
        sourceType: 'module',
        plugins,
        errorRecovery: true,
      });
      
      // Return syntax errors if any
      if (ast.errors && ast.errors.length > 0) {
        return ast.errors.map((error: any) => ({
          line: error?.loc?.line || 0,
          column: error?.loc?.column || 0,
          message: error?.message || 'Unknown syntax error',
          severity: 'error'
        }));
      }
      
      return [];
    } catch (error: any) {
      // If parsing fails completely, return the error
      return [{
        line: error.loc?.line || 0,
        column: error.loc?.column || 0,
        message: error.message || 'Unknown syntax error',
        severity: 'error'
      }];
    }
  }
  
  /**
   * Checks for specific runtime errors
   * @param content File content
   * @param fileName File name
   * @returns Array of runtime errors
   */
  private static checkRuntimeErrors(content: string, fileName: string): Array<{
    line: number;
    column: number;
    message: string;
    severity: 'error' | 'warning';
  }> {
    const errors: Array<{
      line: number;
      column: number;
      message: string;
      severity: 'error' | 'warning';
    }> = [];
    
    try {
      // Parse the code again for AST traversal
      const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');
      const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');
      
      const plugins: parser.ParserPlugin[] = [
        'jsx',
        'classProperties',
        'objectRestSpread',
        'dynamicImport',
        'optionalChaining',
        'nullishCoalescingOperator',
      ];
      
      if (isTypeScript) {
        plugins.push('typescript');
        if (isJSX) {
          plugins.push('jsx');
        }
      }
      
      const ast = parser.parse(content, {
        sourceType: 'module',
        plugins,
        errorRecovery: true,
      });
      
      // Track specific issues we're looking for
      const uninitializedVarAccesses: Array<{var: string, line: number, column: number}> = [];
      const undefinedFunctionCalls: Set<string> = new Set();
      const incorrectHookCalls: Array<{hook: string, line: number, column: number}> = [];
      
      // Track declared variables and functions
      const declaredVars = new Set<string>();
      const initializedVars = new Set<string>();
      const definedFunctions = new Set<string>();
      
      // Add common React/React Native functions to avoid false positives
      const commonFunctions = [
        // React hooks
        'useState', 'useEffect', 'useContext', 'useReducer', 'useCallback', 
        'useMemo', 'useRef', 'useImperativeHandle', 'useLayoutEffect', 'useDebugValue',
        // React Native functions
        'StyleSheet.create', 'Alert.alert', 'Dimensions.get',
        // JavaScript built-ins
        'parseInt', 'parseFloat', 'JSON.parse', 'JSON.stringify', 'String', 'Number',
        'Boolean', 'Array', 'Object', 'Math.floor', 'Math.ceil', 'Math.round',
        'console.log', 'console.error', 'console.warn', 'setTimeout', 'clearTimeout',
        'setInterval', 'clearInterval', 'encodeURIComponent', 'decodeURIComponent',
        // Common React Native component props
        'onPress', 'onChangeText', 'onFocus', 'onBlur', 'onSubmitEditing',
        // State setters (from useState)
        'set', 'setLoading', 'setData', 'setError', 'setUser', 'setIsLoading',
        'setIsSubmitting', 'setErrorMessage', 'setIsLogin', 'setName', 'setEmail',
        'setPassword', 'setTitle', 'setDescription', 'setPriority', 'setCategory',
        'setAttachments', 'setAvatar', 'setFilter', 'setSelectedTask', 'setNewTaskVisible',
        'setTaskDetailVisible', 'setTasks', 'setAiSummary', 'setShowingNearbyTasks',
        'setSearchQuery', 'setSelectedLocation', 'setErrorMsg', 'setUserLocation',
        'setRegion',
        // Common callback props
        'onSave', 'onClose', 'onDelete', 'onToggleComplete'
      ];
      
      // Add common functions to defined functions set
      commonFunctions.forEach(func => definedFunctions.add(func));
      
      // Specific function names to check for (the ones you mentioned)
      const specificFunctionsToCheck = new Set(['updateTaskList']);
      
      // traverse(ast, {
      //   // Track variable declarations
      //   VariableDeclarator(path) {
      //     if (path.node.id.type === 'Identifier') {
      //       const varName = path.node.id.name;
      //       declaredVars.add(varName);
      //
      //       // Check if it's initialized
      //       if (path.node.init) {
      //         initializedVars.add(varName);
      //       }
      //     }
      //   },
      //
      //   // Track function declarations
      //   FunctionDeclaration(path) {
      //     if (path.node.id && path.node.id.type === 'Identifier') {
      //       definedFunctions.add(path.node.id.name);
      //     }
      //   },
      //
      //   // Track arrow functions
      //   ArrowFunctionExpression(path) {
      //     if (path.parent.type === 'VariableDeclarator' &&
      //         path.parent.id.type === 'Identifier') {
      //       definedFunctions.add(path.parent.id.name);
      //     }
      //   },
      //
      //   // Check for accessing properties on uninitialized variables
      //   MemberExpression(path) {
      //     if (path.node.object.type === 'Identifier') {
      //       const varName = path.node.object.name;
      //
      //       // Check if variable is declared but not initialized
      //       if (declaredVars.has(varName) && !initializedVars.has(varName)) {
      //         uninitializedVarAccesses.push({
      //           var: varName,
      //           line: path.node.loc?.start.line || 0,
      //           column: path.node.loc?.start.column || 0
      //         });
      //       }
      //     }
      //   },
      //
      //   // Check for function calls
      //   CallExpression(path) {
      //     // Check for React hooks without required arguments
      //     if (path.node.callee.type === 'Identifier' &&
      //         path.node.callee.name === 'useContext' &&
      //         (!path.node.arguments || path.node.arguments.length === 0)) {
      //
      //       incorrectHookCalls.push({
      //         hook: 'useContext',
      //         line: path.node.loc?.start.line || 0,
      //         column: path.node.loc?.start.column || 0
      //       });
      //     }
      //
      //     // Check for specific undefined function calls
      //     if (path.node.callee.type === 'Identifier') {
      //       const funcName = path.node.callee.name;
      //
      //       if (specificFunctionsToCheck.has(funcName) && !definedFunctions.has(funcName)) {
      //         undefinedFunctionCalls.add(funcName);
      //
      //         errors.push({
      //           line: path.node.loc?.start.line || 0,
      //           column: path.node.loc?.start.column || 0,
      //           message: `Reference to undefined function '${funcName}'`,
      //           severity: 'error'
      //         });
      //       }
      //     }
      //   }
      // });
      
      // Add errors for uninitialized variable accesses
      uninitializedVarAccesses.forEach(access => {
        errors.push({
          line: access.line,
          column: access.column,
          message: `Accessing property on potentially uninitialized variable '${access.var}'`,
          severity: 'error'
        });
      });
      
      // Add errors for incorrect hook calls
      incorrectHookCalls.forEach(call => {
        errors.push({
          line: call.line,
          column: call.column,
          message: `React.${call.hook}() called without required context parameter`,
          severity: 'error'
        });
      });
      
      return errors;
    } catch (error: any) {
      console.error('Error in checkRuntimeErrors:', error);
      return [{
        line: 0,
        column: 0,
        message: `Error checking for runtime issues: ${error.message || 'Unknown error'}`,
        severity: 'error'
      }];
    }
  }
  
  /**
   * Generates a human-readable summary of validation issues
   * @param fileResults Validation results for each file
   * @returns Summary string
   */
  /**
   * Checks for import issues like missing or duplicate imports
   * @param content File content
   * @param fileName File name
   * @returns Array of import-related errors
   */
  // private static checkImportIssues(content: string, fileName: string): Array<{
  //   line: number;
  //   column: number;
  //   message: string;
  //   severity: 'error' | 'warning';
  // }> {
  //   const errors: Array<{
  //     line: number;
  //     column: number;
  //     message: string;
  //     severity: 'error' | 'warning';
  //   }> = [];
  //
  //   try {
  //     // Skip non-code files
  //     if (!fileName.match(/\.(tsx?|jsx?)$/)) {
  //       return [];
  //     }
  //
  //     // Parse the code
  //     const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');
  //     const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');
  //
  //     const plugins: parser.ParserPlugin[] = [
  //       'jsx',
  //       'classProperties',
  //       'objectRestSpread',
  //       'dynamicImport',
  //       'optionalChaining',
  //       'nullishCoalescingOperator',
  //     ];
  //
  //     if (isTypeScript) {
  //       plugins.push('typescript');
  //       if (isJSX) {
  //         plugins.push('jsx');
  //       }
  //     }
  //
  //     const ast = parser.parse(content, {
  //       sourceType: 'module',
  //       plugins,
  //       errorRecovery: true,
  //     });
  //
  //     // Track imports
  //     const imports: Map<string, Array<{line: number, column: number}>> = new Map();
  //     const importSpecifiers: Map<string, Array<{name: string, line: number, column: number}>> = new Map();
  //     const requiredModules: Map<string, Array<{line: number, column: number}>> = new Map();
  //
  //     // Common React Native packages that should be imported
  //     const commonReactNativePackages = [
  //       'react',
  //       'react-native',
  //       'expo',
  //       '@expo/vector-icons',
  //       'expo-status-bar',
  //       'expo-constants',
  //       'expo-font',
  //       'expo-asset',
  //       'expo-location',
  //       'expo-image-picker',
  //       'expo-file-system',
  //       'expo-notifications',
  //       'expo-camera',
  //       'expo-sensors',
  //       'expo-updates',
  //       'expo-splash-screen',
  //       'react-navigation',
  //       '@react-navigation/native',
  //       '@react-navigation/stack',
  //       '@react-navigation/bottom-tabs',
  //       '@react-navigation/drawer',
  //       'react-native-gesture-handler',
  //       'react-native-reanimated',
  //       'react-native-screens',
  //       'react-native-safe-area-context',
  //       '@react-native-community/masked-view',
  //       'react-native-maps',
  //       'react-native-svg',
  //       'react-native-web',
  //     ];
  //
  //     // Track used packages
  //     const usedPackages = new Set<string>();
  //
  //     // Traverse the AST to find imports and requires
  //     traverse(ast, {
  //       // Track import declarations
  //       ImportDeclaration(path) {
  //         const source = path.node.source.value;
  //         const location = {
  //           line: path.node.loc?.start.line || 0,
  //           column: path.node.loc?.start.column || 0
  //         };
  //
  //         // Track the import source
  //         if (!imports.has(source)) {
  //           imports.set(source, []);
  //         }
  //         imports.get(source)!.push(location);
  //
  //         // Track imported specifiers
  //         if (!importSpecifiers.has(source)) {
  //           importSpecifiers.set(source, []);
  //         }
  //
  //         // Track default imports
  //         if (path.node.specifiers) {
  //           path.node.specifiers.forEach(specifier => {
  //             if (t.isImportSpecifier(specifier) || t.isImportDefaultSpecifier(specifier)) {
  //               const name = t.isIdentifier(specifier.local) ? specifier.local.name : '';
  //               if (name) {
  //                 importSpecifiers.get(source)!.push({
  //                   name,
  //                   line: specifier.loc?.start.line || 0,
  //                   column: specifier.loc?.start.column || 0
  //                 });
  //               }
  //             }
  //           });
  //         }
  //       },
  //
  //       // Track require calls
  //       CallExpression(path) {
  //         if (
  //           path.node.callee.type === 'Identifier' &&
  //           path.node.callee.name === 'require' &&
  //           path.node.arguments.length > 0 &&
  //           path.node.arguments[0].type === 'StringLiteral'
  //         ) {
  //           const source = path.node.arguments[0].value;
  //           const location = {
  //             line: path.node.loc?.start.line || 0,
  //             column: path.node.loc?.start.column || 0
  //           };
  //
  //           if (!requiredModules.has(source)) {
  //             requiredModules.set(source, []);
  //           }
  //           requiredModules.get(source)!.push(location);
  //         }
  //       },
  //
  //       // Track JSX elements to detect used components
  //       JSXIdentifier(path) {
  //         // Check if this is a component name (starts with uppercase)
  //         const name = path.node.name;
  //         if (name && name[0] === name[0].toUpperCase()) {
  //           usedPackages.add(name);
  //         }
  //       },
  //
  //       // Track identifiers that might be from packages
  //       Identifier(path) {
  //         const name = path.node.name;
  //
  //         // Check for common React hooks
  //         if (name.startsWith('use') && name[3] && name[3] === name[3].toUpperCase()) {
  //           usedPackages.add('react');
  //         }
  //
  //         // Check for React components
  //         if (name === 'React' || name === 'Component') {
  //           usedPackages.add('react');
  //         }
  //
  //         // Check for StyleSheet, View, Text, etc.
  //         if (['StyleSheet', 'View', 'Text', 'Image', 'TouchableOpacity', 'ScrollView', 'FlatList'].includes(name)) {
  //           usedPackages.add('react-native');
  //         }
  //       }
  //     });
  //
  //     // Check for duplicate imports
  //     imports.forEach((locations, source) => {
  //       if (locations.length > 1) {
  //         // Multiple import statements for the same module
  //         errors.push({
  //           line: locations[1].line,
  //           column: locations[1].column,
  //           message: `Duplicate import for '${source}'. Already imported at line ${locations[0].line}.`,
  //           severity: 'warning'
  //         });
  //       }
  //     });
  //
  //     // Check for duplicate requires
  //     requiredModules.forEach((locations, source) => {
  //       if (locations.length > 1) {
  //         errors.push({
  //           line: locations[1].line,
  //           column: locations[1].column,
  //           message: `Duplicate require for '${source}'. Already required at line ${locations[0].line}.`,
  //           severity: 'warning'
  //         });
  //       }
  //
  //       // Check if a module is both imported and required
  //       if (imports.has(source)) {
  //         errors.push({
  //           line: locations[0].line,
  //           column: locations[0].column,
  //           message: `Module '${source}' is both required and imported. Use consistent import style.`,
  //           severity: 'warning'
  //         });
  //       }
  //     });
  //
  //     // Check for duplicate import specifiers
  //     importSpecifiers.forEach((specifiers, source) => {
  //       const names = new Map<string, {line: number, column: number}>();
  //
  //       specifiers.forEach(spec => {
  //         if (names.has(spec.name)) {
  //           errors.push({
  //             line: spec.line,
  //             column: spec.column,
  //             message: `Duplicate import specifier '${spec.name}' from '${source}'. Already imported at line ${names.get(spec.name)!.line}.`,
  //             severity: 'warning'
  //           });
  //         } else {
  //           names.set(spec.name, { line: spec.line, column: spec.column });
  //         }
  //       });
  //     });
  //
  //     // Check for potentially missing imports based on used components/packages
  //     if (isJSX) {
  //       // For React Native files, check if React is imported
  //       if (usedPackages.has('react') && !imports.has('react') && !requiredModules.has('react')) {
  //         errors.push({
  //           line: 1,
  //           column: 0,
  //           message: "Missing import for 'react'. React must be imported for JSX to work.",
  //           severity: 'error'
  //         });
  //       }
  //
  //       // For React Native components, check if react-native is imported
  //       const reactNativeComponents = ['View', 'Text', 'StyleSheet', 'Image', 'TouchableOpacity', 'ScrollView', 'FlatList'];
  //       const hasReactNativeComponent = reactNativeComponents.some(comp => usedPackages.has(comp));
  //
  //       if (hasReactNativeComponent && !imports.has('react-native') && !requiredModules.has('react-native')) {
  //         errors.push({
  //           line: 1,
  //           column: 0,
  //           message: "Missing import for 'react-native'. Import it to use React Native components.",
  //           severity: 'error'
  //         });
  //       }
  //     }
  //
  //     return errors;
  //   } catch (error: any) {
  //     console.error('Error in checkImportIssues:', error);
  //     return [{
  //       line: 0,
  //       column: 0,
  //       message: `Error checking for import issues: ${error.message || 'Unknown error'}`,
  //       severity: 'error'
  //     }];
  //   }
  // }

  private static generateValidationSummary(fileResults: Record<string, ValidationResult>): string {
    const fileNames = Object.keys(fileResults);
    const errorFiles = fileNames.filter(name => !fileResults[name].isValid);
    
    if (errorFiles.length === 0) {
      return 'All files passed validation.';
    }
    
    let summary = `Found issues in ${errorFiles.length} file(s):\n\n`;
    let totalIssues = 0;
    
    errorFiles.forEach(fileName => {
      const result = fileResults[fileName];
      summary += `${fileName}:\n`;
      
      if (result.errors && result.errors.length > 0) {
        totalIssues += result.errors.length;
        result.errors.forEach(error => {
          summary += `  - Line ${error.line}: ${error.severity.toUpperCase()}: ${error.message}\n`;
        });
      } else {
        summary += `  - Unknown error (no details available)\n`;
      }
      
      summary += '\n';
    });
    
    summary = `Found ${totalIssues} issues across ${errorFiles.length} file(s):\n\n` + summary;
    
    return summary;
  }
}
