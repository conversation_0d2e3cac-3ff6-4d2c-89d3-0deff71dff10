import { FileItem } from '@/types/file';

const STORAGE_KEY = 'magically_files';

interface StoredFileData {
  files: FileItem[];
  timestamp: number;
  groupId: string;
}

export const fileStorage = {
  saveFiles: (files: FileItem[], groupId: string) => {
    try {
      if (typeof window === 'undefined') return;
      
      const data: StoredFileData = {
        files,
        timestamp: Date.now(),
        groupId
      };
      
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving files to localStorage:', error);
    }
  },

  getFiles: (groupId: string): FileItem[] | null => {
    try {
      if (typeof window === 'undefined') return null;
      
      const storedData = localStorage.getItem(STORAGE_KEY);
      if (!storedData) return null;

      const data: StoredFileData = JSON.parse(storedData);
      
      // Check if data is stale (older than 1 hour)
      if (Date.now() - data.timestamp > 60 * 60 * 1000) {
        localStorage.removeItem(STORAGE_KEY);
        return null;
      }

      // Check if group ID matches
      if (data.groupId !== groupId) {
        return null;
      }

      return data.files;
    } catch (error) {
      console.error('Error reading files from localStorage:', error);
      return null;
    }
  },

  clearFiles: () => {
    try {
      if (typeof window === 'undefined') return;
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing files from localStorage:', error);
    }
  }
};
