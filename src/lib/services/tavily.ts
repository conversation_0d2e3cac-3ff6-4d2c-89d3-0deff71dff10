import {tavily, TavilyClient, TavilyExtractResponse} from '@tavily/core';

interface TokenBucket {
    tokens: number;
    lastRefill: number;
}

interface RateLimits {
    requestsPerMinute: number;
    requestsPerDay: number;
}

const TAVILY_RATE_LIMITS: RateLimits = {
    requestsPerMinute: 60,    // 60 RPM
    requestsPerDay: 1000     // 1000 RPD
};

export interface TavilySearchResponse {
    answer?: string;
    query: string;
    responseTime: number;
    images: Array<any>;
    results: Array<TavilySearchResult>;
    error?: string;
}

export interface TavilySearchResult {
    url: string;
    title: string;
    content: string;
    score: number;
    rawContent?: string;
}


export class TavilyService {
    private tavilyClient!: TavilyClient;
    private apiKeyIndex: number = 0;
    private readonly rateLimits = TAVILY_RATE_LIMITS;
    private readonly refillIntervals = {
        minute: 60 * 1000,    // 1 minute in ms
        day: 24 * 60 * 60 * 1000  // 24 hours in ms
    };

    private minuteRequestBucket: TokenBucket = {
        tokens: this.rateLimits.requestsPerMinute,
        lastRefill: Date.now()
    };

    private dayRequestBucket: TokenBucket = {
        tokens: this.rateLimits.requestsPerDay,
        lastRefill: Date.now()
    };

    private tavilyKeys = [
        '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',
        '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',
        '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'
    ];

    constructor() {
        this.setTavilyClient();
    }

    private setTavilyClient(index = 0): void {
        const apiKey = this.tavilyKeys[this.apiKeyIndex + index];
        if (!apiKey) {
            throw new Error('No Tavily API keys available');
        }
        this.tavilyClient = tavily({apiKey});
    }

    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    private refillBucket(bucket: TokenBucket, maxTokens: number, interval: number): void {
        const now = Date.now();
        const timePassed = now - bucket.lastRefill;
        const cycles = Math.floor(timePassed / interval);

        if (cycles > 0) {
            bucket.tokens = Math.min(maxTokens, bucket.tokens + (maxTokens * cycles));
            bucket.lastRefill = now;
        }
    }

    private async waitForTokens(
        bucket: TokenBucket,
        tokensNeeded: number,
        maxTokens: number,
        interval: number
    ): Promise<void> {
        while (bucket.tokens < tokensNeeded) {
            this.refillBucket(bucket, maxTokens, interval);
            if (bucket.tokens < tokensNeeded) {
                await this.sleep(100); // Wait 100ms before checking again
            }
        }
        bucket.tokens -= tokensNeeded;
    }

    private async enforceRateLimit(): Promise<void> {
        // Refill and check minute-based limits
        await this.waitForTokens(
            this.minuteRequestBucket,
            1,
            this.rateLimits.requestsPerMinute,
            this.refillIntervals.minute
        );

        // Refill and check day-based limits
        await this.waitForTokens(
            this.dayRequestBucket,
            1,
            this.rateLimits.requestsPerDay,
            this.refillIntervals.day
        );
    }

    private async switchApiKey(): Promise<void> {
        // this.tavilyKeys.shift(); // Remove the current key
        this.setTavilyClient(this.apiKeyIndex + 1); // Set the new key
    }

    async search(
        query: string,
        searchDepth: 'basic' | 'advanced' = 'advanced',
        maxResults: number = 10,
        options: {
            numberOfTries: number,
            includedDomains?: string[],
            excludedDomains?: string[],
            days?: number,
            includeRawContent?: boolean,
            topic?: "general" | "news" | "finance"
        } = {
            numberOfTries: 1
        }
    ): Promise<TavilySearchResponse> {
        await this.enforceRateLimit();

        try {

            console.log('Tavily request:', query);
            const results = await this.tavilyClient.search(query, {
                searchDepth,
                maxResults,
                days: options.days,
                includeRawContent: options.includeRawContent,
                topic: options.topic,
                excludeDomains: options.excludedDomains,
                includeDomains: options.includedDomains
            });

            return results;
        } catch (error: any) {
            // Check for rate limit error and retry with a new key
            if (
                error.response?.status === 429 &&
                options?.numberOfTries < this.tavilyKeys.length
            ) {
                options.numberOfTries += 1;
                console.warn(`Rate limit exceeded. Switching API key... Attempt ${options.numberOfTries}`);
                await this.switchApiKey();
                return this.search(query, searchDepth, maxResults, options); // Retry with incremented attempt
            }

            // Log and rethrow error
            console.error('Tavily search error:', error.message, error.stack);
            throw error;
        }
    }

    async extract(url: string, options: {
        numberOfTries: number,
        extractDepth?: 'basic' | 'advanced',
        includeImages?: boolean;
    } = {
        numberOfTries: 1
    }): Promise<TavilyExtractResponse> {
        await this.enforceRateLimit();

        try {

            console.log('Tavily extract:', url);
            const results = await this.tavilyClient.extract([
                url
            ], {
                extractDepth: options?.extractDepth || 'basic',
                includeImages: options?.includeImages || true
            });

            return results;
        } catch (error: any) {
            // Check for rate limit error and retry with a new key
            if (
                error.response?.status === 429 &&
                options?.numberOfTries < this.tavilyKeys.length
            ) {
                options.numberOfTries += 1;
                console.warn(`Rate limit exceeded. Switching API key... Attempt ${options.numberOfTries}`);
                await this.switchApiKey();
                return this.extract(url, options); // Retry with incremented attempt
            }

            // Log and rethrow error
            console.error('Tavily extract error:', error.message, error.stack);
            throw error;
        }


    }

    async searchBatch(queries: string[], searchDepth: 'basic' | 'advanced' = 'advanced', maxResults: number = 10, options: {
        numberOfTries: number,
        includedDomains?: string[],
        excludedDomains?: string[],
        days?: number,
        includeRawContent?: boolean
    } = {
        numberOfTries: 1
    }): Promise<TavilySearchResponse[]> {
        const results: TavilySearchResponse[] = [];

        await Promise.all(queries.map(async query => {
            try {
                const result = await this.search(query, searchDepth, maxResults);
                results.push(result);
            } catch (error: any) {
                console.error(`Error searching for query "${query}":`, error);
                results.push({error: error.message, query} as TavilySearchResponse);
            }
        }));

        return results;
    }
}
