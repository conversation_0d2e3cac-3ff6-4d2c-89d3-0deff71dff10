import { customModel } from "@/lib/ai";
import { generateObject } from "ai";
import { z } from "zod";
import { FileItem } from "@/types/file";

/**
 * Represents a code snippet with its location and metadata
 */
export interface CodeSnippetLocation {
  fileName: string;
  startLine: number;
  endLine: number;
  snippetType: string;
  snippetName: string;
  reason: string;
}

/**
 * SnippetExtractor identifies relevant code snippets within files
 * It uses an LLM to analyze file contents and return line numbers of relevant code
 * This approach saves tokens by not returning the actual content in the LLM response
 */
export class SnippetExtractor {
  /**
   * Identify relevant code snippets within the provided files based on the query
   * @param query The user's query
   * @param files Array of files to analyze
   * @returns Array of snippet locations with line numbers
   */
  public async identifyRelevantSnippets(
    query: string,
    files: { name: string; content: string }[]
  ): Promise<CodeSnippetLocation[]> {
    try {
      console.time('identify-relevant-snippets');
      
      // Prepare file summaries to reduce token usage
      const fileSummaries = files.map(file => {
        return {
          name: file.name,
          // Count lines for reference
          lineCount: file.content.split('\n').length,
          // Extract top-level structure for context
          structure: this.extractFileStructure(file.content)
        };
      });

      // Use LLM to identify relevant snippets
      const result = await generateObject({
        model: customModel('openai/gpt-4o-mini'), // Using smaller model to reduce cost
        temperature: 0.1,
        schema: z.object({
          snippets: z.array(z.object({
            fileName: z.string(),
            startLine: z.number(),
            endLine: z.number(),
            snippetType: z.string(),
            snippetName: z.string(),
            reason: z.string()
          }))
        }),
        system: `You are a code analysis expert. Your task is to identify the most relevant code snippets within files for a specific query.
        
For each file, identify specific functions, components, classes, types, or other code blocks that are most relevant to the query.
Return ONLY the line numbers (start and end) for each relevant code snippet, not the actual code.

Guidelines:
- Focus on complete logical units (functions, classes, components)
- Include enough context to understand the snippet (e.g., include function signature and full body)
- Prioritize code that directly implements functionality related to the query
- For each snippet, provide a brief reason why it's relevant to the query
- Aim for 3-5 snippets total across all files, focusing on quality over quantity`,
        prompt: `Query: ${query}

I've identified these files as potentially relevant. For each file, I'm providing its name, line count, and a summary of its structure:

${fileSummaries.map(file => `
File: ${file.name}
Line Count: ${file.lineCount}
Structure:
${file.structure}
`).join('\n\n')}

For each relevant file, identify the specific line numbers of code snippets that are most relevant to the query.
Return the start and end line numbers, the type of snippet (function, component, class, etc.), and the name of the snippet.
`
      });

      console.timeEnd('identify-relevant-snippets');
      return result.object.snippets;
    } catch (error) {
      console.error('Error identifying relevant snippets:', error);
      return [];
    }
  }

  /**
   * Extract actual code snippets based on the identified locations
   * @param snippetLocations Array of snippet locations
   * @param files Array of files
   * @returns Array of code snippets with content
   */
  public extractSnippets(
    snippetLocations: CodeSnippetLocation[],
    files: { name: string; content: string }[]
  ): Array<CodeSnippetLocation & { content: string }> {
    return snippetLocations.map(location => {
      const file = files.find(f => f.name === location.fileName);
      if (!file) {
        return { ...location, content: '' };
      }

      // Extract the content based on line numbers
      const lines = file.content.split('\n');
      const snippetLines = lines.slice(
        Math.max(0, location.startLine - 1),
        Math.min(lines.length, location.endLine)
      );

      return {
        ...location,
        content: snippetLines.join('\n')
      };
    });
  }

  /**
   * Extract a minimal representation of file structure
   * @param content File content
   * @returns String representation of file structure
   */
  private extractFileStructure(content: string): string {
    // Extract imports
    const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+[^;]+|[^;{]*)\s+from\s+['""][^'"]+['""];?|import\s+['""][^'"]+['""];?/g;
    const imports = content.match(importRegex) || [];

    // Extract function and component declarations
    const functionRegex = /(?:export\s+)?(?:function|const)\s+(\w+)\s*(?:\([^)]*\)|\s*=\s*\([^)]*\)\s*=>)/g;
    const functions: any[] = [];
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      functions.push(match[0]);
    }

    // Extract class declarations
    const classRegex = /(?:export\s+)?class\s+(\w+)(?:\s+extends\s+[^{]+)?/g;
    const classes = content.match(classRegex) || [];

    // Extract type and interface declarations
    const typeRegex = /(?:export\s+)?(?:type|interface)\s+(\w+)(?:\s+extends\s+[^{]+)?/g;
    const types = content.match(typeRegex) || [];

    // Combine into a structured summary
    return `
Imports:
${imports.slice(0, 5).map(imp => `- ${imp}`).join('\n')}${imports.length > 5 ? '\n- ...' : ''}

Functions/Components:
${functions.slice(0, 10).map(func => `- ${func.substring(0, 100)}${func.length > 100 ? '...' : ''}`).join('\n')}${functions.length > 10 ? '\n- ...' : ''}

Classes:
${classes.map(cls => `- ${cls}`).join('\n')}

Types:
${types.slice(0, 5).map(type => `- ${type}`).join('\n')}${types.length > 5 ? '\n- ...' : ''}
`;
  }
}
