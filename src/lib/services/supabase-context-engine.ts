import {z} from "zod";
import {generateObject} from "ai";
import {customModel} from "@/lib/ai";
import {Project} from "@/lib/db/schema";
import {SupabaseIntegrationProvider} from "../integrations/supabase/SupabaseIntegrationProvider";

/**
 * Interface for Supabase resource identification result
 */
interface ResourceIdentification {
    resourceType: 'table' | 'function' | 'policy' | 'bucket' | 'dbFunction' | 'trigger' | 'secret';
    resourceName: string;
    relevanceScore: number;
    reasoning: string;
}

/**
 * Two-stage approach for Supabase resource discovery
 * 1. First stage identifies relevant resource types and names
 * 2. Second stage fetches detailed information only for those resources
 */
export class SupabaseContextEngine {
    private supabaseProvider: SupabaseIntegrationProvider;
    private project: Project;
    private resourceIndex: Record<string, string[]> = {};
    private resourceDetails: Record<string, any> = {};
    private fullResources: {
        schema: any[];
        functions: any[];
        secrets: any[];
        rlsPolicies: any[];
        dbFunctions: any[];
        triggers: any[];
        storageBuckets: any[];
    } | null = null;

    /**
     * Initialize with project
     */
    constructor(project: Project) {
        this.project = project;
        this.supabaseProvider = new SupabaseIntegrationProvider();
    }

    /**
     * Build a lightweight index of all Supabase resources
     * This is the first stage of the two-stage approach
     */
    async buildResourceIndex(): Promise<Record<string, string[]>> {
        console.time('build-resource-index');

        if (!this.project.connectionId || !this.project.supabaseProjectId) {
            throw new Error('Project is not connected to Supabase');
        }

        console.log(`[SupabaseContextEngine] Building resource index for project: ${this.project.supabaseProjectId}`);

        try {
            // Use the existing getLatestInstructionsForChat method to fetch all resources at once
            const {
                schema,
                functions,
                secrets,
                rlsPolicies,
                dbFunctions,
                triggers,
                storageBuckets
            } = await this.supabaseProvider.getLatestInstructionsForChat({
                project: this.project
            });

            // Initialize resource index with all resource types
            this.resourceIndex = {
                tables: [],
                functions: [],
                policies: [],
                buckets: [],
                dbFunctions: [],
                triggers: [],
                secrets: []
            };

            // Extract resource names for the index
            if (Array.isArray(schema)) {
                this.resourceIndex.tables = schema
                    .filter(table => table && typeof table === 'object' && 'table_name' in table)
                    .map(table => (table as any).table_name);
            }

            if (Array.isArray(functions)) {
                this.resourceIndex.functions = functions
                    .filter(func => func && typeof func === 'object' && 'name' in func)
                    .map(func => func.name);
            }

            if (Array.isArray(rlsPolicies)) {
                this.resourceIndex.policies = rlsPolicies
                    .filter(policy => policy && typeof policy === 'object' && 'table' in policy && 'policy_name' in policy)
                    .map(policy => `${(policy as any).table}:${(policy as any).policy_name}`);
            }

            if (Array.isArray(storageBuckets)) {
                this.resourceIndex.buckets = storageBuckets
                    .filter(bucket => bucket && typeof bucket === 'object' && 'name' in bucket)
                    .map(bucket => bucket.name);
            }

            if (Array.isArray(dbFunctions)) {
                this.resourceIndex.dbFunctions = dbFunctions
                    .filter(func => func && typeof func === 'object' && 'function_name' in func)
                    .map(func => (func as any).function_name);
            }

            if (Array.isArray(triggers)) {
                this.resourceIndex.triggers = triggers
                    .filter(trigger => trigger && typeof trigger === 'object' && 'trigger_name' in trigger)
                    .map(trigger => (trigger as any).trigger_name);
            }

            if (Array.isArray(secrets)) {
                this.resourceIndex.secrets = secrets
                    .filter(secret => secret && typeof secret === 'object' && 'name' in secret)
                    .map(secret => secret.name);
            }

            // Store the full resources for later detailed retrieval
            this.fullResources = {
                schema: Array.isArray(schema) ? schema : [],
                functions: Array.isArray(functions) ? functions : [],
                secrets: Array.isArray(secrets) ? secrets : [],
                rlsPolicies: Array.isArray(rlsPolicies) ? rlsPolicies : [],
                dbFunctions: Array.isArray(dbFunctions) ? dbFunctions : [],
                triggers: Array.isArray(triggers) ? triggers : [],
                storageBuckets: Array.isArray(storageBuckets) ? storageBuckets : []
            };

            console.log('[SupabaseContextEngine] Resource index built:', {
                tables: this.resourceIndex.tables.length,
                functions: this.resourceIndex.functions.length,
                policies: this.resourceIndex.policies.length,
                buckets: this.resourceIndex.buckets.length,
                dbFunctions: this.resourceIndex.dbFunctions.length,
                triggers: this.resourceIndex.triggers.length,
                secrets: this.resourceIndex.secrets.length
            });

            return this.resourceIndex;
        } catch (error) {
            console.error('[SupabaseContextEngine] Error building resource index:', error);
            throw error;
        } finally {
            console.timeEnd('build-resource-index');
        }
    }


    /**
     * First stage: Find relevant resources for a query
     */
    async findRelevantResources(query: string): Promise<{
        resources: ResourceIdentification[],
        reasoning: string,
        hasAdditional: boolean,
        additionalResourcesSummary?: string,
        mustRetrieveAdditionalResources?: boolean
    }> {
        console.time('find-relevant-resources');

        // Make sure we have the resource index
        if (Object.keys(this.resourceIndex).length === 0) {
            await this.buildResourceIndex();
        }

        const prompt = `Query: "${query}"

Available Supabase Resources:
${Object.entries(this.resourceIndex)
            .map(([type, names]) => `${type.toUpperCase()}: ${names.join('\n ')}`)
            .join('\n\n')}

Select the most relevant resources for this query. Limit your selection to at most 10 resources total.`

        // Use LLM to identify relevant resources
        const result = await generateObject({
            model: customModel('openai/gpt-4.1'), // Using a smaller model to reduce costs
            temperature: 0.1,
            schema: z.object({
                resources: z.array(z.object({
                    resourceType: z.enum(['table', 'function', 'policy', 'bucket', 'dbFunction', 'trigger', 'secret']).describe("Type of resource. Must match the exact terms."),
                    resourceName: z.string().describe("Name of the resource"),
                    relevanceScore: z.number().min(1).max(10).describe("Relevance score from 1-10"),
                    reasoning: z.string().describe("Why this resource is relevant to the query")
                })),
                reasoning: z.string().describe("Explanation of why these resources were selected"),
                hasAdditional: z.boolean().describe('Apart from the 10 returned, are there more possible resources that may relate to the query?'),
                additionalResourcesSummary: z.string().optional().describe(`List the SPECIFIC additional resources from the provided resource list that are relevant but weren't included in your selection due to the 10-resource limit.
        Be definitive about which resources exist and are relevant - do NOT use hypothetical language like "if X exists" when referring to resources you can see in the provided list.
        Use precise resource names from the available resources list.
        Explain exactly why each additional resource is relevant and how it relates to the resources you've already selected.
        If certain resources are critical for understanding the system, use direct language like "You MUST query for X because it is essential for understanding Y."
        `),
                mustRetrieveAdditionalResources: z.boolean().optional().describe('If additional resources must be retrieved, set this to true. This will force LLM to request additional resources on the basis of the additionalResourcesSummary.'),
            }),
            system: `You are a Supabase database expert. Your task is to identify which resources in a Supabase project are most relevant to a specific query.

You will be given:
1. A query about the Supabase project
2. A list of available resources categorized by type

Your job is to select the most relevant resources that would help answer the query, including resources that reveal important relationships between entities. Consider the following guidelines:

1. IDENTIFY CORE RESOURCES: First identify the primary resources directly mentioned in the query.

2. IDENTIFY RELATED RESOURCES: Then identify resources that have relationships with the primary resources:
   - Tables that reference each other through foreign keys
   - Functions that operate on identified tables
   - Policies that control access to identified tables
   - Triggers that affect identified tables
   - Secrets that might be used by identified functions

3. CONSIDER DATA FLOW: Think about how data flows through the application. If a query mentions user authentication, consider not just the users table, but also related tables like profiles, sessions, or audit logs.

4. SET mustRetrieveAdditionalResources=true when:
   - Critical relationship information is missing
   - The query cannot be properly answered without additional context
   - There are clear dependencies on resources not included in the initial selection

5. PROVIDE CLEAR REASONING: For each resource, explain why it's relevant and how it relates to other resources.

IMPORTANT: Balance between being comprehensive and focused. Don't include everything, but don't miss critical relationships either.`,
            prompt
        });

        console.timeEnd('find-relevant-resources');
        return result.object;
    }

    /**
     * Second stage: Get detailed information about specific resources
     */
    async getResourceDetails(resources: ResourceIdentification[]): Promise<Record<string, any>> {
        console.time('get-resource-details');

        if (!this.fullResources) {
            // If we don't have the full resources cached, rebuild the index
            await this.buildResourceIndex();

            if (!this.fullResources) {
                throw new Error('Failed to build resource index');
            }
        }

        const details: Record<string, any> = {
            tables: [],
            functions: [],
            policies: [],
            buckets: [],
            dbFunctions: [],
            triggers: [],
            secrets: []
        };

        // Group resources by type
        const resourcesByType: Record<string, string[]> = {
            table: [],
            function: [],
            policy: [],
            bucket: [],
            dbFunction: [],
            trigger: [],
            secret: []
        };

        // Populate resourcesByType
        for (const resource of resources) {
            if (resourcesByType[resource.resourceType]) {
                resourcesByType[resource.resourceType].push(resource.resourceName);
            }
        }

        // Get details for each resource type from the cached full resources
        if (resourcesByType.table.length > 0 && Array.isArray(this.fullResources.schema)) {
            details.tables = this.fullResources.schema.filter(table =>
                table && typeof table === 'object' && 'table_name' in table &&
                resourcesByType.table.includes(table.table_name)
            );
        }

        if (resourcesByType.function.length > 0 && Array.isArray(this.fullResources.functions)) {
            details.functions = this.fullResources.functions.filter(func =>
                func && typeof func === 'object' && 'name' in func &&
                resourcesByType.function.includes(func.name)
            );
        }

        if (resourcesByType.policy.length > 0 && Array.isArray(this.fullResources.rlsPolicies)) {
            // Policies are in format "table:policy"
            const policyMap = resourcesByType.policy.map(p => {
                const [tableName, policyName] = p.split(':');
                return {tableName, policyName};
            });

            details.policies = this.fullResources.rlsPolicies.filter(policy =>
                policy && typeof policy === 'object' && 'table' in policy && 'policy_name' in policy &&
                policyMap.some(p => p.tableName === policy.table && p.policyName === policy.policy_name)
            );
        }

        if (resourcesByType.bucket.length > 0 && Array.isArray(this.fullResources.storageBuckets)) {
            details.buckets = this.fullResources.storageBuckets.filter(bucket =>
                bucket && typeof bucket === 'object' && 'name' in bucket &&
                resourcesByType.bucket.includes(bucket.name)
            );
        }

        if (resourcesByType.dbFunction.length > 0 && Array.isArray(this.fullResources.dbFunctions)) {
            details.dbFunctions = this.fullResources.dbFunctions.filter(func =>
                func && typeof func === 'object' && 'function_name' in func &&
                resourcesByType.dbFunction.includes(func.function_name)
            );
        }

        if (resourcesByType.trigger.length > 0 && Array.isArray(this.fullResources.triggers)) {
            details.triggers = this.fullResources.triggers.filter(trigger =>
                trigger && typeof trigger === 'object' && 'trigger_name' in trigger &&
                resourcesByType.trigger.includes(trigger.trigger_name)
            );
        }

        if (resourcesByType.secret.length > 0 && Array.isArray(this.fullResources.secrets)) {
            details.secrets = this.fullResources.secrets.filter(secret =>
                secret && typeof secret === 'object' && 'name' in secret &&
                resourcesByType.secret.includes(secret.name)
            );
        }

        console.timeEnd('get-resource-details');
        this.resourceDetails = details;
        return details;
    }

    // The fetch methods are no longer needed as we're using the cached resources from SupabaseIntegrationProvider

    /**
     * Helper function to normalize resource type for matching
     * Handles case insensitivity and partial matches like 'function', 'functions', 'fun', etc.
     */
    private normalizeResourceType(resourceType: string): string | null {
        resourceType = resourceType.toLowerCase();

        // Map of partial/alternative names to canonical resource types
        const resourceTypeMap: Record<string, string> = {
            'table': 'table',
            'tables': 'table',
            'tab': 'table',
            'tbl': 'table',

            'function': 'function',
            'functions': 'function',
            'func': 'function',
            'fun': 'function',
            'fn': 'function',

            'policy': 'policy',
            'policies': 'policy',
            'pol': 'policy',

            'bucket': 'bucket',
            'buckets': 'bucket',
            'bkt': 'bucket',
            'storage': 'bucket',

            'dbfunction': 'dbFunction',
            'dbfunctions': 'dbFunction',
            'dbfunc': 'dbFunction',
            'dbfn': 'dbFunction',
            'db_function': 'dbFunction',

            'trigger': 'trigger',
            'triggers': 'trigger',
            'trig': 'trigger',

            'secret': 'secret',
            'secrets': 'secret',
            'sec': 'secret'
        };

        // Try exact match first
        if (resourceTypeMap[resourceType]) {
            return resourceTypeMap[resourceType];
        }

        // Try partial match
        for (const [key, value] of Object.entries(resourceTypeMap)) {
            if (key.startsWith(resourceType) || resourceType.startsWith(key)) {
                return value;
            }
        }

        return null; // No match found
    }

    /**
     * Check if a resource should be excluded based on the excludedResources list
     */
    private shouldExcludeResource(resource: ResourceIdentification, excludedResources: string[]): boolean {
        if (!excludedResources || excludedResources.length === 0) {
            return false;
        }

        for (const excludedResource of excludedResources) {
            const parts = excludedResource.split('.');
            if (parts.length !== 2) continue;

            const [excludedType, excludedName] = parts;
            const normalizedExcludedType = this.normalizeResourceType(excludedType);

            if (!normalizedExcludedType) continue;

            // Check if the resource type matches and the name matches or is a wildcard
            if (normalizedExcludedType === resource.resourceType &&
                (excludedName === '*' || excludedName.toLowerCase() === resource.resourceName.toLowerCase())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Main method to get relevant Supabase resources for a query
     * @param query The natural language query to find relevant resources for
     * @param excludedResources Optional array of resources to exclude in the format "resourceType.resourceName"
     */
    async getRelevantSupabaseResources(query: string, excludedResources?: string[]): Promise<any> {
        // Stage 1: Find relevant resources
        const {
            resources: allResources,
            reasoning,
            additionalResourcesSummary,
            hasAdditional,
            mustRetrieveAdditionalResources
        } = await this.findRelevantResources(query);

        // Filter out excluded resources if any
        let excludedCount = 0;
        const resources = excludedResources && excludedResources.length > 0
            ? allResources.filter(resource => {
                const shouldExclude = this.shouldExcludeResource(resource, excludedResources);
                if (shouldExclude) excludedCount++;
                return !shouldExclude;
            })
            : allResources;

        if (excludedCount > 0) {
            console.log(`[SupabaseContextEngine] Excluded ${excludedCount} resources based on user preferences`);
        }

        if (resources.length === 0) {
            console.log("[SupabaseContextEngine] No relevant resources found");
            return {
                query,
                resources: [],
                reasoning: reasoning || "No relevant resources found",
                details: {}
            };
        }

        // Stage 2: Get details for those resources
        const details = await this.getResourceDetails(resources);

        // Calculate token usage metrics
        const result = {
            query,
            resources,
            reasoning,
            details,
            additionalResourcesSummary,
            hasAdditional,
            mustRetrieveAdditionalResources,
            excludedResources: excludedResources || []
        };

        // Log metrics
        const resultString = JSON.stringify(result);
        console.log(`[SupabaseContextEngine] Result size: ${resultString.length} chars / ~${Math.ceil(resultString.length / 4)} tokens`);

        return result;
    }
}
