import { ExpoModuleLoader } from './expo-module-loader';

/**
 * Error interface for module validation errors
 */
interface ModuleError extends Error {
  line?: number;
  column?: number;
  sourceFile?: string;
  file?: string;
}

/**
 * Validates Expo code using the same module loading and error detection
 * approach as the actual Expo Snack runtime
 */
export class ExpoValidator {
  /**
   * Validates a collection of files using the Expo Snack runtime approach
   * This will detect only the first error encountered during module loading,
   * exactly matching the behavior of the actual Expo Snack runtime
   * 
   * @param files Array of file objects with name and content
   * @returns Validation result with the first error encountered
   */
  static validateFiles(files: Array<{name: string, content: string}>): {
    isValid: boolean;
    error?: ModuleError;
    summary: string;
  } {
    console.log(`Starting Expo validation of ${files.length} files`);
    
    const moduleLoader = new ExpoModuleLoader();
    const result = moduleLoader.validateFiles(files);
    
    // Generate a summary message
    let summary = '';
    if (result.isValid) {
      summary = 'No errors detected in the Expo code.';
    } else if (result.error) {
      const errorFile = result.error.sourceFile || (result.error as any).file || 'unknown file';
      summary = `Error in ${errorFile}: ${result.error.message}`;
      if (result.error.line) {
        summary += ` at line ${result.error.line}`;
        if (result.error.column) {
          summary += `, column ${result.error.column}`;
        }
      }
    }
    
    return {
      ...result,
      summary
    };
  }
}
