[{"templateName": "bare", "files": [{"name": "App.tsx", "content": "import React from 'react';\nimport {DarkTheme, DefaultTheme, NavigationContainer} from '@react-navigation/native';\nimport {StatusBar} from 'expo-status-bar';\nimport {SafeAreaProvider} from 'react-native-safe-area-context';\nimport {useColorScheme} from 'react-native';\nimport { Toaster } from 'sonner-native';\nimport MainTabNavigator from './navigation';\n\n/**\n * Do not add navigation stack here. Add it in the navigation folder.\n */\nexport default function App() {\n    const colorScheme = useColorScheme();\n    const isDark = colorScheme === 'dark';\n\n    // Always extend the base theme from react.navigation.\n    // Otherwise, error such as cannot read property 'n.medium' of undefined will occur which basically means the fonts property is missing from the theme.\n    const navigationTheme = {\n        ...(isDark ? DarkTheme : DefaultTheme),\n        colors: {\n            // Change this to match the app's theme. Either use Dark or light. Add conditional only when theme switching is required.\n            ...DefaultTheme.colors\n            // isDark ? DarkTheme.colors : DefaultTheme.colors\n        },\n    };\n\n    return (\n        <SafeAreaProvider>\n            <NavigationContainer theme={navigationTheme}>\n                <StatusBar\n                    style={isDark ? 'light' : 'dark'}\n                />\n                <Toaster theme={'light'} richColors />\n                <MainTabNavigator />\n            </NavigationContainer>\n        </SafeAreaProvider>\n    );\n}", "language": "typescript"}, {"name": "navigation/index.tsx", "content": "import React from 'react';\nimport { Platform, useColorScheme } from 'react-native';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { Feather } from '@expo/vector-icons';\nimport HomeScreen from '../screens/HomeScreen';\n\n// Define types for navigation\nexport type MainTabsParamList = {\n    Home: undefined;\n    Home1: undefined;\n    Home2: undefined;\n};\n\nconst Tab = createBottomTabNavigator<MainTabsParamList>();\n\n// Main tab navigator\nexport const MainTabNavigator = () => {\n    const colorScheme = useColorScheme();\n    const isDark = colorScheme === 'dark';\n\n    return (\n        <Tab.Navigator\n            screenOptions={({route}) => ({\n                headerShown: false,\n                tabBarIcon: ({focused, color, size}) => {\n                    let iconName: keyof typeof Feather.glyphMap = 'circle';\n\n                    if (route.name === 'Home') {\n                        iconName = 'home';\n                    } else if (route.name === 'Home1') {\n                        iconName = 'compass';\n                    } else if (route.name === 'Home2') {\n                        iconName = 'user';\n                    } else {\n                        iconName = 'circle';\n                    }\n\n                    return <Feather name={iconName} size={20} color={color}/>;\n                },\n                tabBarStyle: {\n                    // Always use this configuration to ensure the tab text does not get cut and has enough space for the text and the icon\n                    height: Platform.OS === 'ios' ? 72 : 60,\n                    paddingBottom: 8,\n                    borderTopWidth: 0,\n                    elevation: 0,\n                    shadowOpacity: 0,\n                    // Ensure tab bar doesn't overlap with bottom notch\n                    ...(Platform.OS === 'ios' ? {paddingBottom: 0} : {}),\n                },\n                tabBarLabelStyle: {\n                    fontSize: 12,\n                    fontWeight: '500',\n                }\n            })}\n        >\n            <Tab.Screen name=\"Home\" component={HomeScreen}/>\n            <Tab.Screen name=\"Home1\" component={() => null}/>\n            <Tab.Screen name=\"Home2\" component={() => null}/>\n        </Tab.Navigator>\n    );\n};\n\nexport default MainTabNavigator;\n", "language": "typescript"}, {"name": "screens/HomeScreen.tsx", "content": "import React, { useRef } from 'react';\nimport {View, Text, StyleSheet, Animated, ScrollView, useColorScheme} from 'react-native';\nimport {SparklesIcon} from \"lucide-react-native\";\n\nexport default function HomeScreen() {\n    const isDark = useColorScheme() === 'dark';\n\n    return (\n        <View style={[styles.mainContainer]}>\n            <View style={styles.container}>\n                <SparklesIcon size={50} style={[styles.logo]}/>\n                <Text style={[styles.logo, {color: '#1e1e1e'}]}>magically</Text>\n                <Text style={[styles.tagline, {color: '#1e1e1e'}]}>Android, iOS mobile apps in minutes</Text>\n                <Text style={[styles.subtitle, {color: '#1e1e1e'}]}>AI will help you build your app</Text>\n            </View>\n        </View>\n    );\n}\n\nconst styles = StyleSheet.create({\n    mainContainer: {\n        flex: 1,\n        backgroundColor: '#FFFFFF'\n    },\n    container: { flex: 1, alignItems: 'center', justifyContent: 'center' },\n    logo: { fontSize: 42, fontWeight: 'bold', marginBottom: 16 },\n    tagline: { fontSize: 18, marginBottom: 8 },\n    subtitle: { fontSize: 16 }\n});\n", "language": "typescript"}, {"name": "stores/authStore.ts", "content": "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n}\n\n/**\n * AUTH SYSTEM GUIDE - When to use which auth flow:\n * \n * 1. <PERSON><PERSON><PERSON><PERSON> ('signin'):\n *    - Default mode for returning users\n *    - Validates email/password with real-time form validation\n *    - <PERSON><PERSON> \"Email not confirmed\" error → switches to 'verify-email'\n *    - <PERSON>les \"Invalid credentials\" error → shows user-friendly message\n * \n * 2. SIGNUP ('signup'):\n *    - For new user registration\n *    - Validates name, email, password, confirm password\n *    - Password must have uppercase, lowercase, number\n *    - On success → switches to 'verify-email' mode\n * \n * 3. RESET PASSWORD ('reset'):\n *    - For users who forgot password\n *    - Sends reset link to email\n *    - Shows success state with instructions\n *    - Common with Supabase: \"Email rate limit exceeded\"\n * \n * 4. VERIFY EMAIL ('verify-email'):\n *    - After signup or when email not confirmed\n *    - Shows resend button with 60s cooldown\n *    - Explains verification process step-by-step\n *    - Handles \"Token expired\" and \"Invalid token\" errors\n * \n * 5. CONFIRM EMAIL ('confirm-email'):\n *    - When user clicks verification link (deep linking)\n *    - Processes email confirmation token\n *    - On success → user is signed in\n * \n * SUPABASE INTEGRATION NOTES:\n * - Replace mock implementations with actual Supabase auth calls\n * - All common Supabase errors are mapped to user-friendly messages\n * - Storage keys use centralized config (see config/index.ts)\n * - Email verification is required before sign-in (configurable in Supabase)\n */\n\nimport { STORAGE_KEYS } from '../constants';\n\nexport type AuthMode = 'signin' | 'signup' | 'reset' | 'verify-email' | 'confirm-email';\n\nexport interface AuthState {\n  user: User | null;\n  isLoading: boolean;\n  error: string | null;\n  mode: AuthMode;\n  emailForVerification: string | null;\n  isEmailVerified: boolean;\n}\n\nexport interface AuthActions {\n  // Auth actions\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string, name?: string) => Promise<void>;\n  signOut: () => Promise<void>;\n  resetPassword: (email: string) => Promise<void>;\n  confirmEmail: (token: string) => Promise<void>;\n  resendVerification: (email: string) => Promise<void>;\n  \n  // State management\n  setMode: (mode: AuthMode) => void;\n  setError: (error: string | null) => void;\n  clearError: () => void;\n  setLoading: (loading: boolean) => void;\n  setUser: (user: User | null) => void;\n}\n\nexport type AuthStore = AuthState & AuthActions;\n\n// Common Supabase auth errors and their user-friendly messages\nconst getAuthErrorMessage = (error: any): string => {\n  const message = error?.message || error || 'An unexpected error occurred';\n  \n  // Supabase specific errors\n  if (message.includes('Invalid login credentials')) {\n    return 'Invalid email or password. Please check your credentials.';\n  }\n  if (message.includes('Email not confirmed')) {\n    return 'Please verify your email address before signing in.';\n  }\n  if (message.includes('User already registered')) {\n    return 'An account with this email already exists. Try signing in instead.';\n  }\n  if (message.includes('Password should be at least')) {\n    return 'Password must be at least 6 characters long.';\n  }\n  if (message.includes('Unable to validate email address')) {\n    return 'Please enter a valid email address.';\n  }\n  if (message.includes('Email rate limit exceeded')) {\n    return 'Too many emails sent. Please wait before requesting another.';\n  }\n  if (message.includes('Token has expired')) {\n    return 'Verification link has expired. Please request a new one.';\n  }\n  if (message.includes('Invalid token')) {\n    return 'Invalid verification link. Please request a new one.';\n  }\n  if (message.includes('Network request failed')) {\n    return 'Network error. Please check your connection and try again.';\n  }\n  \n  return message;\n};\n\nexport const useAuthStore = create<AuthStore>()(persist(\n  (set, get) => ({\n    // Initial state\n    user: null,\n    isLoading: false,\n    error: null,\n    mode: 'signin',\n    emailForVerification: null,\n    isEmailVerified: false,\n\n    // Auth actions - Mock implementation (replace with Supabase when connected)\n    signIn: async (email: string, password: string) => {\n      set({ isLoading: true, error: null });\n      try {\n        // Mock implementation - replace with Supabase auth\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        // Simulate email verification check\n        if (email.includes('unverified')) {\n          set({ \n            isLoading: false, \n            error: 'Please verify your email address before signing in.',\n            emailForVerification: email,\n            mode: 'verify-email'\n          });\n          return;\n        }\n        \n        const user: User = {\n          id: '1',\n          email,\n          name: email.split('@')[0]\n        };\n        \n        set({ user, isLoading: false, isEmailVerified: true });\n      } catch (error) {\n        set({ isLoading: false, error: getAuthErrorMessage(error) });\n      }\n    },\n\n    signUp: async (email: string, password: string, name?: string) => {\n      set({ isLoading: true, error: null });\n      try {\n        // Mock implementation - replace with Supabase auth\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        set({ \n          isLoading: false,\n          emailForVerification: email,\n          mode: 'verify-email',\n          error: null\n        });\n      } catch (error) {\n        set({ isLoading: false, error: getAuthErrorMessage(error) });\n      }\n    },\n\n    signOut: async () => {\n      set({ isLoading: true });\n      try {\n        // Mock implementation - replace with Supabase auth\n        await new Promise(resolve => setTimeout(resolve, 500));\n        set({ \n          user: null, \n          isLoading: false, \n          error: null, \n          mode: 'signin',\n          emailForVerification: null,\n          isEmailVerified: false\n        });\n      } catch (error) {\n        set({ isLoading: false, error: getAuthErrorMessage(error) });\n      }\n    },\n\n    resetPassword: async (email: string) => {\n      set({ isLoading: true, error: null });\n      try {\n        // Mock implementation - replace with Supabase auth\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        set({ \n          isLoading: false,\n          error: null,\n          emailForVerification: email\n        });\n      } catch (error) {\n        set({ isLoading: false, error: getAuthErrorMessage(error) });\n      }\n    },\n\n    confirmEmail: async (token: string) => {\n      set({ isLoading: true, error: null });\n      try {\n        // Mock implementation - replace with Supabase auth\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        set({ \n          isLoading: false,\n          isEmailVerified: true,\n          mode: 'signin'\n        });\n      } catch (error) {\n        set({ isLoading: false, error: getAuthErrorMessage(error) });\n      }\n    },\n\n    resendVerification: async (email: string) => {\n      set({ isLoading: true, error: null });\n      try {\n        // Mock implementation - replace with Supabase auth\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        set({ isLoading: false, error: null });\n      } catch (error) {\n        set({ isLoading: false, error: getAuthErrorMessage(error) });\n      }\n    },\n\n    // State management\n    setMode: (mode: AuthMode) => set({ mode, error: null }),\n    setError: (error: string | null) => set({ error }),\n    clearError: () => set({ error: null }),\n    setLoading: (isLoading: boolean) => set({ isLoading }),\n    setUser: (user: User | null) => set({ user })\n  }),\n  {\n    name: STORAGE_KEYS.AUTH_STORE,\n    storage: {\n      getItem: async (name: string) => {\n        const value = await AsyncStorage.getItem(name);\n        return value ? JSON.parse(value) : null;\n      },\n      setItem: async (name: string, value: any) => {\n        await AsyncStorage.setItem(name, JSON.stringify(value));\n      },\n      removeItem: async (name: string) => {\n        await AsyncStorage.removeItem(name);\n      }\n    },\n    partialize: (state) => ({\n      user: state.user,\n      isEmailVerified: state.isEmailVerified\n    })\n  }\n));", "language": "typescript"}]}, {"templateName": "base", "files": [{"name": "App.tsx", "content": "/**\n * @magic_component: App\n * @magic_purpose: Root component that sets up providers and navigation\n * @magic_category: Core\n * @magic_keywords: app,root,providers,navigation\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useEffect } from 'react';\nimport { StatusBar } from 'expo-status-bar';\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\nimport { useColorScheme } from 'react-native';\n\n// Import navigation\nimport Navigation from './navigation';\n\n// Import context providers\nimport { AppProvider } from './contexts/AppContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\n\n// Import utilities\nimport { logger } from './utils/logger';\n\n/**\n * Root App component that sets up the application with all required providers\n * and the navigation structure\n * \n * IMPORTANT: We avoid using union types and typeof as they can break the app\n */\nexport default function App() {\n  // Log app startup\n  useEffect(() => {\n    logger.info('Application started', 'GENERAL', { timestamp: new Date().toISOString() });\n  }, []);\n\n  return (\n    <SafeAreaProvider\n      initialMetrics={{\n        frame: { x: 0, y: 0, width: 0, height: 0 },\n        insets: { top: 0, left: 0, right: 0, bottom: 0 },\n      }}\n    >\n      {/* Theme provider for consistent theming across the app */}\n      <ThemeProvider>\n        {/* Global app state provider */}\n        <AppProvider>\n          {/* Main navigation container */}\n          <Navigation />\n        </AppProvider>\n      </ThemeProvider>\n      {/* Status bar with adaptive style */}\n      <StatusBar style=\"auto\" />\n    </SafeAreaProvider>\n  );\n}", "language": "typescript"}, {"name": "components/screens/HomeScreen/HomeHeader.tsx", "content": "/**\n * @magic_component: HomeHeader\n * @magic_platform: all\n * @magic_purpose: Header component for the home screen with status bar handling and theme toggle\n * @magic_category: Components/Headers\n * @magic_keywords: header,status bar,theme,toggle,navigation\n * @magic_connects: ThemeContext\n */\nimport React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  TouchableOpacity,\n  Platform,\n  StatusBar as RNStatusBar,\n} from 'react-native';\nimport { StatusBar } from 'expo-status-bar';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { Feather } from '@expo/vector-icons';\nimport { useTheme } from '../../../contexts/ThemeContext';\nimport { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../../constants/theme';\nimport { logger } from '../../../utils/logger';\n\ninterface HomeHeaderProps {\n  title?: string;\n  showBackButton?: boolean;\n  onBackPress?: () => void;\n  rightComponent?: React.ReactNode;\n}\n\n/**\n * Header component for the home screen that handles status bar and safe area\n * \n * This component automatically adjusts for the status bar and includes a theme toggle.\n * It can optionally display a back button and custom right component.\n * \n * @example\n * <HomeHeader \n *   title=\"Home\" \n *   showBackButton={false}\n * />\n */\nconst HomeHeader: React.FC<HomeHeaderProps> = ({\n  title = 'Home',\n  showBackButton = false,\n  onBackPress,\n  rightComponent,\n}) => {\n  // Get safe area insets to handle notches and status bars\n  const insets = useSafeAreaInsets();\n  \n  // Get theme context for dark mode toggle\n  const { isDarkMode, toggleTheme, colors } = useTheme();\n  \n  // Calculate statusBarHeight based on platform and insets\n  const statusBarHeight = Platform.OS === 'ios' ? insets.top : RNStatusBar.currentHeight || 0;\n  \n  return (\n    <View style={[\n      styles.container, \n      { paddingTop: statusBarHeight },\n      isDarkMode && styles.darkContainer\n    ]}>\n      {/* Status bar with proper style based on theme */}\n      <StatusBar style={isDarkMode ? 'light' : 'dark'} />\n      \n      <View style={styles.content}>\n        {/* Left section with optional back button */}\n        <View style={styles.leftSection}>\n          {showBackButton && (\n            <TouchableOpacity \n              onPress={onBackPress} \n              style={styles.backButton}\n              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}\n            >\n              <Feather \n                name=\"chevron-left\" \n                size={24} \n                color={isDarkMode ? colors.text : COLORS.text} \n              />\n            </TouchableOpacity>\n          )}\n          <Text \n            style={[\n              styles.title, \n              isDarkMode && styles.darkText,\n              showBackButton && styles.titleWithBack\n            ]}\n          >\n            {title}\n          </Text>\n        </View>\n        \n        {/* Right section with theme toggle and optional custom component */}\n        <View style={styles.rightSection}>\n          {/* Theme toggle button */}\n          <TouchableOpacity \n            onPress={toggleTheme} \n            style={styles.iconButton}\n            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}\n          >\n            <Feather \n              name={isDarkMode ? 'sun' : 'moon'} \n              size={20} \n              color={isDarkMode ? colors.text : COLORS.text} \n            />\n          </TouchableOpacity>\n          \n          {/* Optional right component */}\n          {rightComponent}\n        </View>\n      </View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    backgroundColor: COLORS.background,\n    borderBottomWidth: 1,\n    borderBottomColor: COLORS.border,\n    zIndex: 10,\n  },\n  darkContainer: {\n    backgroundColor: '#121212',\n    borderBottomColor: '#2A2A2A',\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    height: 56,\n    paddingHorizontal: SPACING.medium,\n  },\n  leftSection: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    flex: 1,\n  },\n  rightSection: {\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n  title: {\n    ...TYPOGRAPHY.h3,\n    color: COLORS.text,\n  },\n  darkText: {\n    color: COLORS.white,\n  },\n  titleWithBack: {\n    marginLeft: SPACING.small,\n  },\n  backButton: {\n    padding: 4,\n  },\n  iconButton: {\n    padding: 4,\n    marginLeft: SPACING.medium,\n  },\n});\n\nexport default HomeHeader;\n", "language": "typescript"}, {"name": "components/screens/PlaceholderScreen.tsx", "content": "/**\n * @magic_component: PlaceholderScreen\n * @magic_platform: all\n * @magic_purpose: A placeholder screen that informs users they can ask the AI to implement features\n * @magic_category: Screens/Placeholder\n * @magic_keywords: placeholder,empty,feature,prompt\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useState } from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  TouchableOpacity,\n  ScrollView,\n  Clipboard,\n  ToastAndroid,\n  Platform,\n  Alert,\n  ViewStyle,\n  TextStyle,\n} from 'react-native';\nimport { Feather } from '@expo/vector-icons';\nimport { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../constants/theme';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { logger } from '../../utils/logger';\n\n/**\n * Feature prompt interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface FeaturePrompt {\n  title: string;\n  description: string;\n  prompt: string;\n  icon: string; // Feather icon name\n}\n\n/**\n * PlaceholderScreen props interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface PlaceholderScreenProps {\n  // Title of the placeholder screen\n  title?: string;\n  \n  // Description of the placeholder screen\n  description?: string;\n  \n  // Feature name to display in the placeholder\n  featureName?: string;\n  \n  // Custom feature prompts to display\n  customPrompts?: FeaturePrompt[];\n}\n\n/**\n * Placeholder screen that informs users they can ask the AI to implement features\n * \n * This screen displays a message to the user that they can ask the AI to implement\n * a specific feature, along with example prompts they can use.\n * \n * @example\n * <PlaceholderScreen\n *   title=\"User Profile\"\n *   description=\"This screen will display user profile information\"\n *   featureName=\"User Profile\"\n * />\n */\nconst PlaceholderScreen: React.FC<PlaceholderScreenProps> = ({\n  title = 'Feature Not Implemented',\n  description = 'This feature has not been implemented yet. You can ask the AI to implement it for you.',\n  featureName = 'this feature',\n  customPrompts,\n}) => {\n  // Get theme from context\n  const { isDarkMode, colors } = useTheme();\n  \n  // State for showing copy confirmation\n  const [copiedPromptIndex, setCopiedPromptIndex] = useState<number | null>(null);\n  \n  // Default feature prompts\n  const defaultPrompts: FeaturePrompt[] = [\n    {\n      title: 'Basic Implementation',\n      description: 'Ask for a basic implementation of the feature',\n      prompt: `Please implement ${featureName} with basic functionality.`,\n      icon: 'code',\n    },\n    {\n      title: 'Detailed Implementation',\n      description: 'Ask for a detailed implementation with specific requirements',\n      prompt: `Please implement ${featureName} with the following requirements:\\n- User-friendly interface\\n- Data persistence\\n- Error handling\\n- Loading states`,\n      icon: 'layers',\n    },\n    {\n      title: 'Advanced Implementation',\n      description: 'Ask for an advanced implementation with specific design and functionality',\n      prompt: `Please implement ${featureName} with a modern design and the following features:\\n- Clean, responsive UI\\n- Data validation\\n- Offline support\\n- Animations and transitions\\n- Comprehensive error handling`,\n      icon: 'star',\n    },\n  ];\n  \n  // Use custom prompts if provided, otherwise use default prompts\n  const prompts = customPrompts || defaultPrompts;\n  \n  // Copy prompt to clipboard\n  const copyToClipboard = (prompt: string, index: number) => {\n    Clipboard.setString(prompt);\n    setCopiedPromptIndex(index);\n    \n    // Show toast or alert based on platform\n    if (Platform.OS === 'android') {\n      ToastAndroid.show('Prompt copied to clipboard', ToastAndroid.SHORT);\n    } else {\n      Alert.alert('Copied', 'Prompt copied to clipboard');\n    }\n    \n    // Reset copied state after 2 seconds\n    setTimeout(() => {\n      setCopiedPromptIndex(null);\n    }, 2000);\n    \n    logger.ui('Prompt copied to clipboard', { promptIndex: index });\n  };\n  \n  return (\n    <ScrollView\n      style={[\n        styles.container,\n        isDarkMode && styles.darkContainer,\n      ]}\n      contentContainerStyle={styles.contentContainer}\n    >\n      {/* Header */}\n      <View style={styles.header}>\n        <Feather\n          name=\"code\"\n          size={48}\n          color={colors.primary}\n          style={styles.icon}\n        />\n        <Text style={[\n          styles.title,\n          isDarkMode && styles.darkText,\n        ]}>\n          {title}\n        </Text>\n        <Text style={[\n          styles.description,\n          isDarkMode && styles.darkTextSecondary,\n        ]}>\n          {description}\n        </Text>\n      </View>\n      \n      {/* Prompts */}\n      <View style={styles.promptsContainer}>\n        <Text style={[\n          styles.promptsTitle,\n          isDarkMode && styles.darkText,\n        ]}>\n          Example Prompts\n        </Text>\n        \n        {prompts.map((prompt, index) => (\n          <View\n            key={index}\n            style={[\n              styles.promptCard,\n              isDarkMode && styles.darkPromptCard,\n            ]}\n          >\n            <View style={styles.promptHeader}>\n              <Feather\n                name={prompt.icon as any}\n                size={24}\n                color={colors.primary}\n                style={styles.promptIcon}\n              />\n              <Text style={[\n                styles.promptTitle,\n                isDarkMode && styles.darkText,\n              ]}>\n                {prompt.title}\n              </Text>\n            </View>\n            \n            <Text style={[\n              styles.promptDescription,\n              isDarkMode && styles.darkTextSecondary,\n            ]}>\n              {prompt.description}\n            </Text>\n            \n            <View style={[styles.promptTextContainer, isDarkMode && styles.darkPromptTextContainer]}>\n              <Text style={[\n                styles.promptText,\n                isDarkMode && styles.darkPromptText,\n              ]}>\n                {prompt.prompt}\n              </Text>\n            </View>\n            \n            <TouchableOpacity\n              style={[\n                styles.copyButton,\n                copiedPromptIndex === index && styles.copiedButton,\n              ]}\n              onPress={() => copyToClipboard(prompt.prompt, index)}\n            >\n              <Feather\n                name={copiedPromptIndex === index ? 'check' : 'copy'}\n                size={16}\n                color={copiedPromptIndex === index ? COLORS.success : colors.primary}\n                style={styles.copyIcon}\n              />\n              <Text style={[\n                styles.copyText,\n                copiedPromptIndex === index && styles.copiedText,\n              ]}>\n                {copiedPromptIndex === index ? 'Copied!' : 'Copy to Clipboard'}\n              </Text>\n            </TouchableOpacity>\n          </View>\n        ))}\n      </View>\n      \n      {/* Footer */}\n      <View style={styles.footer}>\n        <Text style={[\n          styles.footerText,\n          isDarkMode && styles.darkTextSecondary,\n        ]}>\n          Copy a prompt and ask the AI to implement {featureName} for you.\n        </Text>\n      </View>\n    </ScrollView>\n  );\n};\n\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: COLORS.background,\n  },\n  darkContainer: {\n    backgroundColor: '#121212',\n  },\n  contentContainer: {\n    padding: SPACING.large,\n    paddingBottom: SPACING.xlarge,\n  },\n  header: {\n    alignItems: 'center',\n    marginBottom: SPACING.xlarge,\n  },\n  icon: {\n    marginBottom: SPACING.medium,\n  },\n  title: {\n    ...TYPOGRAPHY.h2,\n    color: COLORS.text,\n    textAlign: 'center',\n    marginBottom: SPACING.small,\n  },\n  description: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    textAlign: 'center',\n  },\n  promptsContainer: {\n    marginBottom: SPACING.xlarge,\n  },\n  promptsTitle: {\n    ...TYPOGRAPHY.h3,\n    color: COLORS.text,\n    marginBottom: SPACING.medium,\n  },\n  promptCard: {\n    backgroundColor: COLORS.cardBackground,\n    borderRadius: 12,\n    padding: SPACING.medium,\n    marginBottom: SPACING.medium,\n    ...MIXINS.shadow,\n  },\n  darkPromptCard: {\n    backgroundColor: '#1E1E1E',\n  },\n  promptHeader: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: SPACING.small,\n  },\n  promptIcon: {\n    marginRight: SPACING.small,\n  },\n  promptTitle: {\n    ...TYPOGRAPHY.subtitle,\n    color: COLORS.text,\n  },\n  promptDescription: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    marginBottom: SPACING.medium,\n  },\n  promptTextContainer: {\n    backgroundColor: '#F5F5F5', // Light mode default\n    borderRadius: 8,\n    padding: SPACING.medium,\n    marginBottom: SPACING.medium,\n  },\n  darkPromptTextContainer: {\n    backgroundColor: '#2A2A2A', // Dark mode background\n  },\n  promptText: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.text,\n    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',\n  },\n  darkPromptText: {\n    color: '#E0E0E0',\n  },\n  copyButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: 'transparent',\n    borderWidth: 1,\n    borderColor: COLORS.primary,\n    borderRadius: 8,\n    padding: SPACING.small,\n  },\n  copiedButton: {\n    backgroundColor: 'rgba(76, 175, 80, 0.1)',\n    borderColor: COLORS.success,\n  },\n  copyIcon: {\n    marginRight: SPACING.small,\n  },\n  copyText: {\n    ...TYPOGRAPHY.button,\n    color: COLORS.primary,\n  },\n  copiedText: {\n    color: COLORS.success,\n  },\n  footer: {\n    alignItems: 'center',\n  },\n  footerText: {\n    ...TYPOGRAPHY.caption,\n    color: COLORS.textTertiary,\n    textAlign: 'center',\n  },\n  darkText: {\n    color: '#FFFFFF',\n  },\n  darkTextSecondary: {\n    color: '#A0A0A0',\n  },\n});\n\nexport default PlaceholderScreen;\n", "language": "typescript"}, {"name": "components/ui/BottomSheet.tsx", "content": "/**\n * @magic_component: BottomSheet\n * @magic_platform: all\n * @magic_purpose: A customizable bottom sheet component that slides up from the bottom of the screen\n * @magic_category: UI/Feedback\n * @magic_keywords: bottomsheet,sheet,drawer,modal,slide\n * @magic_props: visible,onClose,title,children\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useEffect, useRef, useState } from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  Modal,\n  Animated,\n  Dimensions,\n  ScrollView,\n  TouchableOpacity,\n  Pressable,\n  Platform,\n} from 'react-native';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { Feather } from '@expo/vector-icons';\nimport { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../constants/theme';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { logger } from '../../utils/logger';\n\n// Get screen dimensions\nconst { height: screenHeight } = Dimensions.get('window');\n\n/**\n * BottomSheet props interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface BottomSheetProps {\n  // Whether the bottom sheet is visible\n  visible: boolean;\n  \n  // Function to call when the bottom sheet is closed\n  onClose: () => void;\n  \n  // Title of the bottom sheet\n  title?: string;\n  \n  // Content of the bottom sheet\n  children: React.ReactNode;\n  \n  // Whether to close the bottom sheet when clicking outside\n  closeOnOverlayPress?: boolean;\n  \n  // Whether to show the close button\n  showCloseButton?: boolean;\n  \n  // Height of the bottom sheet (percentage of screen height)\n  height?: number;\n  \n  // Custom styles for the bottom sheet content\n  contentStyle?: any;\n  \n  // Whether the bottom sheet content is scrollable\n  scrollable?: boolean;\n  \n  // Footer content for the bottom sheet\n  footer?: React.ReactNode;\n  \n  // Whether to show a drag handle at the top of the bottom sheet\n  showDragHandle?: boolean;\n}\n\n/**\n * BottomSheet component that slides up from the bottom of the screen\n * \n * @example\n * <BottomSheet\n *   visible={isBottomSheetVisible}\n *   onClose={() => setIsBottomSheetVisible(false)}\n *   title=\"Bottom Sheet Title\"\n * >\n *   <Text>Bottom sheet content goes here</Text>\n * </BottomSheet>\n */\nconst BottomSheet: React.FC<BottomSheetProps> = ({\n  visible,\n  onClose,\n  title,\n  children,\n  closeOnOverlayPress = true,\n  showCloseButton = true,\n  height = 50,\n  contentStyle,\n  scrollable = true,\n  footer,\n  showDragHandle = true,\n}) => {\n  // Get theme from context\n  const { isDarkMode, colors } = useTheme();\n  \n  // Animation value for the bottom sheet\n  const translateY = useRef(new Animated.Value(screenHeight)).current;\n  \n  // State to track if the sheet is fully visible\n  const [isSheetVisible, setIsSheetVisible] = useState(false);\n  \n  // Get safe area insets for handling notches and home indicators\n  const insets = useSafeAreaInsets();\n  \n  // Handle animation when visibility changes\n  useEffect(() => {\n    if (visible) {\n      logger.ui('Bottom sheet opened', { title });\n      Animated.timing(translateY, {\n        toValue: 0,\n        duration: 300,\n        useNativeDriver: true,\n      }).start(() => {\n        setIsSheetVisible(true);\n      });\n    } else {\n      Animated.timing(translateY, {\n        toValue: screenHeight,\n        duration: 300,\n        useNativeDriver: true,\n      }).start(() => {\n        setIsSheetVisible(false);\n      });\n    }\n  }, [visible, translateY]);\n  \n  // Handle closing the bottom sheet\n  const handleClose = () => {\n    logger.ui('Bottom sheet closed', { title });\n    onClose();\n  };\n  \n  // Handle overlay press\n  const handleOverlayPress = () => {\n    if (closeOnOverlayPress) {\n      handleClose();\n    }\n  };\n  \n  // Prevent propagation of touches on the bottom sheet content\n  const handleContentPress = (e: any) => {\n    e.stopPropagation();\n  };\n  \n  return (\n    <Modal\n      visible={visible}\n      transparent\n      animationType=\"fade\"\n      onRequestClose={handleClose}\n    >\n      <Pressable \n        onPress={handleOverlayPress}\n        style={[\n          styles.container,\n          isDarkMode && styles.darkContainer,\n        ]}\n      >\n        <Pressable onPress={handleContentPress}>\n          <Animated.View\n            style={[\n              styles.sheetContainer,\n              {\n                transform: [{ translateY }],\n                paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0, // Dynamic padding based on safe area\n              },\n              isDarkMode && styles.darkSheetContainer,\n            ]}\n          >\n            {/* Drag Handle */}\n            {showDragHandle && (\n              <View style={styles.dragHandleContainer}>\n                <View style={[\n                  styles.dragHandle,\n                  isDarkMode && styles.darkDragHandle,\n                ]} />\n              </View>\n            )}\n            \n            {/* Header */}\n            {(title || showCloseButton) && (\n              <View style={styles.header}>\n                {title && (\n                  <Text style={[\n                    styles.title,\n                    isDarkMode && styles.darkText,\n                  ]}>\n                    {title}\n                  </Text>\n                )}\n                \n                {showCloseButton && (\n                  <TouchableOpacity\n                    onPress={handleClose}\n                    style={styles.closeButton}\n                    hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}\n                  >\n                    <Feather\n                      name=\"x\"\n                      size={24}\n                      color={isDarkMode ? colors.textSecondary : colors.text}\n                    />\n                  </TouchableOpacity>\n                )}\n              </View>\n            )}\n            \n            {/* Body */}\n            {scrollable ? (\n              <ScrollView\n                style={styles.scrollView}\n                contentContainerStyle={styles.scrollViewContent}\n                showsVerticalScrollIndicator={false}\n              >\n                {children}\n              </ScrollView>\n            ) : (\n              <View style={styles.body}>\n                {children}\n              </View>\n            )}\n            \n            {/* Footer */}\n            {footer && (\n              <View style={[\n                styles.footer,\n                isDarkMode && styles.darkFooter,\n              ]}>\n                {footer}\n              </View>\n            )}\n          </Animated.View>\n        </Pressable>\n      </Pressable>\n    </Modal>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'flex-end',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n  },\n  darkContainer: {\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n  },\n  sheetContainer: {\n    backgroundColor: COLORS.cardBackground,\n    borderTopLeftRadius: 20,\n    borderTopRightRadius: 20,\n    minHeight: 200,\n    maxHeight: screenHeight * 0.85, // Slightly reduced to account for safe areas\n    width: '100%',\n    paddingBottom: Platform.OS === 'ios' ? 20 : 0, // Add padding for iOS home indicator\n  },\n  darkSheetContainer: {\n    backgroundColor: '#1E1E1E',\n  },\n  darkContent: {\n    backgroundColor: '#1E1E1E',\n  },\n  dragHandleContainer: {\n    width: '100%',\n    alignItems: 'center',\n    paddingVertical: 10,\n  },\n  dragHandle: {\n    width: 40,\n    height: 5,\n    borderRadius: 3,\n    backgroundColor: COLORS.border,\n  },\n  darkDragHandle: {\n    backgroundColor: '#3E3E3E',\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: SPACING.medium,\n    borderBottomWidth: 1,\n    borderBottomColor: COLORS.border,\n  },\n  title: {\n    ...TYPOGRAPHY.h3,\n    color: COLORS.text,\n    flex: 1,\n  },\n  darkText: {\n    color: '#FFFFFF',\n  },\n  closeButton: {\n    padding: 4,\n  },\n  scrollView: {\n    flex: 1,\n  },\n  scrollViewContent: {\n    padding: SPACING.medium,\n  },\n  body: {\n    flex: 1,\n    padding: SPACING.medium,\n  },\n  footer: {\n    padding: SPACING.medium,\n    borderTopWidth: 1,\n    borderTopColor: COLORS.border,\n  },\n  darkFooter: {\n    borderTopColor: '#2A2A2A',\n  },\n});\n\nexport default BottomSheet;\n", "language": "typescript"}, {"name": "components/ui/Button.tsx", "content": "/**\n * @magic_component: Button\n * @magic_platform: all\n * @magic_purpose: A customizable button component with different variants and states\n * @magic_category: UI/Inputs\n * @magic_keywords: button,press,action,input,touch\n * @magic_props: onPress,title,variant,disabled,loading,icon\n */\nimport React from 'react';\nimport {\n  TouchableOpacity,\n  Text,\n  StyleSheet,\n  ActivityIndicator,\n  View,\n  StyleProp,\n  ViewStyle,\n  TextStyle,\n} from 'react-native';\nimport { Feather } from '@expo/vector-icons';\nimport { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';\n\ntype ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';\n\ninterface ButtonProps {\n  title: string;\n  onPress: () => void;\n  variant?: ButtonVariant;\n  disabled?: boolean;\n  loading?: boolean;\n  icon?: keyof typeof Feather.glyphMap;\n  style?: StyleProp<ViewStyle>;\n  textStyle?: StyleProp<TextStyle>;\n  /**\n   * Whether to show ellipsis for long text (defaults to true)\n   */\n  ellipsis?: boolean;\n}\n\n/**\n * Button component that supports different variants, loading states, and icons\n * \n * @example\n * <Button \n *   title=\"Press Me\" \n *   onPress={() => {}} \n *   variant=\"primary\"\n *   icon=\"arrow-right\"\n * />\n */\nconst Button: React.FC<ButtonProps> = ({\n  title,\n  onPress,\n  variant = 'primary',\n  disabled = false,\n  loading = false,\n  icon,\n  style,\n  textStyle,\n  ellipsis = true,\n}) => {\n  // Determine button style based on variant\n  const getButtonStyle = () => {\n    switch (variant) {\n      case 'primary':\n        return styles.primaryButton;\n      case 'secondary':\n        return styles.secondaryButton;\n      case 'outline':\n        return styles.outlineButton;\n      case 'ghost':\n        return styles.ghostButton;\n      default:\n        return styles.primaryButton;\n    }\n  };\n\n  // Determine text style based on variant\n  const getTextStyle = () => {\n    switch (variant) {\n      case 'primary':\n        return styles.primaryText;\n      case 'secondary':\n        return styles.secondaryText;\n      case 'outline':\n        return styles.outlineText;\n      case 'ghost':\n        return styles.ghostText;\n      default:\n        return styles.primaryText;\n    }\n  };\n\n  return (\n    <TouchableOpacity\n      style={[\n        styles.button,\n        getButtonStyle(),\n        disabled && styles.disabledButton,\n        style,\n      ]}\n      onPress={onPress}\n      disabled={disabled || loading}\n      activeOpacity={0.7}\n    >\n      {loading ? (\n        <ActivityIndicator\n          size=\"small\"\n          color={variant === 'primary' ? COLORS.white : COLORS.primary}\n        />\n      ) : (\n        <View style={styles.content}>\n          {icon && (\n            <Feather\n              name={icon}\n              size={18}\n              color={\n                variant === 'primary'\n                  ? COLORS.white\n                  : variant === 'secondary'\n                  ? COLORS.white\n                  : COLORS.primary\n              }\n              style={styles.icon}\n            />\n          )}\n          <Text \n            style={[getTextStyle(), disabled && styles.disabledText, textStyle]}\n            numberOfLines={ellipsis ? 1 : undefined}\n            ellipsizeMode={ellipsis ? \"tail\" : undefined}\n          >\n            {title}\n          </Text>\n        </View>\n      )}\n    </TouchableOpacity>\n  );\n};\n\nconst styles = StyleSheet.create({\n  button: {\n    paddingVertical: SPACING.medium,\n    paddingHorizontal: SPACING.large,\n    borderRadius: BORDER_RADIUS.medium,\n    alignItems: 'center',\n    justifyContent: 'center',\n    minHeight: 48,\n  },\n  primaryButton: {\n    backgroundColor: COLORS.primary,\n  },\n  secondaryButton: {\n    backgroundColor: COLORS.secondary,\n  },\n  outlineButton: {\n    backgroundColor: COLORS.transparent,\n    borderWidth: 1,\n    borderColor: COLORS.primary,\n  },\n  ghostButton: {\n    backgroundColor: COLORS.transparent,\n  },\n  disabledButton: {\n    backgroundColor: COLORS.textTertiary,\n    borderColor: COLORS.textTertiary,\n  },\n  content: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  primaryText: {\n    ...TYPOGRAPHY.button,\n    color: COLORS.white,\n  },\n  secondaryText: {\n    ...TYPOGRAPHY.button,\n    color: COLORS.white,\n  },\n  outlineText: {\n    ...TYPOGRAPHY.button,\n    color: COLORS.primary,\n  },\n  ghostText: {\n    ...TYPOGRAPHY.button,\n    color: COLORS.primary,\n  },\n  disabledText: {\n    color: COLORS.white,\n  },\n  icon: {\n    marginRight: SPACING.small,\n  },\n});\n\nexport default Button;\n", "language": "typescript"}, {"name": "components/ui/Modal.tsx", "content": "/**\n * @magic_component: Modal\n * @magic_platform: all\n * @magic_purpose: A customizable modal component for displaying content on top of the screen\n * @magic_category: UI/Feedback\n * @magic_keywords: modal,dialog,popup,overlay\n * @magic_props: visible,onClose,title,children\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  Modal as RNModal,\n  TouchableOpacity,\n  Pressable,\n  ScrollView,\n  Dimensions,\n  Platform,\n} from 'react-native';\nimport { Feather } from '@expo/vector-icons';\nimport { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../constants/theme';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { logger } from '../../utils/logger';\n\n// Get screen dimensions\nconst { width: screenWidth, height: screenHeight } = Dimensions.get('window');\n\n/**\n * Modal props interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface ModalProps {\n  // Whether the modal is visible\n  visible: boolean;\n  \n  // Function to call when the modal is closed\n  onClose: () => void;\n  \n  // Title of the modal\n  title?: string;\n  \n  // Content of the modal\n  children: React.ReactNode;\n  \n  // Whether to close the modal when clicking outside\n  closeOnOverlayPress?: boolean;\n  \n  // Whether to show the close button\n  showCloseButton?: boolean;\n  \n  // Custom styles for the modal content\n  contentStyle?: any;\n  \n  // Custom styles for the modal container\n  containerStyle?: any;\n  \n  // Whether the modal content is scrollable\n  scrollable?: boolean;\n  \n  // Footer content for the modal\n  footer?: React.ReactNode;\n}\n\n/**\n * Modal component for displaying content on top of the screen\n * \n * @example\n * <Modal\n *   visible={isModalVisible}\n *   onClose={() => setIsModalVisible(false)}\n *   title=\"Modal Title\"\n * >\n *   <Text>Modal content goes here</Text>\n * </Modal>\n */\nconst Modal: React.FC<ModalProps> = ({\n  visible,\n  onClose,\n  title,\n  children,\n  closeOnOverlayPress = true,\n  showCloseButton = true,\n  contentStyle,\n  containerStyle,\n  scrollable = true,\n  footer,\n}) => {\n  // Get theme from context\n  const { isDarkMode, colors } = useTheme();\n  \n  // Handle closing the modal\n  const handleClose = () => {\n    logger.ui('Modal closed', { title });\n    onClose();\n  };\n  \n  // Handle overlay press\n  const handleOverlayPress = () => {\n    if (closeOnOverlayPress) {\n      handleClose();\n    }\n  };\n  \n  // Prevent propagation of touches on the modal content\n  const handleContentPress = (e: any) => {\n    e.stopPropagation();\n  };\n  \n  // Render the modal content\n  const renderContent = () => (\n    <View style={[\n      styles.content,\n      isDarkMode && styles.darkContent,\n      contentStyle,\n    ]}>\n      {/* Header */}\n      {(title || showCloseButton) && (\n        <View style={[styles.header, isDarkMode && styles.darkHeader]}>\n          {title && (\n            <Text style={[\n              styles.title,\n              isDarkMode && styles.darkText,\n            ]}>\n              {title}\n            </Text>\n          )}\n          \n          {showCloseButton && (\n            <TouchableOpacity\n              onPress={handleClose}\n              style={styles.closeButton}\n              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}\n            >\n              <Feather\n                name=\"x\"\n                size={24}\n                color={isDarkMode ? colors.textSecondary : colors.text}\n              />\n            </TouchableOpacity>\n          )}\n        </View>\n      )}\n      \n      {/* Body */}\n      {scrollable ? (\n        <ScrollView\n          style={styles.scrollView}\n          contentContainerStyle={[styles.scrollViewContent, isDarkMode && styles.darkBody]}\n        >\n          {children}\n        </ScrollView>\n      ) : (\n        <View style={[styles.body, isDarkMode && styles.darkBody]}>\n          {children}\n        </View>\n      )}\n      \n      {/* Footer */}\n      {footer && (\n        <View style={[\n          styles.footer,\n          isDarkMode && styles.darkFooter,\n        ]}>\n          {footer}\n        </View>\n      )}\n    </View>\n  );\n  \n  return (\n    <RNModal\n      visible={visible}\n      transparent\n      animationType=\"fade\"\n      onRequestClose={handleClose}\n    >\n      <Pressable onPress={handleOverlayPress} style={({ pressed }) => [\n        styles.container,\n        isDarkMode && styles.darkContainer,\n        containerStyle,\n      ]}>\n        <Pressable onPress={handleContentPress}>\n          {renderContent()}\n        </Pressable>\n      </Pressable>\n    </RNModal>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    padding: SPACING.large,\n  },\n  darkContainer: {\n    backgroundColor: 'rgba(0, 0, 0, 0.7)',\n  },\n  content: {\n    width: '90%',\n    maxWidth: Math.min(500, screenWidth * 0.9),\n    maxHeight: screenHeight * 0.8,\n    backgroundColor: COLORS.cardBackground,\n    borderRadius: 12,\n    overflow: 'hidden',\n    ...MIXINS.shadow,\n  },\n  darkContent: {\n    backgroundColor: '#1E1E1E',\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: SPACING.medium,\n    borderBottomWidth: 1,\n    borderBottomColor: COLORS.border,\n  },\n  darkHeader: {\n    borderBottomColor: '#2A2A2A',\n  },\n  title: {\n    ...TYPOGRAPHY.h3,\n    color: COLORS.text,\n    flex: 1,\n  },\n  darkText: {\n    color: '#FFFFFF',\n  },\n  closeButton: {\n    padding: 4,\n  },\n  scrollView: {\n    maxHeight: 400,\n  },\n  scrollViewContent: {\n    padding: SPACING.medium,\n  },\n  body: {\n    padding: SPACING.medium,\n  },\n  darkBody: {\n    backgroundColor: '#1E1E1E',\n  },\n  footer: {\n    padding: SPACING.medium,\n    borderTopWidth: 1,\n    borderTopColor: COLORS.border,\n  },\n  darkFooter: {\n    borderTopColor: '#2A2A2A',\n  },\n});\n\nexport default Modal;\n", "language": "typescript"}, {"name": "components/ui/Skeleton.tsx", "content": "/**\n * @magic_component: Skeleton\n * @magic_platform: all\n * @magic_purpose: A customizable skeleton loader for showing loading states\n * @magic_category: UI/Feedback\n * @magic_keywords: skeleton,loader,loading,placeholder,shimmer\n * @magic_props: width,height,borderRadius,style\n */\nimport React, { useEffect, useRef } from 'react';\nimport { View, StyleSheet, Animated, ViewStyle, StyleProp } from 'react-native';\nimport { COLORS } from '../../constants/theme';\n\ninterface SkeletonProps {\n  width?: number | string;\n  height?: number | string;\n  borderRadius?: number;\n  style?: StyleProp<ViewStyle>;\n  /**\n   * Whether to show the shimmer effect\n   * @default true\n   */\n  shimmer?: boolean;\n}\n\n/**\n * Skeleton component for displaying loading states\n * \n * Use this component to show loading placeholders before content is loaded\n * \n * @example\n * // Basic usage\n * <Skeleton width={200} height={20} />\n * \n * // Card skeleton\n * <View style={styles.card}>\n *   <Skeleton width=\"100%\" height={200} borderRadius={8} />\n *   <View style={styles.cardContent}>\n *     <Skeleton width=\"70%\" height={24} />\n *     <Skeleton width=\"100%\" height={16} style={{ marginTop: 8 }} />\n *     <Skeleton width=\"100%\" height={16} style={{ marginTop: 4 }} />\n *   </View>\n * </View>\n */\nconst Skeleton: React.FC<SkeletonProps> = ({\n  width = '100%',\n  height = 20,\n  borderRadius = 4,\n  style,\n  shimmer = true,\n}) => {\n  // Animation value for shimmer effect\n  const shimmerAnimation = useRef(new Animated.Value(0)).current;\n\n  // Start shimmer animation when component mounts\n  useEffect(() => {\n    if (shimmer) {\n      const shimmerLoop = Animated.loop(\n        Animated.timing(shimmerAnimation, {\n          toValue: 1,\n          duration: 1500,\n          useNativeDriver: false,\n        })\n      );\n      \n      shimmerLoop.start();\n      \n      return () => {\n        shimmerLoop.stop();\n      };\n    }\n  }, [shimmer, shimmerAnimation]);\n\n  // Interpolate animation value for shimmer gradient\n  const shimmerTranslate = shimmerAnimation.interpolate({\n    inputRange: [0, 1],\n    outputRange: [typeof width === 'number' ? -width : -100, typeof width === 'number' ? width : 100],\n  });\n\n  return (\n    <View\n      style={[\n        styles.container,\n        {\n          width: width as any, // Cast to any to avoid type issues\n          height: height as any, // Cast to any to avoid type issues\n          borderRadius,\n        },\n        style,\n      ]}\n    >\n      {shimmer && (\n        <Animated.View\n          style={[\n            styles.shimmer,\n            {\n              transform: [{ translateX: shimmerTranslate }],\n            },\n          ]}\n        />\n      )}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    backgroundColor: COLORS.border,\n    overflow: 'hidden',\n    position: 'relative',\n  },\n  shimmer: {\n    width: '100%',\n    height: '100%',\n    position: 'absolute',\n    backgroundColor: 'rgba(255, 255, 255, 0.3)',\n    transform: [{ translateX: -100 }],\n  },\n});\n\nexport default Skeleton;\n", "language": "typescript"}, {"name": "constants/lucideIcons.ts", "content": "/**\n * @magic_module: LucideIcons\n * @magic_purpose: List of supported Lucide React icons for use in the application\n * @magic_category: Constants\n * @magic_keywords: icons,lucide,svg,ui\n */\n\n/**\n * List of supported Lucide React icons\n * \n * IMPORTANT: When using icons in the application, only use icons from this list\n * to ensure compatibility and consistent styling.\n * \n * For more information, visit: https://lucide.dev/icons/\n */\nexport const LUCIDE_ICONS = [\n  // Essential UI icons\n  'activity',\n  'alert-circle',\n  'alert-triangle',\n  'archive',\n  'arrow-down',\n  'arrow-left',\n  'arrow-right',\n  'arrow-up',\n  'bell',\n  'bookmark',\n  'calendar',\n  'check',\n  'check-circle',\n  'chevron-down',\n  'chevron-left',\n  'chevron-right',\n  'chevron-up',\n  'clipboard',\n  'clock',\n  'copy',\n  'edit',\n  'external-link',\n  'eye',\n  'eye-off',\n  'file',\n  'filter',\n  'flag',\n  'folder',\n  'heart',\n  'help-circle',\n  'home',\n  'image',\n  'info',\n  'link',\n  'list',\n  'lock',\n  'log-in',\n  'log-out',\n  'mail',\n  'map',\n  'map-pin',\n  'menu',\n  'message-circle',\n  'message-square',\n  'minus',\n  'minus-circle',\n  'more-horizontal',\n  'more-vertical',\n  'paperclip',\n  'plus',\n  'plus-circle',\n  'refresh-cw',\n  'save',\n  'search',\n  'send',\n  'settings',\n  'share',\n  'shopping-bag',\n  'shopping-cart',\n  'star',\n  'sun',\n  'moon',\n  'tag',\n  'trash',\n  'trash-2',\n  'upload',\n  'user',\n  'users',\n  'x',\n  'x-circle',\n  'zap',\n  \n  // Device and tech icons\n  'battery',\n  'bluetooth',\n  'camera',\n  'cast',\n  'cloud',\n  'code',\n  'database',\n  'download',\n  'hard-drive',\n  'headphones',\n  'mic',\n  'monitor',\n  'phone',\n  'printer',\n  'server',\n  'smartphone',\n  'tablet',\n  'tv',\n  'wifi',\n  \n  // Media and content icons\n  'fast-forward',\n  'film',\n  'music',\n  'pause',\n  'play',\n  'play-circle',\n  'rewind',\n  'skip-back',\n  'skip-forward',\n  'stop-circle',\n  'video',\n  'volume',\n  'volume-1',\n  'volume-2',\n  'volume-x',\n  \n  // Social and communication icons\n  'facebook',\n  'github',\n  'instagram',\n  'linkedin',\n  'twitter',\n  'youtube',\n] as const;\n\n/**\n * Type for Lucide icon names\n * Use this type for icon props to ensure type safety\n */\nexport type LucideIconName = typeof LUCIDE_ICONS[number];\n\n/**\n * Helper function to check if an icon name is a valid Lucide icon\n * @param name The icon name to check\n * @returns Whether the icon is valid\n */\nexport const isValidLucideIcon = (name: string): name is LucideIconName => {\n  return LUCIDE_ICONS.includes(name as LucideIconName);\n};\n", "language": "typescript"}, {"name": "constants/theme.ts", "content": "/**\n * @magic_module: Theme\n * @magic_purpose: Defines the application's design system including colors, typography, spacing, and other UI constants\n * @magic_category: Constants\n * @magic_keywords: theme,colors,typography,spacing,design\n */\nimport { TextStyle } from 'react-native';\n\n// Color palette\nexport const COLORS = {\n  // Primary colors\n  primary: '#4A6FFF',\n  primaryLight: '#7B93FF',\n  primaryDark: '#3257E8',\n  \n  // Secondary colors\n  secondary: '#FF6B6B',\n  secondaryLight: '#FF9B9B',\n  secondaryDark: '#E54F4F',\n  \n  // Background colors\n  background: '#F9FAFC',\n  cardBackground: '#FFFFFF',\n  \n  // Text colors\n  text: '#1A1D26',\n  textSecondary: '#767A8A',\n  textTertiary: '#A0A4B8',\n  \n  // Status colors\n  success: '#4CAF50',\n  warning: '#FFC107',\n  error: '#F44336',\n  info: '#2196F3',\n  \n  // Utility colors\n  border: '#E5E9F2',\n  shadow: 'rgba(0, 0, 0, 0.05)',\n  overlay: 'rgba(0, 0, 0, 0.5)',\n  white: '#FFFFFF',\n  black: '#000000',\n  transparent: 'transparent',\n};\n\n// Font family\nexport const FONTS = {\n  regular: 'System',\n  medium: 'System',\n  bold: 'System',\n};\n\n// Typography\nexport const TYPOGRAPHY: Record<string, TextStyle> = {\n  h1: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    lineHeight: 34,\n    fontFamily: FONTS.bold,\n  },\n  h2: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    lineHeight: 30,\n    fontFamily: FONTS.bold,\n  },\n  h3: {\n    fontSize: 20,\n    fontWeight: 'bold',\n    lineHeight: 26,\n    fontFamily: FONTS.bold,\n  },\n  subtitle: {\n    fontSize: 16,\n    fontWeight: '600',\n    lineHeight: 22,\n    fontFamily: FONTS.medium,\n  },\n  body: {\n    fontSize: 14,\n    fontWeight: 'normal',\n    lineHeight: 20,\n    fontFamily: FONTS.regular,\n  },\n  caption: {\n    fontSize: 12,\n    fontWeight: 'normal',\n    lineHeight: 18,\n    fontFamily: FONTS.regular,\n  },\n  button: {\n    fontSize: 16,\n    fontWeight: '600',\n    lineHeight: 22,\n    fontFamily: FONTS.medium,\n  },\n};\n\n// Spacing scale\nexport const SPACING = {\n  tiny: 4,\n  small: 8,\n  medium: 16,\n  large: 24,\n  xlarge: 32,\n  xxlarge: 48,\n};\n\n// Border radius\nexport const BORDER_RADIUS = {\n  small: 4,\n  medium: 8,\n  large: 12,\n  xlarge: 16,\n  round: 999,\n};\n\n// Shadows\nexport const createShadow = (elevation: number = 1) => ({\n  shadowColor: COLORS.shadow,\n  shadowOffset: { width: 0, height: elevation * 2 },\n  shadowOpacity: 0.1,\n  shadowRadius: elevation * 3,\n  elevation,\n});\n\n// Common style mixins\nexport const MIXINS = {\n  row: {\n    flexDirection: 'row' as const,\n    alignItems: 'center' as const,\n  },\n  center: {\n    justifyContent: 'center' as const,\n    alignItems: 'center' as const,\n  },\n  shadow: createShadow(2),\n};\n", "language": "typescript"}, {"name": "contexts/AppContext.tsx", "content": "/**\n * @magic_component: AppContext\n * @magic_purpose: Global application context for managing app-wide state with persistence\n * @magic_category: Context/Global\n * @magic_keywords: context,state,global,persistence\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { logger } from '../utils/logger';\n\n/**\n * App state interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface AppState {\n  // Whether this is the first launch of the app\n  isFirstLaunch: boolean;\n  \n  // Add more app state properties here as needed\n  // Note: Dark mode is now managed by ThemeContext\n}\n\n/**\n * App context type interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface AppContextType {\n  // Current app state\n  appState: AppState;\n  \n  // Whether the app state is loading\n  loading: boolean;\n  \n  // Error message if any\n  error: string | null;\n  \n  // Set whether this is the first launch\n  setFirstLaunch: (isFirst: boolean) => Promise<void>;\n}\n\n// Create the context with undefined as default value\nconst AppContext = createContext<AppContextType | undefined>(undefined);\n\n// Initial state\nconst initialState: AppState = {\n  isFirstLaunch: true,\n};\n\n// Storage key\nconst STORAGE_KEY = 'app_state';\n\n/**\n * App context provider component\n * @magic_component: AppProvider\n * @magic_purpose: Provides global app state with persistence using AsyncStorage\n */\nexport const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [appState, setAppState] = useState<AppState>(initialState);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Load state from AsyncStorage on mount\n  useEffect(() => {\n    const loadState = async () => {\n      try {\n        setLoading(true);\n        const storedState = await AsyncStorage.getItem(STORAGE_KEY);\n        \n        if (storedState) {\n          setAppState(JSON.parse(storedState));\n        }\n      } catch (e) {\n        setError('Failed to load app state');\n        console.error('Failed to load app state:', e);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadState();\n  }, []);\n\n  // Save state to AsyncStorage whenever it changes\n  useEffect(() => {\n    const saveState = async () => {\n      try {\n        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(appState));\n      } catch (e) {\n        setError('Failed to save app state');\n        console.error('Failed to save app state:', e);\n      }\n    };\n\n    if (!loading) {\n      saveState();\n    }\n  }, [appState, loading]);\n\n  // Note: Dark mode is now managed by ThemeContext\n\n  // Update first launch setting\n  const setFirstLaunch = async (isFirst: boolean) => {\n    setAppState(prev => ({ ...prev, isFirstLaunch: isFirst }));\n  };\n\n  // Context value\n  const value: AppContextType = {\n    appState,\n    loading,\n    error,\n    setFirstLaunch,\n  };\n\n  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;\n};\n\n/**\n * Custom hook to use the app context\n * @magic_hook: useApp\n * @magic_purpose: Provides access to global app state and methods\n */\nexport const useApp = (): AppContextType => {\n  const context = useContext(AppContext);\n  \n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  \n  return context;\n};\n", "language": "typescript"}, {"name": "contexts/ThemeContext.tsx", "content": "/**\n * @magic_component: ThemeContext\n * @magic_purpose: Provides theme management for the application\n * @magic_category: Context/Theme\n * @magic_keywords: theme,dark mode,light mode,context,provider\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\nimport { COLORS } from '../constants/theme';\nimport { logger } from '../utils/logger';\n\n// Storage key for theme preference\nconst THEME_STORAGE_KEY = 'theme_preference';\n\n/**\n * Theme options\n * \n * IMPORTANT: We use string constants instead of union types to avoid breaking the app\n */\nexport const ThemeMode = {\n  LIGHT: 'light',\n  DARK: 'dark',\n  SYSTEM: 'system',\n};\n\n/**\n * Theme context state interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface ThemeContextState {\n  // Current theme mode (light, dark, or system)\n  themeMode: string;\n  \n  // Whether the current appearance is dark (true) or light (false)\n  isDarkMode: boolean;\n  \n  // Theme colors based on current mode\n  colors: {\n    primary: string;\n    primaryLight: string;\n    primaryDark: string;\n    secondary: string;\n    secondaryLight: string;\n    secondaryDark: string;\n    background: string;\n    cardBackground: string;\n    text: string;\n    textSecondary: string;\n    textTertiary: string;\n    border: string;\n    success: string;\n    warning: string;\n    error: string;\n    info: string;\n  };\n}\n\n/**\n * Theme context interface\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\ninterface ThemeContextValue extends ThemeContextState {\n  // Set the theme mode (light, dark, or system)\n  setThemeMode: (mode: string) => void;\n  \n  // Toggle between light and dark mode\n  toggleTheme: () => void;\n}\n\n// Create the context with a default value\nconst ThemeContext = createContext<ThemeContextValue | null>(null);\n\n/**\n * Theme provider component\n * \n * Provides theme management for the application\n */\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  // State for the theme mode\n  const [themeMode, setThemeModeState] = useState<string>(ThemeMode.SYSTEM);\n  \n  // State for whether the device is in dark mode\n  const [isDeviceDarkMode, setIsDeviceDarkMode] = useState<boolean>(false);\n  \n  // Calculate whether we're in dark mode based on theme mode and device setting\n  const isDarkMode = \n    themeMode === ThemeMode.DARK || \n    (themeMode === ThemeMode.SYSTEM && isDeviceDarkMode);\n  \n  // Load theme preference from storage on mount\n  useEffect(() => {\n    const loadThemePreference = async () => {\n      try {\n        const storedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);\n        if (storedTheme) {\n          setThemeModeState(storedTheme);\n          logger.info('Theme preference loaded', 'STATE', { themeMode: storedTheme });\n        }\n      } catch (error) {\n        logger.error('Failed to load theme preference', 'STATE', { error });\n      }\n    };\n    \n    loadThemePreference();\n  }, []);\n  \n  // Save theme preference when it changes\n  const setThemeMode = async (mode: string) => {\n    try {\n      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);\n      setThemeModeState(mode);\n      logger.info('Theme preference saved', 'STATE', { themeMode: mode });\n    } catch (error) {\n      logger.error('Failed to save theme preference', 'STATE', { error });\n    }\n  };\n  \n  // Toggle between light and dark mode\n  const toggleTheme = () => {\n    const newMode = isDarkMode ? ThemeMode.LIGHT : ThemeMode.DARK;\n    setThemeMode(newMode);\n    logger.ui('Theme toggled', { from: themeMode, to: newMode });\n  };\n  \n  // Get colors based on current theme\n  const colors = isDarkMode ? {\n    // Dark theme colors\n    primary: COLORS.primary,\n    primaryLight: COLORS.primaryLight,\n    primaryDark: COLORS.primaryDark,\n    secondary: COLORS.secondary,\n    secondaryLight: COLORS.secondaryLight,\n    secondaryDark: COLORS.secondaryDark,\n    background: '#121212',\n    cardBackground: '#1E1E1E',\n    text: '#FFFFFF',\n    textSecondary: '#A0A0A0',\n    textTertiary: '#6C6C6C',\n    border: '#2A2A2A',\n    success: COLORS.success,\n    warning: COLORS.warning,\n    error: COLORS.error,\n    info: COLORS.info,\n  } : {\n    // Light theme colors\n    primary: COLORS.primary,\n    primaryLight: COLORS.primaryLight,\n    primaryDark: COLORS.primaryDark,\n    secondary: COLORS.secondary,\n    secondaryLight: COLORS.secondaryLight,\n    secondaryDark: COLORS.secondaryDark,\n    background: COLORS.background,\n    cardBackground: COLORS.cardBackground,\n    text: COLORS.text,\n    textSecondary: COLORS.textSecondary,\n    textTertiary: COLORS.textTertiary,\n    border: COLORS.border,\n    success: COLORS.success,\n    warning: COLORS.warning,\n    error: COLORS.error,\n    info: COLORS.info,\n  };\n  \n  // Context value\n  const contextValue: ThemeContextValue = {\n    themeMode,\n    isDarkMode,\n    colors,\n    setThemeMode,\n    toggleTheme,\n  };\n  \n  return (\n    <ThemeContext.Provider value={contextValue}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\n/**\n * Hook to use the theme context\n * \n * IMPORTANT: This must be used within a ThemeProvider\n */\nexport const useTheme = (): ThemeContextValue => {\n  const context = useContext(ThemeContext);\n  \n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  \n  return context;\n};\n", "language": "typescript"}, {"name": "data/mockData.ts", "content": "/**\n * @magic_module: MockData\n * @magic_purpose: Provides mock data for the application to use during development\n * @magic_category: Data\n * @magic_keywords: mock,data,sample,development\n */\n\n// Product type definition\nexport interface Product {\n  id: string;\n  title: string;\n  description: string;\n  price: number;\n  imageUrl: string;\n  category: string;\n  rating: number;\n  reviews: number;\n  isNew?: boolean;\n  isFeatured?: boolean;\n}\n\n// User type definition\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  avatar: string;\n  preferences: {\n    darkMode: boolean;\n    notifications: boolean;\n  };\n}\n\n// Mock products data\nexport const mockProducts: Product[] = [\n  {\n    id: 'p1',\n    title: 'Modern Sofa',\n    description: 'A comfortable modern sofa with premium fabric and sturdy construction.',\n    price: 599.99,\n    imageUrl: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc',\n    category: 'furniture',\n    rating: 4.5,\n    reviews: 128,\n    isNew: true,\n    isFeatured: true,\n  },\n  {\n    id: 'p2',\n    title: 'Wooden Coffee Table',\n    description: 'Handcrafted wooden coffee table with a natural finish.',\n    price: 249.99,\n    imageUrl: 'https://images.unsplash.com/photo-1532372576444-dda954194ad0',\n    category: 'furniture',\n    rating: 4.3,\n    reviews: 86,\n    isNew: true,\n  },\n  {\n    id: 'p3',\n    title: 'Pendant Light',\n    description: 'Modern pendant light with adjustable height and warm lighting.',\n    price: 129.99,\n    imageUrl: 'https://images.unsplash.com/photo-1507473885765-e6ed057f782c',\n    category: 'lighting',\n    rating: 4.7,\n    reviews: 54,\n    isFeatured: true,\n  },\n  {\n    id: 'p4',\n    title: 'Ceramic Vase',\n    description: 'Handmade ceramic vase with a unique pattern and glossy finish.',\n    price: 79.99,\n    imageUrl: 'https://images.unsplash.com/photo-1612196808214-b7e239e5f6b7',\n    category: 'decor',\n    rating: 4.2,\n    reviews: 32,\n  },\n  {\n    id: 'p5',\n    title: 'Wool Rug',\n    description: 'Soft wool rug with a geometric pattern, perfect for living rooms.',\n    price: 349.99,\n    imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7',\n    category: 'textiles',\n    rating: 4.8,\n    reviews: 95,\n    isFeatured: true,\n  },\n];\n\n// Mock user data\nexport const mockUser: User = {\n  id: 'u1',\n  name: 'Alex Johnson',\n  email: '<EMAIL>',\n  avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde',\n  preferences: {\n    darkMode: false,\n    notifications: true,\n  },\n};\n\n// Function to get mock products\nexport const getMockProducts = (): Product[] => {\n  return mockProducts;\n};\n\n// Function to get a mock product by ID\nexport const getMockProductById = (id: string): Product | undefined => {\n  return mockProducts.find(product => product.id === id);\n};\n\n// Function to get featured products\nexport const getFeaturedProducts = (): Product[] => {\n  return mockProducts.filter(product => product.isFeatured);\n};\n\n// Function to get new products\nexport const getNewProducts = (): Product[] => {\n  return mockProducts.filter(product => product.isNew);\n};\n\n// Function to get mock user\nexport const getMockUser = (): User => {\n  return mockUser;\n};\n", "language": "typescript"}, {"name": "hooks/useFetch.ts", "content": "/**\n * @magic_hook: useFetch\n * @magic_purpose: Custom hook for data fetching with loading, error states and caching\n * @magic_category: Hooks/Data\n * @magic_keywords: fetch,api,data,loading,error,cache\n */\nimport { useState, useEffect, useCallback } from 'react';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\ninterface UseFetchOptions {\n  cacheKey?: string;\n  cacheDuration?: number; // in milliseconds\n  initialData?: any;\n  dependencies?: any[];\n}\n\ninterface UseFetchResult<T> {\n  data: T | null;\n  loading: boolean;\n  error: Error | null;\n  refetch: () => Promise<void>;\n}\n\n/**\n * Custom hook for data fetching with loading and error states\n * @param url The URL to fetch data from\n * @param options Configuration options for the fetch operation\n * @returns Object containing data, loading state, error state, and refetch function\n */\nfunction useFetch<T>(\n  url: string,\n  options: UseFetchOptions = {}\n): UseFetchResult<T> {\n  const {\n    cacheKey,\n    cacheDuration = 5 * 60 * 1000, // 5 minutes default\n    initialData = null,\n    dependencies = [],\n  } = options;\n\n  const [data, setData] = useState<T | null>(initialData);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<Error | null>(null);\n\n  // Function to fetch data\n  const fetchData = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      // Check cache first if cacheKey is provided\n      if (cacheKey) {\n        const cachedData = await AsyncStorage.getItem(cacheKey);\n        if (cachedData) {\n          const { data: cacheData, timestamp } = JSON.parse(cachedData);\n          const isExpired = Date.now() - timestamp > cacheDuration;\n          \n          if (!isExpired) {\n            setData(cacheData);\n            setLoading(false);\n            return;\n          }\n        }\n      }\n\n      // Fetch fresh data\n      const response = await fetch(url);\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! Status: ${response.status}`);\n      }\n      \n      const result = await response.json();\n      setData(result);\n      \n      // Cache the data if cacheKey is provided\n      if (cacheKey) {\n        await AsyncStorage.setItem(\n          cacheKey,\n          JSON.stringify({\n            data: result,\n            timestamp: Date.now(),\n          })\n        );\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err : new Error(String(err)));\n    } finally {\n      setLoading(false);\n    }\n  }, [url, cacheKey, cacheDuration, ...dependencies]);\n\n  // Fetch data on mount and when dependencies change\n  useEffect(() => {\n    fetchData();\n  }, [fetchData]);\n\n  // Function to manually refetch data\n  const refetch = useCallback(async () => {\n    await fetchData();\n  }, [fetchData]);\n\n  return { data, loading, error, refetch };\n}\n\nexport default useFetch;\n", "language": "typescript"}, {"name": "hooks/useNavigationLogger.ts", "content": "/**\n * @magic_hook: useNavigationLogger\n * @magic_purpose: Tracks and logs navigation changes throughout the app\n * @magic_category: Hooks/Navigation\n * @magic_keywords: navigation,logger,tracking,analytics,events\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport { useRef, useEffect } from 'react';\nimport { NavigationContainerRef, NavigationState, PartialState } from '@react-navigation/native';\nimport { logger } from '../utils/logger';\n\n/**\n * Route information interface\n */\ninterface RouteInfo {\n  routeName: string;\n  params?: Record<string, any>;\n  key: string;\n  childRoute?: RouteInfo | null;\n}\n\n/**\n * Options for the navigation logger\n */\ninterface NavigationLoggerOptions {\n  // Enable or disable navigation logging\n  enabled?: boolean;\n  \n  // Custom identifier for this navigation container\n  containerId?: string;\n  \n  // Additional metadata to include with all navigation logs\n  metadata?: Record<string, any>;\n}\n\n/**\n * Hook that tracks and logs navigation changes\n * \n * This hook registers listeners for navigation state changes and logs them\n * using the structured logger. It captures screen transitions, params, and timing.\n * \n * @param navigationRef Reference to the navigation container\n * @param options Configuration options for the logger\n */\nexport function useNavigationLogger(\n  navigationRef: React.RefObject<NavigationContainerRef<any>>,\n  options: NavigationLoggerOptions = {}\n): void {\n  // Default options\n  const {\n    enabled = true,\n    containerId = 'main',\n    metadata = {}\n  } = options;\n  \n  // Store the previous state for comparison\n  const previousStateRef = useRef<NavigationState | PartialState<NavigationState> | undefined>(undefined);\n  \n  // Track navigation timing\n  const navigationTimingRef = useRef<Record<string, number>>({});\n  \n  useEffect(() => {\n    if (!enabled || !navigationRef.current) {\n      return;\n    }\n    \n    // Function to extract route information\n    const getRouteInfo = (state: NavigationState | PartialState<NavigationState> | undefined): RouteInfo | null => {\n      if (!state) return null;\n      \n      const routes = state.routes;\n      const index = state.index ?? 0;\n      \n      if (!routes || index >= routes.length) return null;\n      \n      const currentRoute = routes[index];\n      const routeKey = currentRoute.key || `route-${currentRoute.name}-${Date.now()}`;\n      \n      const result: RouteInfo = {\n        routeName: currentRoute.name,\n        params: currentRoute.params,\n        key: routeKey\n      };\n      \n      // If the route has a nested navigator, recursively get its active route\n      if (currentRoute.state) {\n        result.childRoute = getRouteInfo(currentRoute.state);\n      }\n      \n      return result;\n    };\n    \n    // Log navigation state changes\n    const handleStateChange = () => {\n      if (!navigationRef.current) return;\n      \n      const currentState = navigationRef.current.getRootState();\n      const previousState = previousStateRef.current;\n      \n      const currentRouteInfo = getRouteInfo(currentState);\n      const previousRouteInfo = getRouteInfo(previousState);\n      \n      if (!currentRouteInfo) return;\n      \n      const timestamp = Date.now();\n      const routeName = currentRouteInfo.routeName;\n      \n      // Calculate time spent on previous screen\n      let timeSpent = 0;\n      if (previousRouteInfo && navigationTimingRef.current[previousRouteInfo.key]) {\n        timeSpent = timestamp - navigationTimingRef.current[previousRouteInfo.key];\n      }\n      \n      // Store timestamp for current route\n      navigationTimingRef.current[currentRouteInfo.key] = timestamp;\n      \n      // Log the navigation event\n      logger.navigation('Screen changed', 'NAVIGATION', {\n        from: previousRouteInfo?.routeName || 'Unknown',\n        to: routeName,\n        params: currentRouteInfo.params || {},\n        timeSpentOnPreviousScreen: timeSpent,\n        timestamp,\n        containerId,\n        ...metadata\n      });\n      \n      // Update previous state\n      previousStateRef.current = currentState;\n    };\n    \n    // Log initial state\n    const handleReady = () => {\n      if (!navigationRef.current) return;\n      \n      const currentState = navigationRef.current.getRootState();\n      const currentRouteInfo = getRouteInfo(currentState);\n      \n      if (currentRouteInfo) {\n        navigationTimingRef.current[currentRouteInfo.key] = Date.now();\n        \n        logger.navigation('Navigation initialized', 'NAVIGATION', {\n          initialRoute: currentRouteInfo.routeName,\n          params: currentRouteInfo.params || {},\n          timestamp: Date.now(),\n          containerId,\n          ...metadata\n        });\n        \n        previousStateRef.current = currentState;\n      }\n    };\n    \n    // Register listeners\n    const unsubscribeReady = navigationRef.current.addListener('ready', handleReady);\n    const unsubscribeStateChange = navigationRef.current.addListener('state', handleStateChange);\n    \n    // Clean up listeners\n    return () => {\n      unsubscribeReady();\n      unsubscribeStateChange();\n    };\n  }, [enabled, navigationRef, containerId, metadata]);\n}\n\nexport default useNavigationLogger;\n", "language": "typescript"}, {"name": "hooks/useNavigationWatcher.ts", "content": "/**\n * @magic_hook: useNavigationWatcher\n * @magic_purpose: Simplified centralized navigation tracking\n */\nimport { useRef, useEffect } from 'react';\nimport { NavigationContainerRef, NavigationState } from '@react-navigation/native';\nimport { logger } from '../utils/logger';\n\n/**\n * A simplified hook for tracking navigation state changes\n * \n * @param navigationRef Reference to the navigation container\n * @param options Optional configuration\n */\nexport function useNavigationWatcher(\n  navigationRef: React.RefObject<NavigationContainerRef<any>>,\n  options: { disabled?: boolean } = {}\n): void {\n  const { disabled = false } = options;\n  const prevRouteRef = useRef<string | null>(null);\n  \n  useEffect(() => {\n    if (disabled || !navigationRef.current) {\n      return;\n    }\n    \n    // Log initial navigation state\n    const handleReady = () => {\n      if (!navigationRef.current) return;\n      \n      const state = navigationRef.current.getRootState();\n      const currentRouteName = getCurrentRouteName(state);\n      \n      if (currentRouteName) {\n        logger.info(`Navigation initialized at: ${currentRouteName}`, 'NAVIGATION');\n        prevRouteRef.current = currentRouteName;\n      }\n    };\n    \n    // Log navigation state changes\n    const handleStateChange = () => {\n      if (!navigationRef.current) return;\n      \n      const state = navigationRef.current.getRootState();\n      const currentRouteName = getCurrentRouteName(state);\n      \n      if (currentRouteName && prevRouteRef.current !== currentRouteName) {\n        logger.info(`Screen changed: ${prevRouteRef.current || 'Unknown'} → ${currentRouteName}`, 'NAVIGATION');\n        prevRouteRef.current = currentRouteName;\n      }\n    };\n    \n    // Helper to extract current route name from state\n    function getCurrentRouteName(state: NavigationState | undefined): string | null {\n      if (!state) return null;\n      \n      const route = state.routes[state.index || 0];\n      if (route.state) {\n        return getCurrentRouteName(route.state as NavigationState);\n      }\n      return route.name;\n    }\n    \n    // Register listeners\n    const unsubscribeReady = navigationRef.current.addListener('ready', handleReady);\n    const unsubscribeStateChange = navigationRef.current.addListener('state', handleStateChange);\n    \n    // Clean up listeners\n    return () => {\n      unsubscribeReady();\n      unsubscribeStateChange();\n    };\n  }, [disabled, navigationRef]);\n}\n\nexport default useNavigationWatcher;\n", "language": "typescript"}, {"name": "libs/supabase.ts", "content": "/**\n * @magic_module: SupabaseClient\n * @magic_purpose: Provides a configured Supabase client for database access\n * @magic_category: Database\n * @magic_keywords: supabase,database,auth,storage\n * \n * IMPORTANT: DO NOT MODIFY OR USE THIS FILE UNTIL SUPABASE IS CONNECTED TO THE PROJECT\n * \n * This file contains the configuration for connecting to a Supabase backend.\n * It is currently set up with empty credentials and should not be used until\n * proper Supabase credentials are provided and the connection is established.\n * \n * NOTE TO AI: Importing 'react-native-url-polyfill/auto' is NOT needed for Supabase to work.\n * Do not add this import when implementing Supabase functionality.\n */\nimport { createClient } from '@supabase/supabase-js';\n\n// Supabase URL and anon key - DO NOT USE UNTIL PROPERLY CONFIGURED\n// Replace these with your actual Supabase credentials when ready to connect\nconst supabaseUrl = '';\nconst supabaseAnonKey = '';\n\n/**\n * IMPORTANT: THIS CLIENT IS NOT READY FOR USE\n * \n * This Supabase client is configured with empty credentials and should not be used\n * until proper credentials are provided and the connection is established.\n * \n * When ready to use Supabase:\n * 1. Create a Supabase project at https://supabase.com\n * 2. Get your project URL and anon key from the project settings\n * 3. Update the credentials in this file\n * 4. Remove the warning comments\n */\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n/**\n * Example usage (DO NOT USE UNTIL PROPERLY CONFIGURED):\n * \n * // Fetch data from a table\n * const { data, error } = await supabase\n *   .from('your_table')\n *   .select('*');\n * \n * // Insert data into a table\n * const { data, error } = await supabase\n *   .from('your_table')\n *   .insert([{ column1: 'value1', column2: 'value2' }]);\n * \n * // Authentication\n * const { user, session, error } = await supabase.auth.signIn({\n *   email: '<EMAIL>',\n *   password: 'password123',\n * });\n */\n\nexport default supabase;\n", "language": "typescript"}, {"name": "navigation/TabNavigator.tsx", "content": "/**\n * @magic_component: TabNavigator\n * @magic_purpose: Bottom tab navigation for the main app screens\n * @magic_category: Navigation\n * @magic_keywords: tabs,navigation,bottom tabs\n *\n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React from 'react';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { Feather } from '@expo/vector-icons';\n\nimport { Platform } from 'react-native';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\n\n// Import screens\nimport HomeTab from '../screens/tabs/HomeTab';\nimport ExploreTab from '../screens/tabs/ExploreTab';\nimport ProfileTab from '../screens/tabs/ProfileTab';\n\n// Import types and theme\nimport { TabParamList } from './types';\nimport { COLORS } from '../constants/theme';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { logger } from '../utils/logger';\n\n// Create tab navigator\nconst Tab = createBottomTabNavigator<TabParamList>();\n\n/**\n * TabNavigator component that handles the bottom tab navigation\n *\n * This navigator includes:\n * - Home tab\n * - Explore tab\n * - Profile tab\n *\n * Each tab has its own icon and label, and the active tab is highlighted.\n */\nexport default function TabNavigator() {\n    // Get theme from context\n    const { isDarkMode, colors } = useTheme();\n\n    // We don't need to manually handle insets - React Navigation does this for us\n\n    // Log when tab navigator is created\n    React.useEffect(() => {\n        logger.info('Tab navigator created', 'NAVIGATION', {\n            timestamp: new Date().toISOString(),\n            theme: isDarkMode ? 'dark' : 'light'\n        });\n    }, [isDarkMode]);\n\n    return (\n        <Tab.Navigator\n            screenOptions={({ route }) => ({\n                tabBarIcon: ({ focused, color, size }) => {\n                    // Set icon based on route name\n                    if (route.name === 'Home') {\n                        return <Feather name=\"home\" size={size} color={color} />;\n                    } else if (route.name === 'Explore') {\n                        return <Feather name=\"map\" size={size} color={color} />;\n                    } else if (route.name === 'Profile') {\n                        return <Feather name=\"user\" size={size} color={color} />;\n                    }\n\n                    // Default icon\n                    return <Feather name=\"circle\" size={size} color={color} />;\n                },\n                tabBarActiveTintColor: colors.primary,\n                tabBarInactiveTintColor: isDarkMode ? colors.textSecondary : colors.textTertiary,\n                // Let React Navigation handle the tab bar styling and safe areas\n                // This matches how Airbnb and other professional apps handle it\n                headerShown: false,\n            })}\n        >\n            <Tab.Screen\n                name=\"Home\"\n                component={HomeTab}\n            />\n            <Tab.Screen\n                name=\"Explore\"\n                component={ExploreTab}\n            />\n            <Tab.Screen\n                name=\"Profile\"\n                component={ProfileTab}\n            />\n        </Tab.Navigator>\n    );\n}\n", "language": "typescript"}, {"name": "navigation/index.tsx", "content": "/**\n * @magic_component: Navigation\n * @magic_purpose: Main navigation container that sets up the app's navigation structure\n * @magic_category: Navigation\n * @magic_keywords: navigation,router,stack\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useRef, useEffect } from 'react';\nimport { NavigationContainer, DefaultTheme, DarkTheme, NavigationContainerRef } from '@react-navigation/native';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\n\nimport { Platform, Text, ActivityIndicator, View } from 'react-native';\n\n// Import navigators\nimport TabNavigator from './TabNavigator';\n\n// Import screens\n\nimport ModalExampleScreen from '../screens/modals/ModalExampleScreen';\nimport BottomSheetExampleScreen from '../screens/modals/BottomSheetExampleScreen';\n\n// Import types and utilities\nimport { RootStackParamList } from './types';\nimport { COLORS } from '../constants/theme';\nimport { useTheme } from '../contexts/ThemeContext';\n\n\n// Import hooks\nimport useNavigationWatcher from '../hooks/useNavigationWatcher';\nimport { logger } from '../utils/logger';\n\n// Create navigator\nconst Stack = createNativeStackNavigator<RootStackParamList>();\n\n/**\n * Main navigation component that sets up the app's navigation structure\n * \n * We're using a simple stack navigator with a single screen (HomeScreen)\n * that implements its own custom tab navigation UI.\n * \n * IMPORTANT: We avoid union types to prevent app breakage\n */\nconst Navigation = () => {\n  // Use the theme from ThemeContext\n  const { isDarkMode, colors } = useTheme();\n  \n  // Create a navigation reference for the navigation logger\n  const navigationRef = useRef<NavigationContainerRef<RootStackParamList>>(null);\n  \n  // Create a theme that extends the default navigation theme\n  const navigationTheme = {\n    ...(isDarkMode ? DarkTheme : DefaultTheme),\n    colors: {\n      ...(isDarkMode ? DarkTheme.colors : DefaultTheme.colors),\n      primary: colors.primary,\n      background: colors.background,\n      card: colors.cardBackground,\n      text: colors.text,\n      border: colors.border,\n    },\n  };\n\n  // Initialize the simplified navigation watcher\n  useNavigationWatcher(navigationRef);\n  \n  // Log when navigation container is created\n  logger.info('Navigation container created', 'NAVIGATION', {\n    timestamp: new Date().toISOString(),\n    theme: isDarkMode ? 'dark' : 'light'\n  });\n  \n  // Handle URL changes for web platform is now managed by the navigation watcher\n  \n  return (\n    <NavigationContainer \n      ref={navigationRef}\n      theme={navigationTheme}\n\n      // This is the key fix for iOS tab bar spacing\n      documentTitle={{\n        formatter: (options, route) =>\n          options?.title ?? route?.name ?? 'Magically',\n      }}\n\n      fallback={<View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><ActivityIndicator size=\"large\" color={colors.primary} /><Text style={{ marginTop: 10, color: colors.text }}>Loading...</Text></View>}\n    >\n      <Stack.Navigator\n        screenOptions={{\n          headerShown: false,\n        }}\n      >\n        {/* Main Tab Navigator */}\n        <Stack.Screen name=\"Main\" component={TabNavigator} />\n        \n        {/* Modal Screens */}\n\n        <Stack.Screen \n          name=\"ModalExample\" \n          component={ModalExampleScreen} \n          options={{\n            presentation: 'modal',\n            animation: 'slide_from_bottom',\n          }}\n        />\n        <Stack.Screen \n          name=\"BottomSheetExample\" \n          component={BottomSheetExampleScreen} \n          options={{\n            presentation: 'transparentModal',\n            animation: 'slide_from_bottom',\n            contentStyle: { backgroundColor: 'transparent' },\n          }}\n        />\n        {/* Add additional screens here when they are created */}\n      </Stack.Navigator>\n    </NavigationContainer>\n  );\n};\n\nexport default Navigation;\n", "language": "typescript"}, {"name": "navigation/types.ts", "content": "/**\n * @magic_module: NavigationTypes\n * @magic_purpose: Defines TypeScript types for the navigation system\n * @magic_category: Navigation\n * @magic_keywords: navigation,types,params,routes\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport { NavigatorScreenParams } from '@react-navigation/native';\nimport { NativeStackNavigationProp } from '@react-navigation/native-stack';\nimport { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';\nimport { CompositeNavigationProp } from '@react-navigation/native';\n\n// Main tab navigation parameters\nexport type TabParamList = {\n  Home: undefined;\n  Explore: undefined;\n  Profile: undefined;\n};\n\n// Main stack navigation parameters\nexport type RootStackParamList = {\n  // Main tab navigator\n  Main: undefined;\n  \n  // Modal screens\n  ModalExample: undefined;\n  BottomSheetExample: undefined;\n  ProductDetail: { productId: string; title: string };\n  Settings: undefined;\n};\n\n// Type for useNavigation hook - use this for the HomeScreen\nexport type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;\n\nexport type ExploreScreenNavigationProp = CompositeNavigationProp<\n  BottomTabNavigationProp<TabParamList, 'Explore'>,\n  NativeStackNavigationProp<RootStackParamList>\n>;\n\nexport type ProfileScreenNavigationProp = CompositeNavigationProp<\n  BottomTabNavigationProp<TabParamList, 'Profile'>,\n  NativeStackNavigationProp<RootStackParamList>\n>;\n\n// Type for useNavigation hook - use this for stack screens\nexport type RootStackNavigationProp = NativeStackNavigationProp<RootStackParamList>;\n", "language": "typescript"}, {"name": "screens/modals/BottomSheetExampleScreen.tsx", "content": "/**\n * @magic_component: BottomSheetExampleScreen\n * @magic_purpose: Example bottom sheet screen using React Navigation's modal presentation\n * @magic_category: Screens/Modals\n * @magic_keywords: bottom sheet,modal,screen,example\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useEffect, useRef } from 'react';\nimport { \n  View, \n  Text, \n  StyleSheet, \n  TouchableOpacity,\n  Pressable,\n  Animated,\n  Dimensions,\n  Platform\n} from 'react-native';\nimport { Feather } from '@expo/vector-icons';\nimport { useNavigation } from '@react-navigation/native';\nimport { useSafeAreaInsets } from 'react-native-safe-area-context';\nimport { logger } from '../../utils/logger';\n\n// Import components and utilities\nimport Button from '../../components/ui/Button';\nimport { COLORS, SPACING, TYPOGRAPHY } from '../../constants/theme';\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Get screen dimensions\nconst { height: screenHeight } = Dimensions.get('window');\n\n/**\n * BottomSheetExampleScreen component\n * \n * This is a proper bottom sheet screen using React Navigation's modal presentation\n * instead of a custom bottom sheet component.\n */\nexport default function BottomSheetExampleScreen() {\n  // Navigation\n  const navigation = useNavigation();\n  \n  // Use theme from ThemeContext\n  const { isDarkMode, colors } = useTheme();\n  \n  // Get safe area insets\n  const insets = useSafeAreaInsets();\n  \n  // Animation value for the bottom sheet\n  const translateY = useRef(new Animated.Value(screenHeight)).current;\n  \n  // Animate in on mount\n  useEffect(() => {\n    logger.ui('Bottom sheet screen opened', { screen: 'BottomSheetExample' });\n    \n    Animated.timing(translateY, {\n      toValue: 0,\n      duration: 300,\n      useNativeDriver: true,\n    }).start();\n  }, []);\n  \n  // Handle close\n  const handleClose = () => {\n    logger.ui('Bottom sheet screen closed', { screen: 'BottomSheetExample' });\n    \n    Animated.timing(translateY, {\n      toValue: screenHeight,\n      duration: 300,\n      useNativeDriver: true,\n    }).start(() => {\n      navigation.goBack();\n    });\n  };\n  \n  return (\n    <View style={[styles.container, isDarkMode && styles.darkContainer]}>\n      {/* Semi-transparent backdrop - press to close */}\n      <Pressable \n        style={styles.backdrop} \n        onPress={handleClose}\n      />\n      \n      {/* Bottom Sheet */}\n      <Animated.View \n        style={[\n          styles.bottomSheet, \n          isDarkMode && styles.darkBottomSheet,\n          { transform: [{ translateY }] },\n          { paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0 }\n        ]}\n      >\n        {/* Drag Handle */}\n        <View style={styles.dragHandleContainer}>\n          <View style={[styles.dragHandle, isDarkMode && styles.darkDragHandle]} />\n        </View>\n        \n        {/* Header */}\n        <View style={styles.header}>\n          <Text style={[styles.title, isDarkMode && styles.darkText]}>\n            Bottom Sheet Example\n          </Text>\n          \n          <TouchableOpacity\n            onPress={handleClose}\n            style={styles.closeButton}\n            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}\n          >\n            <Feather\n              name=\"x\"\n              size={24}\n              color={isDarkMode ? colors.textSecondary : colors.text}\n            />\n          </TouchableOpacity>\n        </View>\n        \n        {/* Content */}\n        <View style={styles.content}>\n          <Text style={[styles.description, isDarkMode && styles.darkTextSecondary]}>\n            This is a bottom sheet screen using React Navigation's modal presentation.\n            It slides up from the bottom and can be dismissed by tapping the backdrop or close button.\n          </Text>\n          \n          <View style={styles.iconContainer}>\n            <Feather\n              name=\"arrow-up\"\n              size={64}\n              color={colors.primary}\n            />\n          </View>\n          \n          <Text style={[styles.infoText, isDarkMode && styles.darkTextSecondary]}>\n            Bottom sheets are perfect for contextual actions, filters, or additional information\n            that doesn't need a full screen.\n          </Text>\n          \n          <Button\n            title=\"Close Bottom Sheet\"\n            onPress={handleClose}\n            style={styles.button}\n            icon=\"x\"\n          />\n        </View>\n      </Animated.View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'flex-end',\n    backgroundColor: 'transparent',\n  },\n  darkContainer: {\n    backgroundColor: 'transparent',\n  },\n  backdrop: {\n    ...StyleSheet.absoluteFillObject,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n  },\n  bottomSheet: {\n    backgroundColor: COLORS.cardBackground,\n    borderTopLeftRadius: 20,\n    borderTopRightRadius: 20,\n    minHeight: 300,\n    maxHeight: screenHeight * 0.85,\n    width: '100%',\n  },\n  darkBottomSheet: {\n    backgroundColor: '#1E1E1E',\n  },\n  dragHandleContainer: {\n    width: '100%',\n    alignItems: 'center',\n    paddingVertical: 10,\n  },\n  dragHandle: {\n    width: 40,\n    height: 5,\n    borderRadius: 3,\n    backgroundColor: COLORS.border,\n  },\n  darkDragHandle: {\n    backgroundColor: '#3E3E3E',\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: SPACING.medium,\n    borderBottomWidth: 1,\n    borderBottomColor: COLORS.border,\n  },\n  title: {\n    ...TYPOGRAPHY.h3,\n    color: COLORS.text,\n  },\n  closeButton: {\n    padding: 4,\n  },\n  content: {\n    padding: SPACING.large,\n    alignItems: 'center',\n  },\n  description: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    textAlign: 'center',\n    marginBottom: SPACING.large,\n  },\n  iconContainer: {\n    width: 120,\n    height: 120,\n    borderRadius: 60,\n    backgroundColor: COLORS.cardBackground,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginVertical: SPACING.large,\n    borderWidth: 1,\n    borderColor: COLORS.border,\n  },\n  infoText: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    textAlign: 'center',\n    marginBottom: SPACING.xlarge,\n  },\n  button: {\n    width: 200,\n  },\n  darkText: {\n    color: COLORS.white,\n  },\n  darkTextSecondary: {\n    color: '#A0A0A0',\n  },\n});\n", "language": "typescript"}, {"name": "screens/modals/ModalExampleScreen.tsx", "content": "/**\n * @magic_component: ModalExampleScreen\n * @magic_purpose: Example modal screen using React Navigation's modal presentation\n * @magic_category: Screens/Modals\n * @magic_keywords: modal,screen,example\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React from 'react';\nimport { \n  View, \n  Text, \n  StyleSheet, \n  SafeAreaView,\n  TouchableOpacity\n} from 'react-native';\nimport { Feather } from '@expo/vector-icons';\nimport { useNavigation } from '@react-navigation/native';\nimport { logger } from '../../utils/logger';\n\n// Import components and utilities\nimport Button from '../../components/ui/Button';\nimport { COLORS, SPACING, TYPOGRAPHY } from '../../constants/theme';\nimport { useTheme } from '../../contexts/ThemeContext';\n\n/**\n * ModalExampleScreen component\n * \n * This is a proper modal screen using React Navigation's modal presentation\n * instead of a custom modal component.\n */\nexport default function ModalExampleScreen() {\n  // Navigation\n  const navigation = useNavigation();\n  \n  // Use theme from ThemeContext\n  const { isDarkMode, colors } = useTheme();\n  \n  // Handle close\n  const handleClose = () => {\n    logger.ui('Modal screen closed', { screen: 'ModalExample' });\n    navigation.goBack();\n  };\n  \n  return (\n    <SafeAreaView style={[styles.container, isDarkMode && styles.darkContainer]}>\n      <View style={styles.header}>\n        <Text style={[styles.title, isDarkMode && styles.darkText]}>\n          Modal Example\n        </Text>\n        \n        <TouchableOpacity\n          onPress={handleClose}\n          style={styles.closeButton}\n          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}\n        >\n          <Feather\n            name=\"x\"\n            size={24}\n            color={isDarkMode ? colors.textSecondary : colors.text}\n          />\n        </TouchableOpacity>\n      </View>\n      \n      <View style={styles.content}>\n        <Text style={[styles.description, isDarkMode && styles.darkTextSecondary]}>\n          This is a modal screen using React Navigation's modal presentation.\n          It's a full screen that slides up from the bottom, following platform conventions.\n        </Text>\n        \n        <View style={styles.iconContainer}>\n          <Feather\n            name=\"layers\"\n            size={64}\n            color={colors.primary}\n          />\n        </View>\n        \n        <Text style={[styles.infoText, isDarkMode && styles.darkTextSecondary]}>\n          Modal screens are great for temporary focused tasks that don't fit into the main navigation flow.\n        </Text>\n        \n        <Button\n          title=\"Close Modal\"\n          onPress={handleClose}\n          style={styles.button}\n          icon=\"x\"\n        />\n      </View>\n    </SafeAreaView>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: COLORS.background,\n  },\n  darkContainer: {\n    backgroundColor: '#121212',\n  },\n  header: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: SPACING.medium,\n    borderBottomWidth: 1,\n    borderBottomColor: COLORS.border,\n  },\n  title: {\n    ...TYPOGRAPHY.h2,\n    color: COLORS.text,\n  },\n  closeButton: {\n    padding: 4,\n  },\n  content: {\n    flex: 1,\n    padding: SPACING.large,\n    alignItems: 'center',\n  },\n  description: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    textAlign: 'center',\n    marginBottom: SPACING.large,\n  },\n  iconContainer: {\n    width: 120,\n    height: 120,\n    borderRadius: 60,\n    backgroundColor: COLORS.cardBackground,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginVertical: SPACING.large,\n  },\n  infoText: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    textAlign: 'center',\n    marginBottom: SPACING.xlarge,\n  },\n  button: {\n    width: 200,\n  },\n  darkText: {\n    color: COLORS.white,\n  },\n  darkTextSecondary: {\n    color: '#A0A0A0',\n  },\n});\n", "language": "typescript"}, {"name": "screens/tabs/ExploreTab.tsx", "content": "/**\n * @magic_component: ExploreTab\n * @magic_purpose: Explore tab screen showing discovery content\n * @magic_category: Screens/Tabs\n * @magic_keywords: explore,discover,tab,screen\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useState, useEffect } from 'react';\nimport { \n  View, \n  Text, \n  StyleSheet, \n  ScrollView, \n  RefreshControl,\n  Dimensions\n} from 'react-native';\nimport { Feather } from '@expo/vector-icons';\nimport { useNavigation } from '@react-navigation/native';\nimport { logger } from '../../utils/logger';\n\n// Import components and utilities\nimport HomeHeader from '../../components/screens/HomeScreen/HomeHeader';\nimport Button from '../../components/ui/Button';\nimport Skeleton from '../../components/ui/Skeleton';\nimport { useApp } from '../../contexts/AppContext';\nimport { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';\nimport { ExploreScreenNavigationProp } from '../../navigation/types';\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Get screen dimensions for responsive layouts\nconst { width: screenWidth } = Dimensions.get('window');\n\n/**\n * ExploreTab component showing discovery content\n */\nexport default function ExploreTab() {\n  // Navigation\n  const navigation = useNavigation<ExploreScreenNavigationProp>();\n  \n  // Use theme from ThemeContext\n  const { isDarkMode, colors } = useTheme();\n  \n  // App context - used for non-theme related state\n  const { appState } = useApp();\n  \n  // State\n  const [isLoading, setIsLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  \n  // Simulate loading data\n  useEffect(() => {\n    // Simulate API loading delay\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 1500);\n    \n    return () => clearTimeout(timer);\n  }, []);\n  \n  // Handle refresh\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    // Simulate refresh delay\n    setTimeout(() => {\n      setRefreshing(false);\n    }, 1000);\n  };\n  \n  return (\n    <View style={[styles.container, isDarkMode && styles.darkContainer]}>\n      {/* Header */}\n      <HomeHeader title=\"Explore\" />\n      \n      <ScrollView\n        style={styles.scrollView}\n        contentContainerStyle={styles.scrollViewContent}\n        refreshControl={\n          <RefreshControl\n            refreshing={refreshing}\n            onRefresh={handleRefresh}\n            colors={[colors.primary]}\n            tintColor={colors.primary}\n          />\n        }\n      >\n        <View style={styles.tabContent}>\n          <Text style={[styles.sectionTitle, isDarkMode && styles.darkText]}>\n            Discover\n          </Text>\n          \n          <Text style={[styles.sectionDescription, isDarkMode && styles.darkTextSecondary]}>\n            Explore new content and features in this section.\n          </Text>\n          \n          {/* Content */}\n          {isLoading ? (\n            <View style={styles.skeletonGrid}>\n              {[...Array(4)].map((_, index) => (\n                <View key={index} style={styles.skeletonGridItem}>\n                  <Skeleton width=\"100%\" height={120} />\n                  <View style={styles.skeletonContent}>\n                    <Skeleton width=\"80%\" height={16} />\n                    <Skeleton width=\"50%\" height={12} style={{ marginTop: 4 }} />\n                  </View>\n                </View>\n              ))}\n            </View>\n          ) : (\n            <View style={styles.exploreContent}>\n              <Feather \n                name=\"map\" \n                size={64} \n                color={isDarkMode ? colors.textSecondary : colors.textTertiary} \n              />\n              <Text style={[styles.emptyStateText, isDarkMode && styles.darkText]}>\n                No items to explore yet\n              </Text>\n              <Button\n                title=\"Refresh\"\n                onPress={handleRefresh}\n                style={{ marginTop: SPACING.medium }}\n                icon=\"refresh-cw\"\n              />\n            </View>\n          )}\n        </View>\n      </ScrollView>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: COLORS.background,\n  },\n  darkContainer: {\n    backgroundColor: '#121212',\n  },\n  scrollView: {\n    flex: 1,\n  },\n  scrollViewContent: {\n    flexGrow: 1,\n  },\n  tabContent: {\n    padding: SPACING.large,\n    flex: 1,\n  },\n  sectionTitle: {\n    ...TYPOGRAPHY.h2,\n    color: COLORS.text,\n    marginBottom: SPACING.small,\n  },\n  sectionDescription: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    marginBottom: SPACING.large,\n  },\n  // Skeleton styles\n  skeletonGrid: {\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    justifyContent: 'space-between',\n    marginTop: SPACING.medium,\n  },\n  skeletonGridItem: {\n    width: (screenWidth - SPACING.large * 2 - SPACING.medium) / 2,\n    marginBottom: SPACING.medium,\n    borderRadius: 8,\n    overflow: 'hidden',\n  },\n  skeletonContent: {\n    padding: SPACING.small,\n  },\n  // Explore tab styles\n  exploreContent: {\n    flex: 1,\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingVertical: SPACING.xlarge,\n  },\n  emptyStateText: {\n    ...TYPOGRAPHY.subtitle,\n    color: COLORS.text,\n    marginTop: SPACING.medium,\n  },\n  // Dark mode text styles\n  darkText: {\n    color: COLORS.white,\n  },\n  darkTextSecondary: {\n    color: '#A0A0A0',\n  },\n});\n", "language": "typescript"}, {"name": "screens/tabs/HomeTab.tsx", "content": "/**\n * @magic_component: HomeTab\n * @magic_purpose: Home tab screen showing the main content\n * @magic_category: Screens/Tabs\n * @magic_keywords: home,tab,screen,main\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useState, useEffect } from 'react';\nimport { \n  View, \n  Text, \n  StyleSheet, \n  ScrollView, \n  RefreshControl,\n  Dimensions,\n  ActivityIndicator\n} from 'react-native';\nimport { useNavigation } from '@react-navigation/native';\nimport { logger } from '../../utils/logger';\n\n// Import components and utilities\nimport HomeHeader from '../../components/screens/HomeScreen/HomeHeader';\nimport Button from '../../components/ui/Button';\nimport Skeleton from '../../components/ui/Skeleton';\nimport { useApp } from '../../contexts/AppContext';\nimport { formatDate } from '../../utils/helpers';\nimport { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';\nimport { HomeScreenNavigationProp } from '../../navigation/types';\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Get screen dimensions for responsive layouts\nconst { width: screenWidth } = Dimensions.get('window');\n\n/**\n * HomeTab component showing the main content of the app\n */\nexport default function HomeTab() {\n  // Navigation\n  const navigation = useNavigation<HomeScreenNavigationProp>();\n  \n  // Use theme from ThemeContext\n  const { isDarkMode, colors } = useTheme();\n  \n  // App context - used for non-theme related state\n  const { appState } = useApp();\n  \n  // State\n  const [isLoading, setIsLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  \n  // Log theme state for debugging\n  useEffect(() => {\n    logger.info('Theme state in HomeTab', 'UI', {\n      isDarkMode,\n      themeMode: 'Using ThemeContext directly'\n    });\n  }, [isDarkMode]);\n  \n  // Simulate loading data\n  useEffect(() => {\n    // Simulate API loading delay\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 1500);\n    \n    return () => clearTimeout(timer);\n  }, []);\n  \n  // Handle refresh\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    // Simulate refresh delay\n    setTimeout(() => {\n      setRefreshing(false);\n    }, 1000);\n  };\n  \n  // Handle modal open\n  const handleOpenModal = () => {\n    // Use React Navigation to open a modal screen\n    navigation.navigate('ModalExample');\n  };\n  \n  // Handle bottom sheet open\n  const handleOpenBottomSheet = () => {\n    // Use React Navigation to open a bottom sheet screen\n    navigation.navigate('BottomSheetExample');\n  };\n  \n  return (\n    <View style={[styles.container, isDarkMode && styles.darkContainer]}>\n      {/* Home Header */}\n      <HomeHeader title=\"Home\" />\n      \n      <ScrollView\n        style={styles.scrollView}\n        contentContainerStyle={styles.scrollViewContent}\n        refreshControl={\n          <RefreshControl\n            refreshing={refreshing}\n            onRefresh={handleRefresh}\n            colors={[colors.primary]}\n            tintColor={colors.primary}\n          />\n        }\n      >\n        <View style={styles.tabContent}>\n          <Text style={[styles.sectionTitle, isDarkMode && styles.darkText]}>\n            Welcome to Magically\n          </Text>\n          \n          <Text style={[styles.sectionDescription, isDarkMode && styles.darkTextSecondary]}>\n            This is a starter template for your next amazing app. Explore the features and components available.\n          </Text>\n          \n          {/* Buttons to open modal and bottom sheet screens - vertical alignment */}\n          <View style={styles.buttonContainer}>\n            <Button\n              title=\"Open Modal\"\n              onPress={handleOpenModal}\n              style={styles.actionButton}\n              variant=\"primary\"\n              icon=\"layers\"\n            />\n            \n            <Button\n              title=\"Open Bottom Sheet\"\n              onPress={handleOpenBottomSheet}\n              style={styles.actionButton}\n              variant=\"secondary\"\n              icon=\"arrow-up\"\n            />\n          </View>\n          \n          {/* Content */}\n          {isLoading ? (\n            <View style={styles.skeletonContainer}>\n              <Skeleton\n                style={styles.skeletonCard}\n                width=\"100%\"\n                height={200}\n              />\n              <View style={styles.skeletonContent}>\n                <Skeleton width=\"70%\" height={24} />\n                <Skeleton width=\"100%\" height={16} style={{ marginTop: 8 }} />\n                <Skeleton width=\"100%\" height={16} style={{ marginTop: 4 }} />\n                <Skeleton width=\"60%\" height={16} style={{ marginTop: 4 }} />\n              </View>\n            </View>\n          ) : (\n            <View style={styles.contentContainer}>\n              <Text style={[styles.sectionTitle, isDarkMode && styles.darkText]}>\n                Content Loaded\n              </Text>\n              <Text style={[styles.dateText, isDarkMode && styles.darkTextSecondary]}>\n                Last updated: {formatDate(new Date())}\n              </Text>\n            </View>\n          )}\n        </View>\n      </ScrollView>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: COLORS.background,\n  },\n  darkContainer: {\n    backgroundColor: '#121212',\n  },\n  scrollView: {\n    flex: 1,\n  },\n  scrollViewContent: {\n    flexGrow: 1,\n  },\n  tabContent: {\n    padding: SPACING.large,\n    flex: 1,\n  },\n  sectionTitle: {\n    ...TYPOGRAPHY.h2,\n    color: COLORS.text,\n    marginBottom: SPACING.small,\n  },\n  sectionDescription: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    marginBottom: SPACING.large,\n  },\n  contentContainer: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingVertical: SPACING.xlarge,\n  },\n  dateText: {\n    ...TYPOGRAPHY.caption,\n    color: COLORS.textTertiary,\n    marginTop: SPACING.large,\n  },\n  // Skeleton styles\n  skeletonContainer: {\n    marginTop: SPACING.medium,\n  },\n  skeletonCard: {\n    borderRadius: 12,\n  },\n  skeletonContent: {\n    padding: SPACING.medium,\n  },\n  // Dark mode text styles\n  darkText: {\n    color: COLORS.white,\n  },\n  darkTextSecondary: {\n    color: '#A0A0A0',\n  },\n  buttonContainer: {\n    flexDirection: 'column',\n    justifyContent: 'center',\n    alignItems: 'center',\n    marginVertical: SPACING.large,\n    paddingHorizontal: SPACING.medium,\n    gap: SPACING.medium,\n  },\n  actionButton: {\n    width: '80%',  // Wider buttons for vertical layout\n    maxWidth: 300,  // Increased max width\n  },\n});\n", "language": "typescript"}, {"name": "screens/tabs/ProfileTab.tsx", "content": "/**\n * @magic_component: ProfileTab\n * @magic_purpose: Profile tab screen showing user information and settings\n * @magic_category: Screens/Tabs\n * @magic_keywords: profile,user,account,settings,tab,screen\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport React, { useState, useEffect } from 'react';\nimport { \n  View, \n  Text, \n  StyleSheet, \n  ScrollView, \n  TouchableOpacity,\n  Platform,\n  Share\n} from 'react-native';\nimport { Feather } from '@expo/vector-icons';\nimport { useNavigation } from '@react-navigation/native';\nimport { logger } from '../../utils/logger';\n\n\n// Import components and utilities\nimport HomeHeader from '../../components/screens/HomeScreen/HomeHeader';\nimport Button from '../../components/ui/Button';\nimport Skeleton from '../../components/ui/Skeleton';\nimport { useApp } from '../../contexts/AppContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';\nimport { ProfileScreenNavigationProp } from '../../navigation/types';\n\n/**\n * ProfileTab component showing user information and settings\n */\nexport default function ProfileTab() {\n  // Navigation\n  const navigation = useNavigation<ProfileScreenNavigationProp>();\n  \n  // Use theme from ThemeContext\n  const { isDarkMode, colors, toggleTheme } = useTheme();\n  \n  // App context - used for non-theme related state\n  const { appState } = useApp();\n  \n\n  \n  // State\n  const [isLoading, setIsLoading] = useState(true);\n  \n  // Simulate loading data\n  useEffect(() => {\n    // Simulate API loading delay\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 1500);\n    \n    return () => clearTimeout(timer);\n  }, []);\n  \n\n  \n  // Handle sharing profile link\n  const handleShareProfile = async () => {\n    try {\n      logger.ui('Share profile button pressed', { screen: 'Profile' });\n      \n      // Use React Native's Share API\n      await Share.share({\n        title: 'Check out my profile',\n        message: 'Check out my profile!'\n      });\n      \n      logger.info('Profile shared successfully', 'NAVIGATION');\n    } catch (error) {\n      logger.error('Failed to share profile', 'NAVIGATION', { error });\n    }\n  };\n  \n  return (\n    <View style={[styles.container, isDarkMode && styles.darkContainer]}>\n      {/* Header */}\n      <HomeHeader title=\"Profile\" />\n      \n      <ScrollView\n        style={styles.scrollView}\n        contentContainerStyle={styles.scrollViewContent}\n      >\n        {isLoading ? (\n          <View style={styles.profileSkeleton}>\n            <Skeleton\n              style={styles.avatarSkeleton}\n              width={100}\n              height={100}\n              borderRadius={50}\n            />\n            <Skeleton width={150} height={24} style={{ marginTop: 16 }} />\n            <Skeleton width={200} height={16} style={{ marginTop: 8 }} />\n            \n            <Skeleton\n              style={styles.settingsSkeleton}\n              width=\"100%\"\n              height={50}\n              borderRadius={8}\n            />\n            <Skeleton\n              style={styles.settingsSkeleton}\n              width=\"100%\"\n              height={50}\n              borderRadius={8}\n            />\n            <Skeleton\n              style={styles.settingsSkeleton}\n              width=\"100%\"\n              height={50}\n              borderRadius={8}\n            />\n          </View>\n        ) : (\n          <View style={styles.profileContent}>\n            {/* Avatar */}\n            <View style={styles.avatarContainer}>\n              <Feather\n                name=\"user\"\n                size={40}\n                color={isDarkMode ? '#FFFFFF' : COLORS.text}\n                style={styles.avatarIcon}\n              />\n            </View>\n            \n            {/* User Info */}\n            <Text style={[styles.profileName, isDarkMode && styles.darkText]}>\n              John Doe\n            </Text>\n            <Text style={[styles.profileEmail, isDarkMode && styles.darkTextSecondary]}>\n              <EMAIL>\n            </Text>\n            \n            {/* Actions - Vertical button layout */}\n            <View style={styles.buttonGroup}>\n              <Button\n                title=\"Edit Profile\"\n                onPress={() => {\n                  logger.ui('Edit profile button pressed', { screen: 'Profile' });\n                }}\n                style={styles.profileButton}\n                variant=\"primary\"\n                icon=\"edit-2\"\n              />\n              \n              <Button\n                title=\"Toggle Dark Mode\"\n                onPress={() => {\n                  toggleTheme();\n                  logger.ui('Theme toggled', { isDarkMode: !isDarkMode });\n                }}\n                style={styles.profileButton}\n                variant=\"secondary\"\n                icon={isDarkMode ? 'sun' : 'moon'}\n              />\n              \n              <Button\n                title=\"Share Profile\"\n                onPress={handleShareProfile}\n                style={styles.profileButton}\n                variant=\"outline\"\n                icon=\"share-2\"\n              />\n            </View>\n            \n\n            \n\n          </View>\n        )}\n      </ScrollView>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: COLORS.background,\n  },\n  darkContainer: {\n    backgroundColor: '#121212',\n  },\n  scrollView: {\n    flex: 1,\n  },\n  scrollViewContent: {\n    flexGrow: 1,\n    padding: SPACING.large,\n  },\n  // Profile tab styles\n  profileContent: {\n    alignItems: 'center',\n    paddingTop: SPACING.large,\n  },\n  avatarContainer: {\n    width: 100,\n    height: 100,\n    borderRadius: 50,\n    backgroundColor: COLORS.border,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  avatarIcon: {\n    opacity: 0.7,\n  },\n  profileName: {\n    ...TYPOGRAPHY.h3,\n    color: COLORS.text,\n    marginTop: SPACING.medium,\n  },\n  profileEmail: {\n    ...TYPOGRAPHY.body,\n    color: COLORS.textSecondary,\n    marginTop: SPACING.tiny,\n  },\n  buttonGroup: {\n    flexDirection: 'column',\n    justifyContent: 'center',\n    alignItems: 'center',\n    marginTop: SPACING.large,\n    width: '100%',\n    paddingHorizontal: SPACING.medium,\n    gap: SPACING.medium,\n  },\n  profileButton: {\n    width: '80%',\n    maxWidth: 300,\n    height: 48,\n  },\n\n  // Skeleton styles\n  profileSkeleton: {\n    alignItems: 'center',\n    paddingTop: SPACING.large,\n  },\n  avatarSkeleton: {\n    alignSelf: 'center',\n  },\n  settingsSkeleton: {\n    width: '100%',\n    marginTop: SPACING.large,\n  },\n  // Dark mode text styles\n  darkText: {\n    color: COLORS.white,\n  },\n  darkTextSecondary: {\n    color: '#A0A0A0',\n  },\n\n});\n", "language": "typescript"}, {"name": "types/index.ts", "content": "/**\n * @magic_module: Types\n * @magic_purpose: Defines common TypeScript types used throughout the application\n * @magic_category: Types\n * @magic_keywords: types,interfaces,typescript,definitions\n */\n\n// Common response type for API calls\nexport interface ApiResponse<T> {\n  data: T;\n  status: number;\n  message: string;\n  success: boolean;\n}\n\n// Error type for API errors\nexport interface ApiError {\n  status: number;\n  message: string;\n  code?: string;\n}\n\n// Image type for the Image component\nexport interface ImageSource {\n  uri: string;\n  width?: number;\n  height?: number;\n  cache?: 'default' | 'reload' | 'force-cache' | 'only-if-cached';\n}\n\n// Theme type for the app's theme\nexport interface Theme {\n  dark: boolean;\n  colors: {\n    primary: string;\n    background: string;\n    card: string;\n    text: string;\n    border: string;\n    notification: string;\n    error: string;\n    success: string;\n    warning: string;\n  };\n}\n\n// Form field validation\nexport interface ValidationRule {\n  required?: boolean;\n  minLength?: number;\n  maxLength?: number;\n  pattern?: RegExp;\n  validate?: (value: any) => boolean | string;\n  message?: string;\n}\n\n// Form field\nexport interface FormField<T = string> {\n  name: string;\n  label: string;\n  value: T;\n  error?: string;\n  touched?: boolean;\n  validation?: ValidationRule;\n}\n\n// Pagination\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n  total?: number;\n  hasMore?: boolean;\n}\n\n// Sort options\nexport type SortDirection = 'asc' | 'desc';\n\nexport interface SortOption {\n  field: string;\n  direction: SortDirection;\n  label: string;\n}\n\n// Filter options\nexport interface FilterOption {\n  field: string;\n  value: string | number | boolean | string[] | number[];\n  operator?: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains';\n}\n\n// Search params\nexport interface SearchParams {\n  query?: string;\n  filters?: FilterOption[];\n  sort?: SortOption;\n  pagination?: PaginationParams;\n}\n", "language": "typescript"}, {"name": "utils/helpers.ts", "content": "/**\n * @magic_module: Helpers\n * @magic_purpose: Common utility functions used throughout the application\n * @magic_category: Utils\n * @magic_keywords: utilities,helpers,formatting,validation\n */\n\n/**\n * Format a price with currency symbol\n * @param price The price to format\n * @param currency The currency symbol to use\n * @returns Formatted price string\n */\nexport const formatPrice = (price: number, currency: string): string => {\n  if(!currency) currency ='$';\n  return `${currency}${price.toFixed(2)}`;\n};\n\n/**\n * Format a date to a readable string\n * @param date The date to format\n * @param format The format to use (short, medium, long)\n * @returns Formatted date string\n */\nexport const formatDate = (\n  date: Date | string | number,\n  format: 'short' | 'medium' | 'long' = 'medium'\n): string => {\n  const dateObj = new Date(date);\n  \n  switch (format) {\n    case 'short':\n      return dateObj.toLocaleDateString();\n    case 'medium':\n      return dateObj.toLocaleDateString(undefined, {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n      });\n    case 'long':\n      return dateObj.toLocaleDateString(undefined, {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long',\n      });\n    default:\n      return dateObj.toLocaleDateString();\n  }\n};\n\n/**\n * Truncate a string to a specific length and add ellipsis\n * @param str The string to truncate\n * @param length The maximum length\n * @returns Truncated string\n */\nexport const truncateString = (str: string, length: number = 50): string => {\n  if (str.length <= length) return str;\n  return `${str.substring(0, length)}...`;\n};\n\n/**\n * Validate an email address\n * @param email The email to validate\n * @returns Whether the email is valid\n */\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n/**\n * Generate a random ID\n * @param length The length of the ID\n * @returns Random ID string\n */\nexport const generateId = (length: number = 10): string => {\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  \n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  \n  return result;\n};\n\n/**\n * Debounce a function\n * @param func The function to debounce\n * @param wait The wait time in milliseconds\n * @returns Debounced function\n */\nexport const debounce = <T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): ((...args: Parameters<T>) => void) => {\n  let timeout: NodeJS.Timeout | null = null;\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout);\n    \n    timeout = setTimeout(() => {\n      func(...args);\n    }, wait);\n  };\n};\n", "language": "typescript"}, {"name": "utils/logger.ts", "content": "/**\n * @magic_module: Logger\n * @magic_purpose: Structured logging utility for the application\n * @magic_category: Utils\n * @magic_keywords: logger,logging,debug,error,info,navigation\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\n\n// Log levels\nexport enum LogLevel {\n  DEBUG = 'DEBUG',\n  INFO = 'INFO',\n  WARN = 'WARN',\n  ERROR = 'ERROR',\n}\n\n// Log categories\nexport type LogCategory = \n  | 'GENERAL'\n  | 'NAVIGATION'\n  | 'NETWORK'\n  | 'UI'\n  | 'STATE'\n  | 'PERFORMANCE';\n\n/**\n * Log entry structure\n * \n * IMPORTANT: Do not use union types or typeof in this interface as they can break the app\n */\nexport interface LogEntry {\n  timestamp: string;\n  level: string; // Use string instead of union type\n  category: string; // Use string instead of union type\n  message: string;\n  data: Record<string, any> | null;\n  source: string;\n}\n\n/**\n * Structured logger for the application\n * \n * IMPORTANT: This logger outputs logs in a standard format that can be parsed\n * by the Magically platform. Always use this logger instead of console.log directly.\n * \n * IMPORTANT: Do not use union types or typeof in this code as they can break the app\n */\nclass Logger {\n  /**\n   * Log a debug message\n   */\n  debug(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {\n    this.log(LogLevel.DEBUG, category, message, data, source);\n  }\n\n  /**\n   * Log an info message\n   */\n  info(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {\n    this.log(LogLevel.INFO, category, message, data, source);\n  }\n\n  /**\n   * Log a warning message\n   */\n  warn(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {\n    this.log(LogLevel.WARN, category, message, data, source);\n  }\n\n  /**\n   * Log an error message\n   */\n  error(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {\n    this.log(LogLevel.ERROR, category, message, data, source);\n  }\n\n  /**\n   * Log a navigation event\n   */\n  navigation(message: string, category: LogCategory = 'NAVIGATION', data: Record<string, any> | null = null) {\n    this.log(LogLevel.INFO, category, message, data, 'Navigation');\n  }\n\n  /**\n   * Log a network request\n   */\n  network(message: string, data: Record<string, any> | null = null) {\n    this.log(LogLevel.INFO, 'NETWORK', message, data, 'Network');\n  }\n\n  /**\n   * Log a UI event\n   */\n  ui(message: string, data: Record<string, any> | null = null) {\n    this.log(LogLevel.INFO, 'UI', message, data, 'UI');\n  }\n\n  /**\n   * Internal log method\n   */\n  private log(level: string, category: string, message: string, data: Record<string, any> | null, source: string) {\n    const timestamp = new Date().toISOString();\n    \n    const logEntry: LogEntry = {\n      timestamp,\n      level,\n      category,\n      message,\n      data,\n      source,\n    };\n    \n    // Format the log entry as JSON for easy parsing\n    const formattedLog = JSON.stringify(logEntry);\n    \n    // Output to console with appropriate styling\n    switch (level) {\n      case LogLevel.DEBUG:\n        console.log(`%c${formattedLog}`, 'color: gray');\n        break;\n      case LogLevel.INFO:\n        console.log(`%c${formattedLog}`, 'color: blue');\n        break;\n      case LogLevel.WARN:\n        console.warn(`%c${formattedLog}`, 'color: orange');\n        break;\n      case LogLevel.ERROR:\n        console.error(`%c${formattedLog}`, 'color: red');\n        break;\n      default:\n        console.log(formattedLog);\n    }\n    \n    // In a real app, you might also send logs to a server or analytics service\n  }\n}\n\n// Export a singleton instance of the logger\nexport const logger = new Logger();\n\n// Export a convenience function to get the logger instance\nexport function getLogger(): Logger {\n  return logger;\n}\n", "language": "typescript"}, {"name": "utils/navigationUtils.ts", "content": "/**\n * @magic_module: NavigationUtils\n * @magic_purpose: Utility functions for navigation and URL handling\n * @magic_category: Utils/Navigation\n * @magic_keywords: navigation,url,linking,web\n * \n * IMPORTANT: Do not use union types or typeof in this file as they can break the app\n */\nimport { Platform } from 'react-native';\nimport { Linking } from 'react-native';\nimport { logger } from './logger';\n\n/**\n * Get the URL for a specific screen\n * \n * This is useful for sharing links, especially on web\n * \n * @param screen The screen name\n * @param params The screen parameters\n * @returns The URL for the screen\n */\nexport function getScreenURL(screen: string, params?: Record<string, any>): string {\n  try {\n    // Create a basic URL for the screen using standard URL construction\n    const baseUrl = 'https://example.com';\n    const path = `/${screen}`;\n    const url = new URL(path, baseUrl);\n    \n    // Add query parameters if provided\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        url.searchParams.append(key, String(value));\n      });\n    }\n    \n    logger.info('Generated screen URL', 'NAVIGATION', { screen, params, url: url.toString() });\n    \n    return url.toString();\n  } catch (error) {\n    logger.error('Failed to generate screen URL', 'NAVIGATION', { screen, params, error });\n    return '';\n  }\n}\n\n/**\n * Open a URL in the browser or in the app\n * \n * @param url The URL to open\n * @returns A promise that resolves when the URL is opened\n */\nexport async function openURL(url: string): Promise<void> {\n  try {\n    logger.info('Opening URL', 'NAVIGATION', { url });\n    await Linking.openURL(url);\n  } catch (error) {\n    logger.error('Failed to open URL', 'NAVIGATION', { url, error });\n  }\n}\n\n/**\n * Share a deep link to a specific screen\n * \n * @param screen The screen name\n * @param params The screen parameters\n * @param title The title for the share dialog\n * @returns A promise that resolves when the sharing is complete\n */\nexport async function shareScreenLink(screen: string, params?: Record<string, any>, title?: string): Promise<void> {\n  try {\n    // Get the URL for the screen\n    const url = getScreenURL(screen, params);\n    \n    // If on web, use the Web Share API if available\n    if (Platform.OS === 'web' && typeof navigator !== 'undefined' && navigator.share) {\n      await navigator.share({\n        title: title || 'Check this out!',\n        url,\n      });\n      \n      logger.info('Shared screen link using Web Share API', 'NAVIGATION', { screen, url });\n    } else {\n      // Otherwise, just copy to clipboard or use native share\n      // This would need additional implementation for native platforms\n      logger.info('Share functionality not fully implemented for this platform', 'NAVIGATION', { screen, url });\n      \n      // For now, just open the URL\n      await openURL(url);\n    }\n  } catch (error) {\n    logger.error('Failed to share screen link', 'NAVIGATION', { screen, params, error });\n  }\n}\n\n/**\n * Parse URL parameters from a URL string\n * \n * @param url The URL to parse\n * @returns An object with the URL parameters\n */\nexport function parseURLParams(url: string): Record<string, string> {\n  try {\n    // Create a URL object\n    const urlObj = new URL(url);\n    \n    // Get the search params\n    const searchParams = urlObj.searchParams;\n    \n    // Convert to object\n    const params: Record<string, string> = {};\n    searchParams.forEach((value, key) => {\n      params[key] = value;\n    });\n    \n    logger.info('Parsed URL parameters', 'NAVIGATION', { url, params });\n    \n    return params;\n  } catch (error) {\n    logger.error('Failed to parse URL parameters', 'NAVIGATION', { url, error });\n    return {};\n  }\n}\n", "language": "typescript"}]}, {"templateName": "default", "files": [{"name": "App.tsx", "content": "\nimport React from 'react';\nimport {DarkTheme, DefaultTheme, NavigationContainer} from '@react-navigation/native';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\nimport { SafeAreaProvider } from \"react-native-safe-area-context\";\nimport { Toaster } from 'sonner-native';\nimport { ThemeProvider } from './providers/ThemeContext';\nimport TabNavigator from './navigation/TabNavigator';\nimport { View, StyleSheet, useColorScheme } from 'react-native';\n\nconst Stack = createNativeStackNavigator();\n\nfunction RootStack() {\n  return (\n    <Stack.Navigator screenOptions={{\n      headerShown: false\n    }}>\n      <Stack.Screen name=\"MainTabs\" component={TabNavigator} />\n    </Stack.Navigator>\n  );\n}\n\nexport default function App() {\n\n  const colorScheme = useColorScheme();\n  const isDark = colorScheme === 'dark';\n\n  // Always extend the base theme from react.navigation.\n  // Otherwise, error such as cannot read property 'n.medium' of undefined will occur which basically means the fonts property is missing from the theme.\n  const navigationTheme = {\n    ...(isDark ? DarkTheme : DefaultTheme),\n    colors: {\n      // Change this to match the app's theme. Either use Dark or light. Add conditional only when theme switching is required.\n      ... DefaultTheme.colors\n      // isDark ? DarkTheme.colors : DefaultTheme.colors\n    },\n  };\n\n  return (\n    <ThemeProvider>\n      <SafeAreaProvider style={styles.container}>\n        <Toaster />\n        <NavigationContainer theme={navigationTheme}>\n          <RootStack />\n        </NavigationContainer>\n      </SafeAreaProvider>\n    </ThemeProvider>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1\n  }\n});\n", "language": "typescript"}, {"name": "api/apiClient.ts", "content": "\n// A flexible API client with built-in caching, error handling, and retry logic\n\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\n// Types\ntype HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';\n\ninterface RequestOptions {\n  method?: HttpMethod;\n  headers?: Record<string, string>;\n  body?: any;\n  cache?: boolean;\n  cacheTTL?: number; // Time to live in seconds\n  retries?: number;\n  retryDelay?: number; // Delay between retries in ms\n}\n\ninterface ApiResponse<T> {\n  data: T | null;\n  error: Error | null;\n  status: number;\n  headers: Headers;\n  cached?: boolean;\n}\n\n// Default configuration\nconst DEFAULT_CONFIG = {\n  baseUrl: '',\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n  cache: true,\n  cacheTTL: 300, // 5 minutes\n  retries: 2,\n  retryDelay: 1000,\n};\n\n// Cache helpers\nconst getCacheKey = (url: string, options: RequestOptions) => {\n  const method = options.method || 'GET';\n  const body = options.body ? JSON.stringify(options.body) : '';\n  return `api_cache_${method}_${url}_${body}`;\n};\n\nconst getCachedResponse = async <T>(cacheKey: string): Promise<ApiResponse<T> | null> => {\n  try {\n    const cachedData = await AsyncStorage.getItem(cacheKey);\n    if (cachedData) {\n      const { data, expiry } = JSON.parse(cachedData);\n      if (expiry > Date.now()) {\n        return { ...data, cached: true };\n      }\n    }\n    return null;\n  } catch (error) {\n    console.warn('Cache retrieval error:', error);\n    return null;\n  }\n};\n\nconst setCachedResponse = async <T>(\n  cacheKey: string, \n  response: ApiResponse<T>, \n  ttl: number\n): Promise<void> => {\n  try {\n    const expiry = Date.now() + ttl * 1000;\n    await AsyncStorage.setItem(\n      cacheKey,\n      JSON.stringify({\n        data: response,\n        expiry,\n      })\n    );\n  } catch (error) {\n    console.warn('Cache storage error:', error);\n  }\n};\n\n// API client class\nexport class ApiClient {\n  private baseUrl: string;\n  private defaultHeaders: Record<string, string>;\n  private defaultOptions: Omit<RequestOptions, 'method' | 'body' | 'headers'>;\n  \n  constructor(config: Partial<typeof DEFAULT_CONFIG> = {}) {\n    const mergedConfig = { ...DEFAULT_CONFIG, ...config };\n    this.baseUrl = mergedConfig.baseUrl;\n    this.defaultHeaders = mergedConfig.headers;\n    this.defaultOptions = {\n      cache: mergedConfig.cache,\n      cacheTTL: mergedConfig.cacheTTL,\n      retries: mergedConfig.retries,\n      retryDelay: mergedConfig.retryDelay,\n    };\n  }\n  \n  // Set auth token\n  setAuthToken(token: string | null): void {\n    if (token) {\n      this.defaultHeaders['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete this.defaultHeaders['Authorization'];\n    }\n  }\n  \n  // Update base URL\n  setBaseUrl(url: string): void {\n    this.baseUrl = url;\n  }\n  \n  // Main request method\n  async request<T = any>(\n    endpoint: string,\n    options: RequestOptions = {}\n  ): Promise<ApiResponse<T>> {\n    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;\n    const method = options.method || 'GET';\n    \n    // Merge default options with provided options\n    const mergedOptions: RequestOptions = {\n      ...this.defaultOptions,\n      ...options,\n      headers: {\n        ...this.defaultHeaders,\n        ...options.headers,\n      },\n    };\n    \n    // Check cache for GET requests\n    if (method === 'GET' && mergedOptions.cache) {\n      const cacheKey = getCacheKey(url, mergedOptions);\n      const cachedResponse = await getCachedResponse<T>(cacheKey);\n      if (cachedResponse) return cachedResponse;\n    }\n    \n    // Prepare fetch options\n    const fetchOptions: RequestInit = {\n      method,\n      headers: mergedOptions.headers,\n    };\n    \n    // Add body for non-GET requests\n    if (method !== 'GET' && mergedOptions.body) {\n      fetchOptions.body = JSON.stringify(mergedOptions.body);\n    }\n    \n    // Execute request with retry logic\n    let lastError: Error | null = null;\n    let attempts = 0;\n    const maxAttempts = (mergedOptions.retries || 0) + 1;\n    \n    while (attempts < maxAttempts) {\n      try {\n        const response = await fetch(url, fetchOptions);\n        const responseHeaders = response.headers;\n        const status = response.status;\n        \n        // Parse response\n        let data: T | null = null;\n        let error: Error | null = null;\n        \n        try {\n          // Check if response has JSON content\n          const contentType = responseHeaders.get('content-type');\n          if (contentType && contentType.includes('application/json')) {\n            data = await response.json();\n          } else {\n            // Handle non-JSON responses\n            const text = await response.text();\n            data = text as unknown as T;\n          }\n        } catch (e) {\n          error = new Error('Failed to parse response');\n        }\n        \n        // Handle HTTP errors\n        if (!response.ok) {\n          error = new Error(\n            typeof data === 'object' && data !== null && 'message' in data\n              ? String(data.message)\n              : `Request failed with status ${status}`\n          );\n        }\n        \n        const apiResponse: ApiResponse<T> = {\n          data,\n          error,\n          status,\n          headers: responseHeaders,\n        };\n        \n        // Cache successful GET responses\n        if (method === 'GET' && mergedOptions.cache && !error) {\n          const cacheKey = getCacheKey(url, mergedOptions);\n          await setCachedResponse(cacheKey, apiResponse, mergedOptions.cacheTTL || 300);\n        }\n        \n        return apiResponse;\n      } catch (e) {\n        lastError = e instanceof Error ? e : new Error(String(e));\n        attempts++;\n        \n        if (attempts < maxAttempts) {\n          // Wait before retry\n          await new Promise(resolve => \n            setTimeout(resolve, mergedOptions.retryDelay || 1000)\n          );\n        }\n      }\n    }\n    \n    // Return error response after all retries failed\n    return {\n      data: null,\n      error: lastError || new Error('Request failed'),\n      status: 0,\n      headers: new Headers(),\n    };\n  }\n  \n  // Convenience methods\n  async get<T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { ...options, method: 'GET' });\n  }\n  \n  async post<T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { ...options, method: 'POST', body: data });\n  }\n  \n  async put<T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { ...options, method: 'PUT', body: data });\n  }\n  \n  async patch<T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { ...options, method: 'PATCH', body: data });\n  }\n  \n  async delete<T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, { ...options, method: 'DELETE' });\n  }\n  \n  // Cache management\n  async clearCache(): Promise<void> {\n    try {\n      const keys = await AsyncStorage.getAllKeys();\n      const cacheKeys = keys.filter(key => key.startsWith('api_cache_'));\n      if (cacheKeys.length > 0) {\n        await AsyncStorage.multiRemove(cacheKeys);\n      }\n    } catch (error) {\n      console.error('Failed to clear API cache', error);\n    }\n  }\n}\n\n// Create and export a default instance\nexport const api = new ApiClient();\n", "language": "typescript"}, {"name": "components/screens/ExploreScreen/Title.tsx", "content": "import React from \"react\";\nimport {View, StyleSheet, Text} from \"react-native\";\nimport {useTheme} from \"../../../providers/ThemeContext\";\n\nexport const Title = () => {\n    const { theme } = useTheme();\n\n    return (\n        <View style={styles.content}>\n            <Text style={[styles.title, { color: theme.colors.foreground }]}>Explore</Text>\n            <Text style={{ color: theme.colors.foreground }}>Prompt, edit, deploy</Text>\n        </View>\n    )\n}\n\nconst styles = StyleSheet.create({\n    content: {\n        flex: 1,\n        padding: 16,\n        justifyContent: 'center',\n        alignItems: 'center',\n    },\n    title: {\n        marginBottom: 16,\n        fontSize: 20,\n        fontWeight: '600',\n    },\n});\n", "language": "typescript"}, {"name": "components/screens/HomeScreen/Title.tsx", "content": "import React from \"react\";\nimport {View, StyleSheet, Text} from \"react-native\";\n\nexport const Title = () => {\n    return (\n        <View style={styles.content}>\n            <Text style={styles.title}>magically</Text>\n            <Text>Idea to mobile app in minutes.</Text>\n        </View>\n    )\n}\n\nconst styles = StyleSheet.create({\n    content: {\n        flex: 1,\n        padding: 16,\n        justifyContent: 'center',\n        alignItems: 'center',\n    },\n    title: {\n        marginBottom: 16,\n        fontSize: 20,\n        fontWeight: '600',\n    },\n});\n", "language": "typescript"}, {"name": "hooks/useForm.ts", "content": "\nimport { useState, useCallback } from 'react';\n\ntype ValidationRule<T> = {\n  validate: (value: T, formValues: Record<string, any>) => boolean;\n  message: string;\n};\n\ntype FieldConfig<T> = {\n  initialValue: T;\n  required?: boolean;\n  requiredMessage?: string;\n  rules?: ValidationRule<T>[];\n};\n\ntype FormConfig = Record<string, FieldConfig<any>>;\n\ntype FormState<T extends FormConfig> = {\n  values: { [K in keyof T]: T[K]['initialValue'] };\n  errors: { [K in keyof T]?: string };\n  touched: { [K in keyof T]: boolean };\n  isValid: boolean;\n  isDirty: boolean;\n};\n\nexport function useForm<T extends FormConfig>(config: T) {\n  // Initialize form state\n  const initialValues = Object.entries(config).reduce((acc, [key, field]) => {\n    acc[key] = field.initialValue;\n    return acc;\n  }, {} as Record<string, any>);\n  \n  const initialTouched = Object.keys(config).reduce((acc, key) => {\n    acc[key] = false;\n    return acc;\n  }, {} as Record<string, boolean>);\n  \n  const [formState, setFormState] = useState<FormState<T>>({\n    values: initialValues as FormState<T>['values'],\n    errors: {},\n    touched: initialTouched as FormState<T>['touched'],\n    isValid: false,\n    isDirty: false,\n  });\n  \n  // Validate a single field\n  const validateField = useCallback((name: keyof T, value: any) => {\n    const fieldConfig = config[name];\n    \n    // Check if required\n    if (fieldConfig.required && \n        (value === undefined || value === null || value === '')) {\n      return fieldConfig.requiredMessage || 'This field is required';\n    }\n    \n    // Check validation rules\n    if (fieldConfig.rules) {\n      for (const rule of fieldConfig.rules) {\n        if (!rule.validate(value, formState.values)) {\n          return rule.message;\n        }\n      }\n    }\n    \n    return undefined;\n  }, [config, formState.values]);\n  \n  // Validate all fields\n  const validateForm = useCallback(() => {\n    const errors: Record<string, string> = {};\n    let isValid = true;\n    \n    Object.keys(config).forEach(key => {\n      const error = validateField(key, formState.values[key]);\n      if (error) {\n        errors[key] = error;\n        isValid = false;\n      }\n    });\n    \n    return { errors, isValid };\n  }, [config, formState.values, validateField]);\n  \n  // Update form state when a field changes\n  const handleChange = useCallback((name: keyof T, value: any) => {\n    setFormState(prev => {\n      const newValues = { ...prev.values, [name]: value };\n      const error = validateField(name, value);\n      const newErrors = { ...prev.errors };\n      \n      if (error) {\n        newErrors[name] = error;\n      } else {\n        delete newErrors[name];\n      }\n      \n      return {\n        values: newValues,\n        errors: newErrors,\n        touched: { ...prev.touched, [name]: true },\n        isValid: Object.keys(newErrors).length === 0,\n        isDirty: JSON.stringify(newValues) !== JSON.stringify(initialValues),\n      };\n    });\n  }, [initialValues, validateField]);\n  \n  // Mark a field as touched (e.g., on blur)\n  const handleBlur = useCallback((name: keyof T) => {\n    setFormState(prev => {\n      if (prev.touched[name]) return prev;\n      \n      const error = validateField(name, prev.values[name]);\n      const newErrors = { ...prev.errors };\n      \n      if (error) {\n        newErrors[name] = error;\n      } else {\n        delete newErrors[name];\n      }\n      \n      return {\n        ...prev,\n        errors: newErrors,\n        touched: { ...prev.touched, [name]: true },\n        isValid: Object.keys(newErrors).length === 0,\n      };\n    });\n  }, [validateField]);\n  \n  // Reset the form to initial values\n  const resetForm = useCallback(() => {\n    setFormState({\n      values: initialValues as FormState<T>['values'],\n      errors: {},\n      touched: initialTouched as FormState<T>['touched'],\n      isValid: false,\n      isDirty: false,\n    });\n  }, [initialValues, initialTouched]);\n  \n  // Submit handler\n  const handleSubmit = useCallback((onSubmit: (values: FormState<T>['values']) => void) => {\n    return () => {\n      const { errors, isValid } = validateForm();\n      \n      // Mark all fields as touched\n      const allTouched = Object.keys(config).reduce((acc, key) => {\n        acc[key] = true;\n        return acc;\n      }, {} as Record<string, boolean>);\n      \n      setFormState(prev => ({\n        ...prev,\n        errors,\n        touched: allTouched as FormState<T>['touched'],\n        isValid,\n      }));\n      \n      if (isValid) {\n        onSubmit(formState.values);\n      }\n    };\n  }, [config, formState.values, validateForm]);\n  \n  return {\n    values: formState.values,\n    errors: formState.errors,\n    touched: formState.touched,\n    isValid: formState.isValid,\n    isDirty: formState.isDirty,\n    handleChange,\n    handleBlur,\n    handleSubmit,\n    resetForm,\n    validateForm,\n  };\n}\n", "language": "typescript"}, {"name": "hooks/useStorage.ts", "content": "\nimport { useState, useEffect, useCallback } from 'react';\nimport AsyncStorage from '@react-native-async-storage/async-storage';\n\ninterface StorageOptions<T> {\n  defaultValue: T;\n  serialize?: (value: T) => string;\n  deserialize?: (value: string) => T;\n}\n\nexport function useStorage<T>(\n  key: string,\n  options: StorageOptions<T>\n) {\n  const { \n    defaultValue, \n    serialize = JSON.stringify, \n    deserialize = JSON.parse \n  } = options;\n  \n  // State to hold the current value\n  const [storedValue, setStoredValue] = useState<T>(defaultValue);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n  \n  // Load the initial value from storage\n  useEffect(() => {\n    const loadStoredValue = async () => {\n      try {\n        setIsLoading(true);\n        const item = await AsyncStorage.getItem(key);\n        \n        if (item !== null) {\n          try {\n            const value = deserialize(item);\n            setStoredValue(value);\n          } catch (parseError) {\n            console.warn(`Failed to parse stored value for key \"${key}\"`, parseError);\n            setStoredValue(defaultValue);\n          }\n        }\n        setError(null);\n      } catch (e) {\n        console.error(`Error reading from AsyncStorage for key \"${key}\"`, e);\n        setError(e instanceof Error ? e : new Error(String(e)));\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    loadStoredValue();\n  }, [key, defaultValue, deserialize]);\n  \n  // Update the stored value\n  const setValue = useCallback(async (value: T | ((val: T) => T)) => {\n    try {\n      // Allow value to be a function for previous state updates\n      const valueToStore = value instanceof Function ? value(storedValue) : value;\n      \n      // Save state\n      setStoredValue(valueToStore);\n      \n      // Save to AsyncStorage\n      const serialized = serialize(valueToStore);\n      await AsyncStorage.setItem(key, serialized);\n      setError(null);\n    } catch (e) {\n      console.error(`Error saving to AsyncStorage for key \"${key}\"`, e);\n      setError(e instanceof Error ? e : new Error(String(e)));\n    }\n  }, [key, storedValue, serialize]);\n  \n  // Remove the item from storage\n  const removeValue = useCallback(async () => {\n    try {\n      await AsyncStorage.removeItem(key);\n      setStoredValue(defaultValue);\n      setError(null);\n    } catch (e) {\n      console.error(`Error removing from AsyncStorage for key \"${key}\"`, e);\n      setError(e instanceof Error ? e : new Error(String(e)));\n    }\n  }, [key, defaultValue]);\n  \n  return {\n    value: storedValue,\n    setValue,\n    removeValue,\n    isLoading,\n    error,\n  };\n}\n", "language": "typescript"}, {"name": "navigation/TabNavigator.tsx", "content": "\nimport React from 'react';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { Feather } from '@expo/vector-icons';\nimport { useTheme } from '../providers/ThemeContext'\nimport HomeScreen from '../screens/HomeScreen';\nimport ExploreScreen from '../screens/ExploreScreen';\nimport ProfileScreen from '../screens/ProfileScreen';\nimport { defaultTheme } from '../theme';\n\nconst Tab = createBottomTabNavigator();\n\nexport default function TabNavigator() {\n  // Use try/catch to handle the case where ThemeProvider might not be available\n  let theme;\n  try {\n    const themeContext = useTheme();\n    theme = themeContext.theme;\n  } catch (error) {\n    // Fallback to default theme if ThemeProvider is not available\n    theme = defaultTheme;\n  }\n\n  return (\n    <Tab.Navigator\n      screenOptions={({ route }) => ({\n        headerShown: false,\n        tabBarIcon: ({ focused, color, size }) => {\n          let iconName: keyof typeof Feather.glyphMap;\n          \n          if (route.name === 'Home') {\n            iconName = 'home';\n          } else if (route.name === 'Explore') {\n            iconName = 'compass';\n          } else if (route.name === 'Profile') {\n            iconName = 'user';\n          } else {\n            iconName = 'circle';\n          }\n          \n          return <Feather name={iconName} size={size} color={color} />;\n        },\n        tabBarActiveTintColor: theme.colors.primary,\n        tabBarInactiveTintColor: theme.colors.mutedForeground,\n        tabBarStyle: {\n          borderTopColor: theme.colors.border,\n          backgroundColor: theme.colors.background,\n          height: 60,\n          paddingBottom: 8,\n        },\n        tabBarLabelStyle: {\n          fontSize: 12,\n          fontWeight: '500',\n        },\n      })}\n    >\n      <Tab.Screen name=\"Home\" component={HomeScreen} />\n      <Tab.Screen name=\"Explore\" component={ExploreScreen} />\n      <Tab.Screen name=\"Profile\" component={ProfileScreen} />\n    </Tab.Navigator>\n  );\n}\n", "language": "typescript"}, {"name": "providers/ThemeContext.tsx", "content": "import React, { createContext, useContext, useState } from 'react';\nimport { createTheme, ColorMode, Theme } from '../theme';\n\ntype ThemeContextType = {\n  theme: Theme;\n  colorMode: ColorMode;\n  setColorMode: (mode: ColorMode) => void;\n  toggleColorMode: () => void;\n};\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [colorMode] = useState<ColorMode>('light');\n  const [theme] = useState<Theme>(createTheme('light'));\n\n  // These functions are kept for API compatibility but don't change the theme\n  const setColorMode = () => {\n    // No-op. Implement if needed\n  };\n\n  const toggleColorMode = () => {\n    // No-op. Implement if needed\n  };\n\n  const contextValue: ThemeContextType = {\n    theme,\n    colorMode: 'light',\n    setColorMode,\n    toggleColorMode,\n  };\n\n  return (\n      <ThemeContext.Provider value={contextValue}>\n        {children}\n      </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};", "language": "typescript"}, {"name": "screens/ExploreScreen.tsx", "content": "\nimport React from 'react';\nimport { View, StyleSheet, Text } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { useTheme } from '../providers/ThemeContext'\nimport {defaultTheme} from \"../theme\"\n\nexport default function ExploreScreen() {\n  const { theme } = useTheme();\n  \n  return (\n    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>\n      <View style={styles.content}>\n        <Text style={[styles.title, { color: theme.colors.foreground }]}>Explore</Text>\n        <Text style={{ color: theme.colors.foreground }}>Prompt, edit, deploy</Text>\n      </View>\n    </SafeAreaView>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  title: {\n\n  },\n});\n", "language": "typescript"}, {"name": "screens/HomeScreen.tsx", "content": "\nimport React from 'react';\nimport { View, StyleSheet, Text } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { StatusBar } from 'expo-status-bar';\nimport {Title} from \"../components/screens/HomeScreen/Title\";\n\nexport default function HomeScreen() {\n\n  return (\n    <SafeAreaView style={[styles.container]}>\n      <StatusBar />\n\n      <Title/>\n    </SafeAreaView>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  content: {\n    flex: 1,\n    padding: 16,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  title: {\n    marginBottom: 16,\n    fontSize: 20,\n    fontWeight: '600',\n  },\n});\n", "language": "typescript"}, {"name": "screens/ProfileScreen.tsx", "content": "\nimport React from 'react';\nimport { View, StyleSheet, Text } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { StatusBar } from 'expo-status-bar';\n\nexport default function HomeScreen() {\n\n  return (\n      <SafeAreaView style={[styles.container]}>\n        <StatusBar />\n\n        <View style={styles.content}>\n          <Text>Profile</Text>\n          <Text>Idea to mobile app in minutes.</Text>\n        </View>\n      </SafeAreaView>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  content: {\n    flex: 1,\n    padding: 16,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  title: {\n    marginBottom: 16,\n    fontSize: 20,\n    fontWeight: '600',\n  },\n});\n", "language": "typescript"}, {"name": "theme.ts", "content": "\n// A simplified theme system with a single design system\n\n// Color palette with semantic naming\nexport const palette = {\n  // Primary colors\n  primary50: '#EBF5FF',\n  primary100: '#D1E9FF',\n  primary200: '#A4CDFE',\n  primary300: '#76B0FB',\n  primary400: '#4992F7',\n  primary500: '#2563EB', // Primary brand color\n  primary600: '#1D4ED8',\n  primary700: '#1E40AF',\n  primary800: '#1E3A8A',\n  primary900: '#172554',\n\n  // Neutral colors\n  neutral50: '#F9FAFB',\n  neutral100: '#F3F4F6',\n  neutral200: '#E5E7EB',\n  neutral300: '#D1D5DB',\n  neutral400: '#9CA3AF',\n  neutral500: '#6B7280',\n  neutral600: '#4B5563',\n  neutral700: '#374151',\n  neutral800: '#1F2937',\n  neutral900: '#111827',\n\n  // Success colors\n  success50: '#ECFDF5',\n  success500: '#10B981',\n  success700: '#047857',\n\n  // Warning colors\n  warning50: '#FFFBEB',\n  warning500: '#F59E0B',\n  warning700: '#B45309',\n\n  // Error colors\n  error50: '#FEF2F2',\n  error500: '#EF4444',\n  error700: '#B91C1C',\n\n  // Info colors\n  info50: '#EFF6FF',\n  info500: '#3B82F6',\n  info700: '#1D4ED8',\n\n  // Pure colors\n  white: '#FFFFFF',\n  black: '#000000',\n  transparent: 'transparent',\n};\n\n\n// Shadows\nexport const shadows = {\n  none: {\n    shadowColor: palette.transparent,\n    shadowOffset: { width: 0, height: 0 },\n    shadowOpacity: 0,\n    shadowRadius: 0,\n    elevation: 0,\n  },\n  sm: {\n    shadowColor: palette.black,\n    shadowOffset: { width: 0, height: 1 },\n    shadowOpacity: 0.05,\n    shadowRadius: 2,\n    elevation: 1,\n  },\n  md: {\n    shadowColor: palette.black,\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.1,\n    shadowRadius: 4,\n    elevation: 2,\n  },\n  lg: {\n    shadowColor: palette.black,\n    shadowOffset: { width: 0, height: 4 },\n    shadowOpacity: 0.1,\n    shadowRadius: 6,\n    elevation: 4,\n  },\n  xl: {\n    shadowColor: palette.black,\n    shadowOffset: { width: 0, height: 8 },\n    shadowOpacity: 0.1,\n    shadowRadius: 12,\n    elevation: 6,\n  },\n};\n\n// Animation durations\nexport const animation = {\n  duration: {\n    fastest: 100,\n    fast: 200,\n    normal: 300,\n    slow: 500,\n    slowest: 700,\n  },\n};\n\n\n// Define light theme\nexport const lightTheme = {\n  colors: {\n    // Semantic colors\n    background: palette.white,\n    foreground: palette.neutral900,\n    card: palette.white,\n    cardForeground: palette.neutral900,\n    popover: palette.white,\n    popoverForeground: palette.neutral900,\n    primary: palette.primary500,\n    primaryForeground: palette.white,\n    secondary: palette.neutral100,\n    secondaryForeground: palette.neutral900,\n    muted: palette.neutral200,\n    mutedForeground: palette.neutral500,\n    accent: palette.primary100,\n    accentForeground: palette.primary900,\n    destructive: palette.error500,\n    destructiveForeground: palette.white,\n    border: palette.neutral200,\n    input: palette.neutral200,\n    ring: palette.primary500,\n\n    // Status colors\n    success: palette.success500,\n    warning: palette.warning500,\n    error: palette.error500,\n    info: palette.info500,\n  },\n};\n\n// Define dark theme\nexport const darkTheme = {\n  colors: {\n    // Semantic colors\n    background: palette.neutral900,\n    foreground: palette.neutral100,\n    card: palette.neutral800,\n    cardForeground: palette.neutral100,\n    popover: palette.neutral800,\n    popoverForeground: palette.neutral100,\n    primary: palette.primary400,\n    primaryForeground: palette.neutral900,\n    secondary: palette.neutral800,\n    secondaryForeground: palette.neutral100,\n    muted: palette.neutral700,\n    mutedForeground: palette.neutral400,\n    accent: palette.primary800,\n    accentForeground: palette.primary100,\n    destructive: palette.error500,\n    destructiveForeground: palette.white,\n    border: palette.neutral700,\n    input: palette.neutral700,\n    ring: palette.primary400,\n\n    // Status colors\n    success: palette.success500,\n    warning: palette.warning500,\n    error: palette.error500,\n    info: palette.info500,\n  }\n};\n\n// The default theme is the light theme\nexport const defaultTheme = lightTheme;\n\n// Color mode type\nexport type ColorMode = 'light' | 'dark';\n\n// Create theme function\nexport const createTheme = (mode: ColorMode) => {\n  return mode === 'dark' ? darkTheme : lightTheme;\n};\n\n// Export types for theme\nexport type Theme = typeof lightTheme;\n", "language": "typescript"}]}, {"templateName": "ecommerce", "files": [{"name": "App.tsx", "content": "import React from 'react';\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\nimport { ProductListingScreen } from './ProductListingScreen';\nimport { CartProvider } from './CartContext';\n\nexport default function App() {\n    return (\n        <SafeAreaProvider>\n            <CartProvider>\n                <ProductListingScreen />\n            </CartProvider>\n        </SafeAreaProvider>\n    );\n}", "language": "typescript"}, {"name": "CartContext.tsx", "content": "import React, { createContext, useContext, useState, useCallback } from 'react';\nimport { Product } from './types';\n\ninterface CartItem extends Product {\n    quantity: number;\n    selectedSize: string;\n    selectedColor: string;\n}\n\ninterface CartContextType {\n    items: CartItem[];\n    addToCart: (product: Product, size: string, color: string) => void;\n    removeFromCart: (productId: string, size: string, color: string) => void;\n    updateQuantity: (productId: string, size: string, color: string, quantity: number) => void;\n    clearCart: () => void;\n    totalItems: number;\n    totalAmount: number;\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\nexport const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n    const [items, setItems] = useState<CartItem[]>([]);\n\n    const addToCart = useCallback((product: Product, size: string, color: string) => {\n        setItems((currentItems) => {\n            const existingItemIndex = currentItems.findIndex(\n                (item) =>\n                    item.id === product.id &&\n                    item.selectedSize === size &&\n                    item.selectedColor === color\n            );\n\n            if (existingItemIndex > -1) {\n                return currentItems.map((item, index) =>\n                    index === existingItemIndex\n                        ? { ...item, quantity: item.quantity + 1 }\n                        : item\n                );\n            }\n\n            return [...currentItems, { ...product, quantity: 1, selectedSize: size, selectedColor: color }];\n        });\n    }, []);\n\n    const removeFromCart = useCallback((productId: string, size: string, color: string) => {\n        setItems((currentItems) =>\n            currentItems.filter(\n                (item) =>\n                    !(item.id === productId &&\n                        item.selectedSize === size &&\n                        item.selectedColor === color)\n            )\n        );\n    }, []);\n\n    const updateQuantity = useCallback((productId: string, size: string, color: string, quantity: number) => {\n        setItems((currentItems) =>\n            currentItems.map((item) =>\n                item.id === productId &&\n                item.selectedSize === size &&\n                item.selectedColor === color\n                    ? { ...item, quantity }\n                    : item\n            )\n        );\n    }, []);\n\n    const clearCart = useCallback(() => {\n        setItems([]);\n    }, []);\n\n    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);\n    const totalAmount = items.reduce((sum, item) => sum + item.price * item.quantity, 0);\n\n    return (\n        <CartContext.Provider\n            value={{\n                items,\n                addToCart,\n                removeFromCart,\n                updateQuantity,\n                clearCart,\n                totalItems,\n                totalAmount,\n            }}\n        >\n            {children}\n        </CartContext.Provider>\n    );\n};\n\nexport const useCart = () => {\n    const context = useContext(CartContext);\n    if (context === undefined) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n};", "language": "typescript"}, {"name": "CartScreen.tsx", "content": "import React from 'react';\nimport {\n    View,\n    Text,\n    StyleSheet,\n    ScrollView,\n    TouchableOpacity,\n    Image,\n    Dimensions,\n} from 'react-native';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { useCart } from './CartContext';\n\nconst { width } = Dimensions.get('window');\n\ninterface CartScreenProps {\n    onClose: () => void;\n}\n\nexport const CartScreen: React.FC<CartScreenProps> = ({ onClose }) => {\n    const { items, removeFromCart, updateQuantity, totalItems, totalAmount } = useCart();\n\n    if (items.length === 0) {\n        return (\n            <View style={styles.container}>\n                <TouchableOpacity style={styles.closeButton} onPress={onClose}>\n                    <Text style={styles.closeButtonText}>×</Text>\n                </TouchableOpacity>\n                <View style={styles.emptyContainer}>\n                    <Text style={styles.brandText}>MAISON</Text>\n                    <Text style={styles.emptyTitle}>Your cart is empty</Text>\n                    <Text style={styles.emptySubtitle}>Start shopping to add items to your cart</Text>\n                    <TouchableOpacity style={styles.continueShopping} onPress={onClose}>\n                        <Text style={styles.continueShoppingText}>Continue Shopping</Text>\n                    </TouchableOpacity>\n                </View>\n            </View>\n        );\n    }\n\n    return (\n        <View style={styles.container}>\n            <TouchableOpacity style={styles.closeButton} onPress={onClose}>\n                <Text style={styles.closeButtonText}>×</Text>\n            </TouchableOpacity>\n\n            <Text style={styles.brandText}>MAISON</Text>\n            <Text style={styles.title}>Shopping Cart</Text>\n            <Text style={styles.subtitle}>{totalItems} items</Text>\n\n            <ScrollView style={styles.itemsContainer}>\n                {items.map((item, index) => (\n                    <View key={`${item.id}-${item.selectedSize}-${item.selectedColor}-${index}`} style={styles.cartItem}>\n                        <Image source={{ uri: item.imageUrl }} style={styles.itemImage} />\n                        <View style={styles.itemDetails}>\n                            <Text style={styles.itemName}>{item.name}</Text>\n                            <Text style={styles.itemSize}>Size: {item.selectedSize}</Text>\n                            <View style={styles.colorContainer}>\n                                <Text style={styles.itemColor}>Color: </Text>\n                                <View\n                                    style={[styles.colorDot, { backgroundColor: item.selectedColor }]}\n                                />\n                            </View>\n                            <Text style={styles.itemPrice}>${(item.price * item.quantity).toFixed(2)}</Text>\n\n                            <View style={styles.quantityContainer}>\n                                <TouchableOpacity\n                                    style={styles.quantityButton}\n                                    onPress={() => {\n                                        if (item.quantity > 1) {\n                                            updateQuantity(item.id, item.selectedSize, item.selectedColor, item.quantity - 1);\n                                        }\n                                    }}\n                                >\n                                    <Text style={styles.quantityButtonText}>−</Text>\n                                </TouchableOpacity>\n                                <Text style={styles.quantity}>{item.quantity}</Text>\n                                <TouchableOpacity\n                                    style={styles.quantityButton}\n                                    onPress={() => updateQuantity(item.id, item.selectedSize, item.selectedColor, item.quantity + 1)}\n                                >\n                                    <Text style={styles.quantityButtonText}>+</Text>\n                                </TouchableOpacity>\n                            </View>\n                        </View>\n                        <TouchableOpacity\n                            style={styles.removeButton}\n                            onPress={() => removeFromCart(item.id, item.selectedSize, item.selectedColor)}\n                        >\n                            <Text style={styles.removeButtonText}>×</Text>\n                        </TouchableOpacity>\n                    </View>\n                ))}\n            </ScrollView>\n\n            <LinearGradient\n                colors={['rgba(255,255,255,0.8)', '#ffffff']}\n                style={styles.summary}\n            >\n                <View style={styles.summaryRow}>\n                    <Text style={styles.summaryText}>Subtotal</Text>\n                    <Text style={styles.summaryAmount}>${totalAmount.toFixed(2)}</Text>\n                </View>\n                <View style={styles.summaryRow}>\n                    <Text style={styles.summaryText}>Shipping</Text>\n                    <Text style={styles.summaryAmount}>Free</Text>\n                </View>\n                <View style={[styles.summaryRow, styles.totalRow]}>\n                    <Text style={styles.totalText}>Total</Text>\n                    <Text style={styles.totalAmount}>${totalAmount.toFixed(2)}</Text>\n                </View>\n                <TouchableOpacity style={styles.checkoutButton}>\n                    <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>\n                </TouchableOpacity>\n            </LinearGradient>\n        </View>\n    );\n};\n\nconst styles = StyleSheet.create({\n    container: {\n        flex: 1,\n        backgroundColor: '#fff',\n    },\n    closeButton: {\n        position: 'absolute',\n        top: 50,\n        right: 20,\n        zIndex: 10,\n        width: 32,\n        height: 32,\n        borderRadius: 16,\n        backgroundColor: 'rgba(0,0,0,0.5)',\n        justifyContent: 'center',\n        alignItems: 'center',\n    },\n    closeButtonText: {\n        color: '#fff',\n        fontSize: 24,\n        lineHeight: 24,\n    },\n    brandText: {\n        fontSize: 20,\n        fontWeight: '300',\n        letterSpacing: 4,\n        color: '#1a1a1a',\n        marginTop: 60,\n        marginBottom: 16,\n        textAlign: 'center',\n    },\n    title: {\n        fontSize: 24,\n        fontWeight: '600',\n        marginLeft: 20,\n        color: '#000',\n    },\n    subtitle: {\n        fontSize: 14,\n        color: '#666',\n        marginLeft: 20,\n        marginBottom: 20,\n    },\n    itemsContainer: {\n        flex: 1,\n        paddingHorizontal: 20,\n    },\n    cartItem: {\n        flexDirection: 'row',\n        marginBottom: 20,\n        backgroundColor: '#fff',\n        borderRadius: 12,\n        padding: 10,\n        shadowColor: '#000',\n        shadowOffset: { width: 0, height: 2 },\n        shadowOpacity: 0.1,\n        shadowRadius: 4,\n        elevation: 3,\n    },\n    itemImage: {\n        width: 100,\n        height: 120,\n        borderRadius: 8,\n        backgroundColor: '#f5f5f5',\n    },\n    itemDetails: {\n        flex: 1,\n        marginLeft: 15,\n    },\n    itemName: {\n        fontSize: 16,\n        fontWeight: '500',\n        marginBottom: 4,\n    },\n    itemSize: {\n        fontSize: 14,\n        color: '#666',\n        marginBottom: 4,\n    },\n    colorContainer: {\n        flexDirection: 'row',\n        alignItems: 'center',\n        marginBottom: 4,\n    },\n    itemColor: {\n        fontSize: 14,\n        color: '#666',\n    },\n    colorDot: {\n        width: 16,\n        height: 16,\n        borderRadius: 8,\n        marginLeft: 4,\n        borderWidth: 1,\n        borderColor: '#ddd',\n    },\n    itemPrice: {\n        fontSize: 16,\n        fontWeight: '600',\n        marginBottom: 8,\n    },\n    quantityContainer: {\n        flexDirection: 'row',\n        alignItems: 'center',\n    },\n    quantityButton: {\n        width: 28,\n        height: 28,\n        borderRadius: 14,\n        backgroundColor: '#f5f5f5',\n        justifyContent: 'center',\n        alignItems: 'center',\n    },\n    quantityButtonText: {\n        fontSize: 16,\n        color: '#000',\n    },\n    quantity: {\n        marginHorizontal: 12,\n        fontSize: 16,\n    },\n    removeButton: {\n        padding: 4,\n    },\n    removeButtonText: {\n        fontSize: 20,\n        color: '#999',\n    },\n    summary: {\n        padding: 20,\n        paddingBottom: 40,\n        borderTopWidth: 1,\n        borderTopColor: '#f0f0f0',\n    },\n    summaryRow: {\n        flexDirection: 'row',\n        justifyContent: 'space-between',\n        marginBottom: 10,\n    },\n    summaryText: {\n        fontSize: 14,\n        color: '#666',\n    },\n    summaryAmount: {\n        fontSize: 14,\n        color: '#000',\n    },\n    totalRow: {\n        marginTop: 10,\n        paddingTop: 10,\n        borderTopWidth: 1,\n        borderTopColor: '#f0f0f0',\n    },\n    totalText: {\n        fontSize: 16,\n        fontWeight: '600',\n        color: '#000',\n    },\n    totalAmount: {\n        fontSize: 18,\n        fontWeight: '600',\n        color: '#000',\n    },\n    checkoutButton: {\n        backgroundColor: '#000',\n        paddingVertical: 16,\n        borderRadius: 30,\n        alignItems: 'center',\n        marginTop: 20,\n    },\n    checkoutButtonText: {\n        color: '#fff',\n        fontSize: 16,\n        fontWeight: '600',\n    },\n    emptyContainer: {\n        flex: 1,\n        justifyContent: 'center',\n        alignItems: 'center',\n        paddingHorizontal: 40,\n    },\n    emptyTitle: {\n        fontSize: 24,\n        fontWeight: '600',\n        marginBottom: 12,\n        color: '#000',\n    },\n    emptySubtitle: {\n        fontSize: 16,\n        color: '#666',\n        textAlign: 'center',\n        marginBottom: 30,\n    },\n    continueShopping: {\n        paddingHorizontal: 30,\n        paddingVertical: 12,\n        borderRadius: 25,\n        backgroundColor: '#000',\n    },\n    continueShoppingText: {\n        color: '#fff',\n        fontSize: 16,\n        fontWeight: '500',\n    },\n});", "language": "typescript"}, {"name": "FilterBar.tsx", "content": "import React from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';\nimport { SortOption } from './types';\n\ninterface FilterBarProps {\n    selectedCategory: string;\n    onCategoryChange: (category: string) => void;\n    sortOption: SortOption;\n    onSortChange: (sort: SortOption) => void;\n    onFilterPress: () => void;\n}\n\nconst categories = ['All', 'Dresses', 'Tops', 'Pants', 'Blazers', 'Accessories'];\n\nexport const FilterBar: React.FC<FilterBarProps> = ({\n                                                        selectedCategory,\n                                                        onCategoryChange,\n                                                        sortOption,\n                                                        onSortChange,\n                                                        onFilterPress,\n                                                    }) => {\n    return (\n        <View style={styles.container}>\n            <ScrollView\n                horizontal\n                showsHorizontalScrollIndicator={false}\n                contentContainerStyle={styles.categoriesContainer}\n            >\n                {categories.map((category) => (\n                    <TouchableOpacity\n                        key={category}\n                        style={[\n                            styles.categoryButton,\n                            selectedCategory === category && styles.selectedCategory,\n                        ]}\n                        onPress={() => onCategoryChange(category)}\n                    >\n                        <Text\n                            style={[\n                                styles.categoryText,\n                                selectedCategory === category && styles.selectedCategoryText,\n                            ]}\n                        >\n                            {category}\n                        </Text>\n                    </TouchableOpacity>\n                ))}\n            </ScrollView>\n\n            <View style={styles.actionButtons}>\n                <TouchableOpacity\n                    style={styles.sortButton}\n                    onPress={() => {\n                        const sorts: SortOption[] = ['price-asc', 'price-desc', 'name-asc', 'name-desc', 'newest'];\n                        const currentIndex = sorts.indexOf(sortOption);\n                        const nextSort = sorts[(currentIndex + 1) % sorts.length];\n                        onSortChange(nextSort);\n                    }}\n                >\n                    <Text style={styles.actionButtonText}>\n                        {sortOption === 'price-asc' && '↑ Price'}\n                        {sortOption === 'price-desc' && '↓ Price'}\n                        {sortOption === 'name-asc' && '↑ Name'}\n                        {sortOption === 'name-desc' && '↓ Name'}\n                        {sortOption === 'newest' && 'Newest'}\n                    </Text>\n                </TouchableOpacity>\n\n                <TouchableOpacity style={styles.filterButton} onPress={onFilterPress}>\n                    <Text style={styles.actionButtonText}>Filters</Text>\n                </TouchableOpacity>\n            </View>\n        </View>\n    );\n};\n\nconst styles = StyleSheet.create({\n    container: {\n        backgroundColor: '#fff',\n        paddingVertical: 8,\n        borderBottomWidth: 1,\n        borderBottomColor: '#f0f0f0',\n    },\n    categoriesContainer: {\n        paddingHorizontal: 16,\n        gap: 8,\n    },\n    categoryButton: {\n        paddingHorizontal: 16,\n        paddingVertical: 6,\n        borderRadius: 20,\n        backgroundColor: '#f8f8f8',\n    },\n    selectedCategory: {\n        backgroundColor: '#000',\n    },\n    categoryText: {\n        fontSize: 13,\n        color: '#666',\n    },\n    selectedCategoryText: {\n        color: '#fff',\n    },\n    actionButtons: {\n        flexDirection: 'row',\n        justifyContent: 'flex-end',\n        paddingHorizontal: 16,\n        marginTop: 8,\n        gap: 8,\n    },\n    sortButton: {\n        paddingHorizontal: 12,\n        paddingVertical: 6,\n        borderRadius: 16,\n        backgroundColor: '#f0f0f0',\n    },\n    filterButton: {\n        paddingHorizontal: 12,\n        paddingVertical: 6,\n        borderRadius: 16,\n        backgroundColor: '#000',\n    },\n    actionButtonText: {\n        fontSize: 12,\n        color: '#000',\n        fontWeight: '500',\n    },\n});", "language": "typescript"}, {"name": "ProductCard.tsx", "content": "import React from 'react';\nimport { View, Text, Image, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { Product } from './types';\n\ntype ProductCardProps = Product & {\n    onPress: () => void;\n    onFavoritePress: () => void;\n};\n\nconst { width } = Dimensions.get('window');\nconst cardWidth = width / 2 - 24;\n\nexport const ProductCard: React.FC<ProductCardProps> = ({\n                                                            name,\n                                                            price,\n                                                            description,\n                                                            imageUrl,\n                                                            onPress,\n                                                            onFavoritePress,\n                                                            isNew,\n                                                            isFavorite,\n                                                            sizes,\n                                                            colors,\n                                                        }) => {\n    return (\n        <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.9}>\n            <View style={styles.imageContainer}>\n                <Image\n                    source={{ uri: imageUrl }}\n                    style={styles.image}\n                    resizeMode=\"cover\"\n                />\n                <TouchableOpacity\n                    style={styles.favoriteButton}\n                    onPress={onFavoritePress}\n                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}\n                >\n                    <Text style={styles.favoriteIcon}>{isFavorite ? '♥' : '♡'}</Text>\n                </TouchableOpacity>\n                {isNew && (\n                    <LinearGradient\n                        colors={['#000', '#333']}\n                        style={styles.newBadge}\n                    >\n                        <Text style={styles.newBadgeText}>NEW</Text>\n                    </LinearGradient>\n                )}\n            </View>\n            <View style={styles.content}>\n                <Text style={styles.name} numberOfLines={1}>{name}</Text>\n                <Text style={styles.description} numberOfLines={2}>\n                    {description}\n                </Text>\n                <View style={styles.detailsRow}>\n                    <Text style={styles.price}>${price.toFixed(2)}</Text>\n                    <View style={styles.sizesContainer}>\n                        {sizes.slice(0, 3).map((size, index) => (\n                            <Text key={size} style={styles.size}>\n                                {size}{index < Math.min(sizes.length - 1, 2) && ', '}\n                            </Text>\n                        ))}\n                        {sizes.length > 3 && <Text style={styles.size}>...</Text>}\n                    </View>\n                </View>\n                <View style={styles.colorsContainer}>\n                    {colors.map((color) => (\n                        <View\n                            key={color}\n                            style={[styles.colorDot, { backgroundColor: color }]}\n                        />\n                    ))}\n                </View>\n            </View>\n        </TouchableOpacity>\n    );\n};\n\nconst styles = StyleSheet.create({\n    card: {\n        backgroundColor: '#fff',\n        borderRadius: 12,\n        margin: 8,\n        width: cardWidth,\n        shadowColor: '#000',\n        shadowOffset: {\n            width: 0,\n            height: 2,\n        },\n        shadowOpacity: 0.1,\n        shadowRadius: 4,\n        elevation: 3,\n        overflow: 'hidden',\n    },\n    imageContainer: {\n        position: 'relative',\n    },\n    image: {\n        width: '100%',\n        height: cardWidth * 1.3,\n        backgroundColor: '#f5f5f5',\n    },\n    favoriteButton: {\n        position: 'absolute',\n        top: 8,\n        right: 8,\n        backgroundColor: 'rgba(255,255,255,0.9)',\n        borderRadius: 15,\n        width: 30,\n        height: 30,\n        justifyContent: 'center',\n        alignItems: 'center',\n    },\n    favoriteIcon: {\n        fontSize: 18,\n        color: '#ff4d4d',\n    },\n    newBadge: {\n        position: 'absolute',\n        top: 8,\n        left: 8,\n        paddingHorizontal: 8,\n        paddingVertical: 4,\n        borderRadius: 4,\n    },\n    newBadgeText: {\n        color: '#fff',\n        fontSize: 10,\n        fontWeight: '600',\n    },\n    content: {\n        padding: 12,\n    },\n    name: {\n        fontSize: 13,\n        fontWeight: '600',\n        color: '#1a1a1a',\n        marginBottom: 4,\n    },\n    description: {\n        fontSize: 11,\n        color: '#666',\n        marginBottom: 8,\n        lineHeight: 16,\n    },\n    detailsRow: {\n        flexDirection: 'row',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: 8,\n    },\n    price: {\n        fontSize: 14,\n        fontWeight: '700',\n        color: '#1a1a1a',\n    },\n    sizesContainer: {\n        flexDirection: 'row',\n    },\n    size: {\n        fontSize: 11,\n        color: '#666',\n    },\n    colorsContainer: {\n        flexDirection: 'row',\n        gap: 4,\n    },\n    colorDot: {\n        width: 12,\n        height: 12,\n        borderRadius: 6,\n        borderWidth: 1,\n        borderColor: '#f0f0f0',\n    },\n});", "language": "typescript"}, {"name": "ProductDetailsScreen.tsx", "content": "import React, { useState } from 'react';\nimport {\n    View,\n    Text,\n    ScrollView,\n    StyleSheet,\n    Dimensions,\n    TouchableOpacity,\n    Animated,\n    Image,\n} from 'react-native';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { Product } from './types';\nimport { useCart } from './CartContext';\n\nconst { width, height } = Dimensions.get('window');\n\ninterface ProductDetailsScreenProps {\n    product: Product;\n    onClose: () => void;\n}\n\nexport const ProductDetailsScreen: React.FC<ProductDetailsScreenProps> = ({\n                                                                              product,\n                                                                              onClose,\n                                                                          }) => {\n    const [selectedSize, setSelectedSize] = useState<string>('');\n    const [selectedColor, setSelectedColor] = useState<string>('');\n    const buttonAnimation = new Animated.Value(0);\n    const [isAdding, setIsAdding] = useState(false);\n    const { addToCart } = useCart();\n\n    const handleAddToCart = () => {\n        if (!selectedSize || !selectedColor) return;\n\n        setIsAdding(true);\n        addToCart(product, selectedSize, selectedColor);\n\n        Animated.sequence([\n            Animated.timing(buttonAnimation, {\n                toValue: 1,\n                duration: 300,\n                useNativeDriver: true,\n            }),\n            Animated.timing(buttonAnimation, {\n                toValue: 0,\n                duration: 300,\n                useNativeDriver: true,\n            }),\n        ]).start(() => {\n            setTimeout(() => {\n                setIsAdding(false);\n                onClose();\n            }, 500);\n        });\n    };\n\n    const buttonScale = buttonAnimation.interpolate({\n        inputRange: [0, 0.5, 1],\n        outputRange: [1, 0.9, 1],\n    });\n\n    const additionalImages = [\n        'https://images.pexels.com/photos/6786894/pexels-photo-6786894.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        'https://images.pexels.com/photos/7870762/pexels-photo-7870762.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        'https://images.pexels.com/photos/6786894/pexels-photo-6786894.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        'https://images.pexels.com/photos/4046567/pexels-photo-4046567.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n    ];\n\n    return (\n        <View style={styles.container}>\n            <TouchableOpacity style={styles.closeButton} onPress={onClose}>\n                <Text style={styles.closeButtonText}>×</Text>\n            </TouchableOpacity>\n\n            <View style={styles.brandTag}>\n                <Text style={styles.brandText}>MAISON</Text>\n            </View>\n\n            <ScrollView\n                style={styles.scrollView}\n                showsVerticalScrollIndicator={false}\n                stickyHeaderIndices={[1]}\n            >\n                <View style={styles.gallery}>\n                    <Image\n                        source={{ uri: product.imageUrl }}\n                        style={styles.mainImage}\n                        resizeMode=\"cover\"\n                    />\n                    <ScrollView\n                        horizontal\n                        showsHorizontalScrollIndicator={false}\n                        style={styles.thumbnailScroll}\n                    >\n                        {additionalImages.map((image, index) => (\n                            <Image\n                                key={index}\n                                source={{ uri: image }}\n                                style={styles.thumbnail}\n                                resizeMode=\"cover\"\n                            />\n                        ))}\n                    </ScrollView>\n                </View>\n\n                <LinearGradient\n                    colors={['#fff', 'rgba(255,255,255,0.9)']}\n                    style={styles.stickyHeader}\n                >\n                    <Text style={styles.productName}>{product.name}</Text>\n                    <Text style={styles.price}>${product.price.toFixed(2)}</Text>\n                </LinearGradient>\n\n                <View style={styles.details}>\n                    <Text style={styles.description}>{product.description}</Text>\n\n                    <Text style={styles.sectionTitle}>Size</Text>\n                    <View style={styles.sizesContainer}>\n                        {product.sizes.map((size) => (\n                            <TouchableOpacity\n                                key={size}\n                                style={[\n                                    styles.sizeButton,\n                                    selectedSize === size && styles.selectedOption,\n                                ]}\n                                onPress={() => setSelectedSize(size)}\n                            >\n                                <Text\n                                    style={[\n                                        styles.sizeButtonText,\n                                        selectedSize === size && styles.selectedOptionText,\n                                    ]}\n                                >\n                                    {size}\n                                </Text>\n                            </TouchableOpacity>\n                        ))}\n                    </View>\n\n                    <Text style={styles.sectionTitle}>Color</Text>\n                    <View style={styles.colorsContainer}>\n                        {product.colors.map((color) => (\n                            <TouchableOpacity\n                                key={color}\n                                style={[\n                                    styles.colorButton,\n                                    { backgroundColor: color },\n                                    selectedColor === color && styles.selectedColorButton,\n                                ]}\n                                onPress={() => setSelectedColor(color)}\n                            />\n                        ))}\n                    </View>\n\n                    <View style={styles.spacer} />\n                </View>\n            </ScrollView>\n\n            <LinearGradient\n                colors={['rgba(255,255,255,0.9)', '#fff']}\n                style={styles.bottomBar}\n            >\n                <Animated.View\n                    style={[\n                        styles.addToCartButton,\n                        { transform: [{ scale: buttonScale }] },\n                    ]}\n                >\n                    <TouchableOpacity\n                        onPress={handleAddToCart}\n                        disabled={!selectedSize || !selectedColor || isAdding}\n                        style={[\n                            styles.addToCartTouchable,\n                            (!selectedSize || !selectedColor) && styles.disabledButton,\n                        ]}\n                    >\n                        <Text style={styles.addToCartText}>\n                            {isAdding ? 'Added!' : 'Add to Cart'}\n                        </Text>\n                    </TouchableOpacity>\n                </Animated.View>\n            </LinearGradient>\n        </View>\n    );\n};\n\nconst styles = StyleSheet.create({\n    container: {\n        flex: 1,\n        backgroundColor: '#fff',\n    },\n    closeButton: {\n        position: 'absolute',\n        top: 50,\n        right: 20,\n        zIndex: 10,\n        width: 32,\n        height: 32,\n        borderRadius: 16,\n        backgroundColor: 'rgba(0,0,0,0.5)',\n        justifyContent: 'center',\n        alignItems: 'center',\n    },\n    closeButtonText: {\n        color: '#fff',\n        fontSize: 24,\n        lineHeight: 24,\n    },\n    brandTag: {\n        position: 'absolute',\n        top: 50,\n        left: 20,\n        zIndex: 10,\n    },\n    brandText: {\n        fontSize: 16,\n        fontWeight: '300',\n        letterSpacing: 3,\n        color: '#1a1a1a',\n    },\n    scrollView: {\n        flex: 1,\n    },\n    gallery: {\n        width: width,\n    },\n    mainImage: {\n        width: width,\n        height: height * 0.6,\n    },\n    thumbnailScroll: {\n        padding: 8,\n        backgroundColor: '#fff',\n    },\n    thumbnail: {\n        width: 60,\n        height: 80,\n        marginRight: 8,\n        borderRadius: 4,\n    },\n    stickyHeader: {\n        padding: 16,\n        backgroundColor: '#fff',\n        borderBottomWidth: 1,\n        borderBottomColor: '#f0f0f0',\n    },\n    productName: {\n        fontSize: 20,\n        fontWeight: '600',\n        color: '#000',\n    },\n    price: {\n        fontSize: 18,\n        color: '#666',\n        marginTop: 4,\n    },\n    details: {\n        padding: 16,\n    },\n    description: {\n        fontSize: 14,\n        lineHeight: 22,\n        color: '#444',\n        marginBottom: 24,\n    },\n    sectionTitle: {\n        fontSize: 16,\n        fontWeight: '600',\n        color: '#000',\n        marginBottom: 12,\n    },\n    sizesContainer: {\n        flexDirection: 'row',\n        flexWrap: 'wrap',\n        gap: 8,\n        marginBottom: 24,\n    },\n    sizeButton: {\n        paddingHorizontal: 16,\n        paddingVertical: 8,\n        borderRadius: 20,\n        borderWidth: 1,\n        borderColor: '#ddd',\n    },\n    selectedOption: {\n        backgroundColor: '#000',\n        borderColor: '#000',\n    },\n    sizeButtonText: {\n        fontSize: 14,\n        color: '#000',\n    },\n    selectedOptionText: {\n        color: '#fff',\n    },\n    colorsContainer: {\n        flexDirection: 'row',\n        gap: 12,\n        marginBottom: 24,\n    },\n    colorButton: {\n        width: 32,\n        height: 32,\n        borderRadius: 16,\n        borderWidth: 1,\n        borderColor: '#ddd',\n    },\n    selectedColorButton: {\n        borderWidth: 2,\n        borderColor: '#000',\n    },\n    spacer: {\n        height: 100,\n    },\n    bottomBar: {\n        position: 'absolute',\n        bottom: 0,\n        left: 0,\n        right: 0,\n        padding: 16,\n        paddingBottom: 32,\n    },\n    addToCartButton: {\n        width: '100%',\n    },\n    addToCartTouchable: {\n        backgroundColor: '#000',\n        paddingVertical: 16,\n        borderRadius: 30,\n        alignItems: 'center',\n    },\n    disabledButton: {\n        backgroundColor: '#ccc',\n    },\n    addToCartText: {\n        color: '#fff',\n        fontSize: 16,\n        fontWeight: '600',\n    },\n});", "language": "typescript"}, {"name": "ProductListingScreen.tsx", "content": "import React, { useState, useCallback, useRef } from 'react';\nimport {\n    View,\n    StyleSheet,\n    FlatList,\n    Text,\n    RefreshControl,\n    Modal,\n    TouchableOpacity,\n    Image,\n    Animated,\n    Dimensions,\n    TouchableWithoutFeedback\n} from 'react-native';\nimport { StatusBar } from 'expo-status-bar';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { ProductCard } from './ProductCard';\nimport { FilterBar } from './FilterBar';\nimport { Product, SortOption } from './types';\nimport { ProductDetailsScreen } from './ProductDetailsScreen';\nimport { CartScreen } from './CartScreen';\nimport { SideMenu } from './SideMenu';\nimport { useCart } from './CartContext';\n\nconst initialProducts: Product[] = [\n    {\n        id: '1',\n        name: 'Silk Evening Gown',\n        price: 2899.99,\n        description: 'Exquisite silk evening gown with hand-embroidered crystals and a flowing silhouette. Features a delicate sweetheart neckline and an elegant train. Made in our Paris atelier with the finest Italian silk.',\n        imageUrl: 'https://images.pexels.com/photos/12633692/pexels-photo-12633692.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        sizes: ['XS', 'S', 'M', 'L'],\n        colors: ['#F8F0E3', '#000000', '#8B0000'],\n        category: 'Dresses',\n        isNew: true,\n        isFavorite: false,\n    },\n    {\n        id: '2',\n        name: 'Cashmere Wrap Coat',\n        price: 3499.99,\n        description: 'Luxurious double-faced cashmere coat with a relaxed silhouette. Hand-finished edges and horn buttons. Made from the finest Mongolian cashmere, expertly tailored in our Milano atelier.',\n        imageUrl: 'https://images.pexels.com/photos/6800942/pexels-photo-6800942.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        sizes: ['S', 'M', 'L', 'XL'],\n        colors: ['#DEB887', '#1A1A1A', '#F5F5DC'],\n        category: 'Outerwear',\n        isNew: true,\n        isFavorite: true,\n    },\n    {\n        id: '3',\n        name: 'Leather Palazzo Pants',\n        price: 1899.99,\n        description: 'High-waisted palazzo pants in butter-soft leather. Features a wide-leg silhouette and expert pleating. Handcrafted from the finest Italian leather with a silk lining.',\n        imageUrl: 'https://images.pexels.com/photos/11559288/pexels-photo-11559288.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        sizes: ['S', 'M', 'L'],\n        colors: ['#000000', '#8B4513', '#F5F5F5'],\n        category: 'Pants',\n        isNew: false,\n        isFavorite: false,\n    },\n    {\n        id: '4',\n        name: 'Embellished Silk Blouse',\n        price: 1299.99,\n        description: 'Delicate silk crepe blouse with hand-sewn pearl embellishments. Features a high neck and flowing sleeves. Each pearl is individually selected and placed by our artisans.',\n        imageUrl: 'https://images.pexels.com/photos/17879064/pexels-photo-17879064.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        sizes: ['XS', 'S', 'M', 'L'],\n        colors: ['#FFFFFF', '#FFF5EE', '#000000'],\n        category: 'Tops',\n        isNew: true,\n        isFavorite: false,\n    },\n    {\n        id: '5',\n        name: 'Structured Wool Blazer',\n        price: 2499.99,\n        description: 'Impeccably tailored wool blazer with silk lining. Features hand-stitched peaked lapels and genuine horn buttons. Made from the finest Italian wool in our signature cut.',\n        imageUrl: 'https://images.pexels.com/photos/10651182/pexels-photo-10651182.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        sizes: ['S', 'M', 'L', 'XL'],\n        colors: ['#000000', '#1A1A1A', '#2F4F4F'],\n        category: 'Blazers',\n        isNew: false,\n        isFavorite: true,\n    },\n    {\n        id: '6',\n        name: 'Crystal Evening Clutch',\n        price: 1799.99,\n        description: 'Hand-embellished evening clutch with Swarovski crystals. Features a vintage-inspired clasp and Italian leather lining. Each piece takes over 40 hours to craft.',\n        imageUrl: 'https://images.pexels.com/photos/1989164/pexels-photo-1989164.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',\n        sizes: ['ONE SIZE'],\n        colors: ['#FFD700', '#C0C0C0', '#000000'],\n        category: 'Accessories',\n        isNew: true,\n        isFavorite: false,\n    }\n];\n\nexport const ProductListingScreen = () => {\n    const [products, setProducts] = useState<Product[]>(initialProducts);\n    const [selectedCategory, setSelectedCategory] = useState('All');\n    const [sortOption, setSortOption] = useState<SortOption>('newest');\n    const [refreshing, setRefreshing] = useState(false);\n    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);\n    const [isCartVisible, setIsCartVisible] = useState(false);\n    const [isMenuVisible, setIsMenuVisible] = useState(false);\n    const { totalItems } = useCart();\n\n    const menuTranslateX = useRef(new Animated.Value(-Dimensions.get('window').width)).current;\n\n    const toggleMenu = (show: boolean) => {\n        Animated.spring(menuTranslateX, {\n            toValue: show ? 0 : -Dimensions.get('window').width,\n            useNativeDriver: true,\n            damping: 20,\n            mass: 1,\n            stiffness: 100,\n        }).start();\n        setIsMenuVisible(show);\n    };\n\n    const handleFavoritePress = useCallback((productId: string) => {\n        setProducts((prevProducts) =>\n            prevProducts.map((product) =>\n                product.id === productId\n                    ? { ...product, isFavorite: !product.isFavorite }\n                    : product\n            )\n        );\n    }, []);\n\n    const filteredAndSortedProducts = React.useMemo(() => {\n        let result = [...products];\n\n        if (selectedCategory !== 'All') {\n            result = result.filter((product) => product.category === selectedCategory);\n        }\n\n        switch (sortOption) {\n            case 'price-asc':\n                result.sort((a, b) => a.price - b.price);\n                break;\n            case 'price-desc':\n                result.sort((a, b) => b.price - a.price);\n                break;\n            case 'name-asc':\n                result.sort((a, b) => a.name.localeCompare(b.name));\n                break;\n            case 'name-desc':\n                result.sort((a, b) => b.name.localeCompare(a.name));\n                break;\n            case 'newest':\n                result.sort((a, b) => (a.isNew === b.isNew ? 0 : a.isNew ? -1 : 1));\n                break;\n        }\n\n        return result;\n    }, [products, selectedCategory, sortOption]);\n\n    const onRefresh = useCallback(() => {\n        setRefreshing(true);\n        setTimeout(() => {\n            setRefreshing(false);\n        }, 1500);\n    }, []);\n\n    return (\n        <View style={styles.container}>\n            <StatusBar style=\"dark\" />\n\n            {/* Overlay */}\n            {isMenuVisible && (\n                <TouchableWithoutFeedback onPress={() => toggleMenu(false)}>\n                    <View style={styles.overlay} />\n                </TouchableWithoutFeedback>\n            )}\n\n            {/* Side Menu */}\n            <SideMenu\n                isVisible={isMenuVisible}\n                onClose={() => toggleMenu(false)}\n                translateX={menuTranslateX}\n            />\n\n            <LinearGradient\n                colors={['#f8f9fa', '#ffffff']}\n                style={styles.header}\n            >\n                <View style={styles.logoContainer}>\n                    <TouchableOpacity\n                        style={styles.menuButton}\n                        onPress={() => toggleMenu(true)}\n                    >\n                        <View style={styles.menuIcon}>\n                            <View style={styles.menuLine} />\n                            <View style={[styles.menuLine, styles.menuLineShort]} />\n                            <View style={styles.menuLine} />\n                        </View>\n                    </TouchableOpacity>\n                    <View style={styles.brandContainer}>\n                        <Text style={styles.title}>MAISON</Text>\n                        <Text style={styles.subtitle}>PARIS • MILANO • NEW YORK</Text>\n                    </View>\n                    <TouchableOpacity\n                        style={styles.cartButton}\n                        onPress={() => setIsCartVisible(true)}\n                    >\n                        <Text style={styles.cartIcon}>🛍️</Text>\n                        {totalItems > 0 && (\n                            <View style={styles.badge}>\n                                <Text style={styles.badgeText}>{totalItems}</Text>\n                            </View>\n                        )}\n                    </TouchableOpacity>\n                </View>\n                <View style={styles.seasonTag}>\n                    <Text style={styles.seasonText}>FALL/WINTER 2024</Text>\n                </View>\n            </LinearGradient>\n\n            <FilterBar\n                selectedCategory={selectedCategory}\n                onCategoryChange={setSelectedCategory}\n                sortOption={sortOption}\n                onSortChange={setSortOption}\n                onFilterPress={() => console.log('Filter pressed')}\n            />\n\n            <FlatList\n                data={filteredAndSortedProducts}\n                renderItem={({ item }) => (\n                    <ProductCard\n                        {...item}\n                        onPress={() => setSelectedProduct(item)}\n                        onFavoritePress={() => handleFavoritePress(item.id)}\n                    />\n                )}\n                keyExtractor={(item) => item.id}\n                numColumns={2}\n                showsVerticalScrollIndicator={false}\n                contentContainerStyle={styles.listContent}\n                refreshControl={\n                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />\n                }\n                ListEmptyComponent={\n                    <View style={styles.emptyContainer}>\n                        <Text style={styles.emptyText}>No products found</Text>\n                    </View>\n                }\n            />\n\n            <Modal\n                visible={selectedProduct !== null}\n                animationType=\"slide\"\n                presentationStyle=\"fullScreen\"\n            >\n                {selectedProduct && (\n                    <ProductDetailsScreen\n                        product={selectedProduct}\n                        onClose={() => setSelectedProduct(null)}\n                    />\n                )}\n            </Modal>\n\n            <Modal\n                visible={isCartVisible}\n                animationType=\"slide\"\n                presentationStyle=\"fullScreen\"\n            >\n                <CartScreen onClose={() => setIsCartVisible(false)} />\n            </Modal>\n        </View>\n    );\n};\n\nconst styles = StyleSheet.create({\n    container: {\n        flex: 1,\n        backgroundColor: '#fff',\n    },\n    overlay: {\n        ...StyleSheet.absoluteFillObject,\n        backgroundColor: 'rgba(0,0,0,0.3)',\n        zIndex: 999,\n    },\n    header: {\n        paddingTop: 60,\n        paddingBottom: 20,\n        paddingHorizontal: 20,\n    },\n    logoContainer: {\n        flexDirection: 'row',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        marginBottom: 8,\n    },\n    menuButton: {\n        width: 40,\n        height: 40,\n        justifyContent: 'center',\n        alignItems: 'center',\n    },\n    menuIcon: {\n        width: 24,\n        height: 20,\n        justifyContent: 'space-between',\n    },\n    menuLine: {\n        width: '100%',\n        height: 1,\n        backgroundColor: '#1a1a1a',\n    },\n    menuLineShort: {\n        width: '70%',\n    },\n    brandContainer: {\n        alignItems: 'center',\n    },\n    title: {\n        fontSize: 28,\n        fontWeight: '300',\n        color: '#1a1a1a',\n        letterSpacing: 6,\n        textAlign: 'center',\n    },\n    subtitle: {\n        fontSize: 9,\n        color: '#666',\n        letterSpacing: 2,\n        marginTop: 4,\n        fontWeight: '500',\n    },\n    seasonTag: {\n        alignItems: 'center',\n        marginTop: 8,\n    },\n    seasonText: {\n        fontSize: 11,\n        color: '#888',\n        letterSpacing: 1.5,\n        fontWeight: '400',\n    },\n    cartButton: {\n        width: 40,\n        height: 40,\n        justifyContent: 'center',\n        alignItems: 'center',\n        backgroundColor: 'rgba(255,255,255,0.9)',\n        borderRadius: 20,\n        shadowColor: '#000',\n        shadowOffset: {\n            width: 0,\n            height: 2,\n        },\n        shadowOpacity: 0.1,\n        shadowRadius: 3,\n        elevation: 3,\n    },\n    cartIcon: {\n        fontSize: 20,\n    },\n    badge: {\n        position: 'absolute',\n        top: -5,\n        right: -5,\n        backgroundColor: '#1a1a1a',\n        borderRadius: 10,\n        minWidth: 20,\n        height: 20,\n        justifyContent: 'center',\n        alignItems: 'center',\n        paddingHorizontal: 4,\n    },\n    badgeText: {\n        color: '#fff',\n        fontSize: 11,\n        fontWeight: '600',\n    },\n    listContent: {\n        padding: 8,\n    },\n    emptyContainer: {\n        flex: 1,\n        justifyContent: 'center',\n        alignItems: 'center',\n        padding: 20,\n    },\n    emptyText: {\n        fontSize: 16,\n        color: '#666',\n    },\n});", "language": "typescript"}, {"name": "SideMenu.tsx", "content": "import React from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity, ScrollView, Animated } from 'react-native';\nimport { LinearGradient } from 'expo-linear-gradient';\n\ninterface SideMenuProps {\n    isVisible: boolean;\n    onClose: () => void;\n    translateX: Animated.Value;\n}\n\nexport const SideMenu: React.FC<SideMenuProps> = ({ isVisible, onClose, translateX }) => {\n    const menuItems = [\n        { title: 'New Arrivals', subtitle: 'Fall/Winter 2024' },\n        { title: 'Collections', subtitle: 'Curated Elegance' },\n        { title: 'Atelier', subtitle: 'Made to Measure' },\n        { title: 'Personal Stylist', subtitle: 'Book Appointment' },\n        { title: 'Private Events', subtitle: 'Exclusive Access' },\n        { title: 'Heritage', subtitle: 'Our Story' },\n        { title: 'Sustainability', subtitle: 'Our Commitment' },\n        { title: 'Care Guide', subtitle: 'Product Care' },\n    ];\n\n    if (!isVisible) return null;\n\n    return (\n        <Animated.View\n            style={[\n                styles.container,\n                {\n                    transform: [{ translateX }]\n                }\n            ]}\n        >\n            <LinearGradient\n                colors={['#ffffff', '#f8f9fa']}\n                style={styles.content}\n            >\n                <View style={styles.header}>\n                    <Text style={styles.brandName}>MAISON</Text>\n                    <TouchableOpacity style={styles.closeButton} onPress={onClose}>\n                        <Text style={styles.closeText}>×</Text>\n                    </TouchableOpacity>\n                </View>\n\n                <ScrollView style={styles.menuItems} showsVerticalScrollIndicator={false}>\n                    {menuItems.map((item, index) => (\n                        <TouchableOpacity\n                            key={item.title}\n                            style={[\n                                styles.menuItem,\n                                index === 0 && styles.firstMenuItem\n                            ]}\n                        >\n                            <Text style={styles.menuTitle}>{item.title}</Text>\n                            <Text style={styles.menuSubtitle}>{item.subtitle}</Text>\n                        </TouchableOpacity>\n                    ))}\n                </ScrollView>\n\n                <View style={styles.footer}>\n                    <TouchableOpacity style={styles.footerItem}>\n                        <Text style={styles.footerTitle}>Concierge Service</Text>\n                        <Text style={styles.footerSubtitle}>Available 24/7</Text>\n                    </TouchableOpacity>\n\n                    <TouchableOpacity style={styles.footerItem}>\n                        <Text style={styles.footerTitle}>Exclusive Preview</Text>\n                        <Text style={styles.footerSubtitle}>Join Waitlist</Text>\n                    </TouchableOpacity>\n                </View>\n\n                <View style={styles.seasonLabel}>\n                    <Text style={styles.seasonText}>FALL/WINTER 2024</Text>\n                </View>\n            </LinearGradient>\n        </Animated.View>\n    );\n};\n\nconst styles = StyleSheet.create({\n    container: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        bottom: 0,\n        width: '80%',\n        backgroundColor: '#fff',\n        zIndex: 1000,\n        shadowColor: '#000',\n        shadowOffset: {\n            width: 2,\n            height: 0,\n        },\n        shadowOpacity: 0.15,\n        shadowRadius: 15,\n        elevation: 10,\n    },\n    content: {\n        flex: 1,\n        paddingTop: 60,\n    },\n    header: {\n        paddingHorizontal: 24,\n        paddingBottom: 40,\n        flexDirection: 'row',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n    },\n    brandName: {\n        fontSize: 24,\n        fontWeight: '300',\n        letterSpacing: 4,\n        color: '#1a1a1a',\n    },\n    closeButton: {\n        width: 32,\n        height: 32,\n        alignItems: 'center',\n        justifyContent: 'center',\n    },\n    closeText: {\n        fontSize: 24,\n        color: '#1a1a1a',\n        lineHeight: 24,\n    },\n    menuItems: {\n        flex: 1,\n    },\n    menuItem: {\n        paddingVertical: 20,\n        paddingHorizontal: 24,\n        borderTopWidth: 1,\n        borderTopColor: 'rgba(0,0,0,0.05)',\n    },\n    firstMenuItem: {\n        borderTopWidth: 0,\n    },\n    menuTitle: {\n        fontSize: 16,\n        fontWeight: '400',\n        color: '#1a1a1a',\n        marginBottom: 4,\n    },\n    menuSubtitle: {\n        fontSize: 12,\n        color: '#666',\n    },\n    footer: {\n        padding: 24,\n        borderTopWidth: 1,\n        borderTopColor: 'rgba(0,0,0,0.05)',\n    },\n    footerItem: {\n        marginBottom: 20,\n    },\n    footerTitle: {\n        fontSize: 14,\n        fontWeight: '500',\n        color: '#1a1a1a',\n        marginBottom: 2,\n    },\n    footerSubtitle: {\n        fontSize: 12,\n        color: '#666',\n    },\n    seasonLabel: {\n        position: 'absolute',\n        bottom: 20,\n        right: 20,\n        transform: [{ rotate: '-90deg' }],\n        transformOrigin: 'right bottom',\n    },\n    seasonText: {\n        fontSize: 10,\n        letterSpacing: 1,\n        color: '#999',\n    },\n});", "language": "typescript"}, {"name": "types.ts", "content": "export interface Product {\n    id: string;\n    name: string;\n    price: number;\n    description: string;\n    imageUrl: string;\n    sizes: string[];\n    colors: string[];\n    category: string;\n    isNew: boolean;\n    isFavorite: boolean;\n}\n\nexport type SortOption = 'price-asc' | 'price-desc' | 'name-asc' | 'name-desc' | 'newest';\n\nexport interface FilterOptions {\n    categories: string[];\n    priceRange: [number, number];\n    sizes: string[];\n    colors: string[];\n    onlyNew: boolean;\n}", "language": "typescript"}]}]