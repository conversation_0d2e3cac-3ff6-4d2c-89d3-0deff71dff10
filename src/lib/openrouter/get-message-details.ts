import {round} from "lodash";
import {models} from "@/lib/ai/models";

export function calculateCosts(data: TokenConsumptionInput) {
    const modelConfig = models.find(m => m.apiIdentifier === data.model);
    if (!modelConfig) {
        console.warn(`Model ${data.model} not found in configuration`);
        return {inputCost: 0, outputCost: 0}
    }

    // Prices in models.ts are per million tokens
    const inputCost = modelConfig.inputCost
        ? (data.promptTokens / 1_000_000) * modelConfig.inputCost
        : 0;

    const outputCost = modelConfig.outputCost
        ? (data.completionTokens / 1_000_000) * modelConfig.outputCost
        : 0;

    return {inputCost, outputCost};
}


export const getMessageDetailsFromOpenrouter = async (data: TokenConsumptionInput) => {
    const costs = calculateCosts(data);

    console.log('saveTokenConsumption', data)
    let totalCost = round(costs.inputCost + costs.outputCost, 6);
    let outputCost = round(costs.outputCost, 6);
    let inputCost = round(costs.inputCost, 6);
    let cachingDiscount = 0;
    let cacheDiscountPercent = 0;
    let subtotal = totalCost;
    if(data.remoteProviderId) {
        const body = await fetchFromOpenRouter(data.remoteProviderId);
        if(body !== null) {
            totalCost = body.data.total_cost;
            cachingDiscount = body.data.cache_discount;
            subtotal = cachingDiscount + totalCost;

            console.log('Subtotal', subtotal);
            console.log('CachingDiscount Cost', cachingDiscount);
            console.log('finalUsage Cost', totalCost);

            console.log('Percent', (cachingDiscount / subtotal) * 100)
            cacheDiscountPercent = round((cachingDiscount / subtotal) * 100, 2);
        }
    }

    return {
        totalCost,
        cachingDiscount,
        subtotal,
        cacheDiscountPercent,
        inputCost,
        outputCost
    }
}

export const fetchFromOpenRouter = async (remoteProviderId: string) => {
    try {
        await new Promise(resolve => setTimeout(resolve, 3000));
        const cachingResponse = await fetch(`https://openrouter.ai/api/v1/generation?id=${remoteProviderId}`, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${process.env.OPENROUTER_API_KEY}`
            },
        });

        return await cachingResponse.json();
    } catch (e) {
        console.log('Error fetching the response for cache data', e);
        return null;
    }
}