import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function sendPasswordResetEmail({
  email,
  resetLink,
}: {
  email: string;
  resetLink: string;
}) {
  try {
    const result = await resend.emails.send({
      from: 'magically <<EMAIL>>',
      to: email,
      subject: 'Reset your password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; color: #333;">
          <p style="margin-bottom: 20px;">Hello,</p>
          
          <p style="margin-bottom: 20px;">We received a request to reset your password for your magically account. Security is important to us, so we've made this process simple and secure. Please click the link below to create a new password:</p>
          
          <p style="margin-bottom: 20px;"><a href="${resetLink}" style="color: #4F46E5;">Reset your password</a></p>
          
          <p style="margin-bottom: 20px;">This link will expire in 24 hours for security reasons. If you need a new reset link after that time, you can request another one through our login page.</p>
          
          <p style="margin-bottom: 20px;">If you didn't request this password reset, you can safely ignore this email. Your account security is important to us, and no changes will be made without following the link above.</p>
          
          <p style="margin-bottom: 20px;">Need help or have questions? Just reply directly to this email and we'll get back to you as soon as possible. We're here to make your experience with magically as smooth as possible.</p>
          
          <p style="margin-top: 30px;">
            Thanks,<br>
            The magically team
          </p>
        </div>
      `,
      text: `Hello,

We received a request to reset your password for your magically account. Security is important to us, so we've made this process simple and secure. Please click the link below to create a new password:

${resetLink}

This link will expire in 24 hours for security reasons. If you need a new reset link after that time, you can request another one through our login page.

If you didn't request this password reset, you can safely ignore this email. Your account security is important to us, and no changes will be made without following the link above.

Need help or have questions? Just reply directly to this email and we'll get back to you as soon as possible. We're here to make your experience with magically as smooth as possible.

Thanks,
The magically team`,
      replyTo: '<EMAIL>',
      tags: [
        {
          name: 'email_type',
          value: 'password_reset',
        },
      ],
    });
    console.log('result', result)
  } catch (error) {
    console.error('Failed to send reset email:', error);
    throw error;
  }
}

export async function sendWelcomeEmail({
  email,
  name,
}: {
  email: string;
  name: string;
}) {
  try {
    const result = await resend.emails.send({
      from: 'Rajat <<EMAIL>>',
      to: email,
      subject: 'Quick hello and welcome to magically',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; color: #333;">
          <p style="margin-bottom: 20px;">Hey ${name},</p>
          
          <p style="margin-bottom: 20px;">Just wanted to say hello and thank you for joining us at magically. It's great to have you with us on this journey. I'm genuinely excited to see what you'll create with our platform.</p>
          
          <p style="margin-bottom: 20px;">We are on a mission to empower users in bringing their ideas to life without getting bogged down by technical complexities. Our goal is simple: be the most technologically advanced product and yet be so enchantingly simple to use that literally anyone can use it. We want to take on all the complexity ourselves so you can focus entirely on your vision and creativity.</p>
          
          <p style="margin-bottom: 20px;">Your feedback would be incredibly valuable as we continue to refine and improve magically. Be it positive or negative, small or big, it helps us directly enhance the customer experience. We're committed to ensuring that magically never gets in the way of people bringing their ideas to life, but rather amplifies their creative potential.</p>
          
          <p style="margin-bottom: 20px;">If you face any issues or find something confusing, please don't hesitate to reach out. You can reply to this email, post in our Discord, or message me directly. We're here to support you every step of the way and want your experience to be as smooth as possible.</p>
          
          <p style="margin-bottom: 15px;">I'd love to connect with you:</p>
          <ul style="margin-bottom: 20px;">
            <li style="margin-bottom: 10px;"><a href="https://calendly.com/rajat-magically/30min" style="color: #4F46E5;">Chat with me directly</a> - I'm always happy to hear about your experience</li>
            <li style="margin-bottom: 10px;"><a href="https://discord.gg/Cpda56yVYY" style="color: #4F46E5;">Join our Discord community</a> - Connect with other creators and get support</li>
          </ul>
          
          <p style="margin-bottom: 20px;">Remember, every piece of feedback helps us make the experience a little more magical. We're building this for you, and your input shapes what magically becomes.</p>
          
          <p style="margin-top: 30px;">
            Cheers,<br>
            Rajat
          </p>
        </div>
      `,
      text: `Hey ${name},

Just wanted to say hello and thank you for joining us at magically. It's great to have you with us on this journey. I'm genuinely excited to see what you'll create with our platform.

We are on a mission to empower users in bringing their ideas to life without getting bogged down by technical complexities. Our goal is simple: be the most technologically advanced product and yet be so enchantingly simple to use that literally anyone can use it. We want to take on all the complexity ourselves so you can focus entirely on your vision and creativity.

Your feedback would be incredibly valuable as we continue to refine and improve magically. Be it positive or negative, small or big, it helps us directly enhance the customer experience. We're committed to ensuring that magically never gets in the way of people bringing their ideas to life, but rather amplifies their creative potential.

If you face any issues or find something confusing, please don't hesitate to reach out. You can reply to this email, post in our Discord, or message me directly. We're here to support you every step of the way and want your experience to be as smooth as possible.

I'd love to connect with you:
- Chat with me directly: https://calendly.com/rajat-magically/30min - I'm always happy to hear about your experience
- Join our Discord community: https://discord.gg/Cpda56yVYY - Connect with other creators and get support

Remember, every piece of feedback helps us make the experience a little more magical. We're building this for you, and your input shapes what magically becomes.

Cheers,
Rajat`,
      replyTo: '<EMAIL>',
      tags: [
        {
          name: 'email_type',
          value: 'welcome',
        },
      ],
    });
    console.log('Welcome email sent:', result);
    return result;
  } catch (error) {
    console.error('Failed to send welcome email:', error);
    throw error;
  }
}
