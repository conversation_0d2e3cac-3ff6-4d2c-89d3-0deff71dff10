/**
 * Enhanced in-memory store for design preview sessions
 * Supports incremental updates and tracks user interactions
 */

interface SessionData {
  html: string;
  prompt: string;
  lastUpdated: Date;
  status: 'initializing' | 'generating' | 'complete' | 'error';
  // Track incremental updates
  contentChunks: string[];
  // Track user interactions (scroll position, form inputs, etc.)
  userState?: {
    scrollPosition?: { x: number; y: number };
    formValues?: Record<string, string>;
    activeElement?: string;
  };
}

// In-memory store for sessions
const sessions = new Map<string, SessionData>();

// Default expiry time (30 minutes)
const SESSION_EXPIRY_MS = 30 * 60 * 1000;

/**
 * Create a new design preview session
 */
export function createSession(sessionId: string, prompt: string): void {
  sessions.set(sessionId, {
    html: getInitialHTML(),
    prompt,
    lastUpdated: new Date(),
    status: 'initializing',
    contentChunks: [],
    userState: {
      scrollPosition: { x: 0, y: 0 },
      formValues: {},
      activeElement: ''
    }
  });

  // Set up automatic cleanup after session expires
  setTimeout(() => {
    sessions.delete(sessionId);
  }, SESSION_EXPIRY_MS);
}

/**
 * Get session data by ID
 */
export function getSession(sessionId: string): SessionData | undefined {
  return sessions.get(sessionId);
}

/**
 * Update HTML content for a session
 */
export function updateSessionHTML(sessionId: string, html: string): void {
  const session = sessions.get(sessionId);
  if (session) {
    session.html = html;
    session.lastUpdated = new Date();
    sessions.set(sessionId, session);
  }
}

/**
 * Add a content chunk to a session
 * This allows for incremental updates without replacing the entire HTML
 */
export function addContentChunk(sessionId: string, chunk: string): void {
  const session = sessions.get(sessionId);
  if (session) {
    session.contentChunks.push(chunk);
    // Also update the full HTML for clients that don't support incremental updates
    const extractedHtml = extractHtmlFromChunk(chunk);
    if (extractedHtml) {
      session.html = extractedHtml;
    }
    session.lastUpdated = new Date();
    sessions.set(sessionId, session);
  }
}

/**
 * Extract HTML content from a chunk that may contain <PREVIEW_HTML> tags
 */
function extractHtmlFromChunk(chunk: string): string | null {
  // Look for content between <PREVIEW_HTML> tags
  const match = chunk.match(/<PREVIEW_HTML>([\s\S]*?)<\/PREVIEW_HTML>/i);
  if (match && match[1]) {
    let extractedHtml = match[1].trim();

    // Ensure it has proper DOCTYPE and Tailwind
    if (!extractedHtml.includes('<!DOCTYPE html>')) {
      extractedHtml = `<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;">
<script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
${extractedHtml}
</body>
</html>`;
    } else if (!extractedHtml.includes('tailwindcss')) {
      // If it already has DOCTYPE but missing Tailwind, add it
      extractedHtml = extractedHtml.replace('<head>', `<head>
<meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;">
<script src="https://cdn.tailwindcss.com"></script>`);
    }

    return extractedHtml;
  }
  return null;
}

/**
 * Update session status
 */
export function updateSessionStatus(
  sessionId: string,
  status: 'initializing' | 'generating' | 'complete' | 'error'
): void {
  const session = sessions.get(sessionId);
  if (session) {
    session.status = status;
    session.lastUpdated = new Date();
    sessions.set(sessionId, session);
  }
}

/**
 * Get initial loading HTML
 */
function getInitialHTML(): string {
  return `
    <div class="mobile-frame">
      <div class="screen flex items-center justify-center">
        <div class="text-center p-4">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-gray-500">Generating your design...</p>
        </div>
      </div>
    </div>
  `;
}

/**
 * Update user state for a session
 * This allows the iframe to maintain user interactions between refreshes
 */
export function updateUserState(
  sessionId: string,
  userState: Partial<SessionData['userState']>
): void {
  const session = sessions.get(sessionId);
  if (session) {
    session.userState = { ...session.userState, ...userState };
    session.lastUpdated = new Date();
    sessions.set(sessionId, session);
  }
}

/**
 * Get all content chunks for a session
 */
export function getContentChunks(sessionId: string): string[] {
  const session = sessions.get(sessionId);
  return session ? session.contentChunks : [];
}

/**
 * Clean up expired sessions (call this periodically if needed)
 */
export function cleanupExpiredSessions(): void {
  const now = new Date();
  for (const [sessionId, session] of sessions.entries()) {
    if (now.getTime() - session.lastUpdated.getTime() > SESSION_EXPIRY_MS) {
      sessions.delete(sessionId);
    }
  }
}

// Set up periodic cleanup if running on server
if (typeof window === 'undefined') {
  const CLEANUP_INTERVAL = 15 * 60 * 1000; // 15 minutes
  setInterval(cleanupExpiredSessions, CLEANUP_INTERVAL);
}
