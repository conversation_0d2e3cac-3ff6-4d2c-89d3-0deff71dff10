/**
 * Redis-based store for design preview sessions
 * Uses a similar approach to the media API for caching
 */
import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// Session TTL in seconds (30 minutes)
const SESSION_TTL = 30 * 60;

// Session data interface
export interface Screen {
  name: string;
  content: string;
  styles?: string;
}

export interface SessionData {
  html: string;
  finalHtml: string;
  prompt: string;
  lastUpdated: Date;
  status: 'initializing' | 'generating' | 'complete' | 'error';
  contentChunks: string[];
  screens: Screen[];
  userState: {
    scrollPosition: { x: number; y: number };
    formValues: Record<string, any>;
    activeElement: string;
  };
}

/**
 * Helper to get cache key for session data
 */
function getSessionKey(sessionId: string): string {
  return `design:session:${sessionId}`;
}

/**
 * Helper to get cache key for content chunks
 */
function getChunksKey(sessionId: string): string {
  return `design:chunks:${sessionId}`;
}

/**
 * Create a new design preview session
 */
export async function createSession(sessionId: string, prompt: string): Promise<SessionData> {
  const session: SessionData = {
    html: '',
    finalHtml: '',
    prompt,
    lastUpdated: new Date(),
    status: 'initializing',
    contentChunks: [],
    screens: [],
    userState: {
      scrollPosition: { x: 0, y: 0 },
      formValues: {},
      activeElement: '',
    },
  };

  await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
  return session;
}

/**
 * Get session data by ID
 */
export async function getSession(sessionId: string): Promise<SessionData | undefined> {
  try {
    // Try to get from Redis
    const sessionData = await redis.get(getSessionKey(sessionId));
    if (sessionData) {
      // Handle different types of data that might come from Redis
      if (typeof sessionData === 'string') {
        return JSON.parse(sessionData);
      } else if (typeof sessionData === 'object') {
        return sessionData as SessionData;
      }
    }

    // If not in Redis, check fallback store
    return fallbackStore.get(sessionId);
  } catch (error) {
    console.error(`Error getting session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    return fallbackStore.get(sessionId);
  }
}

/**
 * Update HTML content for a session
 * This is a simple storage operation with no content processing
 */
export async function updateSessionHTML(sessionId: string, html: string): Promise<void> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) return;

    // Update HTML and timestamp
    session.html = html;
    session.lastUpdated = new Date();

    // Save back to Redis with renewed TTL
    await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
  } catch (error) {
    console.error(`Error updating HTML for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      session.html = html;
      session.lastUpdated = new Date();
      fallbackStore.set(sessionId, session);
    }
  }
}

/**
 * Update final HTML content for a session
 * This stores the complete, processed HTML separately from the incremental HTML
 */
export async function updateFinalHTML(sessionId: string, html: string): Promise<void> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) return;

    // Update finalHtml and timestamp
    session.finalHtml = html;
    session.lastUpdated = new Date();

    // Save back to Redis with renewed TTL
    await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
    console.log(`Updated final HTML for session ${sessionId}`);
  } catch (error) {
    console.error(`Error updating final HTML for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      session.finalHtml = html;
      session.lastUpdated = new Date();
      fallbackStore.set(sessionId, session);
    }
  }
}

/**
 * Add a content chunk to a session
 * This allows for incremental updates without replacing the entire HTML
 */
export async function addContentChunk(sessionId: string, chunk: string): Promise<void> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) {
      console.log(`Session ${sessionId} not found, creating new session`);
      await createSession(sessionId, 'Generated from chunk');
      const newSession = await getSession(sessionId);
      if (!newSession) return;

      // Continue with the new session
      newSession.contentChunks.push(chunk);
      newSession.lastUpdated = new Date();
      newSession.finalHtml = ''; // Initialize finalHtml
      await redis.set(getSessionKey(sessionId), newSession, { ex: SESSION_TTL });
      
      // Store the chunk in a separate list for efficient retrieval
      // Use rpush to maintain correct order (oldest first)
      await redis.rpush(getChunksKey(sessionId), chunk);
      await redis.expire(getChunksKey(sessionId), SESSION_TTL);
      return;
    }

    // Add chunk to the session's content chunks
    session.contentChunks.push(chunk);
    session.lastUpdated = new Date();

    // Save back to Redis with renewed TTL
    await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });

    // Store the chunk in a separate list for efficient retrieval
    // Use rpush to maintain correct order (oldest first)
    await redis.rpush(getChunksKey(sessionId), chunk);
    await redis.expire(getChunksKey(sessionId), SESSION_TTL);
  } catch (error) {
    console.error(`Error adding chunk for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      session.contentChunks.push(chunk);
      session.lastUpdated = new Date();
      fallbackStore.set(sessionId, session);
    } else {
      // Create a new session in the fallback store
      const newSession: SessionData = {
        html: '',
        finalHtml: '',
        prompt: 'Generated from chunk',
        lastUpdated: new Date(),
        status: 'generating',
        contentChunks: [chunk],
        screens: [],
        userState: {
          scrollPosition: { x: 0, y: 0 },
          formValues: {},
          activeElement: ''
        }
      };
      fallbackStore.set(sessionId, newSession);
    }
  }
}

// Removed extractHtmlFromChunk function as it's not the responsibility of the Redis store

/**
 * Screen Management Functions
 */

/**
 * Add a screen to the session
 */
export async function addScreenToSession(
  sessionId: string,
  name: string,
  content: string,
  styles?: string
): Promise<void> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found for adding screen`);
      return;
    }

    // Check if screen with this name already exists
    const existingIndex = session.screens.findIndex(s => s.name === name);
    if (existingIndex >= 0) {
      // Update existing screen
      session.screens[existingIndex] = {
        name,
        content,
        styles
      };
    } else {
      // Add new screen
      session.screens.push({
        name,
        content,
        styles
      });
    }

    // Update timestamp
    session.lastUpdated = new Date();

    // Save back to Redis with renewed TTL
    await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
  } catch (error) {
    console.error(`Error adding screen to session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      const existingIndex = session.screens.findIndex(s => s.name === name);
      if (existingIndex >= 0) {
        session.screens[existingIndex] = { name, content, styles };
      } else {
        session.screens.push({ name, content, styles });
      }
      session.lastUpdated = new Date();
      fallbackStore.set(sessionId, session);
    }
  }
}

/**
 * Replace a screen in the session
 */
export async function replaceScreenInSession(
  sessionId: string,
  name: string,
  content: string,
  styles?: string
): Promise<void> {
  // This is effectively the same as addScreenToSession since we check for existing screens
  return addScreenToSession(sessionId, name, content, styles);
}

/**
 * Delete a screen from the session
 */
export async function deleteScreenFromSession(
  sessionId: string,
  name: string
): Promise<void> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found for deleting screen`);
      return;
    }

    // Remove screen with matching name
    session.screens = session.screens.filter(s => s.name !== name);

    // Update timestamp
    session.lastUpdated = new Date();

    // Save back to Redis with renewed TTL
    await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
  } catch (error) {
    console.error(`Error deleting screen from session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      session.screens = session.screens.filter(s => s.name !== name);
      session.lastUpdated = new Date();
      fallbackStore.set(sessionId, session);
    }
  }
}

/**
 * Get all screens for a session
 */
export async function getScreensForSession(
  sessionId: string
): Promise<Screen[]> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found for getting screens`);
      return [];
    }

    return session.screens;
  } catch (error) {
    console.error(`Error getting screens for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      return session.screens;
    }
    return [];
  }
}

/**
 * Update session status
 */
export async function updateSessionStatus(
  sessionId: string,
  status: 'initializing' | 'generating' | 'complete' | 'error'
): Promise<void> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) {
      console.log(`Session ${sessionId} not found, creating new session`);
      await createSession(sessionId, 'Generated for status update');
      const newSession = await getSession(sessionId);
      if (!newSession) return;

      // Update the new session
      newSession.status = status;
      newSession.lastUpdated = new Date();
      newSession.finalHtml = ''; // Initialize finalHtml
      await redis.set(getSessionKey(sessionId), newSession, { ex: SESSION_TTL });
      return;
    }

    // Update status and timestamp
    session.status = status;
    session.lastUpdated = new Date();

    // Save back to Redis with renewed TTL
    await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
  } catch (error) {
    console.error(`Error updating status for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      session.status = status;
      session.lastUpdated = new Date();
      fallbackStore.set(sessionId, session);
    } else {
      // Create a new session in the fallback store
      const newSession: SessionData = {
        html: getInitialHTML(),
        finalHtml: '',
        prompt: 'Generated for status update',
        lastUpdated: new Date(),
        status: status,
        contentChunks: [],
        screens: [],
        userState: {
          scrollPosition: { x: 0, y: 0 },
          formValues: {},
          activeElement: ''
        }
      };
      fallbackStore.set(sessionId, newSession);
    }
  }
}

/**
 * Update user state for a session
 * This allows the iframe to maintain user interactions between refreshes
 */
export async function updateUserState(
  sessionId: string,
  userState: Partial<SessionData['userState']>
): Promise<void> {
  try {
    // Get current session
    const session = await getSession(sessionId);
    if (!session) return;

    // Update user state and timestamp
    session.userState = { ...session.userState, ...userState };
    session.lastUpdated = new Date();

    // Save back to Redis with renewed TTL
    await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
  } catch (error) {
    console.error(`Error updating user state for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      session.userState = { ...session.userState, ...userState };
      session.lastUpdated = new Date();
      fallbackStore.set(sessionId, session);
    }
  }
}

/**
 * Get all content chunks for a session
 */
export async function getContentChunks(sessionId: string): Promise<string[]> {
  try {
    // Try to get chunks from Redis list
    const chunks = await redis.lrange(getChunksKey(sessionId), 0, -1);
    if (chunks && chunks.length > 0) {
      return chunks;
    }

    // If not in Redis list, get from session
    const session = await getSession(sessionId);
    return session ? session.contentChunks : [];
  } catch (error) {
    console.error(`Error getting chunks for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    return session ? session.contentChunks : [];
  }
}

/**
 * Clear all content chunks for a session
 * This is useful when starting a new design generation for the same chat
 */
export async function clearContentChunks(sessionId: string): Promise<void> {
  try {
    // Delete the Redis list of chunks
    await redis.del(getChunksKey(sessionId));
    console.log(`Cleared content chunks for session ${sessionId}`);
    
    // Also update the session to have empty content chunks
    const session = await getSession(sessionId);
    if (session) {
      session.contentChunks = [];
      await redis.set(getSessionKey(sessionId), session, { ex: SESSION_TTL });
    }
  } catch (error) {
    console.error(`Error clearing chunks for session ${sessionId}:`, error);
    // Fall back to in-memory if Redis fails
    const session = fallbackStore.get(sessionId);
    if (session) {
      session.contentChunks = [];
      fallbackStore.set(sessionId, session);
    }
  }
}

/**
 * Get initial loading HTML
 * This is a placeholder that will be replaced by the actual content
 */
function getInitialHTML(): string {
  return '';
}

// Fallback in-memory store for when Redis is unavailable
const fallbackStore = new Map<string, SessionData>();

// Clean up expired sessions from the fallback store periodically
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    const now = new Date();
    for (const [sessionId, session] of fallbackStore.entries()) {
      if (now.getTime() - session.lastUpdated.getTime() > SESSION_TTL * 1000) {
        fallbackStore.delete(sessionId);
      }
    }
  }, 15 * 60 * 1000); // 15 minutes
}
