import {streamText, CoreMessage} from 'ai';
import {customModel} from '@/lib/ai';
import {
  updateSessionHTML, 
  updateSessionStatus, 
  addContentChunk, 
  updateFinalHTML,
  addScreenToSession,
  replaceScreenInSession,
  deleteScreenFromSession,
  getScreensForSession
} from '@/lib/store/redis-session-store';
import {debounce} from 'lodash';
import {LLMMediaService} from '@/lib/services/llm-media-service';
import {updateChat, getChatById, saveMessages} from '@/lib/db/queries';
import {generateUUID} from '@/lib/utils';
import {DESIGN_GENERATOR_SYSTEM_PROMPT} from "@/lib/ai/prompt-directory/design-generator-prompt";
import * as HtmlTemplateService from '@/lib/services/html-template-service';
import {ScreenParser, ScreenMeta} from '@/lib/parser/ScreenParser';

/**
 * Parse streaming content to extract HTML
 * This is the central place for HTML extraction and processing
 */
export function parseStreamingContent(content: string): string | null {
    // Extract HTML content using the template service
    const extractedHtml = HtmlTemplateService.extractHtmlFromContent(content);

    if (extractedHtml) {
        // Check if the content already has a mobile frame
        const hasMobileFrame = extractedHtml.includes('class="mobile-frame"') || 
                              extractedHtml.includes('class=\'mobile-frame\'');
        
        // If no mobile frame is present, wrap the content in one
        let processedHtml = extractedHtml;
        if (!hasMobileFrame && !extractedHtml.includes('<!DOCTYPE html>')) {
            processedHtml = `
            <div class="mobile-frame">
                <div class="screen">
                    ${extractedHtml}
                </div>
            </div>`;
        }
        
        // Ensure all mobile frames have the correct aspect ratio
        const htmlWithCorrectFrames = processedHtml.replace(
            /class="mobile-frame"([^>]*>)/g, 
            'class="mobile-frame" style="aspect-ratio: 433/882;"$1'
        );
        
        // Wrap or enhance the HTML with proper structure and styles
        if (htmlWithCorrectFrames.includes('<!DOCTYPE html>')) {
            return HtmlTemplateService.enhanceExistingHtml(htmlWithCorrectFrames);
        } else {
            return HtmlTemplateService.wrapHtmlContent(htmlWithCorrectFrames);
        }
    }
    
    return null;
}



/**
 * Get initial loading HTML for design preview
 * Returns a complete HTML document with loading indicator
 */
export function getInitialLoadingHTML(): string {
    return HtmlTemplateService.getLoadingHtml();
}

/**
 * Start the design generation process for a session
 * @param sessionId - The session ID (which is also the chat ID)
 * @param prompt - The user prompt for design generation
 * @param previousMessages - Optional array of previous messages for context
 */
export async function startDesignGeneration(sessionId: string, prompt: string, previousMessages: any[] = []): Promise<void> {
    try {
        // Update session status in Redis
        await updateSessionStatus(sessionId, 'generating');

        // Update chat status in database (sessionId is the chatId)
        await updateChat({
            id: sessionId,
            updatedAt: new Date(),
            designStatus: 'generating'
        });

        // Create a debounced version of the HTML update function
        // This ensures we don't update too frequently (every 2 seconds)
        const debouncedUpdateHTML = debounce(async (html: string) => {
            await updateSessionHTML(sessionId, html);
        }, 2000, {leading: true, trailing: true, maxWait: 2000});

        // Create a debounced function for updating the HTML with screens
        const debouncedUpdateScreensHTML = debounce(async () => {
            const screens = await getScreensForSession(sessionId);
            if (screens.length > 0) {
                const combinedHtml = HtmlTemplateService.wrapScreens(screens, 'generating');
                await updateSessionHTML(sessionId, combinedHtml);
            }
        }, 500, {leading: true, trailing: true, maxWait: 1000});

        // Initialize the screen parser
        const screenParser = new ScreenParser({
            onScreenStart: async (meta) => {
                console.log(`Screen started: ${meta.name}, mode: ${meta.mode}`);
                
                // Create an empty screen immediately when it starts
                if (meta.mode === 'append' || meta.mode === 'replace') {
                    // Add an initial placeholder content
                    await addScreenToSession(sessionId, meta.name, 
                        `<div class="flex flex-col h-full p-4 animate-pulse">
                            <div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
                            <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                            <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
                            <div class="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                            <div class="flex-1 bg-gray-100 rounded"></div>
                        </div>`, 
                        undefined);
                    
                    // Update the HTML immediately to show the placeholder
                    await debouncedUpdateScreensHTML();
                }
            },
            onScreenDelta: async (name, chunk) => {
                // Handle incremental updates to a screen
                console.log(`Screen delta for ${name}: ${chunk.length} chars`);
                
                // Get the current screen content
                const screens = await getScreensForSession(sessionId);
                const currentScreen = screens.find(s => s.name === name);
                
                if (currentScreen) {
                    // Get the current state from the parser
                    const state = screenParser.getState(messageId);
                    if (state?.currentScreen) {
                        // Update the screen with the accumulated content so far
                        await addScreenToSession(sessionId, name, state.currentScreen.content, state.currentScreen.styles);
                        
                        // Update the HTML with all screens
                        await debouncedUpdateScreensHTML();
                    }
                }
            },
            onScreenComplete: async (screen) => {
                console.log(`Screen complete: ${screen.name}`);
                
                // Process complete screen based on mode
                if (screen.mode === 'append' || screen.mode === 'replace') {
                    await addScreenToSession(sessionId, screen.name, screen.content, screen.styles);
                } else if (screen.mode === 'delete') {
                    await deleteScreenFromSession(sessionId, screen.name);
                }
                
                // Update the combined HTML with all screens
                await debouncedUpdateScreensHTML();
            },
            onParseError: (error) => {
                console.error(`Screen parsing error: ${error}`);
            }
        });

        // Create the system message with the same instructions as in preview-design route
        const systemMessage: CoreMessage = {
            role: 'system',
            content: DESIGN_GENERATOR_SYSTEM_PROMPT
        };

        // Start the AI generation with the same parameters as in preview-design route
        const result = streamText({
            model: customModel("anthropic/claude-3.7-sonnet"),
            messages: [
                systemMessage,
                // Include previous messages for context if available
                ...previousMessages
                    .filter(msg => msg.role === 'user' || msg.role === 'assistant')
                    .map(msg => ({
                        role: msg.role,
                        content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
                    })),
                // Add the current prompt as the latest user message
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
        });

        let accumulatedContent = '';
        const messageId = generateUUID(); // Unique ID for this message stream

        // Process the stream
        for await (const chunk of result.fullStream) {
            if (chunk.type === 'text-delta') {
                accumulatedContent += chunk.textDelta;

                // Add the chunk to the session for incremental updates
                await addContentChunk(sessionId, chunk.textDelta);

                // Parse the chunk using the screen parser
                screenParser.parse(messageId, chunk.textDelta);

                // Fallback: if no screens are detected, use the old method
                const screens = await getScreensForSession(sessionId);
                if (screens.length === 0) {
                    // Parse the content and update the session if we have valid HTML
                    const parsedHTML = parseStreamingContent(accumulatedContent);
                    if (parsedHTML) {
                        debouncedUpdateHTML(parsedHTML);
                    }
                }
            }
        }

        console.log('Design generation stream complete');
        // Ensure the final update is applied
        debouncedUpdateHTML.flush();

        // Get all screens for the final update
        const finalScreens = await getScreensForSession(sessionId);
        let finalHTML: string;

        if (finalScreens.length > 0) {
            // Use the screens approach
            finalHTML = HtmlTemplateService.wrapScreens(finalScreens, 'complete');
        } else {
            // Fallback to the old approach
            finalHTML = parseStreamingContent(accumulatedContent) || 
                `No valid HTML content generated`;
        }

        // Update Redis session with both regular HTML and final HTML
        await updateSessionHTML(sessionId, finalHTML);
        await updateFinalHTML(sessionId, finalHTML); // Store the final HTML separately
        await updateSessionStatus(sessionId, 'complete');

        // Update database chat record with the final HTML and status
        await updateChat({
            id: sessionId,
            updatedAt: new Date(),
            designHtml: finalHTML,
            designStatus: 'complete'
        });

        // Get the chat to get project and user info
        const chat = await getChatById({id: sessionId});
        if (chat) {
            // Save the AI's response as a message in the database
            await saveMessages({
                messages: [{
                    id: generateUUID(),
                    chatId: sessionId,
                    projectId: chat.projectId,
                    role: 'assistant',
                    content: accumulatedContent, // Save the full response including HTML
                    createdAt: new Date(),
                    userId: chat.userId,
                    remoteProvider: null,
                    remoteProviderId: null,
                    componentContexts: null
                }]
            });
        }

        console.log(`Design generation complete for session ${sessionId}`);
    } catch (error) {
        console.error(`Error generating design for session ${sessionId}:`, error);
        // Update Redis session with error state
        await updateSessionHTML(
            sessionId,
            `Error generating design`
        );
        await updateSessionStatus(sessionId, 'error');

        // Update database chat record with error status
        await updateChat({
            id: sessionId,
            updatedAt: new Date(),
            designStatus: 'error'
        });
    }
}
