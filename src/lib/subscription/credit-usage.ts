import { db } from '../db';
import { subscriptions, tokenConsumption } from '../db/schema';
import {eq, sql, desc, and, or, isNull, ne} from 'drizzle-orm';

export type CreditOperationType = 'code_write' | 'file_change' | 'sql_query' | 'tool_call' | 'add_ai_memory';

export interface CreditOperation {
    type: CreditOperationType;
    count: number;
}

export async function updateCreditUsage(userId: string, operations: CreditOperation[]) {
    console.log(`[Messages] Updating message usage for user ${userId}`);
    
    // Get the latest active subscription
    const subscription = await db
        .select()
        .from(subscriptions)
        .where(eq(subscriptions.userId, userId))
        .orderBy(desc(subscriptions.updatedAt));

    if (!subscription || subscription.length === 0) {
        console.log(`[Messages] No subscription found for user ${userId}`);
        return 0;
    }

    const sub = subscription.find(sub => sub.status === 'active') || subscription[0];
    if (!sub) {
        console.log(`[Messages] No active subscription found for user ${userId}`);
        return 0;
    }
    
    // Get the billing cycle start date
    const cycleStartDate = sub.createdAt;
    console.log(`[Messages] Current billing cycle started at: ${cycleStartDate}`);
    
    // Count the number of messages (token consumption records) in the current billing cycle
    const messageCountResult = await db
        .select({
            messageCount: sql<number>`COUNT(*)`
        })
        .from(tokenConsumption)
        .where(
            and(
                eq(tokenConsumption.userId, userId),
                // Use a safer approach that converts dates to ISO strings
                sql`${tokenConsumption.createdAt} >= ${cycleStartDate instanceof Date ? cycleStartDate.toISOString() : cycleStartDate}`,
                ne(tokenConsumption.discounted, true)
            ),
        );
    
    const messagesUsed = messageCountResult[0]?.messageCount || 0;
    console.log(`[Messages] Total messages used in current cycle: ${messagesUsed}`);
    
    // Update the subscription with the message count
    // Note: We're still using the creditsUsed field for now, but it now represents messages used
    await db.transaction(async (tx) => {
        await tx
            .update(subscriptions)
            .set({
                creditsUsed: messagesUsed, // This now represents messages used, not credits
                updatedAt: new Date(),
            })
            .where(eq(subscriptions.id, sub.id));
    });
    
    console.log(`[Messages] Updated subscription ${sub.id} with ${messagesUsed} messages used`);
    return messagesUsed;
}
