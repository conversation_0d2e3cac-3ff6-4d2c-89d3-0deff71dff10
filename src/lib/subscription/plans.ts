export type PlanTier = 'free' | 'starter' | 'pro' | 'plus' | 'prime' | 'anonymous';

// Feature identifiers for access control
export type FeatureId = 
  | 'code_download'
  | 'deployment_web'
  | 'deployment_android'
  | 'deployment_ios'
  | 'push_notifications'
  | 'over_the_air_updates'
  | 'advanced_code_generation'
  | 'version_control'
  | 'custom_integrations'
  | 'usage_analytics'
  | 'priority_support'
  | 'advanced_agent_mode';

export interface Plan {
  id: string;
  name: string;
  tier: PlanTier;
  hidden?: boolean;
  dailyLimit: number;
  messageLimit: number;
  dailyMessageLimit: number;
  price: number;
  operations: number;
  features: string[];
  limitations?: string[];
  isPopular?: boolean;
  allowedFeatures: FeatureId[];
}

export const PLANS: Plan[] = [
  {
    id: 'anonymous',
    name: 'Anonymous',
    tier: 'anonymous',
    price: 0,
    operations: 2,
    hidden: true,
    dailyLimit: 2,
    messageLimit: -1,
    dailyMessageLimit: -1,
    features: [
      'Single File Edits',
      'Community Support',
      'Web Preview'
    ],
    limitations: [
      'No Code Download',
      'No Web, Android or iOS Deployments',
      'No Custom Integrations',
      'Basic Agent Mode Only',
      'No Project Save'
    ],
    allowedFeatures: []
  },
  {
    id: 'free',
    name: 'Free',
    tier: 'free',
    price: 0,
    operations: 35,
    dailyLimit: 4,
    hidden: true,
    messageLimit: -1,
    dailyMessageLimit: -1,
    features: [
      'Single File Edits',
      'Community Support',
      'Web Preview'
    ],
    limitations: [
      'No Code Download',
      'No Web, Android or iOS Deployments',
      'No Custom Integrations',
      'Basic Agent Mode Only'
    ],
    allowedFeatures: [
      'deployment_web'
    ]
  },
  {
    id: 'starter',
    name: 'Starter',
    tier: 'starter',
    price: 15,
    operations: 75,
    hidden: false,
    messageLimit: -1,
    dailyMessageLimit: -1,
    dailyLimit: -1,
    features: [
      'Higher limits',
      'Code Download',
      'Android and iOS one click to stores',
      'Access to new features',
      'Community Support'
    ],
    limitations: [
    ],
    allowedFeatures: [
      'deployment_web',
      'deployment_android',
      'deployment_ios',
      'code_download',
      'push_notifications',
      'over_the_air_updates'
    ]
  },
  {
    id: 'pro',
    name: 'Pro',
    tier: 'pro',
    price: 60,
    operations: 325,
    hidden: false,
    messageLimit: -1,
    dailyMessageLimit: -1,
    dailyLimit: -1,
    isPopular: true,
    features: [
      'Everything in Starter',
      'Advanced Code Generation',
      // 'Version Control Integration',
      'Email Support'
    ],
    allowedFeatures: [
      'code_download',
      'deployment_web',
      'deployment_android',
      'deployment_ios',
      'push_notifications',
      'over_the_air_updates',
      'advanced_code_generation',
      'version_control',
      'advanced_agent_mode'
    ]
  },
  {
    id: 'plus',
    name: 'Plus',
    tier: 'plus',
    price: 99,
    operations: 575,
    hidden: false,
    messageLimit: -1,
    dailyMessageLimit: -1,
    dailyLimit: -1,
    features: [
      'Free auto error fixes',
      'Everything in Pro',
      'Priority Support',
      'Custom Integrations',
      'Usage Analytics'
    ],
    allowedFeatures: [
      'code_download',
      'deployment_web',
      'deployment_android',
      'deployment_ios',
      'push_notifications',
      'over_the_air_updates',
      'advanced_code_generation',
      'version_control',
      'advanced_agent_mode',
      'custom_integrations',
      'usage_analytics',
      'priority_support'
    ]
  },
  {
    id: 'prime',
    name: 'Prime',
    tier: 'prime',
    price: 199,
    operations: 1200,
    hidden: false,
    messageLimit: -1,
    dailyMessageLimit: -1,
    dailyLimit: -1,
    features: [
      'Everything in Plus',
      'Priority Support',
      '1-on-1 Consulting'
    ],
    allowedFeatures: [
      'code_download',
      'deployment_web',
      'deployment_android',
      'deployment_ios',
      'push_notifications',
      'over_the_air_updates',
      'advanced_code_generation',
      'version_control',
      'advanced_agent_mode',
      'custom_integrations',
      'usage_analytics',
      'priority_support'
    ]
  }
];

export const getPlanByTier = (tier: PlanTier): Plan => {
  return PLANS.find(p => p.tier === tier) || PLANS[0];
};

export const getOperationsByTier = (tier: PlanTier): number => {
  return getPlanByTier(tier).operations;
};

export const calculateOveragePrice = (operations: number): number => {
  // $0.10 per operation for overage
  return operations * 0.10;
};

// Helper function to check if a feature is allowed for a given plan tier
export const isFeatureAllowed = (planTier: PlanTier, featureId: FeatureId): boolean => {
  const plan = getPlanByTier(planTier);
  return plan.allowedFeatures.includes(featureId);
};

// Get all allowed features for a plan tier
export const getAllowedFeatures = (planTier: PlanTier): FeatureId[] => {
  return getPlanByTier(planTier).allowedFeatures;
};
