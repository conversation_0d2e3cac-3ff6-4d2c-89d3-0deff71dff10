import { z } from 'zod';

// Keep it simple for MVP - focus on most important fields
export const projectSettingsSchema = z.object({
  appName: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  primaryColor: z.string().optional(),
  visibility: z.enum(['public', 'private', 'hidden']),
  bundleIdentifier: z.string().optional(),
  packageName: z.string().optional(),
});

export type ProjectSettingsFormValues = z.infer<typeof projectSettingsSchema>;
