export interface AppTemplate {
    id: string;
    title: string;
    description: string;
    features: string[];
    designInspiration: string;
    prompt: string;
    link: string;
    preview: {
        light: string;
        dark: string;
    };
    accent: {
        from: string;
        via?: string;
        to: string;
    };
}

export const APP_TEMPLATES: AppTemplate[] = [
    {
        id: 'fashion-commerce',
        title: 'Fashion Store (Zara Style)',
        description: 'Minimalist and premium fashion e-commerce experience',
        features: [
            'Editorial-style product showcases',
            'Interactive size guide',
            'Wishlist and collections',
            'Category filters and search',
            'Mobile-first checkout flow'
        ],
        designInspiration: 'Inspired by <PERSON>ara\'s minimalist aesthetic and editorial approach',
        preview: {
            light: '/previews/zara.png',
            dark: '/previews/zara.png'
        },
        accent: {
            from: '#000000',
            to: '#1A1A1A'
        },
        link: "https://magically.life/project/9512a8b5-ebc0-4f48-b805-e1ff3f413f32/preview",
        prompt: 'Create a Zara-inspired fashion e-commerce app with a minimalist, editorial-style design. Include a product showcase with large imagery, size guide with AR try-on capabilities, and a seamless wishlist and checkout flow. Focus on typography and whitespace for a premium feel.'
    },
    // {
    //     id: 'marketplace',
    //     title: 'Local Marketplace (Mercari Style)',
    //     description: 'Community-driven marketplace for buying and selling',
    //     features: [
    //         'Smart item categorization',
    //         'Chat interface',
    //         'Payment flow UI',
    //         'Rating and review system',
    //         'Search with filters'
    //     ],
    //     designInspiration: 'Based on Mercari\'s user-friendly and trust-focused design',
    //     preview: {
    //         light: '/previews/mercari.png',
    //         dark: '/previews/mercari.png'
    //     },
    //     accent: {
    //         from: '#FF2D55',
    //         to: '#FF375F'
    //     },
    //     prompt: 'Build a Mercari-inspired local marketplace app with smart item categorization and a secure payment system. Include an in-app messaging system, rating mechanism, and location-based search. Focus on building trust between buyers and sellers. '
    // },
    // {
    //     id: 'short-video',
    //     title: 'Short Video (TikTok Style)',
    //     description: 'Engaging short-form video platform with powerful creation tools',
    //     features: [
    //         'Vertical video feed',
    //         'Basic video player',
    //         'Like and share features',
    //         'Social engagement UI',
    //         'Content discovery feed'
    //     ],
    //     designInspiration: 'Inspired by TikTok\'s immersive and engaging interface',
    //     preview: {
    //         light: '/previews/tiktok.png',
    //         dark: '/previews/tiktok.png'
    //     },
    //     accent: {
    //         from: '#FF0050',
    //         via: '#00F2EA',
    //         to: '#FF0050'
    //     },
    //     prompt: 'Design a TikTok-inspired short video app with a vertical scrolling feed and robust creation tools. Include effects, music integration, and social engagement features. Focus on smooth animations and an immersive viewing experience.'
    // },
    // {
    //     id: 'saas-dashboard',
    //     title: 'SaaS Analytics (Stripe Style)',
    //     description: 'Beautiful analytics dashboard for SaaS businesses',
    //     features: [
    //         'Key metrics dashboard',
    //         'Interactive charts',
    //         'Data table views',
    //         'Filter and export options',
    //         'Summary insights'
    //     ],
    //     designInspiration: 'Based on Stripe\'s clean and data-focused dashboard design',
    //     preview: {
    //         light: '/previews/stripe.png',
    //         dark: '/previews/stripe.png'
    //     },
    //     accent: {
    //         from: '#635BFF',
    //         to: '#7A73FF'
    //     },
    //     prompt: 'Create a Stripe-inspired SaaS analytics dashboard with real-time metrics tracking and beautiful data visualizations. Include a custom report builder and automated insights generation. Focus on clarity and data accessibility.'
    // },
    {
        id: 'food-delivery',
        title: 'Food Delivery (DoorDash Style)',
        description: 'Fast and delightful food delivery experience',
        features: [
            'Restaurant discovery',
            'Menu browsing interface',
            'Order status tracking UI',
            'Address selection',
            'Payment method selection'
        ],
        designInspiration: 'Inspired by DoorDash\'s efficient and user-friendly design',
        preview: {
            light: '/previews/doordash.png',
            dark: '/previews/doordash.png'
        },
        accent: {
            from: '#FF3008',
            to: '#FF4D4D'
        },
        link: "https://magically.life/project/5285c2e6-fc23-4803-b270-cf3f6d3acbc2/preview",
        prompt: 'Design a DoorDash-inspired food delivery app with real-time order tracking and smart restaurant discovery. Include group ordering capabilities and a loyalty rewards program. Focus on making the ordering process quick and delightful.'
    },
    {
        id: 'spotify-clone',
        title: 'Music Player (Spotify Style)',
        description: 'A sleek music player app with modern UI and smooth animations',
        features: [
            'Bottom tab navigation',
            'Basic audio player UI',
            'Album artwork display',
            'Dark theme support',
            'Recently played list'
        ],
        designInspiration: 'Inspired by Spotify\'s minimalist dark theme and fluid animations',
        preview: {
            light: '/previews/spotify.png',
            dark: '/previews/spotify.png'
        },
        accent: {
            from: '#1DB954',
            via: '#1ED760',
            to: '#1DB954'
        },
        link: 'https://magically.life/project/d00f5cb7-1a50-4ae8-ad8e-20a6672ec5f5/preview',
        prompt: 'Create a Spotify-inspired music player app with a dark theme. Include a bottom tab navigator with Home, Search, and Library screens. The home screen should show recently played tracks with large album artwork. Add a custom audio player component with play/pause, next/previous controls, and a seek bar. Use stunning for the UI components and react-navigation for routing. '
    },
    // {
    //     id: 'notion-tasks',
    //     title: 'Task Manager (Notion Style)',
    //     description: 'Minimal and powerful task management app with clean typography',
    //     features: [
    //         'Task list management',
    //         'Custom checkboxes',
    //         'Progress indicators',
    //         'Light/dark theme',
    //         'Search functionality'
    //     ],
    //     designInspiration: 'Based on Notion\'s clean typography and minimal UI design',
    //     preview: {
    //         light: '/previews/notion.png',
    //         dark: '/previews/notion.png'
    //     },
    //     accent: {
    //         from: '#2E3338',
    //         to: '#37352F'
    //     },
    //     prompt: 'Build a Notion-inspired task manager with a clean, typography-focused design. Include a main task list with custom checkboxes and smooth animations. Add task filtering options and a search bar. Use stunning components and implement light/dark theme support. Focus on typography and whitespace for a premium feel.'
    // },
    {
        id: 'strava-fitness',
        title: 'Fitness Tracker (Strava Style)',
        description: 'Activity tracking app with beautiful data visualization',
        features: [
            'Activity feed cards',
            'Progress visualization',
            'Calendar view',
            'Social interaction UI',
            'Basic map integration'
        ],
        designInspiration: 'Inspired by Strava\'s data-rich yet clean interface',
        preview: {
            light: '/previews/strava.png',
            dark: '/previews/strava.png'
        },
        accent: {
            from: '#FC4C02',
            to: '#FC5200'
        },
        link: "https://magically.life/project/1fc31bf1-1df2-46d2-ba15-ebf1619fc5cf/preview",
        prompt: 'Create a Strava-inspired fitness tracking app with a focus on beautiful data visualization. Include an activity feed with stats cards, progress circles, and an activity heatmap calendar. Add social features like kudos and comments. Use stunning components for the UI and implement light/dark theme support. ',
    }

];
