/**
 * Client-side utility for connecting to the design preview SSE stream
 */

type DesignStreamCallback = (data: { 
  html?: string; 
  status: string; 
  content?: string;
  lastUpdated?: Date;
}) => void;

export class DesignStreamClient {
  private eventSource: EventSource | null = null;
  private sessionId: string;
  private projectId: string;
  private chatId: string;
  private onUpdate: DesignStreamCallback;
  private onError: (error: Error) => void;
  private onComplete: () => void;
  
  constructor(
    sessionId: string,
    projectId: string,
    chatId: string,
    onUpdate: DesignStreamCallback,
    onError: (error: Error) => void,
    onComplete: () => void
  ) {
    this.sessionId = sessionId;
    this.chatId = chatId;
    this.projectId = projectId;
    this.onUpdate = onUpdate;
    this.onError = onError;
    this.onComplete = onComplete;
  }
  
  /**
   * Connect to the SSE stream
   */
  connect(): void {
    if (this.eventSource) {
      this.disconnect();
    }
    
    this.eventSource = new EventSource(`/api/project/${this.projectId}/chat/${this.chatId}/design-sessions/${this.sessionId}/stream`);
    
    // Handle incoming messages
    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.onUpdate(data);
        
        // If generation is complete, close the connection
        if (data.status === 'complete' || data.status === 'error') {
          this.disconnect();
          this.onComplete();
        }
      } catch (error) {
        console.error('Error parsing SSE data:', error);
      }
    };
    
    // Handle errors
    this.eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      this.onError(new Error('Connection to design stream failed'));
      this.disconnect();
    };
  }
  
  /**
   * Disconnect from the SSE stream
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}
