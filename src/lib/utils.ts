import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import {CoreAssistantMessage, CoreToolMessage, Message, ToolInvocation} from "ai";
import type { Message as DBMessage, Document } from '@/lib/db/schema';
import {Attachment} from "@ai-sdk/ui-utils";
import ApiClient from "@/services/ApiClient";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

interface ApplicationError extends Error {
  info: string;
  status: number;
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}


export const fetcher = async (url: string) => {
  const res = await ApiClient.get(url);

  if (!res.ok) {
    const error = new Error(
        'An error occurred while fetching the data.',
    ) as ApplicationError;

    error.info = await res.json();
    error.status = res.status;

    throw error;
  }

  return res.json();
};

export function sanitizeUIMessages(messages: Array<Message>): Array<Message> {
  const messagesBySanitizedToolInvocations = messages.map((message) => {
    if (message.role !== 'assistant') return message;

    if (!message.toolInvocations) return message;

    const toolResultIds: Array<string> = [];

    for (const toolInvocation of message.toolInvocations) {
      if (toolInvocation.state === 'result') {
        toolResultIds.push(toolInvocation.toolCallId);
      }
    }

    const sanitizedToolInvocations = message.toolInvocations.filter(
        (toolInvocation) =>
            toolInvocation.state === 'result' ||
            toolResultIds.includes(toolInvocation.toolCallId),
    );

    return {
      ...message,
      toolInvocations: sanitizedToolInvocations,
    };
  });

  return messagesBySanitizedToolInvocations.filter(
      (message) =>
          message.content.length > 0 ||
          (message.toolInvocations && message.toolInvocations.length > 0),
  );
}

export function getMessageIdFromAnnotations(message: Message) {
  if (!message.annotations) return message.id;

  const [annotation] = message.annotations;
  if (!annotation) return message.id;

  // @ts-expect-error messageIdFromServer is not defined in MessageAnnotation
  return annotation.messageIdFromServer;
}

export function getDocumentTimestampByIndex(
    documents: Array<Document>,
    index: number,
) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}

export function sanitizeResponseMessages(
    messages: Array<CoreToolMessage | CoreAssistantMessage>,
): Array<CoreToolMessage | CoreAssistantMessage> {
  const toolResultIds: Array<string> = [];

  for (const message of messages) {
    if (message.role === 'tool') {
      for (const content of message.content) {
        if (content.type === 'tool-result') {
          toolResultIds.push(content.toolCallId);
        }
      }
    }
  }

  const messagesBySanitizedContent = messages.map((message) => {
    if (message.role !== 'assistant') return message;

    if (typeof message.content === 'string') return message;

    const sanitizedContent = message.content.filter((content) =>
        content.type === 'tool-call'
            ? toolResultIds.includes(content.toolCallId)
            : content.type === 'text'
                ? content.text.length > 0
                : true,
    );

    return {
      ...message,
      content: sanitizedContent,
    };
  });

  return messagesBySanitizedContent.filter(
      (message) => message.content.length > 0,
  );
}

export function convertToUIMessages(
    messages: Array<DBMessage>,
): Array<Message & {finishReason: string}> {
  const convertedMessages =  messages.reduce((chatMessages: Array<Message & {finishReason: string}>, message) => {
    if (message.role === 'tool') {
      return addToolMessageToChat({
        toolMessage: message as CoreToolMessage,
        messages: chatMessages,
      });
    }

    let textContent = '';
    const toolInvocations: Array<ToolInvocation> = [];
    const attachments: Attachment[] = [];

    if (typeof message.content === 'string') {
      textContent = message.content;
    } else if (Array.isArray(message.content)) {
      for (const content of message.content) {
        if (content.type === 'text') {
          textContent += content.text;
        } else if (content.type === 'tool-call') {
          toolInvocations.push({
            state: 'call',
            toolCallId: content.toolCallId,
            toolName: content.toolName,
            args: content.args,
          });
        } else if(content.type === "image") {
          attachments.push({
            url: content.url || content.image,
            contentType: content.contentType || content.mimeType,
            name: content.name
          })
        }
      }
    }

    chatMessages.push({
      id: message.id,
      role: message.role as Message['role'],
      content: textContent,
      toolInvocations,
      experimental_attachments: attachments,
      createdAt: message.createdAt,
      finishReason: message.finishReason,
      parentUserMessageId: message.parentUserMessageId,
      isAssistantGroupHead: message.isAssistantGroupHead,
      parentAssistantMessageId: message.parentAssistantMessageId
    } as any);

    return chatMessages as any[];
  }, []);


  // const grouped = convertedMessages.reduce(({messages, set}: {messages: Array<Message & {finishReason: string, parentUserMessageId: string}>, set: Set<string, messages>}, message) => {
  //   if(message.parentUserMessageId) {
  //     // Let's find the set of the messages
  //   }
  // }, {set: new Set(), messages: []})
  // return grouped;

  return convertedMessages
}

function addToolMessageToChat({
                                toolMessage,
                                messages,
                              }: {
  toolMessage: CoreToolMessage;
  messages: Array<Message>;
}): Array<Message> {
  return messages.map((message) => {
    if (message.toolInvocations) {
      return {
        ...message,
        toolInvocations: message.toolInvocations.map((toolInvocation) => {
          const toolResult = toolMessage.content.find(
              (tool) => tool.toolCallId === toolInvocation.toolCallId,
          );

          if (toolResult) {
            return {
              ...toolInvocation,
              state: 'result',
              result: toolResult.result,
            };
          }

          return toolInvocation;
        }),
      };
    }

    return message;
  });
}

/**
 * Converts a string to a URL-friendly slug
 * @param text - The text to convert to a slug
 * @returns A URL-friendly slug
 */
export function slugify(text: string): string {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')        // Replace spaces with -
    .replace(/&/g, '-and-')      // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')     // Remove all non-word characters
    .replace(/\-\-+/g, '-')       // Replace multiple - with single -
    .replace(/^-+/, '')          // Trim - from start of text
    .replace(/-+$/, '');         // Trim - from end of text
}


/**
 *
 * @param delayTimeInMs
 */
export function delay(delayTimeInMs: number) {
  return new Promise(resolve => {
    setTimeout(resolve, delayTimeInMs);
  })
}