import { db } from '@/lib/db';
import { tokenConsumption, subscriptions } from '@/lib/db/schema';
import { and, eq, gte, sql } from 'drizzle-orm';
import { PLANS, PlanTier, getPlanByTier } from './subscription/plans';
import { desc } from 'drizzle-orm';

interface SubscriptionStatus {
  subscriptionId?: string;
  isActive: boolean;
  plan: PlanTier;
  status: 'active'| 'inactive'| 'cancelled' | 'expired' | 'past_due';
  expiresAt: Date | null;
  credits: number;        // Now represents total messages allowed
  creditsUsed: number;    // Now represents messages used
  creditsRemaining: number; // Now represents messages remaining
  expiryDate?: string;
}

export async function checkSubscriptionStatus(userId: string, isAnonymous?: boolean): Promise<SubscriptionStatus> {
  // Get appropriate plan based on user type
  const freePlan = PLANS.find(plan => plan.tier === "free");
  const anonymousPlan = PLANS.find(plan => plan.tier === "anonymous");
  
  // Skip subscription check for anonymous users
  if (isAnonymous) {
    // For anonymous users, count messages from token consumption
    const result = await db
      .select({
        messageCount: sql<number>`COUNT(*)`
      })
      .from(tokenConsumption)
      .where(
        eq(tokenConsumption.userId, userId)
      );

    // Ensure we have a number by explicitly converting
    const messagesUsed = Number(result[0]?.messageCount || 0);
    const anonMessageLimit = anonymousPlan?.operations || 100;
    
    return {
      isActive: false,
      status: 'active',
      plan: 'anonymous',
      expiresAt: null,
      credits: anonMessageLimit,  // Now represents message limit
      creditsUsed: messagesUsed,  // Now represents messages used
      creditsRemaining: Math.max(0, anonMessageLimit - messagesUsed)
    };
  }
  
  // For authenticated users, check if they have an active subscription
  const userSubscription = await db
    .select()
    .from(subscriptions)
    .where(and(
        eq(subscriptions.userId, userId),
        eq(subscriptions.isActive, true)
    ))
    .orderBy(desc(subscriptions.updatedAt))
    .limit(1)
    .then(results => results[0]);

  if (!userSubscription || !userSubscription.isActive) {
    // For free tier users, count messages from token consumption
    const result = await db
      .select({
        messageCount: sql<number>`COUNT(*)`
      })
      .from(tokenConsumption)
      .where(
        eq(tokenConsumption.userId, userId)
      );

    // Ensure we have a number by explicitly converting
    const messagesUsed = Number(result[0]?.messageCount || 0);
    const freeMessageLimit = freePlan?.operations || 50;

    return {
      isActive: false,
      plan: 'free',
      status: 'active',
      expiresAt: null,
      credits: freeMessageLimit, // Free tier message limit
      creditsUsed: messagesUsed, // Messages used
      creditsRemaining: Math.max(0, freeMessageLimit - messagesUsed)
    };
  }

  // User has an active subscription - use subscription.creditsUsed as source of truth for message count
  return {
    expiryDate: userSubscription.resetDate ? userSubscription.resetDate.toISOString() : undefined,
    isActive: !!userSubscription.isActive,
    subscriptionId: userSubscription.id,
    status: userSubscription.status as any,
    plan: userSubscription.planId as PlanTier,
    expiresAt: userSubscription.resetDate, // Use resetDate as the expiration date
    credits: userSubscription.credits,      // Now represents message limit
    creditsUsed: userSubscription.creditsUsed || 0, // Now represents messages used
    creditsRemaining: Math.max(0, userSubscription.credits - (userSubscription.creditsUsed || 0))
  };
}

export async function checkMessageLimit(userId: string, isAnonymous: boolean) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Get subscription status first (which already has creditsUsed for paid plans)
  const subscription = await checkSubscriptionStatus(userId, isAnonymous);
  
  // For consistency, use the creditsUsed from subscription status (now represents messages)
  const messagesUsed = subscription.creditsUsed;

  // Get plan details
  const plan = getPlanByTier(subscription.plan);
  
  // Get daily limit from plan
  const dailyLimit = plan.dailyLimit;
  
  // Check if plan has a daily limit (not -1)
  if (dailyLimit > 0) {
    // Count messages used today
    const todayResult = await db
      .select({
        todayMessageCount: sql<number>`COUNT(*)`
      })
      .from(tokenConsumption)
      .where(
        and(
          eq(tokenConsumption.userId, userId),
          gte(tokenConsumption.createdAt, today)
        )
      );
    
    // Ensure we have a number by explicitly converting
    const todayMessagesUsed = Number(todayResult[0]?.todayMessageCount || 0);
    const dailyRemaining = Math.max(0, dailyLimit - todayMessagesUsed);
    
    // Check both daily and monthly message limits
    return {
      canSendMessage: todayMessagesUsed < dailyLimit && messagesUsed < subscription.credits,
      remaining: Math.min(
        dailyRemaining,
        Math.max(0, subscription.credits - messagesUsed)
      ),
      limit: subscription.credits,
      dailyLimit: dailyLimit,
      dailyRemaining: dailyRemaining,
      isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',
      isAnonymous: subscription.plan === 'anonymous',
      planTier: subscription.plan,
      totalCredits: subscription.credits,      // Now represents total message limit
      creditsUsed: messagesUsed,               // Now represents messages used
      creditsRemaining: Math.max(0, subscription.credits - messagesUsed) // Messages remaining
    };
  }

  // Plans without daily limit (dailyLimit = -1)
  return {
    canSendMessage: subscription.creditsRemaining > 0,
    remaining: subscription.creditsRemaining,
    limit: subscription.credits,
    isPro: subscription.plan !== 'free' && subscription.plan !== 'anonymous',
    isAnonymous: subscription.plan === 'anonymous',
    planTier: subscription.plan,
    totalCredits: subscription.credits,      // Now represents total message limit
    creditsUsed: subscription.creditsUsed,   // Now represents messages used
    creditsRemaining: subscription.creditsRemaining, // Messages remaining
    // Include dailyLimit as -1 to indicate unlimited
    dailyLimit: -1,
    dailyRemaining: -1
  };
}
