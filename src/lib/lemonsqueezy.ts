import {createCheckout as LmCheckout, updateSubscription, retrieveSubscription,} from 'lemonsqueezy.ts';
import {CheckoutOptions, UpdateSubscriptionOptions} from '@/types/lemonsqueezy';

// Define CancelSubscriptionOptions interface
export interface CancelSubscriptionOptions {
    subscriptionId: string;
    variantId: number;
    productId: number
}

if (!process.env.LEMON_SQUEEZY_API_KEY) {
    throw new Error('LEMON_SQUEEZY_API_KEY is not set');
}

export const STORE_ID = process.env.LEMON_SQUEEZY_STORE_ID;
export const VARIANT_IDS = {
    starter: process.env.LEMON_SQUEEZY_STARTER_VARIANT_ID,
    pro: process.env.LEMON_SQUEEZY_PRO_VARIANT_ID,
    plus: process.env.LEMON_SQUEEZY_PLUS_VARIANT_ID,
    prime: process.env.LEMON_SQUEEZY_PRIME_VARIANT_ID
} as const;

export const PRODUCT_IDS = {
    starter: process.env.LEMON_SQUEEZY_STARTER_PRODUCT_ID,
    pro: process.env.LEMON_SQUEEZY_PRO_PRODUCT_ID,
    plus: process.env.LEMON_SQUEEZY_PLUS_PRODUCT_ID,
    prime: process.env.LEMON_SQUEEZY_PRIME_PRODUCT_ID
} as const;

export type PlanVariant = keyof typeof VARIANT_IDS;

export async function createCheckout(options: CheckoutOptions) {
    // console.log('options', options)
    try {
        const checkout = await LmCheckout({
            // ...(options as any),
            checkout_data: {
                name: options.checkout_data?.name,
                email: options.checkout_data?.email,
                custom: options?.checkout_data?.custom_data
            },
            product_options: {
                redirect_url: options.product_options?.redirect_url,
                name: options.product_options?.name || "magically Builder Plan",
                description: options.product_options?.description || "Subscribe to your magically Builder plan",
            },
            store: options.store_id + "",
            variant: options.variant_id + "",
            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,
            checkout_options: {
                dark: true,
                embed: false,
                subscription_preview: true,
                media: false,
                desc: true,
            }
        });
        console.log('checkout', checkout.data.attributes)
        return checkout;
    } catch (error) {
        console.error('Error creating checkout:', error);
        throw error;
    }
}

export async function verifyWebhookSignature(
    payload: string,
    signature: string
) {
    // Implement webhook signature verification
    const secret = process.env.LEMON_SQUEEZY_WEBHOOK_SECRET;
    if (!secret) throw new Error('LEMON_SQUEEZY_WEBHOOK_SECRET is not set');

    const crypto = require('crypto');
    const hmac = crypto.createHmac('sha256', secret);
    const digest = hmac.update(payload).digest('hex');

    return signature === digest;
}

/**
 * Update an existing subscription to a new plan
 * @param options Options for updating the subscription
 * @returns The updated subscription data
 */
export async function upgradeSubscription(options: UpdateSubscriptionOptions) {
    try {
        const {subscriptionId, variantId, invoiceImmediately = true, disableProrations = false, productId} = options;

        if (!process.env.LEMON_SQUEEZY_API_KEY) {
            throw new Error('LEMON_SQUEEZY_API_KEY is not set');
        }

        return await updateSubscription({
            id: subscriptionId,
            variantId: variantId + "",
            productId: productId + "",
            disableProrations: disableProrations,
            invoiceImmediately: invoiceImmediately,
            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,
        });
    } catch (error) {
        console.error('Error updating subscription:', JSON.stringify(error, null, 2));
        throw error;
    }
}

// getSubscription function is already defined below

/**
 * Cancel an existing subscription
 * @param options Options for cancelling the subscription
 * @returns The cancelled subscription data
 */
export async function cancelSubscription(options: CancelSubscriptionOptions) {
    try {
        const {subscriptionId, variantId, productId} = options;

        if (!process.env.LEMON_SQUEEZY_API_KEY) {
            throw new Error('LEMON_SQUEEZY_API_KEY is not set');
        }

        return updateSubscription({
            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,
            cancelled: true,
            id: subscriptionId,
            variantId: variantId + "",
            productId: productId + "",
        })
    } catch (error) {
        console.error('Error cancelling subscription:', error);
        throw error;
    }
}

/**
 * Get a subscription by ID
 * @param subscriptionId The ID of the subscription to retrieve
 * @returns The subscription data
 */
export async function getSubscription(subscriptionId: string) {
    try {
        if (!process.env.LEMON_SQUEEZY_API_KEY) {
            throw new Error('LEMON_SQUEEZY_API_KEY is not set');
        }

        return retrieveSubscription({
            apiKey: process.env.LEMON_SQUEEZY_API_KEY as string,
            id: subscriptionId,
        })
    } catch (error) {
        console.error('Error getting subscription:', error);
        throw error;
    }
}
