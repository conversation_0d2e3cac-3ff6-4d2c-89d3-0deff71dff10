import {type CreditOperation, CreditOperationType} from '../subscription/credit-usage';

export interface DiscountedOperation {
    type: CreditOperationType;
    count: number;
    reason: string;
}

export class CreditUsageTracker {
    private operations: CreditOperation[] = [];
    private discountedOperations: DiscountedOperation[] = [];

    /**
     * Track a credit operation that will be charged to the user
     * @param type The type of operation
     * @param count The number of operations (default: 1)
     */
    trackOperation(type: CreditOperationType, count: number = 1) {
        const existingOp = this.operations.find(op => op.type === type);
        if (existingOp) {
            existingOp.count += count;
        } else {
            this.operations.push({ type, count });
        }
    }

    /**
     * Track a discounted operation that won't be charged to the user
     * @param type The type of operation
     * @param reason The reason for the discount (e.g., 'error_fixing')
     * @param count The number of operations (default: 1)
     */
    trackDiscountedOperation(type: CreditOperationType, reason: string, count: number = 1) {
        const existingOp = this.discountedOperations.find(op => op.type === type && op.reason === reason);
        if (existingOp) {
            existingOp.count += count;
        } else {
            this.discountedOperations.push({ type, count, reason });
        }
    }

    /**
     * Get all tracked operations that will be charged
     */
    getOperations(): CreditOperation[] {
        return [...this.operations];
    }

    /**
     * Get all discounted operations that won't be charged
     */
    getDiscountedOperations(): DiscountedOperation[] {
        return [...this.discountedOperations];
    }

    /**
     * Get the total number of credits consumed (charged operations only)
     */
    getCreditConsumption(): number {
        return this.operations.reduce((total, op) => total + op.count, 0);
    }

    /**
     * Get the total number of credits saved through discounts
     */
    getDiscountedCreditCount(): number {
        return this.discountedOperations.reduce((total, op) => total + op.count, 0);
    }

    /**
     * Get the total potential credit consumption (charged + discounted)
     */
    getTotalPotentialConsumption(): number {
        return this.getCreditConsumption() + this.getDiscountedCreditCount();
    }

    /**
     * Clear all tracked operations
     */
    clear() {
        this.operations = [];
        this.discountedOperations = [];
    }
}

