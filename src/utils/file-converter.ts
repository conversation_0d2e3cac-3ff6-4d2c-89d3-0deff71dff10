import { FileItem, FileNode } from '@/types/file';
import { FileItemV2, FileNodeV2 } from '@/types/editor-v2';
import { DEFAULT_CODE } from '@/types/editor';

export const convertV2ToSnackFiles = (fileTree: FileItemV2[]) => {
    const files: Record<string, { type: string; contents: string }> = {};
    
    const processFileTree = (items: FileItemV2[]) => {
        items.forEach((item) => {
            // Use the full path for the file name
            files[item.name] = {
                type: 'CODE',
                contents: item.content,
            };
        });
    };
    
    processFileTree(fileTree);
    return encodeURIComponent(JSON.stringify(files));
};

// Convert v1 FileItem[] to v2 FileItemV2[]
export const migrateToV2 = (oldTree: FileItem[]): FileItemV2[] => {
    const convertNode = (node: FileItem, parentPath: string = ''): FileItemV2 => {
        const currentPath = parentPath ? `${parentPath}/${node.name}` : node.name;

        return {
            ...node,
            absolutePath: currentPath,
        } as FileNodeV2;
    };

    return oldTree.map(node => convertNode(node));
};
