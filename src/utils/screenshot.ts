import html2canvas from 'html2canvas';

export const takeScreenshot = async (elementId: string): Promise<string> => {
  try {
    // Get the phone frame element
    const phoneFrame = document.getElementById(elementId);
    if (!phoneFrame) {
      throw new Error('Phone frame not found');
    }

    // Get the iframe
    const iframe = phoneFrame.querySelector('iframe') as HTMLIFrameElement;
    if (!iframe) {
      throw new Error('Iframe not found');
    }

    // Create a canvas with the same dimensions as the phone frame
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    // Set canvas size to match the phone frame
    const phoneFrameRect = phoneFrame.getBoundingClientRect();
    canvas.width = phoneFrameRect.width;
    canvas.height = phoneFrameRect.height;

    // Draw white background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Create a temporary image to capture the phone frame
    const img = new Image();
    img.crossOrigin = 'anonymous';

    // Wait for everything to load
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Use html2canvas for the phone frame
    const frameCanvas = await html2canvas(phoneFrame, {
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      scale: 2,
      logging: true,
      onclone: (clonedDoc) => {
        const clonedFrame = clonedDoc.getElementById(elementId);
        if (clonedFrame) {
          // Ensure the frame is visible
          clonedFrame.style.visibility = 'visible';
          clonedFrame.style.opacity = '1';
        }
      }
    });

    // Draw the frame canvas onto our main canvas
    ctx.drawImage(frameCanvas, 0, 0, canvas.width, canvas.height);

    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('Error taking screenshot:', error);
    throw error;
  }
};
