import {DEFAULT_CODE} from "@/types/editor";
import {FileItem} from "@/types/file";

export const convertToFileTree = (files: typeof DEFAULT_CODE) => {
    // const tree: FileItem [] = [];
    // const dirMap = new Map<string, any>();
    //
    // files.forEach((file) => {
    //     const parts = file.name.split('/');
    //     let currentPath = '';
    //
    //     parts.forEach((part, index) => {
    //         const isLast = index === parts.length - 1;
    //         currentPath = currentPath ? `${currentPath}/${part}` : part;
    //
    //         if (isLast) {
    //             const fileNode = {
    //                 name: part,
    //                 type: 'file' as const,
    //                 language: file.language,
    //                 content: file.content,
    //                 changes: Math.floor(Math.random() * 100), // For demo purposes
    //             };
    //
    //             if (parts.length === 1) {
    //                 tree.push(fileNode);
    //             } else {
    //                 const parentPath = parts.slice(0, -1).join('/');
    //                 const parent = dirMap.get(parentPath);
    //                 if (parent) {
    //                     parent.children.push(fileNode);
    //                 }
    //             }
    //         } else if (!dirMap.has(currentPath)) {
    //             const dirNode = {
    //                 name: part,
    //                 type: 'directory' as const,
    //                 children: [],
    //             };
    //             dirMap.set(currentPath, dirNode);
    //
    //             if (index === 0) {
    //                 tree.push(dirNode);
    //             } else {
    //                 const parentPath = parts.slice(0, index).join('/');
    //                 const parent = dirMap.get(parentPath);
    //                 if (parent) {
    //                     parent.children.push(dirNode);
    //                 }
    //             }
    //         }
    //     });
    // });

    return files;
};
