'use client';

import React from 'react';
import { ActionsStatus, ActionMeta } from '@/lib/parser/ActionsParser';
import { Button } from '@/components/ui/button';
import { ExternalLink, Code, Lightbulb, Wrench, Shield } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {ChatRequestOptions, type CreateMessage, Message} from "ai";
import {toast} from "sonner";
import Image from "next/image";

interface ActionsDisplayProps {
  actionsStatus: ActionsStatus;
  className?: string;
  append: (
      message: Message | CreateMessage,
      chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  onActionClick: (action: ActionMeta) => void;
}

export const ActionsDisplay = ({ actionsStatus, className, append, status, onActionClick }: ActionsDisplayProps) => {
  const { isActive, actions } = actionsStatus;

  if (!isActive || actions.length === 0) {
    return null;
  }

  // Limit to maximum 3 actions
  const limitedActions = actions.slice(0, 3);

  const onActionClickLocal = (action: ActionMeta)=> {
    if(action.type === "tool" && action.tool === "secrets_form") {
      action.allSecretNames = actions.filter(action => action.type === "tool" && action.tool === "secrets_form" && action.secretName).map(action => action.secretName as string);
    }
    onActionClick(action);
  }

  return (
    <div className={cn("mt-6 space-y-3 p-4 border border-zinc-800/40 rounded-lg bg-zinc-900/30", className)}>
      <h4 className="text-sm font-medium text-zinc-400">Suggested Actions</h4>
      <div className="flex flex-wrap gap-2">
        {limitedActions.map((action, index) => (
          <ActionButton key={index} onActionClick={onActionClickLocal} action={action} append={append} status={status}/>
        ))}
      </div>
    </div>
  );
};

interface ActionButtonProps {
  action: ActionMeta;
  append: (
      message: Message | CreateMessage,
      chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  onActionClick: (action: ActionMeta) => void;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
}

const ActionButton = ({ action, append, status, onActionClick }: ActionButtonProps) => {
  const { type, link, tool, content } = action;


  const clickHandler = () => {
    onActionClick(action);
  }
  // Get icon based on action type
  const getIcon = () => {
    switch (type) {
      case 'anchor':
        return <ExternalLink className="h-3 w-3 mr-1" />;
      case 'code':
        return <Code className="h-3 w-3 mr-1" />;
      case 'tool':
        switch (tool) {
          case 'supabase_integration':
            return  <Image
                src={'/icons/integrations/supabase.png'}
                alt={'supabase'}
                width={14}
                height={14}
                className="dark:filter"
            />;
          case 'secrets_form':
            return  <Shield className="h-3 w-3 mr-1" />
          default:
            return <Wrench className="h-3 w-3 mr-1" />;
        }
        break;
      case 'feature':
      default:
        return <Lightbulb className="h-3 w-3 mr-1" />;
    }
  };
  
  // Get variant based on action type
  const getVariant = (): "default" | "secondary" | "outline" | "ghost" | "link" | "destructive" | "accent" => {
    switch (type) {
      case 'anchor':
        return 'secondary';
      case 'code':
        return 'outline';
      case 'tool':
        switch (tool) {
          case 'supabase_integration':
            return 'secondary';
        }
        return 'outline';
      case 'feature':
      default:
        return 'secondary';
    }
  };
  
  // If it's an anchor with a link, render as a Link
  if (type === 'anchor' && link) {
    return (
      <Button asChild variant={getVariant()} size="xs" className="text-xs font-medium shadow-sm hover:shadow-md transition-all">
        <Link href={link} target="_blank" rel="noopener noreferrer">
          {getIcon()}
          {content}
        </Link>
      </Button>
    );
  }
  
  // Otherwise render as a regular button
  return (
    <Button
      variant={getVariant()}
      size="xs"
      className="text-xs font-medium shadow-sm hover:shadow-md transition-all"
      onClick={clickHandler}
    >
      {getIcon()}
      {content}
    </Button>
  );
};

export default ActionsDisplay;
