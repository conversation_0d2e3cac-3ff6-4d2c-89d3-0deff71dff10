'use client';

import type {User} from 'next-auth';
import {usePathname, useRouter} from 'next/navigation';

import {PlusIcon} from '@/components/icons';
import {Button} from '@/components/ui/button';
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarMenu, SidebarMenuButton,
    SidebarMenuItem,
    SidebarSeparator,
    useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import {Tooltip, TooltipContent, TooltipTrigger} from './ui/tooltip';
import {MessageSquare, TargetIcon} from 'lucide-react';
import {SidebarHistory} from "@/components/base/sidebar-history";
import {SidebarUserNav} from "@/components/base/sidebar-user-nav";
import {useAuth} from "@/hooks/use-auth";
import MagicallyLogo from "@/components/logo";
import { SubscriptionIndicator } from "@/components/subscription/subscription-indicator";

export function AppSidebar() {
    const router = useRouter();
    const {setOpenMobile, setOpen} = useSidebar();
    const {session} = useAuth();
    const pathname = usePathname();

    if (!session?.user) {
        return;
    }

    const handleClick = () => {
        setOpen(false);
        setOpenMobile(false);
    }
    return (
        <Sidebar className="group-data-[side=left]:border-r-0">
            <SidebarHeader>
                <SidebarMenu>
                    <div className="flex flex-row justify-between items-center">
                        <Link
                            href="/"
                            onClick={() => {
                                setOpenMobile(false);
                            }}
                            className="flex flex-row gap-3 items-center hover:bg-muted"
                        >
                            <MagicallyLogo logoWidthAction={32}/>
                        </Link>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    variant="ghost"
                                    type="button"
                                    className="p-2 h-fit"
                                    onClick={() => {
                                        setOpenMobile(false);
                                        router.push('/');
                                        router.refresh();
                                    }}
                                >
                                    <PlusIcon/>
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>New Chat</TooltipContent>
                        </Tooltip>
                    </div>
                </SidebarMenu>
            </SidebarHeader>
            <SidebarContent>
                <SidebarMenuItem className="px-4">
                    <SidebarMenuButton
                        asChild
                        isActive={pathname ? pathname === `/projects` : false}
                    >
                        <Link href={`/projects`} onClick={handleClick}>
                            <MessageSquare className="h-4 w-4"/>
                            <span>All Projects</span>
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarHistory user={session.user}/>
                
                <SidebarSeparator />
            </SidebarContent>
            <SidebarFooter>
                <SubscriptionIndicator variant="sidebar" />
                {session.user && <SidebarUserNav user={session.user}/>}
            </SidebarFooter>
        </Sidebar>
    );
}
