'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Terminal as XTerm } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { observer } from 'mobx-react-lite';
import { io, Socket } from 'socket.io-client';
import { terminalStore } from '@/stores/TerminalStore';
import { terminalService } from '@/services/TerminalService';
import { Button } from '@/components/ui/button';
import { X, Maximize2, Minimize2, TerminalSquare } from 'lucide-react';
import '@xterm/xterm/css/xterm.css';

// Define a type for the window object with our custom property
declare global {
  interface Window {
    _directTerminalSocket?: Socket;
  }
}

interface TerminalProps {
  sessionId?: string;
  onClose?: () => void;
  initialCommand?: string;
}

export const Terminal = observer(({ sessionId, onClose, initialCommand }: TerminalProps) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<XTerm | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const lastRenderedOutputRef = useRef<number>(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [inputBuffer, setInputBuffer] = useState('');
  
  // File upload progress state
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  // Connection state tracking
  const [connectionState, setConnectionState] = useState<'connected' | 'disconnected' | 'connecting' | 'reconnecting' | 'error'>('disconnected');
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  // Track visibility state
  const [isVisible, setIsVisible] = useState(true);
  const socketRef = useRef<Socket | null>(null);
  const sessionIdRef = useRef<string | undefined>(sessionId);

  // Manual disconnect function
  const handleDisconnect = () => {
    console.log('Manually disconnecting terminal session');
    setConnectionState('disconnected');
    setConnectionError(null);
    
    if (socketRef.current) {
      // Remove all listeners before disconnecting to prevent memory leaks
      socketRef.current.removeAllListeners();
      socketRef.current.disconnect();
      
      // Update UI to show disconnected state
      if (xtermRef.current) {
        xtermRef.current.write('\r\n\x1b[1;31mTerminal disconnected. Click Reconnect to resume.\x1b[0m\r\n');
      }
      
      console.log('Socket disconnected successfully');
    } else {
      console.warn('No socket connection to disconnect');
    }
  };
  
  // Define the setupDirectSocketConnection function
  const setupDirectSocketConnection = () => {
    if (!terminalStore.state.sessionId) {
      console.error('No session ID available for terminal');
      setConnectionState('error');
      setConnectionError('No session ID available');
      return;
    }
    
    console.log(`Setting up direct socket connection for session ${terminalStore.state.sessionId}`);
    setConnectionState('connecting');
    
    // Create a direct socket connection to the server
    // This bypasses the TerminalService layer completely
    const directSocket = io({
      path: '/api/terminal-ws',
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 10,  // Increased attempts
      reconnectionDelay: 1000,
      timeout: 20000,  // Increased timeout
    });
    
    // Store in ref for visibility change handler
    socketRef.current = directSocket;
    sessionIdRef.current = terminalStore.state.sessionId;
    
    // Debug all socket events
    directSocket.onAny((event: string, ...args: any[]) => {
      console.log(`Direct socket event received: ${event}`, args);
    });
    
    // Handle sandbox timeout errors
    directSocket.on('SANDBOX_TIMEOUT', (data: { error: string, sessionId: string }) => {
      console.error('Sandbox timeout detected:', data);
      
      // Update the terminal UI to show the timeout error
      if (xtermRef.current) {
        xtermRef.current.write('\r\n\x1b[1;31mSandbox timeout detected. The terminal session needs to be restarted.\x1b[0m\r\n');
        xtermRef.current.write('\r\n\x1b[1;33mClick Reconnect to start a new session.\x1b[0m\r\n');
      }
      
      // Update connection state
      setConnectionState('error');
      setConnectionError('Sandbox timeout detected. The terminal session needs to be restarted.');
      
      // Force disconnection of the socket
      directSocket.disconnect();
    });
    
    // Listen directly to the TERMINAL_OUTPUT event
    directSocket.on('TERMINAL_OUTPUT', (data: { sessionId: string, data: string }) => {
      console.log('Direct socket TERMINAL_OUTPUT received:', data);
      
      // Write directly to the terminal
      if (xtermRef.current && data.data) {
        xtermRef.current.write(data.data);
      }
    });
    
    // Handle connection events
    directSocket.on('connect', () => {
      console.log('Direct socket connected');
      setConnectionState('connected');
      setConnectionError(null);
      
      // Join the session room
      directSocket.emit('JOIN_SESSION', { sessionId: terminalStore.state.sessionId });
      
      // Send a test message to the terminal
      if (xtermRef.current) {
        xtermRef.current.write('\r\n\x1b[1;32mTerminal connected.\x1b[0m\r\n');
        
        // If we have an initial command and the component is mounted, send it
        if (initialCommand && isReady) {
          console.log('Sending initial command:', initialCommand);
          directSocket.emit('TERMINAL_INPUT', { 
            sessionId: terminalStore.state.sessionId, 
            data: initialCommand + '\r' 
          });
        }
      }
    });
    
    directSocket.on('connect_error', (error) => {
      console.error('Direct socket connection error:', error);
      setConnectionState('error');
      setConnectionError(error.message);
      
      // Update the terminal UI to show the error
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n\x1b[1;31mConnection error: ${error.message}\x1b[0m\r\n`);
      }
    });
    
    directSocket.on('disconnect', (reason) => {
      console.log('Direct socket disconnected:', reason);
      
      // Only attempt auto-reconnect if we're not manually disconnected
      if (connectionState !== 'disconnected') {
        setConnectionState('reconnecting');
        
        // If document is visible, attempt to reconnect immediately
        if (document.visibilityState === 'visible') {
          console.log('Document is visible, attempting immediate reconnection...');
          setTimeout(() => {
            if (!directSocket.connected && 
                (connectionState === 'connected' || 
                 connectionState === 'connecting' || 
                 connectionState === 'reconnecting' || 
                 connectionState === 'error')) {
              console.log('Manually reconnecting socket...');
              handleReconnect();
            }
          }, 1000);
        }
        
        // Notify user of disconnection
        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[1;33mTerminal disconnected. Attempting to reconnect...\x1b[0m\r\n');
        }
      }
    });
    
    directSocket.on('error', (error) => {
      console.error('Direct socket error:', error);
      setConnectionState('error');
      setConnectionError(error.message);
      
      // Update the terminal UI to show the error
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n\x1b[1;31mSocket error: ${error.message}\x1b[0m\r\n`);
      }
      
      // If document is visible, attempt to reconnect
      if (document.visibilityState === 'visible' && !directSocket.connected && 
          connectionState !== 'disconnected') {
        console.log('Reconnecting after socket error...');
        setTimeout(() => handleReconnect(), 2000);
      }
    });
    
    // Listen for file upload progress events
    directSocket.on('FILE_UPLOAD_PROGRESS', (data: { sessionId: string, progress: number, status: string }) => {
      if (data.sessionId === terminalStore.state.sessionId) {
        console.log(`File upload progress: ${data.progress}%, ${data.status}`);
        setUploadProgress(data.progress);
        setUploadStatus(data.status);
        setIsUploading(data.progress < 100);
      }
    });
    
    // Listen for session errors from server
    directSocket.on('SESSION_ERROR', (data: { error: string }) => {
      console.error('Session error from server:', data.error);
      setConnectionState('error');
      setConnectionError(data.error);
      
      // Notify user of error
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n\x1b[1;31mSession error: ${data.error}\x1b[0m\r\n`);
      }
    });
    
    // Listen for session timeout
    directSocket.on('SESSION_TIMEOUT', () => {
      console.error('Session timed out on the server');
      setConnectionState('error');
      setConnectionError('Session timed out. Please reconnect.');
      
      // Notify user of timeout
      if (xtermRef.current) {
        xtermRef.current.write('\r\n\x1b[1;31mSession timed out on the server.\x1b[0m\r\n');
        xtermRef.current.write('\r\n\x1b[1;33mPlease click Reconnect to create a new session.\x1b[0m\r\n');
      }
    });
    
    // Store the socket for cleanup
    window._directTerminalSocket = directSocket;
    
    // Set up a heartbeat to keep the connection alive
    const heartbeatInterval = setInterval(() => {
      if (directSocket.connected) {
        console.log('Sending heartbeat to keep connection alive');
        directSocket.emit('HEARTBEAT', { sessionId: terminalStore.state.sessionId });
      }
    }, 30000); // Every 30 seconds
    
    // Initialize the session
    directSocket.emit('INIT_SESSION', { sessionId: terminalStore.state.sessionId });
    
    // Set up terminal input handler
    if (xtermRef.current) {
      xtermRef.current.onData((data) => {
        // if (directSocket.connected) {
          // Send the terminal input directly to the server
          directSocket.emit('TERMINAL_INPUT', { 
            sessionId: terminalStore.state.sessionId, 
            data 
          });
        // } else {
        //   console.warn('Cannot send terminal input: socket not connected');
        //   // Provide feedback in the terminal
        //   if (xtermRef.current) {
        //     xtermRef.current.write('\r\n\x1b[1;31mCannot send input: terminal not connected\x1b[0m\r\n');
        //   }
        // }
      });
    }
    
    // Return cleanup function
    return () => {
      clearInterval(heartbeatInterval);
      
      if (directSocket) {
        directSocket.removeAllListeners();
        directSocket.disconnect();
      }
    };
  };
  
  // Manual reconnect function
  const handleReconnect = async () => {
    try {
      console.log('Attempting to reconnect terminal session');
      setConnectionState('reconnecting');
      setConnectionError(null);
      setReconnectAttempts(prev => prev + 1);
      
      // If we have a socket, try to reconnect it first
      if (socketRef.current) {
        console.log('Cleaning up existing socket connection');
        socketRef.current.removeAllListeners();
        socketRef.current.disconnect();
      }
      
      // Update UI to show reconnecting state
      if (xtermRef.current) {
        xtermRef.current.write('\r\n\x1b[1;33mReconnecting terminal session...\x1b[0m\r\n');
      }
      
      // For sandbox timeout errors or after multiple reconnect attempts, always create a new session
      if (connectionError?.includes('timeout') || 
          connectionError?.includes('Sandbox timeout') || 
          reconnectAttempts > 2) {
        console.log('Timeout detected or multiple reconnect attempts, creating a new session');
        // Force a new session
        await terminalStore.initializeSession();
        sessionIdRef.current = terminalStore.state.sessionId || undefined;
      } else {
        // Check if session still exists on the server
        const sessionExists = await terminalService.checkSession(sessionIdRef.current || '');
        
        if (!sessionExists) {
          console.log('Session no longer exists, creating a new one');
          // Initialize a new session
          await terminalStore.initializeSession();
          sessionIdRef.current = terminalStore.state.sessionId || undefined;
        } else {
          console.log('Session still exists, reconnecting');
        }
      }
      
      // Re-initialize socket connection
      if (terminalStore.state.sessionId) {
        setupDirectSocketConnection();
      }
      
      return true;
    } catch (error) {
      console.error('Failed to reconnect:', error);
      setConnectionState('error');
      setConnectionError((error as Error).message);
      
      // Update UI to show error state
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n\x1b[1;31mReconnection failed: ${(error as Error).message}\x1b[0m\r\n`);
        xtermRef.current.write('\r\n\x1b[1;33mTry clicking Reconnect again or refresh the page.\x1b[0m\r\n');
      }
      
      return false;
    }
  };
  // Handle visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isDocumentVisible = document.visibilityState === 'visible';
      console.log(`Document visibility changed: ${isDocumentVisible ? 'visible' : 'hidden'}`);
      setIsVisible(isDocumentVisible);
      
      // If becoming visible again and socket is disconnected, reconnect
      if (isDocumentVisible && socketRef.current && !socketRef.current.connected && 
          (connectionState === 'connected' || connectionState === 'connecting' || 
           connectionState === 'reconnecting' || connectionState === 'error')) {
        console.log('Reconnecting socket due to visibility change');
        handleReconnect();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [connectionState]);

  // Initialize terminal
  useEffect(() => {
    console.log('Initializing xterm.js terminal...');
    
    // Clear any existing content in the container
    if (terminalRef.current) {
      terminalRef.current.innerHTML = '';
      console.log('Cleared terminal container');
    }
    
    // Create a new terminal instance
    const term = new XTerm({
      cursorBlink: true,
      theme: {
        background: '#1e1f28',
        foreground: '#f8f8f2',
        cursor: '#f8f8f2',
        black: '#000000',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#bd93f9',
        magenta: '#ff79c6',
        cyan: '#8be9fd',
        white: '#bbbbbb',
        brightBlack: '#555555',
        brightRed: '#ff5555',
        brightGreen: '#50fa7b',
        brightYellow: '#f1fa8c',
        brightBlue: '#bd93f9',
        brightMagenta: '#ff79c6',
        brightCyan: '#8be9fd',
        brightWhite: '#ffffff',
      },
    });
    
    // Initialize fit addon
    const fitAddon = new FitAddon();
    term.loadAddon(fitAddon);
    
    // Save references
    xtermRef.current = term;
    fitAddonRef.current = fitAddon;
    
    // Mount terminal
    if (terminalRef.current) {
      console.log('Opening terminal in container:', terminalRef.current);
      term.open(terminalRef.current);
      console.log('Terminal opened successfully');
      fitAddon.fit();
      
      // Write a test message to confirm it's working
      term.write('\r\n\x1b[1;34mInitializing terminal...\x1b[0m\r\n');
      console.log('Test message written to terminal');
      
      // Set the terminal as ready
      setIsReady(true);
      
      // Set up direct socket connection if we have a session ID
      if (terminalStore.state.sessionId) {
        setupDirectSocketConnection();
      }
    } else {
      console.error('Terminal container ref is not available');
    }
    
    return () => {
      // Clean up terminal when component unmounts
      if (xtermRef.current) {
        console.log('Cleaning up terminal');
        xtermRef.current.dispose();
      }
    };
  }, []);

  // Initialize the terminal session with the direct socket connection
  useEffect(() => {
    if (!isReady) return;
    
    // Initialize terminal session
    const initSession = async () => {
      try {
        console.log('Initializing terminal session...');
        await terminalStore.initializeSession(sessionId);
        console.log('Terminal session initialized successfully');
        
        // Now that the session is initialized, set up the direct socket connection
        setupDirectSocketConnection();
        
        // Write welcome message
        if (xtermRef.current) {
          xtermRef.current.write('\r\n');
          xtermRef.current.write('\x1b[1;32m🚀 Terminal connected to e2b sandbox for EAS build process\x1b[0m\r\n');
          xtermRef.current.write('\r\n');
          xtermRef.current.write('This terminal provides direct access to the build environment.\r\n');
          xtermRef.current.write('You can run commands like:\r\n');
          xtermRef.current.write('  - eas build --platform ios\r\n');
          xtermRef.current.write('  - eas build --platform android\r\n');
          xtermRef.current.write('  - npm install\r\n');
          xtermRef.current.write('\r\n');
          xtermRef.current.write('$ ');
        }
        
        // Execute initial command if provided
        if (initialCommand && terminalStore.state.sessionId && xtermRef.current) {
          setTimeout(() => {
            if (xtermRef.current) {
              xtermRef.current.write(initialCommand);
              setInputBuffer(initialCommand);
              
              setTimeout(() => {
                if (xtermRef.current) {
                  xtermRef.current.write('\r\n');
                }
                terminalStore.executeCommand(initialCommand);
                setInputBuffer('');
              }, 100);
            }
          }, 500);
        }
      } catch (error) {
        console.error('Failed to initialize terminal:', error);
        if (xtermRef.current) {
          xtermRef.current.write(`\r\nError: ${(error as Error).message}\r\n`);
        }
      }
    };
    
    // Initialize the terminal session
    initSession();
    
    // Handle window resize
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        // Fit the terminal to the container
        fitAddonRef.current.fit();
        
        // Get the new dimensions
        const dimensions = fitAddonRef.current.proposeDimensions();
        if (dimensions && terminalStore.state.sessionId && window._directTerminalSocket) {
          console.log('Terminal dimensions:', dimensions);
          
          // Notify the server about the new dimensions
          window._directTerminalSocket.emit('RESIZE_TERMINAL', {
            sessionId: terminalStore.state.sessionId,
            cols: dimensions.cols,
            rows: dimensions.rows
          });
        }
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      
      // Clean up the direct socket connection
      if (window._directTerminalSocket) {
        // Remove all listeners before disconnecting to prevent memory leaks
        window._directTerminalSocket.removeAllListeners();
        window._directTerminalSocket.disconnect();
        delete window._directTerminalSocket;
      }
      
      // Clear socket ref
      socketRef.current = null;
      
      // Properly dispose the terminal
      if (xtermRef.current) {
        try {
          // Unregister all event listeners
          xtermRef.current.onData(() => {});
          xtermRef.current.onResize(() => {});
          xtermRef.current.onSelectionChange(() => {});
          // Dispose the terminal
          xtermRef.current.dispose();
        } catch (error) {
          console.error('Error disposing terminal:', error);
        }
      }
      
      // Clean up the fit addon
      fitAddonRef.current = null;
      
      // Close the session in the store
      terminalStore.closeSession();
    };
  }, [sessionId, initialCommand, isReady]);

  // Handle terminal errors
  useEffect(() => {
    if (!xtermRef.current || !isReady || !terminalStore.state.error) {
      return;
    }

    // Write error to terminal
    xtermRef.current.write(`\r\nError: ${terminalStore.state.error}\r\n$ `);
    
    // Clear error
    terminalStore.clearError();
  }, [terminalStore.state.error, isReady]);

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    
    // Resize terminal after state update
    setTimeout(() => {
      if (fitAddonRef.current) {
        fitAddonRef.current.fit();
      }
    }, 100);
  };

  // Handle close
  const handleClose = () => {
    terminalStore.closeSession();
    if (onClose) {
      onClose();
    }
  };

  return (
    <div 
      className={`
        bg-[#1a1b26] rounded-md overflow-hidden flex flex-col border border-gray-700
        ${isFullscreen ? 'fixed inset-0 z-50' : 'h-[500px]'}
      `}
      style={{ display: 'flex', flexDirection: 'column' }}
    >
      {/* Terminal header */}
      <div className="flex items-center justify-between px-4 py-2 bg-[#15161e] text-gray-200">
        <div className="flex items-center">
          <TerminalSquare className="w-4 h-4 mr-2" />
          <span className="text-sm font-medium">
            {connectionState === 'connected' ? 'EAS Build Terminal' : 
             connectionState === 'connecting' ? 'Connecting...' :
             connectionState === 'reconnecting' ? 'Reconnecting...' :
             connectionState === 'error' ? 'Connection Error' : 'Disconnected'}
            {terminalStore.state.sessionId && (
              <span className="ml-2 text-xs text-gray-400">Session: {terminalStore.state.sessionId.substring(0, 8)}...</span>
            )}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          {/* Connection controls */}
          {connectionState === 'connected' && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs text-gray-400 hover:text-gray-200"
              onClick={handleDisconnect}
            >
              Disconnect
            </Button>
          )}
          {(connectionState === 'disconnected' || connectionState === 'error') && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs text-green-400 hover:text-green-300"
              onClick={handleReconnect}
            >
              Reconnect
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-gray-400 hover:text-gray-200"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-gray-400 hover:text-gray-200"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Terminal container */}
      <div 
        ref={terminalRef} 
        className="flex-1 overflow-hidden"
        style={{ padding: '4px' }}
      />
      
      {/* Upload progress indicator */}
      {isUploading && (
        <div className="p-2 bg-[#15161e] border-t border-gray-700">
          <div className="text-xs text-gray-300 mb-1">
            {uploadStatus} ({uploadProgress}%)
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1.5">
            <div 
              className="bg-blue-500 h-1.5 rounded-full" 
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        </div>
      )}
      
      {/* Error message */}
      {connectionState === 'error' && connectionError && (
        <div className="p-2 bg-red-900 text-white text-xs">
          <div className="font-bold">Connection Error:</div>
          <div>{connectionError}</div>
          {(connectionError.includes('timeout') || connectionError.includes('Sandbox timeout')) && (
            <div className="mt-1">
              The sandbox environment has timed out. Click Reconnect to start a new session.
            </div>
          )}
        </div>
      )}
    </div>
  );
});
