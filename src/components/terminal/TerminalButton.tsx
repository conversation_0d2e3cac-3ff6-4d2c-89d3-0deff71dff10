'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { TerminalSquare } from 'lucide-react';
import {Terminal} from './Terminal';

interface TerminalButtonProps {
  sessionId?: string;
  initialCommand?: string;
  buttonText?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export const TerminalButton: React.FC<TerminalButtonProps> = ({
  sessionId,
  initialCommand,
  buttonText = 'Terminal',
  variant = 'outline',
  size = 'default',
  className = '',
}) => {
  const [showTerminal, setShowTerminal] = useState(false);

  const toggleTerminal = () => {
    setShowTerminal(!showTerminal);
  };

  return (
    <div className="relative">
      <Button
        variant={variant}
        size={size}
        onClick={toggleTerminal}
        className={className}
      >
        <TerminalSquare className="w-4 h-4 mr-2" />
        {buttonText}
      </Button>

      {showTerminal && (
        <div className="absolute bottom-full mb-2 right-0 w-[800px] z-50">
          <Terminal
            sessionId={sessionId}
            initialCommand={initialCommand}
            onClose={() => setShowTerminal(false)}
          />
        </div>
      )}
    </div>
  );
};

export default TerminalButton;
