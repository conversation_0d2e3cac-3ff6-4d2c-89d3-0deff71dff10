'use client';

import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Terminal } from './Terminal';
import { terminalAutomationService, PromptPattern, AutomationOptions } from '@/services/TerminalAutomationService';
import { terminalStore } from '@/stores/TerminalStore';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle2, MessageSquare, Send } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';

export interface AutomatedTerminalProps {
  sessionId?: string;
  onClose?: () => void;
  initialCommand?: string;
  automationEnabled?: boolean;
  customPatterns?: PromptPattern[];
  onPromptAutomated?: (pattern: PromptPattern, match: RegExpMatchArray, response: string) => void;
  onPromptDetected?: (pattern: PromptPattern, match: RegExpMatchArray) => void;
}

export const AutomatedTerminal = observer(({
  sessionId,
  onClose,
  initialCommand,
  automationEnabled = true,
  customPatterns,
  onPromptAutomated,
  onPromptDetected
}: AutomatedTerminalProps) => {
  const [isAutomationEnabled, setIsAutomationEnabled] = useState(automationEnabled);
  const [manualResponse, setManualResponse] = useState('');
  const [isWaitingForInput, setIsWaitingForInput] = useState(false);
  const [lastPrompt, setLastPrompt] = useState<{ pattern: PromptPattern, match: RegExpMatchArray } | null>(null);
  const [automationHistory, setAutomationHistory] = useState<Array<{
    timestamp: number;
    pattern: PromptPattern;
    response: string;
  }>>([]);
  const [showHistory, setShowHistory] = useState(false);
  
  // Initialize automation service when terminal session is ready
  useEffect(() => {
    if (!terminalStore.state.sessionId) return;
    
    const options: AutomationOptions = {
      enabled: isAutomationEnabled,
      customPatterns,
      useDefaultPatterns: true,
      onPromptAutomated: (pattern, match, response) => {
        // Add to history
        setAutomationHistory(prev => [
          {
            timestamp: Date.now(),
            pattern,
            response
          },
          ...prev
        ].slice(0, 20)); // Keep only the last 20 items
        
        // Reset waiting state
        setIsWaitingForInput(false);
        setLastPrompt(null);
        
        // Call external handler if provided
        if (onPromptAutomated) {
          onPromptAutomated(pattern, match, response);
        }
      },
      onPromptDetected: (pattern, match) => {
        // Update state to show we're waiting for input
        setIsWaitingForInput(true);
        setLastPrompt({ pattern, match });
        
        // Call external handler if provided
        if (onPromptDetected) {
          onPromptDetected(pattern, match);
        }
      }
    };
    
    // Initialize automation service
    terminalAutomationService.initialize(terminalStore.state.sessionId, options);
    
    return () => {
      // Clean up automation service
      terminalAutomationService.cleanup();
    };
  }, [terminalStore.state.sessionId, isAutomationEnabled, customPatterns, onPromptAutomated, onPromptDetected]);
  
  // Update automation enabled state when prop changes
  useEffect(() => {
    if (automationEnabled !== isAutomationEnabled) {
      setIsAutomationEnabled(automationEnabled);
      terminalAutomationService.setEnabled(automationEnabled);
    }
  }, [automationEnabled]);
  
  // Toggle automation
  const toggleAutomation = () => {
    const newState = !isAutomationEnabled;
    setIsAutomationEnabled(newState);
    terminalAutomationService.setEnabled(newState);
  };
  
  // Handle manual response submission
  const handleManualResponseSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!manualResponse.trim() || !isWaitingForInput) return;
    
    // Send the response
    terminalAutomationService.respondToPrompt(manualResponse);
    
    // Clear the input
    setManualResponse('');
    
    // Reset waiting state
    setIsWaitingForInput(false);
    setLastPrompt(null);
  };
  
  // Check for waiting input state periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const isWaiting = terminalAutomationService.isWaitingForUserInput();
      const lastPattern = terminalAutomationService.getLastPromptPattern();
      const lastMatch = terminalAutomationService.getLastPromptMatch();
      
      if (isWaiting && lastPattern && lastMatch) {
        setIsWaitingForInput(true);
        setLastPrompt({ pattern: lastPattern, match: lastMatch });
      }
    }, 500);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="flex flex-col space-y-2">
      {/* Automation controls */}
      <div className="flex items-center justify-between p-2 bg-muted/30 dark:bg-muted/10 rounded-md">
        <div className="flex items-center space-x-2">
          <Switch
            id="automation-toggle"
            checked={isAutomationEnabled}
            onCheckedChange={toggleAutomation}
          />
          <Label htmlFor="automation-toggle" className="text-xs">
            Terminal Automation
          </Label>
          
          {isAutomationEnabled && (
            <Badge variant="outline" className="ml-2 text-[10px] py-0 h-5 bg-green-500/10 text-green-500 border-green-500/20">
              Enabled
            </Badge>
          )}
          
          {!isAutomationEnabled && (
            <Badge variant="outline" className="ml-2 text-[10px] py-0 h-5 bg-amber-500/10 text-amber-500 border-amber-500/20">
              Manual Mode
            </Badge>
          )}
        </div>
        
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-6 text-xs"
          onClick={() => setShowHistory(!showHistory)}
        >
          <MessageSquare className="h-3 w-3 mr-1" />
          {automationHistory.length > 0 ? `History (${automationHistory.length})` : 'History'}
        </Button>
      </div>
      
      {/* Automation history */}
      {showHistory && automationHistory.length > 0 && (
        <Card className="p-2 max-h-32 overflow-y-auto text-xs">
          <div className="space-y-1">
            {automationHistory.map((item, index) => (
              <div key={index} className="flex items-start space-x-2 text-[10px]">
                <CheckCircle2 className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <span className="font-medium">{item.pattern.description}:</span>
                  <span className="ml-1 text-muted-foreground">{item.response.trim()}</span>
                  <span className="ml-1 text-muted-foreground/50">
                    ({new Date(item.timestamp).toLocaleTimeString()})
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
      
      {/* Terminal component */}
      <Terminal 
        sessionId={sessionId}
        onClose={onClose}
        initialCommand={initialCommand}
      />
      
      {/* Manual response input */}
      {isWaitingForInput && !isAutomationEnabled && lastPrompt && (
        <form onSubmit={handleManualResponseSubmit} className="flex items-center space-x-2 mt-2">
          <div className="flex-shrink-0">
            <AlertCircle className="h-4 w-4 text-amber-500" />
          </div>
          <div className="flex-grow">
            <div className="text-xs font-medium mb-1">
              {lastPrompt.pattern.description}
            </div>
            <div className="flex items-center space-x-2">
              <Input
                value={manualResponse}
                onChange={(e) => setManualResponse(e.target.value)}
                placeholder="Type your response..."
                className="h-8 text-xs"
                autoFocus
              />
              <Button type="submit" size="sm" className="h-8 px-2">
                <Send className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </form>
      )}
    </div>
  );
});
