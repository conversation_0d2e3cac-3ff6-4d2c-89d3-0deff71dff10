'use client';

import React, { useRef, memo, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, AlertCircle, Smartphone, Code, RefreshCw, Maximize2 } from 'lucide-react';
import { toast } from 'sonner';
import { generateUUID } from '@/lib/utils';

interface PreviewPanelProps {
  onRefresh: () => void;
  projectId: string;
  chatId: string;
}

const PreviewPanel = observer(({ onRefresh, chatId, projectId }: PreviewPanelProps) => {
  const { designPreviewStore } = useStores();
  const { previewHtml, isLoading, streamingContent, sessionId } = designPreviewStore.state;
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string>(sessionId || generateUUID());
  const [fullscreen, setFullscreen] = useState(false);

  // Always use chatId as the sessionId for rendering
  useEffect(() => {
    console.log('[DEBUG] PreviewPanel: Always using chatId as sessionId');
    setCurrentSessionId(chatId);
  }, [chatId]);

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    setFullscreen(!fullscreen);
  };

  const handleCopyHtml = () => {
    if (previewHtml) {
      navigator.clipboard.writeText(previewHtml);
      toast.success('HTML code copied to clipboard');
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <Tabs defaultValue="preview" className="flex flex-col h-full">
        <CardHeader className="pb-0">
          <div className="flex justify-between items-center">
            <CardTitle>App Screen Preview</CardTitle>
            <TabsList>
              <TabsTrigger value="preview" className="flex items-center gap-1">
                <Smartphone className="h-4 w-4" />
                <span>Preview</span>
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center gap-1">
                <Code className="h-4 w-4" />
                <span>HTML</span>
              </TabsTrigger>
            </TabsList>
          </div>
          <CardDescription className="flex justify-between items-center mt-2">
            <span>Visual representation of the described app screen</span>
            {isLoading && streamingContent && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                className="flex items-center gap-1"
              >
                <RefreshCw className="h-3.5 w-3.5" />
                <span>Refresh</span>
              </Button>
            )}
          </CardDescription>
        </CardHeader>

        <TabsContent value="preview" className="flex-1 p-0 m-0 overflow-hidden">
          <CardContent className="p-0 bg-[#f0f0f0] h-full overflow-hidden relative">
            {/* Fullscreen button */}
            {previewHtml && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 z-10 bg-white/80 hover:bg-white/90"
                onClick={toggleFullscreen}
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            )}

            <div className={`h-full flex items-center justify-center overflow-x-auto ${fullscreen ? 'fixed inset-0 z-50 bg-[#f0f0f0]' : ''}`} style={{ overscrollBehaviorX: 'contain' }}>
              {currentSessionId ? (
                <>
                {console.log('[DEBUG] Rendering iframe with URL:', `/api/project/${projectId}/chat/${chatId}/design-render/${currentSessionId}`)}
                <iframe
                  ref={iframeRef}
                  className="w-full h-full"
                  title="App Screen Preview"
                  src={`/api/project/${projectId}/chat/${chatId}/design-render/${currentSessionId}`}
                  sandbox="allow-scripts allow-same-origin"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  style={{ minWidth: '100%', width: '1px' }}
                  key={currentSessionId} /* Force iframe refresh when sessionId changes */
                />
                </>
              ) : (
                <div className="w-full h-full flex flex-col items-center justify-center bg-[#f5f5f5]">
                  {isLoading ? (
                    <>
                      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                      <p className="text-sm text-gray-500">Generating preview...</p>
                    </>
                  ) : (
                    <>
                      <Smartphone className="h-12 w-12 text-gray-300 mb-4" />
                      <p className="text-sm text-gray-500">Describe your app screen to see a preview</p>
                    </>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </TabsContent>

        <TabsContent value="code" className="flex-1 p-0 m-0 overflow-hidden">
          <CardContent className="p-4 h-full flex flex-col">
            {previewHtml ? (
              <>
                <ScrollArea className="flex-1 rounded-md border">
                  <pre className="text-sm font-mono p-4">
                    {previewHtml}
                  </pre>
                </ScrollArea>
                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    onClick={handleCopyHtml}
                  >
                    Copy HTML
                  </Button>
                </div>
              </>
            ) : (
              <div className="flex-1 flex flex-col items-center justify-center text-muted-foreground">
                {isLoading ? (
                  <>
                    <Loader2 className="h-8 w-8 animate-spin mb-4" />
                    <p>Generating HTML code...</p>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-8 w-8 mb-4" />
                    <p>No HTML code generated yet</p>
                    <p className="text-sm mt-2">Describe your app screen to generate HTML</p>
                  </>
                )}
              </div>
            )}
          </CardContent>
        </TabsContent>
      </Tabs>
    </Card>
  );
});

export default memo(PreviewPanel);
