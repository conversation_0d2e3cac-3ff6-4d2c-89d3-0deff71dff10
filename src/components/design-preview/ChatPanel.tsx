'use client';

import React, { useState, useRef, useEffect, memo } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, ArrowUp, X, ThumbsUp, PencilLine } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { toast } from 'sonner';
import { DesignMessageItem } from './DesignMessageItem';

interface ChatPanelProps {
  onSubmit: (e: React.FormEvent) => Promise<void>;
  onStop: () => void;
  input: string;
  setInput: (input: string) => void;
}

type MessageRole = 'user' | 'assistant' | 'system' | 'data';

interface Message {
  id?: string;
  role: MessageRole;
  content: string;
}

const ChatPanel = observer(({ onSubmit, onStop, input, setInput }: ChatPanelProps) => {
  const { designPreviewStore } = useStores();
  const { messages, isLoading, sessionId } = designPreviewStore.state;
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Function to handle design approval
  const handleApproveDesign = async (): Promise<void> => {
    try {
      const projectId = designPreviewStore.state.projectId;
      const response = await fetch(`/api/project/${projectId}/chat/${sessionId}/approve-design`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (!response.ok) {
        throw new Error('Failed to approve design');
      }
      
      // Set approved state in store
      designPreviewStore.setDesignApproved(true);
      toast.success('Design approved! Your app is being built.');
      setIsApprovalDialogOpen(false);
    } catch (error) {
      console.error('Error approving design:', error);
      toast.error('Failed to approve design. Please try again.');
      setIsApprovalDialogOpen(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const handleRequestChanges = () => {
    setInput('I like the design, but can you make these changes: ');
    // Focus the textarea
    const textarea = document.querySelector('textarea');
    if (textarea) {
      textarea.focus();
    }
    setIsApprovalDialogOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading && input.trim()) {
        onSubmit(e as unknown as React.FormEvent);
      }
    }
  };

  // Scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Show approval dialog when design is ready
  const showApprovalDialog = () => {
    setIsApprovalDialogOpen(true);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <Card className="bg-muted/50 p-8 flex flex-col items-center justify-center h-full">
            <CardContent className="text-center space-y-2 pt-6">
              <h3 className="text-lg font-medium">Design Preview</h3>
              <p className="text-sm text-muted-foreground">
                Describe your app and see a visual preview before generating code.
              </p>
            </CardContent>
          </Card>
        ) : (
          messages.map((message: Message, index: number) => {
            // Create a unique key for each message that includes both message ID and index
            const messageKey = `message-${message.id || 'unknown'}-${index}`;
            
            return (
              <div
                key={messageKey}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {/* Check if the message might contain design HTML */}
                {message.role === 'assistant' && message.content.includes('<PREVIEW_HTML>') ? (
                  <DesignMessageItem 
                    key={`design-${messageKey}`}
                    content={message.content}
                    role={message.role}
                    projectId={designPreviewStore.state.projectId}
                    chatId={designPreviewStore.state.chatId}
                    sessionId={`${sessionId}-${index}`} // Make session ID unique per message
                  />
                ) : (
                  <div
                    className={`max-w-[80%] rounded-lg p-4 ${message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
                  >
                    {message.content}
                  </div>
                )}
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Design approval actions */}
      {designPreviewStore.state.previewHtml && !designPreviewStore.state.isDesignApproved && (
        <div className="flex justify-center gap-2 my-4">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={showApprovalDialog}
          >
            <ThumbsUp className="h-4 w-4" />
            Approve Design
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={handleRequestChanges}
          >
            <PencilLine className="h-4 w-4" />
            Request Changes
          </Button>
        </div>
      )}

      {/* Input area */}
      <div className="p-4 border-t">
        <div className="relative">
          <Textarea
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Describe the app screen you want to visualize..."
            className="min-h-[80px] pr-12 resize-none"
          />
          <Button
            type="button"
            size="icon"
            className="absolute bottom-2 right-2 h-8 w-8 rounded-full"
            disabled={!input.trim() || isLoading}
            onClick={(e) => onSubmit(e as unknown as React.FormEvent)}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ArrowUp className="h-4 w-4" />
            )}
          </Button>
        </div>

        {isLoading && (
          <Button
            variant="outline"
            className="w-full mt-4"
            onClick={onStop}
            type="button"
          >
            <X className="mr-2 h-4 w-4" />
            Stop generating
          </Button>
        )}
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={isApprovalDialogOpen}
        onOpenChange={setIsApprovalDialogOpen}
        onConfirm={handleApproveDesign}
        title="Approve Design"
        description="Are you happy with this design and ready to generate code for your app?"
        confirmText="Yes, build my app"
        cancelText="No, I need changes"
        onCancel={handleRequestChanges}
        hideTriggerButton={true}
      />
    </div>
  );
});

export default memo(ChatPanel);
