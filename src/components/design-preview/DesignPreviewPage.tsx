'use client';

import React, {useEffect, useCallback, useState, useRef} from 'react';
import {observer} from 'mobx-react-lite';
import {useStores} from '@/stores/utils/useStores';
import {toast} from 'sonner';
import ChatPanel from './ChatPanel';
import PreviewPanel from './PreviewPanel';
import {generateUUID, convertToUIMessages} from '@/lib/utils';
import {ResizableHandle, ResizablePanel, ResizablePanelGroup} from "@/components/ui/resizable";
import {DesignStreamClient} from '@/lib/design-stream-client';
import {Message} from 'ai';

interface DesignPreviewPageProps {
    projectId: string;
    chatId: string;
    messagesFromDb: Array<any>;
    chat: any; // Chat object from the database
}

const DesignPreviewPage = observer(({projectId, chatId, messagesFromDb, chat}: DesignPreviewPageProps) => {
    const {designPreviewStore} = useStores();
    const {
        sessionId,
        streamingContent,
        isLoading,
        messages
    } = designPreviewStore.state;

    // State to track if messages are loaded
    const [initialMessageSent, setInitialMessageSent] = useState(false);

    // Initialize the store with the project ID and load messages from props
    useEffect(() => {
        // Set project ID in store
        designPreviewStore.setProjectId(projectId);
        designPreviewStore.setChatId(chatId);

        // Process messages from props
        if (messagesFromDb && messagesFromDb.length > 0) {
            // Convert messages to the format expected by the store
            // Set messages in store
            designPreviewStore.setMessages(messagesFromDb);

            // Check if we need to initiate the first message
            const hasUserMessage = messagesFromDb.some(msg => msg.role === 'user');
            const shouldRunInitialMessage = chat && !chat.isInitialized && hasUserMessage && !initialMessageSent;
            
            if (shouldRunInitialMessage) {
                // Find the first user message to use as initial prompt
                const firstUserMessage = messagesFromDb.find(msg => msg.role === 'user');
                if (firstUserMessage) {
                    // Mark as sent so we don't try to send it again
                    setInitialMessageSent(true);
                    
                    // Mark chat as initialized
                    if (chat) {
                        chat.isInitialized = true;
                    }
                    
                    // Use the first user message to start the design generation
                    const prompt = firstUserMessage.content as string;
                    handleInitialDesignGeneration(prompt);
                }
            }
        } else {
            // No messages found, set empty array
            designPreviewStore.setMessages([]);
        }
    }, [projectId, chatId, messagesFromDb, designPreviewStore]);

    // Local state for input and session monitoring
    const [input, setInput] = useState('');
    const [sessionStatus, setSessionStatus] = useState<'idle' | 'initializing' | 'generating' | 'complete' | 'error'>('idle');
    
    // Function to handle initial design generation when chat is not initialized
    const handleInitialDesignGeneration = async (prompt: string) => {
        // Set loading state
        designPreviewStore.setIsLoading(true);
        setSessionStatus('initializing');
        
        // Create a server-side session for the design preview
        const sessionId = await designPreviewStore.createDesignSession(prompt, {isFirstLoad: true});
        
        if (sessionId) {
            console.log('Initial design generation started:', sessionId);
        } else {
            console.error('Failed to create design session');
            toast.error('Failed to start design generation');
            designPreviewStore.setIsLoading(false);
            setSessionStatus('error');
        }
    };

    // Reference to the stream client
    const streamClientRef = useRef<DesignStreamClient | null>(null);

    // Update input in store when local input changes
    useEffect(() => {
        designPreviewStore.setInput(input);
    }, [input, designPreviewStore]);

    // Set up stream connection when session ID changes
    useEffect(() => {
        // Only connect if we have a session ID and we're loading
        console.log('[DEBUG] Stream connection check:', { sessionId, isLoading, sessionStatus });
        if (sessionId && isLoading && sessionStatus !== 'complete' && sessionStatus !== 'error') {
            // Disconnect any existing stream
            if (streamClientRef.current) {
                streamClientRef.current.disconnect();
            }

            // Create a new stream client
            const streamClient = new DesignStreamClient(
                sessionId,
                projectId,
                chatId,
                // Update callback
                (data) => {
                    // Update session status
                    if (data.status === 'initializing' || data.status === 'generating' ||
                        data.status === 'complete' || data.status === 'error') {
                        setSessionStatus(data.status);
                    }

                    // Update HTML content
                    if (data.html) {
                        designPreviewStore.setPreviewHtml(data.html);
                    }

                    // If complete or error, update loading state
                    if (data.status === 'complete' || data.status === 'error') {
                        designPreviewStore.setIsLoading(false);

                        if (data.status === 'complete') {
                            toast.success('Design preview generated successfully');
                        } else {
                            toast.error('Error generating design preview');
                            designPreviewStore.setError('Failed to generate design preview');
                        }
                    }
                },
                // Error callback
                (error) => {
                    console.error('Stream error:', error);
                    toast.error('Error connecting to design stream');
                    designPreviewStore.setError('Failed to connect to design stream');
                    designPreviewStore.setIsLoading(false);
                },
                // Complete callback
                () => {
                    console.log('Stream complete');
                    designPreviewStore.setIsLoading(false);
                }
            );

            // Connect to the stream
            streamClient.connect();

            // Store the reference
            streamClientRef.current = streamClient;

            // Clean up on unmount
            return () => {
                if (streamClientRef.current) {
                    streamClientRef.current.disconnect();
                    streamClientRef.current = null;
                }
            };
        }
    }, [sessionId, isLoading, sessionStatus, designPreviewStore]);

    // Handle form submission
    const handleFormSubmit = useCallback(async (e: React.FormEvent) => {
        console.log('[DEBUG] Form submitted in DesignPreviewPage');
        console.log('[DEBUG] Current state before form submit:', { 
            currentSessionId: designPreviewStore.state.sessionId,
            isLoading: designPreviewStore.state.isLoading,
            status: sessionStatus
        });
        e.preventDefault();
        if (!input.trim()) {
            console.log('[DEBUG] Input is empty, returning');
            return;
        }

        // Add the user message to the messages array
        const userMessage: Message = {
            id: generateUUID(),
            role: 'user',
            content: input.trim(),
            createdAt: new Date()
        };

        // Add the message to the store
        const updatedMessages = [...designPreviewStore.state.messages, userMessage];
        designPreviewStore.setMessages(updatedMessages);

        // Clear input field
        setInput('');

        // Reset state for new generation
        setSessionStatus('initializing');

        // Set loading state
        designPreviewStore.setIsLoading(true);

        // Create a server-side session for the design preview
        const prompt = input.trim();
        console.log('[DEBUG] Creating new design session with prompt:', prompt);
        const newSessionId = await designPreviewStore.createDesignSession(prompt);

        if (newSessionId) {
            console.log('[DEBUG] Server-side session created:', newSessionId);
            console.log('[DEBUG] Store state after session creation:', {
                storeSessionId: designPreviewStore.state.sessionId,
                isLoading: designPreviewStore.state.isLoading
            });
            // The session is created and design generation starts automatically
            // We'll poll for updates in the useEffect
            setSessionStatus('generating');
        } else {
            // If session creation failed, reset loading state
            designPreviewStore.setIsLoading(false);
            toast.error('Failed to create design session');
        }
    }, [input, designPreviewStore]);

    // Handle stop generation
    const handleStopGeneration = useCallback(async () => {
        if (sessionId) {
            // Disconnect stream client
            if (streamClientRef.current) {
                streamClientRef.current.disconnect();
                streamClientRef.current = null;
            }

            // Update session status to stop generation
            try {
                await fetch(`/api/project/${projectId}/chat/${chatId}/design-sessions/${sessionId}/stop`, {
                    method: 'POST'
                });
                designPreviewStore.setIsLoading(false);
                toast.info('Design generation stopped');
            } catch (error) {
                console.error('Error stopping generation:', error);
            }
        }
    }, [sessionId, designPreviewStore]);

    // Handle refresh preview
    const handleRefreshPreview = useCallback(async () => {
        if (sessionId) {
            try {
                // Fetch the latest HTML content directly
                const response = await fetch(`/api/project/${projectId}/chat/${chatId}/design-sessions/${sessionId}/html`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.html) {
                        designPreviewStore.setPreviewHtml(data.html);
                    }
                }
            } catch (error) {
                console.error('Error refreshing preview:', error);
            }
        }
    }, [sessionId, designPreviewStore]);

    // Clean up resources when component unmounts
    useEffect(() => {
        return () => {
            // Disconnect stream client if it exists
            if (streamClientRef.current) {
                streamClientRef.current.disconnect();
                streamClientRef.current = null;
            }

            // Clean up store resources
            designPreviewStore.cleanup();
        };
    }, [designPreviewStore]);

    return (
        <div className="mx-auto h-[calc(100vh-56px)]">

            <ResizablePanelGroup
                direction="horizontal"
                className="h-[calc(100%-120px)] rounded-lg border"
            >
                <ResizablePanel defaultSize={25} minSize={20}>
                    <ChatPanel
                        input={input}
                        setInput={setInput}
                        onSubmit={handleFormSubmit}
                        onStop={handleStopGeneration}
                    />
                </ResizablePanel>

                <ResizableHandle withHandle/>

                <ResizablePanel defaultSize={75} minSize={50}>
                    <PreviewPanel
                        onRefresh={handleRefreshPreview}
                        projectId={projectId}
                        chatId={chatId}
                    />
                </ResizablePanel>
            </ResizablePanelGroup>

        </div>
    );
});

export default DesignPreviewPage;
