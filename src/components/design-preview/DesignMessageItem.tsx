'use client';

import React, { memo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Eye, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';

interface DesignMessageItemProps {
  content: string;
  role: 'user' | 'assistant' | 'system' | 'data';
  projectId: string;
  chatId: string;
  sessionId?: string;
}

export const DesignMessageItem = observer(function DesignMessageItem({
  content,
  role,
  projectId,
  chatId,
  sessionId
}: DesignMessageItemProps) {
  const { designPreviewStore } = useStores();
  const { isLoading, sessionId: activeSessionId } = designPreviewStore.state;
  
  // Check if this message's session is the currently active one being generated
  const isCurrentlyGenerating = isLoading && sessionId === activeSessionId;
  // Check if the message contains HTML design code
  const hasDesignHtml = content.includes('<PREVIEW_HTML>') && content.includes('</PREVIEW_HTML>');
  
  // Extract the HTML content and text content if present
  let designHtml = null;
  let textContent = content;
  let designSessionId = sessionId;
  
  if (hasDesignHtml) {
    const htmlStartIndex = content.indexOf('<PREVIEW_HTML>') + '<PREVIEW_HTML>'.length;
    const htmlEndIndex = content.indexOf('</PREVIEW_HTML>');
    designHtml = content.substring(htmlStartIndex, htmlEndIndex);
    
    // Extract text content (everything before the HTML)
    textContent = content.substring(0, htmlStartIndex - '<PREVIEW_HTML>'.length).trim();
    
    // Extract text after the HTML (summary)
    const afterHtml = content.substring(htmlEndIndex + '</PREVIEW_HTML>'.length).trim();
    if (afterHtml) {
      textContent = textContent ? `${textContent}\n\n${afterHtml}` : afterHtml;
    }
    
    // Extract the session ID if it's embedded in the message
    const sessionMatch = content.match(/session_id:(\S+)/);
    if (sessionMatch && sessionMatch[1]) {
      designSessionId = sessionMatch[1];
    }
  }

  return (
    <div className={`max-w-[80%] rounded-lg p-4 ${role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
      {/* Display the text content */}
      <div className="whitespace-pre-wrap">{textContent}</div>
      
      {/* Show loading indicator when generating but no HTML yet */}
      {isCurrentlyGenerating && !hasDesignHtml && (
        <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Generating your design...</span>
        </div>
      )}
      
      {/* Display the design preview button if there's HTML */}
      {hasDesignHtml && designSessionId && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4"
        >
          <div className="rounded-lg bg-gradient-to-b from-card/95 to-muted/30 border border-border/50 dark:border-white/20 p-4 backdrop-blur-sm shadow-lg relative overflow-hidden">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium">Generated Design</span>
              <Button 
                size="sm" 
                variant="outline" 
                className="flex items-center gap-1 text-xs h-7 px-2"
                onClick={() => {
                  // Open the design in a new tab
                  window.open(`/api/project/${projectId}/chat/${chatId}/design-render/${designSessionId}`, '_blank');
                }}
                disabled={isCurrentlyGenerating}
              >
                <Eye className="h-3 w-3" />
                View Design
              </Button>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <div className="flex-1 flex items-center gap-2">
                <div className="relative h-2 w-2 flex items-center justify-center">
                  {isCurrentlyGenerating ? (
                    <>
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                    </>
                  ) : (
                    <div className="absolute inset-0 rounded-full bg-green-500" />
                  )}
                </div>
                <span className="font-medium">Design Preview</span>
              </div>
              {isCurrentlyGenerating ? (
                <div className="flex items-center gap-1 px-1.5 py-0.5 rounded-md text-[10px] font-medium uppercase tracking-wider text-blue-600 dark:text-blue-400">
                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  Generating
                </div>
              ) : (
                <span className="px-1.5 py-0.5 rounded-md text-[10px] font-medium uppercase tracking-wider text-emerald-600 dark:text-emerald-400">
                  Ready
                </span>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
});

export default DesignMessageItem;
