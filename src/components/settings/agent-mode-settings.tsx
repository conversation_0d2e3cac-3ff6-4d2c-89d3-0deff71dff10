'use client';

import { useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { BotIcon, InfoIcon, SparklesIcon } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { Badge } from '@/components/ui/badge';
import { trackFeatureEvent } from '@/lib/analytics/track';

interface AgentModeSettingsProps {
  className?: string;
}

export function AgentModeSettings({ className }: AgentModeSettingsProps) {
  const [agentModeEnabled, setAgentModeEnabled] = useLocalStorage('agent_mode_enabled', false);
  const [mounted, setMounted] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Ensure component is mounted before rendering to avoid hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleToggle = (checked: boolean) => {
    if (checked && !agentModeEnabled) {
      // Only show dialog when enabling
      setDialogOpen(true);
    } else {
      // When disabling, just update the state directly
      setAgentModeEnabled(false);
      
      // Track agent mode being disabled
      trackFeatureEvent('AGENT_MODE_TOGGLED', {
        feature_name: 'agent_mode',
        is_enabled: false,
        user_type: 'free', // This should be dynamically determined in a real implementation
        previous_state: true,
        new_state: false
      });
    }
  };
  
  const handleCancel = () => {
    // Track when user cancels enabling agent mode
    trackFeatureEvent('AGENT_MODE_TOGGLED', {
      feature_name: 'agent_mode',
      is_enabled: false,
      user_type: 'free',
      previous_state: false,
      new_state: false,
      trigger_source: 'dialog_cancelled'
    });
    
    setDialogOpen(false);
  };

  const handleConfirm = () => {
    setAgentModeEnabled(true);
    setDialogOpen(false);
    
    // Track agent mode being enabled
    trackFeatureEvent('AGENT_MODE_TOGGLED', {
      feature_name: 'agent_mode',
      is_enabled: true,
      user_type: 'free', // This should be dynamically determined in a real implementation
      previous_state: false,
      new_state: true
    });
  };

  if (!mounted) {
    return null;
  }

  return (
    <>
      <div className={`flex items-center space-x-2 ${className || ''}`}>
        <div className="flex items-center gap-2 w-full">
          <BotIcon size={16} className="text-foreground/70" />
          <div className="flex items-center gap-1.5">
            <Label htmlFor="agent-mode-enabled" className="text-xs cursor-pointer">
              AI Agent Mode
            </Label>
            <Badge 
              variant="outline" 
              className="px-1.5 py-0 h-4 text-[10px] border-amber-500/50 text-amber-500 flex items-center gap-0.5"
            >
              <SparklesIcon size={10} />
              <span>BETA</span>
            </Badge>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon size={14} className="text-foreground/50 cursor-help" />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-xs">
                  When enabled, AI Agent Mode allows the AI to take autonomous actions like searching the web, 
                  running code, and using tools to better assist you with complex tasks.
                </p>
                <p className="text-xs mt-2 text-amber-400">
                  <strong>Note:</strong> This feature is experimental and in early beta. It may not work properly in all scenarios.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Switch
          id="agent-mode-enabled"
          checked={agentModeEnabled}
          onCheckedChange={handleToggle}
          aria-label="Toggle agent mode"
          className="scale-75 data-[state=checked]:bg-primary"
        />
      </div>

      <ConfirmationDialog
        title="Enable AI Agent Mode (Beta)"
        hideTriggerButton={true}
        description={
          <p className="space-y-1">
            <span className="block">AI Agent Mode enables faster code edits, better context understanding, and support for large codebases.</span>
            <span className="block text-primary">Enjoy more autonomous assistance with complex tasks.</span>
            <span className="block text-amber-500 text-xs">Note: This is an experimental beta feature and results may vary.</span>
          </p>
        }
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        confirmText="Enable Agent Mode"
        cancelText="Cancel"
      />
    </>
  );
}

// Hook to access the agent mode settings from any component
export function useAgentModeSettings() {
  const [agentModeEnabled, setAgentModeEnabled] = useLocalStorage('agent_mode_enabled', true);
  return { agentModeEnabled, setAgentModeEnabled };
}
