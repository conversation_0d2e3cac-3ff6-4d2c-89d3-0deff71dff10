'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Globe } from 'lucide-react';
import { FormField, FormItem, FormControl } from '@/components/ui/form';
import { ProjectSettingsFormValues } from '@/lib/schemas/project-settings-schema';

interface VisibilitySectionProps {
  isLoading: boolean;
}

export function VisibilitySection({ isLoading }: VisibilitySectionProps) {
  const form = useFormContext<ProjectSettingsFormValues>();
  
  return (
    <Card className="border border-border/50 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium">Project Visibility</CardTitle>
        <CardDescription>
          Control who can view your project
        </CardDescription>
      </CardHeader>
      <CardContent>
        <FormField
          control={form.control}
          name="visibility"
          render={({ field }) => (
            <FormItem className="flex items-center justify-between space-y-0">
              <div className="space-y-0.5">
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="font-medium text-sm">Public Project</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  When enabled, anyone with the link can view this project
                </p>
              </div>
              <FormControl>
                <Switch 
                  checked={field.value === 'public'} 
                  onCheckedChange={(checked) => {
                    field.onChange(checked ? 'public' : 'private');
                    // Auto-submit the form when visibility changes
                    setTimeout(() => form.handleSubmit((data) => {
                      // This will trigger the onSubmit in the parent form
                      form.trigger();
                    })(), 0);
                  }}
                  disabled={isLoading}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
