'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { ProjectSettingsFormValues } from '@/lib/schemas/project-settings-schema';
import { AgentModeSettings } from '@/components/settings/agent-mode-settings';
import { SoundSettings } from '@/components/settings/sound-settings';

interface BasicInfoSectionProps {
  isLoading: boolean;
}

export function BasicInfoSection({ isLoading }: BasicInfoSectionProps) {
  const form = useFormContext<ProjectSettingsFormValues>();
  
  return (
    <>
      <Card className="border border-border/50 shadow-sm mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium">Project Information</CardTitle>
          <CardDescription>
            Basic information about your project
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="appName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter project name" 
                      disabled={isLoading} 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter project description" 
                      disabled={isLoading} 
                      {...field} 
                      className="min-h-[100px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="primaryColor"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Color</FormLabel>
                  <div className="flex gap-2 items-center">
                    <FormControl>
                      <Input 
                        type="color" 
                        disabled={isLoading} 
                        {...field} 
                        className="w-12 h-9 p-1"
                      />
                    </FormControl>
                    <Input 
                      value={field.value} 
                      onChange={field.onChange}
                      disabled={isLoading} 
                      className="flex-1"
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end mt-4">
              <Button 
                type="submit"
                size="sm"
                disabled={isLoading || !form.formState.isDirty}
                className="h-9 px-3 whitespace-nowrap"
              >
                <Check className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="border border-border/50 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium">AI Settings</CardTitle>
          <CardDescription>
            Configure AI assistant behavior and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col space-y-4">
              <AgentModeSettings className="py-2" />
              <SoundSettings className="py-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
