'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { toast } from 'sonner';

interface DownloadSectionProps {
  projectId: string;
}

export function DownloadSection({ projectId }: DownloadSectionProps) {
  const [downloading, setDownloading] = useState(false);
  
  const handleDownload = () => {
    setDownloading(true);
    fetch(`/api/projects/${projectId}/download`, {method: "POST"})
      .then(async response => {
        setDownloading(false);
        if (response.ok) {
          const blob = await response.blob();
          // Create download link
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `project-${projectId}.zip`;
          a.click();
          window.URL.revokeObjectURL(url);
          toast.success('Downloaded successfully');
        } else {
          console.log('Response', response);
          toast.error('Download failed');
        }
      })
      .catch(err => {
        setDownloading(false);
        console.log(err);
        toast.error('Download failed');
      });
  };

  return (
    <Card className="border border-border/50 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium">Download Project</CardTitle>
        <CardDescription>
          Download your project files as a ZIP archive
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Get a copy of your project files to use locally
          </p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleDownload}
            disabled={downloading}
            className="h-9 px-3 whitespace-nowrap"
          >
            <Download className="mr-2 h-4 w-4" />
            {downloading ? 'Downloading...' : 'Download ZIP'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
