'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from '@/components/ui/form';
import { ProjectSettingsFormValues } from '@/lib/schemas/project-settings-schema';

interface AppIdentifiersSectionProps {
  isLoading: boolean;
}

export function AppIdentifiersSection({ isLoading }: AppIdentifiersSectionProps) {
  const form = useFormContext<ProjectSettingsFormValues>();
  
  return (
    <Card className="border border-border/50 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium">App Identifiers</CardTitle>
        <CardDescription>
          Configure identifiers used for your mobile applications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="bundleIdentifier"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bundle Identifier (iOS)</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="com.company.appname" 
                    disabled={isLoading} 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Used for iOS app identification (e.g., com.company.appname)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="packageName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Package Name (Android)</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="com.company.appname" 
                    disabled={isLoading} 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Used for Android app identification (e.g., com.company.appname)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-end mt-4">
            <Button 
              type="submit"
              size="sm"
              disabled={isLoading || !form.formState.isDirty}
              className="h-9 px-3 whitespace-nowrap"
            >
              <Check className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
