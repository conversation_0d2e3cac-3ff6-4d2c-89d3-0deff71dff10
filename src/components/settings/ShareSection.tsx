'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Share2 } from 'lucide-react';

interface ShareSectionProps {
  chatId: string;
  visibility: 'public' | 'private';
  onShareClick: () => void;
}

export function ShareSection({ chatId, visibility, onShareClick }: ShareSectionProps) {
  return (
    <Card className="border border-border/50 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium">Share Project</CardTitle>
        <CardDescription>
          Share your project with others
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          Generate a shareable link to your project that you can send to others.
          {visibility === 'private' && (
            <span className="block mt-1 text-amber-600 text-xs">
              Note: This project is currently private. Make it public to allow others to view it.
            </span>
          )}
        </p>
      </CardContent>
      <CardFooter className="pt-0">
        <Button 
          onClick={onShareClick} 
          variant="outline" 
          size="sm" 
          className="h-9"
        >
          <Share2 className="mr-2 h-4 w-4" />
          Share Project
        </Button>
      </CardFooter>
    </Card>
  );
}
