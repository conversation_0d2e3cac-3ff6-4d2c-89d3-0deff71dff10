'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Globe } from 'lucide-react';

interface VisibilitySectionProps {
  chatId: string;
  isPublic: boolean;
  isLoading: boolean;
  onVisibilityChange: (isPublic: boolean) => void;
}

export function VisibilitySection({ chatId, isPublic, isLoading, onVisibilityChange }: VisibilitySectionProps) {
  const handleVisibilityChange = async (checked: boolean) => {
    try {
      onVisibilityChange(checked);
      
      await fetch(`/api/chat/${chatId}/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ visibility: checked ? 'public' : 'private' }),
      });
      
      toast.success(`Project is now ${checked ? 'public' : 'private'}`);
    } catch (error) {
      console.error('Failed to update visibility:', error);
      toast.error('Failed to update visibility');
      // Revert the UI state if the API call fails
      onVisibilityChange(!checked);
    }
  };

  return (
    <Card className="border border-border/50 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium">Project Visibility</CardTitle>
        <CardDescription>
          Control who can view your project and its deployments
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <div className="flex items-center">
              <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="font-medium text-sm">Public Project</span>
            </div>
            <p className="text-sm text-muted-foreground">
              When enabled, anyone with the link can view this project
            </p>
          </div>
          <Switch 
            id="public-switch" 
            checked={isPublic} 
            onCheckedChange={handleVisibilityChange} 
            disabled={isLoading}
          />
        </div>
      </CardContent>
    </Card>
  );
}
