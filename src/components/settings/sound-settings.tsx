'use client';

import { useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { VolumeIcon, Volume2Icon } from 'lucide-react';

interface SoundSettingsProps {
  className?: string;
}

export function SoundSettings({ className }: SoundSettingsProps) {
  const [playCompletionSound, setPlayCompletionSound] = useLocalStorage('play_completion_sound', true);
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before rendering to avoid hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-2 ${className || ''}`}>
      <div className="flex items-center gap-2 w-full">
        {playCompletionSound ? (
          <Volume2Icon size={16} className="text-foreground/70" />
        ) : (
          <VolumeIcon size={16} className="text-foreground/70" />
        )}
        <Label htmlFor="play-completion-sound" className="text-xs cursor-pointer">
          Play sound on AI completion
        </Label>
      </div>
      <Switch
        id="play-completion-sound"
        checked={playCompletionSound}
        onCheckedChange={setPlayCompletionSound}
        aria-label="Toggle completion sound"
      className="scale-75 data-[state=checked]:bg-primary"

      />
    </div>
  );
}

// Hook to access the sound settings from any component
export function useSoundSettings() {
  const [playCompletionSound, setPlayCompletionSound] = useLocalStorage('play_completion_sound', true);
  return { playCompletionSound, setPlayCompletionSound };
}
