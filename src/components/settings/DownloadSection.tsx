'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { toast } from 'sonner';

interface DownloadSectionProps {
  projectId: string;
}

export function DownloadSection({ projectId }: DownloadSectionProps) {
  const [downloading, setDownloading] = useState(false);

  const handleDownload = async () => {
    setDownloading(true);
    try {
      const response = await fetch(`/api/project/${[projectId]}/download`, {
        method: "POST"
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `project-${projectId}.zip`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('Download started');
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('Failed to download project:', error);
      toast.error('Failed to download project');
    } finally {
      setDownloading(false);
    }
  };

  return (
    <Card className="border border-border/50 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium">Download Project</CardTitle>
        <CardDescription>
          Download a copy of your project files
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-2">
          <p className="text-sm text-muted-foreground">
            Get a ZIP archive containing all the files generated for this project.
          </p>
          <Button 
            onClick={handleDownload} 
            variant="outline" 
            size="sm" 
            disabled={downloading}
            className="h-9 w-fit"
          >
            <Download className="mr-2 h-4 w-4" />
            {downloading ? 'Preparing download...' : 'Download ZIP'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
