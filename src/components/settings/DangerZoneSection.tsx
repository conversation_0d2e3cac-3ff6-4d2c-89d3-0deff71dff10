'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface DangerZoneSectionProps {
  chatId: string;
}

export function DangerZoneSection({ chatId }: DangerZoneSectionProps) {
  const handleDeleteProject = async () => {
    try {
      await fetch(`/api/chat/${chatId}/delete`, {
        method: 'DELETE',
      });
      
      toast.success('Project deleted successfully');
      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Failed to delete project:', error);
      toast.error('Failed to delete project');
    }
  };

  return (
    <Card className="border-destructive/20 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center text-destructive">
          <AlertTriangle className="mr-2 h-4 w-4" />
          Danger Zone
        </CardTitle>
        <CardDescription>
          Actions here can't be undone. Be careful!
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-3 border border-destructive/20 rounded-md bg-destructive/5">
          <h3 className="text-sm font-medium mb-1">Delete Project</h3>
          <p className="text-xs text-muted-foreground mb-3">
            Once you delete a project, there is no going back. This action cannot be undone.
          </p>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm" className="h-8">
                <Trash2 className="mr-2 h-3.5 w-3.5" />
                Delete Project
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete your
                  project and remove all associated data from our servers.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={handleDeleteProject}
                  className="bg-destructive hover:bg-destructive/90"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardContent>
    </Card>
  );
}
