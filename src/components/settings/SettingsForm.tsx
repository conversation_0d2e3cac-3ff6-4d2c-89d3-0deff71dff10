'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { projectSettingsSchema, ProjectSettingsFormValues } from '@/lib/schemas/project-settings-schema';
import { Form } from '@/components/ui/form';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

// Import the settings sections
import { BasicInfoSection } from '@/components/settings/form-sections/BasicInfoSection';
import { AppIdentifiersSection } from '@/components/settings/form-sections/AppIdentifiersSection';
import { VisibilitySection } from '@/components/settings/form-sections/VisibilitySection';
import { DangerZoneSection } from '@/components/settings/form-sections/DangerZoneSection';
import { DownloadSection } from '@/components/settings/form-sections/DownloadSection';
import {Project} from "@/lib/db/schema";

interface SettingsFormProps {
  projectId: string;
  initialData: Project;
  isLoading?: boolean;
}

export function SettingsForm({ projectId, initialData, isLoading = false }: SettingsFormProps) {
  const form = useForm<ProjectSettingsFormValues>({
    resolver: zodResolver(projectSettingsSchema),
    defaultValues: {
      appName: initialData.appName || '',
      description: initialData.description || '',
      primaryColor: initialData.primaryColor || '#000000',
      bundleIdentifier: initialData.bundleIdentifier || '',
      packageName: initialData.packageName || '',
      visibility: initialData.visibility || 'private',
    },
  });

  const onSubmit = async (data: ProjectSettingsFormValues) => {
    try {
      const response = await fetch(`/api/project/${projectId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update project settings');
      }
      
      toast.success('Project settings updated');
    } catch (error) {
      console.error('Failed to update project settings:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update project settings');
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="w-full grid grid-cols-3 h-9 mb-6">
            <TabsTrigger value="basic" className="text-xs">Basic Info</TabsTrigger>
            <TabsTrigger value="identifiers" className="text-xs">App Identifiers</TabsTrigger>
            <TabsTrigger value="danger" className="text-xs">Danger Zone</TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="space-y-6 mt-0">
            <BasicInfoSection isLoading={isLoading} />
            <Separator className="my-6" />
            <VisibilitySection isLoading={isLoading} />
            <Separator className="my-6" />
            <DownloadSection projectId={projectId} />
          </TabsContent>
          
          <TabsContent value="identifiers" className="space-y-6 mt-0">
            <AppIdentifiersSection isLoading={isLoading} />
          </TabsContent>
          
          <TabsContent value="danger" className="mt-0">
            <DangerZoneSection projectId={projectId} />
          </TabsContent>
        </Tabs>
      </form>
    </Form>
  );
}
