'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Check } from 'lucide-react';

interface ProjectNameSectionProps {
  chatId: string;
  initialProjectName: string;
  isLoading: boolean;
}

export function ProjectNameSection({ chatId, initialProjectName, isLoading }: ProjectNameSectionProps) {
  const [projectName, setProjectName] = useState(initialProjectName);
  const [isSaving, setIsSaving] = useState(false);
  
  const handleSaveProjectName = async () => {
    if (!projectName.trim()) {
      toast.error('Project name cannot be empty');
      return;
    }
    
    setIsSaving(true);
    try {
      await fetch(`/api/chat/${chatId}/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title: projectName }),
      });
      
      toast.success('Project name updated');
    } catch (error) {
      console.error('Failed to update project name:', error);
      toast.error('Failed to update project name');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="border border-border/50 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium">Project Name</CardTitle>
        <CardDescription>
          Used to identify your project on the dashboard, Vercel URL, and in the URL of your deployments.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-3">
          <Input
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
            placeholder="Enter project name"
            disabled={isLoading}
            className="h-9 flex-1"
          />
          <Button 
            onClick={handleSaveProjectName} 
            disabled={isLoading || isSaving || projectName === initialProjectName}
            size="sm"
            className="h-9 px-3 whitespace-nowrap"
          >
            <Check className="mr-2 h-4 w-4" />
            Save
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
