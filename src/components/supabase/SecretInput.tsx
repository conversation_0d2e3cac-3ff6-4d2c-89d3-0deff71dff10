import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Copy, Eye, EyeOff, Lock, Shield, AlertCircle } from 'lucide-react';

interface SecretInputProps {
  isOpen: boolean;
  onClose: () => void;
  secretNames: string[];
  onSubmit: (secrets: Record<string, string>) => Promise<void>;
  scope?: string;
  environment?: string;
}

export function SecretInput({ 
  isOpen, 
  onClose, 
  secretNames, 
  onSubmit,
  scope = 'project',
  environment = 'all'
}: SecretInputProps) {
  // Initialize state with empty strings for each secret name
  const [secrets, setSecrets] = useState<Record<string, string>>(() => 
    (secretNames || []).reduce((acc, name) => ({ ...acc, [name]: '' }), {})
  );
  const [visibleSecrets, setVisibleSecrets] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState<string | null>(null);

  // Reset secrets when secretNames change
  useEffect(() => {
    if (secretNames) {
      setSecrets(secretNames.reduce((acc, name) => ({ ...acc, [name]: '' }), {}));
      setVisibleSecrets(secretNames.reduce((acc, name) => ({ ...acc, [name]: false }), {}));
    }
  }, [secretNames]);

  if(!secretNames) {
    return null;
  }

  const handleInputChange = (name: string, value: string) => {
    setSecrets(prev => ({ ...prev, [name]: value }));
  };

  const toggleVisibility = (name: string) => {
    setVisibleSecrets(prev => ({ ...prev, [name]: !prev[name] }));
  };

  const copyToClipboard = (name: string) => {
    navigator.clipboard.writeText(secrets[name] || '');
    setCopied(name);
    setTimeout(() => setCopied(null), 2000);
  };

  const handleSubmit = async () => {
    try {
      // Validate all secrets are provided
      const missingSecrets = secretNames.filter(name => !secrets[name]);
      if (missingSecrets.length > 0) {
        setError(`Please provide values for all required secrets: ${missingSecrets.join(', ')}`);
        return;
      }
      
      setIsSubmitting(true);
      setError(null);
      await onSubmit(secrets);
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save secrets');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-w-[95vw] w-full">
        <DialogHeader>
          <DialogTitle>Enter Secret Values</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-start gap-2 text-sm text-gray-500">
              <Lock className="h-4 w-4 mt-0.5" />
              <p>
                These secrets will be deployed to your Supabase project
                {scope !== 'project' ? ` (scope: ${scope})` : ''} 
                {environment !== 'all' ? ` for the ${environment} environment` : ''}.
              </p>
            </div>
            
            <div className="flex items-start gap-2 text-sm text-gray-500 bg-blue-50 dark:bg-blue-950 p-3 rounded-md">
              <Shield className="h-4 w-4 mt-0.5 text-blue-600 dark:text-blue-400" />
              <p className="text-blue-700 dark:text-blue-300">
                Your credentials are securely stored in your Supabase project. magically does not store or have access to these values on our servers.
              </p>
            </div>
            
            {error && (
              <div className="bg-red-50 dark:bg-red-950 p-3 rounded-md flex items-start gap-2 text-sm">
                <AlertCircle className="h-4 w-4 mt-0.5 text-red-600 dark:text-red-400" />
                <p className="text-red-700 dark:text-red-300">{error}</p>
              </div>
            )}
          </div>
          {secretNames.map((name) => (
            <div key={name} className="flex flex-col gap-2 mb-6">
              <Label htmlFor={name} className="font-medium flex items-center">
                {name}
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <div className="relative w-full">
                <Input
                  id={name}
                  type={visibleSecrets[name] ? "text" : "password"}
                  value={secrets[name] || ''} // Ensure value is never undefined
                  onChange={(e) => handleInputChange(name, e.target.value)}
                  className="pr-20" // Add padding for the buttons
                  placeholder={`Enter ${name}`}
                  required
                />
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-1">
                  <button
                    type="button"
                    onClick={() => toggleVisibility(name)}
                    className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    aria-label={visibleSecrets[name] ? "Hide secret" : "Show secret"}
                  >
                    {visibleSecrets[name] ? (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-500" />
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => copyToClipboard(name)}
                    className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    aria-label="Copy to clipboard"
                    disabled={!secrets[name]}
                  >
                    <Copy className={`h-4 w-4 ${copied === name ? 'text-green-500' : 'text-gray-500'}`} />
                  </button>
                </div>
              </div>
              {!secrets[name] && (
                <p className="text-xs text-red-500">This field is required</p>
              )}
            </div>
          ))}
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            disabled={isSubmitting}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            onClick={handleSubmit}
            disabled={isSubmitting || Object.values(secrets).some(val => !val)}
            className="w-full sm:w-auto"
          >
            {isSubmitting ? 'Saving...' : 'Save Secrets'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
