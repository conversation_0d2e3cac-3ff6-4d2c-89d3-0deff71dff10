'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronRight, Apple, Upload, Eye, EyeOff, Info } from 'lucide-react';
import { BuildConfig } from '@/types/testflight';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

const setupSchema = z.object({
  appName: z.string().min(3, 'App name must be at least 3 characters'),
  bundleId: z.string().regex(/^[a-z][a-z0-9_]*(\.[a-z0-9_]+)+[0-9a-z_]$/i, 'Invalid bundle ID format'),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, 'Version must be in format x.y.z'),
  buildNumber: z.string().regex(/^\d+$/, 'Build number must be numeric')
});

const appleAccountSchema = z.object({
  appleUsername: z.string().email('Must be a valid email'),
  applePassword: z.string().min(1, 'Password is required'),
  appleTeamId: z.string().optional()
});

const expoAccountSchema = z.object({
  expoUsername: z.string().email('Must be a valid email'),
  expoPassword: z.string().min(1, 'Password is required')
});

interface SetupTabProps {
  buildConfig?: BuildConfig;
  isAppleAccountConnected: boolean;
  isExpoAccountConnected: boolean;
  appleUsername?: string;
  applePassword?: string;
  appleTeamId?: string;
  expoUsername?: string;
  expoPassword?: string;
  onConnectApple: (credentials: {
    appleUsername: string;
    applePassword: string;
    appleTeamId?: string;
  }) => void;
  onConnectExpo: (credentials: {
    expoUsername: string;
    expoPassword: string;
  }) => void;
  onContinue: (config: BuildConfig) => void;
  onCancel: () => void;
}

export const SetupTab: React.FC<SetupTabProps> = ({
  buildConfig,
  isAppleAccountConnected,
  isExpoAccountConnected,
  appleUsername = '',
  applePassword = '',
  appleTeamId = '',
  expoUsername = '',
  expoPassword = '',
  onConnectApple,
  onConnectExpo,
  onContinue,
  onCancel
}) => {
  const [showAppleForm, setShowAppleForm] = useState(false);
  const [showExpoForm, setShowExpoForm] = useState(false);
  const [showApplePassword, setShowApplePassword] = useState(false);
  const [showExpoPassword, setShowExpoPassword] = useState(false);
  
  const form = useForm<z.infer<typeof setupSchema>>({
    resolver: zodResolver(setupSchema),
    defaultValues: {
      appName: buildConfig?.appName || '',
      bundleId: buildConfig?.bundleId || '',
      version: buildConfig?.version || '1.0.0',
      buildNumber: buildConfig?.buildNumber || '1'
    }
  });
  
  const appleForm = useForm<z.infer<typeof appleAccountSchema>>({
    resolver: zodResolver(appleAccountSchema),
    defaultValues: {
      appleUsername: appleUsername,
      applePassword: applePassword,
      appleTeamId: appleTeamId
    }
  });
  
  const expoForm = useForm<z.infer<typeof expoAccountSchema>>({
    resolver: zodResolver(expoAccountSchema),
    defaultValues: {
      expoUsername: expoUsername,
      expoPassword: expoPassword
    }
  });

  const onSubmit = (data: z.infer<typeof setupSchema>) => {
    onContinue({
      ...buildConfig,
      ...data
    });
  };
  
  const onSubmitAppleAccount = (data: z.infer<typeof appleAccountSchema>) => {
    onConnectApple(data);
    setShowAppleForm(false);
  };
  
  const onSubmitExpoAccount = (data: z.infer<typeof expoAccountSchema>) => {
    onConnectExpo(data);
    setShowExpoForm(false);
  };

  return (
    <div className="space-y-4">
      <Card className="border-none shadow-none bg-muted/30 dark:bg-muted/10">
        <CardContent className="p-4 flex items-center gap-3">
          <div className="p-2 rounded-full bg-primary/10 dark:bg-primary/20">
            <Apple className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="text-sm font-medium">Apple Developer Account</h3>
            <p className="text-xs text-muted-foreground">Required for TestFlight distribution</p>
          </div>
          <Button 
            className="ml-auto" 
            size="sm" 
            variant={isAppleAccountConnected ? "outline" : "default"}
            onClick={() => setShowAppleForm(!showAppleForm)}
          >
            {isAppleAccountConnected ? 'Connected' : 'Connect'}
          </Button>
        </CardContent>
        {showAppleForm && (
          <CardContent className="p-4 pt-0 border-t border-border/50 mt-2">
            <Form {...appleForm}>
              <form onSubmit={appleForm.handleSubmit(onSubmitAppleAccount)} className="space-y-3">
                <FormField
                  control={appleForm.control}
                  name="appleUsername"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Apple ID Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} className="h-8 text-sm" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={appleForm.control}
                  name="applePassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">App-Specific Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input 
                            type={showApplePassword ? "text" : "password"} 
                            placeholder="••••••••" 
                            {...field} 
                            className="h-8 text-sm pr-10" 
                          />
                          <button 
                            type="button"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.preventDefault();
                              setShowApplePassword(!showApplePassword);
                            }}
                          >
                            {showApplePassword ? 
                              <EyeOff className="h-4 w-4" /> : 
                              <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={appleForm.control}
                  name="appleTeamId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Team ID (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="ABCDE12345" {...field} className="h-8 text-sm" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                
                <div className="pt-1">
                  <div className="flex items-start gap-2 mb-3 p-2 bg-amber-50 dark:bg-amber-950/20 rounded-md">
                    <Info className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-amber-700 dark:text-amber-300">
                      Your credentials are stored only in your browser's local storage and are not sent to any server unless you initiate a build.
                    </p>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setShowAppleForm(false)} 
                      className="h-8 text-xs"
                    >
                      Cancel
                    </Button>
                    <Button type="submit" size="sm" className="h-8 text-xs">
                      Connect Account
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </CardContent>
        )}
      </Card>
      
      <Card className="border-none shadow-none bg-muted/30 dark:bg-muted/10">
        <CardContent className="p-4 flex items-center gap-3">
          <div className="p-2 rounded-full bg-primary/10 dark:bg-primary/20">
            <svg className="h-5 w-5 text-primary" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium">Expo Account</h3>
            <p className="text-xs text-muted-foreground">Required for EAS Build service</p>
          </div>
          <Button 
            className="ml-auto" 
            size="sm" 
            variant={isExpoAccountConnected ? "outline" : "default"}
            onClick={() => setShowExpoForm(!showExpoForm)}
          >
            {isExpoAccountConnected ? 'Connected' : 'Connect'}
          </Button>
        </CardContent>
        {showExpoForm && (
          <CardContent className="p-4 pt-0 border-t border-border/50 mt-2">
            <Form {...expoForm}>
              <form onSubmit={expoForm.handleSubmit(onSubmitExpoAccount)} className="space-y-3">
                <FormField
                  control={expoForm.control}
                  name="expoUsername"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Expo Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} className="h-8 text-sm" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={expoForm.control}
                  name="expoPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Expo Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input 
                            type={showExpoPassword ? "text" : "password"} 
                            placeholder="••••••••" 
                            {...field} 
                            className="h-8 text-sm pr-10" 
                          />
                          <button 
                            type="button"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            onClick={(e) => {
                              e.preventDefault();
                              setShowExpoPassword(!showExpoPassword);
                            }}
                          >
                            {showExpoPassword ? 
                              <EyeOff className="h-4 w-4" /> : 
                              <Eye className="h-4 w-4" />}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                
                <div className="pt-1">
                  <div className="flex items-start gap-2 mb-3 p-2 bg-amber-50 dark:bg-amber-950/20 rounded-md">
                    <Info className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-amber-700 dark:text-amber-300">
                      Your credentials are stored only in your browser's local storage and are not sent to any server unless you initiate a build.
                    </p>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setShowExpoForm(false)} 
                      className="h-8 text-xs"
                    >
                      Cancel
                    </Button>
                    <Button type="submit" size="sm" className="h-8 text-xs">
                      Connect Account
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </CardContent>
        )}
      </Card>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <Card className="border-none shadow-none bg-muted/30 dark:bg-muted/10">
            <CardContent className="p-4 space-y-4">
              <h3 className="text-sm font-medium">App Configuration</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="appName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">App Name</FormLabel>
                      <FormControl>
                        <Input placeholder="My App" {...field} className="h-8 text-sm" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="bundleId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Bundle ID</FormLabel>
                      <FormControl>
                        <Input placeholder="com.example.myapp" {...field} className="h-8 text-sm" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="version"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Version</FormLabel>
                      <FormControl>
                        <Input placeholder="1.0.0" {...field} className="h-8 text-sm" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="buildNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Build Number</FormLabel>
                      <FormControl>
                        <Input placeholder="1" {...field} className="h-8 text-sm" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
              </div>
              
              <div>
                <Label className="text-xs">App Icon</Label>
                <div className="mt-1 flex items-center gap-3">
                  <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center">
                    {buildConfig?.icon ? (
                      <img src={buildConfig.icon} alt="App Icon" className="w-full h-full object-cover rounded-md" />
                    ) : (
                      <div className="text-muted-foreground text-xs">Icon</div>
                    )}
                  </div>
                  <Button size="sm" variant="outline" className="h-8 text-xs">
                    <Upload className="h-3 w-3 mr-1" />
                    Upload
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" size="sm" onClick={onCancel} className="h-8 text-xs">
              Cancel
            </Button>
            <Button 
              type="submit" 
              size="sm" 
              className="h-8 text-xs"
              disabled={!isAppleAccountConnected || !isExpoAccountConnected}
            >
              Continue <ChevronRight className="h-3 w-3 ml-1" />
            </Button>
          </div>
          {(!isAppleAccountConnected || !isExpoAccountConnected) && (
            <p className="text-xs text-amber-500 mt-2">
              {!isAppleAccountConnected && !isExpoAccountConnected
                ? 'Please connect both Apple and Expo accounts to continue'
                : !isAppleAccountConnected
                ? 'Please connect your Apple account to continue'
                : 'Please connect your Expo account to continue'}
            </p>
          )}
        </form>
      </Form>
    </div>
  );
};
