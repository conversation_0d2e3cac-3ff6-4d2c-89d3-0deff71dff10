'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle2, QrCode, Users, Copy, ExternalLink } from 'lucide-react';
import { TestFlightInvite } from '@/types/testflight';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

const inviteSchema = z.object({
  email: z.string().email('Please enter a valid email'),
  name: z.string().optional(),
});

interface InviteTabProps {
  testFlightLink?: string;
  invites: TestFlightInvite[];
  onAddInvite: (email: string, name?: string) => Promise<void>;
  onSendAllInvites?: () => Promise<void>;
  onRemoveInvite?: (email: string) => void;
  onClose: () => void;
}

export const InviteTab: React.FC<InviteTabProps> = ({
  testFlightLink,
  invites,
  onAddInvite,
  onSendAllInvites,
  onRemoveInvite,
  onClose
}) => {
  const [copied, setCopied] = useState(false);
  const [isAddingInvite, setIsAddingInvite] = useState(false);
  const [isSendingAll, setIsSendingAll] = useState(false);
  const [qrVisible, setQrVisible] = useState(false);
  
  const form = useForm<z.infer<typeof inviteSchema>>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: '',
      name: '',
    }
  });

  const onSubmit = async (data: z.infer<typeof inviteSchema>) => {
    try {
      setIsAddingInvite(true);
      await onAddInvite(data.email, data.name);
      form.reset();
    } catch (error) {
      console.error('Failed to add invite:', error);
    } finally {
      setIsAddingInvite(false);
    }
  };

  const handleCopyLink = useCallback(() => {
    if (testFlightLink) {
      navigator.clipboard.writeText(testFlightLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  }, [testFlightLink]);
  
  const handleSendAllInvites = useCallback(async () => {
    if (onSendAllInvites) {
      try {
        setIsSendingAll(true);
        await onSendAllInvites();
      } catch (error) {
        console.error('Failed to send all invites:', error);
      } finally {
        setIsSendingAll(false);
      }
    }
  }, [onSendAllInvites]);
  
  const handleGenerateQR = useCallback(() => {
    setQrVisible(!qrVisible);
  }, [qrVisible]);

  return (
    <div className="space-y-4">
      <Card className="border-none shadow-none bg-green-500/10 dark:bg-green-500/5">
        <CardContent className="p-4 space-y-3 text-center">
          <div className="mx-auto bg-green-500/20 w-10 h-10 rounded-full flex items-center justify-center">
            <CheckCircle2 className="h-5 w-5 text-green-500" />
          </div>
          
          <h3 className="text-sm font-medium">Successfully Published!</h3>
          <p className="text-xs text-muted-foreground">Your app is now available on TestFlight</p>
          
          <div className="flex items-center gap-1 p-2 bg-background dark:bg-muted/10 rounded-md border border-border/50">
            <Input 
              value={testFlightLink || ''} 
              readOnly 
              className="flex-1 border-none text-xs h-7 focus-visible:ring-0 bg-transparent"
            />
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={handleCopyLink}
              className="h-7 px-2"
            >
              {copied ? <CheckCircle2 className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-2 gap-3">
        <Card className="border-none shadow-none bg-muted/30 dark:bg-muted/10">
          <CardContent className="p-3 space-y-2">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-full bg-primary/10 dark:bg-primary/20">
                <QrCode className="h-3.5 w-3.5 text-primary" />
              </div>
              <h4 className="text-xs font-medium">Share QR Code</h4>
            </div>
            <p className="text-[10px] text-muted-foreground">Generate a QR code for testers to scan</p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full h-7 text-xs"
              onClick={handleGenerateQR}
            >
              {qrVisible ? 'Hide QR' : 'Generate QR'}
            </Button>
            {qrVisible && testFlightLink && (
              <div className="mt-2 p-2 bg-white rounded-md flex justify-center">
                <img 
                  src={`https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(testFlightLink)}`} 
                  alt="TestFlight QR Code" 
                  className="w-full max-w-[100px] h-auto" 
                />
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card className="border-none shadow-none bg-muted/30 dark:bg-muted/10">
          <CardContent className="p-3 space-y-2">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-full bg-primary/10 dark:bg-primary/20">
                <Users className="h-3.5 w-3.5 text-primary" />
              </div>
              <h4 className="text-xs font-medium">Email Invites</h4>
            </div>
            <p className="text-[10px] text-muted-foreground">Send invites directly to testers</p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full h-7 text-xs"
              onClick={handleSendAllInvites}
              disabled={isSendingAll || invites.length === 0 || invites.every(invite => invite.sent)}
            >
              {isSendingAll ? 'Sending...' : 'Send All Invites'}
            </Button>
          </CardContent>
        </Card>
      </div>
      
      <Card className="border-none shadow-none bg-muted/30 dark:bg-muted/10">
        <CardContent className="p-4 space-y-3">
          <h3 className="text-sm font-medium">Invite Testers</h3>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
              <div className="grid grid-cols-3 gap-2">
                <div className="col-span-2">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input placeholder="Email address" {...field} className="h-8 text-xs" />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="col-span-1">
                  <Button 
                    type="submit" 
                    size="sm" 
                    className="w-full h-8 text-xs"
                    disabled={isAddingInvite}
                  >
                    {isAddingInvite ? 'Adding...' : 'Add Tester'}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
          
          {invites.length > 0 && (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs font-medium">Email</TableHead>
                    <TableHead className="text-xs font-medium text-right">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invites.map((invite, index) => (
                    <TableRow key={index}>
                      <TableCell className="text-xs py-2">{invite.email}</TableCell>
                      <TableCell className="text-xs py-2 text-right flex items-center justify-end gap-2">
                        {invite.sent ? (
                          invite.status === 'accepted' ? (
                            <Badge variant="outline" className="bg-green-500/10 text-green-500 text-[10px] h-5">
                              Accepted
                            </Badge>
                          ) : invite.status === 'declined' ? (
                            <Badge variant="outline" className="bg-destructive/10 text-destructive text-[10px] h-5">
                              Declined
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-[10px] h-5">
                              Pending
                            </Badge>
                          )
                        ) : (
                          <>
                            <Badge variant="outline" className="bg-amber-500/10 text-amber-500 text-[10px] h-5">
                              Not Sent
                            </Badge>
                            {onRemoveInvite && (
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-5 w-5" 
                                onClick={() => onRemoveInvite(invite.email)}
                              >
                                <svg 
                                  xmlns="http://www.w3.org/2000/svg" 
                                  width="12" 
                                  height="12" 
                                  viewBox="0 0 24 24" 
                                  fill="none" 
                                  stroke="currentColor" 
                                  strokeWidth="2" 
                                  strokeLinecap="round" 
                                  strokeLinejoin="round"
                                >
                                  <path d="M18 6L6 18" />
                                  <path d="M6 6l12 12" />
                                </svg>
                              </Button>
                            )}
                          </>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      
      <div className="flex justify-end gap-2">
        <Button variant="outline" size="sm" onClick={onClose} className="h-8 text-xs">
          Close
        </Button>
        <Button size="sm" onClick={() => window.open(testFlightLink || '', '_blank')} className="h-8 text-xs">
          Open TestFlight
          <ExternalLink className="h-3 w-3 ml-1" />
        </Button>
      </div>
    </div>
  );
};
