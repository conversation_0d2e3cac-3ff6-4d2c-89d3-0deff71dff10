'use client';

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import rootStore from '@/stores/RootStore';
import { Terminal } from '../terminal/Terminal';
import { AutomatedTerminal } from '../terminal/AutomatedTerminal';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, CheckCircle2, Circle, RefreshCw, ChevronRight } from 'lucide-react';
import { BuildProgress, BuildStatus } from '@/types/testflight';

interface PublishStep {
  id: string;
  title: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  icon: React.ReactNode;
}

interface PublishTabProps {
  chatId: string;
}

export const PublishTab: React.FC<PublishTabProps> = observer(({ chatId }) => {
  const { testFlightStore } = rootStore;
  const buildProgress = testFlightStore.getBuildProgress(chatId);
  const [activeTab, setActiveTab] = useState<'logs' | 'terminal'>('logs');
  const [showAutomationHistory, setShowAutomationHistory] = useState(false);

  const getSteps = (): PublishStep[] => {
    const steps: PublishStep[] = [
      {
        id: 'expo-login',
        title: 'Connect Expo Account',
        status: 'pending',
        icon: <Circle className="h-4 w-4" />
      },
      {
        id: 'expo-config',
        title: 'Configure App Settings',
        status: 'pending',
        icon: <Circle className="h-4 w-4" />
      },
      {
        id: 'apple-login',
        title: 'Connect Apple Developer Account',
        status: 'pending',
        icon: <Circle className="h-4 w-4" />
      },
      {
        id: 'build-app',
        title: 'Build iOS App',
        status: 'pending',
        icon: <Circle className="h-4 w-4" />
      },
      {
        id: 'submit-testflight',
        title: 'Submit to TestFlight',
        status: 'pending',
        icon: <Circle className="h-4 w-4" />
      }
    ];

    if (buildProgress) {
      const { status } = buildProgress;

      // Update steps based on current status
      return steps.map(step => {
        let stepStatus: 'pending' | 'active' | 'completed' | 'error' = 'pending';

        // Determine step status based on build progress
        if (step.id === 'expo-login' || step.id === 'expo-config') {
          stepStatus = status === 'idle' ? 'pending' :
                      (status === 'configuring' ? 'active' : 'completed');
        } else if (step.id === 'apple-login') {
          stepStatus = status === 'idle' || status === 'configuring' ? 'pending' :
                      (status === 'preparing' ? 'active' : 'completed');
        } else if (step.id === 'build-app') {
          stepStatus = status === 'idle' || status === 'configuring' || status === 'preparing' ? 'pending' :
                      (status === 'building' ? 'active' : 'completed');
        } else if (step.id === 'submit-testflight') {
          stepStatus = status === 'idle' || status === 'configuring' || status === 'preparing' || status === 'building' ? 'pending' :
                      (status === 'submitting' ? 'active' : 'completed');
        }

        // If build failed, mark all active steps as error
        if (status === 'failed' && stepStatus === 'active') {
          stepStatus = 'error';
        }

        return {
          ...step,
          status: stepStatus,
          icon: stepStatus === 'active'
            ? <Loader2 className="h-4 w-4 animate-spin text-primary" />
            : stepStatus === 'completed'
              ? <CheckCircle2 className="h-4 w-4 text-green-500" />
              : stepStatus === 'error'
                ? <Circle className="h-4 w-4 text-destructive" />
                : <Circle className="h-4 w-4" />
        };
      });
    }

    return steps;
  };

  const steps = getSteps();
  const isPublishing = buildProgress &&
    ['configuring', 'preparing', 'building', 'submitting'].includes(buildProgress.status);

  // Handler functions
  const handleStartPublishing = () => {
    console.log('Start Publishing clicked', { chatId });
    try {
      testFlightStore.startBuild(chatId);
      console.log('Build started successfully');
    } catch (error) {
      console.error('Error starting build:', error);
    }
  };

  const handleReset = () => {
    testFlightStore.resetBuild(chatId);
  };

  const handleCancelBuild = () => {
    testFlightStore.cancelBuild(chatId);
  };

  const handleGoToInvite = () => {
    // Navigate to invite screen
    // This would be implemented based on your app's navigation system
  };

  const handleGoBack = () => {
    // Navigate back
    // This would be implemented based on your app's navigation system
  };

  return (
    <div className="space-y-4">
      {/* Tabs for Logs and Terminal */}
      <Tabs defaultValue="logs" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="logs" onClick={() => setActiveTab('logs')}>Build Logs</TabsTrigger>
          <TabsTrigger value="terminal" onClick={() => setActiveTab('terminal')}>Terminal</TabsTrigger>
        </TabsList>

        <TabsContent value="logs">
          <Card className="border-none shadow-none bg-muted/30 dark:bg-muted/10">
            <CardContent className="p-4 space-y-4">
          <h3 className="text-sm font-medium">Publishing Process</h3>

          <div className="space-y-2">
            {steps.map((step) => (
              <div
                key={step.id}
                className={`flex items-center gap-2 p-2 rounded-md text-xs transition-colors ${
                  step.status === 'active' ? 'bg-primary/10 dark:bg-primary/20' : 
                  step.status === 'completed' ? 'bg-green-500/10 dark:bg-green-500/20' : 
                  step.status === 'error' ? 'bg-destructive/10 dark:bg-destructive/20' :
                  'bg-background dark:bg-muted/5'
                }`}
              >
                {step.icon}
                <span className="font-medium">{step.title}</span>
                {step.status === 'active' && (
                  <Badge variant="outline" className="ml-auto text-[10px] py-0 h-5">
                    Processing
                  </Badge>
                )}
                {step.status === 'completed' && (
                  <Badge className="ml-auto bg-green-500 text-[10px] py-0 h-5">
                    Done
                  </Badge>
                )}
                {step.status === 'error' && (
                  <Badge variant="destructive" className="ml-auto text-[10px] py-0 h-5">
                    Failed
                  </Badge>
                )}
              </div>
            ))}
          </div>

          {buildProgress && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>Progress</span>
                <span>{Math.round(buildProgress.progress)}%</span>
              </div>
              <Progress value={buildProgress.progress} className="h-1.5" />

              {/* Build logs */}
              {buildProgress.logs && buildProgress.logs.length > 0 && (
                <Card className="border border-border/50 mt-3">
                  <ScrollArea className="h-20 rounded-md">
                    <div className="p-2 text-[10px] font-mono">
                      {buildProgress.logs.map((log: { type: string; timestamp: number; message: string }, index: number) => (
                        <div
                          key={index}
                          className={`mb-1 ${
                            log.type === 'error' ? 'text-destructive' : 
                            log.type === 'warning' ? 'text-amber-500' : 
                            log.type === 'success' ? 'text-green-500' : 
                            'text-muted-foreground'
                          }`}
                        >
                          [{new Date(log.timestamp).toLocaleTimeString()}] {log.message}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </Card>
              )}
            </div>
          )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="terminal" className="h-[400px]">
          <div className="h-full flex flex-col">
            <div className="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-800 rounded-t-md">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Automation</span>
                <Switch
                  checked={testFlightStore.isTerminalAutomationEnabled}
                  onCheckedChange={(checked) => testFlightStore.setTerminalAutomationEnabled(checked)}
                />
                <button
                  className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                  onClick={() => setShowAutomationHistory(!showAutomationHistory)}
                >
                  {showAutomationHistory ? 'Hide History' : 'Show History'}
                </button>
              </div>
              <div className="text-xs text-gray-500">
                {testFlightStore.isTerminalAutomationEnabled ? 'Automation Enabled' : 'Manual Mode'}
              </div>
            </div>

            {showAutomationHistory && testFlightStore.terminalAutomationHistory.length > 0 && (
              <div className="p-2 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 max-h-32 overflow-y-auto">
                <h4 className="text-xs font-medium mb-1">Automation History</h4>
                <ul className="text-xs space-y-1">
                  {testFlightStore.terminalAutomationHistory.map((entry: { pattern: { description: string }; timestamp: number; response: string }, i: number) => (
                    <li key={i} className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        {entry.pattern.description || 'Automated response'}
                      </span>
                      <span className="text-gray-500 dark:text-gray-500">
                        {new Date(entry.timestamp).toLocaleTimeString()}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="flex-grow">
              <AutomatedTerminal
                sessionId={testFlightStore.terminalSessionId || ''}
                automationEnabled={testFlightStore.isTerminalAutomationEnabled}
                onPromptAutomated={(pattern, match, response) => {
                  // Handle automated prompt response
                  console.log('Automated response:', response);
                }}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-2">
        {buildProgress ? (
          <>
            {buildProgress.status === 'idle' && (
              <>
                <Button variant="outline" size="sm" onClick={handleGoBack} className="h-8 text-xs">
                  Back
                </Button>
                <Button size="sm" onClick={handleStartPublishing} className="h-8 text-xs">
                  Start Publishing
                </Button>
              </>
            )}

            {isPublishing && (
              <>
                <Button variant="outline" size="sm" onClick={handleCancelBuild} className="h-8 text-xs">
                  Cancel
                </Button>
                <Button disabled size="sm" className="h-8 text-xs">
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  {buildProgress.message || 'Publishing...'}
                </Button>
              </>
            )}

            {buildProgress.status === 'completed' && buildProgress.testFlightLink && (
              <>
                <Button variant="outline" size="sm" onClick={handleReset} className="h-8 text-xs">
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Restart
                </Button>
                <Button size="sm" onClick={handleGoToInvite} className="h-8 text-xs">
                  Invite Testers
                  <ChevronRight className="h-3 w-3 ml-1" />
                </Button>
              </>
            )}

            {buildProgress.status === 'failed' && (
              <>
                <Button variant="outline" size="sm" onClick={handleReset} className="h-8 text-xs">
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Reset
                </Button>
                <Button size="sm" onClick={handleStartPublishing} className="h-8 text-xs">
                  Try Again
                </Button>
              </>
            )}
          </>
        ) : (
          <>
            <Button variant="outline" size="sm" onClick={handleGoBack} className="h-8 text-xs">
              Back
            </Button>
            <Button size="sm" onClick={handleStartPublishing} className="h-8 text-xs">
              Start Publishing
            </Button>
          </>
        )}
      </div>
    </div>
  );
});
