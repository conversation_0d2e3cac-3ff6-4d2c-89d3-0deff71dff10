'use client';

import React, { memo } from 'react';
import { observer } from 'mobx-react-lite';
import { Dialog } from '@/components/ui/dialog';
import { TestFlightPublisher } from './TestFlightPublisher';

interface TestFlightButtonProps {
  chatId: string;
}

// Separate component for the TestFlight button
// This is wrapped in memo and observer to prevent unnecessary re-renders
const TestFlightButtonBase: React.FC<TestFlightButtonProps> = ({ chatId }) => {
  // Using Dialog as a container to prevent re-renders of the parent component
  return (
    <Dialog>
      <TestFlightPublisher chatId={chatId} />
    </Dialog>
  );
};

// Export a memoized version to prevent re-renders
export const TestFlightButton = memo(observer(TestFlightButtonBase));
