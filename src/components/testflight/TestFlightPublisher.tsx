'use client';

import React, { useState, useCallback, memo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { TestFlightInvite } from '@/types/testflight';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Rocket, Users, Settings } from 'lucide-react';
import { SetupTab } from './SetupTab';
import { PublishTab } from './PublishTab';
import { InviteTab } from './InviteTab';

interface TestFlightPublisherProps {
  chatId: string;
}

// Main component
const TestFlightPublisherBase: React.FC<TestFlightPublisherProps> = ({ chatId }) => {
  const { generatorStore, testFlightStore } = useStores();
  const session = generatorStore.getActiveSession(chatId);
  const [isOpen, setIsOpen] = useState(false);
  const [currentTab, setCurrentTab] = useState('setup');
  
  // Initialize build config and load credentials when dialog opens
  useEffect(() => {
    if (isOpen) {
      // Initialize build config if not already initialized
      if (!testFlightStore.getBuildConfig(chatId)) {
        testFlightStore.initBuildConfig(chatId);
      }
      
      // Load credentials from localStorage
      testFlightStore.loadCredentials();
    }
  }, [isOpen, chatId, testFlightStore]);
  
  // Start the build process using the TestFlightStore
  const startPublishing = useCallback(() => {
    if (!session) return;
    setCurrentTab('publish');
    testFlightStore.startBuild(chatId);
  }, [session, chatId, testFlightStore]);
  
  // Get build progress from store
  const buildProgress = testFlightStore.getBuildProgress(chatId);
  const buildConfig = testFlightStore.getBuildConfig(chatId);
  const invites = testFlightStore.getInvites(chatId) || [];
  
  // Reset the process
  const resetProcess = useCallback(() => {
    testFlightStore.resetBuild(chatId);
  }, [chatId, testFlightStore]);
  
  // Add a new invite
  const addInvite = useCallback(async (email: string, name?: string) => {
    return testFlightStore.addInvite(chatId, email, name);
  }, [chatId, testFlightStore]);
  
  // Send all pending invites
  const sendAllInvites = useCallback(async () => {
    const pendingInvites = invites.filter(invite => !invite.sent);
    for (const invite of pendingInvites) {
      await testFlightStore.addInvite(chatId, invite.email, invite.name);
    }
  }, [chatId, invites, testFlightStore]);
  
  // Remove an invite
  const removeInvite = useCallback((email: string) => {
    testFlightStore.removeInvite(chatId, email);
  }, [chatId, testFlightStore]);
  
  // Check if we should update the tab based on build progress
  useEffect(() => {
    if (buildProgress?.status === 'completed' && buildProgress.testFlightLink) {
      setCurrentTab('invite');
    }
  }, [buildProgress]);
  
  if (!session) return null;
  
  const isPublishing = buildProgress && 
    ['configuring', 'preparing', 'building', 'submitting'].includes(buildProgress.status);
  
  return (
    <>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm"
          className="text-primary"
          onClick={() => setIsOpen(true)}
        >
          <Rocket className="h-4 w-4 mr-1" />
          TestFlight
        </Button>
      </DialogTrigger>
      
      <Dialog open={isOpen} onOpenChange={(open) => {
        setIsOpen(open);
        if (open && !testFlightStore.getBuildConfig(chatId)) {
          testFlightStore.initBuildConfig(chatId);
        }
      }}>
        <DialogContent className="sm:max-w-[500px] p-0 overflow-hidden">
          <DialogHeader className="p-4 pb-2">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-full bg-primary/10 dark:bg-primary/20">
                <Rocket className="h-4 w-4 text-primary" />
              </div>
              <DialogTitle className="text-base">Publish to TestFlight</DialogTitle>
            </div>
            <DialogDescription className="text-xs">
              Share your app with testers via Apple TestFlight
            </DialogDescription>
          </DialogHeader>
          
          <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
            <div className="px-4">
              <TabsList className="w-full grid grid-cols-3 h-9">
                <TabsTrigger value="setup" disabled={isPublishing} className="text-xs py-1.5">
                  <Settings className="h-3 w-3 mr-1" />
                  Setup
                </TabsTrigger>
                <TabsTrigger value="publish" disabled={isPublishing && (buildProgress?.progress || 0) < 100} className="text-xs py-1.5">
                  <Rocket className="h-3 w-3 mr-1" />
                  Publish
                </TabsTrigger>
                <TabsTrigger value="invite" disabled={!buildProgress?.testFlightLink} className="text-xs py-1.5">
                  <Users className="h-3 w-3 mr-1" />
                  Invite
                </TabsTrigger>
              </TabsList>
            </div>
            
            <div className="mt-3 px-4 pb-4">
              <TabsContent value="setup" className="m-0">
                <SetupTab 
                  buildConfig={buildConfig}
                  isAppleAccountConnected={testFlightStore.isAppleAccountConnected}
                  isExpoAccountConnected={testFlightStore.isExpoAccountConnected}
                  appleUsername={testFlightStore.appleUsername}
                  applePassword={testFlightStore.applePassword}
                  appleTeamId={testFlightStore.appleTeamId}
                  expoUsername={testFlightStore.expoUsername}
                  expoPassword={testFlightStore.expoPassword}
                  onConnectApple={(credentials) => {
                    testFlightStore.setAppleCredentials(
                      credentials.appleUsername,
                      credentials.applePassword,
                      credentials.appleTeamId
                    );
                  }}
                  onConnectExpo={(credentials) => {
                    testFlightStore.setExpoCredentials(
                      credentials.expoUsername,
                      credentials.expoPassword
                    );
                  }}
                  onContinue={(config) => {
                    testFlightStore.updateBuildConfig(chatId, config);
                    setCurrentTab('publish');
                  }}
                  onCancel={() => setIsOpen(false)}
                />
              </TabsContent>
              
              <TabsContent value="publish" className="m-0">
                <PublishTab 
                  chatId={chatId}
                />
              </TabsContent>
              
              <TabsContent value="invite" className="m-0">
                <InviteTab 
                  testFlightLink={buildProgress?.testFlightLink}
                  invites={invites}
                  onAddInvite={addInvite}
                  onSendAllInvites={sendAllInvites}
                  onRemoveInvite={removeInvite}
                  onClose={() => setIsOpen(false)}
                />
              </TabsContent>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const TestFlightPublisher = memo(observer(TestFlightPublisherBase));
