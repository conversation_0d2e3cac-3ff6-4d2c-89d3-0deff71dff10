'use client';

import type {Message} from 'ai';
import {useChat} from '@ai-sdk/react';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {UIMessage} from '@ai-sdk/ui-utils';
import {observer} from 'mobx-react-lite';
import {useStores} from '@/stores/utils/useStores';
import {Messages} from '@/components/base/messages';
import {MultimodalInput} from '@/components/base/multimodal-input';
import {DesignScreenHandler} from "@/components/design/DesignScreenHandler";
import {useUserMessageId} from "@/hooks/use-user-message-id";
import {useAIMessageId} from "@/hooks/use-ai-message-id";
import {Button} from "@/components/ui/button";
import {ArrowRight} from "lucide-react";
import ApprovalBar from "@/components/design/ApprovalBar";
import * as Sentry from "@sentry/nextjs";
import {trackMessageEvent} from "@/lib/analytics/track";
import {toast} from "sonner";

interface DesignChatProps {
    id: string;
    initialMessages?: Array<UIMessage>;
    projectId: string;
    onScreenUpdate: (update: DesignScreenUpdate) => void;
    runLastUserMessage: boolean;
    onInitialRunInitialized: () => void;
}

export interface DesignScreenUpdate {
    name: string,
    screenId: string,
    status: 'starting' | 'generating' | 'complete' | 'error'
    order: number
}

type DataStreamDelta = {
    type:
        | 'text-delta'
        | 'code-delta'
        | 'title'
        | 'id'
        | 'suggestion'
        | 'clear'
        | 'finish'
        | 'user-message-id'
        | 'ai-message-id'
        | 'design-screen-update'
    content: string | DesignScreenUpdate
};


const DesignChat = observer(({
                                 id,
                                 initialMessages = [],
                                 projectId,
                                 onScreenUpdate,
                                 runLastUserMessage,
                                 onInitialRunInitialized
                             }: DesignChatProps) => {
    const {designPreviewStore, generatorStore, designStore} = useStores();
    const [attachments, setAttachments] = useState<any[]>([]);
    const [droppedFiles, setDroppedFiles] = useState<any[]>([]);
    const [hasMoreLocalOverride, setHasMoreLocalOverride] = useState<boolean | undefined>(undefined);
    const [isLoadingPrevious, setIsLoadingPrevious] = useState(false);
    const [selectMode, setSelectMode] = useState(false);
    const [localComponentContexts, setLocalComponentContexts] = useState<any[]>([]);
    const {setUserMessageIdFromServer} = useUserMessageId();
    const {setAIMessageIdFromServer} = useAIMessageId();
    const lastProcessedIndex = useRef(-1);
    const [lastRunInitialized, setLastRunInitialized] = useState(false);

    // Check if we're on mobile
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
    
    // Ref for the messages container to implement scroll to bottom
    const messagesContainerRef = useRef<HTMLDivElement>(null);

    // Load screens when component mounts
    useEffect(() => {
        // Ensure screens are loaded for this chat
        if (id) {
            designStore.loadScreens(id);
        }
    }, [id, designStore]);

    // Use the AI SDK's useChat hook with the design chat API route
    const {
        messages,
        input,
        setInput,
        handleSubmit,
        isLoading,
        data: dataStream,
        reload,
        append,
        stop,
        setMessages,
        status
    } = useChat({
        id,
        initialMessages,
        api: `/api/project/${projectId}/design/${id}`,
        streamProtocol: "data",
        keepLastMessageOnError: false,
        body: {
            projectId,
            chatId: id
        },
        onFinish: () => {
            // Handle chat completion if needed
        },
        onError: (error) => {
            Sentry.withScope(function (scope) {
                scope.setTag("action", "chat");
                scope.setLevel("error");
                Sentry.captureException(error);
            });

            // Track message error
            trackMessageEvent('ERROR', {
                chat_id: id,
                error_type: error.message?.includes("limit reached") ? 'rate_limit' : 'server_error',
                error_message: error.message || 'Unknown error'
            });

            try {
                let draft = '';
                // const lastUserMessageIndex = messages.findLastIndex(message => message.role === "user");
                // setMessages(messages => {
                //     draft = messages[lastUserMessageIndex].content;
                //     messages = messages.slice(0, lastUserMessageIndex);
                //     return messages;
                // });
                toast.error("There was an error with our LLM provider while processing your last message. You will not be charged for it.");
                setInput(draft)
                const errorData = JSON.parse(error.message);
                // Regardless of the limit reached, we want to first show the upgrade dialog to anonymous users and upgrade dialog once they are logged in
                if (errorData.error?.includes("limit reached")) {
                    if (errorData.isAnonymous) {
                        generatorStore.toggleLoginDialog(true);
                    } else {
                        generatorStore.setUsageLimit(
                            errorData.limit,
                            errorData.remaining
                        );
                        generatorStore.toggleUpgradeDialog(true);
                    }
                } else {
                    toast.error('An error occurred while processing your message');
                }
            } catch (e) {
                console.error('Failed to parse error:', error);
                toast.error('An error occurred while processing your message');
            }
        }
    });

    useEffect(() => {
        if (!dataStream?.length) return;

        const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
        lastProcessedIndex.current = dataStream.length - 1;

        (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {
            if (delta.type === 'user-message-id') {
                setUserMessageIdFromServer(delta.content as string);
                return;
            }
            if (delta.type === 'ai-message-id') {
                setAIMessageIdFromServer(delta.content as string);
                return;
            }

            if (delta.type === 'design-screen-update') {
                const op: DesignScreenUpdate = delta.content as DesignScreenUpdate;
                onScreenUpdate(op);
            }


        });
    }, [dataStream, setUserMessageIdFromServer, setAIMessageIdFromServer]);


    // Placeholder functions for required props
    const votes: { projectId: string | null; chatId: string; messageId: string; isUpvoted: boolean; }[] = [];
    const isReadonly = false;
    const isBlockVisible = false;
    const totalUserMessages = 0;
    const hasMoreMessages = false;
    const setSnackError = (error: any) => console.error(error);
    const showLoadAllConfirmation = () => {
    };
    const onVersionClick = () => {
    };
    const onActionClick = () => {
    };
    const onVisualSelectionClicked = () => {
    };

    // Function to scroll to bottom of messages
    const scrollToBottom = () => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    };
    
    // Scroll to bottom when new messages arrive
    useEffect(() => {
        if (messages.length > 0) {
            scrollToBottom();
        }
    }, [messages.length]);

    const hasRunRef = useRef(false);

    const initiateLatestMessageAutoRun = useCallback(() => {
        if (runLastUserMessage && !lastRunInitialized && !hasRunRef.current) {
            onInitialRunInitialized();
            hasRunRef.current = true;
            setLastRunInitialized(true);
            reload({
                body: {
                    isReload: true,
                    isInitial: true
                }
            })
        }
    }, [runLastUserMessage, reload, lastRunInitialized, setLastRunInitialized])

    useEffect(() => {
        initiateLatestMessageAutoRun()
    }, [initiateLatestMessageAutoRun])

    return (
        <div className="flex flex-col h-full bg-background overflow-hidden">
            {/* Main scrollable area with webkit-overflow-scrolling for smooth mobile scrolling */}
            <div 
                ref={messagesContainerRef}
                className="flex-1 overflow-y-auto pb-safe" 
                style={{ WebkitOverflowScrolling: 'touch' }}
            >
                <div className="flex flex-col h-full min-h-0">
                    <Messages
                        votes={votes}
                        chatId={id}
                        projectId={projectId}
                        isLoading={isLoading}
                        messages={messages}
                        setMessages={setMessages}
                        reload={reload}
                        isReadonly={isReadonly}
                        isBlockVisible={isBlockVisible}
                        append={append}
                        status={status}
                        setInput={setInput}
                        setAttachments={setAttachments}
                        onVersionClick={onVersionClick}
                        lastActiveVersionId={''}
                        onActionClick={onActionClick}
                        setSnackError={setSnackError}
                        hasMoreMessages={hasMoreLocalOverride !== undefined ? hasMoreLocalOverride : hasMoreMessages}
                        totalUserMessages={totalUserMessages}
                        onLoadPrevious={showLoadAllConfirmation}
                        isLoadingPrevious={isLoadingPrevious}
                        removeActions={true}
                    />
                </div>
            </div>

            {/* Mobile scroll to bottom button */}
            {isMobile && messages.length > 3 && (
                <div className="absolute bottom-24 right-4 z-10">
                    <Button 
                        size="sm" 
                        variant="secondary" 
                        className="h-10 w-10 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={scrollToBottom}
                    >
                        <ArrowRight className="h-5 w-5 rotate-90" />
                    </Button>
                </div>
            )}

            {/* Compact Approval Bar for mobile */}
            {!designStore.isDesignApprovedForChat(id) && (
                <div className="flex-shrink-0 border-t border-border/50 bg-background backdrop-blur bg-opacity-90">
                    <ApprovalBar 
                        chatId={id} 
                        projectId={projectId} 
                        compact={true} 
                    />
                </div>
            )}

            {/* Input area - with safe area padding and optimized for mobile */}
            {!isReadonly && (
                <div
                    className="flex-shrink-0 border-t border-border/50 bg-background backdrop-blur bg-opacity-45 pb-safe pt-2"
                    style={{ position: 'relative', zIndex: 5 }}
                >
                    <div className={`${isMobile ? 'px-2' : 'px-4'} py-2 mx-auto`}>
                        <MultimodalInput
                            chatId={id}
                            input={input}
                            inDesignMode={true}
                            setInput={setInput}
                            handleSubmit={handleSubmit}
                            isLoading={isLoading}
                            stop={stop}
                            attachments={attachments}
                            setAttachments={setAttachments}
                            messages={messages}
                            setMessages={setMessages}
                            append={append}
                            projectId={projectId}
                            needsContinuation={false}
                            droppedFiles={droppedFiles}
                            onVisualSelectionClicked={onVisualSelectionClicked}
                            componentContexts={localComponentContexts}
                            onRemoveComponentContext={(id) => {}}
                            onClearComponentContexts={() => {}}
                            selectMode={selectMode}
                        />
                    </div>
                </div>
            )}
        </div>
    );
});

export default DesignChat;
