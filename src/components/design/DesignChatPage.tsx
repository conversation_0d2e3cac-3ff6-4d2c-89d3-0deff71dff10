'use client';

import React, {useState, useTransition} from 'react';
import {Message} from 'ai';
import {DataStreamHandler} from '@/components/base/data-stream-handler';
import {observer} from 'mobx-react-lite';
import {useStores} from '@/stores/utils/useStores';
import {UIMessage} from '@ai-sdk/ui-utils';
import {convertToUIMessages} from '@/lib/utils';
import {ResizableHandle, ResizablePanel, ResizablePanelGroup} from '@/components/ui/resizable';
import {DesignPhoneGallery} from './DesignPhoneGallery';
import DesignChat from './DesignChat';
import {useIsMobile} from '@/hooks/use-mobile';
import {DesignScreenHandler, DesignScreenUpdate} from "@/components/design/DesignScreenHandler";
import { useRef, useEffect } from 'react';
import {DesignScreen} from "@/lib/db/schema";
import { motion, AnimatePresence } from 'framer-motion';
import { Smartphone, MessageSquare, ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {useRouter} from "next/navigation";

// Component that only renders its children when visible but keeps them mounted
const VisibilityWrapper = ({isVisible, children, className, animateProps}) => {
  return (
    <motion.div 
      className={`absolute inset-0 w-full ${className || ''}`}
      style={{pointerEvents: isVisible ? 'auto' : 'none'}}
      animate={isVisible ? {opacity: 1, x: 0, ...animateProps} : {opacity: 0, x: animateProps?.exit?.x || 0}}
      transition={{duration: 0.2}}
    >
      {children}
    </motion.div>
  );
};

interface DesignChatPageProps {
    projectId: string;
    chatId: string;
    chat?: any;
    initialMessages: UIMessage[],
    screens: DesignScreen[]
}


const DesignChatPage = observer(({
                                     projectId,
                                     chatId,
                                     chat,
                                     initialMessages,
                                     screens
                                 }: DesignChatPageProps) => {
    const {generatorStore, designStore} = useStores();
    const isMobile = useIsMobile();


    // No longer need a ref to the gallery component since we're using centralized state
    
    // State for tracking design approval process
    const [isApproving, setIsApproving] = useState(false);
    const [isPending, startTransition] = useTransition();
    const router = useRouter();
    
    // Initialize screens in the designStore when component mounts
    useEffect(() => {
        if (screens && screens.length > 0) {
            // Set the screens in the designStore
            designStore.setScreens(chatId, screens);
            
            // Set active screen if not already set
            const activeScreenId = designStore.getActiveScreenId(chatId);
            if (!activeScreenId) {
                designStore.setActiveScreenId(chatId, screens[0].id);
            }
            
            console.log('Initialized screens in DesignChatPage:', screens.length);
        }
    }, [chatId, screens, designStore]);
    
    // Function to handle screen updates from the design chat
    const handleScreenUpdate = (screenData: DesignScreenUpdate) => {
        console.log('Screen update received:', screenData);
        
        // Update the screen in the designStore directly
        // This ensures screens are updated regardless of which tab is active
        // The DesignPhoneGallery component will sync with the store via its own useEffect
        designStore.updateScreen(chatId, projectId, screenData);
        
        // Auto-switch to designs tab on mobile when a new screen is detected
        // But only if we haven't already auto-switched and user hasn't manually switched back
        const screenCount = designStore.getScreens(chatId).length;
        console.log('Tab switch conditions:', {
            isMobile,
            activeView,
            hasAutoSwitchedToDesigns,
            userSwitchedBackToChat,
            screenStatus: screenData.status,
            screenCount
        });
        
        // Only auto-switch if:
        // 1. We're on mobile
        // 2. Currently on chat tab
        // 3. Haven't already auto-switched
        // 4. User hasn't manually switched back to chat
        // 5. There's at least one screen
        if (isMobile && 
            activeView === 'chat' && 
            !hasAutoSwitchedToDesigns && 
            !userSwitchedBackToChat &&
            screenCount > 0) {
            
            // Force a small delay to ensure the screen is properly loaded in the store
            setTimeout(() => {
                console.log('Auto-switching to designs tab');
                setActiveView('designs');
                setHasAutoSwitchedToDesigns(true);
            }, 500);
        } else {
            console.log('Not switching because conditions not met:', {
                isMobile,
                activeView,
                hasAutoSwitchedToDesigns,
                userSwitchedBackToChat,
                screenCount
            });
        }
    };

    // Handler for loading state changes
    const handleLoadingChange = (isLoading: boolean) => {

    };

    // Handler for when the initial run is initialized
    const handleInitialRunInitialized = () => {
        if (chat) {
            chat.isInitialized = true;
        }
    };

    // State to track which view is active on mobile (chat or designs)
    const [activeView, setActiveView] = useState<'chat' | 'designs'>('chat'); // Set chat as default active tab
    
    // State to track if the user has interacted with the design
    const [hasInteracted, setHasInteracted] = useState(false);
    
    // State to track if we've already auto-switched to designs tab
    const [hasAutoSwitchedToDesigns, setHasAutoSwitchedToDesigns] = useState(false);
    
    // State to track if user manually switched back to chat
    const [userSwitchedBackToChat, setUserSwitchedBackToChat] = useState(false);
    
    // Effect to detect Safari on iOS and apply specific fixes
    useEffect(() => {
        // Check if we're on iOS Safari
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        
        if (isIOS && isSafari) {
            // Add a specific class to the body for iOS Safari fixes
            document.body.classList.add('ios-safari');
            
            // Apply any specific iOS Safari fixes
            const styleElement = document.createElement('style');
            styleElement.textContent = `
                .ios-safari .overflow-auto {
                    -webkit-overflow-scrolling: touch;
                }
                .ios-safari .h-full {
                    height: -webkit-fill-available;
                }
            `;
            document.head.appendChild(styleElement);
            
            return () => {
                document.body.classList.remove('ios-safari');
                document.head.removeChild(styleElement);
            };
        }
    }, []);

    // Handle view switching on mobile
    const handleViewSwitch = (view: 'chat' | 'designs') => {
        setActiveView(view);
        setHasInteracted(true);
        
        // Track when user manually switches back to chat
        if (view === 'chat' && hasAutoSwitchedToDesigns) {
            setUserSwitchedBackToChat(true);
        }
        
        // Scroll to bottom of chat when switching to chat view
        if (view === 'chat') {
            setTimeout(() => {
                const chatContainer = document.querySelector('.chat-container');
                if (chatContainer) {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
            }, 100);
        }
    };

    const onInitialRunInitialized = () => {
        if (chat) {
            chat.isInitialized = true;
        }
    }

    // Render mobile layout with tabs for switching between views
    if (isMobile) {
        return (
            <div className="flex flex-col h-[calc(100vh-56px)]">
                {/* Mobile tabs for switching between chat and designs */}
                <div className="flex border-b border-border bg-background sticky top-0 z-10 shadow-sm">
                    <button
                        className={`flex-1 py-3 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${activeView === 'chat' ? 'text-primary border-b-2 border-primary' : 'text-muted-foreground'}`}
                        onClick={() => handleViewSwitch('chat')}
                    >
                        <MessageSquare className="w-4 h-4" />
                        <span>Chat</span>
                    </button>
                    <button
                        className={`flex-1 py-3 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${activeView === 'designs' ? 'text-primary border-b-2 border-primary' : 'text-muted-foreground'}`}
                        onClick={() => handleViewSwitch('designs')}
                    >
                        <Smartphone className="w-4 h-4" />
                        <span>Designs</span>
                    </button>
                </div>

                {/* Content area with animated transitions between views */}
                <div className="flex-1 overflow-hidden relative">
                    {/* Both components stay mounted, but only one is visible at a time */}
                    <VisibilityWrapper 
                        isVisible={activeView === 'chat'}
                        className="h-full"
                        animateProps={{
                            initial: { opacity: 0, x: hasInteracted ? -20 : 0 },
                            exit: { opacity: 0, x: -20 }
                        }}
                    >
                        <DesignChat
                            id={chatId}
                            initialMessages={initialMessages}
                            projectId={projectId}
                            onScreenUpdate={handleScreenUpdate}
                            runLastUserMessage={(chat && !chat.isInitialized) || false}
                            onInitialRunInitialized={onInitialRunInitialized}
                        />
                    </VisibilityWrapper>
                    
                    <VisibilityWrapper 
                        isVisible={activeView === 'designs'}
                        className="h-full"
                        animateProps={{
                            initial: { opacity: 0, x: hasInteracted ? 20 : 0 },
                            exit: { opacity: 0, x: 20 }
                        }}
                    >
                        <DesignPhoneGallery
                            projectId={projectId}
                            chatId={chatId}
                            initialScreens={screens}
                        />
                    </VisibilityWrapper>
                    
                    {/* Quick switch button */}
                    <div className="absolute bottom-24 right-4 z-10">
                        <Button 
                            size="sm" 
                            variant="secondary" 
                            className="h-10 w-10 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90"
                            onClick={() => handleViewSwitch(activeView === 'chat' ? 'designs' : 'chat')}
                        >
                            {activeView === 'chat' ? 
                                <Smartphone className="h-5 w-5" /> : 
                                <MessageSquare className="h-5 w-5" />}
                        </Button>
                    </div>
                </div>

                <DesignScreenHandler id={chatId} onScreenUpdateAction={handleScreenUpdate}/>
            </div>
        );
    }

    // Desktop layout with resizable panels
    return (
        <div className="flex flex-col h-[calc(100vh-56px)]">
            {/* Resizable layout */}
            <div className="h-full overflow-hidden">
                <ResizablePanelGroup direction="horizontal" className="min-h-[calc(100vh-56px)]">
                    {/* Left panel - Chat */}
                    <ResizablePanel defaultSize={30} className="bg-background">
                        <div className="flex flex-col h-full">
                            <DesignChat
                                id={chatId}
                                initialMessages={initialMessages}
                                projectId={projectId}
                                onScreenUpdate={handleScreenUpdate}
                                runLastUserMessage={(chat && !chat.isInitialized) || false}
                                onInitialRunInitialized={onInitialRunInitialized}
                            />
                        </div>
                    </ResizablePanel>

                    <ResizableHandle withHandle/>

                    {/* Right panel - Design Phone Gallery */}
                    <ResizablePanel defaultSize={70} className="bg-background">
                        <DesignPhoneGallery
                            projectId={projectId}
                            chatId={chatId}
                            initialScreens={screens}
                        />
                    </ResizablePanel>
                </ResizablePanelGroup>

                <DesignScreenHandler id={chatId} onScreenUpdateAction={handleScreenUpdate}/>
                {/*<DataStreamHandler id={chatId}*/}
                {/*                   onFileOpen={() => {}}*/}
                {/*                   operationChange={() => {}}*/}
                {/*                   onContinuationFlagChange={() => {}}*/}
                {/*                   onStreamingComplete={() => {}}*/}
                {/*                   onUpdateUserMessage={() => {}}*/}
                {/*                   />*/}
            </div>
        </div>
    );
});

export default DesignChatPage;
