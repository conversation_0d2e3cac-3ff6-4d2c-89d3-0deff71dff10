'use client';

import React, {useState, useRef, useEffect, forwardRef, useImperativeHandle, useCallback, useMemo, useTransition} from 'react';
import * as htmlToImage from 'html-to-image';
import {observer} from 'mobx-react-lite';
import {useStores} from '@/stores/utils/useStores';
import {useIsMobile} from '@/hooks/use-mobile';
import {PhoneFrame} from '@/components/generator/PhoneFrame';
import {Button} from '@/components/ui/button';
import {Edit, Download, Share2, Plus, Loader2, ChevronLeft, ChevronRight, Check, X, Trash2, CheckCircle2 } from 'lucide-react';
import {toast} from 'sonner';
import {useRouter} from 'next/navigation';
import {motion, AnimatePresence} from 'framer-motion';
import {DesignScreen} from "@/lib/db/schema";
import {memo} from "react";
import { Dialog, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import ApprovalBar from "@/components/design/ApprovalBar";
import { DesignPhaseDialog } from './DesignPhaseDialog';

// Interface for the ScreenItem component props
interface ScreenItemProps {
    screen: DesignScreen;
    projectId: string;
    chatId: string;
    refreshKey: number;
    isActive: boolean;
    onScreenClick: (e: React.MouseEvent) => void;
    onEditClick: (id: string) => void;
    onDeleteClick: (id: string) => void;
    totalScreens: number;
    isMobile: boolean;
}

// Interface for the click event data received from iframe
interface IframeClickEvent {
    type: 'screen-click';
    element: {
        tagName: string;
        className: string;
        id: string;
        text: string;
        position: {
            x: number;
            y: number;
            width: number;
            height: number;
        };
    };
    timestamp: number;
}

// Memoized ScreenItem component for better performance
const ScreenItem = memo(({ 
    screen, 
    projectId, 
    chatId, 
    isActive, 
    refreshKey,
    onScreenClick, 
    onEditClick,
    onDeleteClick,
    totalScreens,
    isMobile
}: ScreenItemProps) => {
    return (
        <motion.div
            id={`screen-${screen.id}`}
            className="relative group"
            initial={{opacity: 0, y: 20}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.3}}
            whileHover={{scale: 1.02}}
        >
            <div className="absolute -top-2 -right-2 z-10 md:opacity-0 md:group-hover:opacity-100 opacity-100 transition-opacity flex gap-1.5 md:gap-2">
                {/*<Button*/}
                {/*    size="sm"*/}
                {/*    variant="secondary"*/}
                {/*    className="h-7 w-7 md:h-8 md:w-8 rounded-full shadow-md"*/}
                {/*    onClick={(e) => {*/}
                {/*        e.stopPropagation();*/}
                {/*        onEditClick(screen.id);*/}
                {/*    }}*/}
                {/*>*/}
                {/*    <Edit className="h-4 w-4"/>*/}
                {/*</Button>*/}
                
                {/* Only show delete button if there's more than one screen or if this screen has an error */}
                {(totalScreens > 1 || screen.status === 'error') && (
                    <ConfirmationDialog
                        title="Remove Screen"
                        description={
                            <div className="py-2">
                                <p>Are you sure you want to remove the <strong>{screen.name}</strong> screen?</p>
                                <p className="text-sm text-muted-foreground mt-2">This action cannot be undone.</p>
                            </div>
                        }
                        confirmText="Remove"
                        cancelText="Cancel"
                        variant="destructive"
                        onConfirm={() => onDeleteClick(screen.id)}
                        trigger={
                            <Button
                                size="sm"
                                variant="destructive"
                                className="h-7 w-7 md:h-8 md:w-8 rounded-full shadow-md"
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                            >
                                <Trash2 className="h-4 w-4"/>
                            </Button>
                        }
                    />
                )}
            </div>

            <div className="mb-2 text-center text-sm font-medium text-white flex items-center justify-center gap-2">
                {screen.name}
                {screen.status === 'generating' && (
                    <span 
                        className="inline-block h-2 w-2 bg-yellow-400 rounded-full animate-pulse"
                        title="Generating content..."
                    />
                )}
                {screen.status === 'error' && (
                    <span 
                        className="inline-block h-2 w-2 bg-red-500 rounded-full"
                        title="Error generating screen"
                    />
                )}
            </div>

            <div
                className={`transition-all duration-200 ${
                    isActive
                        ? 'ring-2 ring-primary rounded-3xl shadow-lg'
                        : 'opacity-90 hover:opacity-100'
                }`}
                style={{
                    WebkitTapHighlightColor: 'transparent',
                    cursor: 'pointer'
                }}
            >
                {
                    /* Check if browser is Safari or mobile device */
                    (() => {
                            
                        const isSafari = typeof window !== 'undefined' && 
                            /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                        
                        // Use simplified frame for Safari or any mobile device
                        if (isSafari) {
                            // Use direct iframe with Safari-specific optimizations
                            return (
                                <div 
                                    className="rounded-[24px] overflow-hidden bg-black border-[8px] border-black"
                                    style={{
                                        // Calculate height based on iPhone dimensions and scale factor
                                        // For a scale factor of 0.71, the height should be 812px * 0.71 = 576px
                                        height: 614, // Fixed height based on scale factor (812 * 0.71)
                                        width: 280, // Fixed width for consistent rendering
                                        maxHeight: isMobile ? (typeof window !== 'undefined' ? window.innerHeight * 0.71 : 614) : 614,
                                        WebkitOverflowScrolling: 'touch', // Safari-specific optimization
                                    }}
                                >
                                    <div 
                                        className="w-full h-full relative overflow-hidden"
                                        style={{
                                            // Safari-specific optimizations
                                            transform: 'translateZ(0)',
                                            WebkitTransform: 'translateZ(0)',
                                            backfaceVisibility: 'hidden',
                                            WebkitBackfaceVisibility: 'hidden',
                                        }}
                                    >
                                        {/* Try direct iframe with Safari optimizations instead of MobileScreenRenderer */}
                                        <iframe
                                            key={`safari-frame-${screen.id}-${refreshKey}-${screen.updatedAt?.toString() || Date.now()}`}
                                            src={`/api/project/${projectId}/design/${chatId}/screens/${screen.id}?t=${Date.now()}&safari=true`}
                                            title={screen.name}
                                            className="border-0"
                                            style={{
                                                width: '375px', // Fixed iPhone width
                                                height: '812px', // Fixed iPhone height
                                                transform: 'scale(0.71)', // Scale to fit container
                                                WebkitTransform: 'scale(0.71)', // Safari-specific prefix
                                                transformOrigin: 'top left',
                                                WebkitTransformOrigin: 'top left',
                                                border: 'none',
                                                // Additional Safari optimizations
                                                display: 'block', // Ensure block display
                                                position: 'absolute',
                                                top: 0,
                                                left: 0
                                            }}
                                            sandbox="allow-same-origin allow-scripts"
                                            loading="eager"
                                        />
                                    </div>
                                </div>
                            );
                        } else {
                            // Regular PhoneFrame for other browsers
                            return (
                                <PhoneFrame 
                                    containerHeight={isMobile ? (typeof window !== 'undefined' ? window.innerHeight * 0.7 : 600) : 600}
                                >
                                    {/* Wrap iframe in a div to capture click events */}
                                    <div 
                                        className="w-full h-full relative"
                                    >
                                        {/* Always use iframe to load from API */}
                                        <iframe
                                            key={`frame-${screen.id}-${refreshKey}-${screen.updatedAt?.toString() || Date.now()}`}
                                            src={`/api/project/${projectId}/design/${chatId}/screens/${screen.id}?t=${Date.now()}`}
                                            title={screen.name}
                                            className="w-full h-full border-0"
                                            style={{
                                                transform: 'scale(1)',
                                                transformOrigin: 'top left',
                                                width: '100%',
                                                height: '100%',
                                            }}
                                            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals"
                                            loading="eager" // Change to eager to ensure it loads immediately
                                        />
                                    </div>
                                </PhoneFrame>
                            );
                        }
                    })()
                }
            </div>
        </motion.div>
    );
}, (prevProps, nextProps) => {
    // Custom comparison function for memo to prevent unnecessary re-renders
    return (
        prevProps.screen.id === nextProps.screen.id &&
        prevProps.screen.status === nextProps.screen.status &&
        prevProps.screen.updatedAt === nextProps.screen.updatedAt &&
        prevProps.isActive === nextProps.isActive &&
        prevProps.refreshKey === nextProps.refreshKey &&
        prevProps.projectId === nextProps.projectId &&
        prevProps.chatId === nextProps.chatId &&
        prevProps.isMobile === nextProps.isMobile
    );
});

interface DesignPhoneGalleryProps {
    projectId: string;
    chatId: string;
    initialScreens: DesignScreen[];
    // onApproveDesigns is no longer needed as we're moving the logic into this component
}

export const DesignPhoneGallery = observer(({ projectId, chatId, initialScreens }: DesignPhoneGalleryProps) => {
    // Get the designStore
    const { designStore } = useStores();
    
    // Initialize the store with initial screens if needed
    useEffect(() => {
        if (initialScreens?.length > 0 && designStore.getScreens(chatId).length === 0) {
            designStore.setScreens(chatId, initialScreens);
        }
    }, [chatId, designStore, initialScreens]);
    
    // Use local state for active screen ID with fallback to store value
    const [activeScreenId, setActiveScreenId] = useState<string | null>(
        designStore.getActiveScreenId(chatId) || 
        (() => {
            const storeScreens = designStore.getScreens(chatId);
            return storeScreens.length > 0 ? storeScreens[0].id : null;
        })() || 
        (initialScreens?.length > 0 ? initialScreens[0].id : null)
    );

    // State to track which iframes need to be refreshed
    const [refreshKeys, setRefreshKeys] = useState<Record<string, number>>({});

    // Canvas state for infinite canvas feel
    const canvasRef = useRef<HTMLDivElement>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [position, setPosition] = useState({x: 0, y: 0});
    const [startPos, setStartPos] = useState({x: 0, y: 0});
    const dragStartRef = useRef({x: 0, y: 0});
    
    // Use the isMobile hook for consistent mobile detection
    const isMobile = useIsMobile();
    
    // State for approval flow
    const [isApproving, setIsApproving] = useState(false);
    // Dialog state moved to ApprovalBar component
    
    // Effect to ensure the mobile preview loads initially
    useEffect(() => {
        if (activeScreenId) {
            // Force refresh the iframe to ensure it loads immediately
            setRefreshKeys(prev => ({
                ...prev,
                [activeScreenId]: (prev[activeScreenId] || 0) + 1
            }));
        }
    }, [activeScreenId]);

    // Initialize the store with initial screens if needed
    useEffect(() => {
        if (initialScreens?.length > 0 && designStore.getScreens(chatId).length === 0) {
            designStore.setScreens(chatId, initialScreens);
        }
    }, [chatId, designStore, initialScreens]);
    
    // Update active screen ID when screens change
    useEffect(() => {
        // Only set active screen if not set and screens exist
        const currentScreens = designStore.getScreens(chatId);
        if (currentScreens.length > 0 && !activeScreenId) {
            setActiveScreenId(currentScreens[0].id);
        }
    }, [chatId, designStore.screens, activeScreenId, setActiveScreenId]);
    
    // Sync local active screen ID with designStore
    useEffect(() => {
        if (activeScreenId) {
            designStore.setActiveScreenId(chatId, activeScreenId);
        }
    }, [activeScreenId, chatId, designStore]);


    // Check if all screens are complete
    useEffect(() => {
        // Let the designStore handle checking if all screens are complete
        designStore.checkAllScreensComplete(chatId);
    }, [chatId, designStore.screens]);


    // No need to listen for data stream events here - we'll receive updates via the ref
    // Function to scroll to a specific screen
    const scrollToScreen = useCallback((id: string) => {
        const screenElement = document.getElementById(`screen-${id}`);
        if (screenElement && canvasRef.current) {
            // For infinite canvas, we adjust the position state
            setPosition({
                x: -screenElement.offsetLeft + 100,
                y: position.y
            });
        }
    }, [position]);

    // Function to handle editing a screen
    const handleEditScreen = useCallback((screenId: string) => {
        // Implement edit functionality
        console.log('Edit screen:', screenId);
        // For now, just show a toast
        toast.info('Screen editing coming soon!');
    }, []);

    // Function to handle deleting a screen - now uses DesignStore.deleteScreen
    const handleDeleteScreen = useCallback(async (screenId: string) => {
        try {
            // Show loading toast
            const loadingToast = toast.loading('Removing screen...');
            
            // Use the DesignStore to delete the screen
            await designStore.deleteScreen(projectId, chatId, screenId);
            
            // Dismiss loading toast and show success toast
            toast.dismiss(loadingToast);
            toast.success('Screen removed successfully');
        } catch (error) {
            console.error('Error removing screen:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to remove screen');
        }
    }, [projectId, chatId, designStore]);

    // Canvas drag handlers for infinite canvas feel
    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        if (e.button !== 0) return; // Only left mouse button
        setIsDragging(true);
        dragStartRef.current = {x: e.clientX - position.x, y: e.clientY - position.y};
        setStartPos({x: e.clientX, y: e.clientY});

        if (canvasRef.current) {
            canvasRef.current.style.cursor = 'grabbing';
        }
    }, [position]);

    const handleMouseMove = useCallback((e: React.MouseEvent) => {
        if (!isDragging) return;

        const newX = e.clientX - dragStartRef.current.x;
        const newY = e.clientY - dragStartRef.current.y;

        setPosition({x: newX, y: newY});
    }, [isDragging, position]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
        if (canvasRef.current) {
            canvasRef.current.style.cursor = 'grab';
        }
    }, []);

    // Add event listeners for mouse events outside the component
    React.useEffect(() => {
        const handleGlobalMouseUp = () => {
            if (isDragging) {
                setIsDragging(false);
                if (canvasRef.current) {
                    canvasRef.current.style.cursor = 'grab';
                }
            }
        };

        window.addEventListener('mouseup', handleGlobalMouseUp);
        return () => {
            window.removeEventListener('mouseup', handleGlobalMouseUp);
        };
    }, [isDragging]);

    // Approval handling moved to ApprovalBar component

    // Handle dialog close - mark the dialog as shown when closed
    const handleDialogClose = useCallback(() => {
        // Mark the dialog as shown for this chat session
        designStore.setDesignPhaseDialogShown(chatId, true);
        // Reset the screen clicked flag (optional, since we only check it once)
        setScreenClicked(false);
    }, [chatId, designStore]);
    
    // Handle dialog approve - mark the dialog as shown when approved
    const handleDialogApprove = useCallback(() => {
        // Mark the dialog as shown for this chat session
        designStore.setDesignPhaseDialogShown(chatId, true);
        // Reset the screen clicked flag (optional, since we only check it once)
        setScreenClicked(false);
    }, [chatId, designStore]);
    
    // Set active screen ID
    const handleSetActiveScreen = useCallback((screenId: string) => {
        setActiveScreenId(screenId);
        designStore.setActiveScreenId(chatId, screenId);
    }, [chatId, designStore]);

    // Router for navigation after approval
    const router = useRouter();

    // Memoize the header component for better performance
    const HeaderComponent = useMemo(() => (
        <div className="sticky top-0 z-20 w-full bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/50 dark:to-emerald-950/50 border-b border-green-200 dark:border-green-900 shadow-sm">
            <div className="container py-3 px-4">
                <ApprovalBar projectId={projectId} chatId={chatId} />
            </div>
        </div>
    ), [projectId, chatId]);

    
    // The handleClose and handleApprove functions are now provided by the useDesignPhaseDialog hook

    // Global click processed flag to prevent multiple rapid clicks
    const clickProcessedRef = useRef(false);
    
    // Track if a screen was clicked in this session
    const [screenClicked, setScreenClicked] = useState(false);
    
    // Determine if we should show the dialog based on:
    // 1. A screen was clicked in this session
    // 2. The dialog hasn't been shown yet for this chat session
    const shouldShowDialog = screenClicked && !designStore.isDesignPhaseDialogShown(chatId);
    
    // Global message handler for iframe clicks
    useEffect(() => {
        const handleIframeMessage = (event: MessageEvent) => {
            // Only process screen-click messages
            if (event.data && event.data.type === 'screen-click') {
                // Don't process if we already handled a click recently
                if (clickProcessedRef.current) return;
                
                console.log('[DesignPhoneGallery] Received click event from iframe');
                
                // Mark as processed to prevent multiple clicks
                clickProcessedRef.current = true;
                
                // Set the flag to indicate a screen was clicked
                setScreenClicked(true);
                
                // If this is the first click and dialog hasn't been shown yet, mark it as shown
                // The dialog will appear because of the shouldShowDialog condition
                if (!designStore.isDesignPhaseDialogShown(chatId)) {
                    // We don't need to mark it as shown here - we'll do that when dialog is closed
                }
                
                // Reset the processed flag after a delay
                setTimeout(() => {
                    clickProcessedRef.current = false;
                }, 500); // 500ms debounce
            }
        };
        
        // Add the event listener
        window.addEventListener('message', handleIframeMessage);
        
        // Clean up the event listener when the component unmounts
        return () => {
            window.removeEventListener('message', handleIframeMessage);
        };
    }, [chatId, designStore]); // Only depends on stable references
    
    return (
        <div className="flex flex-col h-full">
            {/* Design Phase Dialog - shown only once per session when a screen is clicked */}
            {shouldShowDialog && (
                <DesignPhaseDialog 
                    onClose={handleDialogClose} 
                    onApprove={handleDialogApprove} 
                />
            )}

            {/* Header with title and controls */}
            {HeaderComponent}

            {/* Gallery container - optimized for mobile and desktop */}
            <div
                ref={canvasRef}
                className={`flex-1 overflow-auto relative bg-[#1e1e1e] prevent-overscroll ${isMobile ? 'overflow-x-auto -webkit-overflow-scrolling-touch' : 'cursor-grab'}`}
                style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#4a4a4a #2a2a2a',
                }}
                {...(!isMobile ? {
                    onMouseDown: handleMouseDown,
                    onMouseMove: handleMouseMove,
                    onMouseUp: handleMouseUp
                } : {})}
            >
                {/* Canvas content - use transform for desktop, direct scroll for mobile */}
                <div
                    className={`${isMobile ? 'relative' : 'absolute'} min-h-full`}
                    style={{
                        ...(isMobile ? {} : {
                            transform: `translate(${position.x}px, ${position.y}px)`,
                            transition: isDragging ? 'none' : 'transform 0.1s ease-out',
                        }),
                        minWidth: 'max-content',
                        padding: isMobile ? '1rem' : '2rem 4rem',
                        width: 'fit-content',
                    }}
                >
                    {/* Phone frames container - arranged horizontally */}
                    <div className={`flex flex-nowrap ${isMobile ? 'gap-6' : 'gap-16'} h-full`} style={{
                        width: 'fit-content',
                        minWidth: '100%',
                    }}>

                        {designStore.getScreens(chatId).map((screen) => (
                            <ScreenItem
                                key={screen.id}
                                screen={screen}
                                projectId={projectId}
                                chatId={chatId}
                                isActive={activeScreenId === screen.id}
                                refreshKey={refreshKeys[screen.id] || 0}
                                onScreenClick={(e) => {
                                    e.stopPropagation();
                                    handleSetActiveScreen(screen.id);
                                }}
                                onEditClick={handleEditScreen}
                                onDeleteClick={handleDeleteScreen}
                                totalScreens={designStore.getScreens(chatId).length}
                                isMobile={isMobile}
                            />
                        ))}
                    </div>
                </div>
            </div>
            
            {/* Approval confirmation dialog moved to ApprovalBar component */}
        </div>
    );
});

// Component is already exported with observer at line 176
