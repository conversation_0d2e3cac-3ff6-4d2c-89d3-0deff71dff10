import React, {useEffect, useState, useCallback, useRef} from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle2, Loader2 } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useStores } from "@/stores/utils/useStores";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {useRouter} from "next/navigation";

interface ApprovalBarProps {
  projectId: string;
  chatId: string;
  compact?: boolean;
  className?: string;
}

const ApprovalBar = observer(({ 
  projectId, 
  chatId, 
  compact = false,
  className
}: ApprovalBarProps) => {
  const { designStore } = useStores();
  const allScreensComplete = designStore.allScreensComplete;
  const isApproving = designStore.isApproving;
  const isDesignApproved = designStore.isDesignApprovedForChat(chatId);
  const router = useRouter();
  // State for approval dialog
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  
  // Progress timer state
  const progressInterval = useRef<NodeJS.Timeout | null>(null);
  const [timerStarted, setTimerStarted] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0); // Force re-renders to update UI

  // Function to start the progress timer
  const startProgressTimer = useCallback(() => {
    // Reset progress to 0
    designStore.setApprovalProgress(chatId, 0);
    setTimerStarted(true);
    
    console.log('Starting timer for chatId:', chatId);
    
    // Clear any existing interval first
    if (progressInterval.current) {
      clearInterval(progressInterval.current);
      progressInterval.current = null;
    }
    
    // Define step thresholds
    const steps = [
      { threshold: 25, step: 0 },
      { threshold: 50, step: 1 },
      { threshold: 75, step: 2 },
      { threshold: 100, step: 3 }
    ];
    
    // Start with 0% progress
    let progress = 0;
    let currentStep = 0;
    
    // Update every 500ms
    progressInterval.current = setInterval(() => {
      // Increment progress by small amount (0.42% per update = ~2 minutes total)
      progress = Math.min(progress + 0.42, 100);
      
      // Determine current step based on progress
      for (let i = 0; i < steps.length; i++) {
        if (progress <= steps[i].threshold) {
          currentStep = steps[i].step;
          break;
        }
      }
      
      // Update store with current progress
      designStore.setApprovalProgress(chatId, progress);
      
      // Explicitly set the current step to ensure it's correct
      designStore.currentStepByChatId.set(chatId, currentStep);
      
      // Force UI update
      setForceUpdate(prev => prev + 1);
      
      console.log(`Progress: ${progress.toFixed(1)}%, Step: ${currentStep}`);
      
      // If we've reached 100%, clear the interval
      if (progress >= 100) {
        if (progressInterval.current) {
          clearInterval(progressInterval.current);
          progressInterval.current = null;
        }
      }
    }, 500);
    
    // Safety: Clear the interval after 2 minutes
    setTimeout(() => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
        progressInterval.current = null;
      }
    }, 120000); // 2 minutes
  }, [designStore, chatId]);
  
  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
        progressInterval.current = null;
      }
    };
  }, []);

  // Start progress timer when approval starts
  useEffect(() => {
    if (isApproving && !timerStarted) {
      startProgressTimer();
    }
  }, [isApproving, timerStarted, startProgressTimer]);
  
  // Force UI updates every 500ms when approving
  useEffect(() => {
    let updateInterval: NodeJS.Timeout | null = null;
    
    if (isApproving) {
      updateInterval = setInterval(() => {
        setForceUpdate(prev => prev + 1);
      }, 500);
    }
    
    return () => {
      if (updateInterval) {
        clearInterval(updateInterval);
      }
    };
  }, [isApproving]);
  
  // Don't render if design is already approved
  if (isDesignApproved) {
    return null;
  }

  const handleApproveClick = () => {
    // Show confirmation dialog instead of approving directly
    setShowApprovalDialog(true);
  };
  
  const handleConfirmApprove = () => {
    if (allScreensComplete && !isApproving) {
      designStore.approveDesign(projectId, chatId)
          .then((savedChatId) => {
            if(!!savedChatId) {
              router.replace(`/projects/${projectId}/chats/${savedChatId}`)
            }
          })
    }
  };
  
  // Shared approval dialog component
  const approvalDialog = (
    <Dialog open={showApprovalDialog} onOpenChange={(open) => {
      if (!isApproving) {
        setShowApprovalDialog(open);
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isApproving ? "Preparing Your App" : "Approve Design & Build App"}</DialogTitle>
          <DialogDescription>
            {isApproving 
              ? "We're preparing your designs for app development. This will take about 2 minutes." 
              : "You're about to approve these designs and move to the app building phase. Once approved, these designs will be used as the foundation for your mobile app."}
          </DialogDescription>
        </DialogHeader>
        
        {isApproving ? (
          <div className="grid gap-4 py-4">
            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 overflow-hidden">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2.5 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${designStore.getApprovalProgress(chatId)}%` }}
              ></div>
            </div>
            
            {/* Current step indicator */}
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium">
                  {(() => {
                    const currentStep = designStore.getCurrentStep(chatId);
                    // console.log('Current step in display:', currentStep);
                    // Always show a valid step label
                    if (currentStep >= 0 && currentStep < designStore.approvalSteps.length) {
                      return designStore.approvalSteps[currentStep].label;
                    } else {
                      // Default to first step if no valid step
                      return designStore.approvalSteps[0].label;
                    }
                  })()}
                </p>
                <p className="text-xs text-muted-foreground">
                  {(() => {
                    const currentStep = designStore.getCurrentStep(chatId);
                    // Always show step as 1-indexed (user-friendly)
                    const displayStep = currentStep + 1;
                    // console.log(`Display step: ${displayStep}, actual step: ${currentStep}`);
                    return `Step ${displayStep} of ${designStore.approvalSteps.length}`;
                  })()}
                </p>
              </div>
              <div className="text-sm font-medium">
                {Math.round(designStore.getApprovalProgress(chatId))}%
              </div>
            </div>
            
            {/* Steps list */}
            <div className="mt-4 space-y-3">
              {designStore.approvalSteps.map((step, index) => {
                // Force read the current step on each render
                const currentStep = designStore.getCurrentStep(chatId);
                // console.log(`Rendering step ${index}, current step is ${currentStep}`);
                
                return (
                  <div key={index} className="flex items-center">
                    <div className={`flex items-center justify-center w-6 h-6 rounded-full mr-3 ${index <= currentStep ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' : 'bg-gray-100 text-gray-400 dark:bg-gray-800'}`}>
                      {index < currentStep ? (
                        <CheckCircle2 className="w-4 h-4" />
                      ) : index === currentStep ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <span className="text-xs">{index + 1}</span>
                      )}
                    </div>
                    <span className={`text-sm ${index <= currentStep ? 'text-foreground font-medium' : 'text-muted-foreground'}`}>
                      {step.label}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="grid gap-4 py-4">
            <div className="bg-slate-50 dark:bg-slate-900 p-3 rounded-md">
              <p className="text-sm font-medium mb-2">What happens next?</p>
              <ul className="text-sm space-y-2">
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">1.</span>
                  <span>We'll analyze your designs and create an app blueprint</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">2.</span>
                  <span>You'll be taken to the app builder where you can see your designs come to life</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">3.</span>
                  <span>You can then customize and refine your app further</span>
                </li>
              </ul>
            </div>
          </div>
        )}
        
        <DialogFooter className="flex space-x-2 sm:justify-between">
          {!designStore.isApproving && (
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowApprovalDialog(false)}
              disabled={designStore.isApproving}
            >
              Cancel
            </Button>
          )}
          {!designStore.isApproving ? (
            <Button 
              type="button" 
              onClick={handleConfirmApprove}
              disabled={designStore.isApproving}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Start Building App
            </Button>
          ) : (
            <p className="text-xs text-muted-foreground italic">
              Please wait while we prepare your app blueprint...
            </p>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  if (compact) {
    // Compact version for mobile chat view
    return (
      <>
        <div className={cn("flex items-center justify-between px-3 py-2 bg-green-50 dark:bg-green-900/20 border-t border-b border-green-100 dark:border-green-800", className)}>
          <div className="flex items-center space-x-2">
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0" />
            <p className="text-xs text-green-700 dark:text-green-400 truncate">
              {designStore.getScreens(chatId).length} {designStore.getScreens(chatId).length === 1 ? 'screen' : 'screens'} ready to build
            </p>
          </div>
          <Button
            onClick={handleApproveClick} /* This is the same handler as non-compact version */
            disabled={!allScreensComplete || isApproving}
            className="bg-green-600 hover:bg-green-700 text-white font-medium h-7 text-xs px-2"
            size="sm"
          >
            Start Building
          </Button>
        </div>
        {approvalDialog}
      </>
    );
  }

  // Non-compact version for design gallery
  return (
    <>
      <div className={cn("flex items-center justify-between py-2 px-3 bg-green-50 dark:bg-green-900/20 rounded-lg", className)}>
        <div className="flex items-center space-x-2">
          <div className="flex-shrink-0">
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="text-sm font-medium text-green-900 dark:text-green-300">
              {designStore.getScreens(chatId).length} {designStore.getScreens(chatId).length === 1 ? 'screen' : 'screens'} designed
            </h3>
            <p className="text-xs text-green-700 dark:text-green-400">
              {allScreensComplete
                ? 'Ready to build your production app'
                : 'Screens are still being generated...'}
            </p>
          </div>
        </div>
        <Button
          onClick={handleApproveClick}
          disabled={!allScreensComplete || isApproving}
          className="bg-green-600 hover:bg-green-700 text-white font-medium h-8 text-xs px-3"
          size="sm"
        >
          Start Building App
        </Button>
      </div>
      {approvalDialog}
    </>
  );
});

export default ApprovalBar;
