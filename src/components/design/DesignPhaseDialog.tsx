"use client";
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Smartphone, CheckCircle2, ArrowRight, Info, X } from "lucide-react";
import { motion } from 'framer-motion';
import {Alert} from "@/components/ui/alert";

interface DesignPhaseDialogProps {
  onClose: () => void;
  onApprove: () => void;
}

export const DesignPhaseDialog: React.FC<DesignPhaseDialogProps> = ({ onClose, onApprove }) => {
  const [open, setOpen] = useState(true);

  const handleClose = () => {
    setOpen(false);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={() => setOpen(true)}>
      <DialogContent className="sm:max-w-md p-6 bg-black text-white" disableCloseButton={true}>
        {/* Simple progress indicator */}
        <div className="flex items-center justify-center mb-5">
          <div className="flex items-center">
            <div className="flex flex-col items-center">
              <div className="rounded-full bg-purple-600 w-8 h-8 flex items-center justify-center text-white">
                <Smartphone className="h-4 w-4" />
              </div>
              <span className="text-xs font-medium mt-1 text-purple-500">Preview</span>
            </div>
            
            <div className="w-12 h-0.5 bg-gray-700 mx-2"></div>
            
            <div className="flex flex-col items-center">
              <div className="rounded-full bg-gray-700 w-8 h-8 flex items-center justify-center text-gray-400">
                <CheckCircle2 className="h-4 w-4" />
              </div>
              <span className="text-xs font-medium mt-1 text-gray-400">Make it work</span>
            </div>
          </div>
        </div>

        {/* Main message - simplified for non-technical users */}
        <div className="text-center mb-4">
          <h3 className="text-xl font-medium">Your App Preview</h3>
          <div className="text-sm text-gray-300 mt-4 space-y-3">
            <p>You can now see how your app will look. These are just previews - they're not fully working yet.</p>
            
            <div className="bg-gray-800 rounded-lg p-3 mt-3">
              <p className="font-medium text-white mb-2 text-start">What you can do now:</p>
              <ul className="space-y-2 text-start">
                <li className="flex items-start">
                  <span className="text-purple-400 mr-2">•</span>
                  <span>Look through all screens</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-400 mr-2">•</span>
                  <span>Ask for changes in chat</span>
                </li>
              </ul>
            </div>
            
            <p className="mt-4">
              When you're happy with how everything looks, click <span className="text-purple-400 font-medium">Start building</span> to make your app functional.
            </p>
          </div>
        </div>


        {/* Simple dismiss option */}
        <div className="text-center mt-2">
          <Button
            onClick={handleClose}
            className="bg-purple-600 hover:bg-purple-700"
          >
            Got it, let me see
            <ArrowRight className="h-4 w-4 ml-1"/>
          </Button>
        </div>

        {/* Close button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white"
          aria-label="Close"
        >
          <X className="h-4 w-4" />
        </button>
      </DialogContent>
    </Dialog>
  );
};
