'use client';

import {useChat} from '@ai-sdk/react';
import {Message as UIMessage} from 'ai';
import {useEffect, useRef, useState} from 'react';
import {Message, Suggestion} from '@/lib/db/schema';
import {initialBlockData, useBlock} from '@/hooks/use-block';
import {useUserMessageId} from '@/hooks/use-user-message-id';
import {useAIMessageId} from '@/hooks/use-ai-message-id';
import {cx} from 'class-variance-authority';
import {CodeBlock} from '@/types/file';
import {toast} from 'sonner';


export interface DesignScreenUpdate {
    name: string,
    screenId: string,
    status: 'starting' | 'generating' | 'complete' | 'error',
    order: number
}

type DataStreamDelta = {
    type:
        | 'text-delta'
        | 'code-delta'
        | 'title'
        | 'id'
        | 'suggestion'
        | 'clear'
        | 'finish'
        | 'user-message-id'
        | 'ai-message-id'
        | 'design-screen-update'
    content: string | DesignScreenUpdate
};

export function DesignScreenHandler({
                                        id,
                                        onStreamingComplete,
                                        onScreenUpdateAction
                                    }: {
    id: string,
    onStreamingComplete?: () => void;
    onScreenUpdateAction: (update: DesignScreenUpdate) => void;
}) {
    const {data: dataStream, messages, setMessages} = useChat({id, streamProtocol: "text"});
    const {setUserMessageIdFromServer} = useUserMessageId();
    const {setAIMessageIdFromServer} = useAIMessageId();
    const lastProcessedIndex = useRef(-1);

    useEffect(() => {
        console.log('DesignScreenHandler')
    }, [])
    useEffect(() => {
        if (!dataStream?.length) return;

        const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
        lastProcessedIndex.current = dataStream.length - 1;

        (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {
            console.log('newDeltasnewDeltas', delta)
            if (delta.type === 'user-message-id') {
                setUserMessageIdFromServer(delta.content as string);
                return;
            }
            if (delta.type === 'ai-message-id') {
                console.log('AI message id', delta.content);
                onStreamingComplete && onStreamingComplete();
                setAIMessageIdFromServer(delta.content as string);
                return;
            }

            if (delta.type === 'design-screen-update') {
                console.log('jkk', delta)
                const op: DesignScreenUpdate = delta.content as DesignScreenUpdate;
                onScreenUpdateAction(op);
            }


        });
    }, [dataStream, setUserMessageIdFromServer, setAIMessageIdFromServer]);

    return null;
}
