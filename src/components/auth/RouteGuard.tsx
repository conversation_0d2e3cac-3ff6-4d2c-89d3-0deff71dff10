'use client';

import {signIn, useSession} from 'next-auth/react';
import {usePathname, useRouter} from 'next/navigation';
import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';

export function RouteGuard({ children }: { children: React.ReactNode }) {
  const { status } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (status === 'unauthenticated' && !window.location.pathname.includes('/projects')) {
      router.push('/login');
      signIn(undefined, { callbackUrl: pathname as string });
    }
  }, [status, router]);

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen w-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return <>{children}</>;
}
