'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Wand2 } from 'lucide-react';

interface EmailTemplateEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const TEMPLATE_SUGGESTIONS = [
  {
    name: "Re-engagement",
    prompt: "Write a friendly email to re-engage users who haven't used our AI coding assistant in the last 30 days. Remind them of the key features and benefits."
  },
  {
    name: "Product Tips",
    prompt: "Write an email sharing 3 useful tips for getting the most out of our AI coding assistant platform. Include specific examples of how these tips can improve productivity."
  },
  {
    name: "Feature Announcement",
    prompt: "Write an email announcing new features in our AI coding assistant. Focus on how these features solve common pain points for developers."
  }
];

export function EmailTemplateEditor({ value, onChange }: EmailTemplateEditorProps) {
  const [activeTab, setActiveTab] = useState('edit');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationPrompt, setGenerationPrompt] = useState('');
  const [streamedResponse, setStreamedResponse] = useState('');
  
  // Reset streamed response when tab changes
  useEffect(() => {
    if (activeTab === 'generate') {
      setStreamedResponse('');
    }
  }, [activeTab]);
  
  // Generate email content using AI
  const generateEmailContent = async (prompt: string) => {
    setIsGenerating(true);
    setStreamedResponse('');
    toast.info('Generating email content...');
    
    try {
      const response = await fetch('/api/admin/generate-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`Failed to generate email content: ${response.status} ${errorText}`);
      }
      
      // Check if the response is a stream
      if (!response.body) {
        throw new Error('Response body is empty');
      }
      
      // Handle streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      let done = false;
      let accumulatedContent = '';
      let timeoutId: NodeJS.Timeout | null = null;
      
      // Set a timeout to detect stalled streams
      const setupTimeout = () => {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          console.warn('Stream timeout - no data received for 10 seconds');
          done = true;
          reader.cancel('Timeout').catch(console.error);
          if (accumulatedContent) {
            toast.success('Email content generated');
          } else {
            toast.error('Generation timed out. Please try again.');
          }
        }, 10000); // 10 second timeout
      };
      
      setupTimeout();
      
      try {
        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          
          if (value) {
            // Reset timeout on data received
            setupTimeout();
            
            const chunk = decoder.decode(value, { stream: !done });
            accumulatedContent += chunk;
            setStreamedResponse(accumulatedContent);
          }
        }
        
        // Clear timeout when done
        if (timeoutId) clearTimeout(timeoutId);
        
        if (accumulatedContent) {
          toast.success('Email content generated successfully');
        }
      } catch (streamError) {
        console.error('Stream reading error:', streamError);
        if (accumulatedContent) {
          // We have partial content, so we can still use it
          toast.warning('Generation incomplete, but some content was generated');
        } else {
          throw new Error('Failed to read response stream');
        }
      }
    } catch (error) {
      console.error('Error generating email content:', error);
      toast.error(`${error instanceof Error ? error.message : 'Failed to generate email content. Please try again.'}`);
      
      // Provide fallback content if generation fails completely
      if (!streamedResponse) {
        setStreamedResponse(
          `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>We've missed you!</h2>
            <p>Hi there,</p>
            <p>We noticed it's been a while since you've used our AI coding assistant. Our platform has been updated with several new features that make coding even more efficient.</p>
            <p>We'd love to have you back! Just click the button below to log in and see what's new.</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="#" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Log In Now</a>
            </div>
            <p>Best regards,<br>The Team</p>
          </div>`
        );
        toast.info('Using fallback template');
      }
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Use the generated content
  const useGeneratedContent = () => {
    onChange(streamedResponse);
    setActiveTab('edit');
    toast.success('Content applied to editor');
  };
  
  return (
    <Card className="border">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="edit">Edit Content</TabsTrigger>
          <TabsTrigger value="generate">Generate with AI</TabsTrigger>
        </TabsList>
        
        <TabsContent value="edit" className="p-4">
          <Textarea
            placeholder="Write your email content here..."
            className="min-h-[200px]"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        </TabsContent>
        
        <TabsContent value="generate" className="p-4 space-y-4">
          <div className="space-y-2">
            <Textarea
              placeholder="Describe the email you want to generate..."
              className="min-h-[100px]"
              value={generationPrompt}
              onChange={(e) => setGenerationPrompt(e.target.value)}
            />
            
            <div className="flex flex-wrap gap-2">
              {TEMPLATE_SUGGESTIONS.map((template, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setGenerationPrompt(template.prompt)}
                >
                  {template.name}
                </Button>
              ))}
            </div>
            
            <Button
              onClick={() => generateEmailContent(generationPrompt)}
              disabled={isGenerating || !generationPrompt.trim()}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="mr-2 h-4 w-4" />
                  Generate Email
                </>
              )}
            </Button>
          </div>
          
          {streamedResponse && (
            <div className="space-y-2">
              <div 
                className="border rounded-md p-4 bg-muted/50 min-h-[200px] overflow-auto max-h-[400px]"
                dangerouslySetInnerHTML={{ __html: streamedResponse }}
              />
              <div className="text-xs text-muted-foreground text-right">
                {streamedResponse.length} characters generated
              </div>
              
              <Button
                onClick={useGeneratedContent}
                className="w-full"
              >
                Use This Content
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </Card>
  );
}
