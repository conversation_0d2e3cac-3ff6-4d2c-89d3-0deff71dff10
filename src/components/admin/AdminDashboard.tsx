'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import OverviewTab from './OverviewTab';
import ProjectsTab from './ProjectsTab';
import MessagesTab from './MessagesTab';
import UsersTab from './UsersTab';

interface AdminDashboardProps {
  initialProjectsData?: any;
  initialAnalyticsData?: any;
  initialUsersData?: any;
  initialMessagesData?: any;
}

export default function AdminDashboard({ 
  initialProjectsData,
  initialAnalyticsData,
  initialUsersData,
  initialMessagesData
}: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Admin Dashboard</CardTitle>
          <CardDescription>
            Welcome to the Magically Admin Dashboard. Here you can monitor all projects, messages, and user activity.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-7">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="projects">Projects</TabsTrigger>
              <TabsTrigger value="messages">Messages</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
              <TabsTrigger value="errors">Errors</TabsTrigger>
              <TabsTrigger value="chat">Chat</TabsTrigger>
            </TabsList>
            <TabsContent value="overview" className="pt-6">
              <OverviewTab initialData={initialAnalyticsData} />
            </TabsContent>
            <TabsContent value="projects" className="pt-6">
              <ProjectsTab initialData={initialProjectsData} />
            </TabsContent>
            <TabsContent value="messages" className="pt-6">
              <MessagesTab initialData={initialMessagesData} />
            </TabsContent>
            <TabsContent value="users" className="pt-6">
              <UsersTab initialData={initialUsersData} />
            </TabsContent>
            <TabsContent value="subscriptions" className="pt-6">
              <div className="flex justify-center">
                <Button 
                  onClick={() => window.location.href = '/admin/subscriptions'}
                  className="w-full max-w-md"
                >
                  Go to Subscriptions Dashboard
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="errors" className="pt-6">
              <div className="flex justify-center">
                <Button 
                  onClick={() => window.location.href = '/admin/error-analytics'}
                  className="w-full max-w-md"
                >
                  Go to Error Analytics Dashboard
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="chat" className="pt-6">
              <div className="flex justify-center">
                <Button 
                  onClick={() => window.location.href = '/admin/chat'}
                  className="w-full max-w-md"
                >
                  Go to Admin Chat
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
