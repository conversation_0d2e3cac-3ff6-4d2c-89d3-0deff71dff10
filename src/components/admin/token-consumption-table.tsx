'use client';

import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { format } from 'date-fns';

interface TokenConsumption {
  id: string;
  model: string;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  createdAt: string;
  inputCost: number;
  outputCost: number;
  totalCost: number;
  creditsConsumed: number;
  discountedCredits: number;
  discountReason: string;
  cachingDiscount?: number;
  cacheDiscountPercent?: number;
  subtotal?: number;
}

interface TokenConsumptionTableProps {
  data: TokenConsumption[];
  onSort: (field: string) => void;
  sortField: string;
  sortDirection: 'asc' | 'desc';
}

export function TokenConsumptionTable({
  data,
  onSort,
  sortField,
  sortDirection
}: TokenConsumptionTableProps) {
  // Format functions
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatCost = (cost: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(cost);
  };

  return (
    <div className="rounded-md border">
      <Table className="text-xs">
        <TableHeader>
          <TableRow className="h-8">
            <TableHead 
              className="cursor-pointer"
              onClick={() => onSort('createdAt')}
            >
              Date
            </TableHead>
            <TableHead>Input Tokens</TableHead>
            <TableHead>Output Tokens</TableHead>
            <TableHead 
              className="cursor-pointer"
              onClick={() => onSort('totalTokens')}
            >
              Total Tokens
            </TableHead>
            <TableHead>Credit Discount</TableHead>
            <TableHead>Credits Used</TableHead>
            <TableHead>Subtotal Cost</TableHead>
            <TableHead>Cache %</TableHead>
            <TableHead>Cache Discount</TableHead>
            <TableHead 
              className="cursor-pointer"
              onClick={() => onSort('totalCost')}
            >
              Total Cost
            </TableHead>
            <TableHead>Cost per Credit</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="text-xs">
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={11} className="text-center py-4">
                No token consumption data found
              </TableCell>
            </TableRow>
          ) : (
            data.map((item) => (
              <TableRow key={item.id} className="h-8">
                <TableCell>
                  {formatDate(item.createdAt)}
                </TableCell>
                <TableCell>
                  {formatNumber(item.promptTokens)}
                </TableCell>
                <TableCell>
                  {formatNumber(item.completionTokens)}
                </TableCell>
                <TableCell>
                  {formatNumber(item.totalTokens)}
                </TableCell>
                <TableCell>
                  {item.discountedCredits ? (
                    <span className="text-green-500 font-medium">
                      {formatNumber(item.discountedCredits || 0)}
                    </span>
                  ) : (
                    '0'
                  )}
                </TableCell>
                <TableCell>
                  {formatNumber(item.creditsConsumed || 0)}
                </TableCell>
                <TableCell>
                  {formatCost(item.subtotal || item.totalCost)}
                </TableCell>
                <TableCell>
                  {item.cacheDiscountPercent ? (
                    <span className="text-blue-600 font-medium">
                      {item.cacheDiscountPercent.toFixed(2)}%
                    </span>
                  ) : (
                    '0%'
                  )}
                </TableCell>
                <TableCell>
                  {item.cachingDiscount ? (
                    <span className="text-green-500 text-xs">
                      {formatCost(Math.abs(item.cachingDiscount))}
                    </span>
                  ) : (
                    '$0.00'
                  )}
                </TableCell>
                <TableCell>
                  <span className="font-medium">
                    {formatCost(item.totalCost)}
                  </span>
                </TableCell>
                <TableCell>
                  {item.creditsConsumed > 0 ? (
                    <span className="font-medium">
                      ${(item.totalCost / (item.creditsConsumed || 1)).toFixed(4)}
                    </span>
                  ) : (
                    '$0.00'
                  )}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
