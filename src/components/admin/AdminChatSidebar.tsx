'use client';

import React, {useEffect, useState} from 'react';
import { Button } from '@/components/ui/button';
import { Plus, MessageSquare, X, Pencil, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import useSWR from 'swr';
import { fetcher } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';

// Type definitions for chat items
interface ChatItem {
    id: string;
    title: string;
    createdAt: Date;
}

interface AdminChatSidebarProps {
    currentChatId: string;
    isOpen: boolean;
    onToggle: () => void;
    onChatSelect: (id: string) => void;
}

export default function AdminChatSidebar({
    currentChatId,
    isOpen,
    onToggle,
    onChatSelect
}: AdminChatSidebarProps) {
    const [editingChatId, setEditingChatId] = useState<string | null>(null);
    const [newTitle, setNewTitle] = useState('');
    const isMobile = useIsMobile();

    // Fetch chats using SWR
    const { data: chats = [], mutate } = useSWR<ChatItem[]>(
        '/api/admin/chats',
        fetcher,
        {
            onError: (error) => {
                console.error('Failed to load chats:', error);
                toast.error('Failed to load chats');
            }
        }
    );

    useEffect(() => {
        console.log('chats',chats)
    }, [chats])

    const startEditingTitle = (chat: ChatItem) => {
        setEditingChatId(chat.id);
        setNewTitle(chat.title);
    };

    const saveTitle = async (chatId: string) => {
        try {
            const response = await fetch(`/api/admin/chat/${chatId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ title: newTitle }),
            });

            if (!response.ok) {
                throw new Error('Failed to update chat title');
            }

            // Get updated chat from response
            const data = await response.json();
            
            // Update local data
            mutate(
                chats.map(chat => 
                    chat.id === chatId ? { ...chat, title: newTitle } : chat
                ),
                false
            );
            
            toast.success('Chat title updated');
            setEditingChatId(null);
        } catch (error) {
            console.error('Error updating chat title:', error);
            toast.error('Failed to update chat title');
        }
    };

    return (
        <div
            className={cn(
                "flex flex-col h-full w-64 border-r border-border/50 bg-background",
                isMobile && "fixed inset-y-0 left-0 z-50 transition-transform duration-300",
                isMobile && !isOpen && "-translate-x-full"
            )}
        >
            {/* Mobile close button */}
            {isMobile && (
                <div className="flex justify-end p-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onToggle}
                    >
                        <X className="h-5 w-5" />
                    </Button>
                </div>
            )}

            {/* Sidebar header */}
            <div className="px-3 py-2 border-b border-border/50">
                <h2 className="text-lg font-semibold">Admin Chat</h2>
            </div>

            {/* Chat list */}
            <div className="flex-1 overflow-y-auto">
                {chats.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                        No chats yet
                    </div>
                ) : (
                    <div className="space-y-1 p-2">
                        {chats.map(chat => (
                            <div key={chat.id} className="flex items-center group">
                                <Link 
                                    href={`/admin/chat/${chat.id}`} 
                                    className="block flex-1"
                                    onClick={(e) => {
                                        // If we're editing, don't navigate
                                        if (editingChatId === chat.id) {
                                            e.preventDefault();
                                            return;
                                        }
                                        onChatSelect(chat.id);
                                    }}
                                >
                                    <Button
                                        variant={currentChatId === chat.id ? "secondary" : "ghost"}
                                        className={cn(
                                            "w-full justify-start text-left text-sm h-auto py-2 px-3",
                                            currentChatId === chat.id && "bg-secondary"
                                        )}
                                    >
                                        <MessageSquare className="h-4 w-4 mr-2 flex-shrink-0"/>
                                        {editingChatId === chat.id ? (
                                            <Input
                                                value={newTitle}
                                                onChange={(e) => setNewTitle(e.target.value)}
                                                className="h-6 py-0 px-1 text-sm"
                                                onClick={(e) => e.stopPropagation()}
                                                onMouseDown={(e) => e.stopPropagation()}
                                                autoFocus
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        saveTitle(chat.id);
                                                    } else if (e.key === 'Escape') {
                                                        e.preventDefault();
                                                        setEditingChatId(null);
                                                    }
                                                }}
                                            />
                                        ) : (
                                            <span className="truncate max-w-[200px] text-[10px]">{chat.title}</span>
                                        )}
                                    </Button>
                                </Link>
                                
                                {editingChatId === chat.id ? (
                                    <Button 
                                        variant="ghost" 
                                        size="sm" 
                                        className="h-6 w-6 p-0 opacity-100"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            saveTitle(chat.id);
                                        }}
                                    >
                                        <Check className="h-3 w-3" />
                                    </Button>
                                ) : (
                                    <Button 
                                        variant="ghost" 
                                        size="sm" 
                                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            startEditingTitle(chat);
                                        }}
                                    >
                                        <Pencil className="h-3 w-3" />
                                    </Button>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* New chat button */}
            <div className="p-3 border-t border-border/50">
                <Link href="/admin/chat" className="block w-full">
                    <Button className="w-full">
                        <Plus className="h-4 w-4 mr-2"/>
                        New Chat
                    </Button>
                </Link>
            </div>
        </div>
    );
}
