'use client';

import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLocalStorage } from "usehooks-ts";

export type ModelOption = 'anthropic/claude-sonnet-4' | 'google/gemini-2.5-pro-preview' | 'openai/o4-mini-high';

const models: { id: ModelOption; name: string }[] = [
  { id: 'anthropic/claude-sonnet-4', name: 'Claude Sonnet 4' },
  { id: 'google/gemini-2.5-pro-preview', name: 'Gemini 2.5 Pro' },
  { id: 'openai/o4-mini-high', name: 'Openai o3 mini high' },
];

interface ModelSelectorProps {
  onModelChange: (model: ModelOption) => void;
}

export default function ModelSelector({ onModelChange }: ModelSelectorProps) {
  const [selectedModel, setSelectedModel] = useLocalStorage<ModelOption>(
    'admin-chat-model', 
    'anthropic/claude-sonnet-4'
  );

  const handleModelChange = (value: string) => {
    const modelValue = value as ModelOption;
    setSelectedModel(modelValue);
    onModelChange(modelValue);
  };

  return (
    <Select value={selectedModel} onValueChange={handleModelChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select model" />
      </SelectTrigger>
      <SelectContent>
        {models.map((model) => (
          <SelectItem key={model.id} value={model.id}>
            {model.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
