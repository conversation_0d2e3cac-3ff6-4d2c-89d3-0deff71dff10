'use client';

import React, {useRef, useState, useEffect} from 'react';
import {useChat} from '@ai-sdk/react';
import {ArrowRight, Menu} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {toast} from 'sonner';
import {Messages} from '@/components/base/messages';
import {MultimodalInput} from '@/components/base/multimodal-input';
import {useIsMobile} from '@/hooks/use-mobile';
import {cn, generateUUID} from '@/lib/utils';
import {UIMessage} from "@ai-sdk/ui-utils";
import {Message} from "ai";
import AdminChatSidebar from './AdminChatSidebar';
import ModelSelector, { ModelOption } from './ModelSelector';
import { useLocalStorage } from "usehooks-ts";
import {useRouter} from 'next/navigation';
import {DataStreamHandler} from "@/components/base/data-stream-handler";

// Type definition for chat items
interface ChatItem {
    id: string;
    title: string;
    createdAt: Date;
}

export default function AdminChat({initialMessages, chatId}: {
    initialMessages: (Message & { finishReason: string } | UIMessage)[],
    chatId: string
}) {
    const messagesContainerRef = useRef<HTMLDivElement>(null);
    const lastProcessedIndex = useRef(-1);
    const router = useRouter();
    
    // State for chat functionality
    const [attachments, setAttachments] = useState<any[]>([]);
    const [droppedFiles, setDroppedFiles] = useState<any[]>([]);
    const [localComponentContexts, setLocalComponentContexts] = useState<any[]>([]);
    const [isLoadingPrevious, setIsLoadingPrevious] = useState(false);
    const [selectMode, setSelectMode] = useState(false);
    
    // UI state
    const isMobile = useIsMobile();
    const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
    
    // Chat state
    const [currentChatId, setCurrentChatId] = useState(chatId);
    
    // Model selection state
    const [selectedModel, setSelectedModel] = useLocalStorage<ModelOption>(
        'admin-chat-model', 
        'anthropic/claude-sonnet-4'
    );

    // Use the current chat ID in the useChat hook
    const {
        messages,
        input,
        setInput,
        handleSubmit,
        isLoading,
        error,
        reload,
        append,
        setMessages,
        status,
        data
    } = useChat({
        api: '/api/admin/chat',
        id: currentChatId,
        generateId: generateUUID,
        streamProtocol: "data",
        body: {
            chatId: currentChatId,
            model: selectedModel, // Pass the selected model to the API
        },
        sendExtraMessageFields: true,
        initialMessages: initialMessages,
        onError: (error) => {
            console.error('Chat error:', error);
            toast.error('An error occurred during chat');
        }
    });

    // Function to scroll to bottom of messages
    const scrollToBottom = () => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    };

    // Scroll to bottom when messages change
    useEffect(() => {
        scrollToBottom();
        console.log('messages', messages)
    }, [messages]);
    
    // // Process data stream updates
    // useEffect(() => {
    //     if (!dataStream?.length) return;
    //
    //     const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
    //     lastProcessedIndex.current = dataStream.length - 1;
    //
    //     newDeltas.forEach((delta: any) => {
    //         // Process any data stream deltas here if needed
    //         console.log('Processing data stream delta:', delta);
    //         // This ensures the stream is being processed properly
    //     });
    // }, [dataStream]);

    // Handle model change
    const handleModelChange = (model: ModelOption) => {
        setSelectedModel(model);
        toast.success(`Model changed to ${model}`);
    };

    // Handle sidebar toggle
    const toggleSidebar = () => {
        setSidebarOpen(!sidebarOpen);
    };

    // When chat ID changes, update the current chat ID
    useEffect(() => {
        if (chatId) {
            setCurrentChatId(chatId);
        }
    }, [chatId]);

    // Handle chat selection from sidebar
    const handleChatSelect = (id: string) => {
        if (id !== currentChatId) {
            router.push(`/admin/chat/${id}`);
            if (isMobile) {
                setSidebarOpen(false);
            }
        }
    };

    const handleSubmitFromInput = (event: any, options: any) => {
        handleSubmit(event, options)

        setTimeout(() => {
            window.history.replaceState({}, '', `/admin/chat/${chatId}`);
        },  0)
    }

    // Placeholder functions for message actions
    const setSnackError = (error: any) => {
        toast.error(error.message || 'An error occurred');
    };
    
    const onVersionClick = () => {};
    const onActionClick = () => {};
    const onVisualSelectionClicked = () => {};
    const showLoadAllConfirmation = () => {};

    // Mock values for Messages component
    const votes: { chatId: string; projectId: string | null; messageId: string; isUpvoted: boolean; }[] = [];
    const isReadonly = false;
    const isBlockVisible = false;
    const hasMoreMessages = false;
    const totalUserMessages = 0;

    return (
        <div className="flex h-screen w-full overflow-hidden">
            {/* Sidebar */}
            <AdminChatSidebar 
                isOpen={sidebarOpen}
                onToggle={toggleSidebar}
                currentChatId={currentChatId}
                onChatSelect={handleChatSelect}
            />

            {/* Main chat area */}
            <div className="flex flex-col flex-1 h-full overflow-hidden">
                {/* Header with model selector */}
                <div className="flex items-center justify-between border-b border-border/50 px-4 py-2">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={toggleSidebar}
                        className="md:hidden"
                    >
                        <Menu className="h-5 w-5" />
                    </Button>
                    <div className="flex-1 flex justify-end">
                        <ModelSelector
                            onModelChange={handleModelChange}
                        />
                    </div>
                </div>

                {/* Chat messages */}
                <div className="flex-1 overflow-hidden relative">
                    <div
                        ref={messagesContainerRef}
                        className="h-full overflow-y-auto px-4"
                    >
                        {messages.length === 0 ? (
                            <div className="flex h-full items-center justify-center">
                                <div className="text-center">
                                    <h3 className="text-lg font-semibold">Welcome to Admin Chat</h3>
                                    <p className="text-muted-foreground">
                                        Start a conversation by typing a message below
                                    </p>
                                </div>
                            </div>
                        ) : (
                            <Messages
                                votes={votes}
                                chatId={chatId}
                                projectId={generateUUID()}
                                isLoading={isLoading}
                                messages={messages}
                                setMessages={setMessages}
                                reload={reload}
                                isReadonly={isReadonly}
                                isBlockVisible={isBlockVisible}
                                append={append}
                                status={status}
                                setInput={setInput}
                                setAttachments={setAttachments}
                                onVersionClick={onVersionClick}
                                lastActiveVersionId={''}
                                onActionClick={onActionClick}
                                setSnackError={setSnackError}
                                hasMoreMessages={false}
                                totalUserMessages={totalUserMessages}
                                onLoadPrevious={showLoadAllConfirmation}
                                isLoadingPrevious={isLoadingPrevious}
                                removeActions={true}
                            />
                        )}
                    </div>
                </div>

                {/* Mobile scroll to bottom button */}
                {isMobile && messages.length > 3 && (
                    <div className="absolute bottom-24 right-4 z-10">
                        <Button
                            size="sm"
                            variant="secondary"
                            className="h-10 w-10 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90"
                            onClick={scrollToBottom}
                        >
                            <ArrowRight className="h-5 w-5 rotate-90"/>
                        </Button>
                    </div>
                )}

                {/* Input area - with safe area padding and optimized for mobile */}
                {!isReadonly && (
                    <div
                        className="flex-shrink-0 border-t border-border/50 bg-background backdrop-blur bg-opacity-45 pb-safe pt-2"
                        style={{position: 'relative', zIndex: 5}}
                    >
                        <div className={`${isMobile ? 'px-2' : 'px-4'} py-2 mx-auto`}>
                            <MultimodalInput
                                chatId={currentChatId || "admin-chat"}
                                input={input}
                                inDesignMode={true}
                                setInput={setInput}
                                handleSubmit={handleSubmitFromInput}
                                isLoading={isLoading}
                                stop={() => {}}
                                attachments={attachments}
                                setAttachments={setAttachments}
                                messages={messages}
                                setMessages={setMessages}
                                append={append}
                                projectId="admin"
                                needsContinuation={false}
                                droppedFiles={droppedFiles}
                                onVisualSelectionClicked={onVisualSelectionClicked}
                                componentContexts={localComponentContexts}
                                onRemoveComponentContext={(id) => {}}
                                onClearComponentContexts={() => {}}
                                selectMode={selectMode}
                            />
                        </div>
                    </div>
                )}

                <DataStreamHandler id={chatId} operationChange={() => {}} onFileOpen={() => {}}/>
            </div>
        </div>
    );
}
