'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface FinancialSummaryCardProps {
  title: string;
  value: string;
  subValue?: string;
  subValueColor?: string;
  isNegative?: boolean;
}

export function FinancialSummaryCard({
  title,
  value,
  subValue,
  subValueColor = 'text-muted-foreground',
  isNegative = false
}: FinancialSummaryCardProps) {
  return (
    <Card className="h-full">
      <CardHeader className="pb-2 pt-4 px-4">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="pb-4 px-4">
        <div className={`text-xl font-bold ${isNegative ? 'text-green-500' : ''}`}>
          {value}
        </div>
        {subValue && (
          <div className={`text-xs mt-1 ${subValueColor}`}>
            {subValue}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
