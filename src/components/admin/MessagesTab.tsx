'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Import from server actions
import { searchMessageContent } from '@/app/actions/admin-actions';

interface MessagesTabProps {
  initialData?: any;
}

export default function MessagesTab({ initialData }: MessagesTabProps) {
  const [messages, setMessages] = useState<any[]>(initialData?.messages || []);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(!!initialData);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleSearch = () => {
    if (!searchTerm.trim()) return;
    
    setIsSearching(true);
    setHasSearched(true);
    
    startTransition(async () => {
      try {
        // Use server action to search messages
        const results = await searchMessageContent(searchTerm);
        setMessages(results);
      } catch (error) {
        console.error('Error searching messages:', error);
      } finally {
        setIsSearching(false);
      }
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleViewChat = (projectId: string, chatId: string) => {
    router.push(`/projects/${projectId}/chats/${chatId}`);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Message Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-6">
            <Input
              placeholder="Search messages across all projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              className="max-w-md"
            />
            <Button onClick={handleSearch} disabled={isSearching}>
              {isSearching ? 'Searching...' : 'Search'}
            </Button>
          </div>

          {hasSearched && (
            <div>
              <h3 className="text-lg font-medium mb-4">
                {messages.length} {messages.length === 1 ? 'result' : 'results'} for "{searchTerm}"
              </h3>
              
              {messages.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No messages found matching your search.
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message) => (
                    <Card key={message.id} className="overflow-hidden">
                      <div className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <span className="font-medium">{message.userName}</span>
                            <span className="text-muted-foreground mx-2">in</span>
                            <span className="font-medium">{message.projectName}</span>
                            <span className="text-muted-foreground mx-2">→</span>
                            <span className="font-medium">{message.chatTitle}</span>
                          </div>
                          <Badge variant={message.role === 'user' ? 'outline' : 'secondary'}>
                            {message.role}
                          </Badge>
                        </div>
                        
                        <div className="border rounded-md p-3 bg-muted/50 mb-2 whitespace-pre-wrap">
                          {typeof message.content.text === 'string' 
                            ? message.content.text 
                            : JSON.stringify(message.content, null, 2)}
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div className="text-sm text-muted-foreground">
                            {new Date(message.createdAt).toLocaleString()}
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleViewChat(message.projectId, message.chatId)}
                          >
                            View in Chat
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
