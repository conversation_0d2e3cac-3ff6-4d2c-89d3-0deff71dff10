'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Markdown } from '@/components/base/markdown';

// Define the message content types
type TextContent = { type: 'text'; text: string };
type MessageContent = string | TextContent[] | any;

interface MessageTraceViewerProps {
  messageTrace: {
    userMessage: {
      id: string;
      content: MessageContent;
      createdAt: string;
      chatId: string;
      projectId: string;
      userId: string;
    };
    messages: Array<{
      id: string;
      role: string;
      createdAt: string;
      content: MessageContent;
      parsedContent: any;
      tokenCount: number;
      tokenCost: number;
      toolCalls?: Array<{
        toolCallId: string;
        toolName: string;
        args: any;
      }>;
      toolResults?: Array<{
        toolCallId: string;
        toolName: string;
        result: any;
      }>;
    }>;
    stats: {
      messageCount: number;
      assistantMessageCount: number;
      toolMessageCount: number;
      writeTokens: number;
      readTokens: number;
      totalTokens: number;
      writeTokenCost: string;
      readTokenCost: string;
      totalCost: string;
    };
  };
}

export default function MessageTraceViewer({ messageTrace }: MessageTraceViewerProps) {
  const router = useRouter();
  const [expandedContent, setExpandedContent] = useState<Record<string, boolean>>({});

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Toggle content expansion
  const toggleContentExpansion = (id: string) => {
    setExpandedContent(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Extract text from message content
  const extractMessageText = (content: any): string => {
    // If content is a string, return it directly
    if (typeof content === 'string') {
      return content;
    }
    
    // If content is an array of objects with type and text properties
    if (Array.isArray(content)) {
      return content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join(' ');
    }
    
    // If content is an object with a text property
    if (content && typeof content === 'object' && 'text' in content) {
      return content.text;
    }
    
    // Fallback: stringify the content
    try {
      return JSON.stringify(content).substring(0, 100);
    } catch (e) {
      return 'Unable to display content';
    }
  };

  // Format content for display
  const formatContent = (content: any, expanded: boolean) => {
    // If it's a string, handle it directly
    if (typeof content === 'string') {
      return expanded ? (
        <Markdown>{content}</Markdown>
      ) : (
        content.substring(0, 100) + (content.length > 100 ? '...' : '')
      );
    }
    
    // If it's an array of objects with type and text properties (common message format)
    if (Array.isArray(content)) {
      const textItems = content.filter(item => 
        typeof item === 'object' && 
        item !== null && 
        'type' in item && 
        item.type === 'text' && 
        'text' in item
      );
      
      if (textItems.length > 0) {
        const extractedText = textItems
          .map(item => (item as {text: string}).text)
          .join('\n\n');
        
        return expanded ? (
          <Markdown>{extractedText}</Markdown>
        ) : (
          extractedText.substring(0, 100) + (extractedText.length > 100 ? '...' : '')
        );
      }
    }
    
    // For other types of content (objects, etc.)
    const contentStr = JSON.stringify(content, null, 2);
    return expanded ? (
      <pre className="p-4 rounded-md overflow-x-auto border">
        {contentStr}
      </pre>
    ) : (
      contentStr.substring(0, 100) + (contentStr.length > 100 ? '...' : '')
    );
  };

  return (
    <div className="space-y-6">
      <div className="mb-4 flex justify-between items-center">
        <Link href="/admin/token-analytics">
          <Button variant="outline" size="sm">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Token Analytics
          </Button>
        </Link>
        
        <Link
          href={`/projects/${messageTrace.userMessage.projectId}/chats/${messageTrace.userMessage.chatId}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          <Button 
            variant="secondary" 
            size="sm"
          >
            View Original Chat
          </Button>
        </Link>
      </div>

      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle>Conversation Turn Summary</CardTitle>
          <CardDescription>
            Token usage summary for this conversation turn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground">Write Tokens (Assistant)</div>
              <div className="text-2xl font-bold">{messageTrace.stats.writeTokens.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Cost: ${messageTrace.stats.writeTokenCost}</div>
            </div>
            <div className="p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground">Read Tokens (Tool Results)</div>
              <div className="text-2xl font-bold">{messageTrace.stats.readTokens.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Cost: ${messageTrace.stats.readTokenCost}</div>
            </div>
            <div className="p-4 rounded-lg border border-blue-500">
              <div className="text-sm font-medium">Total Tokens</div>
              <div className="text-2xl font-bold">{messageTrace.stats.totalTokens.toLocaleString()}</div>
              <div className="text-sm font-medium">Total Cost: ${messageTrace.stats.totalCost}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground">Message Counts</div>
              <div className="grid grid-cols-3 gap-2 mt-2">
                <div>
                  <div className="text-sm">Assistant</div>
                  <div className="text-xl font-bold">{messageTrace.stats.assistantMessageCount}</div>
                </div>
                <div>
                  <div className="text-sm">Tool</div>
                  <div className="text-xl font-bold">{messageTrace.stats.toolMessageCount}</div>
                </div>
                <div>
                  <div className="text-sm">Total</div>
                  <div className="text-xl font-bold">{messageTrace.stats.messageCount}</div>
                </div>
              </div>
            </div>
            <div className="p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground">User Message</div>
              <div className="mt-2 text-sm">
                <div><strong>Time:</strong> {formatDate(messageTrace.userMessage.createdAt)}</div>
                <div>
                  <strong>Chat ID:</strong> {messageTrace.userMessage.chatId}
                  <Link
                    href={`/projects/${messageTrace.userMessage.projectId}/chats/${messageTrace.userMessage.chatId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ml-2"
                  >
                    <Button 
                      variant="link" 
                      size="sm" 
                      className="p-0 h-auto"
                    >
                      View Chat
                    </Button>
                  </Link>
                </div>
                <div><strong>User ID:</strong> {messageTrace.userMessage.userId}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Message Card */}
      <Card>
        <CardHeader>
          <CardTitle>User Message</CardTitle>
          <CardDescription>
            Original user message that triggered this conversation turn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 rounded-md border">
            {(() => {
              const content = messageTrace.userMessage.content;
              
              if (typeof content === 'string') {
                return <Markdown>{content}</Markdown>;
              }
              
              if (Array.isArray(content)) {
                const textItems = content.filter(item => 
                  typeof item === 'object' && 
                  item !== null && 
                  'type' in item && 
                  item.type === 'text' && 
                  'text' in item
                );
                
                if (textItems.length > 0) {
                  const extractedText = textItems
                    .map(item => (item as {text: string}).text)
                    .join('\n\n');
                  return <Markdown>{extractedText}</Markdown>;
                }
              }
              
              // Fallback for other content types
              return JSON.stringify(content, null, 2);
            })()}
          </div>
        </CardContent>
      </Card>

      {/* Message Trace Accordion */}
      <Card>
        <CardHeader>
          <CardTitle>Message Trace</CardTitle>
          <CardDescription>
            Detailed trace of all messages in this conversation turn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion type="multiple" className="w-full">
            {messageTrace.messages.map((msg, index) => (
              <AccordionItem key={msg.id} value={msg.id}>
                <AccordionTrigger className="hover:bg-slate-50 dark:hover:bg-slate-900 px-4">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <Badge variant={msg.role === 'assistant' ? 'default' : 'secondary'}>
                        {msg.role}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {formatDate(msg.createdAt)}
                      </span>
                      
                      {/* Show tool names on hover */}
                      {msg.role === 'assistant' && msg.toolCalls && msg.toolCalls.length > 0 && (
                        <div className="ml-2 flex flex-wrap gap-1">
                          {msg.toolCalls.map(tool => (
                            <Badge key={tool.toolCallId} variant="outline" className="text-xs">
                              {tool.toolName}
                            </Badge>
                          ))}
                        </div>
                      )}
                      
                      {msg.role === 'tool' && msg.toolResults && msg.toolResults.length > 0 && (
                        <div className="ml-2 flex flex-wrap gap-1">
                          {msg.toolResults.map(tool => (
                            <Badge key={tool.toolCallId} variant="outline" className="text-xs">
                              {tool.toolName}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-sm">
                        {msg.tokenCount.toLocaleString()} tokens
                      </span>
                      <span className="text-sm">
                        ${msg.tokenCost.toFixed(6)}
                      </span>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  {msg.role === 'assistant' && msg.toolCalls && (
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2">Tool Calls:</h4>
                      <div className="space-y-2">
                        {msg.toolCalls.map((toolCall, i) => (
                          <div key={toolCall.toolCallId} className="p-3 rounded-md border border-blue-500">
                            <div className="font-medium">{toolCall.toolName}</div>
                            <div className="text-xs text-slate-500 mb-1">ID: {toolCall.toolCallId}</div>
                            <div className="text-sm">
                              <strong>Args:</strong>
                              <pre className="p-2 rounded-md border mt-1 overflow-x-auto text-xs">
                                {JSON.stringify(toolCall.args, null, 2)}
                              </pre>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {msg.role === 'tool' && msg.toolResults && (
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2">Tool Results:</h4>
                      <div className="space-y-2">
                        {msg.toolResults.map((toolResult, i) => (
                          <div key={toolResult.toolCallId} className="p-3 rounded-md border border-green-500">
                            <div className="font-medium">{toolResult.toolName}</div>
                            <div className="text-xs text-slate-500 mb-1">ID: {toolResult.toolCallId}</div>
                            <div className="text-sm">
                              <strong>Result:</strong>
                              <div className="mt-1">
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => toggleContentExpansion(`result-${toolResult.toolCallId}`)}
                                  className="mb-2"
                                >
                                  {expandedContent[`result-${toolResult.toolCallId}`] ? 'Collapse' : 'Expand'} Result
                                </Button>
                                <div className="p-2 rounded-md border overflow-x-auto text-xs">
                                  {formatContent(
                                    toolResult.result, 
                                    !!expandedContent[`result-${toolResult.toolCallId}`]
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <h4 className="font-semibold mb-2">Raw Content:</h4>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => toggleContentExpansion(`content-${msg.id}`)}
                      className="mb-2"
                    >
                      {expandedContent[`content-${msg.id}`] ? 'Collapse' : 'Expand'} Content
                    </Button>
                    <div className="p-2 rounded-md border overflow-x-auto text-xs">
                      {formatContent(
                        msg.content, 
                        !!expandedContent[`content-${msg.id}`]
                      )}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
}
