'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { getTokenAnalytics } from '@/app/actions/admin-token-actions';
import { Markdown } from '@/components/base/markdown';

interface TokenAnalyticsMessage {
  id: string;
  content: string;
  createdAt: string;
  chatId: string;
  projectId: string;
  userId: string;
  assistantMessageCount: number;
  toolMessageCount: number;
  writeTokens: number;
  readTokens: number;
  totalTokens: number;
  writeTokenCost: string;
  readTokenCost: string;
  totalCost: string;
  toolCalls: Record<string, number>;
}

interface TokenAnalyticsData {
  messages: TokenAnalyticsMessage[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

interface TokenAnalyticsDashboardProps {
  initialData: TokenAnalyticsData;
}

export default function TokenAnalyticsDashboard({ initialData }: TokenAnalyticsDashboardProps) {
  const router = useRouter();
  const [data, setData] = useState<TokenAnalyticsData>(initialData);
  const [currentPage, setCurrentPage] = useState(initialData.pagination.page);
  const [pageSize] = useState(10);
  const [isLoading, setIsLoading] = useState(false);

  // Load data when page changes
  useEffect(() => {
    const loadData = async () => {
      if (currentPage === data.pagination.page) return;
      
      setIsLoading(true);
      try {
        const newData = await getTokenAnalytics({
          page: currentPage,
          pageSize: data.pagination.pageSize
        });
        setData(newData);
      } catch (error) {
        console.error('Error loading token analytics:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, [currentPage, data.pagination.page, data.pagination.pageSize]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Extract text from message content
  const extractMessageText = (content: any): string => {
    // If content is a string, return it directly
    if (typeof content === 'string') {
      return content;
    }
    
    // If content is an array of objects with type and text properties
    if (Array.isArray(content)) {
      return content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join(' ');
    }
    
    // If content is an object with a text property
    if (content && typeof content === 'object' && 'text' in content) {
      return content.text;
    }
    
    // Fallback: stringify the content
    try {
      return JSON.stringify(content).substring(0, 100);
    } catch (e) {
      return 'Unable to display content';
    }
  };

  // Format tool calls for display
  const formatToolCalls = (toolCalls: Record<string, number>) => {
    return Object.entries(toolCalls)
      .map(([tool, count]) => `${tool} (${count})`)
      .join(', ');
  };

  // Navigate to message trace page
  const viewMessageTrace = (messageId: string) => {
    router.push(`/admin/token-analytics/message/${messageId}`);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Calculate total costs
  const totalWriteTokens = data.messages.reduce((sum, msg) => sum + msg.writeTokens, 0);
  const totalReadTokens = data.messages.reduce((sum, msg) => sum + msg.readTokens, 0);
  const totalTokens = totalWriteTokens + totalReadTokens;
  
  const totalWriteCost = data.messages.reduce((sum, msg) => sum + parseFloat(msg.writeTokenCost), 0);
  const totalReadCost = data.messages.reduce((sum, msg) => sum + parseFloat(msg.readTokenCost), 0);
  const totalCost = totalWriteCost + totalReadCost;

  return (
    <div className="space-y-6">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle>Token Usage Summary</CardTitle>
          <CardDescription>
            Current page token usage summary with costs based on $15/million write tokens, $3/million read tokens
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground">Write Tokens (Assistant)</div>
              <div className="text-2xl font-bold">{totalWriteTokens.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Cost: ${totalWriteCost.toFixed(6)}</div>
            </div>
            <div className="p-4 rounded-lg border">
              <div className="text-sm text-muted-foreground">Read Tokens (Tool Results)</div>
              <div className="text-2xl font-bold">{totalReadTokens.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Cost: ${totalReadCost.toFixed(6)}</div>
            </div>
            <div className="p-4 rounded-lg border border-blue-500">
              <div className="text-sm font-medium">Total Tokens</div>
              <div className="text-2xl font-bold">{totalTokens.toLocaleString()}</div>
              <div className="text-sm font-medium">Total Cost: ${totalCost.toFixed(6)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Messages Table */}
      <Card>
        <CardHeader>
          <CardTitle>User Messages with Token Analytics</CardTitle>
          <CardDescription>
            Each row represents a user message and all the assistant/tool messages that follow in that conversation turn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  <TableHead>User Message</TableHead>
                  <TableHead>Messages</TableHead>
                  <TableHead>Write Tokens</TableHead>
                  <TableHead>Read Tokens</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead>Tool Calls</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : data.messages.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      No messages found
                    </TableCell>
                  </TableRow>
                ) : (
                  data.messages.map((message) => (
                    <TableRow key={message.id}>
                      <TableCell className="whitespace-nowrap">
                        {formatDate(message.createdAt)}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {extractMessageText(message.content)}
                      </TableCell>
                      <TableCell>
                        <div className="text-xs">
                          <div>Assistant: {message.assistantMessageCount}</div>
                          <div>Tool: {message.toolMessageCount}</div>
                          <div className="font-bold">Total: {message.assistantMessageCount + message.toolMessageCount}</div>
                        </div>
                      </TableCell>
                      <TableCell>{message.writeTokens.toLocaleString()}</TableCell>
                      <TableCell>{message.readTokens.toLocaleString()}</TableCell>
                      <TableCell className="font-bold">{message.totalTokens.toLocaleString()}</TableCell>
                      <TableCell>${parseFloat(message.totalCost).toFixed(6)}</TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {message.toolCalls && Object.keys(message.toolCalls).length > 0 
                          ? formatToolCalls(message.toolCalls) 
                          : 'None'}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => viewMessageTrace(message.id)}
                          >
                            View Trace
                          </Button>
                          <Link 
                            href={`/projects/${message.projectId}/chats/${message.chatId}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Button 
                              variant="outline" 
                              size="sm"
                            >
                              View Chat
                            </Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {data.pagination.totalPages > 1 && (
            <div className="mt-4 flex justify-center items-center gap-2">
              {currentPage > 1 && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                >
                  Previous
                </Button>
              )}
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, data.pagination.totalPages) }, (_, i) => {
                  const pageNumber = i + 1;
                  return (
                    <Button 
                      key={pageNumber}
                      variant={pageNumber === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNumber)}
                      className="w-8 h-8 p-0"
                    >
                      {pageNumber}
                    </Button>
                  );
                })}
              </div>
              
              {currentPage < data.pagination.totalPages && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                >
                  Next
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
