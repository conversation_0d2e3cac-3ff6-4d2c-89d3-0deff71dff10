'use client';

import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface CachingBreakdownItem {
  hasCaching: string;
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  cachingSavings: number;
}

interface CachingBreakdownProps {
  data: CachingBreakdownItem[];
  loading: boolean;
}

export function CachingBreakdown({ data, loading }: CachingBreakdownProps) {
  // Format functions
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatCost = (cost: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(cost);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className="h-8 w-full" />
        ))}
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-muted-foreground">
        No caching breakdown data available
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table className="text-xs">
        <TableHeader>
          <TableRow className="h-8">
            <TableHead>Caching Status</TableHead>
            <TableHead>Requests</TableHead>
            <TableHead>Total Tokens</TableHead>
            <TableHead>Total Cost</TableHead>
            <TableHead>Caching Savings</TableHead>
            <TableHead>Avg. Tokens per Request</TableHead>
            <TableHead>Cost per Request</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item) => (
            <TableRow key={item.hasCaching} className="h-8">
              <TableCell>
                <Badge 
                  className={item.hasCaching === 'Cached' ? 'bg-blue-100 text-blue-800' : ''}
                  variant={item.hasCaching === 'Cached' ? undefined : 'outline'}
                >
                  {item.hasCaching}
                </Badge>
              </TableCell>
              <TableCell>{formatNumber(item.totalRequests)}</TableCell>
              <TableCell>{formatNumber(item.totalTokens)}</TableCell>
              <TableCell>{formatCost(item.totalCost)}</TableCell>
              <TableCell>
                {item.hasCaching === 'Cached' ? (
                  <span className="text-green-500 text-xs">
                    {formatCost(Math.abs(item.cachingSavings))}
                  </span>
                ) : (
                  '$0.00'
                )}
              </TableCell>
              <TableCell>
                {item.totalRequests > 0 
                  ? formatNumber(Math.round(item.totalTokens / item.totalRequests)) 
                  : '0'}
              </TableCell>
              <TableCell>
                {item.totalRequests > 0 
                  ? formatCost(item.totalCost / item.totalRequests)
                  : '$0.00'}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
