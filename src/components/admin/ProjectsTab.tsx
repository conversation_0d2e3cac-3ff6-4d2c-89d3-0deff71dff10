'use client';

import { useState, useEffect, useCallback, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronUp, ChevronsUpDown } from 'lucide-react';
import { SortDirection, SortField } from '@/lib/db/admin-queries';
import { getProjects, getProjectLatestChat } from '@/app/actions/admin-actions';
import {Chat} from "@/lib/db/schema";

interface ProjectsTabProps {
  initialData?: any;
}

export default function ProjectsTab({ initialData }: ProjectsTabProps) {
  // Initialize state with data from server if available
  const [projects, setProjects] = useState<any[]>(initialData?.projects || []);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [loading, setLoading] = useState(!initialData);
  const [isPending, startTransition] = useTransition();
  const [pagination, setPagination] = useState(initialData?.pagination || {
    currentPage: 1,
    pageSize: 10,
    totalPages: 1,
    totalCount: 0
  });
  const [sortField, setSortField] = useState<SortField>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const router = useRouter();

  // Fetch projects with pagination and sorting using server action
  const fetchProjects = async (
    page = pagination.currentPage, 
    pageSize = pagination.pageSize, 
    field = sortField, 
    direction = sortDirection,
    search = searchTerm
  ) => {
    setLoading(true);
    startTransition(async () => {
      try {
        // Use server action to fetch data
        const result = await getProjects({
          page,
          pageSize,
          sortField: field,
          sortDirection: direction,
          searchTerm: search
        });
        
        setProjects(result.projects);
        setPagination(result.pagination);
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoading(false);
      }
    });
  };
  
  // Handle search with debouncing
  const handleSearch = () => {
    fetchProjects(1, pagination.pageSize, sortField, sortDirection, searchTerm);
  };
  
  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm !== debouncedSearchTerm) {
        setDebouncedSearchTerm(searchTerm);
        handleSearch();
      }
    }, 500);
    
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Initial fetch when component mounts and no data is provided
  useEffect(() => {
    if (projects.length === 0 && !initialData) {
      fetchProjects();
    }
  }, []);
  
  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.totalPages || newPage === pagination.currentPage) return;
    fetchProjects(newPage, pagination.pageSize, sortField, sortDirection);
  };

  // Handle sorting
  const handleSort = (field: SortField) => {
    const newDirection = field === sortField && sortDirection === 'desc' ? 'asc' : 'desc';
    setSortField(field);
    setSortDirection(newDirection);
    fetchProjects(1, pagination.pageSize, field, newDirection);
  };

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (field !== sortField) return <ChevronsUpDown className="h-4 w-4" />;
    return sortDirection === 'desc' ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />;
  };

  const handleViewProject = async (projectId: string) => {
    try {
      // Get the latest chat for this project using server action
      const latestChat = await getProjectLatestChat(projectId);
      
      if (latestChat) {
        // Navigate to the project's chat page
        // router.push(`/projects/${projectId}/chats/${latestChat.id}`);
        window.open(`/projects/${projectId}/${(latestChat as Chat).type === "app" ? "chats": "design"}/${latestChat.id}`);
      } else {
        // If no chat exists, just go to the project page
        window.open(`/projects/${projectId}`);
      }
    } catch (error) {
      console.error('Error navigating to project:', error);
    }
  };

  const isLoadingData = loading || isPending;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>All Projects</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <Input
              placeholder="Search projects by name, slug, or owner..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {pagination.totalCount > 0 ? (
                  <>Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} - {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount}</>
                ) : (
                  <>No projects found</>
                )}
              </span>
            </div>
          </div>
          
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>App</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('createdAt')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Created</span>
                      {getSortIcon('createdAt')}
                    </div>
                  </TableHead>
                  <TableHead>Last Active</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('totalChats')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Chats</span>
                      {getSortIcon('totalChats')}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('totalMessages')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Messages</span>
                      {getSortIcon('totalMessages')}
                    </div>
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingData ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      Loading projects...
                    </TableCell>
                  </TableRow>
                ) : projects.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      No projects found
                    </TableCell>
                  </TableRow>
                ) : (
                  projects.map((project) => (
                    <TableRow key={project.id} onClick={() => handleViewProject(project.id)} className="cursor-pointer">
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          {project.icon ? (
                            <img 
                              src={project.icon} 
                              alt={project.appName} 
                              className="h-8 w-8 rounded-md"
                              style={{ backgroundColor: project.primaryColor }}
                            />
                          ) : (
                            <div 
                              className="h-8 w-8 rounded-md flex items-center justify-center text-white font-bold"
                              style={{ backgroundColor: project.primaryColor || '#4F46E5'}}
                            >
                              {project.appName?.charAt(0) || '?'}
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{project.appName}</div>
                            <div className="text-sm text-muted-foreground">{project.slug}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div>{project.userName}</div>
                          <div className="text-sm text-muted-foreground">{project.userEmail}</div>
                        </div>
                      </TableCell>
                      <TableCell>{new Date(project.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>{new Date(project.lastActive).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{project.totalChats}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{project.totalMessages}</Badge>
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleViewProject(project.id)}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Pagination Controls */}
          <div className="flex items-center justify-between mt-4">
            <div>
              <select 
                className="border rounded p-1 text-sm"
                value={pagination.pageSize}
                onChange={(e) => {
                  const newPageSize = Number(e.target.value);
                  if (newPageSize !== pagination.pageSize) {
                    fetchProjects(1, newPageSize, sortField, sortDirection);
                  }
                }}
                disabled={isLoadingData}
              >
                {[10, 20, 50, 100].map(size => (
                  <option key={size} value={size}>
                    {size} per page
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.currentPage <= 1 || isLoadingData}
                onClick={() => handlePageChange(pagination.currentPage - 1)}
              >
                Previous
              </Button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum;
                  if (pagination.totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (pagination.currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (pagination.currentPage >= pagination.totalPages - 2) {
                    pageNum = pagination.totalPages - 4 + i;
                  } else {
                    pageNum = pagination.currentPage - 2 + i;
                  }
                  
                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === pagination.currentPage ? "default" : "outline"}
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => handlePageChange(pageNum)}
                      disabled={isLoadingData}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
                
                {pagination.totalPages > 5 && pagination.currentPage < pagination.totalPages - 2 && (
                  <>
                    <span className="mx-1">...</span>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => handlePageChange(pagination.totalPages)}
                      disabled={isLoadingData}
                    >
                      {pagination.totalPages}
                    </Button>
                  </>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.currentPage >= pagination.totalPages || isLoadingData}
                onClick={() => handlePageChange(pagination.currentPage + 1)}
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
