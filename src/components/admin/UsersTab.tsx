'use client';

import { useState, useEffect, useTransition } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DataTable, Column, SortDirection } from '@/components/ui/data-table';

// Import from server actions
import { getUsers } from '@/app/actions/admin-actions';
import { SortField } from '@/lib/db/admin-queries';

interface UsersTabProps {
  initialData?: any;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  createdAt: string | Date;
  updatedAt: string | Date;
  provider: string | null;
  projectCount: number;
  chatCount: number;
  messageCount: number;
  lastActive: string | Date;
}

export default function UsersTab({ initialData }: UsersTabProps) {
  const [users, setUsers] = useState<User[]>(initialData?.data || []);
  const [loading, setLoading] = useState(!initialData);
  const [isPending, startTransition] = useTransition();
  
  // Pagination state
  const [pagination, setPagination] = useState({
    pageIndex: initialData?.pagination?.page || 1,
    pageSize: initialData?.pagination?.pageSize || 10,
    pageCount: initialData?.pagination?.pageCount || 1,
    totalCount: initialData?.pagination?.totalCount || 0,
  });
  
  // Sorting state
  const [sorting, setSorting] = useState({
    sortField: 'createdAt' as SortField,
    sortDirection: 'desc' as SortDirection,
  });
  
  // Search state
  const [searchTerm, setSearchTerm] = useState('');
  
  // Filtering state
  const [filters, setFilters] = useState<Record<string, string[]>>({});
  
  const fetchUsers = async () => {
    setLoading(true);
    startTransition(async () => {
      try {
        // Use server action to fetch users with all parameters
        const result = await getUsers({
          page: pagination.pageIndex,
          pageSize: pagination.pageSize,
          sortField: sorting.sortField,
          sortDirection: sorting.sortDirection,
          searchTerm,
          filters,
        });
        
        setUsers(result.data);
        setPagination({
          pageIndex: result.pagination.page,
          pageSize: result.pagination.pageSize,
          pageCount: result.pagination.pageCount,
          totalCount: result.pagination.totalCount,
        });
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    });
  };
  
  // Only fetch if no initial data was provided
  useEffect(() => {
    if (!initialData) {
      fetchUsers();
    }
  }, [initialData]);
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, pageIndex: page }));
    fetchUsers();
  };
  
  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPagination(prev => ({ ...prev, pageSize: size, pageIndex: 1 }));
    fetchUsers();
  };
  
  // Handle sort change
  const handleSortChange = (field: string, direction: SortDirection) => {
    setSorting({ sortField: field as SortField, sortDirection: direction });
    fetchUsers();
  };
  
  // Handle search change
  const handleSearchChange = (term: string) => {
    setSearchTerm(term);
    setPagination(prev => ({ ...prev, pageIndex: 1 }));
    fetchUsers();
  };
  
  // Handle filter change
  const handleFilterChange = (columnId: string, values: string[]) => {
    setFilters(prev => ({ ...prev, [columnId]: values }));
    setPagination(prev => ({ ...prev, pageIndex: 1 }));
    fetchUsers();
  };

  // Define columns for the DataTable
  const columns: Column<User>[] = [
    {
      id: 'name',
      header: 'User',
      sortable: true,
      cell: (row) => (
        <div>
          <div className="font-medium">{row.name || 'Unnamed User'}</div>
          <div className="text-sm text-muted-foreground">{row.email}</div>
        </div>
      ),
    },
    {
      id: 'createdAt',
      header: 'Sign-up Date',
      sortable: true,
      cell: (row) => new Date(row.createdAt).toLocaleDateString(),
    },
    {
      id: 'lastActive',
      header: 'Last Active',
      sortable: true,
      cell: (row) => new Date(row.lastActive).toLocaleDateString(),
    },
    {
      id: 'projectCount',
      header: 'Projects',
      sortable: true,
      cell: (row) => <Badge variant="outline">{row.projectCount}</Badge>,
    },
    {
      id: 'chatCount',
      header: 'Chats',
      sortable: true,
      cell: (row) => <Badge variant="outline">{row.chatCount}</Badge>,
    },
    {
      id: 'messageCount',
      header: 'Messages',
      sortable: true,
      cell: (row) => <Badge variant="outline">{row.messageCount}</Badge>,
    },
    {
      id: 'provider',
      header: 'Auth Provider',
      sortable: true,
      filterable: true,
      filterOptions: [
        { label: 'Google', value: 'google' },
        { label: 'Credentials', value: 'credentials' },
      ],
      cell: (row) => (
        <Badge variant={row.provider === 'google' ? 'secondary' : 'default'}>
          {row.provider || 'unknown'}
        </Badge>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={users}
            loading={loading}
            pagination={{
              pageIndex: pagination.pageIndex,
              pageSize: pagination.pageSize,
              pageCount: pagination.pageCount,
              onPageChange: handlePageChange,
              onPageSizeChange: handlePageSizeChange,
            }}
            sorting={{
              sortField: sorting.sortField,
              sortDirection: sorting.sortDirection,
              onSortChange: handleSortChange,
            }}
            searching={{
              searchTerm,
              onSearchChange: handleSearchChange,
            }}
            filtering={{
              filters,
              onFilterChange: handleFilterChange,
            }}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>User Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <StatCard title="Total Users" value={pagination.totalCount} />
            <StatCard 
              title="Google Auth Users" 
              value={users.filter(u => u.provider === 'google').length} 
              percentage={users.length > 0 ? Math.round((users.filter(u => u.provider === 'google').length / users.length) * 100) : 0}
            />
            <StatCard 
              title="Credentials Users" 
              value={users.filter(u => u.provider === 'credentials').length}
              percentage={users.length > 0 ? Math.round((users.filter(u => u.provider === 'credentials').length / users.length) * 100) : 0}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function StatCard({ title, value, percentage }: { title: string; value: number; percentage?: number }) {
  return (
    <div className="bg-card rounded-lg border p-4">
      <h3 className="text-sm font-medium text-muted-foreground mb-2">{title}</h3>
      <div className="text-2xl font-bold">{value}</div>
      {percentage !== undefined && (
        <p className="text-xs text-muted-foreground">{percentage}% of total users</p>
      )}
    </div>
  );
}
