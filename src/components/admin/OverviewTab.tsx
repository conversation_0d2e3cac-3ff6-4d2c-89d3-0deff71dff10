'use client';

import { useEffect, useState, useTransition } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';

// Import from server actions
import { getAnalytics } from '@/app/actions/admin-actions';

interface OverviewTabProps {
  initialData?: any;
}

export default function OverviewTab({ initialData }: OverviewTabProps) {
  const [analytics, setAnalytics] = useState<any>(initialData || null);
  const [loading, setLoading] = useState(!initialData);
  const [isPending, startTransition] = useTransition();

  const fetchAnalytics = async () => {
    setLoading(true);
    startTransition(async () => {
      try {
        // Use server action to fetch analytics
        const data = await getAnalytics();
        setAnalytics(data);
      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    });
  };
  
  // Only fetch if no initial data was provided
  useEffect(() => {
    if (!initialData) {
      fetchAnalytics();
    }
  }, [initialData]);

  if (loading) {
    return <div className="flex justify-center my-8">Loading analytics...</div>;
  }

  if (!analytics) {
    return <div className="flex justify-center my-8">Failed to load analytics data.</div>;
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard title="Total Projects" value={analytics.totalProjects} subtitle={`+${analytics.newProjectsLast30Days} in last 30 days`} />
        <MetricCard title="Total Users" value={analytics.totalUsers} subtitle={`+${analytics.newUsersLast30Days} in last 30 days`} />
        <MetricCard title="Total Chats" value={analytics.totalChats} subtitle="Across all projects" />
        <MetricCard title="Total Messages" value={analytics.totalMessages} subtitle="Across all chats" />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Message Activity (Last 30 Days)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={analytics.messagesByDay}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return `${date.getMonth() + 1}/${date.getDate()}`;
                    }}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => {
                      const date = new Date(value);
                      return `Date: ${date.toLocaleDateString()}`;
                    }}
                  />
                  <Line type="monotone" dataKey="count" stroke="#8884d8" activeDot={{ r: 8 }} name="Messages" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Growth Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={[...analytics.projectsByDay, ...analytics.usersByDay]
                  .reduce((acc: any[], curr: {date: string; count: number}) => {
                    const existingItem = acc.find((item: {date: string}) => item.date === curr.date);
                    if (existingItem) {
                      if ('count' in curr && curr.date in existingItem) {
                        existingItem.userCount = curr.count;
                      } else {
                        existingItem.projectCount = curr.count;
                      }
                    } else {
                      if ('count' in analytics.projectsByDay.find((item: {date: string}) => item.date === curr.date) || {}) {
                        acc.push({ date: curr.date, projectCount: curr.count, userCount: 0 });
                      } else {
                        acc.push({ date: curr.date, projectCount: 0, userCount: curr.count });
                      }
                    }
                    return acc;
                  }, [] as any[])
                  .sort((a: {date: string}, b: {date: string}) => new Date(a.date).getTime() - new Date(b.date).getTime())
                }>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return `${date.getMonth() + 1}/${date.getDate()}`;
                    }}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => {
                      const date = new Date(value);
                      return `Date: ${date.toLocaleDateString()}`;
                    }}
                  />
                  <Line type="monotone" dataKey="projectCount" stroke="#82ca9d" name="Projects" />
                  <Line type="monotone" dataKey="userCount" stroke="#8884d8" name="Users" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Top Active Projects</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={analytics.topActiveProjects}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="messageCount" fill="#8884d8" name="Messages" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function MetricCard({ title, value, subtitle }: { title: string; value: number; subtitle: string }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value.toLocaleString()}</div>
        <p className="text-xs text-muted-foreground">{subtitle}</p>
      </CardContent>
    </Card>
  );
}
