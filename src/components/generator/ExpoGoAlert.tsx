'use client';

import React from 'react';
import {Info, QrCode} from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import QRCode from 'react-qr-code';
import {observer} from "mobx-react-lite";

interface ExpoGoAlertProps {
    playerUrl?: string;
    size?: "default" | "icon"
}

export const ExpoGoAlert: React.FC<ExpoGoAlertProps> = observer(({playerUrl, size = "default"}) => {
    if (!playerUrl) return null;

    return (
        <Dialog>
            <DialogTrigger asChild>
                <div className="flex items-center gap-2 cursor-pointer group">
                    <div className={`flex items-center  px-3 py-2 bg-black ${size === "icon"? 'rounded-full': 'rounded-md h-9'}`}>
                        {
                            size === "icon" ?
                                <QrCode className="h-8 w-8 text-white/70 group-hover:text-white/90 transition-colors"/>
                                :
                                <>
                                    <Info className="h-4 w-4 mr-2 text-white/70"/>
                                    <span className="text-sm text-white/90 mr-2">Your app will look much better in Expo Go</span>
                                    <QrCode
                                        className="h-4 w-4 text-white/70 group-hover:text-white/90 transition-colors"/>
                                </>

                        }

                    </div>
                </div>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Scan with Expo Go</DialogTitle>
                    <DialogDescription>
                        Open this Snack in your Expo Go app by scanning this QR code.
                    </DialogDescription>
                </DialogHeader>
                <div className="flex items-center justify-center p-6">
                    <QRCode
                        value={playerUrl}
                        size={256}
                        style={{height: "auto", maxWidth: "100%", width: "100%"}}
                        viewBox={`0 0 256 256`}
                    />
                </div>
                <a className="text-center text-xs text-muted-foreground space-y-2" href={playerUrl} target="_blank">
                    <p>Or open directly in Expo Go:</p>
                    <pre
                        className="relative rounded bg-muted p-2 font-mono text-[10px] leading-normal overflow-x-auto whitespace-pre-wrap break-all max-h-20 overflow-y-auto">
                        {playerUrl}
                    </pre>
                </a>
            </DialogContent>
        </Dialog>
    );
});
