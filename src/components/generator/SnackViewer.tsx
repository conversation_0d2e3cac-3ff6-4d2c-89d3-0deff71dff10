'use client';

import React, { useEffect, useRef, useState } from 'react';
import { PhoneFrame } from './PhoneFrame';
import { Snack } from 'snack-sdk';

interface SnackViewerProps {
    snackData?: { id: string; channel: string, url: string };
    onScreenshotTaken?: (screenshot: string) => void;
}

export const SnackViewer: React.FC<SnackViewerProps> = ({ snackData, onScreenshotTaken }) => {
    const frameId = 'snack-phone-frame';
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const [isLoading, setIsLoading] = useState(true);

    // const [snack] = useState(
    //     () =>
    //         new Snack({
    //             // ...defaults,
    //             // disabled: !process.browser,
    //             // codeChangesDelay: INITIAL_CODE_CHANGES_DELAY,
    //             // verbose: VERBOSE,
    //             // webPreviewRef: process.browser ? webPreviewRef : undefined,
    //             // // Optionally you can run the transports inside a web-worker.
    //             // // Encoding data messages for large apps might take several milliseconds
    //             // // and can cause stutter when executed often.
    //             // ...(USE_WORKERS ? { createTransport: createWorkerTransport } : {}),
    //         })
    // );

    if (!snackData?.id) return null;

    // Try the embedded preview URL format
    const playerUrl = `https://snack.expo.dev/embedded/${snackData.id}?platform=web&preview=true&theme=light&supportedPlatforms=web`;

    useEffect(() => {
        const iframe = iframeRef.current;
        if (!iframe) return;

        const handleLoad = () => {
            console.log('Iframe loaded');
            setIsLoading(false);
        };


        iframe.addEventListener('load', handleLoad);
        return () => iframe.removeEventListener('load', handleLoad);
    }, []);

    return (
        <div className="w-full h-full flex items-center justify-center overflow-hidden bg-transparent">
{/*            <PhoneFrame frameId={frameId}>*/}
{/*                <div className="relative w-full h-full">*/}
{/*                    {isLoading && (*/}
{/*                        <div className="absolute inset-0 flex items-center justify-center bg-white z-20">*/}
{/*                            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>*/}
{/*                        </div>*/}
{/*                    )}*/}
{/*                    <style jsx>{`*/}
{/*                        .snack-wrapper {*/}
{/*                            width: 100%;*/}
{/*                            height: 100%;*/}
{/*                            overflow: hidden;*/}
{/*                            position: relative;*/}
{/*                            background: white;*/}
{/*                        }*/}

{/*                        .snack-iframe {*/}
{/*=                            height: 100%;*/}
{/*                            border: none;*/}
{/*                            width: 600px;*/}
{/*                            height: 100%;*/}
{/*                            position: absolute;*/}
{/*                            top: 0;*/}
{/*                            left: -117%;*/}
{/*                            transform-origin: center;*/}
{/*                            -webkit-transform-origin: center;*/}
{/*                            backface-visibility: hidden;*/}
{/*                            -webkit-backface-visibility: hidden;*/}
{/*                            opacity: ${isLoading ? 0 : 1};*/}
{/*                            transition: opacity 0.3s ease;*/}
{/*                        }*/}

{/*                        .snack-overlay {*/}
{/*                            position: absolute;*/}
{/*                            top: 0;*/}
{/*                            left: 0;*/}
{/*                            right: 0;*/}
{/*                            height: 40px;*/}
{/*                            background-color: white;*/}
{/*                            z-index: 10;*/}
{/*                        }*/}

{/*                        .snack-bottom-overlay {*/}
{/*                            position: absolute;*/}
{/*                            bottom: 0;*/}
{/*                            left: 0;*/}
{/*                            right: 0;*/}
{/*                            height: 30px;*/}
{/*                            background-color: white;*/}
{/*                            z-index: 10;*/}
{/*                        }*/}
{/*                    `}</style>*/}
{/*                    <div className="snack-wrapper">*/}
{/*                        <div className="snack-overlay" />*/}
{/*                        <div className="snack-bottom-overlay" />*/}
{/*                        <iframe*/}
{/*                            ref={iframeRef}*/}
{/*                            className="snack-iframe"*/}
{/*                            src={playerUrl}*/}
{/*                            allow="geolocation; camera; microphone"*/}
{/*                            sandbox="allow-forms allow-modals allow-popups allow-presentation allow-same-origin allow-scripts"*/}
{/*                        />*/}
{/*                    </div>*/}
{/*                </div>*/}
{/*            </PhoneFrame>*/}
        </div>
    );
};
