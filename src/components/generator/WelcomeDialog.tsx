"use client";
import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, Sparkles, Zap, Code, Bot, Wrench, ExternalLink, Info, AlertCircle, Loader2 } from "lucide-react";
import { motion, AnimatePresence } from 'framer-motion';

interface WelcomeDialogProps {
  onClose: () => void;
}

interface FeatureItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  videoUrl?: string; // Optional video URL for feature illustration
}

export const WelcomeDialog: React.FC<WelcomeDialogProps> = ({ onClose }) => {
  const [open, setOpen] = useState(true);
  const [currentFeature, setCurrentFeature] = useState(0);
  const [videosPreloaded, setVideosPreloaded] = useState(false);
  const [showFeatures, setShowFeatures] = useState(false);
  const [loadingPhase, setLoadingPhase] = useState<'initial' | 'title' | 'hint' | 'ready'>('initial');
  const videoRef = useRef<HTMLVideoElement>(null);

  const features: FeatureItem[] = [
    {
      id: 'error-fixing',
      title: "Auto error Fixing is Free",
      description: "We run multiple background checks to ensure the sanity of your project and attempt to fix bug at multiple stages. All auto bug fixing is free of cost.",
      icon: <Wrench className="h-5 w-5" />,
      color: "from-green-500 to-emerald-700",
      videoUrl: "https://storage.magically.life/assets/1-error-fix.mp4"
    },
    {
      id: 'restore-checkpoints',
      title: "AI made a mistake? Restore a previous version",
      description: "Easily restore your project to any previous state with our checkpoint system. Never worry about losing your progress again.",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M12 7v5l4 2"/></svg>,
      color: "from-primary to-purple-800",
      videoUrl: "https://storage.magically.life/assets/2-restore-checkpoints.mp4"
    },
    {
      id: 'test-on-phone',
      title: "Test on Your Phone or Instant preview",
      description: "Scan a QR code to instantly test your app on your phone. See your changes in real-time on the actual device.",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"/><path d="M12 18h.01"/></svg>,
      color: "from-blue-500 to-blue-800",
      videoUrl: "https://storage.magically.life/assets/3-test-on-phone.mp4"
    },
    {
      id: 'publish-to-web',
      title: "Publish to Web, Android and iOS",
      description: "Share your app with anyone by publishing it to the web. Get a unique URL that works on any device.",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M12 2a10 10 0 1 0 10 10 10 10 0 0 0-10-10Z"/><path d="m2 12 5-3 2 4.5L13 8l2.5 3L20 7"/></svg>,
      color: "from-yellow-500 to-amber-700",
      videoUrl: "https://storage.magically.life/assets/4-publish-to-web.mp4"
    },
    {
      id: 'supabase-connect',
      title: "Supabase Integration",
      description: "Connect your app to Supabase for authentication, database, and storage. Build full-stack apps with ease.",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M20 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2Z"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>,
      color: "from-pink-500 to-rose-700",
      videoUrl: "https://storage.magically.life/assets/5-supabase-connect.mp4"
    }
  ];

  useEffect(() => {
    // Check if the user has seen the dialog before
    const hasSeenWelcome = localStorage.getItem('hasSeenWelcomeDialog');
    if (hasSeenWelcome) {
      setOpen(false);
      return;
    }
    
    // Sequence the loading phases
    setTimeout(() => setLoadingPhase('title'), 1000);
    setTimeout(() => setLoadingPhase('hint'), 2000);
    setTimeout(() => setLoadingPhase('ready'), 3000);
    
    // Automatically transition to features screen after loading completes
    setTimeout(() => setShowFeatures(true), 5000);
    
    // Prefetch videos in background
    const prefetchVideos = async () => {
      try {
        const promises = features.map(feature => {
          if (feature.videoUrl) {
            return new Promise((resolve, reject) => {
              const video = document.createElement('video');
              video.preload = 'auto';
              video.src = feature.videoUrl!;
              video.onloadeddata = () => resolve(feature.id);
              video.onerror = () => reject(`Failed to load ${feature.id} video`);
            });
          }
          return Promise.resolve(feature.id);
        });
        
        await Promise.all(promises);
        setVideosPreloaded(true);
      } catch (error) {
        console.error('Error prefetching videos:', error);
        // Continue showing dialog even if prefetch fails
        setVideosPreloaded(true);
      }
    };
    
    prefetchVideos();
  }, []);

  const handleClose = () => {
    setOpen(false);
    onClose();
  };

  const handleSkip = () => {
    localStorage.setItem('hasSeenWelcomeDialog', 'true');
    setOpen(false);
    onClose();
  };



  const handleNext = () => {
    if (currentFeature < features.length - 1) {
      setCurrentFeature(currentFeature + 1);
    } else {
      handleClose();
    }
  };

  const handlePrevious = () => {
    if (currentFeature > 0) {
      setCurrentFeature(currentFeature - 1);
    }
  };

  const currentFeatureItem = features[currentFeature];

  return (
    <Dialog open={open}

            onOpenChange={(isOpen) => {
      if (!isOpen) handleClose();
    }}>
      <DialogContent className="sm:max-w-[500px] bg-background text-foreground p-4">
        <button
          onClick={handleClose}
          className="absolute right-3 top-3 text-muted-foreground hover:text-foreground text-sm"
        >
          ✕
        </button>

        {!showFeatures ? (
          // Loading screen
          <div className="py-6 space-y-6 flex flex-col items-center justify-center min-h-[300px]">
            <div className="space-y-4 w-full max-w-[350px] mx-auto transition-all duration-500 ease-in-out transform">
              {/* Spinner */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-3.5 w-3.5 text-accent animate-spin" />
                    <span className="text-muted-foreground">Generating your app</span>
                  </div>
                  <div className="text-[10px] text-muted-foreground">
                    Est. time: 2-3 minutes
                  </div>
                </div>
              </div>
              
              {/* Title - animated in */}
              <div className={`transition-all duration-700 ease-in-out transform ${loadingPhase === 'initial' ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'}`}>
                <h3 className="text-lg font-medium mb-1">magically</h3>
                <p className="text-sm text-muted-foreground">Creating your React Native app...</p>
              </div>
              
              {/* Feature highlights - animated in */}
              <div className={`transition-all duration-700 ease-in-out transform ${loadingPhase === 'initial' || loadingPhase === 'title' ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'}`}>
                <div className="space-y-3 mt-4">
                  <div className="bg-accent/10 border border-accent/20 rounded-md p-3">
                    <div className="flex gap-2 items-start">
                      <div className="p-1.5 rounded-md bg-accent/20 text-accent">
                        <Zap className="h-3.5 w-3.5" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-accent mb-0.5">Free Error Fixing</p>
                        <p className="text-xs text-accent/80">Fix bugs in your app without using any credits</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-background/80 border border-border/30 rounded-md p-2">
                      <div className="flex gap-2 items-start">
                        <div className="p-1 rounded-md bg-primary/10">
                          <Sparkles className="h-3 w-3 text-primary" />
                        </div>
                        <p className="text-xs font-medium">Instant Preview</p>
                      </div>
                    </div>
                    <div className="bg-background/80 border border-border/30 rounded-md p-2">
                      <div className="flex gap-2 items-start">
                        <div className="p-1 rounded-md bg-primary/10">
                          <ExternalLink className="h-3 w-3 text-primary" />
                        </div>
                        <p className="text-xs font-medium">Web & Mobile</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Progress and auto-transition indicator */}
              <div className={`pt-4 transition-all duration-700 ease-in-out transform ${loadingPhase !== 'ready' ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'}`}>
                <div className="w-full h-1 bg-muted/30 rounded-full overflow-hidden mb-3">
                  <div 
                    className="h-full bg-accent rounded-full" 
                    style={{
                      width: '0%',
                      animation: 'progressBarAnimation 2s linear forwards'
                    }}
                  />
                </div>
                <style>
                  {`
                    @keyframes progressBarAnimation {
                      0% { width: 0%; }
                      100% { width: 100%; }
                    }
                  `}
                </style>
                <p className="text-[11px] text-center text-muted-foreground animate-pulse">
                  Loading features...
                </p>
              </div>
            </div>
          </div>
        ) : (
          // Features screen
          <div className="space-y-4 max-h-[80vh] overflow-y-auto animate-in fade-in-0 slide-in-from-bottom-2 duration-300">
            <div>
              <DialogTitle className="text-base font-medium mb-1">
                While we generate your app...
              </DialogTitle>
              <DialogDescription className="text-xs text-muted-foreground">
                Here are some features that make Magically special
              </DialogDescription>
            </div>

            {/* Current Feature */}
            <div className="mb-4">
              <div className="text-xs text-muted-foreground mb-2">Feature {currentFeature + 1} of {features.length}</div>
              
              <div className="flex flex-col p-3 rounded-md border border-border bg-background/5">
                {/* Feature title */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="p-1.5 rounded-md bg-accent/10">
                      {currentFeatureItem.icon}
                    </div>
                    <div className="font-medium text-accent">{currentFeatureItem.title}</div>
                  </div>
                </div>

                {/* Video */}
                {currentFeatureItem.videoUrl && (
                  <div className="flex justify-center mb-3 rounded-md overflow-hidden border border-border">
                    <video 
                      ref={videoRef}
                      src={currentFeatureItem.videoUrl} 
                      className="w-full h-auto rounded object-cover"
                      autoPlay
                      muted
                      loop
                      playsInline // Prevents iOS from going fullscreen
                      preload="auto"
                      controls={false}
                    />
                  </div>
                )}
                
                {/* Description */}
                <p className="text-sm text-muted-foreground mb-2">
                  {currentFeatureItem.description}
                </p>
              </div>
            </div>

            {/* Feature navigation dots */}
            <div className="flex justify-center gap-1 py-2">
              {features.map((feature, index) => (
                <button
                  key={feature.id}
                  className={`h-2 w-2 rounded-full transition-all duration-200 ${
                    index === currentFeature 
                      ? 'bg-accent' 
                      : 'bg-muted hover:bg-muted-foreground/50'
                  }`}
                  onClick={() => setCurrentFeature(index)}
                  aria-label={`View feature ${feature.title}`}
                />
              ))}
            </div>

            {/* Feature list */}
            <div className="grid grid-cols-5 gap-1 border-t border-border pt-3 mt-2">
              {features.map((feature, index) => (
                <button
                  key={feature.id}
                  onClick={() => setCurrentFeature(index)}
                  className={`flex flex-col items-center p-1.5 rounded transition-colors ${index === currentFeature 
                    ? 'bg-accent/10 text-accent' 
                    : 'hover:bg-muted/50'}`}
                >
                  <div className={`flex-shrink-0 p-1 rounded-full ${index === currentFeature ? 'bg-accent/10' : ''}`}>
                    {feature.icon}
                  </div>
                  <span className="text-[10px] font-medium text-center mt-1 leading-tight">{feature.title.split(' ')[0]}</span>
                </button>
              ))}
            </div>
          </div>
        )}
        
        {showFeatures && (
          <div className="flex items-center justify-between border-t border-border pt-3 mt-2">
            <Button 
              variant="ghost" 
              onClick={handleSkip}
              size="sm"
            >
              Skip
            </Button>
            
            <div className="flex gap-2">
              {currentFeature > 0 && (
                <Button 
                  variant="outline" 
                  onClick={handlePrevious}
                  size="sm"
                >
                  Previous
                </Button>
              )}
              <Button 
                onClick={currentFeature < features.length - 1 ? handleNext : () => {
                  localStorage.setItem('hasSeenWelcomeDialog', 'true');
                  handleClose();
                }}
                className="bg-accent hover:bg-accent/90 text-accent-foreground"
                size="sm"
              >
                {currentFeature < features.length - 1 ? 'Next' : 'Check App Progress'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
