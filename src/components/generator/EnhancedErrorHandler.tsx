import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { <PERSON><PERSON>, <PERSON>rk<PERSON>, MessageSquare } from 'lucide-react';
import { toast } from 'sonner';
import { But<PERSON> } from "@/components/ui/button";
import { DiscussWithAI } from '../discuss/DiscussWithAI';
import { ProjectSession } from '@/stores/ProjectSessionStore';

interface EnhancedErrorHandlerProps {
  session: ProjectSession;
  projectId: string;
  chatId: string;
}

export const EnhancedErrorHandler = observer(({ session, projectId, chatId }: EnhancedErrorHandlerProps) => {
  // Always define hooks at the top level
  const [isDiscussOpen, setIsDiscussOpen] = useState(false);
  const toastShownRef = useRef(false);
  
  // Safely compute values that depend on session
  const hasError = !!session.snackError && 
                  session.state.status !== "streaming" && 
                  !session.snackError.handled;
                  
  // Only compute these values if there's an error
  const errorMessage = hasError && session.snackError ? session.snackError.message : '';
  const errorType = hasError && session.snackError ? (session.snackError.type || 'default') : '';
  const timestamp = hasError && session.snackError ? session.snackError.timestamp : 0;
  
  // Format the error message for the discussion
  const formattedError = hasError ? 
    `${errorMessage}\n\nError Type: ${errorType}\nTimestamp: ${new Date(timestamp).toLocaleString()}` : '';
  
  // Get relevant files - for now just use the active file
  const relevantFiles =  session.fileTree;
  
  const handleApplySolution = (solution: string) => {
    // Set the solution as the current message
    session.currentMessage = solution;
    
    // Mark the error as handled
    session.markErrorAsHandled();
    
    // Close the dialog
    setIsDiscussOpen(false);
    
    toast.success('Solution applied! Send your message to fix the error.');
  };
  
  const handleDirectFix = () => {
    // Use the existing error fix function
    session.handleErrorFix();
    toast.dismiss();
  };
  
  // Use useEffect to show the toast notification only once when an error is detected
  useEffect(() => {
    // Reset the toast shown flag when there's no error to show
    if (!hasError) {
      toastShownRef.current = false;
      return;
    }
    
    // Only show the toast if it hasn't been shown for this error yet
    if (hasError && !toastShownRef.current) {
      toastShownRef.current = true;
      
      toast(
        <div className="flex flex-col gap-4">
          <div className="flex items-start gap-3">
            <Bot className="h-5 w-5 text-primary mt-1" />
            <div>
              <h3 className="font-medium">Error Detected</h3>
              <p className="text-sm text-muted-foreground mt-1">
                {errorMessage.length > 100 
                  ? `${errorMessage.substring(0, 100)}...` 
                  : errorMessage}
              </p>
            </div>
          </div>
          <div className="flex gap-2 justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsDiscussOpen(true);
                toast.dismiss();
              }}
            >
              Discuss Error
              <MessageSquare className="h-4 w-4 ml-2" />
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleDirectFix}
            >
              Fix with AI
              <Sparkles className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>,
        {
          duration: Infinity,
          dismissible: true,
          position: "bottom-right",
        }
      );
    }
  }, [hasError, errorMessage, handleDirectFix]);
  
  // Don't render anything if there's no error
  if (!hasError) {
    return null;
  }
  
  return (
    <DiscussWithAI
      isOpen={isDiscussOpen}
      onClose={() => setIsDiscussOpen(false)}
      initialMessage={formattedError}
      type="error-fix"
      chatId={chatId}
      projectId={projectId}
      onApplySolution={handleApplySolution}
      relevantFiles={relevantFiles}
      metadata={{ errorType, timestamp: hasError && session.snackError ? session.snackError.timestamp : 0 }}
    />
  );
});
