import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    Di<PERSON>Title,
    DialogFooter,
} from '@/components/ui/dialog';
import {Button} from '@/components/ui/button';
import {Checkbox} from '@/components/ui/checkbox';
import {Separator} from '@/components/ui/separator';
import {Badge} from '@/components/ui/badge';
import Image from 'next/image';
import {Loader2, ExternalLink, AlertCircle, Plus, Check, Info, Calendar, Database} from 'lucide-react';
import {observer} from 'mobx-react-lite';
import {useStores} from '@/stores/utils/useStores';
import {toast} from "sonner";
import {Alert, AlertDescription} from "@/components/ui/alert";
import {cn} from '@/lib/utils';
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from '@/components/ui/tooltip';
import {format} from 'date-fns';

interface IntegrationDialogProps {
    providerId: string;
    open: boolean;
    onOpenChange: (open: boolean) => void;
    chatId?: string;
}

interface ProjectData {
    id: string;
    name: string;
    status: string;
    region: string;
    database: {
        host: string;
        version: string;
        postgres_engine: string;
        release_channel: string;
    };
    created_at: string;
    selected?: boolean;
}

export const IntegrationDialog = observer(({
    providerId,
    open,
    onOpenChange,
    chatId
}: IntegrationDialogProps) => {
    const {integrationStore} = useStores();
    const linkState = integrationStore.getProjectLinkState(chatId || '');
    const {isLinking} = linkState;

    // Reset link state when dialog closes
    React.useEffect(() => {
        if (!open && chatId) {
            integrationStore.projectLinkState.delete(chatId);
        }
    }, [open, chatId, integrationStore]);
    
    const provider = integrationStore.providers.find(p => p.id === providerId);

    if (!provider) return null;

    React.useEffect(() => {
        if (open) {
            integrationStore.fetchConnection(providerId);
        }
    }, [open, providerId]);

    React.useEffect(() => {
        if (integrationStore.getConnection(providerId)) {
            integrationStore.fetchDatabases(providerId);
        }
    }, [providerId, integrationStore.getConnection(providerId)]);

    const handleConnect = () => {
        const width = 600;
        const height = 800;
        const left = window.screenX + (window.outerWidth - width) / 2;
        const top = window.screenY + (window.outerHeight - height) / 2;

        const popup = window.open(
            integrationStore.getAuthUrl(providerId),
            'Connect Integration',
            `width=${width},height=${height},left=${left},top=${top},toolbar=0,location=0,status=0,menubar=0,scrollbars=1,resizable=1`
        );

        // Poll for popup close and check connection
        const pollTimer = setInterval(() => {
            if (popup?.closed) {
                clearInterval(pollTimer);
                integrationStore.fetchConnection(providerId);
            }
        }, 500);
    };

    const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
    const [isConfirming, setIsConfirming] = useState(false);
    
    const handleSelectProject = (projectId: string) => {
        setSelectedProjectId(projectId === selectedProjectId ? null : projectId);
    };

    const handleLinkProject = async () => {
        if (chatId && selectedProjectId) {
            setIsConfirming(true);
            try {
                await integrationStore.linkProject(chatId, selectedProjectId);
                toast.success(`Successfully linked project`);
                onOpenChange(false);
            } catch (error) {
                toast.error(error instanceof Error ? error.message : 'Failed to link project');
            } finally {
                setIsConfirming(false);
            }
        }
    };

    const handleDisconnect = async () => {
        try {
            await integrationStore.disconnect(providerId);
            onOpenChange(false);
        } catch (error) {
            // Handle error
        }
    };

    const connection = integrationStore.getConnection(providerId);
    const databases = integrationStore.getDatabases(providerId);
    const isLoadingConnection = integrationStore.isLoadingConnection(providerId);
    const isLoadingDatabases = integrationStore.isLoadingDatabases(providerId);

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px] p-4">
                <DialogHeader>
                    <div className="flex items-center gap-2">
                        <Image
                            src={provider.logo}
                            alt={provider.name}
                            width={24}
                            height={24}
                        />
                        <DialogTitle>{provider.name} Integration</DialogTitle>
                    </div>
                    <DialogDescription>
                        {provider.description}
                    </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                    {isLoadingConnection ? (
                        <div className="flex justify-center">
                            <Loader2 className="h-6 w-6 animate-spin text-zinc-500"/>
                        </div>
                    ) : !connection ? (
                        <div className="space-y-4">
                            <p className="text-sm text-zinc-500">
                                Connect your {provider.name} project to get started.
                            </p>
                            <Button onClick={handleConnect} className="w-full">
                                Connect {provider.name}
                            </Button>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <p className="text-sm font-medium">Connected</p>
                                <Button
                                    variant="ghost"
                                    onClick={handleDisconnect}
                                >
                                    Disconnect
                                </Button>
                            </div>

                            <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                    <h4 className="text-xs font-medium">Your Projects</h4>
                                    <div className="flex items-center gap-2">
                                        <a 
                                            href="https://app.supabase.com/projects" 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
                                        >
                                            <ExternalLink className="h-3 w-3" />
                                            <span>View All</span>
                                        </a>
                                        <a 
                                            href="https://app.supabase.com/new/new-project" 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
                                        >
                                            <Plus className="h-3 w-3" />
                                            <span>New Project</span>
                                        </a>
                                    </div>
                                </div>
                                <Separator className="my-2" />
                                {isLoadingDatabases ? (
                                    <div className="flex justify-center py-4">
                                        <Loader2 className="h-5 w-5 animate-spin text-zinc-500"/>
                                        <span className="ml-2 text-xs text-muted-foreground">Loading projects...</span>
                                    </div>
                                ) : databases?.length ? (
                                    <div className="space-y-3">
                                        <div className="rounded-md border overflow-hidden">
                                            {databases.map((dbItem) => {
                                                // Cast to ProjectData to access all properties
                                                const db = dbItem as unknown as ProjectData;
                                                // Check if project is active
                                                const isActive = db.status === 'ACTIVE_HEALTHY' || db.status === 'ACTIVE';
                                                const projectUrl = `https://app.supabase.com/project/${db.id}`;
                                                const createdDate = new Date(db.created_at);
                                                const formattedDate = format(createdDate, 'MMM d, yyyy');
                                                
                                                return (
                                                    <div
                                                        key={db.id}
                                                        className={cn(
                                                            "flex items-start gap-3 p-3 border-b last:border-b-0 border-border/20 transition-colors",
                                                            isActive ? "hover:bg-muted/30 cursor-pointer" : "opacity-70",
                                                            selectedProjectId === db.id && "bg-muted/40 hover:bg-muted/50"
                                                        )}
                                                        onClick={() => isActive && handleSelectProject(db.id)}
                                                    >
                                                        {isActive && (
                                                            <div className="pt-0.5">
                                                                <Checkbox 
                                                                    checked={selectedProjectId === db.id}
                                                                    onCheckedChange={() => handleSelectProject(db.id)}
                                                                    className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                                                />
                                                            </div>
                                                        )}
                                                        
                                                        <div className="flex-1 min-w-0">
                                                            <div className="flex items-center gap-2">
                                                                <span className="font-medium text-sm truncate">{db.name}</span>
                                                                <Badge 
                                                                    variant={isActive ? "default" : "outline"}
                                                                    className={cn("text-[10px] px-1 py-0 h-4", 
                                                                        isActive ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400" : 
                                                                                "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400")}
                                                                >
                                                                    {isActive ? "Active" : "Inactive"}
                                                                </Badge>
                                                                <a 
                                                                    href={projectUrl} 
                                                                    target="_blank" 
                                                                    rel="noopener noreferrer"
                                                                    onClick={(e) => e.stopPropagation()}
                                                                    className="text-muted-foreground hover:text-primary transition-colors"
                                                                >
                                                                    <ExternalLink className="h-3 w-3" />
                                                                </a>
                                                            </div>
                                                            
                                                            <div className="flex flex-wrap gap-x-4 gap-y-1 mt-1 text-xs text-muted-foreground">
                                                                {db.database && (
                                                                    <div className="flex items-center gap-1">
                                                                        <Database className="h-3 w-3" />
                                                                        <span>{db.database.postgres_engine} ({db.database.version})</span>
                                                                    </div>
                                                                )}
                                                                <div className="flex items-center gap-1">
                                                                    <Calendar className="h-3 w-3" />
                                                                    <span>{formattedDate}</span>
                                                                </div>
                                                                {db.region && (
                                                                    <div className="flex items-center gap-1">
                                                                        <Info className="h-3 w-3" />
                                                                        <span>{db.region}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                        
                                                        {!isActive && (
                                                            <a 
                                                                href={projectUrl} 
                                                                target="_blank" 
                                                                rel="noopener noreferrer"
                                                                onClick={(e) => e.stopPropagation()}
                                                                className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
                                                            >
                                                                <AlertCircle className="h-3 w-3" />
                                                                Restore Project
                                                            </a>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                        
                                        {selectedProjectId && chatId && (
                                            <div className="flex justify-end mt-4">
                                                <Button
                                                    onClick={handleLinkProject}
                                                    disabled={isConfirming || !selectedProjectId}
                                                    className="flex items-center gap-1"
                                                >
                                                    {isConfirming ? (
                                                        <>
                                                            <Loader2 className="h-4 w-4 animate-spin"/>
                                                            Linking...
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Check className="h-4 w-4" />
                                                            Confirm & Link Project
                                                        </>
                                                    )}
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div className="py-8 text-center space-y-3">
                                        <p className="text-sm text-zinc-500">No projects found.</p>
                                        <a 
                                            href="https://app.supabase.com/new/new-project" 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1 justify-center"
                                        >
                                            <Plus className="h-3 w-3" />
                                            <span>Create a new project</span>
                                        </a>
                                    </div>
                                )}
                                {chatId && selectedProjectId && (
                                    <Alert variant="default" className="py-2 mt-3">
                                        <div className="flex items-start gap-2">
                                            <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                            <AlertDescription className="text-xs">
                                                Linking a project will set up authentication, database, and storage configurations. 
                                                This action will configure your project for use with magically.
                                            </AlertDescription>
                                        </div>
                                    </Alert>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
});
