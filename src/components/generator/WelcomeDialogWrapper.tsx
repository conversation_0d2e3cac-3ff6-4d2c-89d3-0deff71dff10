"use client";
import React, { useState, useEffect } from 'react';
import { WelcomeDialog } from './WelcomeDialog';
import { useStores } from '@/stores/utils/useStores';

interface WelcomeDialogWrapperProps {
  onSeen?: () => void;
}

export const WelcomeDialogWrapper: React.FC<WelcomeDialogWrapperProps> = ({ onSeen }) => {
  const [showDialog, setShowDialog] = useState(false);

  useEffect(() => {
    // Only run this effect client-side
    if (typeof window === 'undefined') return;
    
    // Add a small delay to ensure the page loads first
    const timer = setTimeout(() => {
      setShowDialog(true);
      console.log('Showing welcome dialog');
    }, 6000);
    
    return () => clearTimeout(timer);
  }, []);
  
  const handleClose = () => {
    setShowDialog(false);
    // Notify parent component that dialog has been seen
    if (onSeen) {
      onSeen();
    }
  };
  
  return showDialog ? <WelcomeDialog onClose={handleClose} /> : null;
};
