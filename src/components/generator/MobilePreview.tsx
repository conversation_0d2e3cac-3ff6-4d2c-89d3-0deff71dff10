'use client';

import React from 'react';

interface MobilePreviewProps {
    children: React.ReactNode;
    frameId?: string;
}

export const MobilePreview: React.FC<MobilePreviewProps> = ({
    children,
    frameId = 'mobile-preview'
}) => {
    return (
        <div id={frameId} className="flex flex-1 w-full h-full">
            <div className="flex flex-col w-full h-full">
                {children}
            </div>
        </div>
    );
};
