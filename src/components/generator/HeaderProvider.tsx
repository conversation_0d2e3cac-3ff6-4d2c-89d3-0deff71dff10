'use client';

import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { Header } from './Header';
import { usePathname, useParams } from 'next/navigation';
import { Chat } from '@/lib/db/schema';

/**
 * HeaderProvider is a client component that wraps the Header
 * and provides it with the necessary context and state.
 * It extracts chatId and activeTab from the URL path.
 */
const HeaderProviderComponent = ({ children, chatId, isInitial, chat, projectId, showActions }: { children: React.ReactNode, chatId?: string, chat?: Chat, isInitial?: boolean, projectId: string, showActions?: boolean }) => {
  const { generatorStore } = useStores();
  const pathname = usePathname();
  // const params = useParams();
  const [activeTab, setActiveTab] = useState<'chat' | 'settings' | 'deployments'>('chat');
  const [visibility, setVisibility] = useState<'public' | 'private'>('private');
  const [chatData, setChatData] = useState<Chat | null>(null);

  useEffect(() => {
    // console.log('params', params)
    if (!pathname) return;
    
    // Extract chatId from URL path
    // Pattern: /generator/:id or /generator/:id/settings or /generator/:id/deployments
    const match = pathname.match(/\/generator\/([^\/]+)(?:\/([^\/]+))?/);
    
    if (match) {
    //   const id = match[1];
      const tab = match[2];
      

      // Determine active tab from URL
      if (tab === 'settings') {
        setActiveTab('settings');
      } else if (tab === 'deployments') {
        setActiveTab('deployments');
      } else {
        setActiveTab('chat');
      }
    }

    // Initialize or get session from GeneratorStore
    if (chatId && !generatorStore.getActiveSession(chatId)) {
      generatorStore.createSession(chatId, {
        isInitial: isInitial || false
      });
    }

    // Set active session
    if (chatId) {
      generatorStore.setActiveSession(chatId);

      // Get visibility from session
      const session = generatorStore.getActiveSession(chatId);
      if (session && !chat) {
        // Get chat data from API to set visibility and get project info
        fetch(`/api/chat/${chatId}`)
            .then(response => response.json())
            .then(data => {
              if (data && data.chat) {
                setVisibility(data.chat.visibility as 'public' | 'private');
                setChatData(data.chat);
              }
            })
            .catch(error => {
              console.error('Error fetching chat data:', error);
            });
      }
    }
  }, [pathname, generatorStore]);

  // Only render the Header if we have a chatId
  return (
    <div className="h-[100dvh] w-screen bg-background flex flex-col">
        <Header
          chatId={chatId}
          visibility={visibility}
          activeTab={activeTab}
          projectId={projectId || chat?.projectId || chatData?.projectId || undefined}
          chatTitle={chat?.title || chatData?.title}
          showActions={!!showActions}
        />
      <div className="flex-1 overflow-auto">
        {children}
      </div>
    </div>
  );
};

// Use observer to make the component reactive to MobX store changes
export const HeaderProvider = observer(HeaderProviderComponent);
