'use client';

import React from 'react';
import {PreviewPanel} from './PreviewPanel';
import {CodeBlock} from "@/types/file";
import {Button} from "@/components/ui/button";
import {ArrowLeft} from "lucide-react";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import {ExpoInstructionsDialog} from "@/components/preview/ExpoInstructionsDialog";
import { motion, AnimatePresence } from 'framer-motion';

interface PreviewDialogProps {
    onClose: () => void;
    onOperationChange: (codeBlock: CodeBlock) => void;
    chatId: string;
    projectId:string;
}

export const PreviewDialog: React.FC<PreviewDialogProps> = observer(({
    chatId,
                                                                onClose,
                                                                onOperationChange,
                                                                         projectId
                                                            }) => {
    const {generatorStore, snackStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);

    if (!session) {
        return;
    }

    return (
        <>
            {/* Overlay */}
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ 
                    opacity: generatorStore.previewOpen || session.state.previewDialogOpen ? 1 : 0,
                    pointerEvents: generatorStore.previewOpen || session.state.previewDialogOpen ? 'auto' : 'none'
                }}
                className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
                onClick={onClose}
            />
            
            {/* Bottom Sheet */}
            <motion.div
                initial={{ y: '100%' }}
                animate={{ 
                    y: generatorStore.previewOpen || session.state.previewDialogOpen ? 0 : '100%'
                }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className="fixed bottom-0 left-0 right-0 h-dvh bg-background z-50 rounded-t-xl overflow-hidden shadow-xl"
            >
                <div className="flex flex-col h-full">
                    {/* Header */}
                    <div className="flex flex-row justify-between items-center px-4 py-3 border-b">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClose}
                            className="text-muted-foreground hover:text-foreground"
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back
                        </Button>

                        <ExpoInstructionsDialog previewUrl={snackStore.getSnackState(chatId)?.url || ''} />
                    </div>

                    {/* Content */}
                    <div className="flex-1 overflow-hidden">
                        <PreviewPanel
                            chatId={chatId}
                            onOperationChange={onOperationChange}
                            className="w-full h-full"
                            projectId={projectId}
                        />
                    </div>
                </div>
            </motion.div>
        </>
    );
});
