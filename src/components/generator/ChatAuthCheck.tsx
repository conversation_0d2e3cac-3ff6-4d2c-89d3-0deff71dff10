'use client';

import { useAnonymousSession } from "@/providers/anonymous-provider";
import GeneratorPage from "./GeneratorPage";
import {Chat, Message, Project} from "@/lib/db/schema";
import { notFound } from "next/navigation";
import { useSession } from "next-auth/react";

interface ChatAuthCheckProps {
    chat: Chat;
    messagesFromDb: Message[];
    isInitial: boolean;
    initialChatId?: string;
    template?: string;
    prompt?: string;
    component?: React.ReactNode;
    activeTab?: 'chat' | 'deployments' | 'settings';
    projectId: string;
    project: Project;
    hasMoreMessages?: boolean;
    totalUserMessages?: number;
}

export function ChatAuthCheck({ chat, messagesFromDb, isInitial, initialChatId, template, project, prompt, component, activeTab = 'chat', projectId, hasMoreMessages, totalUserMessages }: ChatAuthCheckProps) {
    const { anonymousId } = useAnonymousSession();
    const { data: session } = useSession();
    let isAuthorized = false;

    if(!chat) {
        return notFound();
    }
    // For private chats, check ownership
    if (chat?.visibility === 'private') {
        if (session?.user?.id === chat.userId || anonymousId === chat.userId) {
            isAuthorized = true;
        }
    } else {
        // Public chats are always accessible
        isAuthorized = true;
    }

    if (!isAuthorized) {
        return notFound(); // or your 404 component
    }

    // If a custom component is provided, render it instead of the GeneratorPage
    if (component) {
        return component;
    }

    return (
        <GeneratorPage
            chat={chat}
            messagesFromDb={messagesFromDb}
            isInitial={isInitial}
            initialChatId={initialChatId}
            template={template}
            prompt={prompt}
            activeTab={activeTab}
            projectId={projectId}
            project={project}
        />
    );
}
