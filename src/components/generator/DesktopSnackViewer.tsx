import React, {useState, useEffect, useRef, useCallback, useMemo} from 'react';
import { Button } from '@/components/ui/button';
import {Maximize2, RefreshCcwIcon} from 'lucide-react';
import { DraggableButton } from './DraggableButton';
import { useIsMobile } from '@/hooks/use-mobile';
import {Snack, SnackListenerSubscription} from 'snack-sdk';
import { FileItem } from "@/types/file";
import { diff } from 'deep-object-diff';
import { PhoneFrame, DeviceType } from './PhoneFrame';
import { DEFAULT_DEPENDENCIES } from '@/types/editor';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { QRCodeDialog } from './QRCodeDialog';
import { Info } from 'lucide-react';
import { useContainerHeight } from '@/hooks/use-container-height';
import {ExpoGoAlert} from "@/components/generator/ExpoGoAlert";
import {useFullscreen} from "@/hooks/use-fullscreen";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import {ExpoInstructionsDialog} from "@/components/preview/ExpoInstructionsDialog";
import {useContainerDimensions} from "@/hooks/use-container-dimensions";
import {trackFeatureEvent} from "@/lib/analytics/track";
import Image from "next/image";
import {LinkButton} from "@/components/ui/link-button";
import {delay} from "@/lib/utils";
import {toast} from "sonner";
import {debounce} from "lodash";
import {DiscussButton} from "@/components/discuss";
import {Chat as ChatType} from "@/lib/db/schema";

interface DesktopSnackPreviewProps {
    chatId: string;
    projectId: string;
}

const DesktopSnackPreview = observer(({chatId, projectId}: DesktopSnackPreviewProps) => {
    const {generatorStore, snackStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);

    const webPreviewRef = useRef<HTMLIFrameElement>(null);
    const initAttemptedRef = useRef(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isClientReady, setClientReady] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isInitializing, setIsInitializing] = useState(true);
    const state = snackStore.getSnackState(chatId);
    const [dimensions, setDimensions] = useState({ height: 0, width: 0 })
    const { containerRef, getDimensions } = useContainerDimensions((newDimensions) => {
        if(newDimensions.height === 0 && dimensions.height !== 0) {
            return;
        }
        setDimensions(newDimensions);
        if (dimensions.height > 0 && !initAttemptedRef.current) {
            const success = initializeSnack();
        }
    });
    const [isRefreshed, setIsRefreshed] = useState(false);

    const isMobile = useIsMobile();

    if (!session) {
        // console.log('Returing with no session')
        return;
    }

    const toggleFullscreen = useCallback(() => {
        console.log('Attempting fullscreen toggle');
        console.log('isMobile:', isMobile);

        const element = isMobile ? webPreviewRef.current : containerRef.current;
        console.log('Selected element:', element);

        if (!element) {
            console.log('No element found');
            return;
        }

        // Log available methods
        console.log('Available methods:', {
            requestFullscreen: !!element.requestFullscreen,
            webkitRequestFullscreen: !!(element as any).webkitRequestFullscreen,
            mozRequestFullScreen: !!(element as any).mozRequestFullScreen,
            msRequestFullscreen: !!(element as any).msRequestFullscreen
        });

        if (!document.fullscreenElement) {
            try {
                if ((element as any).webkitRequestFullscreen) {
                    console.log('Using webkit fullscreen');
                    (element as any).webkitRequestFullscreen();
                } else if (element.requestFullscreen) {
                    console.log('Using standard fullscreen');
                    element.requestFullscreen();
                } else {
                    console.log('No fullscreen method available');
                }
                setIsFullscreen(true);
            } catch (err) {
                console.error('Fullscreen error:', err);
            }
        } else {
            try {
                if (document.exitFullscreen) {
                    console.log('Using standard exit fullscreen');
                    document.exitFullscreen();
                } else if ((document as any).webkitExitFullscreen) {
                    console.log('Using webkit exit fullscreen');
                    (document as any).webkitExitFullscreen();
                } else {
                    console.log('No exit fullscreen method available');
                }
                setIsFullscreen(false);
            } catch (err) {
                console.error('Exit fullscreen error:', err);
            }
        }
    }, [isMobile]);

    useEffect(() => {
        const handleFullscreenChange = () => {
            // @ts-ignore
            setIsFullscreen(!!(document.fullscreenElement || document.webkitFullscreenElement));
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);

        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
            document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
        };
    }, []);

    useEffect(() => {
        if(isClientReady) {
            toggleFullscreen();
        }
    }, [session.fullScreen]);

    let timeoutId: NodeJS.Timeout | null = null;
    let mounted = true;

    const initializeSnack = async (mounted?: any, timeoutId?: any) => {
        // Wait a bit for the iframe to be ready
        timeoutId = setTimeout(async () => {
            // console.log(`[SnackViewer] Checking iframe readiness for chatId: ${chatId}`, {
            //     hasFiles: !!session.fileTree?.length,
            //     hasDependencies: !!session.dependencies,
            //     hasWebPreviewRef: !!webPreviewRef.current,
            //     hasContentWindow: !!webPreviewRef.current?.contentWindow
            // });

            if (!webPreviewRef.current?.contentWindow) {
                // console.log(`[SnackViewer] No webPreviewRef window yet for chatId: ${chatId}, retrying...`);
                if(!isMobile){
                    initializeSnack();
                }
                return;
            }

            if(!session.fileTree?.length || !session.fileTree.some(file => file.name)) {
                // console.log(`[SnackViewer] Files not loaded yet: ${chatId} ${JSON.stringify(session.fileTree)}, retrying...`);
                initializeSnack();
                return;
            }

            try {
                // console.log(`[SnackViewer] Initializing snack for chatId: ${chatId}`);
                await snackStore.initSnack(
                    chatId,
                    session.fileTree || [],
                    session.dependencies || DEFAULT_DEPENDENCIES,
                    {
                        current: webPreviewRef.current.contentWindow
                    },
                    false
                );
                setClientReady(true);
                setIsLoading(false)
                initAttemptedRef.current = true;
                // console.log(`[SnackViewer] Successfully initialized snack for chatId: ${chatId}`, JSON.stringify(snackStore.getSnackState(chatId)));

                if (mounted) {
                    // console.log(`[SnackViewer] Setting isInitializing to false for chatId: ${chatId}`);
                    setIsInitializing(false);
                }
            } catch (err) {
                console.error(`[SnackViewer] Failed to initialize snack for chatId: ${chatId}:`, err);
            }
        }, 100); // Small delay to ensure iframe is ready
    };

    useEffect(() => {
        initializeSnack();
        return () => {
            mounted = false;
            if(timeoutId) {
                clearTimeout(timeoutId);
            }
            snackStore.cleanupSnack(chatId);
        };
    }, [chatId]);

    useEffect(() => {
        const debounceVisibilityChange = debounce(async () => {
            if (document.visibilityState === 'visible') {
                if (session.changesWhileInactive) {
                    console.log('[SnackViewer] Changes occurred while tab was inactive, syncing');

                    toast.info('Changes occurred while tab was inactive, syncing with preview', {
                        duration: 3000
                    })
                    await delay(1000);
                    // Just ensure the connection is active
                    snackStore.setOnline(chatId);

                    // Apply only the changes that happened while inactive
                    // This could be a new method that doesn't do a full reload
                    await snackStore.updateFiles(chatId, session.fileTree, session.dependencies);

                    // Reset the flag
                    session.setChangesWhileInactive(false);
                }
            }
        }, 1000);

        const handleVisibilityChange = async () => {
            debounceVisibilityChange();
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [chatId, snackStore, session]);


    useEffect(() => {
        if(state?.error) {
            session.setSnackError(state?.error);
        }
    }, [state?.error]);

    useEffect(() => {
        snackStore.setOnline(chatId)
    }, [snackStore]);

    const refresh = () => {
        setIsRefreshed(true)
        snackStore.reloadSnack(chatId);
        // console.log('Clicking refresh')
    }

    const handleDeviceChange = (device: DeviceType) => {
        // Force a re-render of the iframe
        // if (webPreviewRef.current) {
        //     const currentSrc = webPreviewRef.current.src;
        //     webPreviewRef.current.src = '';
        //     setTimeout(() => {
        //         if (webPreviewRef.current) {
        //             webPreviewRef.current.src = currentSrc;
        //         }
        //     }, 50);
        // }
    };

    // Memoize the iframe to prevent unnecessary re-renders
    const iframeContent = useMemo(() => {
        return (
            <iframe
                ref={webPreviewRef}
                className="snack-iframe"
                style={{width: '100%', height: '100%', border: 'none'}}
                src={state?.webPreviewURL || ''}
                allow="accelerometer; ambient-light-sensor; camera; encrypted-media; geolocation; gyroscope; magnetometer; microphone; usb; xr-spatial-tracking"
                sandbox="allow-forms allow-modals allow-pointer-lock allow-popups allow-presentation allow-same-origin allow-scripts"
            />
        )
    }, [state?.webPreviewURL, webPreviewRef, isRefreshed]);

    return (
        <div ref={containerRef}
             className="w-full h-full flex flex-col items-center justify-start pt-0 md:pt-4 gap-4 overflow-hidden bg-gradient-to-br from-background to-background/80">
            {dimensions.height > 0 && (
                <>
                    {/*<div className="absolute bottom-4 right-2 z-[1000]">*/}
                    {/*    <ExpoGoAlert size="icon" playerUrl={state?.url || ''}/>*/}
                    {/*</div>*/}
                    
                    {/* Historical version banner */}
                    {(session?.isViewingHistoricalVersion) && (
                        <div className="absolute top-0 left-0 right-0 z-[1000] bg-accent text-accent-foreground py-1 px-4 text-center text-sm font-medium flex items-center justify-between">
                            <span className="text-xs">Viewing historical version of your project</span>
                            <Button 
                                variant="outline" 
                                size="xs"
                                className="border-accent-foreground"
                                onClick={() => session.projectId ? session.restoreLatestVersion(session.projectId, chatId) : null}
                            >
                                Restore Latest Version
                            </Button>
                        </div>
                    )}
                    
                    {/* Loading indicator for historical version */}
                    {session.isLoadingHistoricalVersion && (
                        <div className="absolute inset-0 z-[2000] bg-black/50 flex items-center justify-center">
                            <div className="bg-background rounded-lg p-6 shadow-xl flex flex-col items-center gap-4">
                                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                                <p className="text-sm font-medium">Loading historical version...</p>
                            </div>
                        </div>
                    )}
                    {/*{!isMobile && (*/}
                    {/*    <div className="flex flex-col gap-y-2 absolute top-0 z-10">*/}

                    {/*        <div className="flex items-center justify-center gap-x-2">*/}
                    {/*            <Button variant="outline" size="sm" onClick={refresh}>*/}
                    {/*                Preview not loading? Try refreshing*/}
                    {/*                <RefreshCcwIcon className="h-4 w-4"/>*/}
                    {/*            </Button>*/}
                    {/*            <ExpoInstructionsDialog previewUrl={state?.url || ''}/>*/}

                    {/*            <Button*/}
                    {/*                variant="secondary"*/}
                    {/*                size="icon"*/}
                    {/*                className="bg-black/70 text-white hover:bg-black/80"*/}
                    {/*                onClick={toggleFullscreen}*/}
                    {/*            >*/}
                    {/*                <Maximize2 className="h-4 w-4"/>*/}
                    {/*            </Button>*/}
                    {/*        </div>*/}
                    {/*    </div>*/}

                    {/*)}*/}
                    <div className="relative w-full h-full">
                        {isMobile ? (
                            <div className="w-full h-full bg-white overflow-hidden relative">
                                <div className="w-full h-full overflow-hidden pointer-events-auto">
                                    {iframeContent}
                                </div>
                                {/*{isMobile && (*/}
                                {/*    <DraggableButton onClick={toggleFullscreen} className=" top-0"/>*/}
                                {/*)}*/}
                                {isLoading && (
                                    <div
                                        className="absolute inset-0 flex items-center justify-center bg-white z-20 pointer-events-none">
                                        <div
                                            className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                                    </div>
                                )}
                                {error && (
                                    <div
                                        className="absolute inset-0 flex items-center justify-center p-8 bg-red-50 z-20">
                                        <div className="text-red-600">Error: {error}</div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="h-full">
                                <PhoneFrame
                                    frameId="snack-phone-frame-v2"
                                    onDeviceChange={handleDeviceChange}
                                    containerHeight={dimensions.height}>
                                    <div className="relative w-full h-full overflow-hidden" style={
                                        {
                                            // borderBottomLeftRadius: "55.75px",
                                            // borderBottomRightRadius: "55.75px",
                                            // borderTopLeftRadius: '24px',
                                            // borderTopRightRadius: '24px',
                                        }
                                    }>
                                        {isLoading && (
                                            <div
                                                className="absolute inset-0 flex items-center justify-center bg-white z-20">
                                                <div
                                                    className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
                                            </div>
                                        )}
                                        {error && (
                                            <div
                                                className="absolute inset-0 flex items-center justify-center p-8 bg-red-50 z-20">
                                                <div className="text-red-600">Error: {error}</div>
                                            </div>
                                        )}
                                        <div className="snack-wrapper absolute inset-0 overflow-hidden">
                                            {iframeContent}
                                        </div>
                                    </div>
                                </PhoneFrame>

                                <div className="absolute right-8 bottom-8">
                                    <div className="flex flex-col space-y-2">
                                        <DiscussButton chatId={chatId} projectId={projectId}/>
                                        <LinkButton
                                            href="https://discord.gg/Cpda56yVYY"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            variant="secondary"
                                            size="default"
                                            className="flex items-center gap-2"
                                            onClick={() => {
                                                // Track when user clicks on Discord help link
                                                trackFeatureEvent('DISCORD_HELP_TRIGGERRED', {
                                                    feature_name: 'discord_help',
                                                    is_enabled: true,
                                                    user_type: 'free', // This should be dynamically determined
                                                    trigger_source: 'header_button',
                                                    // project_id: projectId
                                                });
                                            }}
                                        >

                                            <span>Need help?</span>
                                            <Image src={"/discord.jpg"} alt={"discord logo"} width={28} height={28}
                                                   className="h-5 w-5 rounded-full"/>
                                        </LinkButton>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </>

            )}

            <style jsx>{`
                .snack-wrapper {
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    position: relative;
                    display: flex;
                    align-items: stretch;
                    justify-content: stretch;
                    //border-bottom-left-radius: 55.75px;
                    //border-bottom-right-radius: 55.75px;
                    //border-top-right-radius: 24px;
                    //border-top-left-radius: 24px;
                }

                .snack-iframe {
                    flex: 1;
                    height: 100%;
                    width: 100%;
                    border: none;
                    display: block;
                    position: absolute;
                    top: 0;
                    left: 0;
                    transform-origin: center;
                    -webkit-transform-origin: center;
                    backface-visibility: hidden;
                    -webkit-backface-visibility: hidden;
                    opacity: ${isLoading ? 0 : 1};
                    transition: opacity 0.3s ease;
                    //border-radius: 1.75rem;
                }

                .snack-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 40px;
                    background-color: white;
                    z-index: 10;
                }

                .snack-bottom-overlay {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 30px;
                    background-color: white;
                    z-index: 10;
                }
            `}</style>
        </div>
    );
});

export default DesktopSnackPreview;