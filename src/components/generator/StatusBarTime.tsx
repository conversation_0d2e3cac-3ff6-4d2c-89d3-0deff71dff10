'use client';

import React, { useState, useEffect, memo } from 'react';

interface StatusBarTimeProps {
  initialTime?: string;
}

// This component is specifically designed to avoid re-renders in the parent component
// It manages its own state and updates independently
const StatusBarTimeComponent: React.FC<StatusBarTimeProps> = ({ initialTime }) => {
  // If initialTime is provided, use it; otherwise, set current time
  const [time, setTime] = useState(initialTime || getCurrentTimeString());
  
  // Only set up the interval if no initialTime was provided
  useEffect(() => {
    if (initialTime) return; // Skip interval if we're using a fixed time
    
    // Update time every minute
    const intervalId = setInterval(() => {
      setTime(getCurrentTimeString());
    }, 60000); // Update every minute
    
    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [initialTime]);
  
  return (
    <div className="flex-1 text-left text-sm font-semibold">
      {time}
    </div>
  );
};

// Helper function to get current time in the desired format
function getCurrentTimeString(): string {
  return new Date().toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit', 
    hour12: false 
  }).replace(' ', '');
}

// Memoize the component to prevent re-renders when props don't change
export const StatusBarTime = memo(StatusBarTimeComponent);
