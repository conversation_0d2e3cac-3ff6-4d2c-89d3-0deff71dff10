import {ExpoInstructionsDialog} from "@/components/preview/ExpoInstructionsDialog";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import { trackFeatureEvent } from '@/lib/analytics/track';

const UseInMobileButton = ({chatId}: { chatId: string }) => {
    const {snackStore, generatorStore} = useStores();
    const state = snackStore.getSnackState(chatId);
    const session = generatorStore.getActiveSession(chatId);

    // Create a wrapped component to track the click event
    const TrackedDialogTrigger = (props: React.ComponentProps<typeof ExpoInstructionsDialog>) => {
        // Track when user clicks on mobile view button
        const handleClick = () => {
            trackFeatureEvent('QR_CODE_SCANNED', {
                feature_name: 'mobile_view_instructions',
                is_enabled: true,
                user_type: 'free', // This should be dynamically determined
                trigger_source: 'mobile_button',
                project_id: session?.projectId || ''
            });
        };
        
        return (
            <div onClick={handleClick}>
                <ExpoInstructionsDialog {...props} />
            </div>
        );
    };

    return (
        <TrackedDialogTrigger previewUrl={state?.url || ''} />
    )
};

export default observer(UseInMobileButton);