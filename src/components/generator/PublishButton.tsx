'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Rocket, 
  Globe, 
  Smartphone, 
  LoaderIcon,
  ChevronDown,
  CheckCircle,
  ExternalLink,
  Clock,
  AlertCircle,
  Download,
  Loader2
} from 'lucide-react';
import { useStores } from '@/stores/utils/useStores';
import { observer } from 'mobx-react-lite';
import { toast } from 'sonner';
import { FeatureGate } from '@/components/subscription/feature-gate';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Deployment, DeploymentPlatform, DeploymentStatus } from '@/stores/DeploymentStore';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import {useSubscription} from "@/hooks/use-subscription";
import Link from "next/link";

interface PublishButtonProps {
  projectId: string;
  className?: string;
  handleClick: (arg: any) => any;
}

interface DeploymentInfo {
  platform: DeploymentPlatform;
  status: DeploymentStatus;
  url?: string;
  slug?: string;
  createdAt: Date;
  progress?: number; // Progress percentage (0-100)
}

interface PlatformDeploymentProps {
  deployment: DeploymentInfo;
  onViewUrl?: (url: string) => void;
  projectId: string;
}

interface StatusBadgeProps {
  status: DeploymentStatus;
}

interface BuildProgressProps {
  platform: DeploymentPlatform;
  status: DeploymentStatus;
  progress?: number;
  projectId: string;
  onStatusChange?: (status: DeploymentStatus) => void;
}

export const PublishButton = observer(({ projectId, className, handleClick }: PublishButtonProps) => {
  const { deploymentStore } = useStores();
  const [isPublishing, setIsPublishing] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<DeploymentPlatform | null>(null);
  const [latestDeployments, setLatestDeployments] = useState<Record<DeploymentPlatform, DeploymentInfo | null>>({
    web: null,
    android: null,
    ios: null
  });
  const [isLoading, setIsLoading] = useState(true);
  
  // References for progress timers
  const webProgressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const androidProgressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const webStartTimeRef = useRef<number | null>(null);
  const androidStartTimeRef = useRef<number | null>(null);
  const {subscription} = useSubscription();
  
  // Constants for build durations (in milliseconds)
  const WEB_BUILD_DURATION = 50 * 1000; // 50 seconds
  const ANDROID_BUILD_DURATION = 90 * 1000; // 90 seconds

  // Load latest deployments when component mounts - only once
  useEffect(() => {
    const loadDeployments = async () => {
      if (!projectId) return;
      
      // Check if user has access to deployments feature using the subscription hook
      // Skip loading deployments for anonymous users or if feature not allowed
      if (subscription?.isAnonymous || 
          (!subscription?.allowedFeatures?.includes('deployment_web') &&
           !subscription?.allowedFeatures?.includes('deployment_android') && 
           !subscription?.allowedFeatures?.includes('deployment_ios'))) {
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      try {
        await deploymentStore.loadDeploymentsFromDatabase(projectId);
        const deployments = deploymentStore.getDeployments(projectId);
        
        // Get latest deployment for each platform
        const platforms: DeploymentPlatform[] = ['web', 'android', 'ios'];
        const latest: Record<DeploymentPlatform, DeploymentInfo | null> = {
          web: null,
          android: null,
          ios: null
        };
        
        platforms.forEach(platform => {
          const platformDeployments = deployments.filter(d => d.platform === platform);
          if (platformDeployments.length > 0) {
            // Sort by date (newest first)
            const sorted = [...platformDeployments].sort((a, b) => 
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );
            
            const currentStatus = sorted[0].status as DeploymentStatus;
            
            latest[platform] = {
              platform,
              status: currentStatus,
              url: sorted[0].url,
              slug: sorted[0].slug,
              createdAt: new Date(sorted[0].createdAt)
            };
            
            // Progress will be handled by the BuildProgress component
          }
        });
        
        setLatestDeployments(latest);
      } catch (error) {
        console.error('Failed to load deployments:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Load deployments only once when component mounts
    loadDeployments();
    
    // No polling interval - we'll only refresh after a new deployment is initiated
  }, [projectId, deploymentStore, subscription]);

  // BuildProgress component for showing and managing build progress
  const BuildProgress = observer(({ platform, status, progress, projectId, onStatusChange }: BuildProgressProps) => {
    const { deploymentStore } = useStores();
    const [currentProgress, setCurrentProgress] = useState<number>(progress || 0);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const startTimeRef = useRef<number | null>(null);
    
    // Constants for build durations
    const duration = platform === 'web' ? WEB_BUILD_DURATION : ANDROID_BUILD_DURATION;
    
    // Get platform-specific gradient colors
    const getGradientColors = () => {
      switch (platform) {
        case 'web':
          return 'from-blue-100/30 to-blue-200/40 dark:from-blue-900/20 dark:to-blue-800/30';
        case 'android':
          return 'from-green-100/30 to-green-200/40 dark:from-green-900/20 dark:to-green-800/30';
        case 'ios':
          return 'from-gray-100/30 to-gray-200/40 dark:from-gray-900/20 dark:to-gray-800/30';
        default:
          return 'from-gray-100/30 to-gray-200/40 dark:from-gray-900/20 dark:to-gray-800/30';
      }
    };
    
    // Check build status periodically
    const checkBuildStatus = useCallback(async () => {
      try {
        const deployments = await deploymentStore.loadDeploymentsFromDatabase(projectId);
        const platformDeployments = deployments.filter(d => d.platform === platform);
        
        if (platformDeployments.length > 0) {
          const sorted = [...platformDeployments].sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
          
          const latestStatus = sorted[0].status as DeploymentStatus;
          
          // If build is complete or failed, stop the timer
          if (latestStatus === 'completed' || latestStatus === 'success' || latestStatus === 'failed') {
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            
            // Set progress to 100% if complete
            if (latestStatus === 'completed' || latestStatus === 'success') {
              setCurrentProgress(100);
            }
            
            // Notify parent of status change
            if (onStatusChange) {
              onStatusChange(latestStatus);
            }
          }
        }
      } catch (error) {
        console.error('Error checking build status:', error);
      }
    }, [platform, projectId, deploymentStore, onStatusChange]);
    
    // Start progress timer
    useEffect(() => {
      // Only start if status is processing or deploying
      if (!['processing', 'deploying'].includes(status)) {
        return;
      }
      
      // Set start time
      startTimeRef.current = Date.now();
      
      // Create interval to update progress
      const interval = setInterval(() => {
        const startTime = startTimeRef.current;
        if (!startTime) return;
        
        const elapsed = Date.now() - startTime;
        const newProgress = Math.min(Math.floor((elapsed / duration) * 100), 99); // Cap at 99% until complete
        
        setCurrentProgress(newProgress);
        
        // Check actual build status every 5 seconds
        if (elapsed % 5000 < 500) {
          checkBuildStatus();
        }
        
        // If we've reached the end of the expected duration, do final check
        if (elapsed >= duration) {
          clearInterval(interval);
          checkBuildStatus();
        }
      }, 500);
      
      timerRef.current = interval;
      
      // Cleanup on unmount
      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }, [status, duration, checkBuildStatus]);
    
    // Also check status when component mounts
    useEffect(() => {
      checkBuildStatus();
    }, [checkBuildStatus]);
    
    return (
      <div 
        className={cn(
          "absolute top-0 left-0 h-full bg-gradient-to-r transition-all duration-300 ease-out",
          getGradientColors()
        )}
        style={{ width: `${currentProgress}%`, zIndex: 0 }}
      />
    );
  });
  
  // Clean up timers when component unmounts
  useEffect(() => {
    return () => {
      if (webProgressTimerRef.current) {
        clearInterval(webProgressTimerRef.current);
      }
      if (androidProgressTimerRef.current) {
        clearInterval(androidProgressTimerRef.current);
      }
    };
  }, []);

  // Wrapper for deployment actions that require feature gate check
  const handleDeploymentAction = (action: () => Promise<void>) => {
    // This function will be called by the FeatureGate if the user has access
    return async () => {
      try {
        await action();
      } catch (error) {
        console.error('Deployment action failed:', error);
      }
    };
  };
  
  const handlePublish = async (platform: DeploymentPlatform) => {
    if (!projectId) return;
    
    setSelectedPlatform(platform);
    setIsPublishing(true);
    
    try {
      await deploymentStore.deployProject(projectId, platform);
      toast.success(`Your app is being published to ${getPlatformName(platform)}`);
      
      // Immediately update the UI to show deployment in progress
      await deploymentStore.loadDeploymentsFromDatabase(projectId);
      const deployments = deploymentStore.getDeployments(projectId);
      const platformDeployments = deployments.filter(d => d.platform === platform);
      
      if (platformDeployments.length > 0) {
        const sorted = [...platformDeployments].sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        
        const currentStatus = sorted[0].status as DeploymentStatus;
        
        // Update with the latest deployment status
        setLatestDeployments(prev => ({
          ...prev,
          [platform]: {
            platform,
            status: currentStatus,
            url: sorted[0].url,
            slug: sorted[0].slug,
            createdAt: new Date(sorted[0].createdAt),
            progress: ['processing', 'deploying'].includes(currentStatus) ? 0 : undefined // Only initialize progress if in processing state
          }
        }));
        
        // Progress will be handled by the BuildProgress component
      }
    } catch (error) {
      console.error(`Failed to publish to ${platform}:`, error);
      toast.error(`Failed to publish to ${getPlatformName(platform)}`);
    } finally {
      setIsPublishing(false);
      setSelectedPlatform(null);
    }
  };

  const getPlatformName = (platform: DeploymentPlatform): string => {
    switch (platform) {
      case 'web': return 'Web';
      case 'android': return 'Android';
      case 'ios': return 'iOS';
      default: return platform;
    }
  };
  
  // StatusBadge component for showing deployment status
  const StatusBadge = ({ status }: StatusBadgeProps) => {
    switch (status) {
      case 'completed':
      case 'success':
        return (
          <Badge variant="outline" className="ml-2 bg-green-950 text-green-400 border-green-800 text-[10px] py-0 px-1.5 h-5">
            <CheckCircle className="h-3 w-3 mr-1" />
            <span>Success</span>
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="outline" className="ml-2 bg-red-950 text-red-400 border-red-800 text-[10px] py-0 px-1.5 h-5">
            <AlertCircle className="h-3 w-3 mr-1" />
            <span>Failed</span>
          </Badge>
        );
      case 'queued':
      case 'processing':
      case 'deploying':
        return (
          <Badge variant="outline" className="ml-2 bg-amber-950 text-amber-400 border-amber-800 text-[10px] py-0 px-1.5 h-5">
            <Clock className="h-3 w-3 mr-1" />
            <span>{status}</span>
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="ml-2 text-[10px] py-0 px-1.5 h-5">
            <span>{status}</span>
          </Badge>
        );
    }
  };
  
  const getFormattedUrl = (deployment: DeploymentInfo) => {
    if (deployment.platform === 'web' && deployment.slug) {
      return `https://${deployment.slug}.web.magically.life`;
    }
    return deployment.url || '';
  };
  // PlatformDeployment component for showing a platform's deployment status
  const PlatformDeployment = observer(({ deployment, projectId }: PlatformDeploymentProps) => {
    const [currentDeployment, setCurrentDeployment] = useState<DeploymentInfo>(deployment);
    const { platform, status, progress, url, slug } = currentDeployment;
    const isInProgress = ['queued', 'processing', 'deploying'].includes(status);
    const isCompleted = status === 'completed' || status === 'success';
    
    // Handle status change from BuildProgress component
    const handleStatusChange = useCallback((newStatus: DeploymentStatus) => {
      setCurrentDeployment(prev => ({
        ...prev,
        status: newStatus,
        progress: newStatus === 'completed' || newStatus === 'success' ? 100 : prev.progress
      }));
    }, []);
    
    // Get platform-specific styles and icons
    const getPlatformIcon = () => {
      switch (platform) {
        case 'web':
          return <Globe className="h-4 w-4 text-blue-400 flex-shrink-0" />;
        case 'android':
          return <Smartphone className="h-4 w-4 text-green-400 flex-shrink-0" />;
        case 'ios':
          return <Smartphone className="h-4 w-4 text-gray-400 flex-shrink-0" />;
        default:
          return <Globe className="h-4 w-4 text-gray-400 flex-shrink-0" />;
      }
    };
    
    const getActionButton = () => {
      if (!isCompleted || (!url && !slug)) return null;
      
      const formattedUrl = getFormattedUrl(currentDeployment);
      
      switch (platform) {
        case 'web':
          return (
            <a 
              href={formattedUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center gap-1 text-xs text-blue-400 hover:text-blue-300 flex-shrink-0 ml-2"
            >
              <ExternalLink className="h-3 w-3" />
              View
            </a>
          );
        case 'android':
        case 'ios':
          return (
            <a 
              href={formattedUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className={cn(
                "flex items-center gap-1 text-xs flex-shrink-0 ml-2",
                platform === 'android' ? "text-green-400 hover:text-green-300" : "text-gray-400 hover:text-gray-300"
              )}
            >
              <Download className="h-3 w-3" />
              Download
            </a>
          );
        default:
          return null;
      }
    };
    
    return (
      <div className="relative overflow-hidden">
        {/* Progress bar background for in-progress deployments */}
        {isInProgress && (
          <BuildProgress 
            platform={platform} 
            status={status} 
            progress={progress} 
            projectId={projectId}
            onStatusChange={handleStatusChange}
          />
        )}
        
        <div className="flex items-center justify-between relative z-10 py-1">
          <div className="flex items-center gap-2 flex-1 overflow-hidden">
            {getPlatformIcon()}
            <span className="flex-shrink-0 capitalize">{platform}</span>
            <StatusBadge status={status} />
          </div>
          
          {getActionButton()}
          
          {isInProgress && typeof progress === 'number' && progress > 0 && progress < 100 && (
            <Loader2 className="h-3 w-3 ml-2 animate-spin z-10 relative flex-shrink-0" />
          )}
        </div>
      </div>
    );
  });
  
  const getDeploymentInfo = (platform: DeploymentPlatform) => {
    const deployment = latestDeployments[platform];
    if (!deployment) return null;
    
    return (
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          <span className="text-sm">{getPlatformName(platform)}</span>
          <StatusBadge status={deployment.status} />
        </div>
        <div className="text-xs text-muted-foreground">
          {formatDistanceToNow(deployment.createdAt, { addSuffix: true })}
        </div>
      </div>
    );
  };
  
  const getPublishButtonLabel = () => {
    if (isPublishing) {
      return "Publishing...";
    }
    
    // Check if we have any successful deployments
    const hasSuccessfulDeployments = Object.values(latestDeployments)
      .some(d => d && d.status === 'completed');
    
    return hasSuccessfulDeployments ? "Publish Again" : "Publish";
  };

  const getPlatformIcon = (platform: DeploymentPlatform) => {
    switch (platform) {
      case 'web':
        return <Globe className="h-4 w-4 mr-2" />;
      case 'android':
        return <Smartphone className="h-4 w-4 mr-2" />;
      case 'ios':
        return <Smartphone className="h-4 w-4 mr-2" />;
      default:
        return <Rocket className="h-4 w-4 mr-2" />;
    }
  };

  // Quick publish to web (one-click)
  const handleQuickPublish = async (): Promise<void> => {
    return handlePublish('web');
  };

  return (
    <div className={cn("flex items-center", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="default"
            size="sm"
            className="bg-primary/30 hover:bg-primary/70 border-primary border text-white flex items-center gap-1.5 shadow-sm shadow-primary/20 hover:shadow-primary/40 transition-all"
            disabled={isPublishing}
          >
            {isPublishing ? (
              <>
                <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                Publishing...
              </>
            ) : (
              <>
                <Rocket className="h-4 w-4 mr-1" />
                {getPublishButtonLabel()}
                <ChevronDown className="h-3 w-3 ml-1 opacity-70" />
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-72 p-0 bg-[#121212] text-white border-[#333] overflow-hidden shadow-xl shadow-black/50 rounded-xl">
          <div className="px-4 py-3 border-b border-[#333]">
            <h4 className="text-sm font-medium text-white">Quick Publish</h4>
          </div>
          {/* Publish options */}
          {/* Web Section */}
          <div className="group hover:bg-[#1e1e1e] transition-colors border-b border-[#333]">
            <div className="px-4 py-3 flex items-center gap-3">
              <Globe className="h-5 w-5 text-blue-400" />
              <span className="font-medium">Web</span>
            </div>
            <div className="px-4 pb-2 space-y-1.5">
              <FeatureGate
                featureId="deployment_web"
                upgradeMessage="Upgrade to publish to web"
                wrapHandler={true}
              >
                {({ handleClick }) => (
                  <button 
                    onClick={handleClick(() => handleDeploymentAction(() => handleQuickPublish())())}
                    disabled={isPublishing}
                    className="w-full text-left py-1.5 px-2.5 text-xs bg-blue-500/20 hover:bg-blue-500/40 text-white rounded-md border border-blue-500/30 hover:border-blue-500/60 transition-all flex items-center justify-between group"
                  >
                    <span className="flex items-center gap-2">
                      <Globe className="h-3.5 w-3.5" />
                      Publish to Web
                    </span>
                    <ExternalLink className="h-3 w-3 opacity-70 group-hover:opacity-100 transition-opacity" />
                  </button>
                )}
              </FeatureGate>
            </div>
          </div>

          {/* Android Section */}
          <div className="group hover:bg-[#1e1e1e] transition-colors border-b border-[#333]">
            <div className="px-4 py-3 flex items-center gap-3">
              <Smartphone className="h-5 w-5 text-green-400" />
              <span className="font-medium">Android</span>
            </div>
            <div className="pl-12 pr-4 pb-3 space-y-2">
              <FeatureGate
                featureId="deployment_android"
                upgradeMessage="Upgrade to download Android APK"
                wrapHandler={true}
              >
                {({ handleClick }) => (
                  <button 
                    onClick={handleClick(() => handleDeploymentAction(() => handlePublish('android'))())}
                    disabled={isPublishing}
                    className="w-full text-left py-1.5 px-2.5 text-xs bg-green-500/20 hover:bg-green-500/40 text-white rounded-md border border-green-500/30 hover:border-green-500/60 transition-all flex items-center justify-between group"
                  >
                    <span className="flex items-center gap-2">
                      <Smartphone className="h-3.5 w-3.5" />
                      Download Android APK
                    </span>
                    <Download className="h-3 w-3 opacity-70 group-hover:opacity-100 transition-opacity" />
                  </button>
                )}
              </FeatureGate>
              <div className="flex items-center justify-between py-1.5 px-2.5 rounded-md bg-[#1e1e1e] border border-[#333] opacity-70">
                <span className="text-xs text-gray-400">Publish to Play Store</span>
                <Badge variant="outline" className="text-xs bg-transparent border-[#444] text-gray-400">Soon</Badge>
              </div>
            </div>
          </div>
          
          {/* iOS Section */}
          <div className="group hover:bg-[#1e1e1e] transition-colors border-b border-[#333]">
            <div className="px-4 py-3 flex items-center gap-3">
              <Smartphone className="h-5 w-5 text-gray-400" />
              <span className="font-medium">iOS</span>
            </div>
            <div className="pl-12 pr-4 pb-3 space-y-2">
              {deploymentStore.platformFeatures.ios.deploy !== 'coming_soon' && (
                <FeatureGate
                  featureId="deployment_ios"
                  upgradeMessage="Upgrade to download iOS IPA"
                  wrapHandler={true}
                >
                  {({ handleClick }) => (
                    <button 
                      onClick={handleClick(() => handleDeploymentAction(() => handlePublish('ios'))())}
                      disabled={isPublishing}
                      className="w-full text-left py-1.5 px-2.5 text-xs bg-gray-500/20 hover:bg-gray-500/40 text-white rounded-md border border-gray-500/30 hover:border-gray-500/60 transition-all flex items-center justify-between group"
                    >
                      <span className="flex items-center gap-2">
                        <Smartphone className="h-3.5 w-3.5" />
                        Download iOS IPA
                      </span>
                      <Download className="h-3 w-3 opacity-70 group-hover:opacity-100 transition-opacity" />
                    </button>
                  )}
                </FeatureGate>
              )}
              <div className="flex items-center justify-between py-1.5 px-2.5 rounded-md bg-[#1e1e1e] border border-[#333] opacity-70">
                <span className="text-xs text-gray-400">Publish to App Store</span>
                <Badge variant="outline" className="text-xs bg-transparent border-[#444] text-gray-400">Soon</Badge>
              </div>
            </div>
          </div>
          
          {/* Deployment status section */}
          {!isLoading && (
            <>
              <div className="px-4 py-3 border-t border-b border-[#333] flex items-center justify-between">
                <h4 className="text-sm font-medium text-white">Latest Publications</h4>
                <Link href={`/projects/${projectId}/deployments`} className="text-xs text-blue-400 hover:text-blue-300">View All</Link>
              </div>
              
              {/* Web deployment status */}
              <div className="px-4 py-3 text-sm text-gray-400">
                {latestDeployments.web ? (
                  <FeatureGate
                    featureId="deployment_web"
                    fallback={
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-blue-400 flex-shrink-0" />
                        <span className="flex-shrink-0">Web</span>
                        <Badge variant="outline" className="ml-2 text-[10px] py-0 px-1.5 h-5">
                          <span>Upgrade to view</span>
                        </Badge>
                      </div>
                    }
                  >
                    <PlatformDeployment deployment={latestDeployments.web} projectId={projectId} />
                  </FeatureGate>
                ) : (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <span>No web publications yet</span>
                  </div>
                )}
              </div>
              
              {/* Android deployment status */}
              <div className="px-4 py-3 text-sm text-gray-400 border-t border-[#333]">
                {latestDeployments.android ? (
                  <FeatureGate
                    featureId="deployment_android"
                    fallback={
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4 text-green-400 flex-shrink-0" />
                        <span className="flex-shrink-0">Android</span>
                        <Badge variant="outline" className="ml-2 text-[10px] py-0 px-1.5 h-5">
                          <span>Upgrade to view</span>
                        </Badge>
                      </div>
                    }
                  >
                    <PlatformDeployment deployment={latestDeployments.android} projectId={projectId} />
                  </FeatureGate>
                ) : (
                  <div className="flex items-center gap-2">
                    <Smartphone className="h-4 w-4 text-gray-500" />
                    <span>No Android publications yet</span>
                  </div>
                )}
              </div>
              
              {/* iOS deployment status - only show if available */}
              {deploymentStore.platformFeatures.ios.deploy !== 'coming_soon' && (
                <div className="px-4 py-3 text-sm text-gray-400 border-t border-[#333]">
                  {latestDeployments.ios ? (
                    <FeatureGate
                      featureId="deployment_ios"
                      fallback={
                        <div className="flex items-center gap-2">
                          <Smartphone className="h-4 w-4 text-gray-400 flex-shrink-0" />
                          <span className="flex-shrink-0">iOS</span>
                          <Badge variant="outline" className="ml-2 text-[10px] py-0 px-1.5 h-5">
                            <span>Upgrade to view</span>
                          </Badge>
                        </div>
                      }
                    >
                      <PlatformDeployment deployment={latestDeployments.ios} projectId={projectId} />
                    </FeatureGate>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4 text-gray-500" />
                      <span>No iOS publications yet</span>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
});

export default PublishButton;
