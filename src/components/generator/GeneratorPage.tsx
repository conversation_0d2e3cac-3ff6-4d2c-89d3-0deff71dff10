'use client';

import React, {useEffect, useState, useC<PERSON>back, useMemo} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger} from '@/components/ui/tabs';
import {DEFAULT_CODE, DEFAULT_DEPENDENCIES} from '@/types/editor';
import Chat from "@/components/base/chat";
import { InfiniteChat } from "@/components/infinite-chat";
import {DataStreamHandler} from '@/components/base/data-stream-handler';
import {CodeBlock, FileItem, FileNode} from "@/types/file";
import {Button} from "@/components/ui/button";
import {convertV2ToSnackFiles, migrateToV2} from '@/utils/file-converter';
import {fileStorage} from '@/lib/services/file-storage';
import {APP_TEMPLATES} from "@/lib/appTemplates/appTemplates";
import {PreviewPanel} from './PreviewPanel';
import {PreviewDialog} from './PreviewDialog';
import {useIsMobile} from "@/hooks/use-mobile";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import {Chat as ChatType, Message, Project} from "@/lib/db/schema";
import {convertToUIMessages} from "@/lib/utils";
import {convertToFileTree} from '@/utils/file-tree-converter';
import {SnackErrorObserver} from './SnackErrorObserver';
import {useAnonymousSession} from "@/providers/anonymous-provider";
import {ResizablePanel} from "@/components/ui/resizable-panel";
import {GeneratorSidebar} from "@/components/generator/GeneratorSidebar";
import {WelcomeDialogWrapper} from "@/components/generator/WelcomeDialogWrapper";
import {useLocalStorage} from "usehooks-ts";
import {DiscussButton} from "@/components/discuss";
import {EnhancedErrorHandler} from "@/components/generator/EnhancedErrorHandler";
import {ProjectSession} from "@/stores/ProjectSessionStore";

interface GeneratorPageProps {
    template?: string,
    prompt?: string,
    chat?: ChatType,
    messagesFromDb: Message[];
    isInitial: boolean;
    initialChatId?: string;
    activeTab?: 'chat' | 'deployments' | 'settings';
    projectId: string;
    project: Project;
    hasMoreMessages?: boolean;
    totalUserMessages?: number;
}

const GeneratorPage = observer((props: GeneratorPageProps) => {
    const {template, prompt, chat, messagesFromDb, isInitial, initialChatId, activeTab = 'chat', projectId, project, hasMoreMessages, totalUserMessages} = props;
    const {generatorStore, snackStore} = useStores();
    const isMobile = useIsMobile();

    const {anonymousId, getStoredChat} = useAnonymousSession();
    const [hasSeenWelcomeDialog, setHasSeenWelcomeDialog] = useLocalStorage('hasSeenWelcomeDialog', false);

    // Combine DB messages with anonymous messages if needed - memoized
    const combinedMessages = React.useMemo(() => {
        if (anonymousId) {
            const storedChat = getStoredChat(initialChatId || chat?.id as string);
            return storedChat ? storedChat.messages : [];
        }
        return messagesFromDb;
    }, [anonymousId, initialChatId, chat?.id, messagesFromDb]);

    // Memoize session creation to prevent recreation on every render
    const session = React.useMemo(() => {
        const sessionId = (isInitial ? initialChatId : chat?.id) as string;
        return generatorStore.createSession(sessionId, {
            messages: combinedMessages,
            isInitial,
            projectId
        });
    }, [combinedMessages, isInitial, initialChatId, chat?.id, projectId, generatorStore]);

    // Move side effects out of render function
    useEffect(() => {
        if (!generatorStore.initialLoadComplete) {
            if (chat?.needsContinuation) {
                session?.setNeedsContinuation(true);
            }
            // Needed for the other operations in Breadcrumb, Header, Supabase connection status, SupabasePanel etc.
            generatorStore.setProjectData(projectId, project);
        }
    }, [generatorStore, chat?.needsContinuation, projectId, project, session])

    // Optimize initialization effect with proper dependencies and error handling
    useEffect(() => {
        // Initialize tab selection if not already set
        if (!session.tabSelection) {
            session.setCurrentTab('preview');
        }
        
        // Load files based on session state
        const loadSessionFiles = async () => {
            try {
                if (!isInitial || projectId) {
                    await session.loadFiles({anonymousId: anonymousId as string});
                } else {
                    session.setFileTree(DEFAULT_CODE as FileNode[]);
                    session.setDependencies(DEFAULT_DEPENDENCIES);
                }
            } catch (error) {
                // Handle errors properly instead of logging to console
                console.error('Failed to load project files', error);
                // Using console.error instead of snackStore method since showError doesn't exist
            }
        };
        
        loadSessionFiles();
    }, [isInitial, projectId, session, anonymousId, snackStore]);

    useEffect(() => {
        const firstFile = session.fileTree.find(item => item.type === 'file');
        session.setActiveFile({
            name: firstFile?.name || '',
            content: firstFile?.content || '',
            language: firstFile?.language || 'typescript',
        } as FileNode)
    }, [session]);

    // Project data fetching moved to GeneratorStore


    // const [message, setMessage] = useState('');
    // const [activeTab, setActiveTab] = useState('preview');
    // const [fileTree, setFileTree] = useState<FileItem[]>(convertToFileTree(DEFAULT_CODE));
    // const [playerUrl, setPlayerUrl] = useState<string>();
    // const [dependencies, setDefaultDependencies] = useState(DEFAULT_DEPENDENCIES)
    // const [initialPrompt, setInitialPrompt] = useState('');
    // const [isPreviewOpen, setPreviewOpen] = useState(false);
    // const [loading, setLoading] = useState<boolean>(false);

    // const [activeFile, setActiveFile] = useState(() => {
    //     const firstFile = session.fileTree.find(item => item.type === 'file');
    //     return {
    //         name: firstFile?.name || '',
    //         content: firstFile?.content || '',
    //         language: firstFile?.language || 'typescript',
    //     };
    // });


    // Load file state on mount if chat exists
    // useEffect(() => {
    //     const loadFileState = async () => {
    //         if (!isInitial && chat?.id) {
    //             try {
    //                 const response = await fetch(`/api/chat/${chat.id}/files`);
    //                 if (response.ok) {
    //                     const data = await response.json();
    //                     setFileTree(convertToFileTree(data.files));
    //                     setDefaultDependencies(data.dependencies);
    //                 }
    //             } catch (error) {
    //                 console.error('Error loading file state:', error);
    //             }
    //         }
    //     };
    //     loadFileState();
    // }, [isInitial, chat]);

    useEffect(() => {
        console.log('template', template, prompt)
        if (template || prompt) {
            if (prompt) {
                session.setInitialPrompt(prompt);
            } else {
                const selectedTemplate = APP_TEMPLATES.find(tmp => tmp.id === template);
                if (selectedTemplate) {
                    session.setInitialPrompt(
                        `${selectedTemplate.prompt}.

Design inspiration:
${selectedTemplate.designInspiration}

Features:
${selectedTemplate.features.join("\n")}

                        `);
                }
            }
        }
    }, [template, prompt]);


    const onOperationChange = async (codeBlock: CodeBlock) => {
        if (codeBlock && codeBlock.fileName) {
            await session.updateFileTree(codeBlock);
            if (document.visibilityState === 'hidden') {
                session.setChangesWhileInactive(true);
            }
        }
    }

    const onChatLoadingChange = (chatLoading: boolean) => {
        if (chatLoading !== session.chatLoading) {
            session.setChatLoading(chatLoading);
            if (!chatLoading && isMobile) {
                generatorStore.togglePreview(true);
                session.setPreviewDialogOpen(true)
            }
        }
    }

    const onFileOpen = (fileName: string) => {
        session.setCurrentTab("code");
        session.setActiveFile({
            type: "file",
            content: '',
            name: fileName,
            language: 'typescript',
            changes: 0
        })
    }

    const onInitialRunInitialized = () => {
        if (chat) {
            chat.isInitialized = true;
        }
    }

    const onUpdateUserMessage = (userMessage: string) => {
        session.onUserMessageUpdate(userMessage)
    }

    const onStreamingComplete = () => {
        console.log('Sending controlled updates to the preview');
        session.pushUpdatesToPreview();
    }

    // Memoize message conversion to prevent unnecessary processing on each render
    const messages = useMemo(() => convertToUIMessages(messagesFromDb), [messagesFromDb]);
    
    // Move side effect to useEffect
    useEffect(() => {
        if (!generatorStore.initialLoadComplete) {
            generatorStore.setInitialLoadComplete();
        }
    }, [generatorStore])

    return (
        <div className="h-[calc(100dvh-98px)] relative md:h-[calc(100dvh-56px)] max-h-[100dvh] bg-[#21252B] flex flex-col">
            {/*<SnackErrorObserver session={session} />*/}
            {/*<EnhancedErrorHandler session={session} projectId={projectId} chatId={(chat as ChatType)?.id}/>*/}
            {/* Show WelcomeDialog if user hasn't seen it yet */}
            {/*{!hasSeenWelcomeDialog && session.chatLoading && <WelcomeDialogWrapper onSeen={() => setHasSeenWelcomeDialog(true)} />}*/}
            <ResizablePanel className="flex-1 min-h-0 overflow-hidden"
                            initialLeftWidth={isMobile ? 100 : 40} minLeftWidth={20} maxLeftWidth={80}
                            disabledOnMobile={true}
            >
                {/* Left Panel - Chat */}
                <div className="border-r border-[#181A1F] flex flex-col h-full w-full">
                    <Chat id={isInitial ? initialChatId as string : (chat as ChatType).id}
                          initialMessages={messages as Array<any>}
                          initialPrompt={session.initialPrompt}
                          isReadonly={false}
                          onLoadingChange={onChatLoadingChange}
                          projectId={projectId}
                          runLastUserMessage={(chat && !chat.isInitialized) || false}
                          onInitialRunInitialized={onInitialRunInitialized}
                          hasMoreMessages={hasMoreMessages}
                          totalUserMessages={totalUserMessages}
                    />
                    <DataStreamHandler id={isInitial ? initialChatId as string : (chat as ChatType).id}
                                       operationChange={onOperationChange} 
                                       onFileOpen={onFileOpen}
                                       onContinuationFlagChange={(needsContinuation) => session.setNeedsContinuation(needsContinuation)}
                                       onUpdateUserMessage={onUpdateUserMessage}
                                       onStreamingComplete={onStreamingComplete}
                    />
                </div>

                {/* Right Panel - Code and Preview */}
                <div className="h-full w-full hidden md:flex">
                    {/* Using key with chatId to force complete unmount/remount when chat changes */}
                    <PreviewPanel
                        key={isInitial ? initialChatId : (chat as ChatType)?.id}
                        chatId={isInitial ? initialChatId as string : (chat as ChatType).id}
                        onOperationChange={onOperationChange}
                        projectId={projectId}
                    />
                </div>

            </ResizablePanel>

            {/* Mobile Preview Dialog */}
            <PreviewDialog
                chatId={isInitial ? initialChatId as string : (chat as ChatType).id}
                onClose={() => {
                    generatorStore.togglePreview(false);
                    session.setPreviewDialogOpen(false);
                }}
                onOperationChange={onOperationChange}
                projectId={projectId}
            />

            {/* WelcomeDialogWrapper moved to top level and only shown for initial chats */}
        </div>
    );
})

export default GeneratorPage;
