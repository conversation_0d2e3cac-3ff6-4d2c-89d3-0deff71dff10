import React, { useState, useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { LogLevel } from '@/stores/LogStore';
import { Terminal, Bug, Wifi, Trash2, ChevronUp, ChevronDown, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {LogEntry} from "@/types/logs";

interface LogViewerProps {
  chatId: string;
  className?: string;
}

// Memoized log entry component to prevent unnecessary rerenders
const LogEntryItem = React.memo(({ log, onFixWithAI }: { log: LogEntry; onFixWithAI?: (log: LogEntry) => void }) => {
  const getLogColor = (type: LogLevel) => {
    switch (type) {
      case 'error':
        return 'text-destructive';
      case 'warning':
        return 'text-amber-500';
      case 'info':
        return 'text-blue-500';
      case 'debug':
        return 'text-muted-foreground';
      default:
        return 'text-foreground';
    }
  };

  // We could potentially receive logs from different sources in the future
  // such as device logs, network requests, or custom app events
  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'console':
        return <Terminal className="h-2.5 w-2.5 mr-1" />;
      case 'network':
        return <Wifi className="h-2.5 w-2.5 mr-1" />;
      case 'snack':
        return <Bug className="h-2.5 w-2.5 mr-1" />;
      default:
        return null;
    }
  };

  const formattedTime = new Date(log.timestamp).toLocaleTimeString();
  const isError = log.type === 'error';

  return (
    <div className="py-1 px-2 pr-4 border-b border-border text-[10px] leading-tight font-mono text-wrap w-full">
      <div className="grid grid-cols-[auto_1fr] w-full gap-2 pr-2">
        <span className="text-muted-foreground text-[10px] whitespace-nowrap">{formattedTime}</span>
        <div className={`min-w-0 overflow-hidden ${getLogColor(log.type)}`}>
          <div className="flex items-center justify-between gap-1 w-full">
            <div className="flex items-center gap-1 flex-shrink overflow-hidden">
              {/* Only show source badge if it's not the default 'snack' source */}
              {log.source !== 'snack' && (
                <Badge variant="outline" className="h-4 px-1 text-[10px] flex items-center">
                  {getSourceIcon(log.source)}
                  {log.source}
                </Badge>
              )}
              <Badge variant="outline" className={`h-4 px-1 text-[10px] ${getLogColor(log.type)}`}>
                {log.type}
              </Badge>
              {
                  log.platform && (
                      <Badge variant="outline" className="h-4 px-1 text-[10px] flex items-center">
                        {log.platform}
                      </Badge>
                  )
              }
              {
                  log.clientId && (
                      <Badge variant="outline" className="h-4 px-1 text-[10px] flex items-center">
                        Client ID: {log.clientId}
                      </Badge>
                  )
              }
            </div>
            {/* Show Fix with AI button only for errors */}
            {isError && onFixWithAI && (
              <Button 
                variant="default"
                size="sm" 
                className="h-4 px-1.5 text-[9px] border-primary text-primary-foreground hover:bg-primary/90 shrink-0"
                onClick={() => onFixWithAI(log)}
              >
                Fix with AI
                <Sparkles className="h-2 w-2 ml-1" />
              </Button>
            )}
          </div>
          <div className="mt-0.5 whitespace-pre-wrap break-words overflow-hidden text-ellipsis">{log.message}</div>
        </div>
      </div>
    </div>
  );
});

// Main log viewer component
const LogViewerBase = ({ chatId, className = '' }: LogViewerProps) => {
  const { logStore, generatorStore } = useStores();
  const [activeTab, setActiveTab] = useState<string>('all');
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  // Track if user has manually scrolled
  const userScrolledRef = useRef<boolean>(false);
  // Store previous log IDs to detect changes
  const prevLogIdsRef = useRef<string>('');
  
  // Handle manual scrolling detection
  const handleScroll = useCallback(() => {
    if (!scrollRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 20;
    
    // If user scrolled away from bottom, mark as manually scrolled
    if (!isAtBottom) {
      userScrolledRef.current = true;
    } else {
      // If user scrolled to bottom, reset the flag
      userScrolledRef.current = false;
    }
  }, []);
  
  // Setup scroll event listener
  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;
    
    scrollElement.addEventListener('scroll', handleScroll);
    return () => scrollElement.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);
  
  // Update logs based on current filter
  const updateLogs = useCallback((isInitialLoad = false) => {
    let filteredLogs: LogEntry[] = [];
    
    switch (activeTab) {
      case 'console':
        filteredLogs = logStore.getLogs(chatId, { source: 'console' });
        break;
      case 'network':
        filteredLogs = logStore.getLogs(chatId, { source: 'network' });
        break;
      case 'errors':
        filteredLogs = logStore.getLogs(chatId, { type: 'error' });
        break;
      case 'snack':
        filteredLogs = logStore.getLogs(chatId, { source: 'snack' });
        break;
      default:
        filteredLogs = logStore.getLogs(chatId);
    }
    
    // Only extract IDs for comparison to detect new logs
    const currentLogIds = JSON.stringify(filteredLogs.map(log => log.id));
    const hasNewLogs = currentLogIds !== prevLogIdsRef.current;
    
    // Only update state if logs have actually changed
    if (hasNewLogs || isInitialLoad) {
      setLogs(filteredLogs);
      prevLogIdsRef.current = currentLogIds;
      
      // Only auto-scroll in two cases:
      // 1. This is the initial load, OR
      // 2. There are new logs AND user hasn't manually scrolled away from bottom
      if (scrollRef.current && (isInitialLoad || (hasNewLogs && !userScrolledRef.current))) {
        // Use requestAnimationFrame for smoother scrolling
        requestAnimationFrame(() => {
          if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
          }
        });
      }
    }
  }, [chatId, activeTab, logStore]);
  
  // Setup polling for logs
  useEffect(() => {
    // Initial load - always scroll to bottom
    updateLogs(true);
    
    // Poll for new logs every second, but don't force scroll
    const intervalId = setInterval(() => {
      updateLogs(false);
    }, 1000);
    
    return () => clearInterval(intervalId);
  }, [chatId, activeTab, updateLogs]);
  
  const clearLogs = useCallback(() => {
    logStore.clearLogs(chatId);
    updateLogs(true); // Force refresh and scroll to bottom after clearing
  }, [chatId, logStore, updateLogs]);
  
  // Handle fixing errors with AI
  const handleFixWithAI = useCallback((log: LogEntry) => {
    const session = generatorStore.getActiveSession(chatId);
    if (!session) return;
    
    // Create an error object similar to what SnackErrorObserver expects
    const errorObj = {
      message: log.message,
      timestamp: log.timestamp || Date.now(),
      handled: false
    };
    
    // Set the error in the session and trigger the fix
    session.snackError = errorObj;
    session.handleErrorFix();
  }, [chatId, generatorStore]);
  
  const getLogCount = useCallback((type?: 'error' | 'warning' | 'info' | 'debug', source?: 'console' | 'network' | 'snack') => {
    if (type) {
      return logStore.getLogs(chatId, { type }).length;
    }
    if (source) {
      return logStore.getLogs(chatId, { source }).length;
    }
    return logStore.getLogs(chatId).length;
  }, [chatId, logStore]);
  
  const errorCount = getLogCount('error');
  
  return (
    <div className={cn("w-full h-full overflow-hidden flex flex-col", className)}>
      {/* Main content - always visible */}
        <div className="flex flex-col h-full w-full overflow-hidden">
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full h-full flex flex-col overflow-hidden">
            <TabsList className="bg-card p-0 h-6 rounded-none border-b border-border flex justify-center w-full">
              <TabsTrigger 
                value="all" 
                className="h-6 px-2 text-[10px] rounded-none border-b-2 border-transparent data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:text-primary"
              >
                All ({getLogCount()})
              </TabsTrigger>
              {/* Only show console tab if there are console logs */}
              {getLogCount(undefined, 'console') > 0 && (
                <TabsTrigger 
                  value="console" 
                  className="h-6 px-2 text-[10px] rounded-none border-b-2 border-transparent data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:text-primary"
                >
                  Console ({getLogCount(undefined, 'console')})
                </TabsTrigger>
              )}
              {/* Only show network tab if there are network logs */}
              {getLogCount(undefined, 'network') > 0 && (
                <TabsTrigger 
                  value="network" 
                  className="h-6 px-2 text-[10px] rounded-none border-b-2 border-transparent data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:text-primary"
                >
                  Network ({getLogCount(undefined, 'network')})
                </TabsTrigger>
              )}
              <TabsTrigger 
                value="snack" 
                className="h-6 px-2 text-[10px] rounded-none border-b-2 border-transparent data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:text-primary"
              >
                Snack ({getLogCount(undefined, 'snack')})
              </TabsTrigger>
              {/* Only show errors tab if there are errors */}
              {errorCount > 0 && (
                <TabsTrigger 
                  value="errors" 
                  className="h-6 px-2 text-[10px] rounded-none border-b-2 border-transparent data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:text-primary"
                >
                  Errors ({errorCount})
                </TabsTrigger>
              )}
            </TabsList>
            
            <TabsContent value={activeTab} className="p-0 m-0 flex-1 overflow-hidden h-[calc(100%-24px)]">
              <ScrollArea className="h-full bg-background overflow-y-scroll" ref={scrollRef as any}>
                {logs.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-muted-foreground text-xs">
                    No logs to display
                  </div>
                ) : (
                  logs.map(log => <LogEntryItem key={log.id} log={log} onFixWithAI={handleFixWithAI} />)
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>
    </div>
  );
};

// Export memoized component to prevent unnecessary rerenders
export const LogViewer = observer(LogViewerBase);
