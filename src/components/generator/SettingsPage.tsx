'use client';

import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { Skeleton } from '@/components/ui/skeleton';
import { ShareDialog } from './ShareDialog';
import { toast } from 'sonner';

// Import the new form component
import { SettingsForm } from '@/components/settings/SettingsForm';
import {Project} from "@/lib/db/schema";

interface SettingsPageProps {
  chatId: string;
  project: Project
}

const SettingsPage = observer(({ chatId, project }: SettingsPageProps) => {
  const { generatorStore, notificationStore } = useStores();
  const [isLoading, setIsLoading] = useState(false);
  const [projectData, setProjectData] = useState<Project>(project || {
    appName: '',
    description: '',
    primaryColor: '#000000',
    bundleIdentifier: '',
    packageName: '',
    visibility: 'private' as 'public' | 'private'
  });



  return (
    <div className="flex flex-col">
      <div className="flex-1 overflow-auto p-4 md:p-6">
        <div className="max-w-3xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold tracking-tight">Project Settings</h1>
            <p className="text-sm text-muted-foreground mt-1">Manage your project configuration and preferences</p>
          </div>
          
          {isLoading ? (
            <div className="space-y-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-10 w-full mt-2" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ))}
            </div>
          ) : (
            <>
              <SettingsForm 
                projectId={chatId} 
                initialData={projectData} 
                isLoading={isLoading} 
              />
              
              {/* Hidden share dialog trigger */}
              <div className="hidden">
                <ShareDialog
                  chatId={chatId}
                  initialVisibility={projectData.visibility}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
});

export default SettingsPage;
