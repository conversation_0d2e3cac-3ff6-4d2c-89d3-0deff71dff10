'use client';

import React from 'react';
import QRCode from 'react-qr-code';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { QrCode } from 'lucide-react';

interface QRCodeDialogProps {
  playerUrl: string;
}

export function QRCodeDialog({ playerUrl }: QRCodeDialogProps) {
  if(!playerUrl) {
    return;
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon">
          <QrCode className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Scan with Expo Go</DialogTitle>
          <DialogDescription>
            Open this Snack in your Expo Go app by scanning this QR code.
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center justify-center p-6">
          <QRCode
            value={playerUrl}
            size={256}
            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
            viewBox={`0 0 256 256`}
          />
        </div>
        <div className="text-center text-xs text-muted-foreground space-y-2">
          <p>Or open directly in Expo Go:</p>
          <pre className="relative rounded bg-muted p-2 font-mono text-[10px] leading-normal overflow-x-auto whitespace-pre-wrap break-all max-h-20 overflow-y-auto">
            {playerUrl}
          </pre>
        </div>
      </DialogContent>
    </Dialog>
  );
}
