'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Maximize2 } from "lucide-react";

interface DraggableButtonProps {
    onClick: () => void;
    className?: string;
    children?: any
}

export const DraggableButton: React.FC<DraggableButtonProps> = ({ onClick, className = "", children }) => {
    const [position, setPosition] = useState({ x: 20, y: window.innerHeight - 100 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const buttonRef = useRef<HTMLButtonElement>(null);

    useEffect(() => {
        const handleTouchStart = (e: TouchEvent) => {
            if (!buttonRef.current?.contains(e.target as Node)) return;
            setIsDragging(true);
            const touch = e.touches[0];
            setDragStart({
                x: touch.clientX - position.x,
                y: touch.clientY - position.y
            });
        };

        const handleTouchMove = (e: TouchEvent) => {
            if (!isDragging) return;
            e.preventDefault();
            const touch = e.touches[0];
            let newX = touch.clientX - dragStart.x;
            let newY = touch.clientY - dragStart.y;

            // Boundary checks
            const maxX = window.innerWidth - (buttonRef.current?.offsetWidth || 0);
            const maxY = window.innerHeight - (buttonRef.current?.offsetHeight || 0);
            newX = Math.max(0, Math.min(newX, maxX));
            newY = Math.max(0, Math.min(newY, maxY));

            setPosition({ x: newX, y: newY });
        };

        const handleTouchEnd = () => {
            if (!isDragging) return;
            setIsDragging(false);
        };

        document.addEventListener('touchstart', handleTouchStart, { passive: false });
        document.addEventListener('touchmove', handleTouchMove, { passive: false });
        document.addEventListener('touchend', handleTouchEnd);

        return () => {
            document.removeEventListener('touchstart', handleTouchStart);
            document.removeEventListener('touchmove', handleTouchMove);
            document.removeEventListener('touchend', handleTouchEnd);
        };
    }, [isDragging, dragStart, position]);

    const handleClick = (e: React.MouseEvent) => {
        if (!isDragging) {
            onClick();
        }
    };

    return (
        <Button
            ref={buttonRef}
            variant="secondary"
            size="icon"
            className={`fixed bg-black/70 text-white hover:bg-black/80 shadow-lg ${className}`}
            style={{
                left: `${position.x}px`,
                top: `${position.y}px`,
                touchAction: 'none',
                transition: isDragging ? 'none' : 'all 0.2s ease',
                zIndex: 1000
            }}
            onClick={handleClick}
        >
            {
                children || <Maximize2 className="h-4 w-4" />
            }

        </Button>
    );
};
