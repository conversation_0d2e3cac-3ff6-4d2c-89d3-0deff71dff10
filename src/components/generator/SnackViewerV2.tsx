import React, {useState, useEffect, useRef, useCallback, useMemo} from 'react';
import { Button } from '@/components/ui/button';
import {AlertCircle, Maximize2, RefreshCcwIcon, Bot, Sparkles} from 'lucide-react';
import { toast } from 'sonner';
import { DraggableButton } from './DraggableButton';
import { useIsMobile } from '@/hooks/use-mobile';
import {Snack, SnackListenerSubscription} from 'snack-sdk';
import { FileItem } from "@/types/file";
import { diff } from 'deep-object-diff';
import { PhoneFrame, DeviceType } from './PhoneFrame';
import { DEFAULT_DEPENDENCIES } from '@/types/editor';
import {Alert, AlertDescription, AlertTitle} from "@/components/ui/alert";
import { QRCodeDialog } from './QRCodeDialog';
import { Info } from 'lucide-react';
import { useContainerDimensions } from '@/hooks/use-container-dimensions';
import {ExpoGoAlert} from "@/components/generator/ExpoGoAlert";
import {useFullscreen} from "@/hooks/use-fullscreen";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import {ExpoInstructionsDialog} from "@/components/preview/ExpoInstructionsDialog";

interface SnackViewerV2Props {
    chatId: string;
    onSnackReady?: (snackId: string) => void;
}

const SnackViewerV2 = observer(({chatId, onSnackReady}: SnackViewerV2Props) => {
    const {generatorStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);

    if (!session) {
        return;
    }
    const webPreviewRef = useRef<HTMLIFrameElement>(null);
    const snackRef = useRef<Snack | null>(null);
    const prevFilesRef = useRef<FileItem[]>(session.fileTree);
    const initAttemptedRef = useRef(false);
    const stateListenerRef = useRef<SnackListenerSubscription | null>(null);
    const logListenerRef = useRef<SnackListenerSubscription | null>(null);
    const isMobile = useIsMobile();
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [snackState, setSnackState] = useState<any>(null);
    const [isClientReady, setClientReady] = useState(false);
    const [dimensions, setDimensions] = useState({ height: 0, width: 0 })
    const { containerRef, getDimensions } = useContainerDimensions((dimensions) => {
        setDimensions(dimensions);
        if (dimensions.height > 0 && !initAttemptedRef.current) {
            const success = initializeSnack();
            if (success) {
                initAttemptedRef.current = true;
            }
        }
    });
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isRefreshed, setIsRefreshed] = useState(false);

    const toggleFullscreen = useCallback(() => {
        console.log('Attempting fullscreen toggle');
        console.log('isMobile:', isMobile);
        
        const element = isMobile ? webPreviewRef.current : containerRef.current;
        console.log('Selected element:', element);
        
        if (!element) {
            console.log('No element found');
            return;
        }

        // Log available methods
        console.log('Available methods:', {
            requestFullscreen: !!element.requestFullscreen,
            webkitRequestFullscreen: !!(element as any).webkitRequestFullscreen,
            mozRequestFullScreen: !!(element as any).mozRequestFullScreen,
            msRequestFullscreen: !!(element as any).msRequestFullscreen
        });

        if (!document.fullscreenElement) {
            try {
                if ((element as any).webkitRequestFullscreen) {
                    console.log('Using webkit fullscreen');
                    (element as any).webkitRequestFullscreen();
                } else if (element.requestFullscreen) {
                    console.log('Using standard fullscreen');
                    element.requestFullscreen();
                } else {
                    console.log('No fullscreen method available');
                }
                setIsFullscreen(true);
            } catch (err) {
                console.error('Fullscreen error:', err);
            }
        } else {
            try {
                if (document.exitFullscreen) {
                    console.log('Using standard exit fullscreen');
                    document.exitFullscreen();
                } else if ((document as any).webkitExitFullscreen) {
                    console.log('Using webkit exit fullscreen');
                    (document as any).webkitExitFullscreen();
                } else {
                    console.log('No exit fullscreen method available');
                }
                setIsFullscreen(false);
            } catch (err) {
                console.error('Exit fullscreen error:', err);
            }
        }
    }, [isMobile]);

    useEffect(() => {
        const handleFullscreenChange = () => {
            // @ts-ignore
            setIsFullscreen(!!(document.fullscreenElement || document.webkitFullscreenElement));
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        
        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
            document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
        };
    }, []);





    const initializeSnack = useCallback(() => {
        const { height: containerHeight } = dimensions;
        if (!webPreviewRef.current?.contentWindow || !containerHeight || typeof window === 'undefined') {
            console.log('Cannot initialize Snack:', {
                hasContentWindow: !!webPreviewRef.current?.contentWindow,
                containerHeight,
                isClient: typeof window !== 'undefined'
            });
            return false;
        }

        console.log('Initializing Snack with:', {
            contentWindow: !!webPreviewRef.current.contentWindow,
            containerHeight,
            filesCount: session.fileTree.length
        });

        setIsRefreshed(false)

        const snackFiles = session.fileTree.reduce((acc, file) => {
            if (file.type === "file") {
                acc[file.name] = {
                    type: 'CODE' as const,
                    contents: file.content
                };
            }
            return acc;
        }, {} as Record<string, { type: 'CODE', contents: string }>);

        // Cleanup previous instance
        if (snackRef.current) {
            try {
                snackRef.current.setOnline(false);
                snackRef.current = null;
            } catch (e) {
                console.error('Error cleaning up previous Snack instance:', e);
            }
        }

        try {
            const snack = new Snack({
                files: snackFiles,
                dependencies: session.dependencies,
                verbose: true,
                online: true,
                sdkVersion: '52.0.0',
                name: 'AI Generated App',
                description: 'Generated with AI',
                webPlayerURL: "https://storage.magically.life/v2/52",
                webPreviewRef: {
                    current: webPreviewRef.current.contentWindow
                },
                codeChangesDelay: 1000
            });

            // Set up state listener first before assigning to ref
            const stateListener = snack.addStateListener(async (state, prevState) => {
                console.log('state', state, state.url, prevState.url)
                setSnackState(state);
                
                if (state.url) {
                    setIsLoading(false);
                    // console.log('prevState', expoUrl, state?.url)
                    // if (expoUrl !== state?.url) {
                        // onSnackReady?.(`${state.url}&snack-channel=${state.channel}`);
                        // setExpoUrl(state.url);
                    // }
                }
            });

            // Set up log listener
            const logListener = snack.addLogListener(({ type, message }) => {
                console.log(`Snack ${type}:`, message);
                if (type === 'error') {
                    setError(message);
                    session.setSnackError(message);
                }
            });

            // Only after successful setup, assign to refs
            snackRef.current = snack;
            stateListenerRef.current = stateListener;
            logListenerRef.current = logListener;

            setClientReady(true);
            snack.setOnline(true);
            return true;
        } catch (err: any) {
            console.error('Failed to initialize Snack:', err);
            setError(err.message);
            return false;
        }
    }, [session.fileTree, session.dependencies, onSnackReady, isRefreshed]);


    // Handle iframe ref changes
    const handleIframeRef = useCallback((node: HTMLIFrameElement | null) => {
        console.log('Iframe ref changed:', {
            node: !!node,
            contentWindow: node?.contentWindow
        });
        webPreviewRef.current = node;

        // Try to initialize if we have all the pieces
        if (node) {
            initializeSnack();
        }
    }, [initializeSnack]);



    // Update files when they change
    useEffect(() => {
        if (!snackRef.current || !isClientReady) return;

        const hasChanged = session.fileTree.some((file, index) => {
            const prevFile = prevFilesRef.current[index];
            // @ts-ignore
            return !prevFile || prevFile.name !== file.name || prevFile.content !== file.content;
        });

        if (!hasChanged) return;

        const snackFiles = session.fileTree.reduce((acc, file) => {
            if (file.type === "file") {
                acc[file.name] = {
                    type: 'CODE' as const,
                    contents: file.content
                };
            }
            return acc;
        }, {} as Record<string, { type: 'CODE', contents: string }>);

        console.log('Updating Snack files');
        snackRef.current.updateFiles(snackFiles);
        snackRef.current.sendCodeChanges();
        prevFilesRef.current = session.fileTree;
    }, [session.fileTree, isClientReady]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            try {
                if (snackRef.current) {
                    snackRef.current.setOnline(false);
                }
                // Clean up listeners if they exist
                // stateListenerRef.current?.remove?.();
                // logListenerRef.current?.remove?.();
                
                // Clear all refs
                snackRef.current = null;
                // stateListenerRef.current = undefined;
                // logListenerRef.current = undefined;
            } catch (e) {
                console.error('Error during cleanup:', e);
            }
        };
    }, []);

    const handleDeviceChange = (device: DeviceType) => {
        // Force a re-render of the iframe
        if (webPreviewRef.current) {
            const currentSrc = webPreviewRef.current.src;
            webPreviewRef.current.src = '';
            setTimeout(() => {
                if (webPreviewRef.current) {
                    webPreviewRef.current.src = currentSrc;
                }
            }, 50);
        }
    };

    const refresh = () => {
        setIsRefreshed(true)
    }

    // Memoize the iframe to prevent unnecessary re-renders
    const iframeContent = useMemo(() => (
        <iframe
            ref={handleIframeRef}
            className="snack-iframe"
            style={{ width: '100%', height: '100%', border: 'none' }}
            src={isClientReady && snackState?.webPreviewURL ? snackState.webPreviewURL : undefined}
            allow="accelerometer; ambient-light-sensor; camera; encrypted-media; geolocation; gyroscope; magnetometer; microphone; usb; xr-spatial-tracking"
            sandbox="allow-forms allow-modals allow-pointer-lock allow-popups allow-presentation allow-same-origin allow-scripts"
        />
    ), [isClientReady, snackState?.webPreviewURL, handleIframeRef]);

    return (
        <div ref={containerRef} className="w-full h-full flex flex-col items-center justify-start pt-0 md:pt-4 gap-4 overflow-hidden bg-transparent relative">
            {dimensions.height > 0 && (
                <>
                    <div className="absolute bottom-4 right-2 z-[1000]">
                        <ExpoGoAlert size="icon" playerUrl={snackState?.url}/>
                    </div>
                    {!isMobile && (
                        <div className="flex flex-col gap-y-2 absolute top-0 z-10">

                            <div className="flex items-center justify-center gap-x-2">
                                <Button variant="outline" size="sm" onClick={refresh}>
                                    Preview not loading? Try refreshing
                                    <RefreshCcwIcon className="h-4 w-4"/>
                                </Button>
                                <ExpoInstructionsDialog previewUrl={snackState?.ur}/>

                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="bg-black/70 text-white hover:bg-black/80"
                                    onClick={toggleFullscreen}
                                >
                                    <Maximize2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>

                    )}
                    <div className="relative w-full h-full">
                        {isMobile ? (
                            <div className="w-full h-full bg-white overflow-hidden relative">
                                <div className="w-full h-full overflow-hidden pointer-events-auto">
                                    {iframeContent}
                                </div>
                                {isMobile && (
                                    <DraggableButton onClick={toggleFullscreen} className=" top-0" />
                                )}
                                {isLoading && (
                                    <div className="absolute inset-0 flex items-center justify-center bg-white z-20 pointer-events-none">
                                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                                    </div>
                                )}
                                {error && (
                                    <div className="absolute inset-0 flex items-center justify-center p-8 bg-red-50 z-20">
                                        <div className="text-red-600">Error: {error}</div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-8 h-full">
                                <PhoneFrame
                                    frameId="snack-phone-frame-v2"
                                    onDeviceChange={handleDeviceChange}
                                    containerHeight={dimensions.height}>
                                    <div className="relative w-full h-full bg-black overflow-hidden" style={
                                        {
                                            borderBottomLeftRadius: "55.75px",
                                            borderBottomRightRadius: "55.75px",
                                            borderTopLeftRadius: '24px',
                                            borderTopRightRadius: '24px',
                                        }
                                    }>
                                        {isLoading && (
                                            <div className="absolute inset-0 flex items-center justify-center bg-white z-20">
                                                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
                                            </div>
                                        )}
                                        {error && (
                                            <div className="absolute inset-0 flex items-center justify-center p-8 bg-red-50 z-20">
                                                <div className="text-red-600">Error: {error}</div>
                                            </div>
                                        )}
                                        <div className="snack-wrapper absolute inset-0 overflow-hidden">
                                            {iframeContent}
                                        </div>
                                    </div>
                                </PhoneFrame>
                            </div>
                        )}
                    </div>
                </>

            )}

            <style jsx>{`
                .snack-wrapper {
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    position: relative;
                    background: black;
                    display: flex;
                    align-items: stretch;
                    justify-content: stretch;
                    border-bottom-left-radius: 55.75px;
                    border-bottom-right-radius: 55.75px;
                    border-top-right-radius: 24px;
                    border-top-left-radius: 24px;
                }

                .snack-iframe {
                    flex: 1;
                    height: 100%;
                    width: 100%;
                    border: none;
                    display: block;
                    background: black !important;
                    position: absolute;
                    top: 0;
                    left: 0;
                    transform-origin: center;
                    -webkit-transform-origin: center;
                    backface-visibility: hidden;
                    -webkit-backface-visibility: hidden;
                    opacity: ${isLoading ? 0 : 1};
                    transition: opacity 0.3s ease;
                    //border-radius: 1.75rem;
                }

                .snack-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 40px;
                    background-color: white;
                    z-index: 10;
                }

                .snack-bottom-overlay {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 30px;
                    background-color: white;
                    z-index: 10;
                }
            `}</style>
        </div>
    );
});

export default SnackViewerV2;