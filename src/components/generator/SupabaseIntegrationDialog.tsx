import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import { Loader2 } from 'lucide-react';

interface SupabaseIntegrationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SupabaseIntegrationDialog({
  open,
  onOpenChange,
}: SupabaseIntegrationDialogProps) {
  const { data: connection, isLoading: isLoadingConnection } = useQuery({
    queryKey: ['integration', 'supabase'],
    queryFn: async () => {
      const response = await fetch('/api/integrations/supabase');
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Failed to fetch connection');
      }
      return response.json();
    },
  });

  const { data: databases, isLoading: isLoadingDatabases } = useQuery({
    queryKey: ['integration', 'supabase', 'databases', connection?.id],
    queryFn: async () => {
      if (!connection) return null;
      const response = await fetch('/api/integrations/supabase/databases');
      if (!response.ok) throw new Error('Failed to fetch databases');
      return response.json();
    },
    enabled: !!connection,
  });

  const handleConnect = () => {
    const provider = new URL('/api/integrations/supabase', window.location.origin);
    window.location.href = provider.toString();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Image
              src="/supabase-logo.svg"
              alt="Supabase"
              width={24}
              height={24}
              className="dark:filter dark:invert"
            />
            <DialogTitle>Supabase Integration</DialogTitle>
          </div>
          <DialogDescription>
            Connect your Supabase project to deploy databases for your mobile apps.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {isLoadingConnection ? (
            <div className="flex justify-center">
              <Loader2 className="h-6 w-6 animate-spin text-zinc-500" />
            </div>
          ) : !connection ? (
            <div className="space-y-4">
              <p className="text-sm text-zinc-500">
                Connect your Supabase project to get started.
              </p>
              <Button onClick={handleConnect} className="w-full">
                Connect Supabase
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">Connected</p>
                <Button
                  variant="ghost"
                  onClick={() =>
                    fetch('/api/integrations/supabase', {
                      method: 'DELETE',
                    }).then(() => window.location.reload())
                  }
                >
                  Disconnect
                </Button>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Your Databases</h4>
                {isLoadingDatabases ? (
                  <div className="flex justify-center">
                    <Loader2 className="h-4 w-4 animate-spin text-zinc-500" />
                  </div>
                ) : databases?.length ? (
                  <div className="rounded-md border">
                    {databases.map((db: any) => (
                      <div
                        key={db.id}
                        className="flex items-center justify-between p-3 text-sm"
                      >
                        <span>{db.name}</span>
                        <span className="text-zinc-500">{db.status}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-zinc-500">No databases found.</p>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
