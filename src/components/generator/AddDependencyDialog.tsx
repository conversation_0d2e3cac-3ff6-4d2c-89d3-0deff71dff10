import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface AddDependencyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (packageName: string, version: string) => void;
}

const AddDependencyDialog = ({ isOpen, onClose, onAdd }: AddDependencyDialogProps) => {
  const [packageName, setPackageName] = useState('');
  const [version, setVersion] = useState('*');
  const [error, setError] = useState('');
  
  const handleSubmit = () => {
    // Basic validation
    if (!packageName.trim()) {
      setError('Package name is required');
      return;
    }
    
    // Check for valid package name format (alphanumeric, hyphens, scopes, and forward slash delimiters)
    const packageNameRegex = /^(@[a-z0-9-~][a-z0-9-._~]*\/)?[a-z0-9-~][a-z0-9-._~]*(\/[a-z0-9-~][a-z0-9-._~]*)*$/i;
    if (!packageNameRegex.test(packageName)) {
      setError('Invalid package name format');
      return;
    }
    
    onAdd(packageName, version);
    resetForm();
  };
  
  const resetForm = () => {
    setPackageName('');
    setVersion('*');
    setError('');
  };
  
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Dependency</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="packageName" className="text-right">
              Package
            </Label>
            <Input
              id="packageName"
              value={packageName}
              onChange={(e) => {
                setPackageName(e.target.value);
                setError('');
              }}
              placeholder="e.g., react-native-maps"
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="version" className="text-right">
              Version
            </Label>
            <Input
              id="version"
              value={version}
              onChange={(e) => setVersion(e.target.value)}
              placeholder="e.g., ^1.0.0 or *"
              className="col-span-3"
            />
          </div>
          {error && (
            <div className="text-destructive text-xs mt-1">{error}</div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSubmit}>Add Dependency</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddDependencyDialog;
