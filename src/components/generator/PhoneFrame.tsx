'use client';

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useFrameDimensions } from '@/hooks/use-frame-dimensions';
import { Battery, Signal, Wifi } from 'lucide-react';
import { StatusBarTime } from './StatusBarTime';

export type DeviceType = 'pixel9pro' | 'iphone16promax';

interface PhoneFrameProps {
  children: React.ReactNode;
  frameId?: string;
  onDeviceChange?: (device: DeviceType) => void;
  containerHeight: number;
}

const deviceNames = {
  pixel9pro: 'Google Pixel 9 Pro',
  iphone16promax: 'iPhone 16 Pro Max'
};

export const PhoneFrame: React.FC<PhoneFrameProps> = ({ 
  children, 
  frameId = 'phone-frame', 
  onDeviceChange,
  containerHeight
}) => {
  const [device, setDevice] = React.useState<DeviceType>('iphone16promax');
  const dimensions = useFrameDimensions(device, containerHeight);

  const handleDeviceChange = (value: DeviceType) => {
    setDevice(value);
    onDeviceChange?.(value);
  };

  if (!dimensions.ready) {
    return null; // Don't render anything until we have calculated dimensions
  }

  return (
    <div className="flex flex-col items-center gap-4 h-full">
      {/*<Select value={device} onValueChange={handleDeviceChange}>*/}
      {/*  <SelectTrigger className="w-[200px]">*/}
      {/*    <SelectValue placeholder="Select device" />*/}
      {/*  </SelectTrigger>*/}
      {/*  <SelectContent>*/}
      {/*    <SelectItem value="pixel9pro">{deviceNames.pixel9pro}</SelectItem>*/}
      {/*    <SelectItem value="iphone16promax">{deviceNames.iphone16promax}</SelectItem>*/}
      {/*  </SelectContent>*/}
      {/*</Select>*/}

      <div id={frameId} className="flex flex-1 w-full">
        <div className="flex flex-col w-full flex-1 overflow-hidden">
          <div 
            className="h-full relative" 
            style={{ 
              aspectRatio: '0.49093 / 1',
              maxHeight: `${dimensions.height}px`,
              margin: '0 auto',
              pointerEvents: 'none'
            }}>
            <svg
                width="100%"
                height="100%"
                viewBox="0 0 433 882"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                preserveAspectRatio="xMidYMid meet"
                className="w-full h-full"
            >
              <defs>
                <linearGradient id="blackGradient" x1="0" y1="0" x2="433" y2="882">
                  <stop offset="0%" stopColor="#5d5d5d" stopOpacity="0.1" />
                  <stop offset="50%" stopColor="#1A1A1A" stopOpacity="0.05" />
                  <stop offset="100%" stopColor="#0A0A0A" stopOpacity="0.1" />
                </linearGradient>
                <clipPath id="roundedCorners">
                  <rect x="14" y="12" width="404" height="858" rx="62" ry="62"/>
                </clipPath>

              </defs>
              {/*Main frame - Ultra Thin Black */}
              <path
                  d="M10 73C10 37.6538 38.6538 9 74 9H359C394.346 9 423 37.6538 423 73V809C423 844.346 394.346 873 359 873H74C38.6538 873 10 844.346 10 809V73Z"
                  className="fill-[#2e2e2e] dark:fill-[#2e2e2e]"/>

              {/* Volume buttons - Darker black */}
              <path d="M8 171C8 170.448 8.44772 170 9 170H11V204H9C8.44772 204 8 203.552 8 203V171Z"
                    className="fill-[#5d5d5d] dark:fill-[#5d5d5d]"/>
              <path d="M9 234C9 233.448 9.44772 233 10 233H11.5V300H10C9.44772 300 9 299.552 9 299V234Z"
                    className="fill-[#5d5d5d] dark:fill-[#5d5d5d]"/>
              <path d="M9 319C9 318.448 9.44772 318 10 318H11.5V385H10C9.44772 385 9 384.552 9 384V319Z"
                    className="fill-[#5d5d5d] dark:fill-[#5d5d5d]"/>

              {/* Power button - Darker black */}
              <path d="M422 279H424C424.552 279 425 279.448 425 280V384C425 384.552 424.552 385 424 385H422V279Z"
                    className="fill-[#5d5d5d] dark:fill-[#5d5d5d]"/>

              {/* Inner frame - Deep black (50% thinner bezel) */}
              <path
                  d="M14 74C14 39.7583 41.7583 12 76 12H356C390.242 12 418 39.7583 418 74V808C418 842.242 390.242 870 356 870H76C41.7583 870 14 842.242 14 808V74Z"
                  className="fill-[#0A0A0A] dark:fill-[#0A0A0A]"/>

              {/* Dynamic Island */}
              {/*<path opacity="1"*/}
              {/*      d="M174 5H258V5.5C258 6.60457 257.105 7.5 256 7.5H176C174.895 7.5 174 6.60457 174 5.5V5Z"*/}
              {/*      className="fill-[#5d5d5d] dark:fill-[#5d5d5d]"/>*/}

              {/* Screen bezel */}
              <path
                  d="M21.25 75C21.25 44.2101 46.2101 19.25 77 19.25H355C385.79 19.25 410.75 44.2101 410.75 75V807C410.75 837.79 385.79 862.75 355 862.75H77C46.2101 862.75 21.25 837.79 21.25 807V75Z"
                  className="fill-[#5d5d5d] dark:fill-[#5d5d5d]"/>

              {/* Screen background */}
              <path
                  d="M21.25 75C21.25 44.2101 46.2101 19.25 77 19.25H355C385.79 19.25 410.75 44.2101 410.75 75V807C410.75 837.79 385.79 862.75 355 862.75H77C46.2101 862.75 21.25 837.79 21.25 807V75Z"
                  className="fill-white dark:fill-white"/>

               {/*Status Bar - with higher z-index */}
              <foreignObject x="21.25" y="19.25" width="389.5" height="48" style={{zIndex: 20}} className="pointer-events-auto">
                <div className="w-full h-full flex items-center justify-between px-8 text-black dark:text-black">
                  <StatusBarTime />
                  <div className="flex-1"></div> {/* Spacer for Dynamic Island */}
                  <div className="flex items-center gap-3">
                    <svg width="18" height="12" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-black dark:text-black">
                      <rect x="0" y="7" width="3" height="5" rx="1" fill="currentColor" />
                      <rect x="5" y="5" width="3" height="7" rx="1" fill="currentColor" />
                      <rect x="10" y="3" width="3" height="9" rx="1" fill="currentColor" />
                      <rect x="15" y="1" width="3" height="11" rx="1" fill="currentColor" />
                    </svg>
                    <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-black dark:text-black">
                      <path fillRule="evenodd" clipRule="evenodd" d="M7.99967 2.39999C5.96634 2.39999 4.04634 3.11999 2.613 4.39999L1.33301 3.11999C3.15967 1.43999 5.49967 0.399994 7.99967 0.399994C10.4997 0.399994 12.8397 1.43999 14.6663 3.11999L13.3863 4.39999C11.953 3.11999 10.033 2.39999 7.99967 2.39999Z" fill="currentColor"/>
                      <path fillRule="evenodd" clipRule="evenodd" d="M7.99967 6.39999C6.76634 6.39999 5.61301 6.83999 4.73301 7.59999L3.45301 6.31999C4.693 5.19999 6.27301 4.51999 7.99967 4.51999C9.72634 4.51999 11.3063 5.19999 12.5463 6.31999L11.2663 7.59999C10.3863 6.83999 9.23301 6.39999 7.99967 6.39999Z" fill="currentColor"/>
                      <path fillRule="evenodd" clipRule="evenodd" d="M7.99967 10.4C7.55967 10.4 7.15967 10.24 6.85301 9.96L7.99967 8.8L9.14634 9.96C8.83967 10.24 8.43967 10.4 7.99967 10.4Z" fill="currentColor"/>
                    </svg>
                    <div className="flex items-center">
                      <svg width="24" height="12" viewBox="0 0 24 12" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-black dark:text-black">
                        <rect x="0.5" y="0.5" width="20" height="11" rx="2.5" stroke="currentColor" />
                        <rect x="2" y="2" width="12.16" height="8" rx="1" fill="currentColor" />
                        <path d="M23 4V8C23.8284 8 24.5 7.32843 24.5 6.5V5.5C24.5 4.67157 23.8284 4 23 4Z" fill="currentColor" />
                      </svg>
                    </div>
                  </div>
                </div>
              </foreignObject>

              {/* Main Content Area */}
              <foreignObject x="21" y="14" width="390.4" height="824" className="overflow-hidden pointer-events-auto">
                <div className="w-full h-full">
                  <div className="h-[52px]"></div> {/* Spacer for status bar */}
                  <div className="h-[calc(100%-52px)] overflow-hidden" style={{
                    borderRadius: 24,
                    borderTopLeftRadius: 2,
                    borderTopRightRadius: 2,
                  }}> {/* Content area minus status bar */}
                    {children}
                  </div>
                </div>
              </foreignObject>
              
              {/* Simple Bottom Indicator */}
              <g transform="translate(0, 5)">
                <rect x="136" y="840" width="160" height="6" rx="3" ry="3" fill="#000000" opacity="0.3" />
              </g>

              {/* Dynamic Island housing - width increased by 40% */}
              <path
                  d="M145 42.5C145 35.5964 150.596 30 157.5 30H275.5C282.404 30 288 35.5964 288 42.5C288 49.4036 282.404 55 275.5 55H157.5C150.596 55 145 49.4036 145 42.5Z"
                  className="fill-[#000000] dark:fill-[#000000]"/>

              {/* Camera and sensors */}
              <path
                  d="M270 42.5C270 38.3579 273.358 35 277.5 35C281.642 35 285 38.3579 285 42.5C285 46.6421 281.642 50 277.5 50C273.358 50 270 46.6421 270 42.5Z"
                  className="fill-[#333333] dark:fill-[#333333]"/>
              <path
                  d="M274 42.5C274 40.567 275.567 39 277.5 39C279.433 39 281 40.567 281 42.5C281 44.433 279.433 46 277.5 46C275.567 46 274 44.433 274 42.5Z"
                  className="fill-[#000000] dark:fill-[#000000]"/>

               {/* Overlay the black gradient */}
              {/*<path*/}
              {/*    d="M2 73C2 32.6832 34.6832 0 75 0H357C397.317 0 430 32.6832 430 73V809C430 849.317 397.317 882 357 882H75C34.6832 882 2 849.317 2 809V73Z"*/}
              {/*    fill="url(#blackGradient)"/>*/}
            </svg>
          </div>
        </div>
      </div>
    </div>
  )
};
