'use client';

import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { ProjectSession } from '@/stores/ProjectSessionStore';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';

interface SnackErrorObserverProps {
    session: ProjectSession;
}

export const SnackErrorObserver = observer(({ session }: SnackErrorObserverProps) => {
    useEffect(() => {
        const error = session.snackError;
        if(session.state.status === "streaming") {
            return;
        }
        if (error && !error.handled) {
            toast(
                <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                        <Bot className="h-5 w-5 text-primary mt-1" />
                        <div className="flex-1">
                            <p className="font-medium mb-1">{error.type === 'supabase' ? 'Error running SQL query': 'Error in Preview' }</p>
                            <p className="text-sm text-muted-foreground">{error.message}</p>
                        </div>
                    </div>
                    <Button 
                        variant="default" 
                        className="w-full" 
                        onClick={() => {
                            session.handleErrorFix();
                            toast.dismiss();
                        }}
                    >
                        Fix with AI
                        <Sparkles className="h-4 w-4 ml-2" />
                    </Button>
                </div>,
                {
                    duration: Infinity,
                    dismissible: true,
                    position: "bottom-right",
                    onDismiss: () => {
                        session.markErrorAsHandled();
                    }
                }
            );
        }
    }, [session, session.snackError]);

    return null;
});
