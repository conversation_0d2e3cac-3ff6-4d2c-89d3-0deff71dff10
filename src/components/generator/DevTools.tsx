import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { Terminal, Package, ChevronUp, ChevronDown, Trash2, AlertCircle, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { LogViewer } from '@/components/generator/LogViewer';
import { LogEntry } from "@/types/logs";
import AddDependencyDialog from './AddDependencyDialog';

interface DevToolsProps {
  chatId: string;
  className?: string;
}

interface DependencyItemProps {
  name: string;
  version: string;
  onRemove: (name: string) => void;
}

const DependencyItem = ({ name, version, onRemove }: DependencyItemProps) => {
  return (
    <div className="py-1 px-2 border-b border-border text-[10px] leading-tight font-mono">
      <div className="flex items-center justify-between">
        <span className="font-semibold">{name}</span>
        <div className="flex items-center gap-1">
          <Badge variant="outline" className="h-4 px-1 text-[10px]">{version}</Badge>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-4 w-4 p-0 hover:bg-transparent hover:text-destructive"
            onClick={(e) => {
              e.stopPropagation();
              onRemove(name);
            }}
            title="Remove dependency"
          >
            <Trash2 className="h-2.5 w-2.5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const DevToolsBase = ({ chatId, className = '' }: DevToolsProps) => {
  // State for dependency search and dialog
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDependencyDialogOpen, setIsAddDependencyDialogOpen] = useState(false);
  
  // Add CSS to highlight the header when there are errors
  React.useEffect(() => {
    // Add a style tag for the error highlight if it doesn't exist
    if (!document.getElementById('devtools-error-style')) {
      const style = document.createElement('style');
      style.id = 'devtools-error-style';
      style.innerHTML = `
        [data-error="true"] {
          background-color: rgba(239, 68, 68, 0.05);
        }
      `;
      document.head.appendChild(style);
    }
    
    return () => {
      // Clean up the style when component unmounts
      const style = document.getElementById('devtools-error-style');
      if (style) style.remove();
    };
  }, []);
  const { logStore, snackStore, generatorStore } = useStores();
  const [activeTab, setActiveTab] = useState<string>('logs');
  const [isExpanded, setIsExpanded] = useState(false);
  const [dependencies, setDependencies] = useState<Record<string, any>>({});
  const session = generatorStore.getActiveSession(chatId);

  if(!session) {
    return null;
  }
  
  // Get logs and filter for errors to count them
  const logs = logStore.getLogs(chatId);
  const errorCount = logs.filter(log => log.type === 'error').length;
  
  // Update dependencies when snack state changes
  useEffect(() => {
    const updateDependencies = () => {
      setDependencies(session.dependencies);
    };
    
    // Initial update
    updateDependencies();
    
    // Set up interval to check for dependency changes
    const intervalId = setInterval(updateDependencies, 2000);
    
    return () => clearInterval(intervalId);
  }, [chatId, session.dependencies]);
  
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };
  
  // Count dependencies
  const dependencyCount = Object.keys(dependencies).length;
  
  return (
    <div className={cn("bg-card border-t border-border w-full z-10 flex-col overflow-hidden", className)}>
      {/* Header - Always visible */}
      <div 
        className="h-8 px-2 flex items-center justify-between cursor-pointer hover:bg-muted/30 transition-colors"
        onClick={toggleExpanded}
        data-error={errorCount > 0 ? 'true' : 'false'}
      >
        <div className="flex items-center space-x-2">
          <Terminal className="h-3.5 w-3.5 text-muted-foreground" />
          <span className="text-xs font-medium">DevTools</span>
          
          {/* Error indicator */}
          {errorCount > 0 && (
            <Badge variant="destructive" className="h-4 px-1 text-[10px] flex items-center animate-pulse">
              <AlertCircle className="h-2 w-2 mr-0.5" />
              {errorCount}
            </Badge>
          )}
          
          {/* Dependency count */}
          {dependencyCount > 0 && (
            <Badge variant="outline" className="h-4 px-1 text-[10px] flex items-center">
              <Package className="h-2 w-2 mr-0.5" />
              {dependencyCount}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center">
          {isExpanded ? (
            <ChevronUp className="h-3.5 w-3.5 text-muted-foreground" />
          ) : (
            <ChevronDown className="h-3.5 w-3.5 text-muted-foreground" />
          )}
        </div>
      </div>
      
      {/* Expanded content */}
      {isExpanded && (
        <div className="flex flex-col h-full w-full overflow-hidden">
          <Tabs defaultValue="logs" value={activeTab} onValueChange={setActiveTab} className="w-full overflow-hidden">
            <TabsList className="bg-card p-0 h-6 rounded-none border-b border-border flex overflow-x-auto">
              <TabsTrigger 
                value="logs" 
                className="h-6 px-2 text-[10px] rounded-none border-b-2 border-transparent data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:text-primary"
              >
                Logs {errorCount > 0 && `(${errorCount})`}
              </TabsTrigger>
              <TabsTrigger 
                value="dependencies" 
                className="h-6 px-2 text-[10px] rounded-none border-b-2 border-transparent data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-primary data-[state=active]:text-primary"
              >
                Dependencies ({dependencyCount})
              </TabsTrigger>
            </TabsList>
            
            {/* Logs Tab */}
            <TabsContent value="logs" className="p-0 m-0 mt-0 flex-1 h-[244px] overflow-hidden">
              <ScrollArea className="h-full">
                <LogViewer chatId={chatId} />
              </ScrollArea>
            </TabsContent>
            
            {/* Dependencies Tab */}
            <TabsContent value="dependencies" className="p-0 m-0 mt-0 flex-1 h-[244px] overflow-hidden">
              <ScrollArea className="h-full bg-background">
                {dependencyCount === 0 ? (
                  <div className="flex items-center justify-center h-full text-muted-foreground text-xs">
                    No dependencies detected
                  </div>
                ) : (
                  <>
                    <div className="py-1 px-2 border-b border-border text-[10px] font-medium flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span>Package dependencies ({dependencyCount})</span>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-4 w-4 p-0 hover:bg-transparent hover:text-primary"
                          onClick={() => setIsAddDependencyDialogOpen(true)}
                          title="Add dependency"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="relative w-32">
                        <input
                          type="text"
                          placeholder="Search..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="w-full h-5 text-[10px] px-2 py-0.5 rounded-sm border border-border bg-background focus:outline-none focus:ring-1 focus:ring-primary"
                        />
                      </div>
                    </div>
                    {Object.entries(dependencies)
                      // Sort alphabetically by package name
                      .sort(([nameA], [nameB]) => nameA.localeCompare(nameB))
                      // Filter based on search query
                      .filter(([name]) => searchQuery === '' || name.toLowerCase().includes(searchQuery.toLowerCase()))
                      .map(([name, info]) => (
                        <DependencyItem 
                          key={name} 
                          name={name} 
                          version={typeof info === 'object' && info.version ? info.version : '*'}
                          onRemove={async (packageName) => {
                            try {
                              await session.removeDependency(packageName);
                            } catch (error) {
                              console.error('Error removing dependency:', error);
                            }
                          }}
                        />
                      ))}
                    {/* Show message when no search results */}
                    {searchQuery !== '' && 
                      Object.entries(dependencies).filter(([name]) => 
                        name.toLowerCase().includes(searchQuery.toLowerCase())
                      ).length === 0 && (
                        <div className="py-2 px-2 text-center text-muted-foreground text-[10px]">
                          No matching dependencies found
                        </div>
                      )}
                  </>
                )}
              </ScrollArea>
              
              {/* Add Dependency Dialog */}
              <AddDependencyDialog 
                isOpen={isAddDependencyDialogOpen}
                onClose={() => setIsAddDependencyDialogOpen(false)}
                onAdd={async (packageName: string, version: string) => {
                  try {
                    // Add the dependency - this now automatically saves to the API
                    await session.addDependency(packageName, version);
                    // Close the dialog
                    setIsAddDependencyDialogOpen(false);
                  } catch (error) {
                    console.error('Error adding dependency:', error);
                  }
                }}
              />
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
};

// Export memoized component to prevent unnecessary rerenders
export const DevTools = observer(DevToolsBase);
