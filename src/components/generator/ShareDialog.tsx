import React, {useState} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DialogTrigger,
} from "@/components/ui/dialog";
import {Button} from "@/components/ui/button";
import {Share2, Link as LinkIcon, Copy, Twitter, Linkedin} from "lucide-react";
import {VisibilitySelector, VisibilityType} from './VisibilitySelector';
import {toast} from 'sonner';

interface ShareDialogProps {
    chatId: string;
    initialVisibility: VisibilityType;
}

export const ShareDialog: React.FC<ShareDialogProps> = ({
                                                            chatId,
                                                            initialVisibility,
                                                        }) => {
    const [open, setOpen] = useState(false);
    const url = `${window.location.protocol}//${window.location.host}/project/${chatId}/preview`

    const handleCopyLink = async () => {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(url);
            } else {
                // Fallback for non-HTTPS or browsers without clipboard API
                const textArea = document.createElement('textarea');
                textArea.value = url;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    textArea.remove();
                } catch (err) {
                    console.error('Fallback: Oops, unable to copy', err);
                    throw new Error('Failed to copy');
                }
            }
            toast.success('Link copied to clipboard!');
        } catch (err) {
            console.error('Failed to copy link:', err);
            toast.error('Failed to copy link');
        }
    };

    const handleShare = (platform: string) => {
        const encodedUrl = encodeURIComponent(url);
        const text = encodeURIComponent("✨ Just built a mobile app in minutes using AI! No coding required. Try it out:");
        const hashtags = encodeURIComponent("AI,MobileApp,NoCode,MagicallyAI,BuildInPublic");

        let shareUrl = "";
        switch (platform) {
            case "twitter":
                shareUrl = `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${text}&hashtags=${hashtags}`;
                break;
            case "linkedin":
                shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
                break;
        }

        if (shareUrl) {
            window.open(shareUrl, "_blank", "noopener,noreferrer");
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button
                    variant="ghost"
                    size="icon"
                    className="text-gray-400 hover:text-white"
                >
                    <Share2 className="h-5 w-5"/>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md bg-background border-border text-foreground p-6">
                <DialogHeader className="text-center space-y-4">
                    <div
                        className="inline-flex p-3 rounded-full bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-600/10">
                        <div className="text-3xl animate-bounce-subtle">✨🎉✨</div>
                    </div>
                    <div className="space-y-2.5">
                        <DialogTitle
                            className="text-2xl font-bold bg-gradient-to-r from-blue-500 via-purple-500 to-pink-600 bg-clip-text text-transparent">
                            Congratulations!
                        </DialogTitle>
                        <p className="text-lg font-medium text-foreground/90">
                            Your Mobile App is Ready!
                        </p>
                    </div>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                        You've just created a mobile app with AI in minutes — that's incredible!
                    </p>
                </DialogHeader>
                <div className="relative mt-6">
                    <div
                        className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-600/5 rounded-lg blur-sm"
                        aria-hidden="true"/>
                    <div className="relative space-y-6">
                        {/* Visibility Section */}
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <label className="text-sm font-medium text-gray-400">
                                    Visibility
                                </label>
                                <div className="flex items-center gap-1.5">
                                    <div className="w-1.5 h-1.5 rounded-full bg-green-500 animate-pulse"/>
                                    <span className="text-xs text-muted-foreground">Live</span>
                                </div>
                            </div>
                            <VisibilitySelector
                                chatId={chatId}
                                selectedVisibilityType={initialVisibility}
                                className="w-full"
                            />
                        </div>

                        {/* Share Options */}
                        <div className="space-y-4 mt-6">
                            <div className="grid grid-cols-2 gap-3">
                                <Button
                                    variant="outline"
                                    className="bg-[#1DA1F2]/10 hover:bg-[#1DA1F2]/20 hover:text-[#1DA1F2] h-16 flex flex-col items-center justify-center space-y-1.5 group transition-all duration-200"
                                    onClick={() => handleShare("twitter")}
                                >
                                    <Twitter className="h-5 w-5 transition-colors duration-200"/>
                                    <span className="text-xs font-medium">Share on Twitter</span>
                                </Button>
                                <Button
                                    variant="outline"
                                    className="bg-[#0A66C2]/10 hover:bg-[#0A66C2]/20 hover:text-[#0A66C2] h-16 flex flex-col items-center justify-center space-y-1.5 group transition-all duration-200"
                                    onClick={() => handleShare("linkedin")}
                                >
                                    <Linkedin className="h-5 w-5 transition-colors duration-200"/>
                                    <span className="text-xs font-medium">Share on LinkedIn</span>
                                </Button>
                            </div>

                            {/* Copy Link Section */}
                            <div className="space-y-3 mt-4">
                                <Button
                                    variant="outline"
                                    className="w-full bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-600/5 hover:from-blue-500/10 hover:via-purple-500/10 hover:to-pink-600/10 h-12 transition-all duration-200"
                                    onClick={handleCopyLink}
                                >
                                    <Copy className="h-4 w-4 mr-2 text-muted-foreground"/>
                                    <span>Copy App Link</span>
                                </Button>
                                <p className="text-xs text-muted-foreground/80 text-center px-6">
                                    Anyone can instantly view and test your app in their browser
                                </p>
                            </div>

                        </div>
                    </div>


                </div>
            </DialogContent>
        </Dialog>
    );
};
