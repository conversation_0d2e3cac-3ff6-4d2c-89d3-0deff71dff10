'use client';

import React from 'react';
import Editor from '@monaco-editor/react';
import { ChevronRightIcon } from 'lucide-react';
import { editorTheme } from '@/styles/themes/editor';
import { editor, languages } from 'monaco-editor';
import { DEFAULT_DEPENDENCIES } from '@/types/editor';

interface EditorLayoutProps {
  path: string;
  content: string;
  language: string;
  onChange: (value: string | undefined) => void;
}

export const EditorLayout: React.FC<EditorLayoutProps> = ({
  path,
  content,
  language,
  onChange,
}) => {
  return (
    <div className="flex-1 flex flex-col h-full bg-[#1E2127]">
      {/* Breadcrumb */}
      <div className="flex items-center px-4 text-sm text-[#8F96A3] border-b border-[#181A1F]">
        <ChevronRightIcon className="h-4 w-4 mx-1" />
        <span className="text-white">{path}</span>
      </div>

      {/* Editor */}
      <div className="flex-1">
        <Editor
          height="100%"
          defaultLanguage={language || 'typescript'}
          value={content}
          onChange={onChange}
          theme="custom-dark"
          beforeMount={(monaco) => {
            monaco.editor.defineTheme('custom-dark', editorTheme);

            // Configure React Native TypeScript settings
            monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
              jsx: monaco.languages.typescript.JsxEmit.React,
              jsxFactory: 'React.createElement',
              reactNamespace: 'React',
              allowNonTsExtensions: true,
              moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
              target: monaco.languages.typescript.ScriptTarget.Latest,
              allowJs: true,
              typeRoots: ['node_modules/@types']
            });

            // Add React Native type definitions
            monaco.languages.typescript.typescriptDefaults.addExtraLib(
              `declare module 'react-native' {
                export interface ViewStyle {
                  flex?: number;
                  backgroundColor?: string;
                  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch';
                  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around';
                  // Add more style properties as needed
                }
                export interface TextStyle {
                  fontSize?: number;
                  color?: string;
                  fontWeight?: string;
                  // Add more text style properties as needed
                }
                export interface StyleSheet {
                  create<T extends { [key: string]: any }>(styles: T): T;
                }
                export const StyleSheet: StyleSheet;
                export const View: React.ComponentType<{ style?: ViewStyle }>;
                export const Text: React.ComponentType<{ style?: TextStyle }>;
              }`,
              'react-native.d.ts'
            );

            // Add dependency validation
            const allowedDependencies = Object.keys(DEFAULT_DEPENDENCIES);
            monaco.languages.registerHoverProvider('typescript', {
              provideHover: (model, position) => {
                const word = model.getWordAtPosition(position);
                if (word) {
                  const importMatch = model.getLineContent(position.lineNumber).match(/from\s+['"]([^'"]+)/)
                  if (importMatch) {
                    const dependency = importMatch[1];
                    if (!allowedDependencies.includes(dependency)) {
                      return {
                        contents: [{
                          value: `⚠️ Warning: ${dependency} is not in the allowed dependencies list.\n\nAllowed dependencies:\n${allowedDependencies.join('\n')}`
                        }]
                      };
                    } else {
                      return {
                        contents: [{
                          value: `✅ ${dependency} is an allowed dependency.\nVersion: ${DEFAULT_DEPENDENCIES[dependency].version}`
                        }]
                      };
                    }
                  }
                }
                return null;
              }
            });
          }}
          options={{
            fontFamily: 'JetBrains Mono, monospace',
            fontSize: 12,
            lineHeight: 1.4,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            renderLineHighlight: 'all',
            lineNumbers: 'on',
            cursorStyle: 'line',
            cursorWidth: 2,
            padding: { top: 16 },
            smoothScrolling: true,
            wordWrap: 'on',
            automaticLayout: true,
          } as editor.IStandaloneEditorConstructionOptions}
        />
      </div>
    </div>
  );
};
