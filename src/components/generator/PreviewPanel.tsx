'use client';

import React, {useState, useRef, useContext, createContext, useCallback, useEffect, lazy, Suspense} from 'react';
import {computed, reaction} from "mobx";
import {observer} from "mobx-react-lite";
import {Button} from "@/components/ui/button";
import {EditorV2} from '@/components/editor-v2/Editor';
import SnackViewerV2 from "@/components/generator/SnackViewerV2";
import {FileItem, CodeBlock, FileNode} from "@/types/file";
import {Code, Eye, Terminal as TerminalIcon, Rocket, RefreshCcwIcon, Maximize2, Database} from 'lucide-react';
import { SupabasePanel } from '@/components/integrations/supabase/SupabasePanel';
import {migrateToV2} from '@/utils/file-converter';
import {useStores} from "@/stores/utils/useStores";
import {useIsMobile} from "@/hooks/use-mobile";
import {DesktopPreview} from "@/components/preview/DesktopPreview";
import DesktopSnackViewer from "@/components/generator/DesktopSnackViewer";
import {DevTools} from "@/components/generator/DevTools";
import {Terminal} from "@/components/terminal/Terminal";
import ViewInMobileButton from "@/components/generator/ViewInMobileButton";
import {ConfirmationDialog} from "@/components/ui/confirmation-dialog";
import { trackIdeaFlowEvent, trackFeatureEvent } from "@/lib/analytics/track";

interface PreviewPanelProps {
    onOperationChange: (codeBlock: CodeBlock) => void;
    className?: string;
    chatId: string;
    projectId: string;
}

// Create a context for the visibility state
const VisibilityContext = createContext<{
    isVisible: boolean;
}>({isVisible: false});

// Component that only renders its children when visible
const VisibilityWrapper = React.memo(({isVisible, children}: { isVisible: boolean; children: React.ReactNode }) => {
    const divRef = useRef<HTMLDivElement>(null);
    const [mounted, setMounted] = useState(false);
    const previousVisibility = useRef(isVisible);

    // Only show children after initial mount to prevent double render
    useEffect(() => {
        if (!mounted) {
            setMounted(true);
            previousVisibility.current = isVisible;
        } else if (previousVisibility.current !== isVisible) {
            console.log('VisibilityWrapper visibility changed:', { 
                from: previousVisibility.current, 
                to: isVisible 
            });
            previousVisibility.current = isVisible;
        }
    }, [isVisible, mounted]);

    if (!mounted) return null;

    return (
        <VisibilityContext.Provider value={{isVisible}}>
            <div
                ref={divRef}
                className="absolute inset-0 w-full h-full"
                style={{display: isVisible ? 'block' : 'none'}}
            >
                {children}
            </div>
        </VisibilityContext.Provider>
    );
}, (prev, next) => {
    console.log('VisibilityWrapper memo compare:', {
        prevVisible: prev.isVisible,
        nextVisible: next.isVisible,
        equal: prev.isVisible === next.isVisible
    });
    return prev.isVisible === next.isVisible;
});

// Memoized preview component
const Preview = React.memo(({chatId, projectId, onSnackReady, isMobile}: {
    chatId: string;
    projectId: string;
    onSnackReady: (url: string) => void,
    isMobile: boolean
}) => {
    console.log('Preview render with:', { chatId, onSnackReady, isMobile });
    
    // Use state to prevent flickering during initial load
    const [initialRender, setInitialRender] = React.useState(true);
    const [firstPreviewTracked, setFirstPreviewTracked] = React.useState(false);
    const loadStartTime = React.useRef(Date.now());
    
    React.useEffect(() => {
        if (initialRender) {
            setInitialRender(false);
        }
    }, []);
    
    // Track first preview load
    React.useEffect(() => {
        if (!firstPreviewTracked) {
            // We'll track this after a short delay to ensure the preview has loaded
            const timer = setTimeout(() => {
                trackIdeaFlowEvent('FIRST_PREVIEW_LOADED', {
                    chat_id: chatId,
                    time_to_first_preview: Date.now() - loadStartTime.current
                });
                setFirstPreviewTracked(true);
            }, 2000); // Give the preview 2 seconds to load
            
            return () => clearTimeout(timer);
        }
    }, [chatId, firstPreviewTracked]);

    // During initial render on mobile, show mobile view to prevent flicker
    if (initialRender && typeof window !== 'undefined' && window.innerWidth < 768) {
        return <DesktopSnackViewer chatId={chatId} projectId={projectId}/>;
    }

    return isMobile ? 
        <DesktopSnackViewer chatId={chatId} projectId={projectId}/> :
        <DesktopSnackViewer chatId={chatId} projectId={projectId}/>;
}, (prev, next) => {
    console.log('Preview memo compare:', {
        prevProps: { chatId: prev.chatId, onSnackReady: prev.onSnackReady, isMobile: prev.isMobile },
        nextProps: { chatId: next.chatId, onSnackReady: next.onSnackReady, isMobile: next.isMobile },
        equal: prev.chatId === next.chatId && prev.onSnackReady === next.onSnackReady && prev.isMobile === next.isMobile
    });
    return prev.chatId === next.chatId && prev.onSnackReady === next.onSnackReady && prev.isMobile === next.isMobile;
});

// Memoized editor component
const Editor = React.memo(({chatId, onFileOperation}: { chatId: string; onFileOperation: any }) => {
    console.log('Editor render with:', { chatId });
    const editorSessionStartTime = React.useRef(Date.now());
    const {generatorStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);
    
    // Track editor session start
    React.useEffect(() => {
        // Track when user starts using the code editor
        trackFeatureEvent('CODE_EDITED', {
            feature_name: 'editor_session_start',
            is_enabled: true,
            user_type: 'free', // This should be dynamically determined based on subscription tier
            project_id: session?.projectId || '',
            trigger_source: 'tab_code'
        });
        
        // Track editor session end on unmount
        return () => {
            const sessionDuration = Date.now() - editorSessionStartTime.current;
            // Only track if session was longer than 5 seconds (to filter out quick tab switches)
            if (sessionDuration > 5000) {
                trackFeatureEvent('CODE_EDITED', {
                    feature_name: 'editor_session_end',
                    is_enabled: true,
                    user_type: 'free', // This should be dynamically determined based on subscription tier
                    project_id: session?.projectId || '',
                    deployment_time: sessionDuration, // Reusing this field to track session duration
                    trigger_source: 'tab_switch'
                });
            }
        };
    }, [chatId, session]);
    
    return <EditorV2 chatId={chatId} onFileOperation={onFileOperation}/>;
}, (prev, next) => {
    const equal = prev.chatId === next.chatId;
    console.log('Editor memo compare:', { 
        prevChatId: prev.chatId,
        nextChatId: next.chatId,
        equal
    });
    return equal;
});

const TabHeader = observer(({currentTab, onTabChange, chatId, projectId}: { currentTab: string, onTabChange: (tab: string) => void, chatId: string, projectId }) => {
    // Use ref to track renders and changes
    const renderCount = useRef(0);
    const prevTab = useRef(currentTab);

    const {generatorStore, snackStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);
    const project = generatorStore.getProjectData(projectId);


    useEffect(() => {
        renderCount.current++;
        if (prevTab.current !== currentTab) {
            console.log('TabHeader tab changed:', {
                from: prevTab.current,
                to: currentTab,
                renderCount: renderCount.current
            });
            
            // Track tab change event with more detailed properties
            trackFeatureEvent('FILE_TREE_NAVIGATED', {
                feature_name: 'preview_panel_tabs',
                is_enabled: true,
                user_type: 'free', // This should be dynamically determined
                trigger_source: `tab_${currentTab}`,
                previous_state: prevTab.current !== '' ? true : false,
                new_state: true,
                file_path: currentTab // Using tab name as the path for analytics
            });
            
            prevTab.current = currentTab;
        }
    }, [currentTab]);

    const toggleFullscreen = () => {
        // Track fullscreen toggle event
        trackFeatureEvent('FULL_SCREEN_TOGGLED', {
            feature_name: 'preview_fullscreen_toggle',
            is_enabled: true,
            user_type: 'free', // This should be dynamically determined
            trigger_source: 'fullscreen_button',
            project_id: session?.projectId || '',
            previous_state: session?.fullScreen || false,
            new_state: !(session?.fullScreen || false)
        });
        
        session?.toggleFullScreen();
    }

    const refresh = () => {
        console.log(`[REFRESH_TRACE] PreviewPanel.refresh: Manual refresh triggered for chatId: ${chatId}`);
        const refreshTime = new Date().getTime();
        
        // Track preview refresh event
        trackFeatureEvent('PREVIEW_REFRESHED', {
            feature_name: 'preview_manual_refresh',
            is_enabled: true,
            user_type: 'free', // This should be dynamically determined
            trigger_source: 'refresh_button',
            project_id: session?.projectId || ''
        });
        
        snackStore.reloadSnack(chatId);
        
        console.log(`[REFRESH_TRACE] PreviewPanel.refresh: Refresh initiated at ${refreshTime} for chatId: ${chatId}`);
    }

    return (
            <div className="flex items-center justify-between w-full">
                <div className="flex items-center">
                    <button
                        onClick={() => onTabChange('preview')}
                        className={`h-8 px-3 flex items-center gap-2 text-xs rounded-sm hover:bg-muted/50 transition-colors
                ${currentTab === 'preview' ? 'bg-muted text-foreground' : 'text-muted-foreground'}`}
                    >
                        <Eye className="h-3.5 w-3.5"/>
                        Preview
                    </button>
                    <button
                        onClick={() => onTabChange('code')}
                        className={`h-8 px-3 flex items-center gap-2 text-xs rounded-sm hover:bg-muted/50 transition-colors
                ${currentTab === 'code' ? 'bg-muted text-foreground' : 'text-muted-foreground'}`}
                    >
                        <Code className="h-3.5 w-3.5"/>
                        Code
                    </button>

                            <button
                                onClick={() => onTabChange('supabase')}
                                className={`h-8 px-3 flex items-center gap-2 text-xs rounded-sm hover:bg-muted/50 transition-colors
                ${currentTab === 'supabase' ? 'bg-muted text-foreground' : 'text-muted-foreground'}`}
                            >
                                <Database className="h-3.5 w-3.5"/>
                                Backend
                            </button>

                </div>

                <div className="flex items-center justify-center gap-x-2">
                    <ConfirmationDialog title={"Reload the app"}
                                        description={"Any connected active devices will be disconnected and may have to be connected again."}
                                        onConfirm={refresh}
                                        triggerClassName="bg-transparent hover:bg-transparent"
                    >
                        <Button variant="outline" size="xs">
                            Preview not loading? Try refreshing
                            <RefreshCcwIcon className="h-4 w-4"/>
                        </Button>
                    </ConfirmationDialog>

                    <ViewInMobileButton chatId={chatId}/>

                    <Button
                        variant="secondary"
                        size="icon"
                        className="bg-black/70 text-white hover:bg-black/80"
                        onClick={toggleFullscreen}
                    >
                        <Maximize2 className="h-4 w-4"/>
                    </Button>
                </div>
                {/*<button*/}
                {/*    onClick={() => onTabChange('terminal')}*/}
                {/*    className={`h-8 px-3 flex items-center gap-2 text-xs rounded-sm hover:bg-muted/50 transition-colors*/}
                {/*${currentTab === 'terminal' ? 'bg-muted text-foreground' : 'text-muted-foreground'}`}*/}
                {/*>*/}
                {/*    <TerminalIcon className="h-3.5 w-3.5"/>*/}
                {/*    Terminal*/}
                {/*</button>*/}
            </div>

    )
        ;
});

const PreviewActions = React.memo(({ chatId, projectId }: { chatId: string, projectId: string }) => {
    // Import the TestFlightButton component
    const TestFlightButton = React.lazy(() => import('@/components/testflight/TestFlightButton').then(mod => ({ default: mod.TestFlightButton })));
    // Import the DeployButton component
    const DeployButton = React.lazy(() => import('@/components/deployment/DeployButton').then(mod => ({ default: mod.DeployButton })));
    
    return (
        <div className="flex items-center gap-1">
            <React.Suspense fallback={null}>
                <TestFlightButton chatId={chatId} />
            </React.Suspense>
            <React.Suspense fallback={null}>
                <DeployButton projectId={projectId} />
            </React.Suspense>
        </div>
    );
});

const PreviewPanelBase = observer(({
                                       chatId,
                                       projectId,
                                       onOperationChange,
                                       className = ""
                                   }: PreviewPanelProps) => {
    const [screenshot, setScreenshot] = useState<string | null>(null);
    const {generatorStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);
    const isMobile = useIsMobile();

    console.log('[isMobile] PreviewPanelBase:', isMobile);

    const handleFileOperation = useCallback((operation: any) => {
        console.log('handleFileOperation called');
        
        // Track file operation event
        trackFeatureEvent('CODE_EDITED', {
            feature_name: 'editor_file_operation',
            is_enabled: true,
            user_type: 'free', // This should be dynamically determined
            file_path: operation.absolutePath,
            trigger_source: operation.type || 'unknown',
            project_id: session?.projectId || ''
        });
        
        const codeBlock: CodeBlock = {
            type: operation.type === 'delete' ? null : operation.type,
            absolutePath: operation.absolutePath,
            content: operation.content || '',
        };
        onOperationChange(codeBlock);
    }, [session]);  // Add session to deps to access projectId

    const handleSnackReady = useCallback((playerUrl: string) => {
        console.log('handleSnackReady called with:', playerUrl);
        session?.setPreviewUrl(playerUrl);
    }, [session]);

    // Use computed value for current visibility states
    const visibilityStates = computed(() => ({
        previewVisible: session?.tabSelection === 'preview',
        codeVisible: session?.tabSelection === 'code',
        terminalVisible: session?.tabSelection === 'terminal',
        supabaseVisible: session?.tabSelection === 'supabase'
    })).get();

    const handleTabChange = useCallback((tab: string) => {
        if (!session || tab === session.tabSelection) return; // Skip if no change
        console.log('handleTabChange called with:', tab);
        
        // Track tab change event
        trackFeatureEvent('FILE_TREE_NAVIGATED', {
            feature_name: 'preview_panel_tabs',
            is_enabled: true,
            user_type: 'free', // This should be dynamically determined in a real implementation
            trigger_source: `tab_${tab}`
        });
        
        session.setCurrentTab(tab as any);
    }, [session]); // Keep session in deps since we're using observer

    if (!session) return null;

    return (
        <div className={`w-full h-full flex flex-col ${className}`}>
            <div className="h-9 bg-background border-b border-border flex justify-between items-center px-2 gap-1">
                <div className="flex justify-between items-center w-full">
                    <TabHeader currentTab={session.tabSelection} onTabChange={handleTabChange} chatId={chatId} projectId={projectId}/>
                    {/*<PreviewActions chatId={chatId} />*/}
                </div>
            </div>

            <div className="flex-1 relative w-full h-full flex flex-col">
                <div className="flex-1 relative w-full">
                    <VisibilityWrapper isVisible={visibilityStates.previewVisible}>
                        <Preview chatId={chatId} projectId={projectId} isMobile={isMobile} onSnackReady={handleSnackReady}/>
                    </VisibilityWrapper>
                    <VisibilityWrapper isVisible={visibilityStates.codeVisible}>
                        <Editor chatId={chatId} onFileOperation={handleFileOperation}/>
                    </VisibilityWrapper>
                    <VisibilityWrapper isVisible={session?.tabSelection === 'supabase'}>
                        <SupabasePanel chatId={chatId} projectId={projectId} />
                    </VisibilityWrapper>
                    {/*<VisibilityWrapper isVisible={visibilityStates.terminalVisible}>*/}
                    {/*    <div className="h-full w-full p-2">*/}
                    {/*        <Terminal*/}
                    {/*            sessionId={chatId}*/}
                    {/*            initialCommand="pwd && ls -la"*/}
                    {/*        />*/}
                    {/*    </div>*/}
                    {/*</VisibilityWrapper>*/}
                </div>
                {/* DevTools is always visible and contains logs and dependencies */}
                <DevTools chatId={chatId} className="mt-1 hidden md:inline-flex" />
            </div>
        </div>
    )
});

// Wrap in memo to prevent unnecessary re-renders from parent
// Outer component that prevents unnecessary re-renders
// Wrap the observer component with memo for prop comparison
export const PreviewPanel = React.memo(PreviewPanelBase, (prev, next) => {
    // console.log('PreviewPanel memo compare:', {
    //     prevProps: { chatId: prev.chatId },
    //     nextProps: { chatId: next.chatId },
    //     equal: prev.chatId === next.chatId
    // });
    return prev.chatId === next.chatId && prev.projectId === next.projectId;
});
