'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Slider } from '@/components/ui/slider';
import { CheckCircle2, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface ExitInterviewProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  subscriptionId: string;
  onCancellationComplete?: () => void;
}

// This interface must match the API's CancellationFeedback interface
interface CancellationFeedback {
  signupGoal: string;
  stopReason: string;
  recommendScore: number;
  recommendReason?: string;
}

export function ExitInterview({
  open,
  onOpenChange,
  subscriptionId,
  onCancellationComplete
}: ExitInterviewProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [feedback, setFeedback] = useState<CancellationFeedback>({
    signupGoal: '',
    stopReason: '',
    recommendScore: 5,
    recommendReason: ''
  });
  
  const [wantsFeedbackCall, setWantsFeedbackCall] = useState(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const steps = [
    {
      title: "What was your main goal when you signed up?",
      key: "signupGoal",
      options: [
        { value: "build_mvp", label: "Build an MVP to test my idea" },
        { value: "create_production_app", label: "Create a production app for my business" },
        { value: "learn_no_code", label: "Learn if no-code could work for me" },
        { value: "replace_current_process", label: "Replace my current app development process" },
        { value: "other", label: "Other" }
      ]
    },
    {
      title: "What stopped you from achieving that goal?",
      key: "stopReason",
      options: [
        { value: "too_complex", label: "App building process was too complex" },
        { value: "didnt_work", label: "Generated app didn't work as expected" },
        { value: "missing_features", label: "Missing features I needed" },
        { value: "too_expensive", label: "Too expensive for the value" },
        { value: "better_alternative", label: "Found a better alternative" },
        { value: "other", label: "Other" }
      ]
    },
    {
      title: "How likely are you to recommend Magically?",
      key: "recommendScore",
      subKey: "recommendReason",
      placeholder: "What would make you more likely to recommend us?"
    },
    {
      title: "Would you like to schedule a feedback call?",
      key: "feedbackCall",
      description: "Schedule a quick call to provide detailed feedback and get a refund (up to $15 for Magically Starter) after the call. This helps us improve the overall experience."
    }
  ];

  const isCurrentStepValid = useCallback(() => {
    const currentStepData = steps[currentStep];
    if (currentStepData.key === 'signupGoal' || currentStepData.key === 'stopReason') {
      return !!feedback[currentStepData.key as keyof CancellationFeedback];
    }
    return true;
  }, [currentStep, feedback, steps]);

  const handleNext = useCallback(() => {
    if (!isCurrentStepValid()) {
      // Optionally, add user feedback here (e.g., toast, shake animation)
      return;
    }
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, steps, isCurrentStepValid, setCurrentStep]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep, setCurrentStep]);

  // Effect for cleaning up the timeout if the component unmounts
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handleSubmit = useCallback(async () => {
    if (isSubmitting) {
      return; // Prevent multiple submissions if already processing
    }
    setIsSubmitting(true); // Set loading state immediately for UI feedback

    // Clear any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set a new debounce timeout
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId,
          feedback: {
            ...feedback,
            // Include feedback call preference in the metadata
            requestedFeedbackCall: wantsFeedbackCall
          }
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to cancel subscription');
      }

      const result = await response.json();

      if (onCancellationComplete) {
        onCancellationComplete();
      }

      toast.success('Subscription cancelled successfully.');

      onOpenChange(false); // Close the dialog
      router.refresh();    // Refresh page data

      // Reset internal component state
      setCurrentStep(0);
      setFeedback({ signupGoal: '', stopReason: '', recommendScore: 5, recommendReason: '' });
      setWantsFeedbackCall(false);

      } catch (error) {
        console.error('Error during API call:', error);
        // Ensure alert is shown for API call errors within the timeout
        toast.error('An unexpected error occurred while cancelling. Please try again.');
      } finally {
        setIsSubmitting(false); // Reset loading state after API call attempt
      }
    }, 1000); // 1-second debounce period
  }, [isSubmitting, subscriptionId, feedback, wantsFeedbackCall, onOpenChange, onCancellationComplete, router, setCurrentStep, setFeedback, setWantsFeedbackCall]);

  // Success state
  if (currentStep === steps.length) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[400px] p-0 overflow-hidden">
          <div className="flex flex-col items-center justify-center text-center p-8 space-y-4">
            <div className="rounded-full bg-green-50 p-3">
              <CheckCircle2 className="h-8 w-8 text-green-500" />
            </div>
            <DialogTitle className="text-xl font-medium">Subscription Cancelled</DialogTitle>
            <DialogDescription>
              You'll have access until the end of your billing period.
            </DialogDescription>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Error state
  if (currentStep === steps.length + 1) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[400px] p-0 overflow-hidden">
          <div className="flex flex-col items-center justify-center text-center p-8 space-y-4">
            <div className="rounded-full bg-red-50 p-3">
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
            <DialogTitle className="text-xl font-medium">Something went wrong</DialogTitle>
            <DialogDescription>
              We couldn't cancel your subscription. Please try again.
            </DialogDescription>
            <Button 
              variant="outline" 
              onClick={() => setCurrentStep(0)}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const step = steps[currentStep];
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[450px] p-0 overflow-hidden">
        <DialogHeader className="p-6 pb-3 border-b">
          <DialogTitle className="text-lg font-medium">Cancel Subscription</DialogTitle>
          <DialogDescription className="text-sm">
            We're sorry to see you go. Your feedback helps us improve.
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-6">
          <h3 className="text-base font-medium mb-4">{step.title}</h3>
          
          {(step.key === 'signupGoal' || step.key === 'stopReason') && (
            <RadioGroup 
              value={feedback[step.key]}
              onValueChange={(value) => setFeedback({...feedback, [step.key]: value})}
              className="space-y-2.5"
            >
              {step.options?.map((option) => (
                <div 
                  key={option.value} 
                  className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors"
                >
                  <RadioGroupItem value={option.value} id={`${step.key}-${option.value}`} />
                  <Label htmlFor={`${step.key}-${option.value}`} className="flex-grow cursor-pointer">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}
          
          {step.key === 'recommendScore' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Not likely</span>
                  <span>Very likely</span>
                </div>
                <div className="px-1">
                  <Slider
                    value={[feedback.recommendScore]}
                    min={0}
                    max={10}
                    step={1}
                    onValueChange={(value) => setFeedback({...feedback, recommendScore: value[0]})}
                    className="my-4"
                  />
                </div>
                <div className="flex justify-between px-1">
                  {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                    <span 
                      key={num} 
                      className={`w-6 h-6 flex items-center justify-center text-xs rounded-full 
                        ${feedback.recommendScore === num ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'}`}
                    >
                      {num}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="pt-4">
                <Label htmlFor="recommend-reason" className="text-sm font-medium mb-2 block">
                  Why did you give this score?
                </Label>
                <Textarea
                  id="recommend-reason"
                  placeholder={step.placeholder}
                  value={feedback.recommendReason || ''}
                  onChange={(e) => setFeedback({...feedback, recommendReason: e.target.value})}
                  className="resize-none h-24"
                />
              </div>
            </div>
          )}
          
          {step.key === 'feedbackCall' && (
            <div className="space-y-6">
              <p className="text-sm text-muted-foreground">{step.description}</p>
              
              <div className="space-y-4">
                <RadioGroup 
                  value={wantsFeedbackCall ? 'yes' : 'no'}
                  onValueChange={(value) => setWantsFeedbackCall(value === 'yes')}
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors">
                    <RadioGroupItem value="yes" id="feedback-call-yes" />
                    <Label htmlFor="feedback-call-yes" className="flex-grow cursor-pointer">
                      Yes, I'd like to schedule a call and get a refund
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/20 transition-colors">
                    <RadioGroupItem value="no" id="feedback-call-no" />
                    <Label htmlFor="feedback-call-no" className="flex-grow cursor-pointer">
                      No, I'll skip the call
                    </Label>
                  </div>
                </RadioGroup>
                
                {wantsFeedbackCall && (
                  <div className="mt-4 p-4 bg-muted/20 rounded-md">
                    <h4 className="font-medium mb-2">Schedule a Feedback Call</h4>
                    <p className="text-sm mb-3">After clicking the link below, you'll be able to choose a time that works for you.</p>
                    <a 
                      href="https://calendly.com/magically-feedback/exit-interview"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
                    >
                      Schedule Call & Get Refund
                    </a>
                    <p className="text-xs text-muted-foreground mt-2">Your refund (up to $15 for Magically Starter) will be processed after the call.</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter className="flex justify-between p-6 border-t bg-muted/10">
          <Button 
            variant="ghost" 
            onClick={currentStep === 0 ? () => onOpenChange(false) : handlePrevious}
            disabled={isSubmitting}
          >
            {currentStep === 0 ? 'Cancel' : 'Back'}
          </Button>
          
          <Button 
            variant={currentStep === steps.length - 1 ? "destructive" : "default"}
            onClick={currentStep === steps.length - 1 ? handleSubmit : handleNext}
            disabled={isSubmitting || (currentStep === steps.length - 1 ? !isCurrentStepValid() : false)}
          >
            {currentStep === steps.length - 1 ? 
              (isSubmitting ? "Processing..." : "Confirm Cancellation") : 
              "Continue"
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
