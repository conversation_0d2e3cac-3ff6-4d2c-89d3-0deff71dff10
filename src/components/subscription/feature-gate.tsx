'use client';

import React, { <PERSON>actNode, MouseEvent, ReactElement } from 'react';
import useS<PERSON> from 'swr';
import { FeatureId, isFeatureAllowed } from '@/lib/subscription/plans';
import { Button } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';
import { fetcher } from '@/lib/utils';
import { useStores } from '@/stores/utils/useStores';
import { toast } from 'sonner';

interface StandardFeatureGateProps {
  /**
   * The feature ID to check access for
   */
  featureId: FeatureId;
  /**
   * The content to render if the user has access to the feature
   */
  children: ReactNode;
  /**
   * Optional custom upgrade message
   */
  upgradeMessage?: string;
  /**
   * Optional fallback content to show instead of the default upgrade prompt
   */
  fallback?: ReactNode;
  /**
   * Must be false or undefined for standard mode
   */
  wrapHandler?: false;
  /**
   * Optional callback to execute when a user tries to access a gated feature
   */
  onFeatureAttempt?: () => void;
}

interface HandlerFeatureGateProps {
  /**
   * The feature ID to check access for
   */
  featureId: FeatureId;
  /**
   * Function that receives a wrapped handler function
   * The wrapped handler will check access before executing the original handler
   */
  children: (params: { 
    handleClick: (originalHandler: (e: MouseEvent) => void) => (e: MouseEvent) => void 
  }) => ReactElement;
  /**
   * Optional custom upgrade message
   */
  upgradeMessage?: string;
  /**
   * Must be true for handler mode
   */
  wrapHandler: true;
  /**
   * Optional callback to execute when a user tries to access a gated feature
   */
  onFeatureAttempt?: () => void;
  /**
   * Not used in handler mode
   */
  fallback?: never;
}

type FeatureGateProps = StandardFeatureGateProps | HandlerFeatureGateProps;

export function FeatureGate(props: FeatureGateProps) {
  const { featureId, upgradeMessage = 'Upgrade your plan to access this feature', onFeatureAttempt } = props;
  const { generatorStore } = useStores();
  
  // Use SWR to fetch subscription status
  const { data: status, error, isLoading } = useSWR(
    '/api/subscription/status',
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // Cache for 1 minute
    }
  );

  // Show loading state
  if (isLoading) {
    return <div className="p-2 text-sm text-muted-foreground">Loading...</div>;
  }

  // Show error state
  if (error) {
    return <div className="p-2 text-sm text-red-500">Unable to verify access</div>;
  }

  // Check if the feature is allowed for the user's plan
  const hasAccess = status && isFeatureAllowed(status.planTier, featureId);

  // Handle the case where we're wrapping a handler function
  if (props.wrapHandler === true) {
    // Create a function that wraps the original handler
    const handleClick = (originalHandler: (e: MouseEvent) => void) => (e: MouseEvent) => {
      if (hasAccess) {
        // If user has access, call the original handler
        originalHandler(e);
      } else {
        // Prevent default behavior
        e.preventDefault();
        e.stopPropagation();
        
        // Call the onFeatureAttempt callback if provided
        if (onFeatureAttempt) {
          onFeatureAttempt();
        }

        // Show appropriate dialog based on user status
        const isAnonymous = status?.isAnonymous;
        
        if (isAnonymous) {
          // For anonymous users, show login dialog
          toast.info('Please sign in to access this feature');
          generatorStore.toggleLoginDialog(true);
        } else {
          // For authenticated users, show upgrade dialog
          toast.info(upgradeMessage);
          generatorStore.toggleUpgradeDialog(true);
        }
      }
    };
    
    // Call the children function with the wrapped handler
    return props.children({ handleClick });
  }
  
  // Standard mode: If user has access, render the children
  if (hasAccess) {
    return <>{props.children}</>;
  }

  // If a custom fallback is provided, render it
  if ('fallback' in props && props.fallback) {
    return <>{props.fallback}</>;
  }

  // Otherwise, render the appropriate prompt based on user status
  const isAnonymous = status?.isAnonymous;
  
  return (
    <div className="flex flex-col items-center justify-center p-4 border border-dashed border-muted-foreground/30 rounded-md bg-muted/20 text-center space-y-2">
      <Sparkles className="h-5 w-5 text-primary/70" />
      <p className="text-sm text-muted-foreground">
        {isAnonymous ? 'Please sign in to access this feature' : upgradeMessage}
      </p>
      <Button 
        size="sm" 
        variant="outline" 
        className="mt-2"
        onClick={() => isAnonymous ? 
          generatorStore.toggleLoginDialog(true) : 
          generatorStore.toggleUpgradeDialog(true)
        }
      >
        {isAnonymous ? 'Sign In' : 'Upgrade Now'}
      </Button>
    </div>
  );
}
