'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import useSWR from 'swr';
import {
  Sparkles, Zap, CreditCard, ChevronRight,
  BarChart3, Building2, Rocket, CalendarClock,
  CreditCard as CreditCardIcon, AlertCircle, CheckCircle
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { fetcher } from '@/lib/utils';
import { PlanTier, PLANS } from '@/lib/subscription/plans';
import { ExitInterview } from './exit-interview';
import { trackSubscriptionEvent } from '@/lib/analytics/track';

interface SubscriptionStatus {
  isActive: boolean;
  planTier: PlanTier;
  planName: string;
  expiresAt?: string | null;
  credits: {
    total: number;
    used: number;
    remaining: number;
  };
  dailyLimit?: number;
  dailyRemaining?: number;
  subscriptionId?: string;
  status?: 'active' | 'cancelled' | 'expired' | 'past_due' | 'inactive';
  currentPeriodEnd?: string;
  cancelAt?: string;
  isPro?: boolean;
}

export function SubscriptionManagement() {
  const router = useRouter();
  const [showExitInterview, setShowExitInterview] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);

  const { data: status, error, isLoading, mutate } = useSWR<SubscriptionStatus>(
    '/api/subscription/status',
    fetcher,
    {
      refreshInterval: 60000, // Refresh every minute
      revalidateOnFocus: true,
    }
  );

  // Format large numbers with k suffix
  const formatNumber = (num: number) => {
    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();
  };

  const getPlanIcon = (plan: PlanTier) => {
    switch (plan) {
      case 'prime':
        return <Sparkles className="h-5 w-5 text-yellow-400" />;
      case 'plus':
        return <Sparkles className="h-5 w-5 text-yellow-400" />;
      case 'pro':
        return <Zap className="h-5 w-5 text-purple-400" />;
      case 'starter':
        return <Rocket className="h-5 w-5 text-blue-400" />;
      case 'free':
        return <CreditCardIcon className="h-5 w-5 text-gray-400" />;
      default:
        return <CreditCardIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getPlanColor = (plan: PlanTier) => {
    switch (plan) {
      case 'prime':
        return 'bg-yellow-400';
      case 'plus':
        return 'bg-yellow-400';
      case 'pro':
        return 'bg-purple-400';
      case 'starter':
        return 'bg-blue-400';
      default:
        return 'bg-gray-400';
    }
  };

  const handleViewPlans = () => {
    router.push('/pricing');
  };

  const handleUpgrade = async (planId: string) => {
    try {
      setIsUpgrading(true);

      // Get plan details for tracking
      const planDetails = PLANS.find(plan => plan.id === planId);

      // Track upgrade initiation
      trackSubscriptionEvent('UPGRADE_INITIATED', {
        current_plan: status?.planTier || 'unknown',
        plan_type: planDetails?.tier || planId,
        price: planDetails?.price || 0,
        currency: 'USD',
        entry_point: 'subscription_management'
      });

      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          returnUrl: '/subscription',
          planId
        })
      });

      const data = await response.json();

      if (data.error) {
        // Track upgrade failure
        trackSubscriptionEvent('UPGRADE_FAILED', {
          current_plan: status?.planTier || 'unknown',
          plan_type: planDetails?.tier || planId,
          // Using any to bypass type checking for custom properties
          ...(data.error ? { error_message: data.error } : {})
        });

        throw new Error(data.error);
      }

      // Track upgrade initiated (instead of checkout initiated which isn't in the allowed event types)
      trackSubscriptionEvent('UPGRADE_INITIATED', {
        current_plan: status?.planTier || 'unknown',
        plan_type: planDetails?.tier || planId,
        entry_point: 'subscription_management'
      });

      // Redirect to checkout URL
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error upgrading plan:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  // Format date to readable format
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="animate-pulse flex flex-col items-center gap-4">
          <div className="h-8 w-40 bg-muted rounded"></div>
          <div className="h-40 w-full max-w-md bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !status) {
    return (
      <Alert variant="destructive" className="max-w-3xl mx-auto my-8">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Unable to load subscription information. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    );
  }

  const usagePercentage = Math.min(100, Math.round((status.credits.used / status.credits.total) * 100));
  const planDetails = PLANS.find(plan => plan.tier === status.planTier);

  // Get color based on usage percentage
  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-amber-500';
    return getPlanColor(status.planTier);
  };

  return (
    <div className="space-y-8 max-w-4xl mx-auto">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Subscription Management</h1>
        <p className="text-muted-foreground">Manage your subscription and usage details</p>
      </div>

      {/* Current Plan Card */}
      <Card className="overflow-hidden">
        <CardHeader className="bg-muted/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getPlanIcon(status.planTier)}
              <CardTitle className="capitalize">{status.planName} Plan</CardTitle>
            </div>
            <Badge
              variant={status.status === 'active' ? 'default' :
                      status.status === 'cancelled' ? 'destructive' :
                      status.status === 'past_due' ? 'destructive' : 'outline'}
            >
              {status.status === 'active' ? 'Active' :
               status.status === 'cancelled' ? 'Cancelled' :
               status.status === 'past_due' ? 'Past Due' : 'Inactive'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-6 space-y-6">
          {/* Subscription Status Information */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Subscription Details</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Plan:</div>
                  <div className="font-medium capitalize">{status.planName}</div>

                  <div>Status:</div>
                  <div className="font-medium capitalize">{status.status || 'Unknown'}</div>

                  <div>Price:</div>
                  <div className="font-medium">${planDetails?.price || 0}/month</div>

                  {status.currentPeriodEnd && (
                    <>
                      <div>Current Period Ends:</div>
                      <div className="font-medium">{formatDate(status.currentPeriodEnd)}</div>
                    </>
                  )}

                  {status.status === 'cancelled' && status.cancelAt && (
                    <>
                      <div>Access Until:</div>
                      <div className="font-medium">{formatDate(status.cancelAt)}</div>
                    </>
                  )}
                </div>
              </div>

              {status.status === 'cancelled' && (
                <Alert>
                  <CalendarClock className="h-4 w-4" />
                  <AlertTitle>Subscription Cancelled</AlertTitle>
                  <AlertDescription>
                    Your subscription has been cancelled but you still have access until {formatDate(status.cancelAt || status.currentPeriodEnd)}.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Usage Information */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Usage</h3>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm">Messages</span>
                  <div>
                    <span className="text-sm font-medium">{formatNumber(status.credits.remaining)}</span>
                    <span className="text-xs text-muted-foreground"> / {formatNumber(status.credits.total)}</span>
                  </div>
                </div>
                <Progress value={usagePercentage} className="h-2">
                  <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />
                </Progress>
                {usagePercentage >= 90 && (
                  <p className="text-xs text-red-500 mt-1">
                    Very low on messages. Consider upgrading your plan.
                  </p>
                )}
                {usagePercentage >= 75 && usagePercentage < 90 && (
                  <p className="text-xs text-amber-500 mt-1">
                    Running low on messages.
                  </p>
                )}
              </div>

              {status.dailyLimit && status.dailyLimit > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm">Daily Limit</span>
                    <div>
                      <span className="text-sm font-medium">{status.dailyRemaining}</span>
                      <span className="text-xs text-muted-foreground"> / {status.dailyLimit}</span>
                    </div>
                  </div>
                  <Progress
                    value={Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}
                    className="h-2"
                  >
                    <div
                      className={`h-full ${getPlanColor(status.planTier)}`}
                      style={{ width: `${Math.min(100, Math.round(((status.dailyLimit - (status.dailyRemaining || 0)) / status.dailyLimit) * 100))}%` }}
                    />
                  </Progress>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Plan Features */}
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-2">Plan Features</h3>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {planDetails?.features.map((feature, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between items-center bg-muted/30 border-t">
          <div className="flex flex-col sm:flex-row gap-3 w-full justify-end">
            {status.status === 'active' && (
              <>
                {/* Upgrade button with better styling */}
                <Button 
                  variant="default" 
                  size="sm"
                  className="sm:w-auto px-6 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
                  onClick={handleViewPlans}
                >
                  <Zap className="mr-2 h-4 w-4" />
                  Upgrade Plan
                </Button>
                
                {/* Cancel subscription button with subtle styling */}
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="sm:w-auto px-6 text-gray-500 hover:text-gray-700 hover:bg-gray-100/50"
                  onClick={() => setShowExitInterview(true)}
                  disabled={!status.subscriptionId}
                >
                  Cancel Plan
                </Button>
              </>
            )}
            
            {status.status === 'cancelled' && (
              <Button 
                variant="default" 
                size="sm"
                className="sm:w-auto px-6 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                onClick={handleViewPlans}
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Resubscribe
              </Button>
            )}
            
            {(status.status === 'expired' || status.status === 'past_due') && (
              <Button 
                variant="default" 
                size="sm"
                className="sm:w-auto px-6 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white"
                onClick={handleViewPlans}
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Renew Subscription
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>

      {/* Exit Interview Modal with improved integration */}
      {status.subscriptionId && (
        <ExitInterview
          open={showExitInterview}
          onOpenChange={(open) => {
            setShowExitInterview(open);
            if (!open) {
              // Refresh data when modal is closed
              setTimeout(() => mutate(), 500);
            }
          }}
          subscriptionId={status.subscriptionId}
          onCancellationComplete={() => {
            // Refresh data after cancellation
            mutate();
          }}
        />
      )}
    </div>
  );
}
