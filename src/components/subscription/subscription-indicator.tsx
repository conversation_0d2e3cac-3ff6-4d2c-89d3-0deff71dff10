'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import useSWR from 'swr';
import { Sparkles, Zap, CreditCard, ChevronRight, BarChart3, ChevronDown } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { fetcher } from '@/lib/utils';
import { PlanTier } from '@/lib/subscription/plans';

interface SubscriptionStatus {
  isActive: boolean;
  isPro: boolean;
  planTier: PlanTier;
  planName: string;
  expiresAt?: string | null;
  credits: {
    total: number;
    used: number;
    remaining: number;
  };
  dailyLimit?: number;
  dailyRemaining?: number;
}

export function SubscriptionIndicator({ className, variant = 'default' }: { 
  className?: string;
  variant?: 'default' | 'compact' | 'sidebar';
}) {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  
  const { data: status, error, isLoading } = useSWR<SubscriptionStatus>(
    '/api/subscription/status',
    fetcher,
    {
      refreshInterval: 60000, // Refresh every minute
      revalidateOnFocus: true,
    }
  );
  
  // Format large numbers with k suffix
  const formatNumber = (num: number) => {
    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();
  };

  const getPlanIcon = (plan: PlanTier) => {
    switch (plan) {
      case 'prime':
      return <Sparkles className="h-4 w-4 text-yellow-400" />;
      case 'plus':
        return <Sparkles className="h-4 w-4 text-yellow-400" />;
      case 'pro':
        return <Zap className="h-4 w-4 text-purple-400" />;
      case 'starter':
        return <CreditCard className="h-4 w-4 text-blue-400" />;
      default:
        return <CreditCard className="h-4 w-4 text-gray-400" />;
    }
  };

  const getPlanColor = (plan: PlanTier) => {
    switch (plan) {
      case 'prime':
        return 'bg-yellow-400';
      case 'plus':
        return 'bg-yellow-400';
      case 'pro':
        return 'bg-purple-400';
      case 'starter':
        return 'bg-blue-400';
      default:
        return 'bg-gray-400';
    }
  };

  const handleViewSubscription = () => {
    if(status?.isPro) {
      router.push('/subscription');
    } else {
      router.push('/pricing');
    }
    setOpen(false);
  };

  if (isLoading) {
    return (
      <Button variant="ghost" size="sm" className={cn("h-8 gap-1", className)} disabled>
        <BarChart3 className="h-4 w-4" />
        <span className="text-xs">Loading...</span>
      </Button>
    );
  }

  if (error || !status) {
    return (
      <Button variant="ghost" size="sm" className={cn("h-8 gap-1", className)} onClick={handleViewSubscription}>
        <CreditCard className="h-4 w-4" />
        <span className="text-xs">Subscription</span>
      </Button>
    );
  }

  const usagePercentage = Math.min(100, Math.round((status.credits.used / status.credits.total) * 100));
  
  // Get color based on usage percentage
  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 80) return 'bg-amber-500';
    return getPlanColor(status.planTier);
  };
  
  // Compact version (just icon and credits remaining)
  if (variant === 'compact') {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className={cn("h-7 px-2 gap-1 rounded-full border border-border/30", className)}
            onClick={handleViewSubscription}
          >
            {getPlanIcon(status.planTier)}
            <span className="text-xs font-medium">{formatNumber(status.credits.remaining)}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent side="right" className="p-2 text-xs">
          <div className="font-medium">{formatNumber(status.credits.remaining)} / {formatNumber(status.credits.total)} credits</div>
          <div className="text-muted-foreground capitalize">{status.planName} Plan</div>
          {status.dailyLimit && status.dailyLimit > 0 && (
            <div className="text-muted-foreground mt-1">
              Daily: {status.dailyRemaining} / {status.dailyLimit}
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    );
  }

  // Sidebar version (vertical layout)
  if (variant === 'sidebar') {
    return (
      <div 
        className={cn("flex flex-col gap-0.5 px-2 py-1 cursor-pointer hover:bg-muted/30 rounded transition-colors", className)}
        onClick={handleViewSubscription}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <div className="w-3.5 h-3.5">{getPlanIcon(status.planTier)}</div>
            <span className="text-[10px] font-medium capitalize">{status.planName}</span>
          </div>
          <ChevronRight className="h-2.5 w-2.5 opacity-60" />
        </div>
        <div className="space-y-0.5">
          <div className="flex items-center justify-between text-[9px] text-muted-foreground">
            <span>Messages</span>
            <span>{formatNumber(status.credits.remaining)} / {formatNumber(status.credits.total)}</span>
          </div>
          <Progress value={usagePercentage} className="h-0.5">
            <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />
          </Progress>
          
          {status.dailyLimit && status.dailyLimit > 0 && (
            <div className="flex items-center justify-between text-[9px] text-muted-foreground mt-0.5">
              <span>Daily</span>
              <span>{status.dailyRemaining} / {status.dailyLimit}</span>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Default version (popover with details)
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className={cn("h-7 px-2 gap-1.5 rounded-md border-border/40", className)}
        >
          {getPlanIcon(status.planTier)}
          <span className="text-xs font-medium">{formatNumber(status.credits.remaining)}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3" align="end">
        <div 
          className="space-y-2.5 cursor-pointer" 
          onClick={() => {
            setOpen(false);
            handleViewSubscription();
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1.5">
              {getPlanIcon(status.planTier)}
              <span className="text-sm font-medium capitalize">{status.planName}</span>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-6 px-2 text-xs" 
              onClick={(e) => {
                e.stopPropagation();
                setOpen(false);
                handleViewSubscription();
              }}
            >
              Manage
            </Button>
          </div>
          
          <div className="space-y-0.5">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Message</span>
              <span className="font-medium">{formatNumber(status.credits.remaining)} / {formatNumber(status.credits.total)}</span>
            </div>
            <Progress value={usagePercentage} className="h-1.5">
              <div className={`h-full ${getUsageColor(usagePercentage)}`} style={{ width: `${usagePercentage}%` }} />
            </Progress>
            {usagePercentage >= 90 && (
              <p className="text-[10px] text-red-500 pt-0.5">
                Very low on messages
              </p>
            )}
            {usagePercentage >= 80 && usagePercentage < 90 && (
              <p className="text-[10px] text-amber-500 pt-0.5">
                Running low on messages
              </p>
            )}
            
            {status.dailyLimit && status.dailyLimit > 0 && (
              <div className="flex items-center justify-between text-xs mt-2">
                <span className="text-muted-foreground">Daily Limit</span>
                <span className="font-medium">{status.dailyRemaining} / {status.dailyLimit}</span>
              </div>
            )}
          </div>
          
          {status.planTier !== 'plus' && (
            <Button 
              className="w-full text-xs h-7 mt-1" 
              onClick={(e) => {
                e.stopPropagation();
                setOpen(false);
                handleViewSubscription();
              }}
            >
              Upgrade Plan
            </Button>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
