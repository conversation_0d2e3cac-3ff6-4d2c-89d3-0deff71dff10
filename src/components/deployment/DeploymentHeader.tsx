'use client';

import React from 'react';
import { DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DeploymentPlatform } from '@/stores/DeploymentStore';
import { Smartphone, Globe, Tablet, Rocket, Server, Cloud } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';

interface DeploymentHeaderProps {
  activeTab: DeploymentPlatform;
  onTabChange: (tab: DeploymentPlatform) => void;
}

export function DeploymentHeader({ activeTab, onTabChange }: DeploymentHeaderProps) {
  // Get platform status badges
  const getPlatformBadge = (platform: DeploymentPlatform) => {
    if (platform === activeTab) {
      return null; // Don't show badge on active tab
    }
    
    // This would normally come from the platformFeatures prop
    // For now, we'll just mark iOS as coming soon
    if (platform === 'ios') {
      return (
        <Badge 
          variant="outline" 
          className="ml-1 text-[9px] px-1 py-0 h-3.5 bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/30 dark:text-amber-400 dark:border-amber-800"
        >
          Soon
        </Badge>
      );
    }
    
    return null;
  };
  
  // Get platform icon with appropriate color
  const getPlatformIcon = (platform: DeploymentPlatform) => {
    switch(platform) {
      case 'web':
        return <Cloud className={cn("h-3.5 w-3.5", activeTab === 'web' ? "text-blue-500" : "")} />;
      case 'android':
        return <Smartphone className={cn("h-3.5 w-3.5", activeTab === 'android' ? "text-green-500" : "")} />;
      case 'ios':
        return <Tablet className={cn("h-3.5 w-3.5", activeTab === 'ios' ? "text-orange-500" : "")} />;
      default:
        return <Globe className="h-3.5 w-3.5" />;
    }
  };
  
  return (
    <div className="border-b">
      <DialogHeader className="px-6 pt-6 pb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-full bg-primary/10">
            <Rocket className="h-5 w-5 text-primary" />
          </div>
          <div>
            <DialogTitle className="text-xl font-semibold tracking-tight">Deploy Project</DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground mt-1">
              Deploy your project to make it accessible to users and stakeholders
            </DialogDescription>
          </div>
        </div>
      </DialogHeader>
      
      <Tabs value={activeTab} onValueChange={(value) => onTabChange(value as DeploymentPlatform)} className="px-6 pb-0">
        <TabsList className="h-10 w-full justify-start gap-2 bg-transparent p-0">
          <TabsTrigger 
            value="web" 
            className={cn(
              "text-xs flex items-center gap-1.5 h-9 border-b-2 rounded-none px-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none",
              activeTab === 'web' ? "border-primary" : "border-transparent"
            )}
          >
            {getPlatformIcon('web')}
            <span>Web</span>
            {getPlatformBadge('web')}
          </TabsTrigger>
          
          <TabsTrigger 
            value="android" 
            className={cn(
              "text-xs flex items-center gap-1.5 h-9 border-b-2 rounded-none px-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none",
              activeTab === 'android' ? "border-primary" : "border-transparent"
            )}
          >
            {getPlatformIcon('android')}
            <span>Android</span>
            {getPlatformBadge('android')}
          </TabsTrigger>
          
          <TabsTrigger 
            value="ios" 
            className={cn(
              "text-xs flex items-center gap-1.5 h-9 border-b-2 rounded-none px-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none",
              activeTab === 'ios' ? "border-primary" : "border-transparent"
            )}
          >
            {getPlatformIcon('ios')}
            <span>iOS</span>
            {getPlatformBadge('ios')}
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
