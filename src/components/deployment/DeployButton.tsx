'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Rocket } from 'lucide-react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import DeploymentDialog from "@/components/deployment/DeploymentDialog";

interface DeployButtonProps {
    projectId: string;
}

export const DeployButton = observer(({ projectId }: DeployButtonProps) => {
  const [open, setOpen] = useState(false);
  const { deploymentStore } = useStores();
  
  const handleOpenChange = (open: boolean) => {
    setOpen(open);
    deploymentStore.toggleDeploymentDialog(open);
  };

  return (
    <>
      <Button 
        variant="outline" 
        size="sm" 
        className="h-7 gap-1" 
        onClick={() => handleOpenChange(true)}
      >
        <Rocket className="h-3.5 w-3.5" />
        Deploy
      </Button>
      
      <DeploymentDialog
        open={open}
        onOpenChange={handleOpenChange}
        projectId={projectId}
      />
    </>
  );
});
