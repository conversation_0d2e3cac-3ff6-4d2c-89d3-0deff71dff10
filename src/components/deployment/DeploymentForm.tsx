'use client';

import React, { useState } from 'react';
import { DeploymentPlatform } from '@/stores/DeploymentStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { AlertCircle, ArrowRight, CheckCircle, Cloud, Code, Cog, Rocket, Server, Clock } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface DeploymentFormProps {
  target: DeploymentPlatform;
  onDeploy: (options: any) => void;
  isDeploying: boolean;
  platformFeatures: Record<string, any>;
}

export function DeploymentForm({ target, onDeploy, isDeploying, platformFeatures }: DeploymentFormProps) {
  // Define the type for our options to fix TypeScript errors
  type DeploymentOptions = {
    target: DeploymentPlatform;
    // No other options needed for one-click deployment
  };

  // Simplified options - only target is needed
  const [options, setOptions] = useState<DeploymentOptions>({
    target
  });

  const handleChange = (key: string, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };
  
  // Determine platform status based on the actual API implementation
  const isPlatformAvailable = () => {
    if (target === 'android' || target === 'web') return true; // Android and Web are available
    return false; // Only iOS is coming soon
  };
  
  const isComingSoon = !isPlatformAvailable();
  
  // Get the appropriate icon for each target
  const getTargetIcon = () => {
    switch(target) {
      case 'web':
        return <Cloud className="h-5 w-5 text-blue-500" />;
      case 'android':
        return <Server className="h-5 w-5 text-green-500" />;
      case 'ios':
        return <Cog className="h-5 w-5 text-orange-500" />;
      default:
        return <Code className="h-5 w-5" />;
    }
  };
  
  // Get the appropriate title for each target
  const getTargetTitle = () => {
    switch(target) {
      case 'web':
        return 'Web Deployment';
      case 'android':
        return 'Android Deployment';
      case 'ios':
        return 'iOS Deployment';
      default:
        return 'Deployment';
    }
  };
  
  // Get the appropriate description for each target
  const getTargetDescription = () => {
    switch(target) {
      case 'web':
        return 'Deploy your application to a web hosting service';
      case 'android':
        return 'Build and deploy your Android application';
      case 'ios':
        return 'Build and deploy your iOS application';
      default:
        return 'Configure your deployment options';
    }
  };

  // This section was removed to fix duplicate declaration
  
  return (
    <Card className="border-dashed">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          {getTargetIcon()}
          <div>
            <CardTitle className="text-base">{getTargetTitle()}</CardTitle>
            <CardDescription className="text-xs">{getTargetDescription()}</CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4 pt-0">
        {isComingSoon ? (
          <div className="flex items-center justify-center p-6 border rounded-md bg-muted/20">
            <div className="text-center space-y-2">
              <AlertCircle className="h-8 w-8 text-amber-500 mx-auto opacity-80" />
              <p className="text-sm font-medium text-muted-foreground">This feature is coming soon!</p>
              <p className="text-xs text-muted-foreground">We're working hard to bring {target} deployments to you.</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="space-y-4">
              {/* Android deployment - simplified with no input fields */}
              {target === 'android' && (
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-xs font-medium">One-Click Android Deployment</h4>
                      <p className="text-[10px] text-muted-foreground mt-1">
                        Build and deploy your Android APK with a single click
                      </p>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30">
                      <Server className="h-3 w-3 mr-1" />
                      APK Build
                    </Badge>
                  </div>
                </div>
              )}

              {/* Web deployment - simplified with no input fields */}
              {target === 'web' && (
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-xs font-medium">One-Click Web Deployment</h4>
                      <p className="text-[10px] text-muted-foreground mt-1">
                        Deploy your app to the web with a single click
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* iOS-specific information - shown as coming soon */}
              {target === 'ios' && (
                <div className="opacity-60">
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-xs font-medium">iOS Deployment</h4>
                        <p className="text-[10px] text-muted-foreground mt-1">
                          iOS deployment coming soon
                        </p>
                      </div>
                      <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/30">
                        <Clock className="h-3 w-3 mr-1" />
                        Coming Soon
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="pt-0">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="w-full">
                <Button 
                  onClick={() => onDeploy(options)} 
                  disabled={isDeploying || isComingSoon} 
                  className={cn(
                    "w-full h-10 transition-all",
                    isDeploying ? "bg-blue-600 hover:bg-blue-700" : ""
                  )}
                >
                  {isDeploying ? (
                    <>
                      <div className="h-4 w-4 mr-2 rounded-full border-2 border-t-transparent border-white animate-spin" />
                      Deploying...
                    </>
                  ) : (
                    <>
                      <Rocket className="mr-2 h-4 w-4" />
                      Deploy {target.charAt(0).toUpperCase() + target.slice(1)}
                    </>
                  )}
                </Button>
              </div>
            </TooltipTrigger>
            {isComingSoon && (
              <TooltipContent>
                <p className="text-xs">This feature is coming soon</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </CardFooter>
    </Card>
  );
}
