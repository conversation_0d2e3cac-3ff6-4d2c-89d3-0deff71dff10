"use client";

import React, { useState, useEffect, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { DeploymentPlatform, Deployment } from '@/stores/DeploymentStore';
import { StoreContext } from '@/stores/utils/StoreContext';
import { DeploymentForm } from './DeploymentForm';
import { DeploymentHistory } from './DeploymentHistory';
import { DeploymentHeader } from './DeploymentHeader';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

interface DeploymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
}

export const DeploymentDialog = observer(({ open, onOpenChange, projectId }: DeploymentDialogProps) => {
  const rootStore = useContext(StoreContext);
  if (!rootStore) return null;

  const { deploymentStore, notificationStore } = rootStore;
  const [activeTab, setActiveTab] = useState<DeploymentPlatform>('web');
  const [isLoading, setIsLoading] = useState(false);
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployments, setDeployments] = useState<any[]>([]);
  const [deploymentSuccess, setDeploymentSuccess] = useState(false);

  const { platformFeatures } = deploymentStore;

  // Load deployments when dialog opens
  useEffect(() => {
    if (open) {
      loadDeployments();
    }
  }, [projectId, deploymentStore, open, activeTab]);

  // Load deployments from the store
  const loadDeployments = async () => {
    if (!projectId) return;
    
    setIsLoading(true);
    try {
      await deploymentStore.loadDeploymentsFromDatabase(projectId);
      // Get all deployments for this chat
      const loadedDeployments = deploymentStore.getDeployments(projectId);
      setDeployments(loadedDeployments);
    } catch (error) {
      console.error('Failed to load deployments:', error);
      notificationStore.showNotification({
        title: 'Error',
        message: 'Failed to load deployments',
        type: 'error',
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: DeploymentPlatform) => {
    setActiveTab(value);
    // Reset deployment success state when changing tabs
    setDeploymentSuccess(false);
  };

  const handleDeploy = async (options: any) => {
    if (!projectId) return;

    setIsDeploying(true);
    try {
      await deploymentStore.deployProject(projectId, options.target);
      await loadDeployments(); // Refresh deployments list
      setDeploymentSuccess(true);
      notificationStore.showNotification({
        title: 'Deployment started',
        message: `Your ${options.target} deployment has been queued`,
        type: 'success',
        duration: 5000
      });
    } catch (error) {
      console.error('Deployment failed:', error);
      notificationStore.showNotification({
        title: 'Deployment failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        type: 'error',
        duration: 5000
      });
    } finally {
      setIsDeploying(false);
    }
  };

  const getDeploymentsByTarget = (target: DeploymentPlatform) => {
    return deployments?.filter((d: Deployment) => d.platform === target) || [];
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] p-0 overflow-hidden">
        <DeploymentHeader 
          activeTab={activeTab} 
          onTabChange={handleTabChange} 
        />

        <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as DeploymentPlatform)} className="w-full">
          {/* Web Deployment Tab */}
          <TabsContent value="web" className="space-y-4 px-6 py-4">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-[200px] w-full rounded-md" />
              </div>
            ) : deploymentSuccess ? (
              <div className="space-y-6">
                <Alert className="bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-900">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <AlertTitle className="text-green-800 dark:text-green-400">Deployment initiated</AlertTitle>
                  <AlertDescription className="text-green-700 dark:text-green-500">
                    Your web deployment has been queued and will be processed shortly.
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Deployment Status</h3>
                  <div className="rounded-md border p-4 bg-muted/30">
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-blue-500 animate-pulse" />
                      <span className="font-medium">Processing your deployment</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1 ml-6">This may take a few minutes. You can check the status in the deployments tab.</p>
                  </div>
                </div>
                
                <Separator />
                
                <DeploymentHistory 
                  deployments={getDeploymentsByTarget('web')} 
                  target="web" 
                  isLoading={isLoading} 
                />
              </div>
            ) : (
              <div className="space-y-6">
                <div className="max-w-md mx-auto">
                  <h3 className="text-sm font-medium tracking-tight mb-4">One-Click Deployment</h3>
                  <DeploymentForm 
                    target="web" 
                    onDeploy={handleDeploy} 
                    isDeploying={isDeploying} 
                    platformFeatures={platformFeatures}
                  />
                </div>
              </div>
            )}
          </TabsContent>

          {/* Android Deployment Tab */}
          <TabsContent value="android" className="space-y-4 px-6 py-4">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-[200px] w-full rounded-md" />
              </div>
            ) : deploymentSuccess ? (
              <div className="space-y-6">
                <Alert className="bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-900">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <AlertTitle className="text-green-800 dark:text-green-400">Deployment initiated</AlertTitle>
                  <AlertDescription className="text-green-700 dark:text-green-500">
                    Your Android deployment has been queued and will be processed shortly.
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Deployment Status</h3>
                  <div className="rounded-md border p-4 bg-muted/30">
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-blue-500 animate-pulse" />
                      <span className="font-medium">Building your APK</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1 ml-6">This may take a few minutes. You can check the status in the deployments tab.</p>
                  </div>
                </div>
                
                <Separator />
                
                <DeploymentHistory 
                  deployments={getDeploymentsByTarget('android')} 
                  target="android" 
                  isLoading={isLoading} 
                />
              </div>
            ) : (
              <div className="max-w-md mx-auto">
                <h3 className="text-sm font-medium tracking-tight mb-4">One-Click Deployment</h3>
                <DeploymentForm 
                  target="android" 
                  onDeploy={handleDeploy} 
                  isDeploying={isDeploying} 
                  platformFeatures={platformFeatures}
                />
              </div>
            )}
          </TabsContent>

          {/* iOS Deployment Tab */}
          <TabsContent value="ios" className="space-y-4 px-6 py-4">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-[200px] w-full rounded-md" />
              </div>
            ) : (
              <div className="space-y-6">
                <Alert className="bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-900">
                  <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <AlertTitle className="text-amber-800 dark:text-amber-400">Coming Soon</AlertTitle>
                  <AlertDescription className="text-amber-700 dark:text-amber-500">
                    iOS deployment is coming soon! We're working hard to bring this feature to you.
                  </AlertDescription>
                </Alert>
                
                <div className="max-w-md mx-auto">
                  <h3 className="text-sm font-medium tracking-tight mb-4">One-Click Deployment</h3>
                  <DeploymentForm 
                    target="ios" 
                    onDeploy={handleDeploy}
                    isDeploying={isDeploying} 
                    platformFeatures={platformFeatures}
                  />
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
});

export default DeploymentDialog;
