'use client';

import React from 'react';
import { Deployment, DeploymentPlatform } from '@/stores/DeploymentStore';
import { formatDistanceToNow, format } from 'date-fns';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ExternalLink, AlertCircle, CheckCircle, Clock, Download, Eye, History, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Skeleton } from '@/components/ui/skeleton';

interface DeploymentHistoryProps {
  deployments: Deployment[];
  target: DeploymentPlatform;
  isLoading: boolean;
}

export function DeploymentHistory({ deployments, target, isLoading }: DeploymentHistoryProps) {
  const filteredDeployments = deployments.filter(d => d.platform === target);
  
  if (isLoading) {
    return (
      <Card className="border-dashed">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-5 w-16" />
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }
  
  if (filteredDeployments.length === 0) {
    return (
      <Card className="border-dashed">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <History className="h-4 w-4 text-muted-foreground" />
              <CardTitle className="text-sm font-medium">Deployment History</CardTitle>
            </div>
            <Badge variant="outline" className="text-xs font-normal">
              0 deployments
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="rounded-full bg-muted/30 p-3 mb-3">
              <History className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="text-sm font-medium text-muted-foreground">No deployment history</p>
            <p className="text-xs text-muted-foreground mt-1">You haven't deployed to {target} yet</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-dashed">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <History className="h-4 w-4 text-primary/70" />
            <CardTitle className="text-sm font-medium">Deployment History</CardTitle>
          </div>
          <Badge variant="outline" className="text-xs font-normal">
            {filteredDeployments.length} {filteredDeployments.length === 1 ? 'deployment' : 'deployments'}
          </Badge>
        </div>
        <CardDescription className="text-xs pt-1">
          Recent {target} deployments
        </CardDescription>
      </CardHeader>
      
      <CardContent className="p-0">
        <ScrollArea className="h-[220px]">
          <div className="space-y-2 p-4 pt-0">
            {filteredDeployments.map((deployment, index) => (
              <React.Fragment key={deployment.id}>
                <DeploymentHistoryItem deployment={deployment} />
                {index < filteredDeployments.length - 1 && <Separator className="my-2" />}
              </React.Fragment>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

function DeploymentHistoryItem({ deployment }: { deployment: Deployment }) {
  const isInProgress = ['processing', 'queued'].includes(deployment.status);
  const isCompleted = deployment.status === 'completed';
  const isFailed = deployment.status === 'failed';
  
  const statusColors = {
    completed: "text-green-600 dark:text-green-400",
    failed: "text-red-600 dark:text-red-400",
    processing: "text-blue-600 dark:text-blue-400",
    queued: "text-amber-600 dark:text-amber-400"
  };
  
  const badgeVariants = {
    completed: "bg-green-50 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-400 dark:border-green-800",
    failed: "bg-red-50 text-red-700 border-red-200 dark:bg-red-950/30 dark:text-red-400 dark:border-red-800",
    processing: "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/30 dark:text-blue-400 dark:border-blue-800",
    queued: "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/30 dark:text-amber-400 dark:border-amber-800"
  };
  
  const getActionButton = () => {
    if (deployment.url && isCompleted) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                size="sm" 
                variant="ghost" 
                className="h-7 px-2 text-xs"
                asChild
              >
                <a 
                  href={deployment.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-1"
                >
                  {deployment.platform === 'android' ? (
                    <>
                      <Download className="h-3 w-3" />
                      <span>Download</span>
                    </>
                  ) : (
                    <>
                      <Eye className="h-3 w-3" />
                      <span>View</span>
                    </>
                  )}
                </a>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p className="text-xs">
                {deployment.platform === 'android' ? 'Download APK' : 'View deployed site'}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    
    if (isInProgress) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                size="sm" 
                variant="ghost" 
                className="h-7 px-2 text-xs cursor-not-allowed opacity-70"
                disabled
              >
                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                <span>{deployment.status === 'queued' ? 'Queued' : 'Building'}</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p className="text-xs">
                {deployment.status === 'queued' ? 'Waiting in queue' : 'Build in progress'}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    
    return null;
  };
  
  return (
    <div className="text-xs">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <div className={cn(
            "rounded-full p-1",
            badgeVariants[deployment.status as keyof typeof badgeVariants] || "bg-muted"
          )}>
            {getStatusIcon(deployment.status)}
          </div>
          <div>
            <div className="font-medium flex items-center gap-1">
              <span>v{deployment.version}</span>
              <Badge 
                variant="outline" 
                className={cn(
                  "text-[10px] font-normal ml-1 px-1.5 py-0 h-4",
                  badgeVariants[deployment.status as keyof typeof badgeVariants] || "bg-muted",
                  isInProgress && "animate-pulse"
                )}
              >
                {deployment.status}
              </Badge>
            </div>
            <div className="text-muted-foreground text-[10px]">
              {format(new Date(deployment.createdAt), 'MMM d, yyyy h:mm a')}
            </div>
          </div>
        </div>
        
        <div>
          {getActionButton()}
        </div>
      </div>
      
      {deployment.error && (
        <div className="mt-1 p-2 rounded-md bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-900">
          <div className="flex items-start gap-1.5">
            <AlertCircle className="h-3 w-3 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <p className="text-[10px] text-red-700 dark:text-red-400">
              {deployment.error.length > 100 ? `${deployment.error.substring(0, 100)}...` : deployment.error}
            </p>
          </div>
        </div>
      )}
      
      {isInProgress && !deployment.error && (
        <div className="mt-1 p-2 rounded-md bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-900">
          <div className="flex items-center gap-1.5">
            <Clock className="h-3 w-3 text-blue-600 dark:text-blue-400 animate-pulse" />
            <p className="text-[10px] text-blue-700 dark:text-blue-400">
              {deployment.status === 'queued' ? 'Build queued and waiting to start...' : 'Build in progress, this may take a few minutes...'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />;
    case 'failed':
      return <AlertCircle className="h-3 w-3 text-red-600 dark:text-red-400" />;
    case 'processing':
      return <RefreshCw className="h-3 w-3 text-blue-600 dark:text-blue-400 animate-spin" />;
    case 'queued':
      return <Clock className="h-3 w-3 text-amber-600 dark:text-amber-400 animate-pulse" />;
    default:
      return <Clock className="h-3 w-3 text-muted-foreground" />;
  }
}
