"use client";

import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { signIn, useSession } from 'next-auth/react';
import { useAnonymousSession } from '@/providers/anonymous-provider';
import { TransferStatus } from './transfer-status';
import { useTransferOwnership } from '@/hooks/use-transfer-ownership';
import { trackUserEvent, trackSubscriptionEvent } from '@/lib/analytics/track';

export const LoginDialog = observer(() => {
  const [isLoading, setIsLoading] = useState(false);
  const { generatorStore } = useStores();
  const { loginDialogOpen, toggleLoginDialog } = generatorStore;
  const { anonymousId } = useAnonymousSession();
  const { data: session } = useSession();
  const { status: transferStatus, transfer, setStatus } = useTransferOwnership();
  
  // Track when login dialog is opened
  useEffect(() => {
    if (loginDialogOpen) {
      trackUserEvent('SIGNED_IN', {
        auth_method: 'dialog_shown',
        referral_source: window.location.pathname
      });
      
      // Also track as a subscription event since this is often related to usage limits
      trackSubscriptionEvent('UPGRADE_DIALOG_VIEWED', {
        trigger_reason: 'login_prompt',
        current_plan: 'anonymous'
      });
    }
  }, [loginDialogOpen]);

  const handleLogin = async (provider: string) => {
    try {
      // Track login attempt with specific provider
      trackUserEvent('SIGNED_IN', {
        auth_method: provider,
        referral_source: window.location.pathname
      });
      
      setIsLoading(true);
      // Store anonymous ID for transfer
      if (anonymousId) {
        localStorage.setItem('pendingTransfer', anonymousId);
      }
      await signIn(provider, { callbackUrl: window.location.href });
    } catch (error) {
      console.error('Login error:', error);
      
      // Track login error
      trackUserEvent('SIGNED_IN', {
        auth_method: provider,
        error_type: 'login_error',
        referral_source: window.location.pathname
      });
      
      setIsLoading(false);
    }
  };

  // Check and handle pending transfer
  useEffect(() => {
    const pendingTransfer = localStorage.getItem('pendingTransfer');
    if (pendingTransfer && session?.user) {
      transfer(pendingTransfer);
    }
  }, [session, transfer]);

  useEffect(() => {
    if(transferStatus === "success") {
      toggleLoginDialog(false);
    }
  }, [transferStatus]);

  return (
    <>
      <Dialog 
        open={loginDialogOpen} 
        onOpenChange={(open) => generatorStore.toggleLoginDialog(open)}
      >
      <DialogContent className="sm:max-w-[360px] bg-black text-white p-4">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">Continue Building Your App</DialogTitle>
          <DialogDescription className="text-gray-400">
            Sign in to continue building your app and access more features.
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-4 mt-4">
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-white">What you&apos;ll get:</h4>
            <ul className="space-y-2 text-sm text-gray-400">
              <li>✓ More app generations</li>
              <li>✓ Save & resume projects</li>
              <li>✓ Access to all templates</li>
              <li>✓ Custom configurations</li>
            </ul>
          </div>

          <Button
            className="w-full bg-white text-black hover:bg-gray-200"
            onClick={() => handleLogin('google')}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <>
                <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Continue with Google
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
    <TransferStatus 
      open={transferStatus === 'loading' || transferStatus === 'success'} 
      status={transferStatus === 'loading' ? 'loading' : 'success'}
      onOpenChange={(open) => {
        if (!open && transferStatus === 'success') {
          setStatus('idle');
        }
      }}
    />
    </>
  );
});
