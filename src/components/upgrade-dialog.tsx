"use client";

import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, ChevronRight, ChevronDown } from 'lucide-react';
import { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { PLANS, PlanTier, Plan, getPlanByTier } from '@/lib/subscription/plans';
import { useStores } from '@/stores/utils/useStores';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { trackSubscriptionEvent } from '@/lib/analytics/track';

export const UpgradeDialog = observer(() => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('starter');
  const [currentPlan, setCurrentPlan] = useState<PlanTier>('free');
  const [showAllPlans, setShowAllPlans] = useState(false);
  const { generatorStore } = useStores();
  const pathname = usePathname();
  const { usageLimit, upgradeDialogOpen } = generatorStore;
  
  // Get all paid plans
  const paidPlans = PLANS.filter(plan => plan.tier !== 'free');
  
  // Get current plan index
  const currentPlanIndex = paidPlans.findIndex(plan => plan.tier === currentPlan);
  
  // Check if user is on the highest tier plan
  const isHighestTier = currentPlanIndex === paidPlans.length - 1;
  
  // Get available plans (only higher tier plans than current)
  const availablePlans = paidPlans.filter(plan => {
    // Get index to determine if plan is higher tier
    const planIndex = paidPlans.findIndex(p => p.tier === plan.tier);
    
    // Only show plans with higher tier (higher index)
    return planIndex > currentPlanIndex;
  });
  
  // Get next plan (next higher tier after current)
  const nextPlanIndex = currentPlanIndex + 1 < paidPlans.length ? currentPlanIndex + 1 : -1;
  // For free plan, show first paid plan. For highest tier, don't show a next plan.
  const nextPlan = currentPlan === 'free' ? paidPlans[1] :
                  (nextPlanIndex >= 0 ? paidPlans[nextPlanIndex] : null);
  
  // Get current plan details
  const currentPlanDetails = getPlanByTier(currentPlan);
  
  // Get selected plan details
  const selectedPlanDetails = PLANS.find(plan => plan.id === selectedPlan);
  
  // Fetch current plan on mount
  useEffect(() => {
    const fetchCurrentPlan = async () => {
      try {
        const response = await fetch('/api/subscription/status');
        const data = await response.json();
        if (data.planTier) {
          setCurrentPlan(data.planTier as PlanTier);
          
          // If current plan is not free, select the next tier as default
          if (data.planTier !== 'free' || data.planTier !== 'anonymous') {
            const currentIndex = PLANS.findIndex(p => p.tier === data.planTier);
            if (currentIndex >= 0 && currentIndex < PLANS.length - 1) {
              setSelectedPlan(PLANS[currentIndex + 1].id);
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch subscription status:', error);
      }
    };
    
    if (upgradeDialogOpen) {
      fetchCurrentPlan();
      
      // Track when upgrade dialog is opened
      trackSubscriptionEvent('UPGRADE_DIALOG_VIEWED', {
        current_plan: currentPlan,
        trigger_reason: usageLimit.remaining === 0 ? 'usage_limit_reached' : 'manual',
        message_limit_remaining: usageLimit.remaining
      });
    }
  }, [upgradeDialogOpen, currentPlan, usageLimit]);

  const handleUpgrade = async (planId: string = selectedPlan) => {
    try {
      setIsLoading(true);
      
      // Get selected plan details for tracking
      const planDetails = PLANS.find(plan => plan.id === planId);
      
      // Track upgrade initiation
      trackSubscriptionEvent('UPGRADE_INITIATED', {
        current_plan: currentPlan,
        plan_type: planDetails?.tier || planId,
        price: planDetails?.price || 0,
        currency: 'USD',
        entry_point: 'upgrade_dialog'
      });
      
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId,
          returnUrl: pathname,
        }),
      });

      const data = await response.json();

      if (data.error) {
        console.error('Checkout error:', data.error);
        
        // Track upgrade failure
        trackSubscriptionEvent('UPGRADE_FAILED', {
          current_plan: currentPlan,
          plan_type: planDetails?.tier || planId,
          error_message: data.error
        });
        
        return;
      }

      // If this was an upgrade that was processed directly (no checkout URL)
      if (data.upgraded) {
        // Track successful upgrade
        trackSubscriptionEvent('UPGRADE_COMPLETED', {
          current_plan: currentPlan,
          plan_type: planDetails?.tier || planId,
          price: planDetails?.price || 0,
          currency: 'USD'
        });
        
        // Close the dialog
        generatorStore.toggleUpgradeDialog(false);
        
        // Show success message or refresh the page to update subscription status
        alert(data.message || 'Plan upgraded successfully!');
        window.location.reload();
        return;
      }

      // For new subscriptions, redirect to checkout URL
      window.location.href = data.url;
    } catch (error) {
      console.error('Error initiating checkout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog 
      open={upgradeDialogOpen}
      onOpenChange={(open) => {
        if (!open && upgradeDialogOpen) {
          // Track when dialog is dismissed
          trackSubscriptionEvent('UPGRADE_DISMISSED', {
            current_plan: currentPlan,
            time_viewed: Date.now() - (upgradeDialogOpen ? Date.now() : 0) // Approximate time viewed
          });
        }
        generatorStore.toggleUpgradeDialog(open);
      }}
    >
      <DialogContent className="sm:max-w-[400px] bg-black text-white p-4" disableCloseButton={true}>
        <button
          onClick={() => generatorStore.upgradeDialogOpen = false}
          className="absolute right-3 top-3 text-white/70 hover:text-white text-sm"
        >
          ✕
        </button>
        
        <div className="space-y-3 max-h-[60vh] overflow-y-auto">
          <div>
            <DialogTitle className="text-base font-medium mb-1">
              {currentPlan === 'free' ? 'Upgrade Your Plan' : 'Change Your Plan'}
            </DialogTitle>
            <DialogDescription className="text-xs text-white/70">
              {usageLimit.remaining === 0 
                ? 'Upgrade now for higher message limits'
                : `${usageLimit.remaining} message left today • Upgrade for more`}
            </DialogDescription>
          </div>

          {/* Current Plan */}
          <div className="mb-3">
            <div className="text-xs text-white/70 mb-1">Current Plan</div>
            <div className="flex items-center justify-between p-3 rounded-md bg-white/5 border border-white/10">
              <div className="flex flex-col">
                <div className="flex items-baseline gap-1">
                  <div className="text-xl font-semibold tracking-tight">
                    {currentPlan === 'free' ? 'Free' : `$${currentPlanDetails.price}`}
                  </div>
                  {currentPlan !== 'free' && <div className="text-xs text-white/50">/mo</div>}
                </div>
                <div className="text-[10px] text-white/60 mt-0.5">
                  {currentPlanDetails.operations} messages/month
                </div>
              </div>
              <div className="text-[11px] text-white/70">
                <span className="px-1.5 py-0.5 bg-primary/10 text-primary rounded">
                  {currentPlan === 'free' ? 'Free Plan' : `${currentPlanDetails.name} Plan`}
                </span>
              </div>
            </div>

          </div>

          {/* Next Plan */}
          {nextPlan && !isHighestTier ? (
            <div className="flex flex-col gap-2">
              <div className="text-xs text-white/70 mb-1 flex justify-between items-center">
                <span>{currentPlan === 'free' ? 'Recommended Plan' : 'Upgrade Option'}</span>
                {/*{currentPlan !== 'free' && nextPlan && (*/}
                {/*  <span className="text-[10px] text-accent">*/}
                {/*    +{nextPlan.operations - currentPlanDetails.operations} more credits*/}
                {/*  </span>*/}
                {/*)}*/}
              </div>
              <div 
                className={cn(
                  "flex items-center justify-between p-3 rounded-md border",
                  selectedPlan === nextPlan.id 
                    ? "bg-accent/10 border-accent/30" 
                    : "bg-white/5 border-white/10"
                )}
                onClick={() => setSelectedPlan(nextPlan.id)}
                style={{ cursor: 'pointer' }}
              >
                <div className="flex flex-col">
                  <div className="flex items-baseline gap-1">
                    <div className="text-2xl font-semibold">${nextPlan.price}</div>
                    <div className="text-xs text-white/50">/mo</div>
                  </div>
                  <div className="text-[10px] text-white/60 mt-0.5">
                    {nextPlan.operations} messages/month
                  </div>
                </div>
                <div className="text-[11px] text-white/70">
                  <span className="px-1.5 py-0.5 bg-accent/10 text-accent rounded">{nextPlan.name} Plan</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 gap-1 text-[11px] text-white/70">
                {nextPlan.features.map((feature: string, i: number) => (
                  <div key={i} className="flex items-center gap-1">
                    <span className="text-accent text-[10px]">✓</span> {feature}
                  </div>
                ))}
                {/*{nextPlan.features.length > 4 && (*/}
                {/*  <div className="text-[10px] text-accent/80 flex items-center gap-1">*/}
                {/*    +{nextPlan.features.length - 4} more features*/}
                {/*  </div>*/}
                {/*)}*/}
              </div>
            </div>
          ) : isHighestTier ? (
            <div className="flex flex-col gap-2 p-4 border border-accent/20 rounded-lg bg-accent/5">
              <div className="text-center">
                <div className="text-lg font-medium mb-1">You're on our highest tier plan</div>
                <p className="text-sm text-muted-foreground">
                  Enjoy all the premium features and maximum messages available!
                </p>
              </div>
            </div>
          ) : null}

          {/* Show all plans toggle */}
          {availablePlans.length > 1 && (
            <button 
              onClick={() => setShowAllPlans(!showAllPlans)}
              className="flex items-center text-xs text-accent hover:text-accent/80 mt-2"
            >
              {showAllPlans ? (
                <>
                  <ChevronDown className="h-3 w-3 mr-1" />
                  Hide other plans
                </>
              ) : (
                <>
                  <ChevronRight className="h-3 w-3 mr-1" />
                  Compare all plans
                </>
              )}
            </button>
          )}
          
          {/* All other plans */}
          {showAllPlans && availablePlans.length > 1 && (
            <div className="space-y-4 mt-3 pt-3 border-t border-white/10">
              <div className="text-xs font-medium text-white/80">Other Available Plans</div>
              {availablePlans.slice(1).map((plan) => (
                <div 
                  key={plan.id}
                  className={cn(
                    "flex flex-col p-3 rounded-md border",
                    selectedPlan === plan.id 
                      ? "bg-accent/10 border-accent/30" 
                      : "bg-white/5 border-white/10"
                  )}
                  onClick={() => setSelectedPlan(plan.id)}
                  style={{ cursor: 'pointer' }}
                >
                  {/* Plan header */}
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex flex-col">
                      <div className="flex items-baseline gap-1">
                        <div className="text-lg font-semibold">${plan.price}</div>
                        <div className="text-xs text-white/50">/mo</div>
                      </div>
                      <div className="text-[10px] text-white/60 mt-0.5">
                        {plan.operations} messages/month
                      </div>
                    </div>
                    <div className="text-[11px] text-white/70">
                      <span className="px-1.5 py-0.5 bg-accent/10 text-accent rounded">{plan.name} Plan</span>
                    </div>
                  </div>
                  

                  
                  {/* Features */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-3 gap-y-1 text-[11px] text-white/70">
                    {plan.features.map((feature: string, i: number) => (
                      <div key={i} className="flex items-center gap-1">
                        <span className="text-accent text-[10px]">✓</span> {feature}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter className="mt-4">
          {isHighestTier ? (
            <Button
              className="w-full h-10 text-sm font-medium"
              onClick={() => generatorStore.toggleUpgradeDialog(false)}
            >
              Close
            </Button>
          ) : (
            <Button
              className="w-full h-10 text-sm font-medium"
              onClick={() => handleUpgrade(selectedPlan)}
              disabled={isLoading || selectedPlan === currentPlan}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-1.5 h-3 w-3 animate-spin" />
                  Processing...
                </>
              ) : selectedPlan === currentPlan ? (
                'Current Plan'
              ) : (
                <div className="flex flex-col items-center justify-center">
                  <span>{currentPlan === 'free' ? 'Upgrade Now' : 'Change Plan'}</span>
                  {selectedPlanDetails && (
                    <span className="text-[10px] opacity-80">
                      {selectedPlanDetails.name} (${selectedPlanDetails.price}/mo)
                    </span>
                  )}
                </div>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});
