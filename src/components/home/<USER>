"use client";
import React from 'react';
import {SidebarToggle} from '../base/sidebar-toggle';
import Link from "next/link";
import {signOut, useSession} from 'next-auth/react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {SidebarMenuButton} from "@/components/ui/sidebar";
import Image from "next/image";
import {ChevronDown, ChevronUp} from "lucide-react";
import {auth} from "@/app/(auth)/auth";
import {useAuth} from '@/hooks/use-auth';
import {Button} from "@/components/ui/button";
import MagicallyLogo from "@/components/logo";

interface HeaderProps {

}

export const HomeHeader: React.FC<HeaderProps> = () => {
    const {isAuthenticated, isLoading, handleRedirect, session} = useAuth();

    return (
        <div className="h-16 border-b border-[#181A1F]/40 bg-background/60 backdrop-blur-xl backdrop-saturate-150 w-full fixed top-0 z-[999] supports-[backdrop-filter]:bg-background/60
        flex shrink-0 px-4 py-2 justify-between items-center">

            <div className="flex space-x-4">
                {
                    isAuthenticated ?
                        <SidebarToggle/> :
                        null
                }

                <MagicallyLogo logoWidthAction={24} asLink/>
            </div>



            <div className="flex items-center justify-center">
                {
                    !isAuthenticated ?
                        <Link href='/login' className="px-8 py-2 inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50
                        [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground">
                            Login
                        </Link> :
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost">
                                    <Image
                                        src={session?.user.image || `https://avatar.vercel.sh/${session?.user.email}`}
                                        alt={session?.user.email ?? 'User Avatar'}
                                        width={24}
                                        height={24}
                                        className="rounded-full"
                                    />
                                    <span className="truncate max-w-16 md:max-w-32 lg:max-w-48">{session?.user?.name}</span>
                                    <ChevronDown className="ml-auto"/>
                                </Button>
                            </DropdownMenuTrigger>

                            <DropdownMenuContent
                                side="top"
                                className="w-[--radix-popper-anchor-width]"
                            >
                                <DropdownMenuItem asChild>
                                    <button
                                        type="button"
                                        className="w-full cursor-pointer"
                                        onClick={() => {
                                            signOut({
                                                redirectTo: '/',
                                            });
                                        }}
                                    >
                                        Sign out
                                    </button>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                }

            </div>
        </div>
    );
};
