'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Wand2 } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import MagicallyLogo from "@/components/logo";

interface IntroAnimationProps {
  onComplete?: () => void;
}

export function IntroAnimation({ onComplete }: IntroAnimationProps) {
  const [step, setStep] = useState(0);
  const [showAnimation, setShowAnimation] = useState(true);
  
  useEffect(() => {
    // Animation sequence timing
    const timers = [
      setTimeout(() => setStep(1), 800),  // Text appears
      setTimeout(() => setStep(2), 1600), // Wand appears
      setTimeout(() => setStep(3), 2400), // Magic effect
      setTimeout(() => {
        // Start fade out
        setStep(4);
        
        // Complete animation and notify parent
        setTimeout(() => {
          setShowAnimation(false);
          if (onComplete) onComplete();
        }, 1000); // Fade out duration
      }, 3000)
    ];
    
    // Cleanup timers
    return () => timers.forEach(timer => clearTimeout(timer));
  }, [onComplete]);

  return (
    <AnimatePresence>
      {showAnimation && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-background"
          initial={{ opacity: 1 }}
          animate={{ opacity: step === 4 ? 0 : 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative flex flex-col items-center">
            {/* Step 1: Text appears */}
            <AnimatePresence>
              {step >= 1 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-6 text-2xl md:text-3xl font-medium"
                >
                  Turn your ideas into
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Step 2: Wand appears */}
            <div className="relative h-20 w-20 flex items-center justify-center">
              <AnimatePresence>
                {step >= 2 && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, rotate: -45 }}
                    animate={{ opacity: 1, scale: 1, rotate: 0 }}
                    className="relative z-10"
                  >
                    <MagicallyLogo iconOnly={true}/>
                  </motion.div>
                )}
              </AnimatePresence>
              
              {/* Step 3: Magic effect */}
              <AnimatePresence>
                {step >= 3 && (
                  <>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.2 }}
                      animate={{ opacity: 1, scale: 2 }}
                      className="absolute inset-0 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-xl"
                    />
                    
                    {/* Sparkles */}
                    {[...Array(8)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{ 
                          opacity: 0, 
                          x: 0, 
                          y: 0,
                          scale: 0.5
                        }}
                        animate={{ 
                          opacity: [0, 1, 0], 
                          x: Math.cos(i * Math.PI / 4) * 50, 
                          y: Math.sin(i * Math.PI / 4) * 50,
                          scale: 1
                        }}
                        transition={{ 
                          duration: 0.8,
                          times: [0, 0.5, 1]
                        }}
                        className={cn(
                          "absolute h-2 w-2 rounded-full",
                          i % 2 === 0 ? "bg-primary" : "bg-accent"
                        )}
                      />
                    ))}
                    
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="absolute -bottom-16 text-3xl md:text-4xl font-bold"
                    >
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">
                        reality
                      </span>
                    </motion.div>
                  </>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
