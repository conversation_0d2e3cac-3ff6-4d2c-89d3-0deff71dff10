'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Volume2, VolumeX, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DemoVideoHeroProps {
  onComplete: () => void;
  skipIntro?: boolean; // Added to allow skipping if main intro animation is also skipped
}

const PROMPT_TEXT = "Create a stunning animated fitness tracker for me";
const TYPING_SPEED = 60; // ms per character
const POST_TYPING_DELAY = 1000; // ms to wait after typing before video starts
const VIDEO_FADE_DURATION = 1.5; // seconds for video to fade in
const POST_VIDEO_DELAY = 700; // ms to wait after video ends before completing

export function DemoVideoHero({ onComplete, skipIntro = false }: DemoVideoHeroProps) {
  const [currentStep, setCurrentStep] = useState<'typing' | 'generating' | 'video' | 'finished'>('typing');
  const [typedPrompt, setTypedPrompt] = useState('');
  const [isMuted, setIsMuted] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  // DEBUG: Log current step
  useEffect(() => {
    console.log('Current Demo Step:', currentStep);
    if (currentStep === 'video' && videoRef.current) {
      console.log('Video ref available, attempting to play. Src:', videoRef.current.src);
      videoRef.current.play().catch(error => console.error("Error attempting to play video:", error));
    }
  }, [currentStep]);

  useEffect(() => {
    if (skipIntro) {
      onComplete();
      return;
    }

    if (currentStep === 'typing') {
      if (typedPrompt.length < PROMPT_TEXT.length) {
        const timer = setTimeout(() => {
          setTypedPrompt(PROMPT_TEXT.substring(0, typedPrompt.length + 1));
        }, TYPING_SPEED);
        return () => clearTimeout(timer);
      } else {
        // Finished typing, move to generating
        const timer = setTimeout(() => setCurrentStep('generating'), POST_TYPING_DELAY);
        return () => clearTimeout(timer);
      }
    }
  }, [currentStep, typedPrompt, skipIntro, onComplete]);

  useEffect(() => {
    if (currentStep === 'generating') {
      // Simulate generation then move to video
      const timer = setTimeout(() => setCurrentStep('video'), 500); // Short generating pulse
      return () => clearTimeout(timer);
    }
  }, [currentStep]);

  useEffect(() => {
    if (currentStep === 'video' && videoRef.current) {
      videoRef.current.play().catch(error => console.error("Error attempting to play video:", error));
    }
  }, [currentStep]);

  const handleVideoEnd = () => {
    setCurrentStep('finished');
    const timer = setTimeout(onComplete, POST_VIDEO_DELAY);
    return () => clearTimeout(timer);
  };

  const toggleMute = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent click from bubbling up if video is interactive
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };

  if (skipIntro) return null;

  return (
    <div className="relative w-full h-screen flex flex-col items-center justify-center overflow-hidden bg-black">
      <AnimatePresence mode="wait">
        {/* STEP 1: Typing Prompt */} 
        {currentStep === 'typing' && (
          <motion.div
            key="typing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-2xl px-6 text-center z-10"
          >
            <div className="inline-block bg-gradient-to-r from-neutral-900 via-neutral-800 to-neutral-900 p-4 sm:p-6 rounded-xl shadow-2xl border border-neutral-700">
              <p className="font-mono text-sm sm:text-lg md:text-xl text-left text-green-400 min-h-[3em] sm:min-h-[2em]">
                <span className="text-blue-400">magically&gt;</span> {typedPrompt}
                <span className="animate-ping text-green-400">_</span>
              </p>
            </div>
          </motion.div>
        )}

        {/* STEP 2: Generating Pulse (Optional visual cue) */} 
        {currentStep === 'generating' && (
          <motion.div
            key="generating"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className="z-10"
          >
            <Zap className="w-16 h-16 text-purple-400 animate-pulse" />
          </motion.div>
        )}

        {/* STEP 3: Video Presentation */} 
        {currentStep === 'video' && (
          <motion.div
            key="video"
            initial={{ opacity: 0, scale: 0.85 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.85, transition: { duration: VIDEO_FADE_DURATION / 2 } }}
            transition={{ duration: VIDEO_FADE_DURATION, ease: "easeOut" }}
            className="relative w-full h-full flex items-center justify-center z-0 p-4 sm:p-8"
          >
            {/* Video Player Wrapper - Constrains size and maintains aspect ratio */}
            <div 
              className={cn(
                "relative bg-neutral-800 shadow-2xl rounded-lg md:rounded-xl lg:rounded-2xl overflow-hidden group",
                "w-full h-full max-w-full max-h-full", // Allow it to take space
                "sm:max-w-[300px] md:max-w-[340px] lg:max-w-[380px] xl:max-w-[420px]", // Max widths for phone-like appearance
                "aspect-[9/16]" // Enforce aspect ratio of the video itself
              )}
              // DEBUG: style={{ backgroundColor: 'deeppink' }} // Temporary background for visibility
            >
              <video
                ref={videoRef}
                className="absolute top-0 left-0 w-full h-full object-cover" // object-cover to fill, object-contain to show all
                src="/videos/clipped-grit-demo.mp4" // Ensure this path is correct in /public
                playsInline
                autoPlay
                muted={isMuted} // Start muted
                onEnded={handleVideoEnd}
                loop={false} // Video should play once
              />
              {/* Subtle Mute/Unmute Button */}
              <button
                onClick={toggleMute}
                className="absolute top-3 right-3 p-1.5 sm:p-2 bg-black/40 hover:bg-black/60 rounded-full text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 focus:outline-none focus:ring-1 focus:ring-purple-400/70"
                aria-label={isMuted ? 'Unmute video' : 'Mute video'}
              >
                {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Optional: Dark overlay during video to focus attention */}
      {currentStep === 'video' && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/30 z-[-1] pointer-events-none"
          />
      )}
    </div>
  );
}

