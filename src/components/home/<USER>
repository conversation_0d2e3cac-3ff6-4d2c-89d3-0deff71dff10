'use client';

import {useState, useEffect} from 'react';
import {HeroSection} from '@/components/home/<USER>';
import {DemoVideoHero} from '@/components/home/<USER>';
import {IntroAnimation} from '@/components/home/<USER>';
import {GradientBackground} from '@/components/ui/gradient-background';

// Function to set a cookie indicating the intro has been seen
export async function setIntroSeenCookie() {
    try {
        // Using fetch to call a server action that sets the cookie
        await fetch('/api/set-intro-cookie', {
            method: 'POST',
        });
    } catch (error) {
        console.error('Failed to set intro cookie:', error);
    }
}

interface HomePageClientLogicProps {
    initialShowFullIntroSequence: boolean;
}

export function HomePageClientLogic({initialShowFullIntroSequence}: HomePageClientLogicProps) {
    // Track which phase of the intro sequence we're in
    const [phase, setPhase] = useState<'introAnimation' | 'demoVideo' | 'heroSection'>(
        initialShowFullIntroSequence ? 'introAnimation' : 'heroSection'
    );

    // Control visibility of main content
    const [mainContentVisible, setMainContentVisible] = useState(!initialShowFullIntroSequence);

    // Handle intro animation completion
    const handleIntroAnimationComplete = () => {
        setPhase('demoVideo');
    };

    // If not showing full intro, main content should be visible from the start
    useEffect(() => {
        if (!initialShowFullIntroSequence) {
            setMainContentVisible(true);
            setPhase('heroSection');
        }
    }, [initialShowFullIntroSequence]);

    return (
        <>
            {/* Intro Animation - only shown if user hasn't seen it before */}
            {phase === 'introAnimation' && initialShowFullIntroSequence && (
                <IntroAnimation onComplete={handleIntroAnimationComplete}/>
            )}


            {/*<GradientBackground className="w-full">*/}
                <HeroSection/>
            {/*</GradientBackground>*/}
        </>
    );
}
