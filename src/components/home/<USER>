'use client';

import {motion} from 'framer-motion';
import {Wand2, <PERSON>, <PERSON>, Sparkles} from 'lucide-react';
import {cn} from '@/lib/utils';

const steps = [
    {
        icon: <Wand2 className="h-6 w-6"/>,
        title: "Describe Your Idea",
        description: "Start by describing your app idea in plain language. No technical jargon required.",
        color: "from-primary/15 to-primary/30",
        borderColor: "border-primary/10",
        iconColor: "text-primary/80",
        delay: 0
    },
    {
        icon: <Sparkles className="h-6 w-6"/>,
        title: "AI Generates Your App",
        description: "Our AI analyzes your description and generates a fully functional app with all the features you need.",
        color: "from-accent/15 to-accent/30",
        borderColor: "border-accent/10",
        iconColor: "text-accent/80",
        delay: 0.1
    },
    {
        icon: <Code className="h-6 w-6"/>,
        title: "Customize & Edit",
        description: "Fine-tune your app with our intuitive editor. Add features, change colors, or modify layouts.",
        color: "from-chart-3/15 to-chart-3/30",
        borderColor: "border-chart-3/10",
        iconColor: "text-chart-3/80",
        delay: 0.2
    },
    {
        icon: <Rocket className="h-6 w-6"/>,
        title: "Deploy & Share",
        description: "Deploy your app to the web with one click and share it with the world.",
        color: "from-chart-5/15 to-chart-5/30",
        borderColor: "border-chart-5/10",
        iconColor: "text-chart-5/80",
        delay: 0.3
    }
];

export function HowItWorks() {
    return (
        <div className="w-full relative overflow-hidden" style={{background: 'linear-gradient(rgb(172, 210, 227) 0%, rgb(250, 215, 225) 100%)'}}>
            <div className="absolute inset-0 backdrop-blur-md"></div>
            <section className="py-16 px-4 md:py-24 w-full max-w-7xl mx-auto text-gray-800 relative z-10">
                <motion.div
                    initial={{opacity: 0, y: 20}}
                    whileInView={{opacity: 1, y: 0}}
                    transition={{duration: 0.5}}
                    viewport={{once: true}}
                    className="text-center mb-16"
                >
                    <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900 font-mono">How Magically Works</h2>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                        From idea to working app in just a few simple steps. No coding required.
                    </p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {steps.map((step, index) => (
                        <motion.div
                            key={index}
                            initial={{opacity: 0, y: 20}}
                            whileInView={{opacity: 1, y: 0}}
                            transition={{duration: 0.5, delay: step.delay}}
                            viewport={{once: true}}
                            className={cn(
                                "rounded-2xl p-6 border shadow-lg",
                                "bg-gradient-to-br backdrop-blur-xl", step.color, "border-white/20",
                                "flex flex-col items-center text-center relative overflow-hidden"
                            )}
                        >
                            <div className={cn(
                                "h-12 w-12 rounded-full flex items-center justify-center mb-4",
                                "bg-white/80 backdrop-blur-sm", step.iconColor
                            )}>
                                {step.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-2 text-gray-900">{step.title}</h3>
                            <p className="text-gray-600">{step.description}</p>
                        </motion.div>
                    ))}
                </div>

                {/* Timeline connector for desktop */}
                {/*<div className="hidden lg:block relative h-1 max-w-4xl mx-auto">*/}
                {/*  <div className="absolute top-[-120px] left-0 right-0 h-1 bg-gradient-to-r from-primary/40 via-accent/40 to-chart-5/40"></div>*/}
                {/*  <div className="absolute top-[-120px] left-[12.5%] h-6 w-1 bg-primary/40"></div>*/}
                {/*  <div className="absolute top-[-120px] left-[37.5%] h-6 w-1 bg-accent/40"></div>*/}
                {/*  <div className="absolute top-[-120px] left-[62.5%] h-6 w-1 bg-chart-3/40"></div>*/}
                {/*  <div className="absolute top-[-120px] left-[87.5%] h-6 w-1 bg-chart-5/40"></div>*/}
                {/*</div>*/}
            </section>
        </div>

    );
}
