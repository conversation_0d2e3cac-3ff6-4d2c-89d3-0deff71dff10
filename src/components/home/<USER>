"use client";
import {motion} from "framer-motion";
import {APP_TEMPLATES} from "@/lib/appTemplates/appTemplates";
import Link from "next/link";
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card";
import {ArrowR<PERSON>, Wand2} from "lucide-react";

const Templates = () => {
    return (
        <motion.div
            initial={{opacity: 0, y: 20}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.5, delay: 0.4}}
            className="w-full max-w-5xl space-y-4 mt-12 pb-16 mx-auto"
        >
            <motion.h1
                initial={{opacity: 0, y: 20}}
                animate={{opacity: 1, y: 0}}
                transition={{duration: 0.5, delay: 0.1}}
                className="mb-6 w-full bg-gradient-to-b text-center from-foreground to-foreground/80 bg-clip-text text-2xl font-bold tracking-tight text-transparent sm:text-3xl"
            >
                Popular Starters
            </motion.h1>
            <div className="space-y-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {APP_TEMPLATES.map((template) => (
                        <Link
                            key={template.id}
                            href={template.link}
                            className="block group">
                            <Card
                                className="overflow-hidden h-full bg-card hover:bg-card/80 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-primary/5">
                                <CardHeader>
                                    <div className="flex items-start justify-between gap-4">
                                        <div className="space-y-1.5 flex-1 min-w-0">
                                            <CardTitle
                                                className="text-xl pr-4 truncate">{template.title}</CardTitle>
                                            <CardDescription
                                                className="line-clamp-2">{template.description}</CardDescription>
                                        </div>
                                        <div
                                            className="h-12 w-12 flex-shrink-0 rounded-full bg-gradient-to-r flex items-center justify-center transition-transform duration-300 group-hover:scale-110"
                                            style={{
                                                backgroundImage: `linear-gradient(to right, ${template.accent.from}, ${template.accent.via || template.accent.to}, ${template.accent.to})`
                                            }}
                                        >
                                            <ArrowRight className="h-5 w-5 text-white"/>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {/* Preview */}
                                    <div
                                        className="relative h-[660px] w-full rounded-lg overflow-hidden bg-gradient-to-b from-background to-muted/50 p-4">
                                        <div
                                            className="absolute inset-0 bg-gradient-to-r opacity-[0.03] group-hover:opacity-[0.05] transition-opacity duration-300"
                                            style={{
                                                backgroundImage: `linear-gradient(to right, ${template.accent.from}, ${template.accent.via || template.accent.to}, ${template.accent.to})`
                                            }}
                                        />
                                        <div
                                            className="relative h-[660px] w-full rounded-lg overflow-hidden border border-border/50 group-hover:border-border transition-colors duration-300">
                                            <motion.img
                                                initial={{scale: 1.1, y: 20, opacity: 0}}
                                                animate={{scale: 1, y: 0, opacity: 1}}
                                                transition={{duration: 0.5, delay: 0.2}}
                                                src={template.preview.dark}
                                                alt={template.title}
                                                className="w-full h-[720px] object-contain object-[center_-36px] group-hover:scale-105 transition-transform duration-500"
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>

                <Link href="/generator" className="block group">
                    <Card
                        className="h-full bg-card hover:bg-card/80 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-primary/5 overflow-hidden">
                        <div
                            className="h-full flex flex-col items-center justify-center p-8 text-center relative">
                            <div
                                className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"/>
                            <div className="relative space-y-3">
                                <div
                                    className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                    <Wand2 className="h-6 w-6 text-primary"/>
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold mb-2">Start from Scratch</h3>
                                    <p className="text-muted-foreground">Have a unique idea? Start with a blank
                                        canvas and let AI guide you.</p>
                                </div>
                            </div>
                        </div>
                    </Card>
                </Link>
            </div>
        </motion.div>
    )
}

export default Templates;