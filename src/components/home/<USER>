'use client';

import React, { useState, useRef, useCallback, type ChangeEvent } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  ImageIcon,
  XIcon,
  Upload,
  CheckCircle,
  ArrowRight,
  Palette,
  Zap,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Attachment } from 'ai';
import Image from 'next/image';
import { toast } from 'sonner';
import MagicallyLogo from '@/components/logo';

interface OnboardingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: (processType: 'design' | 'app', finalAttachments: Attachment[]) => void;
  existingAttachments: Attachment[];
  onAttachmentsChange: (attachments: Attachment[]) => void;
  processFiles: (files: File[]) => Promise<void>;
  uploadQueue: string[];
}

export function OnboardingModal({
  isOpen,
  onClose,
  onContinue,
  existingAttachments,
  onAttachmentsChange,
  processFiles,
  uploadQueue
}: OnboardingModalProps) {
  const [selectedProcess, setSelectedProcess] = useState<'design' | 'app'>('design');
  const [isDragging, setIsDragging] = useState(false);
  const [showDesignInspiration, setShowDesignInspiration] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file input change
  const handleFileChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      processFiles(files);

      // Reset the file input to allow selecting the same file again
      if (event.target) {
        event.target.value = '';
      }
    },
    [processFiles],
  );

  // Drag and drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) setIsDragging(true);
  }, [isDragging]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
  }, [processFiles]);

  const handleContinue = () => {
    onContinue(selectedProcess, existingAttachments);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] sm:max-h-[700px] p-0 bg-background/95 backdrop-blur-xl border-border/50 overflow-hidden flex flex-col">
        {/* Hidden file input */}
        <input
          type="file"
          className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
          ref={fileInputRef}
          multiple
          accept="image/jpeg,image/png,.jpg,.jpeg,.png"
          onChange={handleFileChange}
          tabIndex={-1}
        />

        <div className="relative flex flex-col h-full">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/5 to-chart-3/5" />

          <div className="relative z-10 flex flex-col h-full">
            {/* Header */}
            <div className="px-4 sm:px-6 py-3 border-b border-border/50 bg-background/50 backdrop-blur-sm flex-shrink-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <MagicallyLogo iconOnly className="h-8 w-8" />
                  <div>
                    <h2 className="text-lg font-semibold text-foreground">Let's Build Your App</h2>
                    <p className="text-sm text-muted-foreground">
                      Choose your approach and optionally add inspiration
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="h-8 w-8 rounded-full"
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-4 sm:p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Process Selection */}
                <div className="space-y-4">
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-foreground mb-2">
                      How would you like to build your app?
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Choose the approach that works best for you. You can always switch later.
                    </p>
                  </div>

                  <div className="grid gap-4">
                    {/* Design First Option */}
                    <div
                      className={cn(
                        'relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300',
                        'bg-gradient-to-br backdrop-blur-sm group hover:scale-[1.02]',
                        selectedProcess === 'design'
                          ? 'border-primary/50 bg-primary/5 shadow-lg shadow-primary/10'
                          : 'border-border/50 hover:border-primary/30 bg-secondary/30'
                      )}
                      onClick={() => setSelectedProcess('design')}
                    >
                      <div className="flex items-start gap-4">
                        <div className={cn(
                          'h-12 w-12 rounded-full flex items-center justify-center transition-all',
                          selectedProcess === 'design'
                            ? 'bg-primary/20 text-primary'
                            : 'bg-accent/20 text-accent group-hover:bg-primary/20 group-hover:text-primary'
                        )}>
                          <Palette className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold text-foreground">Design First</h4>
                            <span className="px-2 py-1 text-xs font-medium bg-accent/20 text-accent rounded-full">
                              Most Popular
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-3">
                            Start with visual design and mockups. Perfect for getting stakeholder buy-in and refining your vision before development.
                          </p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <CheckCircle className="h-3 w-3" />
                            <span>Visual mockups first</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                            <CheckCircle className="h-3 w-3" />
                            <span>Iterate on design before coding</span>
                          </div>
                        </div>
                        {selectedProcess === 'design' && (
                          <div className="absolute top-4 right-4">
                            <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center">
                              <CheckCircle className="h-4 w-4 text-white" />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Direct App Build Option */}
                    <div
                      className={cn(
                        'relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300',
                        'bg-gradient-to-br backdrop-blur-sm group hover:scale-[1.02]',
                        selectedProcess === 'app'
                          ? 'border-primary/50 bg-primary/5 shadow-lg shadow-primary/10'
                          : 'border-border/50 hover:border-primary/30 bg-secondary/30'
                      )}
                      onClick={() => setSelectedProcess('app')}
                    >
                      <div className="flex items-start gap-4">
                        <div className={cn(
                          'h-12 w-12 rounded-full flex items-center justify-center transition-all',
                          selectedProcess === 'app'
                            ? 'bg-primary/20 text-primary'
                            : 'bg-chart-3/20 text-chart-3 group-hover:bg-primary/20 group-hover:text-primary'
                        )}>
                          <Zap className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-foreground mb-2">Direct App Build</h4>
                          <p className="text-sm text-muted-foreground mb-3">
                            Jump straight into building a working app. Great for MVPs and when you want to see functionality quickly.
                          </p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <CheckCircle className="h-3 w-3" />
                            <span>Working app immediately</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                            <CheckCircle className="h-3 w-3" />
                            <span>Faster to market</span>
                          </div>
                        </div>
                        {selectedProcess === 'app' && (
                          <div className="absolute top-4 right-4">
                            <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center">
                              <CheckCircle className="h-4 w-4 text-white" />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Optional Design Inspiration Section */}
                <div className="space-y-4">
                  <Button
                    variant="ghost"
                    onClick={() => setShowDesignInspiration(!showDesignInspiration)}
                    className="w-full justify-between p-4 h-auto border border-border/50 hover:border-primary/30 bg-secondary/20 hover:bg-secondary/40"
                  >
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-gradient-to-br from-accent/20 to-chart-4/20 flex items-center justify-center">
                        <ImageIcon className="h-4 w-4 text-accent" />
                      </div>
                      <div className="text-left">
                        <p className="text-sm font-medium text-foreground">Add Design Inspiration</p>
                        <p className="text-xs text-muted-foreground">Optional - Upload images to guide the design</p>
                      </div>
                    </div>
                    {showDesignInspiration ? (
                      <ChevronUp className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>

                  <AnimatePresence>
                    {showDesignInspiration && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="space-y-4 overflow-hidden"
                      >
                        {/* Upload Area */}
                        <div
                          className={cn(
                            'relative border-2 border-dashed rounded-xl p-6 transition-all duration-300',
                            'bg-gradient-to-br from-secondary/30 to-secondary/10 backdrop-blur-sm',
                            isDragging
                              ? 'border-primary/50 bg-primary/5 scale-[1.02]'
                              : 'border-border/50 hover:border-primary/30 hover:bg-primary/5',
                            'cursor-pointer group'
                          )}
                          onDragEnter={handleDragEnter}
                          onDragOver={handleDragOver}
                          onDragLeave={handleDragLeave}
                          onDrop={handleDrop}
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <div className="text-center space-y-3">
                            <div className="mx-auto h-10 w-10 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center group-hover:scale-110 transition-transform">
                              <Upload className="h-5 w-5 text-primary" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-foreground">
                                Drop images here or click to browse
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                JPG, PNG files only
                              </p>
                            </div>
                          </div>

                          {/* Drag overlay */}
                          {isDragging && (
                            <div className="absolute inset-0 bg-primary/10 rounded-xl flex items-center justify-center">
                              <div className="text-center">
                                <ImageIcon className="h-6 w-6 text-primary mx-auto mb-2" />
                                <p className="text-sm font-medium text-primary">Drop images here</p>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Existing Attachments */}
                        {(existingAttachments.length > 0 || uploadQueue.length > 0) && (
                          <div className="space-y-3">
                            <p className="text-sm font-medium text-foreground">Uploaded Images:</p>
                            <div className="flex flex-wrap gap-3">
                              {/* Upload indicators */}
                              {uploadQueue.map((filename, idx) => (
                                <div key={`${filename}-${idx}`} className="relative">
                                  <div className="w-14 h-14 rounded-lg border border-border overflow-hidden bg-muted flex items-center justify-center">
                                    <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                                  </div>
                                </div>
                              ))}

                              {/* Existing attachments */}
                              {existingAttachments.map((attachment, index) => (
                                <div key={attachment.url || index} className="relative group">
                                  <div className="w-14 h-14 rounded-lg border border-border overflow-hidden">
                                    {attachment.contentType?.startsWith('image/') ? (
                                      <Image
                                        src={attachment.url || ''}
                                        alt={attachment.name || 'Attachment'}
                                        width={56}
                                        height={56}
                                        className="object-cover w-full h-full"
                                        unoptimized={true}
                                      />
                                    ) : (
                                      <div className="w-full h-full flex items-center justify-center bg-muted">
                                        <span className="text-xs">{(attachment.name || 'file').split('.').pop()}</span>
                                      </div>
                                    )}
                                  </div>
                                  <button
                                    className="absolute -top-1 -right-1 bg-destructive text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity shadow-sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onAttachmentsChange(existingAttachments.filter((_, i) => i !== index));
                                    }}
                                    aria-label="Remove attachment"
                                  >
                                    <XIcon className="w-3 h-3" />
                                  </button>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <Button
                    variant="outline"
                    onClick={onClose}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleContinue}
                    className="flex-1 bg-primary hover:bg-primary/90"
                  >
                    Build My App
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
