'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Database, ShieldCheck, RefreshCcw, BarChart3, Lock, Users, Smartphone, Check, Upload } from 'lucide-react';

export function BentoShowcase() {
  return (
    <section className="py-16 px-4 md:py-24 w-full max-w-7xl mx-auto backdrop-blur-md">
      <div className="text-center mb-12">
        <motion.h2 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-3xl md:text-4xl font-bold mb-4"
        >
          Turn your ideas into reality
        </motion.h2>
        <motion.p 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
          className="text-muted-foreground max-w-2xl mx-auto"
        >
          From concept to working app in minutes, not days. magically creates fully functional apps from your ideas.
        </motion.p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        {/* Box 1: Main Feature - Instant App Generation */}
        <motion.div 
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className={cn(
            "md:col-span-2 md:row-span-2 rounded-3xl overflow-hidden",
            "bg-gradient-to-br from-primary/10 to-primary/5",
            "border border-white/10 shadow-lg shadow-primary/5 p-6 md:p-8",
            "backdrop-blur-lg flex flex-col justify-between relative"
          )}
        >
          <div className="mb-6 md:mb-0">
            <h3 className="text-2xl font-semibold mb-3">Instant App Generation</h3>
            <p className="text-muted-foreground mb-4">
              Describe your app idea in plain language and watch as magically creates a fully functional mobile app in minutes.
            </p>
          </div>
          <div className="relative h-64 md:h-80 w-full rounded-xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent z-10"></div>
            <div className="relative z-20 h-full w-full">
              <div className="absolute bottom-4 left-4 right-4 bg-background/80 backdrop-blur-sm rounded-lg p-4 border border-border">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
                    <span className="text-lg">✨</span>
                  </div>
                  <div className="ml-3">
                    <h4 className="font-medium text-sm">Generated in 2 minutes</h4>
                    <p className="text-xs text-muted-foreground">From idea to working prototype</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="absolute inset-0">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20"></div>
              <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
                <video 
                  className="w-full h-full object-cover rounded-xl"
                  src="/videos/personal-finance.mp4"
                  autoPlay
                  muted
                  loop
                  playsInline
                  disablePictureInPicture
                  disableRemotePlayback
                  controlsList="nodownload noremoteplayback"
                ></video>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Box 2: Backend Integration */}
        <motion.div 
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
          className={cn(
            "rounded-3xl overflow-hidden",
            "bg-gradient-to-br from-indigo-500/15 to-indigo-500/5",
            "border border-white/10 shadow-lg shadow-indigo-500/10 p-6",
            "backdrop-blur-lg flex flex-col justify-between relative"
          )}
        >
          <div>
            <h3 className="text-xl font-semibold mb-2">Backend Integration</h3>
            <p className="text-muted-foreground text-sm">
              Connect to databases, APIs, and third-party services. Real data, real functionality.
            </p>
          </div>
          <div className="mt-4 flex items-center justify-center space-x-3">
            <div className="h-12 w-12 rounded-xl bg-indigo-500/20 flex items-center justify-center">
              <Database className="h-6 w-6 text-indigo-400" />
            </div>
            <div className="h-0.5 w-6 bg-indigo-400/30"></div>
            <div className="h-12 w-8 rounded-lg bg-background/50 flex items-center justify-center">
              <Smartphone className="h-6 w-6 text-white/80" />
            </div>
          </div>
        </motion.div>

        {/* Box 3: Built-in Authentication */}
        <motion.div 
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
          className={cn(
            "rounded-3xl overflow-hidden",
            "bg-gradient-to-br from-teal-500/20 to-teal-500/5",
            "border border-white/10 shadow-lg shadow-teal-500/10 p-6",
            "backdrop-blur-lg flex flex-col justify-between relative"
          )}
        >
          <div>
            <h3 className="text-xl font-semibold mb-2">Built-in Authentication</h3>
            <p className="text-muted-foreground text-sm">
              User login, registration, and security handled automatically. Focus on your business logic.
            </p>
          </div>
          <div className="mt-4 flex items-center justify-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-background/50 border border-white/10 flex items-center justify-center">
              <Users className="h-5 w-5 text-teal-300" />
            </div>
            <div className="h-10 w-10 rounded-full bg-background/50 border border-white/10 flex items-center justify-center">
              <Lock className="h-5 w-5 text-teal-300" />
            </div>
            <div className="h-10 w-10 rounded-full bg-background/50 border border-white/10 flex items-center justify-center">
              <ShieldCheck className="h-5 w-5 text-teal-300" />
            </div>
          </div>
        </motion.div>

        {/* Box 4: Live Updates (OTA) */}
        <motion.div 
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className={cn(
            "rounded-3xl overflow-hidden",
            "bg-gradient-to-br from-purple-500/15 to-purple-500/5",
            "border border-white/10 shadow-lg shadow-purple-500/10 p-6",
            "backdrop-blur-lg flex flex-col justify-between relative"
          )}
        >
          <div>
            <h3 className="text-xl font-semibold mb-2">Live Updates (OTA)</h3>
            <p className="text-muted-foreground text-sm">
              Push updates instantly without app store approval. Fix bugs and add features in real-time.
            </p>
          </div>
          <div className="mt-4 flex items-center justify-center">
            <div className="relative">
              <div className="h-12 w-8 rounded-lg bg-purple-500/20 flex items-center justify-center">
                <Smartphone className="h-6 w-6 text-white/80" />
              </div>
              <div className="absolute -right-1 -bottom-1 h-6 w-6 rounded-full bg-purple-500/30 flex items-center justify-center">
                <RefreshCcw className="h-3 w-3 text-white/90 animate-spin-slow" />
              </div>
            </div>
            <div className="ml-3 h-1 w-16 bg-purple-500/20 rounded-full overflow-hidden">
              <motion.div 
                className="h-full bg-purple-500/60 rounded-full"
                initial={{ width: '0%' }}
                whileInView={{ width: '100%' }}
                transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 0.5 }}
                viewport={{ once: false }}
              ></motion.div>
            </div>
          </div>
        </motion.div>
        
        {/* Box 5: Analytics & Compliance */}
        <motion.div 
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
          className={cn(
            "rounded-3xl overflow-hidden",
            "bg-gradient-to-br from-green-500/15 to-green-500/5",
            "border border-white/10 shadow-lg shadow-green-500/10 p-6",
            "backdrop-blur-lg flex flex-col justify-between relative"
          )}
        >
          <div>
            <h3 className="text-xl font-semibold mb-2">Analytics & Compliance</h3>
            <p className="text-muted-foreground text-sm">
              Built-in user tracking and automatic compliance with app store requirements.
            </p>
          </div>
          <div className="mt-4 flex items-center justify-between">
            <div className="h-10 w-10 rounded-lg bg-green-500/20 flex items-center justify-center">
              <BarChart3 className="h-6 w-6 text-green-400" />
            </div>
            <div className="h-8 w-8 rounded-full bg-green-500/20 border border-white/10 flex items-center justify-center">
              <Check className="h-5 w-5 text-green-300" />
            </div>
          </div>
        </motion.div>
        
        {/* Box 6: One-Click Deploy */}
        <motion.div 
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          viewport={{ once: true }}
          className={cn(
            "rounded-3xl overflow-hidden",
            "bg-gradient-to-br from-amber-500/15 to-amber-500/5",
            "border border-white/10 shadow-lg shadow-amber-500/10 p-6",
            "backdrop-blur-lg flex flex-col justify-between relative"
          )}
        >
          <div>
            <h3 className="text-xl font-semibold mb-2">One-Click Deploy</h3>
            <p className="text-muted-foreground text-sm">
              Deploy your app to the web with a single click and share with the world.
            </p>
          </div>
          <div className="mt-4 flex items-center">
            <div className="h-8 w-8 rounded-full bg-amber-500/20 flex items-center justify-center">
              <Upload className="h-4 w-4 text-amber-300" />
            </div>
            <div className="h-1 flex-1 bg-amber-500/20 ml-2 rounded-full overflow-hidden">
              <motion.div 
                className="h-full bg-amber-500/60 rounded-full"
                initial={{ width: '0%' }}
                whileInView={{ width: '75%' }}
                transition={{ duration: 1.5 }}
                viewport={{ once: true }}
              ></motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
