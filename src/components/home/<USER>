'use client';

import { useState, useEffect } from 'react';
import { Wand2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";

interface CreatingProjectDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  // Flag to indicate if the dialog is in a processing state and shouldn't be closed
  isProcessing?: boolean;
}

export function CreatingProjectDialog({ isOpen, onOpenChange, isProcessing = true }: CreatingProjectDialogProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [tipIndex, setTipIndex] = useState(0);
  
  const steps = [
    "Analyzing your idea...",
    "Designing app architecture...",
    "Setting up project...",
    "Almost ready..."
  ];
  
  const tips = [
    "You can customize your app's colors in the settings",
    "Use the chat to ask for UI improvements",
    "Export your code anytime from the project settings",
    "Share your project with teammates using the share button"
  ];
  
  // Effect for UI animations only
  useEffect(() => {
    if (!isOpen) {
      // Reset state when dialog closes
      setCurrentStep(0);
      setTipIndex(0);
      return;
    }
    
    // Rotate through tips
    const tipInterval = setInterval(() => {
      setTipIndex(prev => (prev + 1) % tips.length);
    }, 4000);
    
    // Simulate progress with timed step changes
    const stepInterval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < steps.length - 1) return prev + 1;
        return prev;
      });
    }, 2000);
    
    // Cleanup function
    return () => {
      clearInterval(tipInterval);
      clearInterval(stepInterval);
    };
  }, [isOpen, tips.length, steps.length]);
  
  // Custom handler to prevent closing when processing
  const handleOpenChange = (open: boolean) => {
    // Only allow closing if not in processing state
    if (!open && isProcessing) {
      return; // Prevent closing
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md" disableCloseButton={isProcessing}>
        <div className="flex flex-col items-center justify-center p-4">
          <div className="max-w-md w-full space-y-8">
            <div className="space-y-4 text-center">
              <div className="inline-block relative">
                <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping"></div>
                <div className="relative z-10 rounded-full bg-primary p-4">
                  <Wand2 className="h-8 w-8 text-white" />
                </div>
              </div>
              
              <h2 className="text-2xl font-bold">Creating Your Project</h2>
              <p className="text-muted-foreground">We're turning your idea into reality</p>
            </div>
            
            <div className="space-y-6">
              {steps.map((step, index) => (
                <div 
                  key={index} 
                  className={`flex items-center space-x-3 transition-all duration-500 ${
                    index <= currentStep ? 'opacity-100' : 'opacity-30'
                  }`}
                >
                  <div className={`h-6 w-6 rounded-full flex items-center justify-center ${
                    index < currentStep ? 'bg-green-500' : 
                    index === currentStep ? 'bg-primary animate-pulse' : 'bg-gray-300'
                  }`}>
                    {index < currentStep && (
                      <svg className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    )}
                  </div>
                  <span className="text-sm">{step}</span>
                </div>
              ))}
            </div>
            
            <div className="h-24 flex items-center justify-center">
              <div className="transition-all duration-1000 ease-in-out">
                <p className="text-sm italic text-center">
                  <span className="text-primary font-medium">Tip:</span> {tips[tipIndex]}
                </p>
              </div>
            </div>
            
            <div className="flex justify-center">
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div 
                  className="h-full bg-primary transition-all duration-500 ease-out"
                  style={{ 
                    width: `${Math.min(((currentStep + 1) / steps.length) * 100, 95)}%` 
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
