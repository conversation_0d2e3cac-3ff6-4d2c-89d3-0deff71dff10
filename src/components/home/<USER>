'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { Star, ExternalLink, Users, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

// Simplified Project-like interface based on src/lib/db/schema.ts Project type
interface CommunityApp {
  id: string;
  appName: string | null;
  description?: string | null;
  icon?: string | null; // URL to app icon
  slug: string; // URL-friendly identifier for a potential link
  primaryColor?: string; // Primary brand color for theming the card
  creatorName?: string;
  rating?: number;
  category?: string;
  imageUrl?: string; // A representative image for the app
  reviews?: number;
  color?: string;
  borderColor?: string;
}

// Sample data for 6 community apps
const sampleCommunityAppsData: CommunityApp[] = [
  {
    id: '1',
    appName: 'Zenith Focus Timer',
    description: 'Boost productivity with this sleek Pomodoro timer and task manager, designed for deep work.',
    icon: '/images/showcase/icons/zenith-timer.svg',
    slug: 'zenith-timer',
    primaryColor: 'bg-gradient-to-br from-purple-600/30 to-indigo-600/30',
    creatorName: 'Elena K.',
    rating: 4.9,
    category: 'Productivity',
    imageUrl: '/images/showcase/app-previews/zenith-preview.jpg',
  },
  {
    id: '2',
    appName: 'EcoSnap Challenge',
    description: 'Join daily environmental challenges, share your eco-friendly actions, and inspire others.',
    icon: '/images/showcase/icons/ecosnap.svg',
    slug: 'ecosnap-challenge',
    primaryColor: 'bg-gradient-to-br from-green-600/30 to-teal-600/30',
    creatorName: 'Marcus L.',
  },
  {
    id: '3',
    appName: 'NomadTravel',
    icon: '/images/showcase/nomad-app.png',
    description: 'Travel companion with local experiences and AI itinerary planning.',
    creatorName: 'Maya Johnson',
    category: 'Travel',
    rating: 4.7,
    reviews: 189,
    color: 'from-amber-500/10 to-orange-500/10',
    borderColor: 'border-amber-500/20',
    slug: 'nomad-travel',
  },
  {
    id: '4',
    appName: 'EcoTrack',
    icon: '/images/showcase/eco-app.png',
    description: 'Carbon footprint tracker with personalized sustainability tips.',
    creatorName: 'Liam Parker',
    category: 'Environment',
    rating: 4.6,
    reviews: 152,
    color: 'from-green-500/10 to-lime-500/10',
    borderColor: 'border-green-500/20',
    slug: 'eco-track',
  },
  {
    id: '5',
    appName: 'Artify Creator',
    icon: '/images/showcase/artify-app.png',
    description: 'AI-powered art generation and creative community platform.',
    creatorName: 'Chloe Davis',
    category: 'Art & Design',
    rating: 4.5,
    reviews: 98,
    color: 'from-pink-500/10 to-rose-500/10',
    borderColor: 'border-pink-500/20',
    slug: 'artify-creator',
  },
  {
    id: '6',
    appName: 'BudgetWise Pro',
    icon: '/images/showcase/budgetwise-app.png',
    description: 'Personal finance manager with smart insights and goal tracking.',
    creatorName: 'Ethan Miller',
    category: 'Finance',
    rating: 4.7,
    reviews: 150,
    color: 'from-indigo-500/10 to-violet-500/10',
    borderColor: 'border-indigo-500/20',
    slug: 'budgetwise-pro',
  },
];

const AppCardSkeleton = () => (
  <div className="bg-background/70 backdrop-blur-md p-6 rounded-2xl shadow-lg border border-white/10 animate-pulse">
    <Skeleton className="h-40 w-full rounded-lg mb-4 bg-muted/30" />
    <Skeleton className="h-6 w-3/4 mb-2 bg-muted/30" />
    <Skeleton className="h-4 w-1/2 mb-1 bg-muted/30" />
    <Skeleton className="h-4 w-full mb-3 bg-muted/30" />
    <Skeleton className="h-4 w-5/6 mb-4 bg-muted/30" />
    <div className="flex items-center justify-between">
      <Skeleton className="h-8 w-1/3 bg-muted/30" />
      <Skeleton className="h-6 w-1/4 bg-muted/30" />
    </div>
  </div>
);

const AppCard = ({ app }: { app: CommunityApp }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className={cn(
      "bg-background/40 backdrop-blur-xl p-4 md:p-6 rounded-2xl shadow-lg hover:shadow-primary/30 transition-all duration-300",
      "border", app.borderColor || "border-white/20",
      "hover:scale-[1.02] transform"
    )}
  >
    <div className={cn("relative h-40 w-full rounded-lg mb-4 overflow-hidden", app.color || "bg-muted/30")}>
      {app.icon ? (
        <Image src={app.icon} alt={app.appName || 'App Icon'} layout="fill" objectFit="cover" className="rounded-md" />
      ) : (
        <div className="flex items-center justify-center h-full bg-muted/20 rounded-md">
          <Zap className="h-16 w-16 text-muted-foreground/50" />
        </div>
      )}
       <div className={cn("absolute inset-0 bg-gradient-to-t", app.color, "opacity-30 mix-blend-multiply rounded-md")}></div>
    </div>
    <h3 className="text-lg md:text-xl font-semibold mb-1 truncate text-white">{app.appName || 'Untitled App'}</h3>
    {app.category && (
      <p className="text-xs text-primary mb-2 font-medium tracking-wide uppercase">{app.category}</p>
    )}
    <p className="text-sm text-muted-foreground mb-4 h-12 overflow-hidden text-ellipsis">
      {app.description || 'No description available.'}
    </p>
    <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t border-white/5">
      <span className="truncate">By {app.creatorName || 'Anonymous'}</span>
      {app.rating && (
        <div className="flex items-center shrink-0">
          <Star className="h-3 w-3 fill-amber-400 text-amber-400 mr-1" />
          <span>{app.rating.toFixed(1)} ({app.reviews || 0})</span>
        </div>
      )}
    </div>
  </motion.div>
);

export function CommunityShowcase() {
  const [isLoading, setIsLoading] = useState(true);
  const [apps, setApps] = useState<CommunityApp[]>([]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setApps(sampleCommunityAppsData.slice(0, 6)); // Ensure only 6 apps are shown
      setIsLoading(false);
      console.log('Strartin')
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <section className="py-16 md:py-24 w-full bg-white/5 backdrop-blur-lg relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4 text-white"
          >
            Built by the Community. Real apps. Real users. Real revenue.
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-muted-foreground max-w-2xl mx-auto text-base md:text-lg"
          >
            Discover incredible apps created by our talented community using Magically.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {isLoading
            ? Array.from({ length: 6 }).map((_, index) => (
                <AppCardSkeleton key={index} />
              ))
            : apps.map((app) => (
                <AppCard key={app.id} app={app} />
              ))}
        </div>
      </div>
    </section>
  );
}