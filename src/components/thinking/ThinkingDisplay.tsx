'use client';

import React, { useState, useEffect } from 'react';
import { ThinkingSection, ThinkingItem } from '@/lib/parser/ThinkingParser';
import { Card } from '@/components/ui/card';
import { ThinkingStatus } from '@/hooks/use-thinking-parser';
import { ChevronDown, ChevronUp, LightbulbIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import {Markdown} from "@/components/base/markdown";

interface ThinkingDisplayProps {
  thinkingStatus: ThinkingStatus;
  className?: string;
}

// Use React.memo to prevent unnecessary re-renders
export const ThinkingDisplay = React.memo(({ thinkingStatus, className }: ThinkingDisplayProps) => {
  const { isActive, isComplete, content } = thinkingStatus;
  // Use useRef to maintain state across re-renders
  const [isExpanded, setIsExpanded] = useState(true);
  const isExpandedRef = React.useRef(isExpanded);
  
  // Update the ref when state changes
  useEffect(() => {
    isExpandedRef.current = isExpanded;
  }, [isExpanded]);
  
  // Auto-expand when thinking is complete
  useEffect(() => {
    if (isComplete && content && !isExpandedRef.current) {
      setIsExpanded(false);
    }
  }, [isComplete, content]);

  // Auto-open when thinking is active
  useEffect(() => {
    if (isActive && content && isExpandedRef.current) {
      setIsExpanded(true);
    }
  }, [isActive, content]);

  // Create a stable onClick handler
  const handleToggle = React.useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  if (!isActive || !content) {
    return null;
  }
  


  return (
    <Card 
      className={cn(
        "backdrop-blur-md bg-black/40 border-zinc-800/50 text-zinc-100 mb-4 overflow-hidden",
        "rounded-xl shadow-lg",
        className
      )}
    >
      <button 
        className="flex items-center p-3 cursor-pointer w-full text-left" 
        onClick={handleToggle}
        type="button"
      >
        <LightbulbIcon className="h-4 w-4 mr-2 text-zinc-400" />
        <span className="text-xs font-medium text-zinc-300 flex-1">
          Plan
        </span>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4 text-zinc-400" />
        ) : (
          <ChevronDown className="h-4 w-4 text-zinc-400" />
        )}
      </button>

      {isExpanded && (
        <div className="px-4 pb-4">
          <div className="text-zinc-300 whitespace-pre-wrap leading-normal px-4 text-xs">
            {/*<Markdown>{content}</Markdown>*/}
            {content}
          </div>
        </div>
      )}
    </Card>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.thinkingStatus.isActive === nextProps.thinkingStatus.isActive &&
    prevProps.thinkingStatus.isComplete === nextProps.thinkingStatus.isComplete &&
    prevProps.thinkingStatus.content === nextProps.thinkingStatus.content
  );
});

interface ThinkingSectionDisplayProps {
  section: ThinkingSection;
  index: number;
}

const ThinkingSectionDisplay = ({ section, index }: ThinkingSectionDisplayProps) => {
  const { title, items } = section;

  return (
    <div className="border-l border-zinc-700/50 pl-4 py-1">
      <h4 className="text-sm font-medium text-zinc-200 mb-2">
        {index}. {title}
      </h4>
      
      {items && items.length > 0 && (
        <div className="space-y-2">
          {items.map((item, idx) => (
            <ThinkingItemDisplay key={idx} item={item} />
          ))}
        </div>
      )}
    </div>
  );
};

interface ThinkingItemDisplayProps {
  item: ThinkingItem;
}

const ThinkingItemDisplay = ({ item }: ThinkingItemDisplayProps) => {
  const { key, value } = item;

  return (
    <div className="flex items-start ml-2">
      <div className="w-1 h-1 rounded-full bg-zinc-600 mt-2 mr-2"></div>
      <div>
        <span className="font-medium text-zinc-400">{key}:</span>{' '}
        <span className="text-zinc-300">{value}</span>
      </div>
    </div>
  );
};

export default ThinkingDisplay;
