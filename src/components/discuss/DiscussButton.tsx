import React, {useState} from 'react';
import {Button} from "@/components/ui/button";
import {MessageSquare} from "lucide-react";
import {DiscussWithAI} from './DiscussWithAI';
import {useStores} from '@/stores/utils/useStores';
import {cn} from "@/lib/utils";

interface DiscussButtonProps {
    className?: string;
    initialMessage?: string;
    type?: 'error-fix' | 'code-review' | 'general-discussion';
    chatId: string;
    projectId: string;
}

export const DiscussButton = ({
                                  className = '',
                                  initialMessage = '',
                                  type = 'general-discussion',
                                  chatId,
                                  projectId
                              }: DiscussButtonProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const {generatorStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);

    const handleApplySolution = (solution: string) => {
        if (session) {
            // Set the solution as the current message in the main chat
            session.setCurrentMessage(solution);
        }
    };

    return (
        <>
            <div className="relative inline-block">
                <Button
                    variant="secondary"
                    size="sm"
                    className={cn("rounded-full shadow-md", className)}
                    onClick={() => setIsOpen(true)}
                >
                    Plan with AI
                    <MessageSquare className="h-4 w-4 mr-2"/>
                </Button>
                <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-[10px] font-bold px-1.5 py-0.5 rounded-full">
                    NEW
                </div>
            </div>

            <DiscussWithAI
                isOpen={isOpen}
                chatId={chatId}
                projectId={projectId}
                onClose={() => setIsOpen(false)}
                initialMessage={initialMessage}
                type={type}
                onApplySolution={handleApplySolution}
                relevantFiles={session?.fileTree}
            />
        </>
    );
};
