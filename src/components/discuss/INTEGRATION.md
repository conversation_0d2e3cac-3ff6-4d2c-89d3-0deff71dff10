# Discuss with AI Integration Guide

This document explains how to integrate the "Discuss with AI" feature into the Magically.life application.

## Overview

The "Discuss with AI" feature provides a way for users to have quick discussions with AI about errors, code reviews, or general questions without disrupting their main conversation flow. It's designed to be a lightweight, contextual assistant that can be triggered from various parts of the application.

## Components

1. **DiscussWithAI** - The main dialog component that handles the discussion UI and logic
2. **DiscussButton** - A floating action button that can be placed anywhere in the app
3. **EnhancedErrorHandler** - A replacement for the current SnackErrorObserver that offers both direct fix and discussion options

## API

The feature uses a dedicated API endpoint at `/api/discuss` that doesn't save conversations to the database, making it lightweight and focused.

## Integration Steps

### 1. Replace the SnackErrorObserver with EnhancedErrorHandler

In your main application layout or wherever SnackErrorObserver is currently used:

```tsx
// Before
import { SnackErrorObserver } from '@/components/generator/SnackErrorObserver';

// After
import { EnhancedErrorHandler } from '@/components/generator/EnhancedErrorHandler';

// Replace
<SnackErrorObserver session={session} />

// With
<EnhancedErrorHandler session={session} />
```

### 2. Add the Discuss Button to Key Areas

Add the DiscussButton component to strategic locations in your application:

```tsx
import { DiscussButton } from '@/components/discuss';

// Example: Add to the header
<div className="header-actions">
  <DiscussButton />
</div>

// Example: Add to the code editor toolbar
<div className="editor-toolbar">
  <DiscussButton initialMessage={`I'm working on ${currentFile?.name}. Can you help me with...`} />
</div>

// Example: Add as a floating action button
<div className="fixed bottom-4 right-4 z-50">
  <DiscussButton className="shadow-lg" />
</div>
```

### 3. Add Context-Specific Discuss Buttons

You can create specialized discuss buttons for different contexts:

```tsx
// Code review button
<DiscussButton 
  initialMessage={`Can you review this code in ${currentFile?.name}?`}
  type="code-review" 
/>

// Error fix button
<DiscussButton 
  initialMessage={`I'm getting this error: ${errorMessage}`}
  type="error-fix" 
/>
```

## User Experience Flow

1. User encounters an error or has a question
2. They click on "Discuss with AI" or the error toast shows options
3. A dialog opens with a chat interface
4. User can have a focused discussion with AI about their issue
5. When satisfied, they can apply the solution to the main chat
6. The main AI can then implement the solution

## Benefits

- **Non-disruptive**: Users can get help without losing their main conversation context
- **Focused**: The discussion is targeted on a specific issue
- **Contextual**: The AI has access to relevant files and error information
- **Seamless**: Solutions can be easily transferred to the main chat

## Technical Notes

- The discuss API uses the same StreamService as the main chat but doesn't save to the database
- File context is sent to help the AI understand the code better
- The dialog is designed to be lightweight and fast-loading
