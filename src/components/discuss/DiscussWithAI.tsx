import React, {useState, useRef, useEffect} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {<PERSON>, <PERSON><PERSON><PERSON>, MessageSquare, Loader2} from "lucide-react";
import {<PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle} from "@/components/ui/dialog";
import {toast} from "sonner";
import {generateUUID} from "@/lib/utils";
import {FileNode} from "@/types/file";
import {useStores} from "@/stores/utils/useStores";
import {useChat} from 'ai/react';
import type {Message, Attachment} from 'ai';
import {Markdown} from "@/components/base/markdown";
import {MultimodalInput} from "@/components/base/multimodal-input";
import {observer} from "mobx-react-lite";

interface DiscussWithAIProps {
    isOpen: boolean;
    onClose: () => void;
    initialMessage?: string;
    type?: 'error-fix' | 'code-review' | 'general-discussion';
    onApplySolution?: (solution: string) => void;
    relevantFiles?: FileNode[];
    metadata?: Record<string, any>;
    chatId: string;
    projectId: string;
}

export const DiscussWithAI = observer(({
                                           isOpen,
                                           onClose,
                                           initialMessage = '',
                                           type = 'error-fix',
                                           onApplySolution,
                                           relevantFiles = [],
                                           metadata = {},
                                           chatId,
                                           projectId
                                       }: DiscussWithAIProps) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const {generatorStore, logStore} = useStores();
    // Get active session if chatId is provided
    const session = chatId ? generatorStore.getActiveSession(chatId) : null;
    // Generate a unique ID for this discussion
    const [discussionId] = useState(() => generateUUID());

    // Track if initial message has been set
    const initialMessageSetRef = useRef(false);

    // Format initial message based on type
    const getFormattedPrompt = (message: string) => {
        if (type === 'error-fix') {
            return `I encountered an error: ${message}

Please analyze this error carefully and help me fix it. Consider:
1. The specific error message and its root cause
2. Any related code that might be affected
3. Dependencies that might be missing or misconfigured
4. Similar patterns in the codebase that work correctly
5. Provide a detailed solution with specific code changes

Please provide a detailed solution.`;
        } else if (type === 'code-review') {
            return `I'd like your opinion on this code: ${message}

Please review this code and provide feedback on:
1. Code quality and best practices
2. Potential bugs or edge cases
3. Performance considerations
4. Readability and maintainability
5. Suggestions for improvement

Please be specific in your feedback.`;
        }
        return message;
    };

    // Get relevant files from the session if not provided
    const filesToSend = relevantFiles.length > 0 ? relevantFiles :
        (session?.fileTree || []);

    // State for attachments
    const [attachments, setAttachments] = useState<Attachment[]>([]);

    // Initialize chat without initial messages
    const {
        messages,
        input,
        setInput,
        handleSubmit,
        isLoading,
        append,
        stop
    } = useChat({
        id: discussionId,
        api: '/api/discuss',
        body: {
            files: filesToSend,
            type,
            metadata,
            logs: logStore.getLogs(chatId),
            projectId
        },
        onError: (error) => {
            console.error('Error in discussion:', error);
            toast.error('Failed to get a response. Please try again.');
        }
    });

    // Set initial message only once when dialog opens
    useEffect(() => {
        if (isOpen && initialMessage && !initialMessageSetRef.current && messages.length === 0) {
            // Mark that we've set the initial message
            initialMessageSetRef.current = true;

            // Add the initial message
            append({
                role: 'user',
                content: getFormattedPrompt(initialMessage),
                id: generateUUID()
            });

            // Clear the input
            setInput('');
        } else if (!isOpen) {
            // Reset the flag when the dialog closes
            initialMessageSetRef.current = false;
        }
    }, [isOpen, initialMessage, messages.length, append, setInput, getFormattedPrompt]);

    // Scroll to bottom of messages
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
    }, [messages]);


    const handleApplySolution = () => {
        // Get the last assistant message
        const lastAssistantMessage = [...messages].reverse().find(msg => msg.role === 'assistant');

        if (lastAssistantMessage && onApplySolution) {
            onApplySolution(lastAssistantMessage.content);
            onClose();
            toast.success('Solution applied to main chat!');
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        // Send message on Ctrl+Enter or Cmd+Enter
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            handleSubmit(e as any);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="sm:max-w-[800px] max-h-[90vh] flex flex-col p-2" disableCloseButton>
                <DialogHeader className="flex flex-row items-center justify-between">
                    <DialogTitle className="flex items-center gap-2">
                        <MessageSquare className="h-5 w-5"/>
                        {type === 'error-fix' ? 'Fix Error with AI' :
                            type === 'code-review' ? 'Code Review with AI' : 'Discuss with AI'}
                    </DialogTitle>
                    <Button variant="ghost" size="icon" onClick={onClose}>
                        <X className="h-4 w-4"/>
                    </Button>
                </DialogHeader>

                <div className="flex-1 overflow-y-auto p-4 space-y-4 min-h-[300px] border rounded-md">
                    {messages.length === 0 && (
                        <div className="text-center text-muted-foreground py-10">
                            <p>Start a discussion with AI to get help with your code.</p>
                            {type === 'error-fix' && (
                                <p className="text-sm mt-2">Describe the error you're experiencing in detail for better
                                    results.</p>
                            )}
                        </div>
                    )}

                    {messages.map((msg) => (
                        <div
                            key={msg.id}
                            className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                        >
                            <div
                                className={`p-3 rounded-lg ${
                                    msg.role === 'user'
                                        ? 'bg-primary-foreground text-[#111111] max-w-[80%]'
                                        : 'bg-muted max-w-full'
                                }`}
                            >
                                {msg.role === 'user' ? (
                                    <p className="whitespace-pre-wrap">{msg.content}</p>
                                ) : (
                                    <div className="text-sm markdown-content">
                                        <Markdown>{msg.content as string}</Markdown>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}

                    {isLoading && (
                        <div className="flex justify-center py-4">
                            <Loader2 className="h-6 w-6 animate-spin text-primary"/>
                        </div>
                    )}

                    <div ref={messagesEndRef}/>
                </div>

                <div className="flex flex-col gap-2">
                    <MultimodalInput
                        chatId={discussionId}
                        projectId={"discuss-" + discussionId}
                        input={input}
                        setInput={setInput}
                        handleSubmit={handleSubmit}
                        isLoading={isLoading}
                        stop={stop}
                        attachments={attachments}
                        setAttachments={setAttachments}
                        messages={messages}
                        inDesignMode={true}
                        setMessages={() => {
                        }}
                        append={append}
                        componentContexts={[]}
                        onRemoveComponentContext={() => {
                        }}
                        onClearComponentContexts={() => {
                        }}
                        onVisualSelectionClicked={() => {
                        }}
                        selectMode={false}
                    />

                    {messages.length > 0 && onApplySolution && (
                        <Button
                            onClick={handleApplySolution}
                            variant="default"
                            className="mt-2"
                        >
                            <Sparkles className="h-4 w-4 mr-2"/>
                            Apply Solution to Main Chat
                        </Button>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
});
