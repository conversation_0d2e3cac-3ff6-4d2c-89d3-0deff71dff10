import Form from 'next/form';

import { Input } from './ui/input';
import { Label } from './ui/label';

export function AuthForm({
  action,
  children,
  defaultEmail = '',
  isRegistration
}: {
  action: NonNullable<
    string | ((formData: FormData) => void | Promise<void>) | undefined
  >;
  children: React.ReactNode;
  defaultEmail?: string;
  isRegistration?: boolean;
}) {
  return (
      <Form action={action} className="flex flex-col gap-4 px-4 sm:px-16">
        {
          isRegistration ?
              <div className="flex flex-col gap-2">
                <Label
                    htmlFor="name"
                    className="text-zinc-600 font-normal dark:text-zinc-400"
                >
                  What should we call you?
                </Label>

                <Input
                    id="name"
                    name="name"
                    className="bg-muted text-md md:text-sm"
                    type="text"
                    placeholder="Your first name (Alex)"
                    autoComplete="first-name"
                    required
                    autoFocus
                />
              </div>:
              null
        }

        <div className="flex flex-col gap-2">
          <Label
              htmlFor="email"
              className="text-zinc-600 font-normal dark:text-zinc-400"
          >
            Email Address
          </Label>

          <Input
              id="email"
              name="email"
              className="bg-muted text-md md:text-sm"
              type="email"
              placeholder="<EMAIL>"
              autoComplete="email"
              required
              defaultValue={defaultEmail}
          />
        </div>

        <div className="flex flex-col gap-2">
          <Label
              htmlFor="password"
              className="text-zinc-600 font-normal dark:text-zinc-400"
          >
            Password
          </Label>

          <Input
              id="password"
              name="password"
              className="bg-muted text-md md:text-sm"
              type="password"
              required
          />
        </div>

        {children}
      </Form>
  );
}
