"use client";

import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { StoreContext } from '@/stores/utils/StoreContext';
import { Notification as NotificationType } from '@/stores/NotificationStore';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

const Notifications = observer(() => {
  const rootStore = useContext(StoreContext);
  if (!rootStore) return null;
  
  const { notificationStore } = rootStore;
  const { notifications, dismissNotification } = notificationStore;

  if (!notifications.length) return null;

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {notifications.map((notification: NotificationType) => (
        <NotificationItem 
          key={notification.id} 
          notification={notification} 
          onDismiss={dismissNotification} 
        />
      ))}
    </div>
  );
});

interface NotificationItemProps {
  notification: NotificationType;
  onDismiss: (id: string) => void;
}

const NotificationItem = ({ notification, onDismiss }: NotificationItemProps) => {
  const { id, title, message, type } = notification;

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-100 border-green-500 text-green-800';
      case 'error':
        return 'bg-red-100 border-red-500 text-red-800';
      case 'warning':
        return 'bg-yellow-100 border-yellow-500 text-yellow-800';
      default:
        return 'bg-blue-100 border-blue-500 text-blue-800';
    }
  };

  return (
    <div 
      className={cn(
        'rounded-md border-l-4 p-4 shadow-md animate-slide-in relative',
        getTypeStyles()
      )}
    >
      <div className="flex justify-between items-start">
        <div className="font-medium">{title}</div>
        <button 
          onClick={() => id && onDismiss(id)} 
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={16} />
        </button>
      </div>
      <div className="mt-1 text-sm">{message}</div>
    </div>
  );
};

export default Notifications;
