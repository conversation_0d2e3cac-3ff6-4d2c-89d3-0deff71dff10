import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { QRCodeSVG } from "qrcode.react";
import {Button} from "@/components/ui/button";

interface ExpoInstructionsDialogProps {
    previewUrl: string;
}

export function ExpoInstructionsDialog({ previewUrl }: ExpoInstructionsDialogProps) {
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button className="" size="xs" variant="secondary">
                    Test on your phone
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-2 h-2"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"/></svg>
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>View on Mobile with Expo Go</DialogTitle>
                </DialogHeader>
                <div className="space-y-8 py-4">
                    <div className="flex justify-center">
                        <div className="p-6 bg-muted/30 rounded-2xl backdrop-blur-sm w-64 relative group transition-all duration-200 hover:bg-muted/40">
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-2xl opacity-50 group-hover:opacity-70 transition-opacity duration-200"></div>
                            {/*<QRCode*/}
                            {/*    value={previewUrl || ''}*/}
                            {/*    size={256}*/}
                            {/*    style={{ height: "auto", maxWidth: "100%", width: "100%" }}*/}
                            {/*    viewBox={`0 0 256 256`}*/}
                            {/*    className="rounded-xl relative z-10"*/}
                            {/*/>*/}

                            <QRCodeSVG
                                value={previewUrl}
                                size={256}
                                style={{height: "auto", maxWidth: "100%", width: "100%"}}
                                level="H"
                                className="rounded-xl relative z-10"
                                fgColor="#000000"
                                bgColor="#FFFFFF"
                                includeMargin={true}
                                imageSettings={{
                                    src: "/logo.svg",
                                    height: 40,
                                    width: 40,
                                    excavate: true,
                                }}
                            />
                        </div>
                    </div>

                    <div className="bg-background/50 rounded-md p-3">
                        <p className="text-xs font-medium mb-1.5">Direct Expo Link</p>
                        <code className="text-[10px] text-muted-foreground block break-all whitespace-normal">
                            {previewUrl}
                        </code>
                    </div>

                    <div className="space-y-6">
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">How to view:</h4>
                            <ol className="text-sm text-muted-foreground space-y-2 list-decimal pl-4">
                                <li>Download Expo Go from your device's app store</li>
                                <li>Open Expo Go on your mobile device</li>
                                <li>Tap "Scan QR Code" in Expo Go</li>
                                <li>Point your camera at the QR code</li>
                            </ol>
                        </div>

                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Download Expo Go:</h4>
                            <div className="grid grid-cols-2 gap-4">
                                <a
                                    href="https://play.google.com/store/apps/details?id=host.exp.exponent&hl=en_IN"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-muted/30 hover:bg-muted/50 transition-colors duration-200 rounded-xl p-3 flex items-center justify-center gap-2 group"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-green-500"><path d="M3.609 1.814L13.792 12 3.61 22.186a.996.996 0 0 1-.61-.92V2.734a1 1 0 0 1 .609-.92zm10.89 10.893l2.302 2.302-10.937 6.333 8.635-8.635zm3.799-3.8l-2.878 1.675-2.988-2.988 2.988-2.988 2.878 1.675a1 1 0 0 1 0 1.726zM5.865 2.658L16.802 8.99l-2.302 2.302-8.635-8.635z"/></svg>
                                    <span className="text-sm font-medium">Play Store</span>
                                </a>
                                <a
                                    href="https://itunes.apple.com/app/apple-store/id982107779"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-muted/30 hover:bg-muted/50 transition-colors duration-200 rounded-xl p-3 flex items-center justify-center gap-2 group"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.53 4.08zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z"/></svg>
                                    <span className="text-sm font-medium">App Store</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div className="rounded-xl bg-blue-500/5 p-4 text-xs text-blue-500/90 flex items-start gap-3 border border-blue-500/10">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 mt-0.5 shrink-0">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clipRule="evenodd" />
                        </svg>
                        <span>Expo Go allows you to test your app on a real device without complex setup or app store deployment.</span>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
