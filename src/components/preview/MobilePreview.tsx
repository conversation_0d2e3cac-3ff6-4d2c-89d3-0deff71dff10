import { SnackViewer } from "@/components/preview/SnackViewer";
import React, { useState } from "react";
import { observer } from "mobx-react-lite";
import { useContainerHeight } from "@/hooks/use-container-height";
import { useStores } from "@/stores/utils/useStores";
import { FileItem } from "@/types/file";
import { Wand2, ArrowLeft, Settings2, Share2, ArrowRight, Smartphone, QrCode } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { LinkButton } from '@/components/ui/link-button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import QRCode from 'react-qr-code';
import {ExpoInstructionsDialog} from "@/components/preview/ExpoInstructionsDialog";
import { ShareDialog } from "./ShareDialog";

interface MobilePreviewProps {
    chatId: string;
    files?: FileItem[];
    dependencies?: Record<string, any>;
}

export const MobilePreview = observer(({
                                           chatId,
                                           files,
                                           dependencies
                                       }: MobilePreviewProps) => {
    const {containerRef, containerHeight} = useContainerHeight();
    const {snackStore} = useStores();
    const previewUrl = snackStore.getSnackState(chatId)?.url || '';


    return (
        <div className="w-full h-full relative flex flex-col">
            {/* Fixed Header */}
            <motion.div 
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.3 }}
                className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-xl border-b border-border/50"
            >
                <div className="flex items-center justify-between px-4 h-14">
                    {/* Left Section */}
                    <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                            <div className="relative h-6 w-6">
                                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary to-primary-foreground" />
                                <div className="absolute inset-0.5 rounded-lg bg-background" />
                                <div className="absolute inset-1 rounded-md bg-gradient-to-br from-primary via-primary to-primary-foreground opacity-90" />
                                <Wand2 className="relative z-10 h-full w-full p-1 text-background" />
                            </div>
                            <span className="text-sm font-medium">magically</span>
                        </div>
                    </div>

                    {/* Right Section */}
                    <div className="flex items-center gap-2">
                        <ExpoInstructionsDialog previewUrl={previewUrl} />
                        <ShareDialog chatId={chatId} />
                    </div>
                </div>
            </motion.div>

            {/* Content with padding for header and footer */}
            <div className="pt-14 pb-20 w-full h-full flex-1 overflow-auto">
                <SnackViewer
                    chatId={chatId}
                    files={files}
                    dependencies={dependencies}
                />
            </div>

            {/* Fixed Footer */}
            <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.3 }}
                className="fixed bottom-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-xl border-t border-border/50 h-16"
            >
                <div className="flex items-center justify-between p-4">
                    {/* Left side branding */}
                    <div className="flex items-center gap-2">
                        <div className="relative h-6 w-6">
                            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary to-primary-foreground" />
                            <div className="absolute inset-0.5 rounded-lg bg-background" />
                            <div className="absolute inset-1 rounded-md bg-gradient-to-br from-primary via-primary to-primary-foreground opacity-90" />
                            <Wand2 className="relative z-10 h-full w-full p-1 text-background" />
                        </div>
                        <div className="flex flex-col">
                            <span className="text-sm font-medium">magically</span>
                            <span className="text-[8px]">Create beautiful mobile apps in minutes</span>
                        </div>
                    </div>

                    {/* Right side create button */}
                    <LinkButton
                        href="/"
                        className="w-auto"
                        target="_blank"
                        size="sm"
                    >
                        Create your own
                        <ArrowRight className="h-4 w-4" />
                    </LinkButton>
                </div>
            </motion.div>
        </div>
    )
});