"use client";
import {FC, useState} from 'react';
import useS<PERSON> from 'swr/immutable';
import {PreviewLayout} from "@/components/preview/PreviewLayout";
import {DEFAULT_DEPENDENCIES} from "@/types/editor";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import { fetcher } from '@/lib/utils';
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Share, Twitter, Linkedin, MessageCircle, Copy, MessageSquare } from 'lucide-react';
import { toast } from "sonner";
import { DeployButton } from "./DeployButton";
import {ShareDialog} from "@/components/preview/ShareDialog";

interface PreviewContentProps {
    chatId: string;
}


export const PreviewContent: FC<PreviewContentProps> = observer(({chatId}) => {
    console.log(`[PreviewContent] Rendering for chatId: ${chatId}`);

    const {snackStore} = useStores();
    const {data, error} = useSWR(`/api/chat/${chatId}/files`, fetcher, {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        onSuccess: (data) => {
            console.log(`[PreviewContent] Successfully fetched files for chatId: ${chatId}`, {
                filesCount: data.files?.length,
                hasDependencies: !!data.dependencies
            });
        },
        onError: (err) => {
            console.error(`[PreviewContent] Failed to fetch files for chatId: ${chatId}:`, err);
        }
    });

    if (error) {
        console.log(`[PreviewContent] Rendering error state for chatId: ${chatId}`, error);
        return (
            <div className="w-full h-screen flex items-center justify-center">
                <div className="text-red-600">Failed to load preview</div>
            </div>
        );
    }

    if (!data) {
        console.log(`[PreviewContent] Rendering loading state for chatId: ${chatId}`);
        return (
            <div className="w-full h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
        );
    }

    console.log(`[PreviewContent] Rendering success state for chatId: ${chatId}`, {
        filesCount: data.files?.length,
        hasDependencies: !!data.dependencies
    });

    return (
        <div className="w-full h-screen">
            <div className="relative w-full h-full">
                <div className="absolute top-4 right-4 gap-2 z-30 hidden md:flex">
                    {/*<DeployButton />*/}
                    <ShareDialog chatId={chatId} />
                </div>
                <PreviewLayout 
                    chatId={chatId}
                    files={data.files}
                    dependencies={data.dependencies || DEFAULT_DEPENDENCIES}
                />
            </div>
        </div>
    );
});
