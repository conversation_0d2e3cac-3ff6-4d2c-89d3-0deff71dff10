import React from 'react';
import {observer} from 'mobx-react-lite';
import {useStores} from '@/stores/utils/useStores';
import {PhoneFrame} from '@/components/generator/PhoneFrame';
import {useContainerHeight} from '@/hooks/use-container-height';
import {useIsMobile} from '@/hooks/use-mobile';
import {SnackViewer} from './SnackViewer';
import {FileItem} from '@/types/file';
import QRCode from 'react-qr-code';
import {DesktopPreview} from "@/components/preview/DesktopPreview";
import {MobilePreview} from "@/components/preview/MobilePreview";

interface PreviewLayoutProps {
    chatId: string;
    files?: FileItem[];
    dependencies?: Record<string, any>;
}

export const PreviewLayout: React.FC<PreviewLayoutProps> = observer(({
                                                                         chatId,
                                                                         files,
                                                                         dependencies
                                                                     }) => {
    const {containerRef, containerHeight} = useContainerHeight();
    const isMobile = useIsMobile();
    const {snackStore} = useStores();

    if (isMobile) {
        return (
            <MobilePreview
                chatId={chatId}
                files={files}
                dependencies={dependencies}/>
        );
    }

    return (
        <DesktopPreview
            chatId={chatId}
            files={files}
            dependencies={dependencies}
        />
    );
});
