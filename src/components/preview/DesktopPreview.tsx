import React, {useEffect} from 'react';
import { observer } from 'mobx-react-lite';
import { useContainerHeight } from '@/hooks/use-container-height';
import { PhoneFrame } from '@/components/generator/PhoneFrame';
import { SnackViewer } from './SnackViewer';
import { FileItem } from '@/types/file';
import { ExpoInstructionsDialog } from './ExpoInstructionsDialog';
import { useStores } from '@/stores/utils/useStores';
import { MagicallySection } from './MagicallySection';

interface DesktopPreviewProps {
    chatId: string;
    files?: FileItem[];
    dependencies?: Record<string, any>;
}

const InstructionsSection = observer(({ chatId }: { chatId: string }) => {
    const { snackStore } = useStores();
    const previewUrl = snackStore.getSnackState(chatId)?.url || '';
    return (
        <ExpoInstructionsDialog previewUrl={previewUrl} />
    )
});

export const DesktopPreview = observer(({
    chatId,
    files,
    dependencies
}: DesktopPreviewProps) => {
    const { containerRef, containerHeight } = useContainerHeight();
    const { snackStore } = useStores();
    const previewUrl = snackStore.getSnackState(chatId)?.url || '';

    return (
        <div ref={containerRef} className="w-full h-full flex gap-8 overflow-hidden">
            {/* Phone Preview */}
            <div className="p-4 w-[60%]">
                <PhoneFrame containerHeight={containerHeight}>
                    <SnackViewer
                        chatId={chatId}
                        files={files}
                        dependencies={dependencies}
                    />
                </PhoneFrame>
            </div>

            {/* Right Section */}
            <div className="flex flex-col justify-center py-8 pr-8 flex-1">
                {/* Top Section with Branding */}
                <div className="space-y-8">
                    {/* Magically Branding */}
                    <MagicallySection />

                    {/* View on Mobile Button */}
                    <div className="flex justify-start">
                        <InstructionsSection chatId={chatId}/>
                    </div>
                </div>
            </div>
        </div>
    );
});
