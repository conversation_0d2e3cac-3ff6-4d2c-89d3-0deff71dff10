import { motion } from 'framer-motion';
import {ArrowR<PERSON>, Wand2} from 'lucide-react';
import {LinkButton} from "@/components/ui/link-button";
import MagicallyLogo from "@/components/logo";

export function MagicallySection() {
    return (
        <div className="flex flex-col items-start space-y-4">
            {/* Logo and Title */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-4"
            >
                <MagicallyLogo/>
                <p className="text-muted-foreground">
                    Create beautiful mobile apps in minutes with AI
                </p>
            </motion.div>

            {/* Call to Action Buttons */}
            <div className="flex items-center gap-3">
                <LinkButton
                    href="/"
                    className="w-full"
                    target={"_blank"}
                >
                    Create your own app
                    <ArrowRight className="h-4 w-4"/>
                </LinkButton>
            </div>
        </div>
    );
}
