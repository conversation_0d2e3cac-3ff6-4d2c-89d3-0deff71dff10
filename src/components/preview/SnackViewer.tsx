"use client";
import React, { useRef, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { FileItem } from '@/types/file';
import { DEFAULT_DEPENDENCIES } from '@/types/editor';
import {useStores} from "@/stores/utils/useStores";

interface SnackViewerProps {
    chatId: string;
}

export const SnackViewer = observer(({ chatId, files, dependencies }: SnackViewerProps & { files?: FileItem[], dependencies?: Record<string, any> }) => {
    // console.log(`[SnackViewer] Rendering for chatId: ${chatId}`, {
    //     hasFiles: !!files?.length,
    //     hasDependencies: !!dependencies
    // });

    const { snackStore } = useStores();
    const webPreviewRef = useRef<HTMLIFrameElement>(null);
    const state = snackStore.getSnackState(chatId);
    const [isInitializing, setIsInitializing] = useState(true);

    useEffect(() => {
        let timeoutId: NodeJS.Timeout;
        let mounted = true;

        const initializeSnack = async () => {
            // Wait a bit for the iframe to be ready
            timeoutId = setTimeout(async () => {

                if (!webPreviewRef.current?.contentWindow) {
                    initializeSnack();
                    return;
                }

                try {
                    // console.log(`[SnackViewer] Initializing snack for chatId: ${chatId}`);
                    await snackStore.initSnack(
                        chatId,
                        files || [],
                        dependencies || DEFAULT_DEPENDENCIES,
                        {
                            current: webPreviewRef.current.contentWindow
                        },
                        false
                    );
                    // console.log(`[SnackViewer] Successfully initialized snack for chatId: ${chatId}`);

                    if (mounted) {
                        // console.log(`[SnackViewer] Setting isInitializing to false for chatId: ${chatId}`);
                        setIsInitializing(false);
                    }
                } catch (err) {
                    console.error(`[SnackViewer] Failed to initialize snack for chatId: ${chatId}:`, err);
                }
            }, 100); // Small delay to ensure iframe is ready
        };

        initializeSnack();

        return () => {
            mounted = false;
            clearTimeout(timeoutId);
            snackStore.cleanupSnack(chatId);
        };
    }, [chatId, snackStore, files, dependencies]);

    // Always render the iframe, but control its visibility
    return (
        <div className="w-full h-full relative bg-white overflow-hidden rounded-xl">
            {/* The iframe */}
            <iframe
                ref={webPreviewRef}
                className="w-full h-full border-none relative z-10"
                style={{
                    opacity: (!isInitializing && state && !state.isLoading) ? 1 : 0,
                    transition: 'opacity 0.3s ease'
                }}
                src={state?.webPreviewURL || undefined}
                allow="accelerometer; ambient-light-sensor; camera; encrypted-media; geolocation; gyroscope; magnetometer; microphone; usb; xr-spatial-tracking"
                sandbox="allow-forms allow-modals allow-pointer-lock allow-popups allow-presentation allow-same-origin allow-scripts"
            />

            {/* Loading state */}
            {(isInitializing || !state || state.isLoading) && (
                <div className="absolute inset-0 flex items-center justify-center bg-white z-20">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary" />
                </div>
            )}
            
            {/* Error state */}
            {state?.error && (
                <div className="absolute inset-0 flex items-center justify-center p-8 bg-red-50 z-20">
                    <div className="text-red-600">Error: {state.error}</div>
                </div>
            )}

        </div>
    );
});
