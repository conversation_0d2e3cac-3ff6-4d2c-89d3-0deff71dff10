"use client";

import { FC } from 'react';
import { Di<PERSON>, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Rocket, ExternalLink, MessagesSquare } from 'lucide-react';

interface GetCodeReviewProps {
    className?: string;
}

export const DeployButton: FC<GetCodeReviewProps> = ({ className }) => {
    const DISCORD_URL = 'https://discord.gg/Cpda56yVYY';
    const currentUrl = typeof window !== 'undefined' ? window.location.href : '';

    const handleJoinDiscord = () => {
        const message = encodeURIComponent(`Hey! I need help deploying my app: ${currentUrl}`);
        window.open(`${DISCORD_URL}?message=${message}`, '_blank');
    };

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button 
                    variant="outline" 
                    size="icon"
                    className={className}
                >
                    <Rocket className="h-4 w-4" />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <div className="p-6 space-y-6">
                    <div className="space-y-2 text-center">
                        <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
                            <Rocket className="h-6 w-6 text-primary" />
                        </div>
                        <h2 className="text-2xl font-semibold">Ready to Deploy?</h2>
                        <p className="text-sm text-muted-foreground">
                            Our team of experts will help you deploy your app to the web or app stores.
                        </p>
                    </div>

                    <div className="rounded-lg border bg-muted/50 p-4">
                        <div className="flex items-start gap-4">
                            <div className="mt-0.5">
                                <MessagesSquare className="h-5 w-5 text-primary" />
                            </div>
                            <div className="space-y-1">
                                <h3 className="text-sm font-medium">Get Deployment Support</h3>
                                <p className="text-sm text-muted-foreground">
                                    Join our Discord community where our experts will guide you through:
                                </p>
                                <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4 mt-2">
                                    <li>Setting up your development environment</li>
                                    <li>Deploying as a web application</li>
                                    <li>Publishing to App Store & Play Store</li>
                                    <li>Optimizing for production</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <Button 
                        className="w-full" 
                        onClick={handleJoinDiscord}
                    >
                        Join Discord Community
                        <ExternalLink className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};
