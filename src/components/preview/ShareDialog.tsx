import {FC} from "react";
import {toast} from "sonner";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTrigger} from "@/components/ui/dialog";
import {Button} from "@/components/ui/button";
import {Co<PERSON>, Linkedin, MessageCircle, MessageSquare, Share, Twitter} from "lucide-react";

export const ShareDialog: FC<{chatId: string}> = ({chatId}) => {
    const shareUrl = typeof window !== 'undefined' ? window.location.href : '';

    const handleShare = async (platform: string) => {
        switch (platform) {
            case 'twitter':
                window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}`, '_blank');
                break;
            case 'linkedin':
                window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`, '_blank');
                break;
            case 'discord':
                window.open(`https://discord.com/channels/@me?message=${encodeURIComponent(shareUrl)}`, '_blank');
                break;
            case 'whatsapp':
                window.open(`https://wa.me/?text=${encodeURIComponent(shareUrl)}`, '_blank');
                break;
            case 'clipboard':
                await navigator.clipboard.writeText(shareUrl);
                toast.success('URL copied to clipboard!');
                break;
        }
    };

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                    <Share className="h-4 w-4" />
                </Button>
            </DialogTrigger>
            <DialogContent>
                <div className="p-4">
                    <h2 className="text-lg font-semibold mb-2">Share Preview</h2>
                    <p className="text-sm text-muted-foreground mb-4">Share this preview with your team or on social media</p>
                    <div className="flex flex-wrap gap-4">
                        <Button variant="outline" size="icon" onClick={() => handleShare('twitter')}>
                            <Twitter className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" onClick={() => handleShare('linkedin')}>
                            <Linkedin className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" onClick={() => handleShare('discord')}>
                            <MessageCircle className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" onClick={() => handleShare('whatsapp')}>
                            <MessageSquare className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" onClick={() => handleShare('clipboard')}>
                            <Copy className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
