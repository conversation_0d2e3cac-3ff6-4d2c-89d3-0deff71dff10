'use client';

import {useState, useEffect} from 'react';
import {useSearchParams} from 'next/navigation';
import {toast} from 'sonner';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import Link from 'next/link';

export default function ContactForm() {
    const searchParams = useSearchParams();
    const [email, setEmail] = useState('');
    const [message, setMessage] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

    useEffect(() => {
        const plan = searchParams?.get('plan');
        const initialMessage = searchParams?.get('message');

        if (plan) {
            setSelectedPlan(plan);
        }

        if (initialMessage) {
            setMessage(decodeURIComponent(initialMessage));
        }
    }, [searchParams]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            const response = await fetch('/api/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({email, message}),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to send message');
            }

            toast.success('Message sent successfully! We\'ll get back to you soon.');
            setEmail('');
            setMessage('');
        } catch (error) {
            console.error('Contact form error:', error);
            toast.error('Failed to send message. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
            <div className="container max-w-4xl py-12 mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                        {selectedPlan ? `Get Started with ${selectedPlan}` : 'Contact Us'}
                    </h1>
                    <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                        {selectedPlan ?
                            'Tell us about your project and we\'ll help you get started.' :
                            'Have questions? Check out our '}
                        {!selectedPlan && (
                            <Link href="/faq" className="text-primary hover:underline">FAQ page</Link>
                        )}
                        {!selectedPlan && ' for quick answers to common questions.'}
                    </p>
                </div>

                <Card className="border-muted-foreground/20 backdrop-blur-sm bg-background/50">
                    <CardHeader>
                        <CardTitle>{selectedPlan ? 'Tell us about your needs' : 'Send us a message'}</CardTitle>
                        <CardDescription>
                            {selectedPlan ?
                                `We'll help you get started with the ${selectedPlan} plan.` :
                                'We\'ll get back to you as soon as possible.'}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <Input
                                    type="email"
                                    placeholder="Your email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                />
                            </div>
                            <div>
                                <Textarea
                                    placeholder="Your message"
                                    value={message}
                                    onChange={(e) => setMessage(e.target.value)}
                                    required
                                    rows={5}
                                />
                            </div>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Sending...' : 'Send Message'}
                            </Button>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
