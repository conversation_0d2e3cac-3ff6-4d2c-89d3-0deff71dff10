import {ChatRequestOptions, type CreateMessage, Attachment, TextPart} from 'ai';
import type { Message as BaseMessage } from 'ai';

// Extended Message type with additional properties needed for message grouping
interface Message extends BaseMessage {
  parentUserMessageId?: string | null;
  parentAssistantMessageId?: string | null;
  isAssistantGroupHead?: boolean;
  parts?: any[];
  toolInvocations?: any[];
  experimental_attachments?: any[];
  childMessages?: Message[];
  createdAt?: Date;
  restorationId?: string; // ID to use for checkpoint restoration
}
import {PreviewMessage, ThinkingMessage} from './message';
import {useScrollToBottom} from './use-scroll-to-bottom';
import React, {memo, useRef, useEffect, useState, useMemo} from 'react';
import {Vote} from '@/lib/db/schema';
import equal from 'fast-deep-equal';
import {ActionMeta} from "@/lib/parser/ActionsParser";

interface MessagesProps {
  chatId: string;
  projectId: string;
  isLoading: boolean;
  votes: Array<Vote> | undefined;
  messages: Array<Message>;
  setInput: (value: string) => void;
  setMessages: (
    messages: Message[] | ((messages: Message[]) => Message[]),
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
  isBlockVisible: boolean;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  append: (
      message: Message | CreateMessage,
      chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  setAttachments?: (attachments: Attachment[]) => void;
  onVersionClick: (messageId: string) => void;
  lastActiveVersionId?: string | null;
  onActionClick: (action: ActionMeta) => void;
  setSnackError: any;
  hasMoreMessages?: boolean;
  totalUserMessages?: number;
  onLoadPrevious?: () => void;
  isLoadingPrevious?: boolean;
  addToolResult?: (params: { toolCallId: string; result: any }) => void;
  removeActions: boolean;
}

const getLastMessage = (messages: Message[], role: 'assistant' | 'user') => {
  const lastMessageIndex = [...messages]
      .reverse()
      .findIndex(msg => msg.role === role);

  return lastMessageIndex !== -1
      ? messages[messages.length - 1 - lastMessageIndex]
      : null;
}

function PureMessages({
                        projectId,
  chatId,
  isLoading,
  votes,
  messages,
  setMessages,
    setInput,
  reload,
  isReadonly,
  isBlockVisible,
    status,
                        append,
                        setAttachments,
    onVersionClick,
    lastActiveVersionId,
    onActionClick,
                        setSnackError,
  hasMoreMessages,
  totalUserMessages,
  onLoadPrevious,
  isLoadingPrevious,
                        removeActions,
                        addToolResult
}: MessagesProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);

  // Process messages to combine assistant messages between user messages
  const processedMessages = useMemo(() => {
    // Skip processing if no messages
    if (!messages.length) return messages;

    // Create a map to store the group head messages by their ID
    const groupHeadMessages: Record<string, Message> = {};
    
    // Create a map to store child messages by their parent assistant ID
    const childMessagesByParent: Record<string, Message[]> = {};
    
    // First pass: identify all group head messages and collect child messages
    messages.forEach(message => {
      // For user messages, set restorationId to their own ID
      if (message.role === 'user') {
        message.restorationId = message.id;
        return;
      }
      
      // Store group head messages
      if (message.role === 'assistant' && message.isAssistantGroupHead) {
        groupHeadMessages[message.id] = { 
          ...message, 
          childMessages: [],
          restorationId: message.id // Default to own ID, will update later if there are children
        };
      }
      
      // Collect child messages by parent
      if (message.role === 'assistant' && !message.isAssistantGroupHead && message.parentAssistantMessageId) {
        if (!childMessagesByParent[message.parentAssistantMessageId]) {
          childMessagesByParent[message.parentAssistantMessageId] = [];
        }
        // Set restorationId to own ID for child messages
        const childMessage = { ...message, restorationId: message.id };
        childMessagesByParent[message.parentAssistantMessageId].push(childMessage);
      }
    });
    
    // Second pass: merge child messages into their parent group heads
    Object.keys(childMessagesByParent).forEach(parentId => {
      const parentMessage = groupHeadMessages[parentId];
      if (!parentMessage) return;

      // Sorting is not possible as createdAt is not saved
      const sortedChildMessages = [...childMessagesByParent[parentId]];
      
      // Store the child messages in the parent message in the exact sequence
      parentMessage.childMessages = sortedChildMessages;
      
      // Set the parent's restorationId to the latest child's ID for checkpoint restoration
      if (sortedChildMessages.length > 0) {
        const latestChild = sortedChildMessages[sortedChildMessages.length - 1];
        parentMessage.restorationId = latestChild.id;
      }
      
      // Initialize parts array if needed
      if (!parentMessage.parts) {
        parentMessage.parts = [];
        
        // If the parent has content but no parts, create a text part from the content
        if (typeof parentMessage.content === 'string' && parentMessage.content.trim()) {
          parentMessage.parts.push({
            type: 'text',
            text: parentMessage.content
          });
        }
      }
      
      // Initialize tool invocations array if needed
      if (!parentMessage.toolInvocations) {
        parentMessage.toolInvocations = [];
      }
      
      // Process all child messages for this parent
      sortedChildMessages.forEach(childMessage => {
        // Merge tool invocations
        if (childMessage.toolInvocations && childMessage.toolInvocations.length > 0 && parentMessage.toolInvocations) {
          parentMessage.toolInvocations = [
            ...parentMessage.toolInvocations,
            ...childMessage.toolInvocations
          ];
        }
        
        // Process parts by type
        if (childMessage.parts && childMessage.parts.length > 0) {
          // Only add text parts that come before any tool invocation parts
          const textParts: any[] = [];
          const toolInvocationParts: any[] = [];
          
          // Separate parts by type
          childMessage.parts.forEach(part => {
            if (part.type === 'tool-invocation') {
              toolInvocationParts.push(part);
            } else if (part.type === 'text') {
              textParts.push(part);
            } else {
              // Add any other part types directly
              if (parentMessage.parts) {
                parentMessage.parts.push(part);
              }
            }
          });
          
          // Add text parts first, then tool invocation parts
          if (textParts.length > 0 && parentMessage.parts) {
            parentMessage.parts = [...parentMessage.parts, ...textParts];
          }
          
          if (toolInvocationParts.length > 0 && parentMessage.parts) {
            parentMessage.parts = [...parentMessage.parts, ...toolInvocationParts];
          }
        }
        
        // Merge experimental_attachments if they exist
        if (childMessage.experimental_attachments?.length) {
          if (!parentMessage.experimental_attachments) {
            parentMessage.experimental_attachments = [];
          }
          parentMessage.experimental_attachments = [
            ...parentMessage.experimental_attachments,
            ...childMessage.experimental_attachments
          ];
        }
      });
      
      // Remove duplicate parts by type and content
      if (parentMessage.parts && parentMessage.parts.length > 0) {
        const uniqueParts: any[] = [];
        const seen = new Set();
        
        parentMessage.parts.forEach(part => {
          // Create a key based on type and content
          const key = `${part.type}:${part.type === 'text' ? part.text : JSON.stringify(part)}`;
          
          if (!seen.has(key)) {
            seen.add(key);
            uniqueParts.push(part);
          }
        });
        
        parentMessage.parts = uniqueParts;
      }
    });
    
    // Final pass: build the result array with user messages and merged assistant messages
    const result = messages.map(message => {
      // User messages pass through unchanged
      if (message.role === 'user') {
        return message;
      }
      
      // If this is a child assistant message, filter it out
      if (message.role === 'assistant' && !message.isAssistantGroupHead && message.parentAssistantMessageId) {
        // Return null to filter it out later
        return null;
      }
      
      // If this is a group head, return the merged version
      if (message.isAssistantGroupHead && groupHeadMessages[message.id]) {
        return groupHeadMessages[message.id];
      }
      
      // Default: return the message as is
      return message;
    }).filter(Boolean) as Message[];
    
    // Efficient single-pass deduplication using a Set
    // This prevents React's "Encountered two children with the same key" error
    const seenIds = new Set<string>();
    const deduplicatedResult: Message[] = [];
    
    // Use for-of loop for better performance with early termination
    for (const msg of result) {
      if (!seenIds.has(msg.id)) {
        seenIds.add(msg.id);
        deduplicatedResult.push(msg);
      }
      // No console.warn in production code for performance
    }
    
    return deduplicatedResult;
  }, [messages]);

  // Removed unnecessary console.log useEffect for performance

  /* Original section-based implementation (commented out)
  const processedMessagesOld = useMemo(() => {
    console.log('asdasd', JSON.parse(JSON.stringify([...messages])))
    if (!messages.length) return [];

    // Group messages into sections (user message followed by assistant messages)
    const messageSection: Message[] = [];
    const messageSections: Message[][] = [];

    const me = messages.slice(-10)
    console.log('messages.slice(-2)', JSON.parse(JSON.stringify(me)))
    // First, group messages into sections
    for (const message of [...me]) {
      const { role } = message;

      // When we encounter a user message and already have messages in the section,
      // push the current section and start a new one
      if (role === 'user' && messageSection.length > 0) {
        messageSections.push([...messageSection]);
        messageSection.length = 0;
      }

      messageSection.push(message);
    }

    // Add the last section if it has messages
    if (messageSection.length > 0) {
      messageSections.push([...messageSection]);
    }

    // Process each section to combine assistant messages
    const result: Message[] = [];

    for (const section of messageSections) {
      // First message in section should be a user message
      const userMessage = section[0];
      result.push(userMessage);

      // If there are assistant messages, combine them
      if (section.length > 1) {
        const assistantMessages = section.slice(1).filter(m => m.role === 'assistant');

        if (assistantMessages.length > 0) {
          // Use the first assistant message as the base
          const combinedMessage: Message = { ...assistantMessages[0] };
          console.log('combinedMessage', combinedMessage)
          // Combine tool invocations and parts from all assistant messages
          for (let i = 1; i < assistantMessages.length; i++) {
            const message = assistantMessages[i];

            // Combine tool invocations
            if (message.toolInvocations?.length) {
              if (!combinedMessage.toolInvocations) {
                combinedMessage.toolInvocations = [];
              }
              combinedMessage.toolInvocations = [
                ...combinedMessage.toolInvocations,
                ...message.toolInvocations
              ];
            }

            // Combine parts
            if (message.parts?.length) {
              if (!combinedMessage.parts) {
                combinedMessage.parts = [];
              }
              combinedMessage.parts = [
                ...combinedMessage.parts,
                ...message.parts
              ];
            }
          }

          // Add the combined message to the result
          result.push(combinedMessage);
        }
      }
    }

    return deduplicatedResult;
  }, [messages]);
  */
  
  // Find last assistant message
  const lastAssistantMessage = getLastMessage(processedMessages, 'assistant');

  // Find last user message
  const lastUserMessage = getLastMessage(processedMessages, 'user');

  const lastAssistantMessageContent = lastAssistantMessage?.content;

  // Scroll to bottom function
  const scrollToBottom = () => {
    if (bottomRef.current) {
      try {
        // Try to use scrollIntoView with smooth behavior first
        bottomRef.current.scrollIntoView({ behavior: 'auto', block: 'end' });

        // Also try direct scrolling to ensure it works
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight;
        }
      } catch (e) {
        // Fallback if scrollIntoView fails
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight;
        }
      }
    }
  };

  // Scroll to bottom when messages change or component mounts
  useEffect(() => {
    // Initial scroll
    scrollToBottom();

    // Multiple attempts to ensure scrolling works
    const timeouts: NodeJS.Timeout[] = [];
    for (let delay of [10, 50, 100, 200, 500, 1000]) {
      timeouts.push(setTimeout(scrollToBottom, delay));
    }

    return () => {
      timeouts.forEach(clearTimeout);
    };
  }, [messages.length, isLoading]);

  return (
    <div
      ref={containerRef}
      className="flex flex-col gap-6 flex-1 overflow-y-auto pt-4"
    >
      {/* Load All Messages button */}
      {hasMoreMessages && (
        <div className="flex justify-center mb-4 sticky top-0 z-10">
          <button
            onClick={onLoadPrevious}
            disabled={isLoadingPrevious}
            className="px-4 py-2 text-sm font-medium rounded-md bg-primary/10 hover:bg-primary/20 text-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoadingPrevious ? (
              <>
                <span className="animate-spin h-4 w-4 border-2 border-primary/50 border-t-primary rounded-full"></span>
                Loading all messages...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                {totalUserMessages && messages.length < totalUserMessages 
                  ? `Load All Messages (${messages.length}/${totalUserMessages})` 
                  : 'Load All Messages'}
              </>
            )}
          </button>
        </div>
      )}
      {/* Render processed messages that combine assistant messages */}
      {processedMessages.map((message, index) => (
        <PreviewMessage
          key={message.id}
          chatId={chatId}
          projectId={projectId}
          message={message}
          removeActions={removeActions}
          isLoading={isLoading && index === processedMessages.length - 1}
          vote={
            votes
              ? votes.find((vote) => vote.messageId === message.id)
              : undefined
          }
          setMessages={setMessages}
          addToolResult={addToolResult}
          reload={reload}
          setInput={setInput}
          isReadonly={isReadonly}
          isLastMessage={lastAssistantMessage?.id === message.id}
          isLastUserMessage={lastUserMessage?.id === message.id}
          status={status}
          setAttachments={setAttachments}
          append={append}
          onVersionClick={onVersionClick}
          isVersionActive={lastActiveVersionId === message.id}
          onActionClick={onActionClick}
          setSnackError={setSnackError}
        />
      ))}

      {isLoading && !lastAssistantMessageContent && (
        <div style={{ width: '100%', marginTop: '16px' }}>
          <ThinkingMessage />
        </div>
      )}

      {/* Invisible element at the bottom for scrolling */}
      <div
        ref={bottomRef}
        style={{
          height: '1px',
          width: '100%',
          marginTop: '16px',
          float: 'left',
          clear: 'both'
        }}
      />
    </div>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.isBlockVisible && nextProps.isBlockVisible) return true;
  if (prevProps.isLoading !== nextProps.isLoading) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.chatId && nextProps.chatId) return false;
  if (prevProps.projectId && nextProps.projectId) return false;
  if (prevProps.lastActiveVersionId && nextProps.lastActiveVersionId) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;

  return true;
});
