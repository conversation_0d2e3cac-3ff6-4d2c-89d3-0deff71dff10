// 'use client';
//
// import React, { memo } from 'react';
// import { cn } from '@/lib/utils';
// import { Loader2, CheckCircle } from 'lucide-react';
// import { GetFileContentsToolResult } from './get-file-contents-tool-result';
// import { QueryCodebaseToolResult } from './query-codebase-tool-result';
// import { EditFileToolResult } from './edit-file-tool-result';
// import { GetSupabaseInstructionsToolResult } from './get-supabase-instructions-tool-result';
// import { GetSupabaseLogsToolResult } from './get-supabase-logs-tool-result';
// // Define the InlineToolCall interface here since the import is not working
// interface InlineToolCall {
//   id: string;
//   name: string;
//   args: Record<string, any>;
//   state: 'loading' | 'complete' | 'error';
//   result?: any;
// }
//
// interface InlineToolCallProps {
//   toolCall: InlineToolCall;
//   className?: string;
// }
//
// export const InlineToolCallComponent = memo(function InlineToolCallComponent({
//   toolCall,
//   className
// }: InlineToolCallProps) {
//   const { name, args, state } = toolCall;
//
//   // Render different tool calls based on the tool name
//   const renderToolCall = () => {
//     switch (name) {
//       // case 'getFileContents':
//       //   return <GetFileContentsToolResult files={args.files} />;
//       // case 'queryCodebase':
//       //   return <QueryCodebaseToolResult query={args.query} excludedFiles={args.excludedFiles} state={state} />;
//       // case 'editFile':
//       //   return <EditFileToolResult absolutePath={args.absolutePath} state={state} />;
//       case 'getSupabaseInstructions':
//         return <GetSupabaseInstructionsToolResult reason={args.reason || 'Fetching Supabase schema'} state={state} />;
//       case 'getSupabaseLogs':
//         return <GetSupabaseLogsToolResult reason={args.reason || 'Fetching Supabase logs'} service={args.service} state={state} result={toolCall.result} />;
//       default:
//         return (
//           <div className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-muted/30 rounded-md">
//             {state === 'loading' ? (
//               <>
//                 <Loader2 className="h-3 w-3 animate-spin text-primary" />
//                 <span>Calling {name}...</span>
//               </>
//             ) : (
//               <>
//                 <CheckCircle className="h-3 w-3 text-green-600" />
//                 <span>{name} completed</span>
//               </>
//             )}
//           </div>
//         );
//     }
//   };
//
//   return (
//     <span className={cn("inline-tool-call", className)}>
//       {renderToolCall()}
//     </span>
//   );
// });
