import type { Attachment } from 'ai';
import { LoaderIcon, XIcon } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";

export const PreviewAttachment = ({
  attachment,
  isUploading = false,
  onRemove,
}: {
  attachment: Attachment;
  isUploading?: boolean;
  onRemove?: () => void;
}) => {
  const [showFullView, setShowFullView] = useState(false);
  const { name, url, contentType } = attachment;

  return (
    <>
      <div className="flex flex-col gap-2">
        <div 
          className="w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center cursor-pointer group"
          onClick={() => setShowFullView(true)}
        >
          {contentType ? (
            contentType.startsWith('image') ? (
              <Image
                key={url}
                src={url}
                alt={name ?? 'An image attachment'}
                className="rounded-md object-cover"
                fill
                sizes="(max-width: 768px) 100vw, 80px"
                priority={false}
                unoptimized={true}
              />
            ) : (
              <div className="" />
            )
          ) : (
            <div className="" />
          )}

          {isUploading && (
            <div className="animate-spin absolute text-zinc-500">
              <LoaderIcon />
            </div>
          )}

          {onRemove && (
            <button 
              className="absolute -top-0 -right-1.5 bg-destructive text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity shadow-sm z-10"
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
            >
              <XIcon className="w-3 h-3" />
            </button>
          )}
        </div>
        <div className="text-xs text-zinc-500 max-w-16 truncate">{name}</div>
      </div>

      {/* Full view dialog */}
      <Dialog open={showFullView} onOpenChange={setShowFullView}>
        <DialogContent className="sm:max-w-md">
          {contentType?.startsWith('image') ? (
            <div className="relative w-full h-[300px] md:h-[500px]">
              <Image 
                src={url || ''} 
                alt={name || 'Attachment'} 
                fill 
                className="object-contain"
                unoptimized={true}
              />
            </div>
          ) : (
            <div className="p-4 text-center">
              <p className="text-lg font-medium">{name}</p>
              <a 
                href={url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:underline mt-2 inline-block"
              >
                Open file
              </a>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};
