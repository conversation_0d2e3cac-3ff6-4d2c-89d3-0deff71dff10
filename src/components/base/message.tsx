'use client';

import React, { useEffect, useState } from 'react';
import { useStores } from '@/stores/utils/useStores';
import type {ChatRequestOptions, Message, Attachment, CreateMessage} from 'ai';
import cx from 'classnames';
import {AnimatePresence, motion} from 'framer-motion';
import {useContentParser} from '@/hooks/use-content-parser';
import {FileStatusList} from './file-status-list';
import {memo, useMemo, useCallback} from 'react';

import type {Vote} from '@/lib/db/schema';

import {DocumentToolCall, DocumentToolResult} from './document';
import {Markdown} from './markdown';
import {MessageActions} from './message-actions';
import {PreviewAttachment} from './preview-attachment';
import equal from 'fast-deep-equal';
import {cn, generateUUID} from '@/lib/utils';
import {MessageEditor} from './message-editor';
import {DocumentPreview} from './document-preview';
import {AlertTriangle, SparklesIcon, EditIcon} from "lucide-react";
import {FileOperationResult} from './file-operation-result';
import {Button} from "@/components/ui/button";
import {SQLStatusList} from "@/components/base/sql-status-list";
import {GetFileContentsToolResult} from "@/components/base/get-file-contents-tool-result";
import {QueryCodebaseToolResult} from "@/components/base/query-codebase-tool-result";
import {EditFileToolResult} from "@/components/base/edit-file-tool-result";
import {GetSupabaseInstructionsToolResult} from "@/components/base/get-supabase-instructions-tool-result";
import {GetSupabaseLogsToolResult} from "@/components/base/get-supabase-logs-tool-result";
import {GetClientLogsToolResult} from "@/components/base/get-client-logs-tool-result";
import {SearchWebToolResult} from "@/components/base/search-web-tool-result";
import {DesignToolResult} from "@/components/base/design-tool-result";
import {MultiPerspectiveAnalysisToolResult} from "@/components/base/multi-perspective-analysis-tool-result";
import {ManageSupabaseAuthToolResult} from "@/components/base/manage-supabase-auth-tool-result";
import {ClientTestingToolResult, InlineClientTestingToolResult} from "@/components/base/client-testing-tool-result";
import ThinkingDisplay from "@/components/thinking/ThinkingDisplay";
import {ActionsDisplay} from "@/components/actions/ActionsDisplay";
import {ActionMeta} from "@/lib/parser/ActionsParser";
import {ValidationMessage} from "./validation-message";
import dayjs from "dayjs";
import {ParsedContent} from "./parsed-content";
import {UserMessage} from "./user-message";
import {QuerySupabaseContextToolResult} from "@/components/base/query-supabase-context-tool-result";
import MagicallyLogo from "@/components/logo";

// UserMessage component is now imported from './user-message'

const PurePreviewMessage = ({
                                projectId,
                                chatId,
                                message,
                                vote,
                                isLoading,
                                setMessages,
                                reload,
                                setInput,
                                isReadonly,
                                isLastMessage = false,
                                isLastUserMessage = false,
                                status,
                                setAttachments,
                                append,
                                onVersionClick,
                                isVersionActive,
                                onActionClick,
                                setSnackError,
                                removeActions,
                                addToolResult
                            }: {
    projectId: string;
    chatId: string;
    message: Message;
    vote: Vote | undefined;
    isLoading: boolean;
    setMessages: (
        messages: Message[] | ((messages: Message[]) => Message[]),
    ) => void;
    setInput: (value: string) => void;
    reload: (
        chatRequestOptions?: ChatRequestOptions,
    ) => Promise<string | null | undefined>;
    isReadonly: boolean;
    isLastMessage: boolean;
    isLastUserMessage: boolean;
    status: 'submitted' | 'streaming' | 'ready' | 'error';
    append: (
        message: Message | CreateMessage,
        chatRequestOptions?: ChatRequestOptions,
    ) => Promise<string | null | undefined>;
    setAttachments?: (attachments: Attachment[]) => void;
    onVersionClick: (messageId: string) => void;
    isVersionActive?: boolean;
    onActionClick: (action: ActionMeta) => void;
    setSnackError: any;
    removeActions: boolean
    addToolResult?: (params: { toolCallId: string; result: any }) => void;
}) => {
    const {
        fileStatuses,
        sqlStatuses,
        diffErrors,
        thinkingStatus,
        actionsStatus,
        hiddenContentStatus,
        cleanContent,
        executeQuery
    } = useContentParser(message, projectId, isLastMessage);
    
    // Filter out duplicate content from message.content that appears in message.parts
    const filteredContent = useMemo(() => {
        if(message.role === "user") {
            return message.content;
        }
        if (!message.parts || message.parts.length === 0) {
            return message.content;
        }
        
        // Get text parts only
        const textParts = message.parts
            .filter(part => part.type === 'text' && 'text' in part)
            .map(part => (part as { text: string }).text);
        
        if (textParts.length === 0) {
            return message.content;
        }
        
        // Sort parts by length (descending) to handle overlapping matches
        textParts.sort((a, b) => b.length - a.length);
        
        // Create a filtered version of the content
        let processedContent = message.content;
        
        // Remove each part from the content
        for (const part of textParts) {
            processedContent = processedContent.replace(part, '');
        }
        
        // Clean up any double spaces and trim
        return processedContent.replace(/\s+/g, ' ').trim();
    }, [message.content, message.parts]);

    // Check if this is a validation message
    // const isValidationMessage = message.role === 'system' && (
    //     message.content?.includes('🔍') || message.content?.includes('✅')
    // );

    // Extract validation message type and content
    // const validationType = message.content?.includes('🔍') ? 'validating' : 'validated';
    // const validationContent = message.content?.replace(/^[🔍✅]\s*/, '');
    const { generatorStore } = useStores();
    const [mode, setMode] = useState<'view' | 'edit'>('view');

    const handleCtaSubmit = useCallback((prompt: string) => {
        if (setInput) {
            setInput(prompt);
        }
        // setMessages(messages => [
        //   ...messages,
        //   {
        //     id: Math.random().toString(),
        //     content: prompt,
        //     role: 'user',
        //   }
        // ]);
    }, [setInput]);


    // If this is a validation message, render the ValidationMessage component
    // if (isValidationMessage) {
    //     return <ValidationMessage content={validationContent} type={validationType} />;
    // }

    // const isIntermediateToolMessage = !message.annotations?.length && message.toolInvocations?.length;

    return (
        <AnimatePresence>
            <motion.div
                className={`w-[98%] mx-auto rounded-xl px-8 group/message text-xs ${message.role === 'assistant' ? 'bg-gradient-to-br from-accent/5 to-accent/3 py-4' : ''}`}
                initial={{y: 5, opacity: 0}}
                animate={{y: 0, opacity: 1}}
                data-role={message.role}
            >
                {message.role === 'assistant' && (
                    <div className="flex items-center space-x-2 justify-start mb-2">
                        <div
                            className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background">
                            <div className="translate-y-px">
                                <MagicallyLogo iconOnly logoWidthAction={20}/>
                            </div>

                        </div>
                        <span className="text-md font-brand font-bold ml-2">magically</span>
                    </div>

                )}
                <div
                    className={cn(
                        'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-full',
                        {
                            'w-full': mode === 'edit',
                            'group-data-[role=user]/message:w-fit': mode !== 'edit',
                        },
                    )}
                >


                    <div className="flex flex-col gap-2 min-w-0 flex-1">
                        {message.experimental_attachments && (
                            <div className="flex flex-row justify-end gap-2 flex-wrap">
                                {message.experimental_attachments.map((attachment) => (
                                    <PreviewAttachment
                                        key={attachment.url}
                                        attachment={attachment}
                                    />
                                ))}
                            </div>
                        )}

                        <ThinkingDisplay thinkingStatus={thinkingStatus}/>
                        {message.content && mode === 'view' && (
                            <div className="flex flex-row gap-2 items-start w-full">
                                {/*{message.role === 'user' && !isReadonly && isLastUserMessage && (*/}
                                {/*    <Button*/}
                                {/*        variant="ghost"*/}
                                {/*        className="px-2 h-fit rounded-full text-muted-foreground shrink-0"*/}
                                {/*        onClick={() => {*/}
                                {/*            setMode('edit');*/}
                                {/*        }}*/}
                                {/*    >*/}
                                {/*        <EditIcon/>*/}
                                {/*    </Button>*/}
                                {/*)}*/}

                                <div
                                    className={cn('flex flex-col gap-4 break-words w-full', {
                                        'dark:bg-primary-foreground dark:text-black px-3 py-2 rounded-sm':
                                            message.role === 'user',
                                    })}
                                >
                                    {typeof message.content === 'string' && (
                                        <ParsedContent
                                            content={filteredContent}
                                            message={message}
                                            projectId={projectId}
                                            isLastMessage={isLastMessage}
                                            role={message.role as 'user' | 'assistant' | 'system'}
                                        />
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Display tool calls from either message.parts or extracted tool calls */}
                        {(message.parts && message.parts.length > 0) && (
                            message.parts.map((part, index) => {
                                switch (part.type) {


                                    case "reasoning":
                                        return (
                                            <>
                                                <pre className="text-wrap">{JSON.stringify(part, null, 2)}</pre>
                                                <ThinkingDisplay thinkingStatus={{ isActive: true, isComplete: true, content: part.reasoning, sections: [] } }/>
                                            </>
                                        )
                                    case "text":
                                        if(message.role === "user") {
                                            return;
                                        }
                                        // if(message.content.includes(part.text)) {
                                        //     return;
                                        // }
                                        return (
                                            <div
                                                className={cn('flex flex-col gap-4 break-words w-full')}
                                            >
                                                {/*<pre>{JSON.stringify(actionsStatus)}</pre>*/}
                                                {/*<pre className="text-wrap">{JSON.stringify(part, null, 2)}</pre>*/}
                                                <ParsedContent 
                                                    content={part.text} 
                                                    message={{...message, id: message.id, content: part.text}}
                                                    projectId={projectId} 
                                                    isLastMessage={isLastMessage} 
                                                    role={message.role as 'user' | 'assistant' | 'system'} 
                                                />
                                            </div>
                                        )

                                    case "tool-invocation":

                                        const {toolInvocation} = part;
                                        const {state, toolCallId, toolName, args} = toolInvocation;

                                        if (state === 'result') {
                                            const {result} = toolInvocation;

                                            return (
                                                <div key={`${message.id}_${index}_${toolCallId}`}>
                                                    {
                                                        (toolName === 'getFileContents') ? (
                                                            <GetFileContentsToolResult files={args.files}/>
                                                        ) : toolName === 'queryCodebase' ? (
                                                            <QueryCodebaseToolResult query={args.query} excludedFiles={args.excludedFiles} result={args?.result} state={state}/>
                                                        ) : toolName === 'editFile' ? (
                                                            <EditFileToolResult absolutePath={args.absolutePath} result={args} state={state}/>
                                                        ) : toolName === 'getSupabaseInstructions' ? (
                                                            <GetSupabaseInstructionsToolResult reason={args.reason || 'Fetching Supabase schema'} state={state}/>
                                                        ) : toolName === 'getSupabaseLogs' ? (
                                                            <GetSupabaseLogsToolResult reason={args.reason || 'Fetching Supabase logs'} service={args.service} state={'complete'} result={result}/>
                                                         ) : toolName === 'querySupabaseContext' ? (
                                                            <QuerySupabaseContextToolResult query={args?.query || 'Fetching Supabase resources'} state={'result'} result={result}/>
                                                         ) : toolName === 'searchWeb' ? (
                                                             <SearchWebToolResult query={args?.query || ''} reason={args?.reason || 'Searching the web'} state={'complete'} result={result}/>
                                                         ) : toolName === 'generateDesign' ? (
                                                             <DesignToolResult projectId={projectId} chatId={chatId} reason={args?.prompt || 'Design app screens'} state={'complete'} result={result}/>
                                                          ) : toolName === 'getClientLogs' ? (
                                                             <GetClientLogsToolResult reason={args.reason || 'Fetching client logs'} type={args.type} source={args.source} state={'complete'} result={result}/>
                                                          ) : toolName === 'displayMultiPerspectiveAnalysis' ? (
                                                             <MultiPerspectiveAnalysisToolResult projectId={projectId} chatId={chatId} reason={args?.reason || 'Multi-perspective analysis'} state={'complete'} result={result}/>
                                                          ) : toolName === 'manageSupabaseAuth' ? (
                                                             <ManageSupabaseAuthToolResult reason={args.reason || 'Managing Supabase auth'} action={args.action} state={'complete'} result={result}/>
                                                          ) : toolName === 'clientTesting' ? (
                                                             <InlineClientTestingToolResult 
                                                                 reason={args.reason || 'Testing application'} 
                                                                 state={'complete'} 
                                                                 chatId={chatId} 
                                                                 expectations={args.expectations} 
                                                                 featuresToTest={args.featuresToTest} 
                                                                 toolCallId={toolCallId}
                                                                 addToolResult={addToolResult}
                                                             />
                                                          ) : (
                                                            <></>
                                                            // <pre>{JSON.stringify(result, null, 2)}</pre>
                                                        )}
                                                </div>
                                            );
                                        }
                                        return (
                                            <div
                                                key={toolCallId}
                                                className={cx({
                                                    skeleton: ['getWeather', 'getFileContents', 'queryCodebase', 'editFile', 'getSupabaseInstructions', 'getSupabaseLogs', 'getClientLogs', 'manageSupabaseAuth'].includes(toolName),
                                                })}
                                            >
                                                {toolName === 'getFileContents' ? (
                                                    <GetFileContentsToolResult files={args?.files}/>
                                                ) : toolName === 'queryCodebase' ? (
                                                    <QueryCodebaseToolResult query={args?.query} excludedFiles={args?.excludedFiles} result={args?.result} state={state}/>
                                                ) : toolName === 'editFile' ? (
                                                    <EditFileToolResult absolutePath={args?.absolutePath} result={args} state={state}/>
                                                ) : toolName === 'getSupabaseInstructions' ? (
                                                    <GetSupabaseInstructionsToolResult reason={args?.reason || 'Fetching Supabase schema'} state={state}/>
                                                ) : toolName === 'getSupabaseLogs' ? (
                                                    <GetSupabaseLogsToolResult reason={args?.reason || 'Fetching Supabase logs'} service={args?.service} functionId={args?.functionId} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'}/>
                                                ) : toolName === 'querySupabaseContext' ? (
                                                    <QuerySupabaseContextToolResult query={args?.query || 'Fetching Supabase resources'} state={'call'} result={''}/>
                                                ): toolName === 'searchWeb' ? (
                                                     <SearchWebToolResult query={args?.query || ''} reason={args?.reason || 'Searching the web'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>
                                                 ) : toolName === 'generateDesign' ? (
                                                     <DesignToolResult projectId={projectId} chatId={chatId} reason={args?.prompt || 'Design app screens'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>
                                                 ) : toolName === 'getClientLogs' ? (
                                                    <GetClientLogsToolResult reason={args?.reason || 'Fetching client logs'} type={args?.type} source={args?.source} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>
                                                ) : toolName === 'displayMultiPerspectiveAnalysis' ? (
                                                    <MultiPerspectiveAnalysisToolResult projectId={projectId} chatId={chatId} reason={args?.reason || 'Multi-perspective analysis'} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>
                                                ) : toolName === 'manageSupabaseAuth' ? (
                                                    <ManageSupabaseAuthToolResult reason={args?.reason || 'Managing Supabase auth'} action={args?.action} state={state === 'partial-call' ? 'loading' : state === 'call' ? 'complete' : 'error'} result={args?.result}/>
                                                ):  toolName === 'clientTesting' ? (
                                                    <ClientTestingToolResult
                                                        reason={args?.reason || 'Testing application'}
                                                        state={'loading'}
                                                        chatId={chatId}
                                                        expectations={args?.expectations}
                                                        featuresToTest={args?.featuresToTest}
                                                        toolCallId={toolCallId}
                                                        addToolResult={addToolResult}
                                                    />
                                                ): null}
                                            </div>
                                        );
                                        break;
                                }
                            })
                        )}

                        <FileStatusList
                            files={fileStatuses}
                            messageId={message.id}
                            onFileClick={onVersionClick}
                            isVersionActive={isVersionActive}
                        />
                        <SQLStatusList
                            queries={sqlStatuses}
                            onExecute={executeQuery}
                            onError={(error) => setSnackError(error)}
                            status={status}
                        />

                        {message.content && mode === 'edit' && (
                            <div className="flex flex-row gap-2 items-start">
                                <div className="size-8"/>

                                <MessageEditor
                                    key={message.id}
                                    message={message}
                                    setMode={setMode}
                                    setInput={setInput}
                                    setMessages={setMessages}
                                    reload={reload}
                                    chatId={chatId}
                                />
                            </div>
                        )}




                        {actionsStatus?.isActive && message.role === 'assistant' && (
                            <ActionsDisplay actionsStatus={actionsStatus} append={append} status={status} onActionClick={onActionClick} />
                        )}

                        {
                            !isLoading  ? <p className="text-[8px] mt-0">{dayjs(message.createdAt).format('DD MMM YYYY, HH:mm a')}</p> : null
                        }

                        {!isReadonly && !removeActions  && (
                            <MessageActions
                                key={`action-${message.id}`}
                                chatId={chatId}
                                message={message}
                                vote={vote}
                                reload={reload}
                                isLoading={isLoading}
                                isLastMessage={isLastMessage}
                                isLastUserMessage={isLastUserMessage}
                                setMessages={setMessages}
                                setMode={setMode}
                                status={status}
                                setInput={setInput}
                                setAttachments={setAttachments}
                                onVersionClick={onVersionClick}
                            />
                        )}
                        {/*<p>{(message as any)?.restorationId || message.id}</p>*/}



                    </div>
                </div>
            </motion.div>
        </AnimatePresence>
    );
};

export const PreviewMessage = memo(
    PurePreviewMessage,
    (prevProps, nextProps) => {
        if (prevProps.isLoading !== nextProps.isLoading) return false;
        if (prevProps.isLastMessage !== nextProps.isLastMessage) return false;
        if (prevProps.isLastUserMessage !== nextProps.isLastUserMessage) return false;
        if (prevProps.status !== nextProps.status) return false;
        if (prevProps.message.content !== nextProps.message.content) return false;
        if (prevProps.isVersionActive !== nextProps.isVersionActive) return false;
        if (
            !equal(
                prevProps.message.toolInvocations,
                nextProps.message.toolInvocations,
            )
        )
            return false;
        if (!equal(prevProps.vote, nextProps.vote)) return false;

        return true;
    },
);

export const ThinkingMessage = () => {
    const role = 'assistant';
    const [currentStepIndex, setCurrentStepIndex] = useState(0);
    const [isAnimating, setIsAnimating] = useState(true);

    // Define the sequence of backend process steps
    const processSteps = [
        "Planning solution...",
        "Consulting design patterns...",
        "Analyzing requirements...",
        "Checking dependencies...",
        "Reviewing architecture...",
        "Optimizing for performance...",
        "Finalizing implementation..."
    ];

    // Cycle through the steps until the last one
    useEffect(() => {
        if (!isAnimating) return;

        const interval = setInterval(() => {
            setCurrentStepIndex(prevIndex => {
                // If we've reached the last step, stop cycling
                if (prevIndex === processSteps.length - 1) {
                    clearInterval(interval);
                    return prevIndex;
                }
                return (prevIndex + 1) % processSteps.length;
            });
        }, 2000); // Change message every 2 seconds

        return () => clearInterval(interval);
    }, [isAnimating, processSteps.length]);

    return (
        <motion.div
            className="w-full mx-auto max-w-3xl px-4 group/message"
            initial={{y: 5, opacity: 0}}
            animate={{y: 0, opacity: 1, transition: {delay: 0.5}}}
            data-role={role}
        >
            <div
                className={cx(
                    'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-full group-data-[role=user]/message:py-2 rounded-xl',
                    {
                        'group-data-[role=user]/message:bg-muted': true,
                    },
                )}
            >
                <div className="size-6 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-primary/10">
                    <SparklesIcon size={12} className="text-primary-foreground animate-pulse"/>
                </div>

                <div className="flex flex-col gap-2 w-full">
                    <div className="flex flex-col gap-4">
                        <div className="flex items-center gap-1">
                            <span className="text-muted-foreground font-medium text-xs">
                                {processSteps[currentStepIndex]}
                            </span>
                            <span className="inline-flex">
                                <motion.span
                                    className="h-1 w-1 bg-primary rounded-full"
                                    animate={{ opacity: [0.4, 1, 0.4] }}
                                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                                />
                                <motion.span
                                    className="h-1 w-1 bg-primary rounded-full ml-1"
                                    animate={{ opacity: [0.4, 1, 0.4] }}
                                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.2 }}
                                />
                                <motion.span
                                    className="h-1 w-1 bg-primary rounded-full ml-1"
                                    animate={{ opacity: [0.4, 1, 0.4], scale: [1, 1.3, 1] }}
                                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut", delay: 0.4 }}
                                />
                            </span>
                        </div>

                        {/* Progress steps indicator */}
                        <div className="flex gap-1 mt-1">
                            {processSteps.map((_, index) => (
                                <div
                                    key={index}
                                    className={`h-1 rounded-full transition-all duration-300 ${index <= currentStepIndex ? 'bg-primary w-8' : 'bg-muted w-4'}`}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};
