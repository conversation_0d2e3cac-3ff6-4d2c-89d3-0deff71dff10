import type {ChatRequestOptions, Attachment} from 'ai';
import type { Message as BaseMessage } from 'ai';

// Extended Message type with additional properties needed for message grouping
interface Message extends BaseMessage {
  parentUserMessageId?: string | null;
  parentAssistantMessageId?: string | null;
  isAssistantGroupHead?: boolean;
  parts?: any[];
  toolInvocations?: any[];
  experimental_attachments?: any[];
  childMessages?: Message[];
  createdAt?: Date;
}
import {toast} from 'sonner';
import {useSWRConfig} from 'swr';
import {useCopyToClipboard} from 'usehooks-ts';

import type {Vote} from '@/lib/db/schema';
import {getMessageIdFromAnnotations} from '@/lib/utils';
import {trackFeatureEvent, trackMessageEvent} from '@/lib/analytics/track';
import {ANALYTICS_EVENTS} from '@/lib/analytics/events';

import {CopyIcon, ThumbsDownIcon, ThumbsUpIcon, DatabaseIcon} from 'lucide-react';
import {Dispatch, memo, SetStateAction, useState} from 'react';
import equal from 'fast-deep-equal';
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";
import {Button} from "@/components/ui/button";
import {UndoIcon, RefreshCcwDotIcon} from "lucide-react";
import {ConfirmationDialog} from "@/components/ui/confirmation-dialog";
import {useUserMessageId} from "@/hooks/use-user-message-id";
import {useAIMessageId} from "@/hooks/use-ai-message-id";
import {useStores} from "@/stores/utils/useStores";
import {JSONValue} from "@ai-sdk/ui-utils";

export const MessageActions = ({
                            chatId,
                            message,
                            vote,
                            isLoading,
                            isLastMessage,
                            setMessages,
                            setMode,
                             isLastUserMessage,
                            reload,
                            status,
                            setInput,
                            setAttachments,
                            onVersionClick
                        }: {
    chatId: string;
    message: Message;
    vote: Vote | undefined;
    isLoading: boolean;
    isLastMessage: boolean;
    isLastUserMessage: boolean;
    status: 'submitted' | 'streaming' | 'ready' | 'error';
    setMessages: (
        messages: Message[] | ((messages: Message[]) => Message[]),
    ) => void;
    setMode: Dispatch<SetStateAction<'view' | 'edit'>>;
    reload: (
        chatRequestOptions?: ChatRequestOptions,
    ) => Promise<string | null | undefined>;
    setInput: (value: string) => void;
    setAttachments?: (attachments: Attachment[]) => void;
    onVersionClick: (messageId: string) => void;
}) => {
    const {mutate} = useSWRConfig();
    const [_, copyToClipboard] = useCopyToClipboard();
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const {aiMessageIdFromServer} = useAIMessageId();
    const {userMessageIdFromServer} = useUserMessageId();
    const {generatorStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);

    if (!session) {
        toast.error("Session is not initialized! Please contact support and tell the error code ENOQUOTE_EDIT.")
        return null;
    }

    if (isLoading) return null;

    const handleMessageDelete = async () => {
        setIsSubmitting(true);
        console.log('message.id', message.id)
        
        // Track checkpoint restoration event
        trackFeatureEvent('CHECKPOINT_RESTORED', {
            feature_name: 'checkpoint_restoration',
            is_enabled: true,
            user_type: 'free', // This should be dynamically determined
            trigger_source: 'user_action',
            project_id: session?.projectId || ''
        });

        // Find the next user message to restore to input (before deleting anything)
        const findNextUserMessage = (messages: Message[], currentIndex: number) => {
            // Look for the next user message after the current index
            for (let i = currentIndex + 1; i < messages.length; i++) {
                if (messages[i].role === 'user') {
                    return messages[i];
                }
            }
            return null;
        };

        // Define types for multimodal content
        type TextContent = {
            type: 'text';
            text: string;
        };

        type ImageContent = {
            type: 'image';
            image: string;
            mimeType?: string;
        };

        type MessageContent = TextContent | ImageContent;

        // Extract content and attachments from a message
        const extractContentAndAttachments = (message: Message) => {
            let textContent = '';
            let attachments: Attachment[] = [];

            // Handle different content formats
            if (typeof message.content === 'string') {
                textContent = message.content;
            } else if (Array.isArray(message.content)) {
                // Cast the content to our defined type
                const contentArray = message.content as MessageContent[];
                
                // Extract text content from the first part with type 'text'
                const textPart = contentArray.find(part => part.type === 'text') as TextContent | undefined;
                if (textPart) {
                    textContent = textPart.text || '';
                }

                // Extract attachments from parts with type 'image'
                const imageParts = contentArray.filter(part => part.type === 'image') as ImageContent[];
                attachments = imageParts.map(part => ({
                    url: part.image,
                    contentType: part.mimeType || 'image/png',
                    name: part.image.split('/').pop() || 'image.png'
                }));
            }

            // Also check for attachments property if it exists
            if ('experimental_attachments' in message && Array.isArray((message as any).experimental_attachments) && (message as any).experimental_attachments.length > 0) {
                attachments = [...attachments, ...(message as any).experimental_attachments];
            }

            return { textContent, attachments };
        };

        if(isLastMessage) {
            const messageId = message.id ?? userMessageIdFromServer;

            if (!messageId) {
                toast.error('Something went wrong, please try again!');
                setIsSubmitting(false);
                return;
            }

            await session.removeLatestFileState()
            reload({
                body: {
                    isReload: true,
                }
            });
            setMessages((messages) => {
                const index = messages.findIndex((m) => m.id === message.id);

                if (index !== -1) {
                    // Find next user message before modifying the array
                    const nextUserMessage = findNextUserMessage(messages, index);
                    
                    // If found, set its content to the input field
                    if (nextUserMessage) {
                        const { textContent, attachments } = extractContentAndAttachments(nextUserMessage);
                        // Use setTimeout to ensure this runs after state update
                        setTimeout(() => {
                            setInput(textContent.replace(/<hidden>[\s\S]*?<\/hidden>/g, ''));
                            if (attachments.length > 0 && setAttachments) {
                                // Update the attachments directly through props
                                setAttachments(attachments);
                            }
                        }, 0);
                    }
                    
                    return [...messages.slice(0, index)];
                }

                return messages;
            });
            setIsSubmitting(false);
            return;
        }
        // Use restorationId if available, otherwise fall back to message.id
        let messageId = message.id ?? aiMessageIdFromServer;
        
        // Determine if this is a grouped message (part of a parent-child relationship)
        const isGroupedMessage = !!message.parentAssistantMessageId ||
                               (message.childMessages && message.childMessages.length > 0) ||
            // @ts-ignore
            message.annotations?.some(a => (a as JSONValue)?.messageVersion === "v2");

        if (!messageId) {
            toast.error('Something went wrong, please try again!');
            setIsSubmitting(false);
            return;
        }

        let latestMessageId = null;
        try {
            const result = await session?.deleteMessage(messageId, isGroupedMessage);
            latestMessageId = result;
        } catch (e: any) {
            toast.error(e?.message || 'Error restoring checkpoint. Please contact support. ECLIENT.');
        }

        if(!latestMessageId) {
            return;
        }

        setMessages((messages) => {
            const index = messages.findIndex((m) => m.id === latestMessageId);
            if (index !== -1) {
                // Find next user message before modifying the array
                const nextUserMessage = findNextUserMessage(messages, index);
                
                // If found, set its content to the input field
                if (nextUserMessage) {
                    const { textContent, attachments } = extractContentAndAttachments(nextUserMessage);
                    // Use setTimeout to ensure this runs after state update
                    setTimeout(() => {
                        setInput(textContent);
                        if (attachments.length > 0 && setAttachments) {
                            // Update the attachments directly through props
                            setAttachments(attachments);
                        }
                    }, 0);
                }
                
                return [...messages.slice(0, index + 1)];
            }

            return messages;
        });
        setMode('view');
        setIsSubmitting(false);
    };


    if(message.role !== "user") {
        return (
            <TooltipProvider delayDuration={0}>
                <div className="flex flex-row justify-between gap-2">
                    {
                        !isLastMessage &&
                        <div>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <ConfirmationDialog
                                        title="Revert app to this checkpoint?"
                                        description="This will reset the message and the file states to this checkpoint. All changes after this message will be lost."
                                        onConfirm={handleMessageDelete}
                                        variant="destructive"
                                        confirmText="Revert"
                                        trigger={
                                            <Button
                                                className="py-1 px-3 h-fit text-[10px]"
                                                variant="accent"
                                                disabled={isSubmitting || isLoading || status === "streaming"}
                                            >
                                                <UndoIcon size={2} className="h-0.5 w-0.5"/>
                                                {isLastMessage ? "Retry" : "Restore checkpoint"}
                                            </Button>
                                        }
                                    />

                                </TooltipTrigger>
                                <TooltipContent>Copy</TooltipContent>
                            </Tooltip>

                            {/*<Button*/}
                            {/*    className="py-1 px-3 h-fit text-muted-foreground text-[10px]"*/}
                            {/*    variant="outline"*/}
                            {/*    disabled={isSubmitting || isLoading || status === "streaming"}*/}
                            {/*    onClick={() => onVersionClick && onVersionClick(message.id)}*/}
                            {/*>*/}
                            {/*    <DatabaseIcon size={2} className="h-0.5 w-0.5"/>*/}
                            {/*    Preview this version*/}
                            {/*</Button>*/}
                        </div>
                    }

                    <div className="flex flex-row gap-2">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
                                    // disabled={vote?.isUpvoted}
                                    variant="outline"
                                    onClick={async () => {
                                        const messageId = getMessageIdFromAnnotations(message);
                                        
                                        // Track message upvote event
                                        trackFeatureEvent('MESSAGE_VOTED', {
                                            feature_name: 'message_feedback',
                                            is_enabled: true,
                                            user_type: 'free', // This should be dynamically determined
                                            trigger_source: 'upvote',
                                            project_id: session?.projectId || ''
                                        });

                                        const upvote = fetch('/api/vote', {
                                            method: 'PATCH',
                                            body: JSON.stringify({
                                                chatId,
                                                messageId: message.id,
                                                type: 'up',
                                            }),
                                        });

                                        toast.promise(upvote, {
                                            loading: 'Upvoting Response...',
                                            success: () => {
                                                mutate<Array<Vote>>(
                                                    `/api/vote?chatId=${chatId}`,
                                                    (currentVotes) => {
                                                        if (!currentVotes) return [];

                                                        const votesWithoutCurrent = currentVotes.filter(
                                                            (vote) => vote.messageId !== message.id,
                                                        );

                                                        const currentVote = currentVotes.find(
                                                            (vote) => vote.messageId === message.id,
                                                        )

                                                        return [
                                                            ...votesWithoutCurrent,
                                                            {
                                                                chatId,
                                                                messageId: message.id,
                                                                isUpvoted: true,
                                                                projectId: currentVote?.projectId || '0000-0000-0000-0000',
                                                            },
                                                        ];
                                                    },
                                                    {revalidate: false},
                                                );

                                                return 'Upvoted Response!';
                                            },
                                            error: 'Failed to upvote response.',
                                        });
                                    }}
                                >
                                    <ThumbsUpIcon/>
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>Upvote Response</TooltipContent>
                        </Tooltip>

                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
                                    variant="outline"
                                    // disabled={vote && !vote.isUpvoted}
                                    onClick={async () => {
                                        const messageId = getMessageIdFromAnnotations(message);
                                        
                                        // Track message downvote event
                                        trackFeatureEvent('MESSAGE_VOTED', {
                                            feature_name: 'message_feedback',
                                            is_enabled: true,
                                            user_type: 'free', // This should be dynamically determined
                                            trigger_source: 'downvote',
                                            project_id: session?.projectId || ''
                                        });

                                        const downvote = fetch('/api/vote', {
                                            method: 'PATCH',
                                            body: JSON.stringify({
                                                chatId,
                                                messageId,
                                                type: 'down',
                                            }),
                                        });

                                        toast.promise(downvote, {
                                            loading: 'Downvoting Response...',
                                            success: () => {
                                                mutate<Array<Vote>>(
                                                    `/api/vote?chatId=${chatId}`,
                                                    (currentVotes) => {
                                                        if (!currentVotes) return [];

                                                        const votesWithoutCurrent = currentVotes.filter(
                                                            (vote) => vote.messageId !== message.id,
                                                        );

                                                        const currentVote = currentVotes.find(
                                                            (vote) => vote.messageId === message.id,
                                                        )

                                                        return [
                                                            ...votesWithoutCurrent,
                                                            {
                                                                chatId,
                                                                messageId: message.id,
                                                                isUpvoted: false,
                                                                projectId: currentVote?.projectId || '0000-0000-0000-0000',
                                                            },
                                                        ];
                                                    },
                                                    {revalidate: false},
                                                );

                                                return 'Downvoted Response!';
                                            },
                                            error: 'Failed to downvote response.',
                                        });
                                    }}
                                >
                                    <ThumbsDownIcon/>
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>Downvote Response</TooltipContent>
                        </Tooltip>
                    </div>
                </div>
            </TooltipProvider>
        );
    } else if(message.role === "user") {
        return (
            <TooltipProvider delayDuration={0}>
                <div className="flex flex-row justify-between gap-2">
                    {
                        (isLastUserMessage && !isSubmitting && !isLoading && status !== "streaming") &&

                        <Button
                            className="py-1 px-3 h-fit text-muted-foreground text-[10px]"
                            variant="outline"
                            onClick={() => {
                                // Track message regeneration event
                                trackMessageEvent('REGENERATED', {
                                    message_id: message.id,
                                    chat_id: chatId,
                                    project_id: session?.projectId || '',
                                    error_type: 'none'
                                });
                                reload({
                                    body: {
                                        isReload: true,
                                    }
                                });
                            }}
                            disabled={isSubmitting || isLoading}
                        >
                            Retry
                            <RefreshCcwDotIcon className="h-[4px] w-[4px]"/>
                        </Button>

                    }
                </div>
            </TooltipProvider>
        )
    } else {
        return null;
    }
}
