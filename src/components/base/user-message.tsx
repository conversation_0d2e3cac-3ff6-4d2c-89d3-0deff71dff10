'use client';

import React, { useState, useMemo } from 'react';
import { Markdown } from './markdown';
import { Button } from "@/components/ui/button";

interface UserMessageProps {
  content: string;
}

/**
 * Component to handle truncated user messages with expand/collapse functionality
 */
export function UserMessage({ content }: UserMessageProps) {
    const [isExpanded, setIsExpanded] = useState(false);

    // Process content to remove <hidden> tags and their content
    const processedContent = useMemo(() => {
        return content.replace(/<hidden>[\s\S]*?<\/hidden>/g, '');
    }, [content]);

    const charCount = processedContent.length;
    const isLongMessage = charCount > 500;

    return (
        <div>
            {/* Top info and expand button - not absolute positioned */}
            {isLongMessage && !isExpanded && (
                <div className="flex items-center justify-end gap-1 mb-2">
                    <span className="text-xs text-muted-foreground">
                        {charCount} chars
                    </span>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsExpanded(true)}
                        className="py-0 h-6 text-xs hover:bg-muted/50"
                    >
                        Expand
                    </Button>
                </div>
            )}

            {/* Message content */}
            <Markdown>
                {isExpanded || !isLongMessage
                    ? processedContent
                    : `${processedContent.slice(0, 500)}...`}
            </Markdown>

            {/* Bottom expand/collapse control */}
            {isLongMessage && (
                <div className="flex items-center justify-end gap-2 mt-2">
                    <span className="text-xs text-muted-foreground">
                        {charCount} chars
                    </span>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="py-0 h-6 text-xs hover:bg-muted/50"
                    >
                        {isExpanded ? 'Collapse' : 'Show more'}
                    </Button>
                </div>
            )}
        </div>
    );
}
