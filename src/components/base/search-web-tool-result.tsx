'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ExternalLink, Search, ChevronDown, ChevronRight, Loader2, AlertCircle } from 'lucide-react';

interface SearchWebToolResultProps {
  query: string;
  reason: string;
  state: 'loading' | 'complete' | 'error' | 'partial-call' | 'call' | 'error-call';
  result?: any;
  className?: string;
}

export const SearchWebToolResult = memo(function SearchWebToolResult({ 
  query,
  reason,
  state,
  result,
  className
}: SearchWebToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);
  
  // Parse the result if it's a string
  let parsedResult: any = null;
  if (typeof result === 'string') {
    try {
      parsedResult = JSON.parse(result);
    } catch (e) {
      console.error('Failed to parse web search result', e);
    }
  } else {
    parsedResult = result;
  }

  const results = parsedResult?.results || [];
  const answer = parsedResult?.answer || '';
  const error = parsedResult?.error || '';
  const timestamp = new Date().toISOString();
  
  // Check if results were found
  const hasResults = () => {
    return results.length > 0;
  };

  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'loading' || state === 'partial-call' ? 'border-amber-600/30 bg-amber-600/5' : 
        state === 'error' || state === 'error-call' ? 'border-red-600/30 bg-red-600/5' : 
        'border-blue-600/30 bg-blue-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'loading' || state === 'partial-call' ? (
              <Loader2 className="h-3 w-3 text-amber-600 animate-spin" />
            ) : state === 'error' || state === 'error-call' ? (
              <AlertCircle className="h-3 w-3 text-red-600" />
            ) : (
              <Search className="h-3 w-3 text-blue-600" />
            )}
            
            <span className="text-foreground">
              {state === 'loading' || state === 'partial-call' ? 'Searching the web...' : 
               state === 'error' || state === 'error-call' ? 'Error searching the web' : 
               'Web search results'}
            </span>
          </div>
          
          {(state === 'complete' || state === 'call') && (
            <Badge variant="outline" className="text-[10px] h-4">
              {hasResults() ? 'Results found' : 'No results'}
            </Badge>
          )}
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            <div className="font-medium mb-1">Search query:</div>
            <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
              {query}
            </div>
            
            <div className="mt-2">
              <div className="font-medium mb-1">Reason for search:</div>
              <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
                {reason}
              </div>
            </div>
            
            {(state === 'loading' || state === 'partial-call') && (
              <div className="flex items-center gap-2 mt-2">
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Searching the web for information...</span>
              </div>
            )}
            
            {(state === 'complete' || state === 'call') && (
              <div className="mt-4 space-y-3">
                
                <div className="space-y-3">
                  {results.length > 0 ? (
                    results.map((result: any, index: number) => (
                      <div key={index} className="border rounded-md p-2">
                        <div className="flex items-start justify-between mb-1">
                          <h3 className="font-medium text-[11px]">{result.title}</h3>
                          <a 
                            href={result.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-[10px] text-blue-600 hover:underline flex items-center gap-1"
                          >
                            <ExternalLink size={10} />
                            Source
                          </a>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-2 text-[11px] text-muted-foreground">
                      No search results found
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {(state === 'error' || state === 'error-call') && error && (
              <div className="mt-2 text-destructive p-2 border border-destructive/20 rounded-md bg-destructive/5 text-[11px]">
                {error}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
