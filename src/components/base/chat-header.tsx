'use client';

import Link from 'next/link';
import {useRouter} from 'next/navigation';
import {useWindowSize} from 'usehooks-ts';

import {Button} from '@/components/ui/button';
import {memo} from 'react';
import {useSidebar} from '../ui/sidebar';
import {PlusIcon} from "lucide-react";

function PureChatHeader() {
    const router = useRouter();
    const {open} = useSidebar();

    const {width: windowWidth} = useWindowSize();

    return (
        <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2">
            {/*<SidebarToggle />*/}

            {(!open || windowWidth < 768) && (

                <Button
                    variant="outline"
                    className="order-2 md:order-1 md:px-2 px-2 md:h-fit ml-auto md:ml-0"
                    onClick={() => {
                        router.push('/');
                        router.refresh();
                    }}
                >
                    <PlusIcon/>
                    <span className="md:sr-only">New Chat</span>
                </Button>

            )}
        </header>
    );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
    return true;
});
