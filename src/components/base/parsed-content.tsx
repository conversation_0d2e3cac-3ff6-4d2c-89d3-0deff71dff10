'use client';

import React from 'react';
import { useContentParser } from '@/hooks/use-content-parser';
import { Markdown } from './markdown';
import { UserMessage } from './user-message';
import type { Message } from 'ai';

interface ParsedContentProps {
  content: string;
  message: Message;
  projectId: string;
  isLastMessage: boolean;
  role: 'user' | 'assistant' | 'system';
}

/**
 * ParsedContent component that processes content through the useContentParser hook
 * and renders it with the appropriate styling based on the message role.
 */
export function ParsedContent({ content, message, projectId, isLastMessage, role }: ParsedContentProps) {
  // Process the content through the parser to extract special tags
  const {
    cleanContent,
    fileStatuses,
    sqlStatuses,
    diffErrors,
    thinkingStatus,
    actionsStatus,
    hiddenContentStatus,
    executeQuery
  } = useContentParser({ ...message, content }, projectId, isLastMessage);

  // Render based on role
  if (role === 'user') {
    return <UserMessage content={cleanContent} />;
  }
  
  return (
    <div className="space-y-4">
      <Markdown>{cleanContent}</Markdown>
    </div>
  );
}
