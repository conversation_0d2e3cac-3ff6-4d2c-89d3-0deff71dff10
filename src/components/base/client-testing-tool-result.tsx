'use client';

import React, { memo, useState } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useStores } from '@/stores/utils/useStores';
import { observer } from 'mobx-react-lite';
import { CheckCircle2, PlayCircle } from 'lucide-react';

interface ClientTestingToolResultProps {
  featuresToTest: string;
  expectations: string;
  reason: string;
  toolCallId: string;
  state: 'loading' | 'complete';
  className?: string;
  chatId: string;
  addToolResult?: (params: { toolCallId: string; result: any }) => void;
}

// This component is shown as an overlay when testing is active
export const ClientTestingToolResult = observer(function ClientTestingToolResult({ 
  featuresToTest,
  expectations,
  reason,
  toolCallId,
  state = 'loading',
  className,
  addToolResult,
  chatId
}: ClientTestingToolResultProps) {
  const { generatorStore } = useStores();
  const session = generatorStore.getActiveSession(chatId);
  
  // Handle completion of testing
  const handleCompleteTesting = () => {
    if (session) {
      // Mark testing as complete in the session store
      session.completeClientTesting(toolCallId);
      
      // Send the result back to the AI using addToolResult
      if (addToolResult) {
        addToolResult({
          toolCallId,
          result: {
            result: "DONE",
            toolCallId,
            clientTool: true
          }
        });
      }
    }
  };
  
  // Only show the overlay when in loading state (testing is active)
  if (state === 'complete') {
    return null;
  }

  return (
    <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div 
        className={cn(
          'w-full max-w-md rounded-lg bg-card p-6 shadow-lg',
          'border border-border',
          'transition-all duration-300 ease-in-out',
          className
        )}
      >
        <div className="flex items-center gap-2 mb-4">
          <PlayCircle className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Testing Required</h3>
          <Badge variant="outline" className="ml-auto">
            Ready to test
          </Badge>
        </div>
        
        <div className="space-y-4 mb-6">
          <div>
            <h4 className="text-sm font-medium mb-1">Please test the following:</h4>
            <div className="bg-muted/30 p-3 rounded-md text-sm">
              {featuresToTest}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium mb-1">Expected outcome:</h4>
            <div className="bg-muted/30 p-3 rounded-md text-sm">
              {expectations}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium mb-1">Reason for testing:</h4>
            <div className="bg-muted/30 p-3 rounded-md text-sm">
              {reason}
            </div>
          </div>
        </div>
        
        <div className="flex justify-end gap-3">
          <Button 
            variant="default" 
            onClick={handleCompleteTesting}
            className="gap-2"
          >
            <CheckCircle2 className="h-4 w-4" />
            I am done testing
          </Button>
        </div>
      </div>
    </div>
  );
});

// Inline version for message display (shown in the message thread when testing is complete)
export const InlineClientTestingToolResult = observer(function InlineClientTestingToolResult({ 
  featuresToTest,
  expectations,
  reason,
  toolCallId,
  state = 'complete',
  className,
  chatId,
  addToolResult
}: ClientTestingToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-3 shadow-sm',
        'border border-green-600/30 bg-green-600/5',
        className
      )}
    >
      <div className="flex items-center gap-2">
        <CheckCircle2 className="h-4 w-4 text-green-600" />
        <span className="text-sm font-medium">Testing completed</span>
        <Button 
          variant="ghost" 
          size="sm" 
          className="ml-auto h-7 px-2"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? 'Hide details' : 'Show details'}
        </Button>
      </div>
      
      {isExpanded && (
        <div className="mt-3 space-y-3 text-xs">
          <div>
            <div className="font-medium mb-1">Tested features:</div>
            <div className="bg-muted/30 p-2 rounded-md">
              {featuresToTest}
            </div>
          </div>
          
          <div>
            <div className="font-medium mb-1">Expected outcome:</div>
            <div className="bg-muted/30 p-2 rounded-md">
              {expectations}
            </div>
          </div>
          
          <div>
            <div className="font-medium mb-1">Reason for testing:</div>
            <div className="bg-muted/30 p-2 rounded-md">
              {reason}
            </div>
          </div>
        </div>
      )}
    </div>
  );
});
