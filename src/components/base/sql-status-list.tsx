import React, { memo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { SQLStatus } from '@/hooks/use-sql-status';
import { Loader2, Play, AlertCircle, Check, Database, ChevronDown, RefreshCw } from "lucide-react";
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface SQLStatusListProps {
    queries: SQLStatus[];
    className?: string;
    onExecute: (status: SQLStatus) => Promise<string | null>;
    onError?: (error: string) => void;
    status?: 'submitted' | 'streaming' | 'ready' | 'error';
}

export const SQLStatusList = memo(function SQLStatusList({
    queries,
    className,
    onExecute,
    onError,
    status = 'ready'
}: SQLStatusListProps) {
    if (queries.length === 0) return null;
    
    const handleExecute = useCallback((query: SQLStatus) => {
        // Call onExecute and handle the returned promise
        onExecute(query).then(errorMessage => {
            // If there's an error message and an onError handler, call it
            if (errorMessage && onError) {
                onError(errorMessage);
            }
        }).catch(err => {
            console.error('Error executing query:', err);
        });
    }, [onExecute, onError]);

    return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className={cn(
                'mt-2 rounded-md bg-gradient-to-b from-card/95 to-muted/30 p-2 backdrop-blur-sm shadow-sm relative overflow-hidden',
                'border border-border/50 dark:border-white/20',
                className
            )}
        >
            <div className="space-y-2">
                <div className="text-xs font-medium text-white flex items-center gap-1.5 mb-1">
                    <Database className="h-3.5 w-3.5" />
                    Database Changes
                </div>
                <AnimatePresence mode="popLayout">
                    {queries.map((query) => (
                        <motion.div
                            key={`${query.type}-${query.table}`}
                            initial={{ opacity: 0, height: 0, scale: 0.95 }}
                            animate={{ opacity: 1, height: 'auto', scale: 1 }}
                            exit={{ opacity: 0, height: 0, scale: 0.95 }}
                            transition={{
                                duration: 0.2,
                                scale: {
                                    type: "spring",
                                    damping: 15,
                                    stiffness: 300
                                }
                            }}
                            className={cn(
                                "group rounded-md p-2 text-card-foreground transition-all hover:shadow-sm border hover:border-border/30",
                                query.state === 'error' 
                                    ? "bg-background/80 border-red-500/30 hover:bg-background/90" 
                                    : query.state === 'done'
                                        ? "bg-background/80 border-green-500/30 hover:bg-background/90"
                                        : "bg-background/80 border-border/20 hover:bg-background/90"
                            )}
                        >
                            <div className="flex items-center gap-2">
                                <div className="relative h-1.5 w-1.5 flex items-center justify-center mt-0.5">
                                    {query.state === 'executing' ? (
                                        <>
                                            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                                            <span className="relative inline-flex rounded-full h-1.5 w-1.5 bg-blue-500"></span>
                                        </>
                                    ) : query.state === 'error' ? (
                                        <div className="absolute inset-0 rounded-full bg-red-500" />
                                    ) : query.state === 'done' ? (
                                        <div className="absolute inset-0 rounded-full bg-green-500" />
                                    ) : (
                                        <div className="absolute inset-0 rounded-full bg-yellow-500" />
                                    )}
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex flex-col gap-1">
                                        <div className="flex items-center justify-between gap-2">
                                            <span className="font-medium text-xs truncate">
                                                {query.description || `${query.type.toUpperCase()} Migration: ${query.table}`}
                                            </span>
                                            <div className="flex items-center gap-1.5">
                                                {query.state !== 'executing' && (
                                                    <Button
                                                        size="sm"
                                                        variant="ghost"
                                                        onClick={() => handleExecute(query)}
                                                        className="h-6 px-2 text-xs gap-1"
                                                        disabled={status === 'streaming'}
                                                    >
                                                        {status === 'streaming' ? 'Wait for completion...' : (query.state === 'pending') ? (
                                                            <>
                                                                <Play className="h-3 w-3" />
                                                                Run
                                                            </>
                                                        ) : (
                                                            <>
                                                                <RefreshCw className="h-3 w-3" />
                                                                {query.state === 'error' ? 'Retry' : 'Run Again'}
                                                            </>
                                                        )}
                                                    </Button>
                                                )}
                                                {query.state === 'executing' && (
                                                    <Loader2 className="h-3.5 w-3.5 animate-spin text-blue-500" />
                                                )}
                                                {query.state === 'error' && (
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <AlertCircle className="h-3.5 w-3.5 text-red-500" />
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p className="text-xs">{query.error || 'An error occurred'}</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                )}
                                                {query.state === 'done' && (
                                                    <Check className="h-3.5 w-3.5 text-green-500" />
                                                )}
                                            </div>
                                        </div>
                                        
                                        {/* Error message displayed on its own line */}
                                        {query.state === 'error' && query.error && (
                                            <motion.div 
                                                initial={{ opacity: 0, height: 0 }}
                                                animate={{ opacity: 1, height: 'auto' }}
                                                exit={{ opacity: 0, height: 0 }}
                                                className="relative overflow-hidden mt-1 rounded-md border border-red-500/30"
                                            >
                                                {/* Gradient background similar to file-status-list */}
                                                <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-red-600/20 dark:from-red-900/20 dark:to-red-800/30" />
                                                
                                                <div className="relative z-10 px-2 py-1.5">
                                                    <div className="flex items-center gap-1.5">
                                                        <AlertCircle className="h-3 w-3 text-red-500" />
                                                        <span className="text-[10px] font-medium text-red-600 dark:text-red-400">SQL Error</span>
                                                    </div>
                                                    <p className="text-[10px] text-red-700 dark:text-red-300 mt-1 break-words pl-4">{query.error}</p>
                                                </div>
                                            </motion.div>
                                        )}
                                        
                                        {/* Display SQL comments if available */}
                                        {query.comments && query.comments.length > 0 && (
                                            <div className="mt-1 text-[10px] text-muted-foreground">
                                                {query.comments.map((comment, index) => (
                                                    <div key={index} className="p-1.5 bg-muted/20 rounded-md mb-1 border border-border/30">
                                                        <p className="whitespace-pre-wrap">{comment}</p>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                        
                                        <Collapsible className="w-full">
                                            <CollapsibleTrigger className="flex items-center gap-1 py-1 text-[10px] text-muted-foreground hover:text-foreground">
                                                <ChevronDown className="h-3 w-3" />
                                                View Query
                                            </CollapsibleTrigger>
                                            <CollapsibleContent>
                                                <pre className="text-[10px] leading-4 bg-muted/30 p-2 mt-1 rounded-md overflow-x-auto">
                                                    {query.query}
                                                </pre>
                                            </CollapsibleContent>
                                        </Collapsible>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    ))}
                </AnimatePresence>
            </div>
        </motion.div>
    );
});
