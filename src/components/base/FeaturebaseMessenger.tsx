"use client"; // NextJS 13 requires this. Remove if you are using NextJS 12 or lower
import { useEffect } from "react";
import Script from "next/script";

const FeaturebaseMessenger = () => {
    useEffect(() => {
        const win = window;

        // Initialize Featurebase if it doesn't exist
        if (typeof win.Featurebase !== "function") {
            win.Featurebase = function () {
                (win.Featurebase.q = win.Featurebase.q || []).push(arguments);
            };
        }

        // Boot Featurebase messenger with configuration
        win.Featurebase("boot", {
            appId: process.env.NEXT_PUBLIC_FEATUREBASE_APP_ID,         // required
            // email: "<EMAIL>",         // optional
            // userId: "12345",                   // optional (will be stringified)
            // createdAt: "2025-05-06T12:00:00Z", // optional
            // theme: "light",                    // "light" or "dark"
            // language: "en",                    // short code (e.g. "en", "de", etc.)
            // userHash: generatedToken         // Check the docs for additional details below
            // + feel free to add any more custom values about the user here
        });

        console.log('E', win.Featurebase)
    }, []);

    return (
        <>
            {/* Load the Featurebase SDK */}
            <Script
                src="https://do.featurebase.app/js/sdk.js"
                id="featurebase-sdk"
                strategy="afterInteractive"
            />
        </>
    );
};

export default FeaturebaseMessenger;