'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Settings2 } from 'lucide-react';
import { useLocalStorage } from 'usehooks-ts';

export interface ContinueSettings {
  autoEnabled: boolean;
  messageLimit: number;
  driftDetectionEnabled: boolean;
  useDetailedPrompt: boolean;
}

const defaultSettings: ContinueSettings = {
  autoEnabled: false,
  messageLimit: 5,
  driftDetectionEnabled: true,
  useDetailedPrompt: true,
};

interface ContinueSettingsDialogProps {
  onSettingsChange?: (settings: ContinueSettings) => void;
}

export function ContinueSettingsDialog({ onSettingsChange }: ContinueSettingsDialogProps) {
  const [settings, setSettings] = useLocalStorage<ContinueSettings>(
    'continue-settings',
    defaultSettings
  );
  
  const [open, setOpen] = React.useState(false);

  const handleSettingChange = <K extends keyof ContinueSettings>(
    key: K,
    value: ContinueSettings[K]
  ) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    if (onSettingsChange) {
      onSettingsChange(newSettings);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
          <Settings2 className="h-4 w-4" />
          <span className="sr-only">Continue Settings</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Continue Settings</DialogTitle>
          <DialogDescription>
            Configure how the continue functionality works when AI reaches its context limit.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-continue">Auto Continue</Label>
              <p className="text-xs text-muted-foreground">
                Automatically continue when AI reaches its context limit
              </p>
            </div>
            <Switch
              id="auto-continue"
              checked={settings.autoEnabled}
              onCheckedChange={(checked) => handleSettingChange('autoEnabled', checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="message-limit">Message Limit</Label>
              <p className="text-xs text-muted-foreground">
                Maximum number of messages to send when continuing
              </p>
            </div>
            <Input
              id="message-limit"
              type="number"
              min={1}
              max={10}
              value={settings.messageLimit}
              onChange={(e) => handleSettingChange('messageLimit', parseInt(e.target.value) || 5)}
              className="w-20"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="drift-detection">Drift Detection</Label>
              <p className="text-xs text-muted-foreground">
                Detect and prevent AI from drifting off-topic
              </p>
            </div>
            <Switch
              id="drift-detection"
              checked={settings.driftDetectionEnabled}
              onCheckedChange={(checked) => handleSettingChange('driftDetectionEnabled', checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="detailed-prompt">Detailed Prompt</Label>
              <p className="text-xs text-muted-foreground">
                Use a detailed prompt with instructions for continuing
              </p>
            </div>
            <Switch
              id="detailed-prompt"
              checked={settings.useDetailedPrompt}
              onCheckedChange={(checked) => handleSettingChange('useDetailedPrompt', checked)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => setOpen(false)}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
