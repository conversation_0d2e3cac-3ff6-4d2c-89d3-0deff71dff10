'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRightIcon, Loader2, InfoIcon } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ContinueSettingsDialog, ContinueSettings } from './continue-settings-dialog';
import { useLocalStorage } from 'usehooks-ts';

interface ContinueButtonProps {
  onContinue: () => void;
  isLoading: boolean;
  shouldShow: boolean;
  className?: string;
}

export function ContinueButton({ onContinue, isLoading, shouldShow, className }: ContinueButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [settings, setSettings] = useLocalStorage<ContinueSettings>('continue-settings', {
    autoEnabled: false,
    messageLimit: 5,
    driftDetectionEnabled: true,
    useDetailedPrompt: true,
  });
  
  // Auto-continue functionality
  useEffect(() => {
    if (shouldShow && settings.autoEnabled && !isLoading) {
      const timer = setTimeout(() => {
        handleContinue();
      }, 3000); // 3 second delay before auto-continuing
      
      return () => clearTimeout(timer);
    }
  }, [shouldShow, settings.autoEnabled, isLoading]);
  
  const handleContinue = () => {
    onContinue();
  };
  
  if (!shouldShow) return null;
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: -5 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -5 }}
      className={cn(
        "w-full flex justify-center z-20", // Position at the top of input area
        className
      )}
    >
      <div className="w-full px-2">
        <div 
          className={cn(
            "flex items-center justify-between gap-2 rounded-md px-3 py-1.5 shadow-md w-full",
            "bg-gradient-to-r from-card/95 to-muted/30 border border-border/50 dark:border-white/20",
            "after:absolute after:inset-0 after:bg-gradient-to-b after:from-primary/5 after:via-transparent after:to-primary/5 after:pointer-events-none",
            "after:animate-[shimmer_2s_ease-in-out_infinite] overflow-hidden relative"
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex-1 text-xs">
            <div className="font-medium text-white text-[11px] mb-0.5">Continue to complete your task</div>
            <div className="text-[10px] text-muted-foreground line-clamp-1">
              AI reached its context limit. Continue to keep building.
              {settings.autoEnabled && !isLoading && (
                <span className="ml-1 text-green-400">Auto-continuing in 3s...</span>
              )}
            </div>
          </div>
          
          {/* Settings Dialog */}
          {/*<ContinueSettingsDialog onSettingsChange={setSettings} />*/}
          
          <TooltipProvider>
            <Tooltip delayDuration={300}>
              <TooltipTrigger asChild>
                <div className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
                  <InfoIcon className="h-3 w-3" />
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs text-xs">
                <p>The underlying provider has a limit on the number of tasks it can handle. With new upgrades, as we prioritize reliability, we are doing considerably more work. Don't worry - continuing will retain the context and help you finish your task faster and more reliably in one shot.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <Button 
            onClick={handleContinue}
            disabled={isLoading}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-sm h-7 px-2"
            size="sm"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                <span className="text-[10px]">Continuing...</span>
              </>
            ) : (
              <>
                <span className="text-[10px]">Continue</span>
                <ArrowRightIcon className="ml-1 h-3 w-3" />
              </>
            )}
          </Button>
        </div>
      </div>
    </motion.div>
  )
}

