'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronDown, ChevronRight, Loader2, AlertCircle, Users, UserRound, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Markdown } from './markdown';

interface MultiPerspectiveAnalysisToolResultProps {
  projectId: string;
  chatId: string;
  reason: string;
  state: 'loading' | 'complete' | 'error' | 'partial-call' | 'call' | 'error-call';
  result?: any;
  className?: string;
}

export const MultiPerspectiveAnalysisToolResult = memo(function MultiPerspectiveAnalysisToolResult({ 
  projectId,
  chatId,
  reason,
  state,
  result,
  className
}: MultiPerspectiveAnalysisToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState<'discussion' | 'synthesis'>('discussion');
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);
  
  // Parse the result if it's a string
  let parsedResult: any = null;
  if (typeof result === 'string') {
    try {
      parsedResult = JSON.parse(result);
    } catch (e) {
      console.error('Failed to parse multi-perspective analysis result', e);
    }
  } else {
    parsedResult = result;
  }

  console.log('parsedResult', parsedResult)

  const contributions = parsedResult?.result?.contributions || [];
  const synthesis = parsedResult?.result?.synthesis || '';
  const topic = parsedResult?.result?.topic || 'Topic analysis';
  const personaCount = contributions.length > 0 ? 
    [...new Set(contributions.map((c: any) => c.persona))].length : 0;
  const discussionRounds = contributions.length > 0 ? 
    Math.max(...contributions.map((c: any) => c.round)) : 0;

  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'loading' || state === 'partial-call' ? 'border-amber-600/30 bg-amber-600/5' : 
        state === 'error' || state === 'error-call' ? 'border-red-600/30 bg-red-600/5' : 
        'border-blue-600/30 bg-blue-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'loading' || state === 'partial-call' ? (
              <Loader2 className="h-3 w-3 text-amber-600 animate-spin" />
            ) : state === 'error' || state === 'error-call' ? (
              <AlertCircle className="h-3 w-3 text-red-600" />
            ) : (
              <Users className="h-3 w-3 text-blue-600" />
            )}
            
            <span className="text-foreground">
              {state === 'loading' || state === 'partial-call' ? 'Multi-perspective analysis in progress...' : 
               state === 'error' || state === 'error-call' ? 'Error in multi-perspective analysis' : 
               'Multi-perspective analysis'}
            </span>
          </div>
          
          {(state === 'complete' || state === 'call') && (
            <Badge variant="outline" className="text-[10px] h-4 bg-blue-600/10 text-blue-600 border-blue-600/20">
              {personaCount} perspectives • {discussionRounds} rounds
            </Badge>
          )}
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[2000px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            {/* Topic */}
            <div className="text-sm font-medium text-foreground mb-2">{topic}</div>
            
            {/* Tabs */}
            <div className="flex border-b border-border mb-3">
              <button 
                className={cn(
                  "flex items-center gap-1 px-3 py-1 text-xs font-medium",
                  activeTab === 'discussion' 
                    ? "border-b-2 border-blue-600 text-blue-600" 
                    : "text-muted-foreground hover:text-foreground"
                )}
                onClick={() => setActiveTab('discussion')}
              >
                <MessageCircle className="h-3 w-3" />
                Discussion
              </button>
              <button 
                className={cn(
                  "flex items-center gap-1 px-3 py-1 text-xs font-medium",
                  activeTab === 'synthesis' 
                    ? "border-b-2 border-blue-600 text-blue-600" 
                    : "text-muted-foreground hover:text-foreground"
                )}
                onClick={() => setActiveTab('synthesis')}
              >
                <Users className="h-3 w-3" />
                Synthesis
              </button>
            </div>
            
            {/* Loading state */}
            {(state === 'loading' || state === 'partial-call') && (
              <div className="flex items-center gap-2 mt-4">
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Analyzing from multiple perspectives...</span>
              </div>
            )}
            
            {/* Discussion View */}
            {activeTab === 'discussion' && (state === 'complete' || state === 'call') && contributions.length > 0 && (
              <div className="space-y-4 mt-2 max-h-[600px] overflow-y-auto pr-1">
                {contributions.map((contribution: any, index: number) => (
                  <div key={index} className="border border-border/50 rounded-md p-2 bg-muted/30">
                    <div className="flex items-center gap-1.5 mb-1.5">
                      <UserRound className="h-3 w-3 text-blue-600" />
                      <span className="text-xs font-medium text-foreground">{contribution.persona}</span>
                      <Badge variant="outline" className="text-[10px] h-4 bg-blue-600/5 text-blue-600/80 border-blue-600/20">
                        Round {contribution.round}
                      </Badge>
                    </div>
                    <div className="text-xs prose prose-sm max-w-none">
                      <Markdown>{contribution.content}</Markdown>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {/* Synthesis View */}
            {activeTab === 'synthesis' && (state === 'complete' || state === 'call') && synthesis && (
              <div className="border border-border/50 rounded-md p-3 mt-2 max-h-[600px] overflow-y-auto text-white">
                <div className="prose prose-sm max-w-none text-xs">
                  <Markdown>{synthesis}</Markdown>
                </div>
              </div>
            )}
            
            {/* Error state */}
            {(state === 'error' || state === 'error-call') && parsedResult?.error && (
              <div className="mt-2 text-destructive p-2 border border-destructive/20 rounded-md bg-destructive/5 text-[11px]">
                {parsedResult.error}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
