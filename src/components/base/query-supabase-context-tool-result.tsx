'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { DatabaseIcon, ChevronDown, ChevronRight, Loader2, Table2Icon, FunctionSquareIcon, ShieldIcon, FolderIcon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';

interface ResourceIdentification {
  resourceType: 'table' | 'function' | 'policy' | 'bucket';
  resourceName: string;
  relevanceScore: number;
  reasoning: string;
}

interface QuerySupabaseContextToolResultProps {
  query: string;
  className?: string;
  state: 'partial-call' | 'call' | 'result';
  result: {
    resources?: ResourceIdentification[];
    reasoning?: string;
    details?: {
      tables?: any[];
      functions?: any[];
      policies?: any[];
      buckets?: any[];
    };
  } | string;
}

export const QuerySupabaseContextToolResult = memo(function QuerySupabaseContextToolResult({ 
  query, 
  className,
  state,
  result
}: QuerySupabaseContextToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // Parse result if it's a string
  const parsedResult = typeof result === 'string' ? 
    (() => {
      try {
        return JSON.parse(result);
      } catch (e) {
        return { resources: [], reasoning: '', details: {} };
      }
    })() : result;

  // Count resources by type
  const resourceCounts = {
    tables: parsedResult?.details?.tables?.length || 0,
    functions: parsedResult?.details?.functions?.length || 0,
    policies: parsedResult?.details?.policies?.length || 0,
    buckets: parsedResult?.details?.buckets?.length || 0
  };

  // Get resource icon based on type
  const getResourceIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'table':
        return <Table2Icon className="h-3 w-3 text-blue-500" />;
      case 'function':
        return <FunctionSquareIcon className="h-3 w-3 text-purple-500" />;
      case 'policy':
        return <ShieldIcon className="h-3 w-3 text-amber-500" />;
      case 'bucket':
        return <FolderIcon className="h-3 w-3 text-green-500" />;
      default:
        return <DatabaseIcon className="h-3 w-3 text-gray-500" />;
    }
  };

  return (
    <div
      className={cn(
        'rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'call' ? 'border-green-600/30 bg-green-600/5' : 
        state === 'partial-call' ? 'border-green-600/30 bg-green-600/5' : 
        'border-green-600/30 bg-green-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'call' || state === 'partial-call' ? (
              <Loader2 className="h-3 w-3 text-green-600 animate-spin" />
            ) : (
              <DatabaseIcon className="h-3 w-3 text-green-600" />
            )}
            
            <span className="text-foreground">
              {state === 'call' || state === 'partial-call' 
                ? 'Supabase query running...' 
                : 'Supabase query complete'}
            </span>
          </div>
          
          {state === 'result' && (
            <div className="flex items-center gap-1.5">
              {resourceCounts.tables > 0 && (
                <Badge variant="outline" className="flex items-center gap-0.5 h-5 px-1.5">
                  <Table2Icon className="h-3 w-3" />
                  <span>{resourceCounts.tables}</span>
                </Badge>
              )}
              {resourceCounts.functions > 0 && (
                <Badge variant="outline" className="flex items-center gap-0.5 h-5 px-1.5">
                  <FunctionSquareIcon className="h-3 w-3" />
                  <span>{resourceCounts.functions}</span>
                </Badge>
              )}
              {resourceCounts.policies > 0 && (
                <Badge variant="outline" className="flex items-center gap-0.5 h-5 px-1.5">
                  <ShieldIcon className="h-3 w-3" />
                  <span>{resourceCounts.policies}</span>
                </Badge>
              )}
              {resourceCounts.buckets > 0 && (
                <Badge variant="outline" className="flex items-center gap-0.5 h-5 px-1.5">
                  <FolderIcon className="h-3 w-3" />
                  <span>{resourceCounts.buckets}</span>
                </Badge>
              )}
            </div>
          )}
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            <div className="font-medium mb-1">Query:</div>
            <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
              {query}
            </div>
            
            {state === 'result' && parsedResult?.reasoning && (
              <>
                <div className="font-medium mt-2 mb-1">Reasoning:</div>
                <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md text-[11px]">
                  {parsedResult.reasoning}
                </div>
              </>
            )}
            
            {state === 'result' && parsedResult?.resources && parsedResult.resources.length > 0 && (
              <>
                <div className="font-medium mt-2 mb-1">Relevant Resources:</div>
                <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md text-[11px] max-h-[150px] overflow-y-auto">
                  {parsedResult.resources.map((resource, index) => (
                    <div key={index} className="flex items-center gap-1 mb-1 pb-1 border-b border-border/30 last:border-0">
                      {getResourceIcon(resource.resourceType)}
                      <span className="font-medium">{resource.resourceName}</span>
                      <Badge variant="outline" className="ml-auto h-4 text-[9px]">
                        {resource.resourceType}
                      </Badge>
                      <Badge variant="outline" className="h-4 text-[9px]">
                        {resource.relevanceScore}/10
                      </Badge>
                    </div>
                  ))}
                </div>
              </>
            )}
            
            {state === 'result' && parsedResult?.details && (
              <div className="mt-3">
                <Tabs defaultValue="tables" className="w-full">
                  <TabsList className="grid grid-cols-4 h-7">
                    <TabsTrigger value="tables" className="text-[10px] h-6">Tables</TabsTrigger>
                    <TabsTrigger value="functions" className="text-[10px] h-6">Functions</TabsTrigger>
                    <TabsTrigger value="policies" className="text-[10px] h-6">Policies</TabsTrigger>
                    <TabsTrigger value="buckets" className="text-[10px] h-6">Buckets</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="tables" className="mt-1">
                    {parsedResult.details.tables && parsedResult.details.tables.length > 0 ? (
                      <ScrollArea className="h-[150px]">
                        {parsedResult.details.tables.map((table, index) => (
                          <div key={index} className="mb-2 pb-2 border-b border-border/30 last:border-0">
                            <div className="font-medium">{table.table_name}</div>
                            <div className="text-[10px] mt-1">
                              <table className="w-full border-collapse">
                                <thead>
                                  <tr className="bg-muted/50">
                                    <th className="text-left p-1">Column</th>
                                    <th className="text-left p-1">Type</th>
                                    <th className="text-left p-1">Nullable</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {table.columns && table.columns.map((col, colIdx) => (
                                    <tr key={colIdx} className="even:bg-muted/20">
                                      <td className="p-1">{col.column_name}</td>
                                      <td className="p-1">{col.data_type}</td>
                                      <td className="p-1">{col.is_nullable}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        ))}
                      </ScrollArea>
                    ) : (
                      <div className="text-center py-2 text-muted-foreground">No table details available</div>
                    )}
                  </TabsContent>
                  
                  <TabsContent value="functions" className="mt-1">
                    {parsedResult.details.functions && parsedResult.details.functions.length > 0 ? (
                      <ScrollArea className="h-[150px]">
                        {parsedResult.details.functions.map((func, index) => (
                          <div key={index} className="mb-2 pb-2 border-b border-border/30 last:border-0">
                            <div className="font-medium">{func.name}</div>
                            <div className="grid grid-cols-2 gap-1 mt-1 text-[10px]">
                              <div>
                                <span className="font-medium">Status:</span>{' '}
                                <Badge variant={func.status === 'ACTIVE' ? 'default' : 'secondary'} className="text-[9px] h-4">
                                  {func.status}
                                </Badge>
                              </div>
                              <div>
                                <span className="font-medium">Entrypoint:</span>{' '}
                                <span className="text-muted-foreground">{func.entrypoint_path || 'N/A'}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </ScrollArea>
                    ) : (
                      <div className="text-center py-2 text-muted-foreground">No function details available</div>
                    )}
                  </TabsContent>
                  
                  <TabsContent value="policies" className="mt-1">
                    {parsedResult.details.policies && parsedResult.details.policies.length > 0 ? (
                      <ScrollArea className="h-[150px]">
                        {parsedResult.details.policies.map((policy, index) => (
                          <div key={index} className="mb-2 pb-2 border-b border-border/30 last:border-0">
                            <div className="flex items-center">
                              <span className="font-medium">{policy.policy_name}</span>
                              <Badge className="ml-1 text-[9px] h-4">{policy.command}</Badge>
                            </div>
                            <div className="text-[10px] mt-1">
                              <div><span className="font-medium">Table:</span> {policy.table}</div>
                              <div className="mt-1">
                                <div className="font-medium">Expression:</div>
                                <div className="bg-muted/50 p-1 rounded-sm mt-0.5 font-mono whitespace-pre-wrap">
                                  {policy.expression || 'N/A'}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </ScrollArea>
                    ) : (
                      <div className="text-center py-2 text-muted-foreground">No policy details available</div>
                    )}
                  </TabsContent>
                  
                  <TabsContent value="buckets" className="mt-1">
                    {parsedResult.details.buckets && parsedResult.details.buckets.length > 0 ? (
                      <ScrollArea className="h-[150px]">
                        {parsedResult.details.buckets.map((bucket, index) => (
                          <div key={index} className="mb-2 pb-2 border-b border-border/30 last:border-0">
                            <div className="flex items-center">
                              <span className="font-medium">{bucket.name}</span>
                              <Badge variant={bucket.public ? 'default' : 'outline'} className="ml-1 text-[9px] h-4">
                                {bucket.public ? 'Public' : 'Private'}
                              </Badge>
                            </div>
                            <div className="grid grid-cols-2 gap-1 mt-1 text-[10px]">
                              <div>
                                <span className="font-medium">Created:</span>{' '}
                                <span className="text-muted-foreground">
                                  {new Date(bucket.created_at).toLocaleDateString()}
                                </span>
                              </div>
                              <div>
                                <span className="font-medium">Size Limit:</span>{' '}
                                <span className="text-muted-foreground">
                                  {bucket.file_size_limit 
                                    ? `${Math.round(bucket.file_size_limit / (1024 * 1024))} MB` 
                                    : 'No limit'}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </ScrollArea>
                    ) : (
                      <div className="text-center py-2 text-muted-foreground">No bucket details available</div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            )}
            
            {(state === 'partial-call' || state === 'call') && (
              <div className="flex items-center gap-2 mt-2">
                <div className="animate-pulse h-2 w-2 rounded-full bg-green-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-green-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-green-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Querying Supabase resources...</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
