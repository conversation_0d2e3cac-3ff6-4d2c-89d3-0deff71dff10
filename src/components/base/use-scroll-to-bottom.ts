import { useEffect, useRef, type RefObject } from 'react';

export function useScrollToBottom<T extends HTMLElement>(
  options: {
    scrollOnMessageAdd?: boolean;  // Scroll on new messages
    scrollOnLoad?: boolean;        // Scroll on initial load
  } = {
    scrollOnMessageAdd: true,
    scrollOnLoad: true,
  }
): [RefObject<T>, RefObject<T>] {
  const containerRef = useRef<T>(null);
  const endRef = useRef<T>(null);
  const prevMessageCount = useRef<number>(0);
  const lastContentHeight = useRef<number>(0);
  const isScrollingPaused = useRef<boolean>(false);
  const isNearBottom = useRef<boolean>(true);

  // Initial scroll on component mount
  useEffect(() => {
    const container = containerRef.current;
    const end = endRef.current;

    if (container && end && options.scrollOnLoad) {
      // Use smooth scrolling for better UX
      end.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, []); // Only on mount

  // Main effect for handling scroll behavior
  useEffect(() => {
    const container = containerRef.current;
    const end = endRef.current;

    if (!container || !end || !options.scrollOnMessageAdd) return;

    // Track if user is near the bottom of the container
    const handleScroll = () => {
      if (!container) return;
      
      const { scrollTop, scrollHeight, clientHeight } = container;
      // Consider "near bottom" if within 150px of the bottom
      isNearBottom.current = scrollHeight - (scrollTop + clientHeight) < 150;
      
      // Only pause auto-scrolling if user has manually scrolled up
      isScrollingPaused.current = !isNearBottom.current;
    };

    // Initial scroll position check
    handleScroll();
    container.addEventListener('scroll', handleScroll);

    // Create a more sensitive mutation observer
    const observer = new MutationObserver(() => {
      if (!container || isScrollingPaused.current) return;

      const currentMessageCount = container.children.length;
      const currentContentHeight = container.scrollHeight;
      
      // Detect content changes through multiple means:
      // 1. New messages added (child count changed)
      // 2. Content within existing messages changed (height changed)
      const hasNewMessages = currentMessageCount > prevMessageCount.current;
      const hasContentChanges = currentContentHeight > lastContentHeight.current;
      
      if (hasNewMessages || hasContentChanges) {
        // Update our tracking references
        prevMessageCount.current = currentMessageCount;
        lastContentHeight.current = currentContentHeight;
        
        // Scroll to bottom with smooth behavior for better UX
        end.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    });

    // Initialize tracking values
    prevMessageCount.current = container.children.length;
    lastContentHeight.current = container.scrollHeight;

    // Observe all possible changes that could indicate new content
    observer.observe(container, {
      childList: true,      // Watch for new messages
      subtree: true,        // Watch for changes inside messages
      characterData: true,  // Watch for text changes
      attributes: true,     // Watch for attribute changes that might affect layout
    });

    // Clean up
    return () => {
      observer.disconnect();
      container.removeEventListener('scroll', handleScroll);
    };
  }, [options.scrollOnMessageAdd]);

  return [containerRef, endRef] as [RefObject<T>, RefObject<T>];
}
