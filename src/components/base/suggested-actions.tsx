'use client';

import {motion} from 'framer-motion';
import {ChatRequestOptions, CreateMessage, Message} from 'ai';
import {memo} from 'react';
import {Button} from "@/components/ui/button";

interface SuggestedActionsProps {
    chatId: string;
    append: (
        message: Message | CreateMessage,
        chatRequestOptions?: ChatRequestOptions,
    ) => Promise<string | null | undefined>;
}

function PureSuggestedActions({chatId, append}: SuggestedActionsProps) {
    const suggestedActions = [
        {
            title: 'Task & Habit Tracker',
            label: 'Build a beautiful productivity app',
            action: `Let's create a task and habit tracking app using only React Native Expo features:

Core Features:
- Daily task management with beautiful animations
- Habit streak tracking with progress visualization
- Custom recurring tasks and reminders
- Beautiful charts for productivity insights
- Dark/Light theme support

Design: Clean, minimalist UI with smooth transitions and micro-interactions
Pages: Today, Calendar, Stats, Settings
Key Components: AsyncStorage, Animations, Notifications`,
        },
        {
            title: 'Recipe Collection',
            label: 'Create a personal cookbook app',
            action: `Let's build a recipe collection app with React Native Expo:

Core Features:
- Beautiful recipe cards with images
- Step-by-step cooking instructions
- Ingredient list management
- Cooking timer with notifications
- Offline support

Design: Food-inspired UI with rich imagery and intuitive navigation
Pages: Browse, Recipe Details, Add Recipe, Categories
Key Components: Image handling, Local Storage, Share API`,
        },
        {
            title: 'Personal Journal',
            label: 'Design a mindful journaling experience',
            action: `Let's develop a personal journal app with React Native Expo:

Core Features:
- Rich text entries with image support
- Mood tracking with emojis
- Daily prompts for reflection
- Private lock with biometrics
- Entry search and filtering

Design: Calm, focused UI with subtle animations and typography
Pages: Timeline, Editor, Calendar View, Stats
Key Components: SecureStore, LocalAuthentication, FileSystem`,
        },
        {
            title: 'Workout Timer',
            label: 'Create an interval training companion',
            action: `Let's build a workout timer app with React Native Expo:

Core Features:
- Custom interval timer creation
- Visual and audio cues
- Workout presets and templates
- Background audio support
- Vibration patterns for intervals

Design: High-contrast UI with large numbers and clear indicators
Pages: Timer, Presets, History, Settings
Key Components: Audio, Vibration, Background Tasks`,
        }
    ];

    return (
        <motion.div className="flex flex-col gap-8 px-4 py-8 md:px-8"
                    initial={{opacity: 0, y: 20}}
                    animate={{opacity: 1, y: 0}}
                    transition={{duration: 0.5}}>
            <div className="flex flex-col gap-2">
                <motion.h2
                    className="text-2xl font-bold tracking-tight"

                >
                    Hi!
                </motion.h2>
                <motion.p
                    className="text-md"
                >
                    I'm your AI mobile app developer. Together, we'll build innovative and impactful mobile experiences
                    that users will love.
                </motion.p>
                <motion.p
                    className="text-sm text-muted-foreground"
                >
                    Here are some exciting app ideas we can build together:
                </motion.p>
            </div>

            <motion.div
                className="grid gap-2 md:grid-cols-2"

            >
                {suggestedActions.map((action, i) => (
                    <motion.div
                        key={action.title}
                        className="flex flex-col gap-2"
                    >
                        <Button
                            variant="outline"
                            className="h-auto w-full whitespace-normal p-4 text-left justify-start flex flex-col items-start gap-2"
                            onClick={() => {
                                append({
                                    id: chatId,
                                    content: action.action,
                                    role: 'user',
                                });
                            }}
                        >
                            <span className="font-semibold text-primary">{action.title}</span>
                            <span className="text-sm text-muted-foreground">{action.label}</span>
                        </Button>
                    </motion.div>
                ))}
            </motion.div>
        </motion.div>);
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
