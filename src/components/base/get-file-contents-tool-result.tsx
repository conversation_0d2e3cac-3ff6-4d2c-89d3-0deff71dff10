'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { FileIcon, ChevronDown, ChevronRight, SearchIcon } from 'lucide-react';

interface FileInfo {
  name: string; // Absolute path of the file
}

interface GetFileContentsToolResultProps {
  files: FileInfo[];
  className?: string;
}

export const GetFileContentsToolResult = memo(function GetFileContentsToolResult({ 
  files, 
  className 
}: GetFileContentsToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);
  
  if (files.length === 0) return null;

  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            <SearchIcon className="h-3 w-3 text-muted-foreground" />
            <span className="text-foreground">
              {files.length} file{files.length !== 1 ? 's' : ''} analyzed
            </span>
          </div>
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          {files.map((file, index) => {
            return (
              <div
                key={file.name}
                className={cn(
                  "group relative flex items-center gap-2 rounded-md px-2 py-1 text-xs transition-all hover:bg-muted/50 w-full overflow-hidden",
                  "border border-border/30 hover:border-border/50 bg-card/50"
                )}
              >
                <div className="flex-1 flex items-center gap-2 overflow-hidden relative z-10 min-w-0">
                  <FileIcon className="h-3 w-3 flex-shrink-0 text-muted-foreground" />
                  <span className="truncate font-mono text-[11px] w-full text-foreground/90">
                    {file.name}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
});

// Hook to create a file info object
export function createFileInfo(name: string): FileInfo {
  return { name };
}
