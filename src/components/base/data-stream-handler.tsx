'use client';

import {useChat} from 'ai/react';
import {Message as UIMessage} from 'ai';
import {useEffect, useRef, useState} from 'react';
import {BlockKind} from './block';
import {Message, Suggestion} from '@/lib/db/schema';
import {initialBlockData, useBlock} from '@/hooks/use-block';
import {useUserMessageId} from '@/hooks/use-user-message-id';
import {useAIMessageId} from '@/hooks/use-ai-message-id';
import {cx} from 'class-variance-authority';
import {CodeBlock} from '@/types/file';
import {toast} from 'sonner';


interface FileOpenBlock {
    filename: string
}

interface FileMoveBlock {
    sourcePath: string;
    targetPath: string;
}

interface MessagesUpdate {
    messages: Message[]
}

interface CreditUsageEvent {
    type: 'error-fix' | 'auto-fix';
    message: string;
}

interface ValidationMessage {
    type: 'validating_output' | 'validated_output';
    message: string;
}

interface UpdateUserMessage {
    userMessage: string;
}


type DataStreamDelta = {
    type:
        | 'text-delta'
        | 'code-delta'
        | 'title'
        | 'id'
        | 'suggestion'
        | 'clear'
        | 'finish'
        | 'user-message-id'
        | 'ai-message-id'
        | 'kind'
        | 'file-operation'
        | 'file-move'     // New operation type
        | 'open-file'
        | 'messages-update'
        | 'credit-usage-event'
        | 'validating-output'
        | 'validated-output'
        | 'continuation-flag'
    | 'update-user-message'

    content: string | Suggestion | CodeBlock | FileOpenBlock | FileMoveBlock | MessagesUpdate | CreditUsageEvent | ValidationMessage | ContinuationFlagEvent | UpdateUserMessage;
};

interface ContinuationFlagEvent {
    needsContinuation: boolean;
}

export function DataStreamHandler({id, operationChange, onStreamingComplete, onFileOpen, onContinuationFlagChange, onUpdateUserMessage}: {
    id: string,
    operationChange: (codeBlock: CodeBlock) => any;
    onFileOpen: (fileName: string) => any;
    onContinuationFlagChange?: (needsContinuation: boolean) => void;
    onUpdateUserMessage?: (message: string) => void;
    onStreamingComplete?: () => void;
}) {
    const {data: dataStream, messages, setMessages} = useChat({id, streamProtocol: "text"});
    const {setUserMessageIdFromServer} = useUserMessageId();
    const {setAIMessageIdFromServer} = useAIMessageId();
    const {setBlock} = useBlock();
    const lastProcessedIndex = useRef(-1);
    const [currentOperation, setCurrentOperation] = useState<CodeBlock>({type: null, fileName: '', content: ''});

    useEffect(() => {
        if (!dataStream?.length) return;

        const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
        lastProcessedIndex.current = dataStream.length - 1;

        (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {
            if (delta.type === 'user-message-id') {
                setUserMessageIdFromServer(delta.content as string);
                return;
            }
            if (delta.type === 'ai-message-id') {
                console.log('AI message id', delta.content);
                onStreamingComplete && onStreamingComplete();
                setAIMessageIdFromServer(delta.content as string);
                // setMessages(messages => {
                //     messages[messages.length - 1].id = delta.content as string;
                //     return [...messages];
                // })
                return;
            }
            if (delta.type === "open-file") {
                onFileOpen((delta.content as FileOpenBlock).filename)
                return;
            }
            
            if (delta.type === "file-move") {
                const moveOp = delta.content as FileMoveBlock;
                toast.success(
                    `File moved from ${moveOp.sourcePath} to ${moveOp.targetPath}`,
                    {
                        duration: 4000,
                        position: "top-right",
                        icon: "📂",
                    }
                );
                return;
            }

            if (delta.type === "messages-update") {
                console.log('messages-update', delta.content, typeof delta.content)
                // setMessages(messages => {
                //     (delta.content as MessagesUpdate).messages.reverse().forEach((m, index) => {
                //         // if (messages[messages.length - 1 - index].id.includes("msg-")) {
                //         if(messages[messages.length - 1 - index]) {
                //             messages[messages.length - 1 - index].id = m?.id;
                //             // @ts-ignore
                //             messages[messages.length - 1 - index].parentUserMessageId = m?.parentUserMessageId;
                //             // @ts-ignore
                //             messages[messages.length - 1 - index].parentAssistantMessageId = m?.parentAssistantMessageId;
                //             // @ts-ignore
                //             messages[messages.length - 1 - index].isAssistantGroupHead = m?.isAssistantGroupHead;
                //         }
                //         // }
                //     })
                //
                //     // messages = messages.map(message => {
                //     //     // if (message.id.includes("msg-")) {
                //     //         // find the corresponding user message
                //     //         if (message.role === "user") {
                //     //             (delta.content as MessagesUpdate).messages.find(m => m.role === "user");
                //     //         }
                //     //     // }
                //     //     return message;
                //     // })
                //     return messages;
                // })
                return;
            }
            
            if (delta.type === "credit-usage-event") {
                const creditEvent = delta.content as CreditUsageEvent;
                if (creditEvent.type === "error-fix") {
                    toast.success("Error fixing doesn't consume credits! Keep your app running smoothly.", {
                        duration: 4000,
                        position: "top-right",
                        className: "bg-gradient-to-r from-green-400 to-green-600"
                    });
                } else if (creditEvent.type === "auto-fix") {
                    toast.success("I automatically fixed a small issue for you!", {
                        description: "Your app should now work correctly.",
                        duration: 4000,
                        position: "top-right",
                        className: "bg-gradient-to-r from-green-400 to-green-600"
                    });
                }
                return;
            }
            
            if (delta.type === "continuation-flag") {
                console.log('delta', JSON.stringify(delta))
                const continuationEvent = delta.content as ContinuationFlagEvent;
                console.log(`[DataStreamHandler] Continuation flag: ${continuationEvent?.needsContinuation}`);
                if (onContinuationFlagChange) {
                    onContinuationFlagChange(!!continuationEvent?.needsContinuation);
                }
                return;
            }

            if (delta.type === "update-user-message") {
                if(onUpdateUserMessage) {
                    onUpdateUserMessage((delta.content as UpdateUserMessage)?.userMessage)
                }
                return;
            }
            
            // Handle validation status messages
            if (delta.type === "validating-output" || delta.type === "validated-output") {
                console.log('delta.type', delta.type)
                const validationMessage = delta.content as ValidationMessage;
                
                // Create a special validation message with a distinct visual style
                const validationContent = delta.type === "validating-output" ?
                    `🔍 ${validationMessage.message}` :
                    `✅ ${validationMessage.message}`;
                
                // Add the validation message to the chat as a UIMessage (from 'ai' package)
                const newMessage: UIMessage = {
                    id: `validation-${Date.now()}`,
                    role: 'system',
                    content: validationContent
                };
                
                // Update messages with the new validation message
                setMessages([...messages, newMessage]);
                return;
            }

            if (delta.type === 'file-operation') {
                const op: CodeBlock = delta.content as CodeBlock;
                setCurrentOperation({
                    type: op.type,
                    fileName: op.fileName || op.absolutePath || '',
                    absolutePath: op.absolutePath || op.fileName || '',
                    content: op.content || ''
                });
            }


            // const content = delta.content as string;

            // // Detect file operations
            // if (content.includes('<create_file name="')) {
            //     const fileName = content.match(/name="([^"]+)"/)?.[1];
            //     setCurrentOperation({
            //         type: 'create',
            //         fileName: fileName || '',
            //         content: ''
            //     });
            // } else if (content.includes('<edit_file name="')) {
            //     const fileName = content.match(/name="([^"]+)"/)?.[1];
            //     setCurrentOperation({
            //         type: 'edit',
            //         fileName: fileName || '',
            //         content: ''
            //     });
            // } else if (content.includes('</create_file>') || content.includes('</edit_file>')) {
            //     setCurrentOperation({type: null, fileName: '', content: ''});
            // } else if (currentOperation.type) {
            //     // Accumulate content
            //     setCurrentOperation(prev => ({
            //         ...prev,
            //         content: prev.content + content
            //     }));
            // }

            setBlock((draftBlock) => {
                if (!draftBlock) {
                    return {...initialBlockData, status: 'streaming'};
                }

                switch (delta.type) {
                    case 'id':
                        return {
                            ...draftBlock,
                            documentId: delta.content as string,
                            status: 'streaming',
                        };

                    case 'title':
                        return {
                            ...draftBlock,
                            title: delta.content as string,
                            status: 'streaming',
                        };

                    case 'kind':
                        return {
                            ...draftBlock,
                            kind: delta.content as BlockKind,
                            status: 'streaming',
                        };

                    case 'text-delta':
                        return {
                            ...draftBlock,
                            content: draftBlock.content + (delta.content as string),
                            isVisible:
                                draftBlock.status === 'streaming' &&
                                draftBlock.content.length > 400 &&
                                draftBlock.content.length < 450
                                    ? true
                                    : draftBlock.isVisible,
                            status: 'streaming',
                        };

                    case 'code-delta':
                        return {
                            ...draftBlock,
                            content: delta.content as string,
                            isVisible:
                                draftBlock.status === 'streaming' &&
                                draftBlock.content.length > 300 &&
                                draftBlock.content.length < 310
                                    ? true
                                    : draftBlock.isVisible,
                            status: 'streaming',
                        };

                    case 'clear':
                        return {
                            ...draftBlock,
                            content: '',
                            status: 'streaming',
                        };

                    case 'finish':
                        return {
                            ...draftBlock,
                            status: 'idle',
                        };

                    default:
                        return draftBlock;
                }
            });
        });
    }, [dataStream, setBlock, setUserMessageIdFromServer, setAIMessageIdFromServer]);

    useEffect(() => {
        if (operationChange) {
            operationChange(currentOperation);
        }
    }, [currentOperation]);

    return null;
}
