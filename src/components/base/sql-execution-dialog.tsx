'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, Database, Check, AlertCircle, FileText, Code, RefreshCw } from 'lucide-react';
import type { SQLStatus } from '@/hooks/use-sql-status';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface SQLMeta {
  type: 'up' | 'down' | 'rls';
  source: string;
  table: string;
  description: string;
  state: 'pending' | 'executing' | 'done' | 'error';
  query: string;
  error?: string;
  comments?: string[];
}

interface SQLExecutionDialogProps {
  queries: SQLMeta[];
  messageId: string;
  isOpen: boolean;
  onExecute: (status: SQLMeta) => Promise<string | null>;
  onComplete: () => void;
  setInput?: (text: string) => void; // Add setInput function to allow setting chat input
}

// Helper function to extract comments from SQL query
function extractSQLComments(query: string): string[] {
  const comments: string[] = [];
  
  // Match SQL comments that start with -- Purpose: or -- This or just --
  const commentRegex = /--\s*(?:Purpose|This)?[^\n]*(?:\n\s*--[^\n]*)*\n?/g;
  let match;
  
  while ((match = commentRegex.exec(query)) !== null) {
    // Clean up the comment by removing leading -- and trimming
    const commentBlock = match[0];
    const cleanedComment = commentBlock
      .split('\n')
      .map(line => line.replace(/^\s*--\s*/, '').trim())
      .filter(line => line.length > 0)
      .join('\n');
      
    if (cleanedComment) {
      comments.push(cleanedComment);
    }
  }
  
  return comments;
}

// Helper function to get a summary of what's happening
function getSummaryFromComments(queries: SQLMeta[]): string[] {
  const summaries: string[] = [];
  
  // Extract all comments and find the most meaningful ones
  queries.forEach(query => {
    const comments = query.comments || [];
    if (comments.length > 0) {
      // Get the first line of each comment as a summary
      const firstLines = comments.map(comment => {
        const lines = comment.split('\n');
        return lines[0];
      });
      
      summaries.push(...firstLines);
    }
  });
  
  // Remove duplicates and return
  return [...new Set(summaries)];
}

export function SQLExecutionDialog({
  queries,
  messageId,
  isOpen,
  onExecute,
  onComplete,
  setInput
}: SQLExecutionDialogProps) {
  // State for tracking execution
  const [internalQueries, setInternalQueries] = useState<SQLMeta[]>([]);
  const [executing, setExecuting] = useState(false);
  const [completed, setCompleted] = useState(false);
  const [currentQueryIndex, setCurrentQueryIndex] = useState(-1);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Initialize internal state when dialog opens
  useEffect(() => {
    if (isOpen && queries.length > 0) {
      // Extract comments from queries and enhance the query objects
      const enhancedQueries = queries.map(q => {
        const comments = extractSQLComments(q.query);
        return { ...q, comments };
      });
      
      setInternalQueries(enhancedQueries);
      setExecuting(false);
      setCompleted(false);
      setCurrentQueryIndex(-1);
      setError(null);
      setActiveTab('overview');
    }
  }, [isOpen, queries]);

  // Execute all queries in sequence
  const executeAllQueries = async () => {
    setExecuting(true);
    setError(null);
    
    for (let i = 0; i < internalQueries.length; i++) {
      setCurrentQueryIndex(i);
      
      // Update the current query to executing state
      setInternalQueries(prev => 
        prev.map((q, idx) => 
          idx === i ? { ...q, state: 'executing' } : q
        )
      );
      
      try {
        // Execute the query
        const errorMessage = await onExecute(internalQueries[i]);
        
        if (errorMessage) {
          // Update query state to error
          setInternalQueries(prev => 
            prev.map((q, idx) => 
              idx === i ? { ...q, state: 'error', error: errorMessage } : q
            )
          );
          setError(`Failed to update your database: ${errorMessage}`);
          break;
        } else {
          // Update query state to done
          setInternalQueries(prev => 
            prev.map((q, idx) => 
              idx === i ? { ...q, state: 'done' } : q
            )
          );
        }
      } catch (err: any) {
        // Handle unexpected errors
        const errorMessage = err.message || 'An unexpected error occurred';
        setInternalQueries(prev => 
          prev.map((q, idx) => 
            idx === i ? { ...q, state: 'error', error: errorMessage } : q
          )
        );
        setError(`Failed to update your database: ${errorMessage}`);
        break;
      }
    }
    
    setExecuting(false);
    
    // If no errors occurred, mark as completed
    if (!error) {
      setCompleted(true);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (completed || error) {
      onComplete();
    }
  };
  
  // Handle sending error to chat for AI assistance
  const handleFixWithAI = () => {
    // Format the error message for the AI
    const errorQueries = internalQueries.filter(q => q.state === 'error');
    let errorMessage = "I'm getting the following error(s) when trying to update my database:\n\n";
    
    errorQueries.forEach((query, index) => {
      errorMessage += `Error ${index + 1}: ${query.error}\n`;
      errorMessage += `When executing: \`${query.query}\`\n\n`;
    });
    
    errorMessage += "Can you help me fix this issue?";
    
    // Set the input text in the chat
    if (setInput) {
      setInput(errorMessage);
    }
    
    // Close the dialog
    onComplete();
  };

  // Calculate overall progress
  const progress = internalQueries.reduce((acc, query) => {
    if (query.state === 'done') return acc + 1;
    return acc;
  }, 0);
  
  const progressPercentage = internalQueries.length > 0 
    ? Math.round((progress / internalQueries.length) * 100) 
    : 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        className="max-w-3xl"
        // Only allow closing if completed or error occurred
        disableCloseButton={!completed && !error && executing}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2.5 text-base">
            <Database className="h-5 w-5 text-primary" />
            <span>Database Update Needed</span>
          </DialogTitle>
          <DialogDescription className="text-sm mt-1">
            {!error ? (
              completed ? 
                "Your database has been successfully updated!" :
                "We need to update your database to continue."
            ) : (
              "We encountered an issue updating your database."
            )}
          </DialogDescription>
        </DialogHeader>

        {/* Progress bar */}
        <div className="relative h-2 w-full bg-secondary rounded-full overflow-hidden mt-2">
          <motion.div
            className="absolute top-0 left-0 h-full bg-primary"
            initial={{ width: '0%' }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>

        {/* Tabs for Overview and Queries */}
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview" className="flex items-center gap-1.5 py-1.5 h-9">
              <FileText className="h-4 w-4" />
              <span className="text-sm">Overview</span>
            </TabsTrigger>
            <TabsTrigger value="queries" className="flex items-center gap-1.5 py-1.5 h-9">
              <Code className="h-4 w-4" />
              <span className="text-sm">Queries</span>
            </TabsTrigger>
          </TabsList>
          
          {/* Overview tab - shows summary of changes */}
          <TabsContent value="overview" className="mt-3 max-h-[300px] overflow-y-auto">
            {/* Status indicators with comments inline */}
            <div className="space-y-3">
              {internalQueries.map((query, index) => {
                const comments = query.comments || [];
                return (
                  <motion.div
                    key={`${query.type}-${query.table}-status`}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                    className={cn(
                      "flex flex-col rounded-md border p-1.5",
                      query.state === 'error' 
                        ? "border-red-500/30" 
                        : query.state === 'done'
                          ? "border-green-500/30"
                          : query.state === 'executing'
                            ? "border-blue-500/30"
                            : "border-border/30"
                    )}
                  >
                    <div className="flex items-center gap-2">
                      {/* Status indicator */}
                      {query.state === 'executing' ? (
                        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                      ) : query.state === 'done' ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : query.state === 'error' ? (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      ) : (
                        <div className="h-4 w-4 rounded-full border border-muted-foreground/30" />
                      )}
                      
                      {/* Query name */}
                      <span className="text-sm font-medium">
                        {getNonTechnicalDescription(query)}
                      </span>
                    </div>
                    
                    {/* Show all comments */}
                    {comments.length > 0 && (
                      <div className="ml-6 mt-1.5 text-xs text-muted-foreground">
                        {comments.map((comment, i) => (
                          <p key={i} className="mb-1 whitespace-pre-wrap">{comment}</p>
                        ))}
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
            
            {/* Error summary - even more compact */}
            {error && (
              <div className="mt-2 flex items-center gap-1.5 text-xs text-red-500">
                <AlertCircle className="h-3 w-3" />
                <span>Error updating database.</span>
                <button 
                  onClick={() => setActiveTab('queries')} 
                  className="text-[10px] text-red-600 hover:text-red-500 underline ml-1"
                >
                  Details
                </button>
              </div>
            )}
          </TabsContent>
          
          {/* Queries tab - shows detailed query information */}
          <TabsContent value="queries" className="mt-3 max-h-[300px] overflow-y-auto">
            <div className="space-y-3">
              {internalQueries.map((query, index) => (
                <div
                  key={`${query.type}-${query.table}`}
                  className={cn(
                    "rounded-md border text-card-foreground",
                    query.state === 'error' 
                      ? "border-red-500/30" 
                      : query.state === 'done'
                        ? "border-green-500/30"
                        : query.state === 'executing'
                          ? "border-blue-500/30"
                          : "border-border/30"
                  )}
                >
                  {/* Header with status */}
                  <div className="flex items-center gap-2 p-2.5 border-b border-border/20">
                    {/* Status indicator */}
                    {query.state === 'executing' ? (
                      <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                    ) : query.state === 'done' ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : query.state === 'error' ? (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    ) : (
                      <div className="h-4 w-4 rounded-full border border-muted-foreground/30" />
                    )}

                    {/* Query description */}
                    <span className="font-medium text-sm">
                      {getNonTechnicalDescription(query)}
                    </span>
                  </div>
                  
                  {/* Content section */}
                  <div className="p-3 text-xs">
                    {/* Display SQL comments if available */}
                    {query.comments && query.comments.length > 0 && (
                      <div className="mb-1.5 text-muted-foreground">
                        {query.comments.map((comment, index) => (
                          <p key={index} className="whitespace-pre-wrap">{comment}</p>
                        ))}
                      </div>
                    )}
                    
                    {/* SQL query - more compact */}
                    <pre className="bg-muted/30 p-2.5 rounded-md overflow-x-auto text-xs leading-relaxed">
                      {query.query}
                    </pre>
                    
                    {/* Error message - inline and compact */}
                    {query.state === 'error' && query.error && (
                      <div className="mt-2 text-red-500 text-xs">
                        <span className="font-medium">Error:</span> {query.error}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          {/* Run button - only show if not executing and not completed */}
          {!executing && !completed && !error && (
            <Button
              onClick={executeAllQueries}
              className="w-full flex"
              size="lg"
            >
              Update Database
            </Button>
          )}
          
          {/* Executing button - only show if executing */}
          {executing && !completed && !error && (
            <Button
              disabled
              className="w-full flex"
              size="lg"
            >
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Updating Database...
            </Button>
          )}
          
          {/* Retry button - only show if there's an error */}
          {error && (
            <div className="flex gap-2 w-full flex-1">
              <Button
                onClick={handleFixWithAI}
                variant="destructive"
                className="flex-1"
                size="lg"
              >
                Fix with AI
              </Button>
              <Button 
                onClick={handleClose}
                size="lg"
                variant="secondary"
                className=""
              >
                Close
              </Button>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Helper function to generate user-friendly descriptions
function getNonTechnicalDescription(query: SQLStatus): string {
  // Get the source from the query (e.g., SUPABASE)
  const source = query.source || '';
  const dbName = source.charAt(0).toUpperCase() + source.slice(1).toLowerCase();
  
  // For most users, show a simple database update message
  return `Updating ${dbName} database`;
}

// This function is no longer needed since we're using a simpler approach
// But keeping it in case we want to revert to more detailed descriptions later
function formatTableName(table: string): string {
  if (!table) return 'application';
  return table;
}
