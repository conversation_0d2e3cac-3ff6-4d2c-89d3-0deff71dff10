'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { FileEditIcon, ChevronDown, ChevronRight, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface EditFileToolResultProps {
  absolutePath: string;
  className?: string;
  state: 'partial-call' | 'call' | 'result';
  result: string;
}

export const EditFileToolResult = memo(function EditFileToolResult({ 
  absolutePath, 
  className,
  state,
  result
}: EditFileToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // Extract filename from path for display
  const fileName = absolutePath?.split('/')?.pop() || absolutePath;

  return (
    <div
      className={cn(
        'rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'partial-call' ? 'border-amber-600/30 bg-amber-600/5' : 
        state === 'call' ? 'border-amber-600/30 bg-amber-600/5' : 
        'border-green-600/30 bg-green-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'partial-call' || state === 'call' ? (
              <Loader2 className="h-3 w-3 text-amber-600 animate-spin" />
            ) : (
              <FileEditIcon className="h-3 w-3 text-green-600" />
            )}
            
            <span className="text-foreground">
              {state === 'partial-call' || state === 'call' ? `Editing ${fileName || ''}...` : `Updated ${fileName || ''}`}
            </span>
          </div>
          
          {state === 'result' && (
            <Badge variant="outline" className="text-[10px] h-4">
              File updated
            </Badge>
          )}
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            <div className="font-medium mb-1">File path:</div>
            <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
              {absolutePath}
            </div>

            {(state === 'partial-call' || state === 'call') && (
              <div className="flex items-center gap-2 mt-2">
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Applying changes...</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
