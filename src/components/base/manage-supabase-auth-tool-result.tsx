'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Terminal, ChevronDown, ChevronRight, Loader2, Settings } from 'lucide-react';

interface ManageSupabaseAuthToolResultProps {
  reason: string;
  state: 'loading' | 'complete' | 'error';
  action?: 'getAuthConfig' | 'updateAuthConfig' | 'getSSOProviders';
  result?: any;
  className?: string;
}

export const ManageSupabaseAuthToolResult = memo(function ManageSupabaseAuthToolResult({ 
  reason,
  state,
  action,
  result,
  className
}: ManageSupabaseAuthToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);
  
  // Parse the result if it's a string
  let parsedResult: any = null;
  if (typeof result === 'string') {
    try {
      parsedResult = JSON.parse(result);
    } catch (e) {
      console.error('Failed to parse Supabase auth result', e);
    }
  } else {
    parsedResult = result;
  }

  const timestamp = parsedResult?.timestamp || new Date().toISOString();
  const actionType = action || parsedResult?.action || 'unknown';

  // Get a summary of the result based on action type
  const getResultSummary = () => {
    if (state === 'error') return 'Error';
    if (state === 'loading') return 'Processing...';
    
    switch (actionType) {
      case 'getAuthConfig':
        return 'Auth config retrieved';
      case 'updateAuthConfig':
        return 'Auth config updated';
      case 'getSSOProviders':
        return parsedResult?.result?.ssoProviders?.length > 0 
          ? `${parsedResult.result.ssoProviders.length} SSO providers found` 
          : 'No SSO providers configured';
      default:
        return 'Operation completed';
    }
  };

  // Get a title for the operation
  const getOperationTitle = () => {
    switch (actionType) {
      case 'getAuthConfig':
        return 'Supabase auth configuration retrieved';
      case 'updateAuthConfig':
        return 'Supabase auth configuration updated';
      case 'getSSOProviders':
        return 'Supabase SSO providers retrieved';
      default:
        return 'Supabase auth operation';
    }
  };

  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'loading' ? 'border-amber-600/30 bg-amber-600/5' : 
        state === 'error' ? 'border-red-600/30 bg-red-600/5' : 
        'border-blue-600/30 bg-blue-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'loading' ? (
              <Loader2 className="h-3 w-3 text-amber-600 animate-spin" />
            ) : state === 'error' ? (
              <Terminal className="h-3 w-3 text-red-600" />
            ) : (
              <Settings className="h-3 w-3 text-blue-600" />
            )}
            
            <span className="text-foreground">
              {state === 'loading' ? 'Managing Supabase auth...' : 
               state === 'error' ? 'Error managing Supabase auth' : 
               getOperationTitle()}
            </span>
          </div>
          
          {state === 'complete' && (
            <Badge variant="outline" className="text-[10px] h-4">
              {getResultSummary()}
            </Badge>
          )}
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            <div className="font-medium mb-1">Reason for auth operation:</div>
            <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
              {reason}
            </div>
            
            <div className="mt-2">
              <div className="font-medium mb-1">Action:</div>
              <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
                {actionType}
              </div>
            </div>
            
            <div className="mt-2">
              <div className="font-medium mb-1">Timestamp:</div>
              <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
                {new Date(timestamp).toLocaleString()}
              </div>
            </div>
            
            {state === 'complete' && parsedResult && (
              <div className="mt-2">
                <div className="font-medium mb-1">Result:</div>
                <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px] max-h-[200px] overflow-auto">
                  {JSON.stringify(parsedResult.result, null, 2)}
                </div>
              </div>
            )}
            
            {state === 'loading' && (
              <div className="flex items-center gap-2 mt-2">
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Processing Supabase auth operation...</span>
              </div>
            )}
            
            {state === 'error' && parsedResult?.error && (
              <div className="mt-2">
                <div className="font-medium mb-1 text-red-500">Error:</div>
                <div className="text-red-500 bg-red-50 dark:bg-red-950/30 p-1.5 rounded-md font-mono text-[11px]">
                  {parsedResult.error}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
