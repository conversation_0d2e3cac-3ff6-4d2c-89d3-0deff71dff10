'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { SearchIcon, ChevronDown, ChevronRight, Code2Icon, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface QueryCodebaseToolResultProps {
  query: string;
  excludedFiles?: string[];
  className?: string;
  state: 'partial-call' | 'call' | 'result';
  result: string;
}

export const QueryCodebaseToolResult = memo(function QueryCodebaseToolResult({ 
  query, 
  excludedFiles = [],
  className,
  state,
    result
}: QueryCodebaseToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);


  return (
    <div
      className={cn(
        'rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'call' ? 'border-amber-600/30 bg-amber-600/5' : 
        state === 'partial-call' ? 'border-amber-600/30 bg-amber-600/5' : 
        'border-green-600/30 bg-green-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'call' || state === 'partial-call' ? (
              <Loader2 className="h-3 w-3 text-amber-600 animate-spin" />
            ) : (
              <Code2Icon className="h-3 w-3 text-green-600" />
            )}
            
            <span className="text-foreground">
              {state === 'call' || state === 'partial-call' ? 'Conjurer query running...' : 'Conjurer query complete'}
            </span>
          </div>
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            <div className="font-medium mb-1">Query:</div>
            <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
              {query}
            </div>
            
            {excludedFiles && excludedFiles?.length > 0 && Array.isArray(excludedFiles) && (
              <>
                <div className="font-medium mt-2 mb-1">Excluded files:</div>
                <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px] max-h-[100px] overflow-y-auto">
                  {excludedFiles?.map((file, index) => (
                    <div key={index} className="truncate">
                      {file}
                    </div>
                  ))}
                </div>
              </>
            )}

            
            {(state === 'partial-call' || state === 'call') && (
              <div className="flex items-center gap-2 mt-2">
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Running Conjurer query...</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
