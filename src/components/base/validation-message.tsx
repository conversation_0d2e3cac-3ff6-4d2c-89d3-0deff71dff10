'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Search } from 'lucide-react';

interface ValidationMessageProps {
  content: string;
  type: 'validating' | 'validated';
}

export const ValidationMessage = ({ content, type }: ValidationMessageProps) => {
  const isValidating = type === 'validating';
  
  return (
    <motion.div
      className="w-full mx-auto max-w-3xl px-4 group/message"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
      data-role="system"
    >
      <div className="flex gap-4 w-full py-3 px-4 rounded-lg bg-muted/30 border border-muted">
        <div className="size-6 flex items-center rounded-full justify-center shrink-0 bg-primary/10">
          {isValidating ? (
            <Search size={14} className="text-primary animate-pulse" />
          ) : (
            <CheckCircle size={14} className="text-green-500" />
          )}
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col">
            <span className="text-foreground text-sm">
              {content}
            </span>
            
            {isValidating && (
              <div className="flex gap-1 mt-2">
                <motion.div 
                  className="h-1 w-16 bg-primary/50 rounded-full"
                  animate={{ 
                    width: ["25%", "100%", "25%"],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity, 
                    ease: "easeInOut" 
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};
