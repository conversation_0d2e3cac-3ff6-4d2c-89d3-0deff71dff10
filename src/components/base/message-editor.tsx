'use client';

import {ChatRequestOptions, Message} from 'ai';
import {Dispatch, SetStateAction, useEffect, useRef, useState} from 'react';
import {toast} from 'sonner';
import {useUserMessageId} from '@/hooks/use-user-message-id';
import {Textarea} from "@/components/ui/textarea";
import {Button} from "@/components/ui/button";
import {ConfirmationDialog} from "@/components/ui/confirmation-dialog";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";

export type MessageEditorProps = {
    message: Message;
    chatId: string;
    setMode: Dispatch<SetStateAction<'view' | 'edit'>>;
    setMessages: (
        messages: Message[] | ((messages: Message[]) => Message[]),
    ) => void;
    reload: (
        chatRequestOptions?: ChatRequestOptions,
    ) => Promise<string | null | undefined>;
    setInput: (value: string) => void;
};

export const MessageEditor = observer(({
                                  message,
                                  setMode,
                                  setMessages,
                                  reload,
                                  chatId,
                                  setInput
                              }: MessageEditorProps) => {
    const {userMessageIdFromServer} = useUserMessageId();
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

    const [draftContent, setDraftContent] = useState<string>(message.content);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const {generatorStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);

    if(!session){
        toast.error("Session is not initialized! Please contact support and tell the error code ENOQUOTE_EDIT.")
        return;
    }

    useEffect(() => {
        if (textareaRef.current) {
            adjustHeight();
        }
    }, []);

    const adjustHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
        }
    };

    const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setDraftContent(event.target.value);
        adjustHeight();
    };

    const handleMessageDelete = async () => {
        // setIsSubmitting(true);
        // const messageId = userMessageIdFromServer ?? message.id;
        //
        // if (!messageId) {
        //     toast.error('Something went wrong, please try again!');
        //     setIsSubmitting(false);
        //     return;
        // }
        //
        // const result = await session?.deleteMessage(messageId);
        // console.log('result', result)
        // if (result !== "ok") {
        //     return;
        // }
        //
        // setMessages((messages) => {
        //     const index = messages.findIndex((m) => m.id === message.id);
        //
        //     if (index !== -1) {
        //         const updatedMessage = {
        //             ...message,
        //             content: draftContent,
        //         };
        //
        //         return [...messages.slice(0, index), updatedMessage];
        //     }
        //
        //     return messages;
        // });
        // setMode('view');
        // setIsSubmitting(false);
        // setInput(draftContent || '');
    };

    return (
        <div className="flex flex-col gap-2 w-full">
            <Textarea
                ref={textareaRef}
                className="bg-transparent outline-none overflow-hidden resize-none !text-base rounded-xl w-full"
                value={draftContent}
                onChange={handleInput}
            />

            <div className="flex flex-row gap-2 justify-end">
                <Button
                    variant="outline"
                    className="h-fit py-2 px-3"
                    onClick={() => {
                        setMode('view');
                    }}
                >
                    Cancel
                </Button>
                <ConfirmationDialog
                    title="Revert app to this checkpoint?"
                    description="This will reset the message and the file states to this checkpoint. All changes after this message will be lost."
                    onConfirm={handleMessageDelete}
                    variant="destructive"
                    confirmText="Revert"
                    trigger={
                        <Button
                            variant="destructive"
                            className="h-fit py-2 px-3"
                            disabled={isSubmitting}
                        >
                            Revert
                        </Button>
                    }
                />

            </div>
        </div>
    );
});
