import Link from 'next/link';
import React, { memo, useMemo, useState } from 'react';
import ReactMarkdown, { type Components } from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { CodeBlock } from './code-block';
import cn from 'classnames';

const components: Partial<Components> = {
  // @ts-expect-error
  code: CodeBlock,
  pre: ({ children }) => <div className="whitespace-pre-wrap break-words">{children}</div>,
  ol: ({ node, children, ...props }) => {
    return (
      <ol className="list-decimal text-sm list-outside ml-3 space-y-1 mb-1" {...props}>
        {children}
      </ol>
    );
  },
  li: ({ node, children, ...props }) => {
    const hasNestedList = React.Children.toArray(children).some(
      child => React.isValidElement(child) && (child.type === 'ol' || child.type === 'ul')
    );
    
    return (
      <li 
        className={cn(
          "leading-normal text-sm",
          hasNestedList ? "mb-1" : "mb-0.5",
          // Check if it's a number-only item (like 1. 2. etc)
          typeof children === 'string' && /^\d+\.\s*$/.test(children) ? 'text-sm font-medium text-zinc-700 dark:text-zinc-300' : '',
          // Check if it's a short item (like single words)
          typeof children === 'string' && children.length < 20 ? 'text-zinc-700 dark:text-zinc-300 text-sm' : '',
        )}
        {...props}
      >
        {children}
      </li>
    );
  },
  ul: ({ node, children, ...props }) => {
    return (
      <ul className="list-disc text-sm list-outside ml-3 space-y-1 mb-1" {...props}>
        {children}
      </ul>
    );
  },
  strong: ({ node, children, ...props }) => {
    return (
      <span className="font-semibold" {...props}>
        {children}
      </span>
    );
  },
  a: ({ node, children, ...props }) => {
    return (
      // @ts-expect-error
      <Link
        className="text-blue-500 hover:underline"
        target="_blank"
        rel="noreferrer"
        {...props}
      >
        {children}
      </Link>
    );
  },
  h1: ({ node, children, ...props }) => {
    return (
      <h1 className="text-xl font-semibold mt-5 mb-2" {...props}>
        {children}
      </h1>
    );
  },
  h2: ({ node, children, ...props }) => {
    return (
      <h2 className="text-lg font-semibold mt-4 mb-2" {...props}>
        {children}
      </h2>
    );
  },
  h3: ({ node, children, ...props }) => {
    return (
      <h3 className="text-lg font-semibold mt-4 mb-1.5" {...props}>
        {children}
      </h3>
    );
  },
  h4: ({ node, children, ...props }) => {
    return (
      <h4 className="text-base font-semibold mt-3 mb-1.5" {...props}>
        {children}
      </h4>
    );
  },
  h5: ({ node, children, ...props }) => {
    return (
      <h5 className="text-sm font-semibold mt-3 mb-1" {...props}>
        {children}
      </h5>
    );
  },
  h6: ({ node, children, ...props }) => {
    return (
      <h6 className="text-sm font-semibold mt-2.5 mb-1" {...props}>
        {children}
      </h6>
    );
  },
  p: ({ node, children, ...props }) => {
    // Check if the content looks like an ASCII tree
    const checkForAsciiTree = (content: any): boolean => {
      if (Array.isArray(content)) {
        return content.some(child =>
            typeof child === 'string'
                ? child.includes('├─') || child.includes('└─') || child.includes('│')
                : checkForAsciiTree(child)
        );
      }
      return typeof content === 'string' && (
          content.includes('├─') || content.includes('└─') || content.includes('│')
      );
    };

    const isAsciiTree = checkForAsciiTree(children);

    return (
      <p
        className={`${
          isAsciiTree ? 'my-2 text-sm leading-5 tracking-[-0.01em] whitespace-pre-wrap break-words text-zinc-700 dark:text-zinc-300' : 'my-1.5 text-sm leading-relaxed'
        }`}
        {...props}
      >
        {children}
      </p>
    );
  },
};

const remarkPlugins = [remarkGfm];

const NonMemoizedMarkdown = ({ children }: { children: string }) => {
  return (
    <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>
      {children}
    </ReactMarkdown>
  );
};

export const Markdown = memo(
  NonMemoizedMarkdown,
  (prevProps, nextProps) => prevProps.children === nextProps.children,
);
