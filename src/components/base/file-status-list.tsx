import React, { memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { FileStatus } from '@/hooks/use-file-status';
import {Loader2} from "lucide-react";

interface FileStatusListProps {
  files: FileStatus[];
  className?: string;
  messageId?: string;
  onFileClick?: (messageId: string) => void;
  isVersionActive?: boolean;
}

export const FileStatusList = memo(function FileStatusList({ 
  files, 
  className,
  messageId,
  onFileClick,
  isVersionActive
}: FileStatusListProps) {
  if (files.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      onClick={() => onFileClick && messageId && onFileClick(messageId)}
      className={cn(
        'mt-4 cursor-pointer rounded-lg bg-gradient-to-b p-4 backdrop-blur-sm shadow-lg relative overflow-hidden',
        isVersionActive
          ? 'from-card/95 to-muted/30 border-accent/30 ring-1 ring-accent/10' 
          : 'from-card/95 to-muted/30 border-border/50 dark:border-white/20',
        'border',
        'after:absolute after:inset-0 after:bg-gradient-to-b after:from-primary/5 after:via-transparent after:to-primary/5 after:pointer-events-none',
        'after:animate-[shimmer_2s_ease-in-out_infinite]',
        className
      )}
    >
      <div className="space-y-1">
        <div className="flex justify-between items-center mb-2 ml-1">
          <span className="text-xs font-medium text-white">Version Control</span>
          {isVersionActive && (
            <span className="text-[10px] font-medium px-1.5 py-0.5 rounded-full bg-accent text-accent-foreground/80">Active</span>
          )}
        </div>
        <AnimatePresence mode="popLayout">
          {files.map((file) => (
            <motion.div
              key={file.path}
              initial={{ opacity: 0, height: 0, scale: 1 }}
              animate={{ opacity: 1, height: 'auto', scale: 1 }}
              exit={{ opacity: 0, height: 0, scale: 0.95 }}
              transition={{ 
                duration: 0.2,
                scale: {
                  type: "spring",
                  damping: 15,
                  stiffness: 300
                }
              }}
              className="group relative flex items-center gap-2 rounded-md bg-background/80 px-3 py-1 text-xs text-card-foreground hover:bg-background/90 transition-all hover:shadow-md border border-border/20 hover:border-border/30 cursor-pointer"
            >
              {/* Progress bar background */}
              <div
                className={cn(
                  "absolute top-0 left-0 h-full bg-gradient-to-r transition-all duration-300 ease-out rounded-md",
                  file.mode === 'edit'
                    ? 'from-amber-100/30 to-amber-200/40 dark:from-amber-900/20 dark:to-amber-800/30'
                    : file.mode === 'create'
                      ? 'from-emerald-100/30 to-emerald-200/40 dark:from-emerald-900/20 dark:to-emerald-800/30'
                      : file.mode === 'fix'
                        ? 'from-purple-100/30 to-purple-200/40 dark:from-purple-900/20 dark:to-purple-800/30'
                        : 'from-gray-100/30 to-gray-200/40 dark:from-gray-900/20 dark:to-gray-800/30'
                )}
                style={{
                  width: `${file.percentage || 0}%`,
                  zIndex: 0
                }}
              />

              <div className="flex-1 flex items-center gap-2 overflow-hidden relative z-10">
                <div className="relative h-2 w-2 flex items-center justify-center">
                  {file.state === 'generating' ? (
                    <>
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                    </>
                  ) : (
                    <div className="absolute inset-0 rounded-full bg-green-500" />
                  )}
                </div>
                <span className="truncate font-mono text-[11px]">
                  {file.path}
                </span>
              </div>
              {
                file.state === "generating" ?
                    <Loader2 className="h-3 w-3 rounded-full animate-spin" />
                   :
                    <span className={cn(
                        'px-1.5 py-0.5 rounded-md text-[8px] font-medium uppercase tracking-wider',
                        file.mode === 'edit'
                            ? ' text-amber-600  dark:text-amber-400'
                            : file.mode === 'create'
                                ? ' text-emerald-600  dark:text-emerald-400'
                                : file.mode === 'fix'
                                    ? ' text-purple-600 dark:text-purple-400'
                                    : 'text-gray-600  dark:text-gray-400'
                    )}>
                        {file.mode || 'Generated'}
                    </span>
              }

              <span
                  className="text-[8px] tabular-nums text-muted-foreground group-hover:text-card-foreground transition-colors relative z-10">
                {file.lineCount || 0}
              </span>

            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </motion.div>
  );
});
