'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { TerminalSquare, ChevronDown, ChevronRight, Loader2 } from 'lucide-react';

interface GetClientLogsToolResultProps {
  reason: string;
  state: 'loading' | 'complete' | 'error';
  type?: string;
  source?: string;
  result?: any;
  className?: string;
}

export const GetClientLogsToolResult = memo(function GetClientLogsToolResult({ 
  reason,
  state,
  type,
  source,
  result,
  className
}: GetClientLogsToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);
  
  // Parse the result if it's a string
  let parsedResult: any = null;
  if (typeof result === 'string') {
    try {
      parsedResult = JSON.parse(result);
    } catch (e) {
      console.error('Failed to parse client logs result', e);
    }
  } else {
    parsedResult = result;
  }

  const timestamp = parsedResult?.timestamp || new Date().toISOString();
  const logType = type || parsedResult?.type || 'all';
  const logSource = source || parsedResult?.source || 'all';

  // Get the log count if available
  const getLogCount = () => {
    if (parsedResult?.count !== undefined) {
      return parsedResult.count;
    } else if (Array.isArray(parsedResult?.logs)) {
      return parsedResult.logs.length;
    }
    return 0;
  };
  
  // Get a summary of the logs
  const getLogSummary = () => {
    const count = getLogCount();
    if (count === 0) return 'No logs found';
    return `${count} log entries retrieved`;
  };

  // Format log type for display
  const formatLogType = (type: string) => {
    return type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Format log source for display
  const formatLogSource = (source: string) => {
    return source === 'all' ? 'All Sources' : source.charAt(0).toUpperCase() + source.slice(1);
  };

  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'loading' ? 'border-amber-600/30 bg-amber-600/5' : 
        state === 'error' ? 'border-red-600/30 bg-red-600/5' : 
        'border-green-600/30 bg-green-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'loading' ? (
              <Loader2 className="h-3 w-3 text-amber-600 animate-spin" />
            ) : state === 'error' ? (
              <TerminalSquare className="h-3 w-3 text-red-600" />
            ) : (
              <TerminalSquare className="h-3 w-3 text-green-600" />
            )}
            
            <span className="text-foreground">
              {state === 'loading' ? 'Fetching client logs...' : 
               state === 'error' ? 'Error fetching client logs' : 
               `Client logs retrieved`}
            </span>
          </div>
          
          {state === 'complete' && (
            <Badge variant="outline" className="text-[10px] h-4">
              {getLogSummary()}
            </Badge>
          )}
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            <div className="font-medium mb-1">Reason for fetching logs:</div>
            <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
              {reason}
            </div>
            
            <div className="mt-2 flex gap-2">
              <div>
                <div className="font-medium mb-1">Log Type:</div>
                <Badge variant="secondary" className="text-[10px]">
                  {formatLogType(logType)}
                </Badge>
              </div>
              
              <div>
                <div className="font-medium mb-1">Source:</div>
                <Badge variant="secondary" className="text-[10px]">
                  {formatLogSource(logSource)}
                </Badge>
              </div>
            </div>
            
            <div className="mt-2">
              <div className="font-medium mb-1">Timestamp:</div>
              <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
                {new Date(timestamp).toLocaleString()}
              </div>
            </div>
            
            {state === 'loading' && (
              <div className="flex items-center gap-2 mt-2">
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Retrieving client logs...</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

export default GetClientLogsToolResult;
