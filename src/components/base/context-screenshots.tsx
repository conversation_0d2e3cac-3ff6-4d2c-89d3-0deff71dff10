'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { XIcon, ZoomInIcon, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

// Add custom CSS for hiding scrollbars while maintaining functionality
import './hide-scrollbar.css';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { observer } from 'mobx-react-lite';
import { ComponentContext } from '@/types/component-context';

interface ContextScreenshotsProps {
  screenshots: ComponentContext[];
  onRemove?: (id: string) => void;
  onClear?: () => void;
}

export const ContextScreenshots = observer(({ 
  screenshots,
  onRemove = () => {},
  onClear = () => {}
}: ContextScreenshotsProps) => {
  // State for the preview dialog
  const [previewImage, setPreviewImage] = useState<ComponentContext | null>(null);
  
  if (screenshots.length === 0) return null;
  
  const handleOpenPreview = (screenshot: ComponentContext) => {
    console.log('Opening preview for', screenshot);
    setPreviewImage(screenshot);
  };

  return (
    <>
      <div className="flex flex-col mb-1 mt-1 rounded-md bg-black/30 backdrop-blur-md border border-white/10 overflow-hidden">
        <div className="flex items-center justify-between px-2 py-1 border-b border-white/5">
          <div className="flex items-center gap-1">
            <span className="text-[10px] font-medium text-white/70">Component Context</span>
            <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-blue-500/20 text-blue-400">
              {screenshots.length}
            </span>
          </div>
          <button 
            className="text-[10px] text-white/50 hover:text-white/80 transition-colors"
            onClick={onClear}
            type="button"
          >
            Clear All
          </button>
        </div>
        <div className="flex overflow-x-auto gap-0 py-1 px-1 hide-scrollbar">
          {screenshots.map((screenshot) => (
            <div 
              key={screenshot.id} 
              className="group relative flex-shrink-0 w-[72px] rounded bg-black/20 hover:bg-black/30 transition-all border border-white/10 hover:border-white/20 overflow-hidden cursor-pointer mr-1"
            >
              {/* Main clickable area */}
              <div 
                className="flex flex-col cursor-pointer"
                onClick={() => handleOpenPreview(screenshot)}
              >
                {/* Component thumbnail */}
                <div className="relative h-10 w-full overflow-hidden">
                  {screenshot.isUploading ? (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                      <Loader2 className="h-4 w-4 animate-spin text-white/70" />
                    </div>
                  ) : screenshot.imageUrl ? (
                    <div 
                      className="absolute inset-0 bg-cover bg-center"
                      style={{ backgroundImage: `url(${screenshot.imageUrl})` }}
                    />
                  ) : screenshot.screenshot && screenshot.screenshot.length > 0 ? (
                    <div 
                      className="absolute inset-0 bg-cover bg-center"
                      style={{ backgroundImage: `url(data:image/jpeg;base64,${screenshot.screenshot.substring(0, 100)})` }}
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 border-t border-white/10">
                      <span className="text-[8px] text-white/70">Code context</span>
                    </div>
                  )}
                </div>
                
                {/* Component info */}
                <div className="px-1 py-0.5 flex flex-col bg-black/30">
                  <span className="font-medium truncate text-[9px] leading-tight text-white" title={screenshot.componentName}>
                    {screenshot.componentName}
                  </span>
                  <span className="text-[7px] text-white/60 truncate font-mono leading-tight" title={screenshot.sourceFile}>
                    {screenshot.sourceFile.split('/').pop() || screenshot.sourceFile}
                  </span>
                </div>
              </div>
              
              {/* Controls overlay */}
              <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                {/* Zoom button - centered */}
                <button
                  className="rounded-full bg-black/50 p-1.5 z-10"
                  onClick={() => handleOpenPreview(screenshot)}
                  title="Zoom in"
                  type="button"
                >
                  <ZoomInIcon className="h-3.5 w-3.5 text-white/90 hover:text-white" />
                </button>
                
                {/* Remove button */}
                <button 
                  className="absolute top-0.5 left-0.5 rounded-full bg-black/50 text-white/80 hover:text-white z-10 p-0.5"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('Removing screenshot with id:', screenshot.id);
                    onRemove(screenshot.id);
                  }}
                  title="Remove component"
                  type="button"
                >
                  <XIcon className="h-2.5 w-2.5" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Preview Dialog */}
      <Dialog 
        open={previewImage !== null} 
        onOpenChange={(open) => {
          console.log('Dialog open state changed to:', open);
          if (!open) setPreviewImage(null);
        }}
        modal={true}
      >
        <DialogContent className="sm:max-w-md p-0 overflow-hidden">
          {previewImage && (
            <div className="flex flex-col">
              {/* Full size image or code context */}
              <div className="relative w-full h-[300px]">
                {previewImage.imageUrl ? (
                  <Image 
                    src={previewImage.imageUrl}
                    alt={previewImage.componentName}
                    className="object-contain"
                    fill
                    unoptimized={true}
                  />
                ) : previewImage.screenshot && previewImage.screenshot.length > 0 ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <img 
                      src={`data:image/jpeg;base64,${previewImage.screenshot}`}
                      alt={previewImage.componentName}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                ) : (
                  <div className="w-full h-full flex flex-col items-center justify-center bg-black/10 border border-white/10">
                    <div className="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-xs mb-2">Code Context</div>
                    <span className="text-sm text-white/70 mb-1">Component selected from code</span>
                    <span className="text-xs text-white/50">No screenshot available</span>
                  </div>
                )}
              </div>
              
              {/* Component details */}
              <div className="p-4 bg-background">
                <h3 className="text-lg font-medium mb-1">{previewImage.componentName}</h3>
                <p className="text-sm text-muted-foreground font-mono">{previewImage.sourceFile}</p>
                <p className="text-xs text-muted-foreground mt-1">Line: {previewImage.lineNumber}</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
});
