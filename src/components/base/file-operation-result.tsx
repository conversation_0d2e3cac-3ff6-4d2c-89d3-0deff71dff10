import React from 'react';
import { memo } from 'react';
import { FileIcon, FileEditIcon, FilePlusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";

interface FileOperationResultProps {
  type: 'create' | 'edit';
  absolutePath: string;
  version?: string;
  modifiedFiles?: string[];
}

function PureFileOperationResult({
  type,
  absolutePath,
  version = 'v1',
  modifiedFiles = [],
}: FileOperationResultProps) {
  const {generatorStore} = useStores();
  return (
    <div className="text-white rounded-lg p-2 w-full underline" onClick={() => generatorStore.togglePreview(true)}>
      <div className="flex items-center gap-2 text-sm">
        {type === 'create' ? (
          <FilePlusIcon className="w-4 h-4" />
        ) : (
          <FileEditIcon className="w-4 h-4" />
        )}
        <span>{absolutePath}</span>
        {/*<span className="text-muted-foreground">{version}</span>*/}
      </div>
      {/*{modifiedFiles.length > 0 && (*/}
      {/*  <div className="mt-2 space-y-1">*/}
      {/*    {modifiedFiles.map((file) => (*/}
      {/*      <div key={file} className="flex items-center gap-2 text-sm text-muted-foreground">*/}
      {/*        <div className="w-4 h-4 flex items-center justify-center">•</div>*/}
      {/*        <span>Modified {file}</span>*/}
      {/*      </div>*/}
      {/*    ))}*/}
      {/*  </div>*/}
      {/*)}*/}
    </div>
  );
}

export const FileOperationResult = observer(PureFileOperationResult);
