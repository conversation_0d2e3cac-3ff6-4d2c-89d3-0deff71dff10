'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ExternalLink, Smartphone, ChevronDown, ChevronRight, Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface DesignToolResultProps {
  projectId: string;
  chatId: string;
  reason: string;
  state: 'loading' | 'complete' | 'error' | 'partial-call' | 'call' | 'error-call';
  result?: any;
  className?: string;
}

export const DesignToolResult = memo(function DesignToolResult({ 
  projectId,
  chatId,
  reason,
  state,
  result,
  className
}: DesignToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const router = useRouter();
  
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);
  
  // Parse the result if it's a string
  let parsedResult: any = null;
  if (typeof result === 'string') {
    try {
      parsedResult = JSON.parse(result);
    } catch (e) {
      console.error('Failed to parse design result', e);
    }
  } else {
    parsedResult = result;
  }

  const screenCount = parsedResult?.screenCount || 0;
  const currentProjectScreens = parsedResult?.currentProjectScreens || 0;
  const maxAllowedScreens = parsedResult?.maxAllowedScreens || 0;
  const remainingScreens = parsedResult?.remainingScreens || 0;
  const timestamp = new Date().toISOString();
  
  const viewDesigns = useCallback(() => {
    router.push(`/project/${projectId}/chat/${chatId}/design`);
  }, [router, projectId, chatId]);

  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        state === 'loading' || state === 'partial-call' ? 'border-amber-600/30 bg-amber-600/5' : 
        state === 'error' || state === 'error-call' ? 'border-red-600/30 bg-red-600/5' : 
        'border-purple-600/30 bg-purple-600/5',
        className
      )}
    >
      <div>
        <div 
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            
            {state === 'loading' || state === 'partial-call' ? (
              <Loader2 className="h-3 w-3 text-amber-600 animate-spin" />
            ) : state === 'error' || state === 'error-call' ? (
              <AlertCircle className="h-3 w-3 text-red-600" />
            ) : (
              <Smartphone className="h-3 w-3 text-purple-600" />
            )}
            
            <span className="text-foreground">
              {state === 'loading' || state === 'partial-call' ? 'Generating app designs...' : 
               state === 'error' || state === 'error-call' ? 'Error generating designs' : 
               'App design screens created'}
            </span>
          </div>
          
          {(state === 'complete' || state === 'call') && (
            <Badge variant="outline" className="text-[10px] h-4 bg-purple-600/10 text-purple-600 border-purple-600/20">
              {screenCount} screens created
            </Badge>
          )}
        </div>
        
        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            {/* Design request section removed as requested */}
            
            {(state === 'loading' || state === 'partial-call') && (
              <div className="flex items-center gap-2 mt-4">
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600"></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.2s' }}></div>
                <div className="animate-pulse h-2 w-2 rounded-full bg-amber-600" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-muted-foreground">Creating beautiful app designs...</span>
              </div>
            )}
            
            {/* Only showing loading state or error, button removed as requested */}
            
            {(state === 'error' || state === 'error-call') && parsedResult?.error && (
              <div className="mt-2 text-destructive p-2 border border-destructive/20 rounded-md bg-destructive/5 text-[11px]">
                {parsedResult.error}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
