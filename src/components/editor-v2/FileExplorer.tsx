'use client';

import React from 'react';
import {
    FolderIcon, 
    FileIcon, 
    ChevronDownIcon, 
    FileJsonIcon, 
    FileTextIcon, 
    ImageIcon,
    FileCodeIcon
} from 'lucide-react';
import {cn} from '@/lib/utils';
import {FileNodeV2} from "@/types/editor-v2";

interface DirectoryNode {
    name: string;
    type: 'directory';
    children: TreeNode[];
}

type TreeNode = FileNodeV2 | DirectoryNode;

interface FileExplorerProps {
    files: FileNodeV2[];
    activeFile?: string;
    onFileSelect: (file: FileNodeV2) => void;
    indentSize?: number;
}

// Helper function to get the appropriate icon for a file
const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    // Create a custom icon with extension label
    const FileExtensionIcon = ({ ext, color }: { ext: string, color: string }) => (
        <div className="relative flex items-center justify-center">
            <FileIcon className={`h-3.5 w-3.5 flex-shrink-0 ${color}`} />
            <span className="absolute text-[6px] font-bold text-white top-[6px]">
                {ext.toUpperCase()}
            </span>
        </div>
    );
    
    switch (extension) {
        case 'js':
            return <FileExtensionIcon ext="js" color="text-yellow-400" />;
        case 'jsx':
            return <FileExtensionIcon ext="jsx" color="text-yellow-500" />;
        case 'ts':
            return <FileExtensionIcon ext="ts" color="text-blue-400" />;
        case 'tsx':
            return <FileExtensionIcon ext="tsx" color="text-blue-500" />;
        case 'json':
            return <FileExtensionIcon ext="json" color="text-yellow-300" />;
        case 'md':
            return <FileExtensionIcon ext="md" color="text-gray-400" />;
        case 'png':
            return <FileExtensionIcon ext="png" color="text-purple-400" />;
        case 'jpg':
        case 'jpeg':
            return <FileExtensionIcon ext="jpg" color="text-purple-400" />;
        case 'gif':
            return <FileExtensionIcon ext="gif" color="text-purple-400" />;
        case 'svg':
            return <FileExtensionIcon ext="svg" color="text-purple-400" />;
        case 'css':
            return <FileExtensionIcon ext="css" color="text-blue-300" />;
        case 'scss':
            return <FileExtensionIcon ext="scss" color="text-pink-400" />;
        case 'sass':
            return <FileExtensionIcon ext="sass" color="text-pink-400" />;
        default:
            // For unknown extensions, show the actual extension if it exists
            return extension ? 
                <FileExtensionIcon ext={extension.length > 4 ? extension.substring(0, 3) : extension} color="text-gray-400" /> : 
                <FileIcon className="h-3.5 w-3.5 flex-shrink-0" />;
    }
};

const FileExplorerItem: React.FC<{
    node: TreeNode;
    level: number;
    activeFile?: string;
    onFileSelect: (file: FileNodeV2) => void;
    indentSize: number;
}> = ({node, level, activeFile, onFileSelect, indentSize}) => {
    const [isOpen, setIsOpen] = React.useState(true);
    const isDirectory = node.type === 'directory';
    const isActive = !isDirectory && activeFile === node.name;

    return (
        <div>
            <button
                onClick={() => {
                    if (isDirectory) {
                        setIsOpen(!isOpen);
                    } else {
                        onFileSelect(node as FileNodeV2);
                    }
                }}
                className={cn(
                    'w-full flex items-center justify-between text-[13px] py-[2px] px-1 hover:bg-[#2A2E37] transition-colors group',
                    isActive && 'bg-[#2A2E37]',
                    level === 0 && 'mt-0.5'
                )}
            >
                <div className="flex items-center min-w-0">
                    {/* Arrow container with fixed width */}
                    <div className="w-4 flex justify-center mr-1" style={{marginLeft: `${level * indentSize}px`}}>
                        {isDirectory && (
                            <ChevronDownIcon
                                className={cn('h-3.5 w-3.5 flex-shrink-0 transition-transform', !isOpen && '-rotate-90')}
                            />
                        )}
                    </div>
                    
                    {/* Icon with fixed position */}
                    <div className="w-4 flex justify-center mr-1">
                        {isDirectory ? (
                            <FolderIcon className="h-3.5 w-3.5 flex-shrink-0 text-[#7982FF]"/>
                        ) : (
                            getFileIcon(node.name)
                        )}
                    </div>
                    
                    {/* Filename */}
                    <span className={cn('text-[#ABB2BF] truncate', isActive && 'text-white')}>
                        {node.name}
                    </span>
                </div>
                {!isDirectory && (node as any).lines && (
                    <span className="hidden md:flex text-[11px] text-[#6B727F] opacity-0 group-hover:opacity-100 transition-opacity">
                        {(node as any).lines} lines
                    </span>
                )}
            </button>
            {isDirectory && isOpen && (node as DirectoryNode).children && (
                <div>
                    {(node as DirectoryNode).children.map((child) => (
                        <FileExplorerItem
                            key={child.name}
                            node={child}
                            level={level + 1}
                            activeFile={activeFile}
                            onFileSelect={onFileSelect}
                            indentSize={indentSize}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

function buildFileTree(files: FileNodeV2[]): DirectoryNode {
    const root: DirectoryNode = {name: '', type: 'directory', children: []};

    files.forEach(file => {
        if (!file.name) return;

        const parts = file.name.split('/');
        let current = root;

        // Create path
        for (let i = 0; i < parts.length - 1; i++) {
            const part = parts[i];
            let found = current.children.find(
                child => child.type === 'directory' && child.name === part
            ) as DirectoryNode;

            if (!found) {
                found = {name: part, type: 'directory', children: []};
                current.children.push(found);
            }
            current = found;
        }

        // Add file to final directory
        current.children.push({
            name: parts[parts.length - 1],
            absolutePath: file.name,
            type: 'file',
            language: file.language,
            content: file.content,
            lines: file.content.split('\n').length
        } as any);
    });

    // Sort children
    const sortNodes = (node: DirectoryNode) => {
        node.children.sort((a, b) => {
            if (a.type === b.type) {
                return a.name.localeCompare(b.name);
            }
            return a.type === 'directory' ? -1 : 1;
        });

        node.children.forEach(child => {
            if (child.type === 'directory') {
                sortNodes(child);
            }
        });
    };

    sortNodes(root);
    return root;
}

export const FileExplorerV2: React.FC<FileExplorerProps> = ({
    files,
    activeFile,
    onFileSelect,
    indentSize = 16, // Default indent size of 16px
}) => {
    const fileTree = React.useMemo(() => buildFileTree(files), [files]);

    return (
        <div className="w-[240px] h-full bg-[#21252B] border-r border-[#181A1F] overflow-y-auto text-[13px]">
            {fileTree.children.map((node) => (
                <FileExplorerItem
                    key={node.name}
                    node={node}
                    level={0}
                    activeFile={activeFile}
                    onFileSelect={onFileSelect}
                    indentSize={indentSize}
                />
            ))}
        </div>
    );
};
