'use client';

import React, {useCallback, useEffect, useMemo, useState} from 'react';
import { FileExplorerV2 } from './FileExplorer';
import { EditorLayout } from '../generator/EditorLayout';
import { ActiveFile, FileItemV2, FileNodeV2, FileOperation, getDirectoryFromPath, getFileNameFromPath } from '@/types/editor-v2';
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import {FileNode} from "@/types/file";
import { useResizablePanels } from '@/hooks/use-resizable-panels';
import { useIsMobile } from '@/hooks/use-mobile';
import { ChevronRightIcon, ChevronLeftIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EditorProps {
    chatId: string
    onFileOperation?: (operation: FileOperation) => void;
    key?: string; // Add key to force re-render
}

export const EditorV2: React.FC<EditorProps> = observer(({
    onFileOperation,
    chatId
}) => {
    const {generatorStore} = useStores();
    const session = generatorStore.getActiveSession(chatId);

    if (!session) {
        return;
    }
    // Helper function to find a file by path
    const findFileByPath = (items: (FileItemV2)[], path: string): FileNodeV2 | null => {
        for (const item of items) {
            if ((item.absolutePath || item.name) === path) {
                return item;
            }
        }
        return null;
    };

    // Track only the active file path, not the content
    const [activeFilePath, setActiveFilePath] = useState<string | null>(null);

    // Get active file from current files
    const activeFile = useMemo(() => {
        if (!activeFilePath) {
            // If no active file, get the first file
            const firstFile = session.v2FileTree[0] as FileNodeV2;
            if (firstFile) {
                setActiveFilePath(firstFile.absolutePath);
                return {
                    name: firstFile.name,
                    absolutePath: firstFile.absolutePath,
                    content: firstFile.content,
                    language: firstFile.language,
                };
            }
            return null;
        }

        // Find the current active file
        const current = findFileByPath(session.v2FileTree as any, activeFilePath);
        return current ? {
            name: current.name,
            absolutePath: current.absolutePath,
            content: current.content,
            language: current.language,
        } : null;
    }, [session.v2FileTree, activeFilePath]);

    const handleFileSelect = useCallback((file: FileNodeV2) => {
        setActiveFilePath(file.absolutePath);
    }, []);

    const handleFileChange = useCallback((newContent: string | undefined) => {
        if (!activeFile || !newContent) return;

        // Create the file operation
        const operation: FileOperation = {
            type: 'edit',
            absolutePath: activeFile.absolutePath,
            content: newContent,
            language: activeFile.language,
            name: activeFile?.name
        };

        // Notify parent
        onFileOperation?.(operation);
    }, [activeFile, onFileOperation]);

    const createFile = useCallback((path: string, content: string = '', language: string = 'typescript') => {
        const operation: FileOperation = {
            type: 'create',
            absolutePath: path,
            content,
            language,
        };

        // Notify parent to handle the file creation
        onFileOperation?.(operation);
        
        // Set the new file as active
        setActiveFilePath(path);
    }, [onFileOperation]);

    const { leftWidth, handleMouseDown, isDragging } = useResizablePanels({
        initialLeftWidth: 240,
        minLeftWidth: 180,
        maxLeftWidth: 400,
        persistKey: 'editor-file-explorer'
    });
    const isMobileView = useIsMobile();
    const [showFileExplorer, setShowFileExplorer] = useState(!isMobileView);

    return (
        <div className="flex h-full relative">
            {/* Mobile file explorer toggle button */}
            {isMobileView && (
                <button 
                    onClick={() => setShowFileExplorer(!showFileExplorer)}
                    className="absolute top-2 left-2 z-10 p-1 bg-[#21252B] rounded-md border border-[#181A1F] shadow-md"
                >
                    {showFileExplorer ? 
                        <ChevronLeftIcon className="h-4 w-4 text-[#ABB2BF]" /> : 
                        <ChevronRightIcon className="h-4 w-4 text-[#ABB2BF]" />
                    }
                </button>
            )}
            
            {/* File Explorer - conditionally shown on mobile */}
            <div 
                className={cn(
                    "h-full transition-all duration-300 ease-in-out",
                    isMobileView && !showFileExplorer ? "w-0 opacity-0" : "",
                    isMobileView && showFileExplorer ? "absolute z-10 left-0 top-0 shadow-lg" : ""
                )}
                style={!isMobileView ? { width: `${leftWidth}px` } : {}}
            >
                {(showFileExplorer || !isMobileView) && (
                    <FileExplorerV2
                        files={session.v2FileTree as any}
                        activeFile={activeFile?.absolutePath}
                        onFileSelect={handleFileSelect}
                    />
                )}
            </div>
            
            {/* Resizer - only shown on desktop */}
            {!isMobileView && (
                <div
                    className={cn(
                        "w-1 h-full bg-[#181A1F] hover:bg-blue-500 cursor-col-resize transition-colors z-10",
                        isDragging && "bg-blue-500"
                    )}
                    onMouseDown={handleMouseDown}
                />
            )}
            
            {/* Editor */}
            <div className={cn(
                "h-full transition-all duration-300 ease-in-out",
                isMobileView ? "flex-1" : ""
            )} style={!isMobileView ? { width: `calc(100% - ${leftWidth}px - 4px)` } : {}}>
                {activeFile && (
                    <EditorLayout
                        path={activeFile.name}
                        content={activeFile.content}
                        language={activeFile.language}
                        onChange={handleFileChange}
                    />
                )}
            </div>
        </div>
    );
});
