'use client';

import React from 'react';
import { observer } from 'mobx-react-lite';
import { Project } from '@/lib/db/schema';
import { Badge } from '@/components/ui/badge';
import { CheckCircle2, XCircle } from 'lucide-react';

interface ConnectionStatusProps {
  project: Project;
}

export const ConnectionStatus = observer(({ project }: ConnectionStatusProps) => {
  const isConnected = !!project?.supabaseProjectId && !!project?.connectionId;
  
  return (
    <div className="p-4 border-b flex items-center justify-between">
      <div className="flex items-center gap-2">
        <img 
          src="/icons/integrations/supabase.png" 
          alt="Supabase" 
          className="h-6 w-6" 
        />
        <h2 className="text-sm font-medium">Supabase Integration</h2>
      </div>
      
      {isConnected ? (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
          <CheckCircle2 className="h-3 w-3" />
          <span>Connected</span>
        </Badge>
      ) : (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          <span>Not Connected</span>
        </Badge>
      )}
    </div>
  );
});
