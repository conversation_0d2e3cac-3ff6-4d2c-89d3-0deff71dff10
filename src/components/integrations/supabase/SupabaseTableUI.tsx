'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Search, PlusCircle, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import './SupabaseTableUI.css';

// Dummy data representing records from a 'users' table
const dummyData = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active', last_login: '2023-10-26T10:00:00Z', avatar: '/avatars/01.png' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Editor', status: 'Active', last_login: '2023-10-25T15:30:00Z', avatar: '/avatars/02.png' },
  { id: 3, name: 'Charlie Brown', email: '<EMAIL>', role: 'Viewer', status: 'Inactive', last_login: '2023-09-12T11:20:00Z', avatar: '/avatars/03.png' },
  { id: 4, name: 'Diana Prince', email: '<EMAIL>', role: 'Editor', status: 'Active', last_login: '2023-10-26T09:00:00Z', avatar: '/avatars/04.png' },
  { id: 5, name: 'Ethan Hunt', email: '<EMAIL>', role: 'Admin', status: 'Active', last_login: '2023-10-24T18:45:00Z', avatar: '/avatars/05.png' },
];

const TableViewHeader = () => (
  <div className="flex flex-col md:flex-row justify-between items-center mb-6 space-y-4 md:space-y-0">
    <div>
      <h1 className="text-2xl font-bold text-gray-800">Users</h1>
      <p className="text-sm text-gray-500">Manage your user data with ease.</p>
    </div>
    <div className="flex items-center space-x-2">
      <div className="relative w-full md:w-64">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input placeholder="Search users..." className="pl-10" />
      </div>
      <Select defaultValue="all">
        <SelectTrigger className="w-[120px]">
          <SelectValue placeholder="Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Statuses</SelectItem>
          <SelectItem value="active">Active</SelectItem>
          <SelectItem value="inactive">Inactive</SelectItem>
        </SelectContent>
      </Select>
      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
        <PlusCircle className="h-4 w-4 mr-2" />
        Add User
      </Button>
    </div>
  </div>
);

const RecordCard = ({ record }) => (
  <Card className="record-card transition-all hover:shadow-lg">
    <CardContent className="p-4 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <img src={record.avatar} alt={record.name} className="h-12 w-12 rounded-full" />
        <div>
          <p className="font-semibold text-gray-800">{record.name}</p>
          <p className="text-sm text-gray-500">{record.email}</p>
        </div>
      </div>
      <div className="hidden md:flex items-center space-x-6">
        <div className="text-center">
          <p className="text-xs text-gray-500">Role</p>
          <p className="font-medium text-gray-700">{record.role}</p>
        </div>
        <div className="text-center">
          <p className="text-xs text-gray-500">Status</p>
          <span className={`px-2 py-1 text-xs rounded-full ${record.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {record.status}
          </span>
        </div>
        <div className="text-center">
          <p className="text-xs text-gray-500">Last Login</p>
          <p className="font-medium text-gray-700">{new Date(record.last_login).toLocaleDateString()}</p>
        </div>
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem><Edit className="mr-2 h-4 w-4" /> Edit</DropdownMenuItem>
          <DropdownMenuItem className="text-red-600"><Trash2 className="mr-2 h-4 w-4" /> Delete</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </CardContent>
  </Card>
);

const TablePagination = () => (
    <Pagination className="mt-6">
        <PaginationContent>
            <PaginationItem>
                <PaginationPrevious href="#" />
            </PaginationItem>
            <PaginationItem>
                <PaginationLink href="#">1</PaginationLink>
            </PaginationItem>
            <PaginationItem>
                <PaginationLink href="#" isActive>
                    2
                </PaginationLink>
            </PaginationItem>
            <PaginationItem>
                <PaginationLink href="#">3</PaginationLink>
            </PaginationItem>
            <PaginationItem>
                <PaginationNext href="#" />
            </PaginationItem>
        </PaginationContent>
    </Pagination>
);

export const SupabaseTableUI = () => {
  const [sort, setSort] = useState({ column: 'name', direction: 'asc' });

  const sortedData = [...dummyData].sort((a, b) => {
    if (a[sort.column] < b[sort.column]) return sort.direction === 'asc' ? -1 : 1;
    if (a[sort.column] > b[sort.column]) return sort.direction === 'asc' ? 1 : -1;
    return 0;
  });

  const handleSort = (column) => {
    setSort(prevSort => ({
      column,
      direction: prevSort.column === column && prevSort.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const SortableHeader = ({ children, columnName }) => (
    <Button variant="ghost" onClick={() => handleSort(columnName)} className="text-xs text-gray-500 font-semibold uppercase tracking-wider">
      {children}
      {sort.column === columnName && (sort.direction === 'asc' ? <ChevronUp className="h-4 w-4 ml-1" /> : <ChevronDown className="h-4 w-4 ml-1" />)}
    </Button>
  );

  return (
    <div className="h-full w-full bg-gray-50 p-6 overflow-y-auto">
      <TableViewHeader />
      <div className="hidden md:flex justify-between items-center px-4 py-2">
        <SortableHeader columnName="name">User</SortableHeader>
        <div className="flex-1 grid grid-cols-3 text-center">
            <SortableHeader columnName="role">Role</SortableHeader>
            <SortableHeader columnName="status">Status</SortableHeader>
            <SortableHeader columnName="last_login">Last Login</SortableHeader>
        </div>
        <div className="w-8"></div>
      </div>
      <div className="space-y-3">
        {sortedData.map(record => <RecordCard key={record.id} record={record} />)}
      </div>
      <TablePagination />
    </div>
  );
};
