'use client';

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Copy, ExternalLink, RefreshCw, ChevronDown, ChevronRight, Terminal, Check } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toast } from 'sonner';

interface FunctionsListProps {
  projectId: string;
}

// Helper function to render function logs
const renderFunctionLogs = (functionId: string, supabaseStore: any, projectId: string) => {
  const details = supabaseStore.getFunctionDetails(functionId);
  
  if (!details) {
    return (
      <div className="p-4 text-sm text-muted-foreground">
        Click to load logs...
      </div>
    );
  }
  
  if (details.loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <RefreshCw className="animate-spin h-4 w-4 text-muted-foreground mr-2" />
        <span className="text-sm">Loading logs...</span>
      </div>
    );
  }
  
  if (details.error) {
    return (
      <div className="p-4 text-sm text-destructive">
        Error loading logs: {details.error}
        <Button 
          variant="link" 
          size="sm" 
          className="p-0 h-auto ml-2"
          onClick={() => supabaseStore.fetchFunctionLogs(projectId, functionId)}
        >
          Retry
        </Button>
      </div>
    );
  }
  
  // Check if logs exist and have the expected structure
  const logsArray = details.logs?.logs?.result || [];
  if (logsArray.length === 0) {
    return (
      <div className="p-4 text-sm text-muted-foreground">
        No logs found for this function.
      </div>
    );
  }
  
  return (
    <div className="max-h-60 overflow-y-auto">
      <div className="p-2 bg-black text-white font-mono text-xs">
        {logsArray.map((log: any, index: number) => (
          <div key={index} className="py-1">
            <span className="text-gray-400">
              [{new Date(Number(log.timestamp) / 1000).toLocaleTimeString()}]
            </span>{' '}
            <span className={`${log.event_type === 'Error' ? 'text-red-400' : ''}`}>
              {log.event_message}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export const FunctionsList = observer(({ projectId }: FunctionsListProps) => {
  const { supabaseStore, generatorStore } = useStores();
  const [copied, setCopied] = useState<string | null>(null);
  const [expandedFunction, setExpandedFunction] = useState<string | null>(null);
  
  const functions = supabaseStore.getFunctions(projectId);
  const isLoading = supabaseStore.isLoadingFunctions(projectId);
  const error = supabaseStore.getFunctionError(projectId);
  
  const handleRefresh = async () => {
    const project = generatorStore.getProjectById(projectId);
    if (project) {
      await supabaseStore.fetchFunctions(project);
    }
  };
  
  // Load functions on component mount if not already loaded
  React.useEffect(() => {
    if (!supabaseStore.hasLoadedProject(projectId)) {
      handleRefresh();
    }
  }, [projectId, supabaseStore, handleRefresh]);

  const copyFunctionId = (id: string) => {
    navigator.clipboard.writeText(id);
    setCopied(id);
    
    toast.success('Function ID copied to clipboard');
    
    setTimeout(() => {
      setCopied(null);
    }, 2000);
  };
  
  const toggleFunctionDetails = async (functionId: string) => {
    if (expandedFunction === functionId) {
      setExpandedFunction(null);
      return;
    }
    
    setExpandedFunction(functionId);
    
    // Fetch logs for this function if they haven't been loaded yet
    const details = supabaseStore.getFunctionDetails(functionId);
    if (!details || (!details.logs?.length && !details.loading)) {
      await supabaseStore.fetchFunctionLogs(projectId, functionId);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="animate-spin h-6 w-6 text-muted-foreground" />
      </div>
    );
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Functions</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  if (!functions || functions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Functions Found</CardTitle>
          <CardDescription>
            This project doesn't have any edge functions yet.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Edge Functions</h3>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
      
      <div className="space-y-2">
        {functions.map((func) => (
          <Collapsible 
            key={func.id} 
            open={expandedFunction === func.id}
            onOpenChange={() => toggleFunctionDetails(func.id)}
            className="border rounded-md overflow-hidden"
          >
            <div className="flex items-center justify-between p-4 hover:bg-muted/50 cursor-pointer">
              <div className="flex items-center space-x-4">
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-0 h-6 w-6">
                    {expandedFunction === func.id ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <div>
                  <div className="font-medium">{func.name}</div>
                  <div className="text-xs text-muted-foreground">Version: {func.version}</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge variant="outline">
                  {func.status}
                </Badge>
                
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-6 px-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    copyFunctionId(func.id);
                  }}
                  title="Copy Function ID"
                >
                  {copied === func.id ? (
                    <Check className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
            </div>
            
            <CollapsibleContent>
              <div className="border-t p-4 bg-muted/30">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-sm font-medium">Function Details</div>
                  <div className="text-xs text-muted-foreground">
                    Updated: {formatDistanceToNow(new Date(func.updated_at), { addSuffix: true })}
                  </div>
                </div>
                
                <Card className="mb-4">
                  <CardHeader className="py-2 px-4">
                    <CardTitle className="text-sm flex items-center">
                      <Terminal className="h-4 w-4 mr-2" />
                      Function Logs
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    {renderFunctionLogs(func.id, supabaseStore, projectId)}
                  </CardContent>
                </Card>
                
                <div className="text-xs">
                  <span className="text-muted-foreground">Function ID: </span>
                  <code className="px-1 py-0.5 bg-muted rounded">{func.id}</code>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        ))}
      </div>
    </div>
  );
});

<div className="text-xs text-muted-foreground">
  <p>Tip: To debug a specific function, use the function ID with getSupabaseLogs tool:</p>
  <pre className="mt-1 p-2 bg-muted rounded-md overflow-x-auto">
    getSupabaseLogs({"{"} service: 'edge-function', functionId: '[function-id]' {"}"})
  </pre>
</div>
