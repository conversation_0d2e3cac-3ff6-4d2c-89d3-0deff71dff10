import React from 'react';

interface GradientBackgroundProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'subtle' | 'medium' | 'bold';
}

export function GradientBackground({ 
  children, 
  className = '',
  intensity = 'medium'
}: GradientBackgroundProps) {
  // Map intensity to CSS classes
  const intensityClasses = {
    subtle: 'opacity-40',
    medium: 'opacity-60',
    bold: 'opacity-80'
  };
  
  return (
    <div className={`relative overflow-hidden z-0 ${className}`}>
      {/* Base gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 via-blue-600/10 to-amber-500/10 z-0"></div>
      
      {/* GPU-accelerated gradient blobs with transform animations */}
      <div
        className={`absolute -top-20 left-1/4 w-[400px] h-[400px] rounded-full filter blur-[60px] ${intensityClasses[intensity]}`}
        style={{ 
          background: 'linear-gradient(135deg, rgba(147,51,234,0.7) 0%, rgba(79,70,229,0.7) 100%)',
          transform: 'translate3d(0, 0, 0)',
          animation: 'blob-move-1 15s ease-in-out infinite',
          willChange: 'transform',
          backfaceVisibility: 'hidden'
        }}
      ></div>
      
      <div
        className={`absolute top-10 right-1/4 w-[450px] h-[450px] rounded-full filter blur-[70px] ${intensityClasses[intensity]}`}
        style={{ 
          background: 'linear-gradient(225deg, rgba(59,130,246,0.7) 0%, rgba(16,185,129,0.5) 100%)',
          transform: 'translate3d(0, 0, 0)',
          animation: 'blob-move-2 18s ease-in-out infinite',
          willChange: 'transform',
          backfaceVisibility: 'hidden'
        }}
      ></div>
      
      <div
        className={`absolute top-1/3 left-1/2 w-[380px] h-[380px] rounded-full filter blur-[50px] ${intensityClasses[intensity]}`}
        style={{ 
          background: 'linear-gradient(45deg, rgba(245,158,11,0.6) 0%, rgba(239,68,68,0.4) 100%)',
          transform: 'translate3d(0, 0, 0)',
          animation: 'blob-move-3 20s ease-in-out infinite',
          willChange: 'transform',
          backfaceVisibility: 'hidden'
        }}
      ></div>
      
      {/* More prominent dotted fabric pattern with GPU-accelerated animation */}
      <div 
        className="absolute inset-0 opacity-50 z-[1]"
        style={{ 
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M1 0h2v20H1V0zm0 0v2h20V0H1z\' fill=\'%239C92AC\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")',
          backgroundSize: '15px 15px',
          transform: 'translate3d(0, 0, 0)',
          animation: 'fabric-float 10s ease-in-out infinite',
          willChange: 'transform',
          backfaceVisibility: 'hidden'
        }}
      ></div>
      
      {/* Enhanced floating particles with GPU-accelerated animation */}
      <div 
        className="absolute inset-0 opacity-40 z-[1]"
        style={{
          backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.4) 1.5px, transparent 1.5px), radial-gradient(circle, rgba(255,255,255,0.3) 1px, transparent 1px)',
          backgroundSize: '30px 30px, 45px 45px',
          backgroundPosition: '0 0, 15px 15px',
          transform: 'translate3d(0, 0, 0)',
          animation: 'particles-drift 15s linear infinite',
          willChange: 'transform, background-position',
          backfaceVisibility: 'hidden'
        }}
      ></div>
      
      {/* Shimmer effect overlay with GPU acceleration */}
      <div 
        className="absolute inset-0 bg-gradient-to-t from-transparent to-white/5 z-[1] pointer-events-none"
        style={{
          transform: 'translate3d(0, 0, 0)',
          animation: 'shimmer 2s ease-in-out infinite',
          willChange: 'opacity',
          backfaceVisibility: 'hidden'
        }}
      ></div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}
