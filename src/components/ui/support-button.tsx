import React from 'react';
import { Button } from './button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './dropdown-menu';
import { Mail, MessageCircleMore } from 'lucide-react';

interface SupportButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  emailAddress?: string;
  whatsappNumber?: string;
}

export function SupportButton({
  className = '',
  variant = 'default',
  size = 'default',
  emailAddress = '<EMAIL>',
  whatsappNumber = '+1234567890', // Replace with actual WhatsApp number
}: SupportButtonProps) {
  const handleEmailClick = () => {
    window.location.href = `mailto:${emailAddress}`;
  };

  const handleWhatsAppClick = () => {
    // Format the number to remove any non-numeric characters
    const formattedNumber = whatsappNumber.replace(/\D/g, '');
    window.open(`https://wa.me/${formattedNumber}`, '_blank');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          Support
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleEmailClick}>
          <Mail className="mr-2 h-4 w-4" />
          <span>Email Support</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleWhatsAppClick}>
          <MessageCircleMore className="mr-2 h-4 w-4" />
          <span>WhatsApp Support</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
