import React from 'react';
import { But<PERSON> } from './button';
import { cn } from '@/lib/utils';

interface GradientButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
  asChild?: boolean;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

export function GradientButton({ 
  children, 
  className = '',
  asChild = false,
  size = 'default',
  variant = 'default',
  ...props
}: GradientButtonProps) {
  return (
    <Button
      className={cn(
        'relative overflow-hidden transition-all',
        'bg-gradient-to-r from-primary to-accent hover:from-accent hover:to-primary',
        'border-0 text-white shadow-md hover:shadow-lg',
        className
      )}
      size={size}
      variant={variant}
      asChild={asChild}
      {...props}
    >
      {children}
    </Button>
  );
}
