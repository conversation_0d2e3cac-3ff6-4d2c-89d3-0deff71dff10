import React from 'react';
import { cn } from '@/lib/utils';

interface GradientCardProps {
  children: React.ReactNode;
  className?: string;
  hoverEffect?: boolean;
}

export function GradientCard({ 
  children, 
  className = '',
  hoverEffect = true
}: GradientCardProps) {
  return (
    <div 
      className={cn(
        'relative rounded-lg overflow-hidden bg-card border border-border/50',
        'backdrop-blur-sm shadow-sm',
        hoverEffect && 'transition-all duration-300 hover:shadow-md hover:border-primary/30',
        'before:absolute before:inset-0 before:rounded-lg before:p-[1px]',
        'before:bg-gradient-to-br before:from-primary/10 before:via-transparent before:to-accent/10',
        'before:mask-border-gradient',
        className
      )}
    >
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}

// Add this to your globals.css or create a utility class
// .mask-border-gradient {
//   mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
//   mask-composite: exclude;
// }
