'use client';

import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Search,
  X,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';

export type SortDirection = 'asc' | 'desc';

export interface Column<T> {
  id: string;
  header: string;
  accessorKey?: keyof T;
  accessorFn?: (row: T) => React.ReactNode;
  cell?: (row: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  filterOptions?: { label: string; value: string }[];
}

export interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    pageIndex: number;
    pageSize: number;
    pageCount: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (size: number) => void;
  };
  sorting?: {
    sortField: string;
    sortDirection: SortDirection;
    onSortChange: (field: string, direction: SortDirection) => void;
  };
  searching?: {
    searchTerm: string;
    onSearchChange: (term: string) => void;
  };
  filtering?: {
    filters: Record<string, string[]>;
    onFilterChange: (columnId: string, values: string[]) => void;
  };
}

export function DataTable<T>({
  columns,
  data,
  loading = false,
  pagination,
  sorting,
  searching,
  filtering,
}: DataTableProps<T>) {
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>(
    filtering?.filters || {}
  );

  useEffect(() => {
    if (filtering?.filters) {
      setSelectedFilters(filtering.filters);
    }
  }, [filtering?.filters]);

  const handleFilterChange = (columnId: string, values: string[]) => {
    const newFilters = { ...selectedFilters, [columnId]: values };
    setSelectedFilters(newFilters);
    filtering?.onFilterChange(columnId, values);
  };

  const renderSortIcon = (column: Column<T>) => {
    if (!sorting || !column.sortable) return null;
    
    if (sorting.sortField === column.id) {
      return sorting.sortDirection === 'asc' ? (
        <ArrowUp className="ml-2 h-4 w-4" />
      ) : (
        <ArrowDown className="ml-2 h-4 w-4" />
      );
    }
    
    return <ArrowUpDown className="ml-2 h-4 w-4" />;
  };

  const handleSort = (column: Column<T>) => {
    if (!sorting || !column.sortable) return;
    
    const direction =
      sorting.sortField === column.id && sorting.sortDirection === 'asc'
        ? 'desc'
        : 'asc';
    
    sorting.onSortChange(column.id, direction);
  };

  const pageSizeOptions = [10, 20, 30, 50, 100];

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        {searching && (
          <div className="relative w-full sm:w-72">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              value={searching.searchTerm}
              onChange={(e) => searching.onSearchChange(e.target.value)}
              className="pl-8"
            />
            {searching.searchTerm && (
              <button
                onClick={() => searching.onSearchChange('')}
                className="absolute right-2 top-2.5"
              >
                <X className="h-4 w-4 text-muted-foreground" />
              </button>
            )}
          </div>
        )}

        {filtering && (
          <div className="flex flex-wrap gap-2">
            {columns
              .filter((column) => column.filterable && column.filterOptions)
              .map((column) => (
                <DropdownMenu key={column.id}>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      {column.header}
                      {selectedFilters[column.id]?.length > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {selectedFilters[column.id].length}
                        </Badge>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    {column.filterOptions?.map((option) => (
                      <DropdownMenuCheckboxItem
                        key={option.value}
                        checked={selectedFilters[column.id]?.includes(option.value)}
                        onCheckedChange={(checked) => {
                          const currentValues = selectedFilters[column.id] || [];
                          const newValues = checked
                            ? [...currentValues, option.value]
                            : currentValues.filter((v) => v !== option.value);
                          handleFilterChange(column.id, newValues);
                        }}
                      >
                        {option.label}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ))}
          </div>
        )}
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id}>
                  <div
                    className={`flex items-center ${
                      column.sortable ? 'cursor-pointer' : ''
                    }`}
                    onClick={() => column.sortable && handleSort(column)}
                  >
                    {column.header}
                    {renderSortIcon(column)}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Loading...
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results found
                </TableCell>
              </TableRow>
            ) : (
              data.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map((column) => (
                    <TableCell key={column.id}>
                      {column.cell
                        ? column.cell(row)
                        : column.accessorFn
                        ? column.accessorFn(row)
                        : column.accessorKey
                        ? String(row[column.accessorKey] || '')
                        : ''}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Select
              value={String(pagination.pageSize)}
              onValueChange={(value) => pagination.onPageSizeChange(Number(value))}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder="Page size" />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={String(size)}>
                    {size} rows
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="text-sm text-muted-foreground">
              Page {pagination.pageIndex} of {pagination.pageCount || 1}
            </div>
          </div>

          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={() => pagination.onPageChange(1)}
              disabled={pagination.pageIndex <= 1}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => pagination.onPageChange(pagination.pageIndex - 1)}
              disabled={pagination.pageIndex <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => pagination.onPageChange(pagination.pageIndex + 1)}
              disabled={pagination.pageIndex >= pagination.pageCount}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => pagination.onPageChange(pagination.pageCount)}
              disabled={pagination.pageIndex >= pagination.pageCount}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
