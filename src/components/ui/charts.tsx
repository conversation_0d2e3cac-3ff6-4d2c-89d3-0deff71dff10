'use client';

import React from 'react';
import {
  <PERSON><PERSON><PERSON> as Trem<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as Trem<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON> as Tremor<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Card,
  Title,
  Text
} from '@tremor/react';

interface ChartProps {
  data: any[];
  index: string;
  categories?: string[];
  colors?: string[];
  valueFormatter?: (value: number) => string;
  className?: string;
  showLegend?: boolean;
  showGridLines?: boolean;
  showAnimation?: boolean;
}

export function BarChart({
  data,
  index,
  categories = ['value'],
  colors = ['blue'],
  valueFormatter = (value) => `${value}`,
  className = '',
  showLegend = true,
  showGridLines = true,
  showAnimation = true
}: ChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <Text>No data available</Text>
      </div>
    );
  }

  return (
    <TremorBarChart
      data={data}
      index={index}
      categories={categories}
      colors={colors}
      valueFormatter={valueF<PERSON>atter}
      showLegend={showLegend}
      showGridLines={showGridLines}
      showAnimation={showAnimation}
      className={className}
    />
  );
}

export function LineChart({
  data,
  index,
  categories = ['value'],
  colors = ['blue'],
  valueFormatter = (value) => `${value}`,
  className = '',
  showLegend = true,
  showGridLines = true,
  showAnimation = true
}: ChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <Text>No data available</Text>
      </div>
    );
  }

  return (
    <TremorLineChart
      data={data}
      index={index}
      categories={categories}
      colors={colors}
      valueFormatter={valueFormatter}
      showLegend={showLegend}
      showGridLines={showGridLines}
      showAnimation={showAnimation}
      className={className}
    />
  );
}

export function PieChart({
  data,
  index,
  category = 'value',
  colors,
  valueFormatter = (value) => `${value}`,
  className = '',
  showAnimation = true
}: {
  data: any[];
  index: string;
  category?: string;
  colors?: string[];
  valueFormatter?: (value: number) => string;
  className?: string;
  showAnimation?: boolean;
}) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <Text>No data available</Text>
      </div>
    );
  }

  return (
    <TremorDonutChart
      data={data}
      index={index}
      category={category}
      colors={colors}
      valueFormatter={valueFormatter}
      showAnimation={showAnimation}
      className={className}
    />
  );
}
