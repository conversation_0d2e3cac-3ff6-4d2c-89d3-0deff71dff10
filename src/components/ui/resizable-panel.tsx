'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {useIsMobile} from "@/hooks/use-mobile";

interface ResizablePanelProps {
  children: [React.ReactNode, React.ReactNode]; // Exactly two children
  initialLeftWidth?: number; // Initial width of left panel in percentage
  minLeftWidth?: number; // Minimum width of left panel in percentage
  maxLeftWidth?: number; // Maximum width of left panel in percentage
  className?: string;
  resizerClassName?: string;
  disabledOnMobile?: boolean;
}

export function ResizablePanel({
  children,
  initialLeftWidth = 40,
  minLeftWidth = 20,
  maxLeftWidth = 80,
  className,
  resizerClassName,
  disabledOnMobile = false
}: ResizablePanelProps) {
  const isMobile = useIsMobile();
  const [leftWidth, setLeftWidth] = useState(
    isMobile && disabledOnMobile ? 100 : initialLeftWidth
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const resizerRef = useRef<HTMLDivElement>(null);
  const isResizing = useRef(false);
  const startX = useRef(0);
  const startWidth = useRef(0);

  // Handle mouse down on resizer
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    isResizing.current = true;
    startX.current = e.clientX;
    startWidth.current = leftWidth;
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  };

  // Update leftWidth when mobile state changes
  useEffect(() => {
    if (isMobile && disabledOnMobile) {
      setLeftWidth(100);
    } else if (!isMobile && leftWidth === 100) {
      // Reset to initial width when transitioning from mobile to desktop
      setLeftWidth(initialLeftWidth);
    }
  }, [isMobile, disabledOnMobile, initialLeftWidth, leftWidth]);

  // Handle mouse move for resizing
  useEffect(() => {
    // Skip setting up resize handlers if disabled on mobile
    if (isMobile && disabledOnMobile) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing.current || !containerRef.current) return;
      
      const containerWidth = containerRef.current.getBoundingClientRect().width;
      const deltaX = e.clientX - startX.current;
      const deltaPercentage = (deltaX / containerWidth) * 100;
      const newLeftWidth = Math.min(
        Math.max(startWidth.current + deltaPercentage, minLeftWidth),
        maxLeftWidth
      );
      
      setLeftWidth(newLeftWidth);
    };

    const handleMouseUp = () => {
      isResizing.current = false;
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [minLeftWidth, maxLeftWidth, isMobile, disabledOnMobile]);

  return (
    <div 
      ref={containerRef}
      className={cn('flex relative', className)}
    >
      {/* Left panel */}
      <div 
        className="h-full overflow-hidden"
        style={{ width: `${leftWidth}%` }}
      >
        {children[0]}
      </div>

      {/* Resizer - Only shown when not disabled on mobile */}
      {(!isMobile || !disabledOnMobile) && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                ref={resizerRef}
                className={cn(
                  'w-1 h-full cursor-col-resize flex-shrink-0 hover:bg-primary/50 active:bg-primary',
                  'flex items-center justify-center z-10 relative group',
                  'after:content-[""] after:absolute after:h-full after:w-5 after:left-[-2px] after:cursor-col-resize',
                  resizerClassName
                )}
                onMouseDown={handleMouseDown}
              >
                {/* Visible drag handle */}
                <div className="h-20 w-3 hover:scale-x-125 bg-primary/30 rounded-full absolute group-hover:bg-primary/70 transition-colors" />

                {/* Drag indicator dots */}
                <div className="absolute flex flex-col items-center justify-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="w-1 h-1 rounded-full bg-accent"></div>
                  <div className="w-1 h-1 rounded-full bg-accent"></div>
                  <div className="w-1 h-1 rounded-full bg-accent"></div>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="bg-background border border-border text-foreground">
              <p>Drag to resize panels</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* Right panel */}
      <div 
        className="h-full overflow-hidden"
        style={{ 
          width: `${isMobile && disabledOnMobile ? 0 : 100 - leftWidth}%`,
          display: isMobile && disabledOnMobile ? 'none' : 'block'
        }}
      >
        {children[1]}
      </div>
    </div>
  );
}
