'use client';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu } from 'lucide-react';
import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  playlists: string[];
}

export function Sidebar({ className, playlists }: SidebarProps) {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  const routes = [
    {
      href: '/',
      label: 'Home',
      active: pathname === '/',
    },
    {
      href: '/generator',
      label: 'App Generator',
      active: pathname === '/generator',
    },
    {
      href: '/templates',
      label: 'Templates',
      active: pathname === '/templates',
    },
  ];

  return (
    <>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 lg:hidden"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle Menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="pl-1 pr-0">
          <div className="px-7 py-4">
            <Link href="/" className="flex items-center space-x-2">
              <span className="font-bold">magically.life</span>
            </Link>
          </div>
          <ScrollArea className="my-4 h-[calc(100vh-8rem)] pb-10 pl-6">
            <div className="space-y-4">
              <div className="py-2">
                <h2 className="mb-2 px-2 text-lg font-semibold tracking-tight">
                  Discover
                </h2>
                <div className="space-y-1">
                  {routes.map((route) => (
                    <Button
                      key={route.href}
                      variant={route.active ? 'secondary' : 'ghost'}
                      className="w-full justify-start"
                      asChild
                    >
                      <Link href={route.href}>{route.label}</Link>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
      <div className={cn('pb-12', className)}>
        <div className="space-y-4 py-4">
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
              Discover
            </h2>
            <div className="space-y-1">
              {routes.map((route) => (
                <Button
                  key={route.href}
                  variant={route.active ? 'secondary' : 'ghost'}
                  className="w-full justify-start"
                  asChild
                >
                  <Link href={route.href}>{route.label}</Link>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
