'use client';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

interface TransferStatusProps {
  open: boolean;
  status: 'loading' | 'success';
  onOpenChange: (open: boolean) => void;
}

export function TransferStatus({ open, status, onOpenChange }: TransferStatusProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[360px] bg-black text-white p-8" disableCloseButton={true}>
        <div className="flex flex-col items-center justify-center gap-4">
          {status === 'loading' ? (
            <>
              <Loader2 className="h-8 w-8 animate-spin" />
              <p className="text-sm text-gray-400">Setting up your account...</p>
            </>
          ) : (
            <>
              <div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center">
                <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-sm text-gray-400">Your account is successfully set up!</p>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
