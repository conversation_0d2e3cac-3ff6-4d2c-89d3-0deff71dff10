import { Attachment, Message, CreateMessage, ChatRequestOptions } from 'ai';
import { Vote, Message as DBMessage } from '@/lib/db/schema';
import { ActionMeta } from '@/lib/parser/ActionsParser';

export interface InfiniteChatProps {
  id: string;
  initialMessages?: Array<Message>;
  initialPrompt?: string;
  isReadonly: boolean;
  onLoadingChange?: (isLoading: boolean) => any;
  projectId: string;
  runLastUserMessage: boolean;
  onInitialRunInitialized: () => any;
}

export interface UseInfiniteMessagesOptions {
  chatId: string;
  projectId: string;
  initialMessages?: Message[];
  limit?: number;
}

export interface UseInfiniteMessagesResult {
  messages: Message[];
  isLoading: boolean;
  isLoadingMore: boolean;
  isDebouncing: boolean;
  hasMore: boolean;
  loadError: string | null;
  loadMore: () => void;
  setMessages: (messages: Message[] | ((messages: Message[]) => Message[])) => void;
  append: (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>;
  reload: (chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>;
  input: string;
  setInput: (value: string) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  stop: () => void;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  votes: Vote[] | undefined;
}

export interface InfiniteMessagesProps {
  chatId: string;
  projectId: string;
  isLoading: boolean;
  isLoadingMore: boolean;
  isDebouncing: boolean;
  votes: Array<Vote> | undefined;
  messages: Array<Message>;
  setInput: (value: string) => void;
  setMessages: (messages: Message[] | ((messages: Message[]) => Message[])) => void;
  reload: (chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>;
  isReadonly: boolean;
  isBlockVisible: boolean;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  append: (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>;
  setAttachments?: (attachments: Attachment[]) => void;
  onVersionClick: (messageId: string) => void;
  lastActiveVersionId?: string | null;
  onActionClick: (action: ActionMeta) => void;
  setSnackError: any;
  loadMore: () => void;
  hasMore: boolean;
  loadError?: string | null;
}
