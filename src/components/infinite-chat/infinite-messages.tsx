import { Message, CreateMessage, ChatRequestOptions, Attachment } from 'ai';
import { PreviewMessage, ThinkingMessage } from '../base/message';
import React, {useRef, useEffect, memo, useState} from 'react';
import { Vote } from '@/lib/db/schema';
import equal from 'fast-deep-equal';
import { ActionMeta } from "@/lib/parser/ActionsParser";
import { InfiniteMessagesProps } from './types';

const getLastMessage = (messages: Message[], role: 'assistant' | 'user') => {
  const lastMessageIndex = [...messages]
    .findIndex(msg => msg.role === role);

  return lastMessageIndex !== -1
    ? messages[messages.length - 1 - lastMessageIndex]
    : null;
}

function PureInfiniteMessages({
  projectId,
  chatId,
  isLoading,
  isLoadingMore,
  isDebouncing,
  votes,
  messages,
  setMessages,
  setInput,
  reload,
  isReadonly,
  isBlockVisible,
  status,
  append,
  setAttachments,
  onVersionClick,
  lastActiveVersionId,
  onActionClick,
  setSnackError,
  loadMore,
  hasMore,
  loadError
}: InfiniteMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const sentinelRef = useRef<HTMLDivElement>(null);

  // Find last assistant message
  const lastAssistantMessage = getLastMessage(messages, 'assistant');

  // Find last user message
  const lastUserMessage = getLastMessage(messages, 'user');

  const lastAssistantMessageContent = lastAssistantMessage?.content;

  // Throttle function to prevent multiple calls in quick succession
  const [isThrottled, setIsThrottled] = useState(false);

  // Set up intersection observer for infinite scrolling with throttling
  useEffect(() => {
    if (!sentinelRef.current) return;

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      if (entry.isIntersecting && hasMore && !isLoadingMore && !isThrottled && !isDebouncing) {
        // Set throttle flag to prevent multiple calls
        setIsThrottled(true);

        // Call loadMore
        loadMore();

        // Reset throttle after a delay
        setTimeout(() => {
          setIsThrottled(false);
        }, 500); // 500ms throttle period
      }
    };

    const observer = new IntersectionObserver(
      handleIntersection,
      {
        threshold: 0.1,
        rootMargin: '100px 0px 0px 0px' // Add some margin to trigger earlier
      }
    );

    observer.observe(sentinelRef.current);

    return () => {
      if (sentinelRef.current) {
        observer.unobserve(sentinelRef.current);
      }
    };
  }, [hasMore, isLoadingMore, isThrottled, isDebouncing, loadMore]);

  // Simple scroll position management for infinite scrolling
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);
  const previousHeightRef = useRef(0);
  const previousScrollTopRef = useRef(0);
  
  // Handle scroll position preservation when loading older messages
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;
    
    if (isLoadingMore) {
      // Before loading more messages, save the current scroll position and height
      previousHeightRef.current = container.scrollHeight;
      previousScrollTopRef.current = container.scrollTop;
    } else if (previousHeightRef.current > 0) {
      // After loading more messages, adjust the scroll position to maintain the same relative position
      const heightDifference = container.scrollHeight - previousHeightRef.current;
      if (heightDifference > 0) {
        // Set the scroll position to maintain the same view the user had before
        container.scrollTop = previousScrollTopRef.current + heightDifference;
      }
      // Reset the references
      previousHeightRef.current = 0;
    }
  }, [isLoadingMore]);

  // Detect if user has scrolled up (away from bottom)
  useEffect(() => {
    const handleScroll = () => {
      if (messagesContainerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
        // Consider "at bottom" if within 100px of the bottom
        const isAtBottom = scrollHeight - scrollTop - clientHeight < 100;
        setShouldScrollToBottom(isAtBottom);
      }
    };

    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Track if messages were added by the AI (not from loading more)
  const prevMessagesLengthRef = useRef(messages.length);
  const [wasMessageAddedByAI, setWasMessageAddedByAI] = useState(false);
  
  // Track first render for initial scroll
  const isFirstRender = useRef(true);
  
  // Detect when a new message is added by the AI (not from loading more)
  useEffect(() => {
    // Skip if we're loading more messages (pagination)
    if (isLoadingMore) return;
    
    // Check if messages were added
    if (messages.length > prevMessagesLengthRef.current) {
      // Check if the newest message is from the assistant
      const newestMessage = messages[messages.length - 1];
      if (newestMessage && newestMessage.role === 'assistant') {
        setWasMessageAddedByAI(true);
      }
    } else {
      setWasMessageAddedByAI(false);
    }
    
    // Update the reference
    prevMessagesLengthRef.current = messages.length;
  }, [messages.length, isLoadingMore]);
  
  // Auto-scroll to bottom only in specific cases:
  // 1. On initial load (first render)
  // 2. When a new AI message is added AND user was already at bottom
  useEffect(() => {
    // First render - scroll to bottom
    if (isFirstRender.current) {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'auto' });
      }
      isFirstRender.current = false;
      return;
    }
    
    // New AI message and user was at bottom - scroll to bottom
    if (messagesEndRef.current && !isLoadingMore && wasMessageAddedByAI && shouldScrollToBottom) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages.length, isLoadingMore, shouldScrollToBottom, wasMessageAddedByAI]);

  return (
    <div
      ref={messagesContainerRef}
      className="flex flex-col min-w-0 gap-6 flex-1 overflow-y-auto pt-4 h-full"
    >
      {/* Top area with loading/error indicators */}
      <div className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm">
        {/* Loading indicator for older messages */}
        {(isLoadingMore || isDebouncing) && (
          <div className="flex justify-center py-2">
            <div className="flex items-center gap-2">
              <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <div className="text-muted-foreground text-sm">
                {isDebouncing ? 'Preparing to load messages...' : 'Loading older messages...'}
              </div>
            </div>
          </div>
        )}

        {/* No more messages indicator */}
        {!hasMore && messages.length > 0 && !loadError && (
          <div className="flex justify-center py-2">
            <div className="text-muted-foreground text-xs">
              Beginning of conversation
            </div>
          </div>
        )}

        {/* Error message */}
        {loadError && (
          <div className="flex justify-center py-2">
            <div className="flex flex-col items-center gap-2">
              <div className="text-destructive text-xs">
                {loadError}
              </div>
              <button
                onClick={() => loadMore()}
                className="text-xs text-primary hover:underline px-2 py-1 rounded-md hover:bg-primary/10"
                disabled={isLoadingMore}
              >
                Try again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Sentinel element for infinite scrolling - placed at the top for bottom-to-top scrolling */}
      <div ref={sentinelRef} className="h-8 mb-4 flex items-center justify-center" />

      {/* Empty state when no messages */}
      {messages.length === 0 && !isLoadingMore && !loadError && (
        <div className="flex justify-center items-center h-32">
          <div className="text-muted-foreground text-sm">
            No messages yet. Start a conversation!
          </div>
        </div>
      )}

      {/* Use index in key to ensure uniqueness in case of duplicate message IDs */}
      {messages.map((message, index) => (
        <PreviewMessage
          key={`${message.id}-${index}`}
          chatId={chatId}
          projectId={projectId}
          message={message}
          isLoading={isLoading && messages.length - 1 === index}
          vote={
            votes
              ? votes.find((vote) => vote.messageId === message.id)
              : undefined
          }
          setMessages={setMessages}
          reload={reload}
          setInput={setInput}
          isReadonly={isReadonly}
          isLastMessage={lastAssistantMessage?.id === message.id}
          isLastUserMessage={lastUserMessage?.id === message.id}
          status={status}
          setAttachments={setAttachments}
          append={append}
          onVersionClick={onVersionClick}
          isVersionActive={lastActiveVersionId === message.id}
          onActionClick={onActionClick}
          setSnackError={setSnackError}
        />
      ))
      }

      {/* Show thinking message only when there's no assistant message being streamed */}
      {status === 'streaming' && !messages.some(m => m.role === 'assistant' && m.id === messages[messages.length - 1].id) && 
        <ThinkingMessage />
      }

      <div
        ref={messagesEndRef}
        className="shrink-0 min-w-[24px] min-h-[24px]"
      />
    </div>
  );
}

// Use memo with proper comparison to detect streaming updates
export const InfiniteMessages = memo(PureInfiniteMessages, (prevProps, nextProps) => {
  // Always re-render when status changes (especially to/from streaming)
  if (prevProps.status !== nextProps.status) return false;
  
  // Always re-render when loading state changes
  if (prevProps.isLoading !== nextProps.isLoading) return false;
  
  // Check for changes in message array length
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  
  // Critical: Check for content changes in the last message (streaming updates)
  if (prevProps.messages.length > 0 && nextProps.messages.length > 0) {
    const prevLastMessage = prevProps.messages[prevProps.messages.length - 1];
    const nextLastMessage = nextProps.messages[nextProps.messages.length - 1];
    
    // If the last message content has changed, we need to re-render
    if (prevLastMessage.content !== nextLastMessage.content) return false;
  }
  
  // Other state changes that should trigger re-renders
  if (prevProps.isLoadingMore !== nextProps.isLoadingMore) return false;
  if (prevProps.isDebouncing !== nextProps.isDebouncing) return false;
  if (prevProps.hasMore !== nextProps.hasMore) return false;
  if (prevProps.loadError !== nextProps.loadError) return false;
  if (prevProps.chatId !== nextProps.chatId) return false;
  if (prevProps.projectId !== nextProps.projectId) return false;
  if (prevProps.lastActiveVersionId !== nextProps.lastActiveVersionId) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;

  // If none of the above conditions triggered a re-render, we can skip it
  return true;
});
