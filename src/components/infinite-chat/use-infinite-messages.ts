import { useChat } from 'ai/react';
import { useState, useEffect, useCallback } from 'react';
import { Message, Attachment } from 'ai';
import { Vote, Message as DBMessage } from '@/lib/db/schema';
import { useSession } from 'next-auth/react';
import { useSWRConfig } from 'swr';
import { generateUUID, convertToUIMessages } from '@/lib/utils';
import { useAnonymousSession } from '@/providers/anonymous-provider';
import { useStores } from '@/stores/utils/useStores';
import { DEFAULT_CODE } from '@/types/editor';
import { UseInfiniteMessagesOptions, UseInfiniteMessagesResult } from './types';
import {trackMessageEvent} from "@/lib/analytics/track";

export function useInfiniteMessages({
  chatId,
  projectId,
  initialMessages = [],
  limit = 50
}: UseInfiniteMessagesOptions): UseInfiniteMessagesResult {
  const { mutate } = useSWRConfig();
  const { anonymousId, hasReachedLimit, incrementChatCount, storeChat } = useAnonymousSession();
  const { status: authStatus } = useSession();
  const { generatorStore, integrationStore, logStore } = useStores();
  const session = generatorStore.getActiveSession(chatId);

  // State for pagination
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [votes, setVotes] = useState<Vote[]>();
  const [attachments, setAttachments] = useState<Attachment[]>([]);

  // Initialize chat with Vercel AI SDK
  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    isLoading,
    stop,
    reload,
    data,
    status
  } = useChat({
    id: chatId,
    keepLastMessageOnError: false,
    experimental_prepareRequestBody: (options: {
      id: string;
      messages: Message[];
      requestData?: any;
      requestBody?: object;
    }) => {
      options.messages = options.messages.slice(-20);
      return {
        files: session?.fileTree?.length ? session?.fileTree : DEFAULT_CODE,
        activeFile: session?.activeFile,
        dependencies: session?.dependencies,
        linkSupabaseProjectId: integrationStore.currentSelectedProjectId,
        linkSupabaseConnection: integrationStore.getConnection("supabase")?.id,
        projectId: session?.projectId,
        logs: logStore.getLogs(chatId),
        agentModeEnabled: true,
        messages: options.messages,
        id: chatId,
        ...options.requestBody
      }
    },
    headers: anonymousId ? {
      'x-anonymous-id': anonymousId,
      'x-chat-count': hasReachedLimit ? '2' : '1',
    } : {},
    initialMessages,
    generateId: () => {
      return generateUUID();
    },
    sendExtraMessageFields: true,
    streamProtocol: "data",
    onResponse: () => {
      // Track when AI begins streaming a response
      trackMessageEvent('AI_RESPONSE_STREAMING', {
        chat_id: chatId
      });
    },
    onFinish: async (message) => {
      // Track when a message is finished
      trackMessageEvent('RECEIVED', {
        chat_id: chatId,
        message_id: message.id
      });

      session?.markStreamingComplete();

      // Track when AI completes a response
      trackMessageEvent('AI_RESPONSE_RECEIVED', {
        chat_id: chatId,
        message_content_length: message.content.length
      });

      if (anonymousId) {
        // Store messages and file state for anonymous users
        storeChat(chatId, [...initialMessages, message], session?.fileTree);
        if (!hasReachedLimit) {
          incrementChatCount();
        }
      } else {
        // Update both history and subscription status for authenticated users
        mutate('/api/history');
        mutate('/api/subscription/status');
      }
    }
  });

  // Load initial messages and fetch votes for the chat
  useEffect(() => {
    // Skip initial data fetching if already in progress
    if (isLoadingMore) return;
    
    const fetchInitialData = async () => {
      try {
        // Set initial messages from props
        if (initialMessages && initialMessages.length > 0) {
          // Initial messages should already be in the correct format from GeneratorPage
          // which uses convertToUIMessages

          // Sort messages by timestamp to ensure correct order
          const sortedMessages = [...initialMessages]

          setMessages(sortedMessages);
          
          // No need to reset skip count, we're using the oldest message ID as cursor
          
          setHasMore(true); // Assume there are more messages to load

          // Skip the API call since we already have initial messages
          console.log('Using initial messages from server-side rendering, skipping API call');
        } else if (chatId) {
          // If no initial messages were provided and no cursor is set, fetch the first batch
          // This prevents double API calls when cursor is already set
          try {
            // Use URLSearchParams to properly encode parameters
            const params = new URLSearchParams({
              limit: limit.toString(),
              cursor: 'null', // Initial load has no cursor
              direction: 'desc'
            });

            console.log('[DEBUG] Fetching from URL:', `/api/chats/${chatId}/messages?${params.toString()}`);
            const response = await fetch(`/api/chats/${chatId}/messages?${params.toString()}`);

            console.log('[DEBUG] Response status:', response.status);
            if (!response.ok) {
              throw new Error(`Failed to fetch messages: ${response.status}`);
            }

            const data = await response.json();

            // Check for error in response
            if (data.error) {
              throw new Error(data.error);
            }

            // Convert DB messages to UI messages format
            const messages = convertToUIMessages(data.messages as DBMessage[]);

            // Sort messages by timestamp to ensure correct order
            const sortedMessages = [...messages]

            setMessages(sortedMessages);
            setHasMore(data.hasMore);
          } catch (error) {
            console.error('Failed to fetch initial messages:', error);
            setLoadError(error instanceof Error ? error.message : 'Failed to load messages. Please try again.');
            // Still set empty messages to prevent loading state from being stuck
            setMessages([]);
          }
        }

        // Fetch votes
        try {
          const votesResponse = await fetch(`/api/chat/${chatId}/votes`);
          if (votesResponse.ok) {
            const votesData = await votesResponse.json();
            setVotes(votesData);
          }
        } catch (votesError) {
          console.error('Failed to fetch votes:', votesError);
          // Non-critical error, continue without votes
        }
      } catch (error) {
        console.error('Failed to fetch initial data:', error);
        // Set empty messages to prevent loading state from being stuck
        setMessages([]);
      }
    };

    if (chatId) {
      fetchInitialData();
    }
  }, [chatId, initialMessages, limit, setMessages]);

  // State for retry logic and debouncing
  const [retryCount, setRetryCount] = useState(0);
  const [retryTimeout, setRetryTimeout] = useState<NodeJS.Timeout | null>(null);
  const [debounceTimeout, setDebounceTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isDebouncing, setIsDebouncing] = useState(false);

  // Debounced function to load more messages
  const debouncedLoadMore = useCallback((isRetry = false) => {
    // Prevent duplicate calls - if already loading, don't even set up the debounce
    if (isLoadingMore) return;
    
    // Clear any existing debounce timeout
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
      setDebounceTimeout(null);
    }

    // Set a new debounce timeout
    const timeout = setTimeout(async () => {
      setIsDebouncing(false);

      // Clear any existing retry timeout
      if (retryTimeout) {
        clearTimeout(retryTimeout);
        setRetryTimeout(null);
      }

      // Double-check loading state and hasMore flag
      console.log('[DEBUG] Current state:', { isLoadingMore, hasMore, messagesCount: messages.length });
      if (isLoadingMore) {
        console.log('[DEBUG] Already loading more messages, skipping');
        return;
      }
      
      if (!hasMore && !isRetry) {
        console.log('[DEBUG] No more messages to load and not retrying, skipping');
        return;
      }
      
      setIsLoadingMore(true);
      if (!isRetry) {
        setLoadError(null);
        setRetryCount(0);
      }

      try {
        // Find the oldest message ID to use as cursor
        let oldestMessageId: string | null = null;
        if (messages.length > 0) {
          // Sort messages by timestamp to find the oldest one
          const sortedMessages = [...messages].sort((a, b) => {
            const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return dateA - dateB;
          });
          
          // Use the ID of the oldest message as cursor
          oldestMessageId = sortedMessages[0]?.id;
        }
        
        console.log('[DEBUG] Loading more messages with oldestMessageId:', oldestMessageId);

        // Call the API endpoint to fetch older messages
        // Use URLSearchParams to properly encode parameters
        const params = new URLSearchParams();
        
        // Add parameters
        params.append('limit', limit.toString());
        params.append('direction', 'desc');
        
        // Handle cursor parameter - convert null to 'null' string for URL params
        if (oldestMessageId) {
          params.append('cursor', oldestMessageId);
        } else {
          params.append('cursor', 'null');
        }

        console.log('[DEBUG] Fetching from URL:', `/api/chats/${chatId}/messages?${params.toString()}`);
        const response = await fetch(`/api/chats/${chatId}/messages?${params.toString()}`);

        console.log('[DEBUG] Response status:', response.status);
        if (!response.ok) {
          throw new Error(`Failed to fetch messages: ${response.status}`);
        }

        const data = await response.json();
        console.log('[DEBUG] Response data:', { 
          hasMessages: !!data?.messages, 
          messageCount: data?.messages?.length || 0,
          hasMore: data?.hasMore
        });

        // Check for error in response
        if (data.error) {
          throw new Error(data.error);
        }

        // Convert DB messages to UI messages format
        const olderMessages = convertToUIMessages(data.messages as DBMessage[]);

        if (olderMessages.length === 0) {
          setHasMore(false);
        } else {
          // No need to update skip count, we're using the oldest message ID as cursor
          // Merge older messages with current messages, avoiding duplicates and ensuring correct order by timestamp
          setMessages(prevMessages => {
            // Create a Map of existing message IDs for quick lookup and deduplication
            const existingMessagesMap = new Map(prevMessages.map(msg => [msg.id, msg]));

            // Add new messages to the map, overwriting any duplicates
            olderMessages.forEach(msg => {
              if (!existingMessagesMap.has(msg.id)) {
                existingMessagesMap.set(msg.id, msg);
              }
            });

            // If no new unique messages were added, we might have reached the end
            if (existingMessagesMap.size === prevMessages.length) {
              setHasMore(false);
            }

            // Convert map back to array and sort by createdAt timestamp (newest last)
            const allMessages = Array.from(existingMessagesMap.values());
            return allMessages
          });
          
          setHasMore(data.hasMore);
        }

        // Reset retry count on success
        setRetryCount(0);
      } catch (error) {
        console.error('[DEBUG] Error loading more messages:', error);
        setLoadError(error instanceof Error ? error.message : String(error));
        // Log more details about the current state
        console.log('[DEBUG] Error state:', { 
          messagesLength: messages.length,
          chatId,
          isLoadingMore,
          hasMore
        });

        // Implement exponential backoff for retries
        if (retryCount < 3) { // Maximum 3 retries
          const nextRetryCount = retryCount + 1;
          setRetryCount(nextRetryCount);

          // Exponential backoff: 2^retry * 1000ms (1s, 2s, 4s)
          const retryDelay = Math.pow(2, nextRetryCount) * 1000;
          console.log(`Retrying in ${retryDelay}ms (attempt ${nextRetryCount})`);

          const timeout = setTimeout(() => {
            loadMore(true); // Retry with isRetry flag
          }, retryDelay);

          setRetryTimeout(timeout);
        } else {
          console.log('Maximum retry attempts reached');
        }
      } finally {
        setIsLoadingMore(false);
      }
    }, 300); // 300ms debounce delay

    setDebounceTimeout(timeout);
    setIsDebouncing(true);
  }, [chatId, hasMore, isLoadingMore, limit, messages, retryCount, retryTimeout, setMessages]);

  // Wrapper function for the debounced load more
  const loadMore = useCallback((isRetry = false) => {
    console.log('[DEBUG] loadMore called with messages count:', messages.length);
    debouncedLoadMore(isRetry);
  }, [debouncedLoadMore, messages.length]);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      // Clean up retry timeout
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }

      // Clean up debounce timeout
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
    };
  }, [retryTimeout, debounceTimeout]);

  return {
    messages,
    isLoading,
    isLoadingMore,
    isDebouncing,
    hasMore,
    loadError,
    loadMore,
    setMessages,
    append,
    reload,
    input,
    setInput,
    handleSubmit,
    stop,
    status,
    votes
  };
}
