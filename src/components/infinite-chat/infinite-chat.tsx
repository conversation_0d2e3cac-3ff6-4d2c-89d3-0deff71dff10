'use client';

import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Attachment } from 'ai';
import { useStores } from '@/stores/utils/useStores';
import { useBlockSelector } from '@/hooks/use-block';
import { MultimodalInput } from '../base/multimodal-input';
import { InfiniteMessages } from './infinite-messages';
import { useInfiniteMessages } from './use-infinite-messages';
import { InfiniteChatProps } from './types';
import { ActionMeta } from '@/lib/parser/ActionsParser';
import {IntegrationDialog} from "@/components/generator/IntegrationDialog";
import {SecretInput} from "@/components/supabase/SecretInput";

const InfiniteChat = observer(({
  id,
  initialMessages = [],
  isReadonly,
  initialPrompt,
  onLoadingChange,
  projectId,
  runLastUserMessage,
  onInitialRunInitialized
}: InfiniteChatProps) => {
  const { generatorStore, integrationStore, logStore } = useStores();
  const session = generatorStore.getActiveSession(id);

  const [integrationOpen, setIntegrationOpen] = useState(false);
  const [isSecretOpen, setIsSecretOpen] = useState(false);
  const [secretNames, setSecretNames] = useState<string[] | null>(null);
  const [droppedFiles, setDroppedFiles] = useState<File[]>();
  const [selectMode, setSelectMode] = useState(false);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [snackError, setSnackError] = useState<string | null>(null);
  if (!session) {
    return;
  }
  const  isBlockVisible  = useBlockSelector((state) => state.isVisible);

  // Use our custom hook for infinite messages
  const {
    messages,
    isLoading,
    isLoadingMore,
    isDebouncing,
    hasMore,
    loadError,
    loadMore,
    setMessages,
    append,
    reload,
    input,
    setInput,
    handleSubmit,
    stop,
    status,
    votes
  } = useInfiniteMessages({
    chatId: id,
    projectId,
    initialMessages
  });

  // Notify parent component about loading state changes
  useEffect(() => {
    onLoadingChange?.(isLoading);
  }, [isLoading, onLoadingChange]);

  // Run the last user message if needed (for uninitialized chats)
  useEffect(() => {
    if (runLastUserMessage && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'user') {
        reload();
        onInitialRunInitialized();
      }
    }
  }, [runLastUserMessage, messages, reload, onInitialRunInitialized]);

  // Handle initial prompt if provided
  useEffect(() => {
    if (initialPrompt && messages.length === 0) {
      append({
        id: crypto.randomUUID(),
        content: initialPrompt,
        role: 'user',
      });
    }
  }, [initialPrompt, messages.length, append]);

  const integrationClicked = (open: boolean) => {
    setIntegrationOpen(open);
  };

  const onSecretSubmit = async (secretValues: Record<string, string>) => {
    const result = await session.saveSecrets(secretValues, projectId, append as any);
    if (result) {
      setIsSecretOpen(false);
    }
    setSecretNames(null);
  };

  const onVersionClick = (messageId: string) => {
    // Handle version click
    console.log('Version clicked:', messageId);
  };

  const onActionClick = (action: ActionMeta) => {
    // Handle action click
    console.log('Action clicked:', action);
  };

  const onVisualSelectionClicked = () => {
    setSelectMode(!selectMode);
  };

  const localComponentContexts = session.componentContexts;

  return (
    <>
      <div className="flex flex-col h-full bg-background overflow-hidden">
        {/* Main scrollable area */}
        <div className="flex-1 overflow-y-auto pb-safe">
          <div className="flex flex-col h-full min-h-0">
            <InfiniteMessages
              votes={votes}
              chatId={id}
              projectId={projectId}
              isLoading={isLoading}
              isLoadingMore={isLoadingMore}
              isDebouncing={isDebouncing}
              setInput={setInput}
              messages={messages}
              setMessages={setMessages}
              reload={reload}
              isReadonly={isReadonly}
              isBlockVisible={isBlockVisible}
              append={append}
              status={status}
              setAttachments={setAttachments}
              onVersionClick={onVersionClick}
              lastActiveVersionId={''}
              onActionClick={onActionClick}
              setSnackError={setSnackError}
              loadMore={loadMore}
              hasMore={hasMore}
              loadError={loadError}
            />
          </div>
        </div>

        {/* Input area - with safe area padding */}
        {!isReadonly && (
          <div
            className="flex-shrink-0 border-t border-border/50 bg-background backdrop-blur bg-opacity-45 pb-safe pt-2">
            <div className="px-4 py-2 mx-auto">
              <MultimodalInput
                chatId={id}
                input={input}
                inDesignMode={false}
                setInput={setInput}
                handleSubmit={handleSubmit}
                isLoading={isLoading}
                stop={stop}
                attachments={attachments}
                setAttachments={setAttachments}
                messages={messages}
                setMessages={setMessages}
                append={append}
                projectId={projectId}
                droppedFiles={droppedFiles}
                onVisualSelectionClicked={onVisualSelectionClicked}
                componentContexts={localComponentContexts}
                onRemoveComponentContext={(id) => session.removeComponentContext(id)}
                onClearComponentContexts={() => session.clearComponentContexts()}
                selectMode={selectMode}
              />
            </div>
          </div>
        )}
      </div>

      <IntegrationDialog
        providerId={'supabase'}
        open={integrationOpen}
        chatId={id}
        onOpenChange={integrationClicked}
      />

      <SecretInput
        isOpen={isSecretOpen && !!secretNames}
        onClose={() => setIsSecretOpen(false)}
        onSubmit={onSecretSubmit}
        secretNames={secretNames as string[]}
      />
    </>
  );
});

export default InfiniteChat;
