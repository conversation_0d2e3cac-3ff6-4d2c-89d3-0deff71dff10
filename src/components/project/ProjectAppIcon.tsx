import Image from "next/image";
import {AppWindow, <PERSON><PERSON>, Smartphone} from "lucide-react";
import {Project} from "@/lib/db/schema";
import React from "react";

const ProjectAppIcon = ({project} : {project: Project}) => {
    // Generate a consistent color based on project name or ID
    const getInitial = () => {
        if (!project.appName) return "A";
        return project.appName.charAt(0).toUpperCase();
    };
    
    // Generate a consistent background color based on project ID
    const getBackgroundColor = () => {
        const id = project.id || "default";
        const colors = [
            "bg-zinc-700", "bg-slate-700", "bg-stone-700", 
            "bg-gray-700", "bg-neutral-700", "bg-zinc-800"
        ];
        const index = id.split("").reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0) % colors.length;
        return colors[index];
    };
    
    return (
        <div className="h-12 w-12 relative bg-muted rounded-md flex-shrink-0 overflow-hidden">
            {project.icon ? (
                <Image
                    src={project.icon}
                    alt={project.appName || 'Project'}
                    fill
                    className="object-cover"
                />
            ) : (
                <div className={`w-full h-full flex items-center justify-center ${getBackgroundColor()} text-white`}>
                    {project.appName ? (
                        <span className="text-lg font-semibold">{getInitial()}</span>
                    ) : (
                        <AppWindow className="h-5 w-5 text-white/80" />
                    )}
                </div>
            )}
        </div>
    )
}

export default ProjectAppIcon;