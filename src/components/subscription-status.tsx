import { useSubscription } from '@/hooks/use-subscription';
import { But<PERSON> } from './ui/button';
import { Loader2, LogIn } from 'lucide-react';
import cn from "classnames";
import { useStores } from '@/stores/utils/useStores';
import { useMediaQuery } from '@/hooks/use-media-query';
import posthog from "posthog-js";

export function SubscriptionStatus() {
  const { subscription, isLoading } = useSubscription();
  const { generatorStore } = useStores();
  const isMobile = useMediaQuery('(max-width: 768px)');

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <Loader2 className="h-3 w-3 animate-spin" />
        Loading...
      </div>
    );
  }


  if (!subscription) {
    return null;
  }

  posthog.setPersonProperties({
    plan: subscription?.planTier,
    isPaidUser: subscription?.isPro,
    messageLimit: subscription?.messageLimit,
    messagesRemaining: subscription?.messagesRemaining
  })

  // Calculate usage percentages for different limits
  
  // 1. Calculate credits usage (replacing the legacy message limit)
  // Use credits.total and credits.remaining from the subscription API
  // Ensure values are non-negative
  const safeCreditsRemaining = Math.max(0, subscription.credits?.remaining || 0);
  const usedCredits = subscription.credits?.total - safeCreditsRemaining;
  const creditsPercentageUsed = (usedCredits / subscription.credits?.total) * 100;
  
  // 2. Calculate daily limit usage if applicable
  const hasDailyLimit = subscription.dailyLimit > 0;
  // Ensure values are non-negative
  const safeDailyRemaining = Math.max(0, subscription.dailyRemaining || 0);
  const usedDailyCredits = hasDailyLimit ? (subscription.dailyLimit - safeDailyRemaining) : 0;
  const dailyPercentageUsed = hasDailyLimit ? (usedDailyCredits / subscription.dailyLimit) * 100 : 0;
  
  // 3. Determine which limit to display based on user type and limits
  const shouldShowDailyLimit = () => {
    // For free and anonymous users, prioritize daily limit unless daily is left and monthly is less than 10%
    if (!subscription.isPro) {
      // If daily limit exists and monthly is critical (less than 10% remaining)
      const isMonthlyLimitCritical = (subscription.credits?.remaining / subscription.credits?.total) * 100 < 10;
      
      // Show monthly limit only when daily limit is not depleted AND monthly limit is critical
      return !(subscription.dailyRemaining > 0 && isMonthlyLimitCritical);
    }
    
    // For Pro users, show the most restrictive limit
    return hasDailyLimit && dailyPercentageUsed > creditsPercentageUsed;
  };
  
  const showingDailyLimit = shouldShowDailyLimit();
  
  // 4. Select the most relevant percentage to display
  const percentageUsed = showingDailyLimit ? dailyPercentageUsed : creditsPercentageUsed;
  
  // 5. Check if we should show the bar (for all users, including Pro)
  const shouldShowBar = percentageUsed >= 80;
  
  // 6. Don't show anything for Pro users unless they're close to a limit
  if (subscription.isPro && !shouldShowBar) {
    return null;
  }

  // 7. Handle click based on user type
  const handleClick = () => {
    if (subscription.isAnonymous) {
      // For anonymous users, show login dialog
      generatorStore.toggleLoginDialog(true);
    } else {
      // For authenticated users, show upgrade dialog
      generatorStore.setUsageLimit(
        subscription.credits?.total || 0,
        subscription.credits?.remaining || 0
      );
      generatorStore.toggleUpgradeDialog(true);
    }
  }

  // Determine the appropriate message based on which limit we're showing
  const getMessage = () => {
    if (showingDailyLimit) {
      return {
        label: 'Daily:',
        value: safeDailyRemaining <= 0 
          ? 'You have run out of messages for the day' 
          : `${safeDailyRemaining} message${safeDailyRemaining !== 1 ? 's' : ''} left`
      };
    } else {
      return {
        label: 'Monthly:',
        value: safeCreditsRemaining <= 0 
          ? 'You have run out of messages for the month' 
          : `${safeCreditsRemaining} message${safeCreditsRemaining !== 1 ? 's' : ''} left`
      };
    }
  };
  
  // Get the sign-in message for anonymous users (separate from credit display)
  const getSignInMessage = () => {
    return isMobile ? 'Sign in' : 'Sign in to save your project';
  };

  return (
    <div className="flex items-center gap-2 text-xs bg-background/50 px-2.5 py-1.5 rounded-lg border border-border/50">
      <div className="flex items-center gap-2 flex-1">
        <div className="flex-1 min-w-[140px]">
          <div className="flex flex-col gap-0.5">
            <div className="flex items-center gap-1.5">
              {/* Display the credits remaining with clear label */}
              {(showingDailyLimit && safeDailyRemaining <= 0) || 
               (!showingDailyLimit && safeCreditsRemaining <= 0) ? (
                <span className="text-red-500 font-medium">{getMessage().value}</span>
              ) : (
                <>
                  <span className="font-medium text-muted-foreground">{getMessage().label}</span>
                  <span className="text-muted-foreground">{getMessage().value}</span>
                </>
              )}
              {/* Show sign-in message for anonymous users */}
              {subscription.isAnonymous && (
                <span className="ml-1 text-accent">(Sign in to save your work)</span>
              )}
            </div>
          </div>
          {/* Only show progress bar if not on mobile */}
          <div className="h-1 w-full bg-muted/30 rounded-full overflow-hidden mt-1.5 hidden md:flex">
            <div 
              className={cn(
                "h-full transition-all duration-500",
                percentageUsed >= 90 ? "bg-red-500" :
                percentageUsed >= 80 ? "bg-amber-500" : "bg-primary"
              )}
              style={{ width: `${percentageUsed}%` }}
            />
          </div>
        </div>
      </div>
      <Button
        variant="ghost"
        size="xs"
        className={cn(
          "font-medium",
          percentageUsed >= 90
            ? "text-red-500 hover:text-red-500 hover:bg-red-500/10"
            : "text-accent hover:text-accent hover:bg-accent/10"
        )}
        onClick={handleClick}
      >
        {/* Show icon for mobile anonymous users to save space */}
        {subscription.isAnonymous ? (
          <>
            <LogIn className="h-3 w-3 mr-1" />
            Sign in
          </>
        ) : (
          'Upgrade'
        )}
      </Button>
    </div>
  );
}
