'use client';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { HelpCircle } from 'lucide-react';
import { faqs } from '@/data/faqs';

export function PricingFAQs() {
  // Filter only billing-related FAQs
  const billingFaqs = faqs.filter(faq => faq.category === 'billing');
  
  return (
    <div className="max-w-3xl mx-auto mt-16 mb-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Frequently Asked Questions</h2>
        <p className="text-muted-foreground">Everything you need to know about credits and billing</p>
      </div>
      
      <Accordion type="single" collapsible className="space-y-3">
        {billingFaqs.map((faq) => (
          <AccordionItem
            key={faq.id}
            value={faq.id}
            className="border border-muted-foreground/20 rounded-lg px-5 py-2 backdrop-blur-sm bg-background/50 hover:border-primary/30 transition-all duration-300"
          >
            <AccordionTrigger className="text-left hover:no-underline">
              <div className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-primary/70" />
                <span className="text-base font-medium">{faq.question}</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="text-muted-foreground text-sm leading-relaxed pt-2">
              {faq.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}
