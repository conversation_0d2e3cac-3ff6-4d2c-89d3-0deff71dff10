"use client";

import {createContext, useContext, useEffect, useState} from 'react'
import {generateUUID} from "@/lib/utils";

interface AnonymousChat {
    id: string;
    messages: any[];
    fileStates: Record<string, any>;
    createdAt: Date;
}

type AnonymousContextType = {
    anonymousId: string | null
    chatCount: number
    createAnonymousSession: () => void
    clearAnonymousSession: () => Promise<void>
    incrementChatCount: () => void
    hasReachedLimit: boolean
    storeChat: (chatId: string, messages: any[], fileState: any) => void
    getStoredChat: (chatId: string) => AnonymousChat | null
}

const AnonymousContext = createContext<AnonymousContextType>(null!)

export function AnonymousProvider({children}: { children: React.ReactNode }) {
    const [anonymousId, setAnonymousId] = useState<string | null>(null)
    const [chatCount, setChatCount] = useState(0)
    const [chats, setChats] = useState<Record<string, AnonymousChat>>({})    
    const hasReachedLimit = chatCount >= 2

    const createAnonymousSession = () => {
        const newId = generateUUID()
        localStorage.setItem('anon_id', newId)
        localStorage.setItem('chat_count', '0')
        setAnonymousId(newId)
        setChatCount(0)
    }

    const clearAnonymousSession = async () => {
        localStorage.removeItem('anon_id')
        localStorage.removeItem('chat_count')
        localStorage.removeItem('anon_chats')
        setAnonymousId(null)
        setChatCount(0)
        setChats({})
    }

    useEffect(() => {
        const existingId = localStorage.getItem('anon_id')
        const existingCount = localStorage.getItem('chat_count')
        const existingChats = localStorage.getItem('anon_chats')
        
        if (existingId) {
            setAnonymousId(existingId)
            setChatCount(existingCount ? parseInt(existingCount) : 0)
            setChats(existingChats ? JSON.parse(existingChats) : {})
        } else {
            createAnonymousSession()
        }
    }, [])

    // Save chats to localStorage whenever they change
    useEffect(() => {
        if (Object.keys(chats).length > 0) {
            // localStorage.setItem('anon_chats', JSON.stringify(chats))
        }
    }, [chats])

    const incrementChatCount = () => {
        const newCount = chatCount + 1
        localStorage.setItem('chat_count', newCount.toString())
        setChatCount(newCount)
    }

    const storeChat = (chatId: string, messages: any[], fileState: any) => {
        setChats(prevChats => {
            const existingChat = prevChats[chatId] || { id: chatId, messages: [], fileStates: {}, createdAt: new Date() };
            
            // Append new messages without duplicates
            const existingMessageIds = new Set(existingChat.messages.map((m: any) => m.id));
            const newMessages = [...existingChat.messages];
            
            messages.forEach(message => {
                if (!existingMessageIds.has(message.id)) {
                    newMessages.push(message);
                }
            });

            // Update file states
            const updatedFileStates = { ...existingChat.fileStates, ...fileState };

            return {
                ...prevChats,
                [chatId]: {
                    ...existingChat,
                    messages: newMessages,
                    fileStates: updatedFileStates,
                }
            };
        });
    };

    const getStoredChat = (chatId: string): AnonymousChat | null => {
        return chats[chatId] || Object.values(chats)[Object.values(chats).length - 1] || null;
    };

    return (
        <AnonymousContext.Provider
            value={{
                anonymousId,
                chatCount,
                hasReachedLimit,
                createAnonymousSession,
                clearAnonymousSession,
                incrementChatCount,
                storeChat,
                getStoredChat
            }}>
            {children}
        </AnonymousContext.Provider>
    )
}

export const useAnonymousSession = () => useContext(AnonymousContext)