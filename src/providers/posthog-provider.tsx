// app/providers.tsx
'use client'

import posthog, {CaptureResult} from 'posthog-js'
import { PostHogProvider as PHProvider } from 'posthog-js/react'
import { useEffect } from 'react'
import {usePostHogUser} from "@/hooks/use-posthog-user";
import PostHogPageView from "@/components/base/posthog-pageview"

export function CSPostHogProvider({ children }: { children: React.ReactNode }) {
    useEffect(() => {
        posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
            api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
            before_send: (cr: CaptureResult | null): CaptureResult | null => {
                if (process.env.NEXT_PUBLIC_ENV === 'production') {
                    return cr;
                }
                return null;
            },
            capture_pageview: false, // Disable automatic pageview capture, as we capture manually
            capture_pageleave: true,
            autocapture:true,
            save_campaign_params: true,
            capture_heatmaps: true,
            enable_heatmaps: true,
            persistence: 'localStorage', // Use localStorage for better persistence
            bootstrap: {
                distinctID: undefined, // Will be set by usePostHogUser
                isIdentifiedID: false
            },
            session_recording: {
                recordCrossOriginIframes: true
            }
        })
    }, [])

    return (
        <PHProvider client={posthog}>
            <PostHogUserTracker>
                <PostHogPageView />
            {children}
            </PostHogUserTracker>
            </PHProvider>
    )
}

// Internal component to handle user tracking
function PostHogUserTracker({ children }: { children: React.ReactNode }) {
    usePostHogUser(); // This will handle user identification
    return <>{children}</>;
}