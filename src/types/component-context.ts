export interface ComponentContext {
  id: string;
  componentName: string;
  element: string;
  sourceFile: string;
  lineNumber: number;
  screenshot: string; // Base64 string initially
  imageUrl?: string;  // URL after upload
  isUploading?: boolean;
  uploadError?: string;
}

export interface ComponentContextAttachment {
  name: string;
  url: string;
  contentType: string;
  componentContext?: {
    componentName: string;
    element: string;
    sourceFile: string;
    lineNumber: number;
  };
}
