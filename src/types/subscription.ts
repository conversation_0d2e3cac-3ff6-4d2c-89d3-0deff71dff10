import { PlanTier } from '@/lib/subscription/plans';

export interface SubscriptionStatus {
  isActive: boolean;
  planTier: PlanTier;
  planName: string;
  expiresAt?: string | null;
  credits: {
    total: number;
    used: number;
    remaining: number;
  };
  dailyLimit?: number;
  dailyRemaining?: number;
  features?: string[];
  messageLimit?: number;
  messagesRemaining?: number;
  isPro?: boolean;
}
