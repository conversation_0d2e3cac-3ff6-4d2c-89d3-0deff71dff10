interface TokenConsumptionInput {
    model: string;  // This should be the apiIdentifier
    totalTimeToken: number;
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    chatId: string;
    projectId: string;
    messageId: string;
    userId: string;
    isAnonymous: boolean;
    remoteProviderId: string;
    remoteProvider: string;
    creditsConsumed: number;
    discountedCredits?: number;
    discountReason?: string;
    errorId?: string;
    isAutoFixed?: boolean;
}