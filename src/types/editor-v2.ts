export interface BaseNodeV2 {
    name: string;
    type: 'file' | 'directory';
    absolutePath: string;  // New property for absolute path
}

export interface FileNodeV2 extends BaseNodeV2 {
    type: 'file';
    language: string;
    content: string;
    changes?: number;
}


export type FileItemV2 = FileNodeV2;

export interface FileOperation {
    type: 'create' | 'edit' | 'delete';
    absolutePath: string;
    content?: string;
    language?: string;
    name?: string
}

export interface ActiveFile {
    name: string;
    absolutePath: string;
    content: string;
    language: string;
}

// Utility functions
export const getFileNameFromPath = (path: string): string => {
    return path.split('/').pop() || path;
};

export const getDirectoryFromPath = (path: string): string => {
    const parts = path.split('/');
    parts.pop();
    return parts.join('/');
};

export const isDescendantPath = (parentPath: string, childPath: string): boolean => {
    return childPath.startsWith(parentPath + '/');
};
