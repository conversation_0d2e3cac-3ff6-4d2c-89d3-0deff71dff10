export type FileItem = FileNode;

export interface CodeBlock {
    type: 'edit' | 'create' | null,
    fileName?: string,  // For backward compatibility
    absolutePath?: string,  // New field for absolute paths
    content: string
}

export interface BaseNode {
    name: string,
    type: 'file'
}

export interface FileNode extends BaseNode {
    type: 'file',
    language: string,
    content: string,
    changes?: number
}