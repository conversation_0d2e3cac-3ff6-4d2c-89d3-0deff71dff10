import 'next-auth';
import { JWT } from '@auth/core/jwt';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string | null;
      image?: string | null;
      provider?: string;
      isNewUser?: boolean;
    };
  }

  interface User {
    id: string;
    email: string;
    name?: string | null;
    image?: string | null;
    provider?: string;
  }
}

declare module '@auth/core/jwt' {
  interface JWT {
    id: string;
    email: string;
    name?: string | null;
    picture?: string | null;
    provider?: string;
    isNewUser?: boolean;
  }
}
