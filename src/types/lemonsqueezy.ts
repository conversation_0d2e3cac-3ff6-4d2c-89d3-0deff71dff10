export interface LemonSqueezyWebhookEvent {
  meta: {
    event_name: string;
    custom_data: Record<string, any>;
  };
  data: {
    type: string;
    id: string;
    attributes: {
      store_id: number;
      customer_id: number;
      order_id: number;
      product_id: number;
      variant_id: number;
      status: string;
      status_formatted: string;
      user_name: string;
      user_email: string;
      created_at: string;
      updated_at: string;
      ends_at?: string;
      renews_at?: string;
      cancelled?: boolean;
      cancelled_at?: string;
      metadata?: Record<string, any>;
    };
  };
}

export interface CheckoutOptions {
  store_id: number;
  variant_id: number;
  custom_price?: number;
  product_options?: {
    name?: string;
    description?: string;
    media?: string[];
    redirect_url?: string;
  };
  checkout_options?: {
    dark?: boolean;
    logo?: string;
    desc?: string;
    terms_url?: string;
    privacy_url?: string;
  };
  checkout_data?: {
    email?: string;
    name?: string;
    billing_address?: {
      country?: string;
    };
    tax_number?: string;
    custom_data?: string[];
  };
  preview?: boolean;
}

export interface SubscriptionStatus {
  isActive: boolean;
  planId?: string;
  subscriptionId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface UpdateSubscriptionOptions {
  subscriptionId: string;
  variantId: string | number;
  productId: string | number;
  invoiceImmediately?: boolean;
  disableProrations?: boolean;
}
