// TestFlight related types

export type BuildStatus = 
  | 'idle' 
  | 'configuring' 
  | 'preparing' 
  | 'building' 
  | 'submitting' 
  | 'completed' 
  | 'failed';

export type BuildConfig = {
  appName: string;
  bundleId: string;
  version: string;
  buildNumber: string;
  icon?: string;
  appleTeamId?: string;
};

export type BuildProgress = {
  status: BuildStatus;
  message: string;
  progress: number;
  error?: string;
  testFlightLink?: string;
  buildId?: string;
  logs: Array<{
    timestamp: number;
    message: string;
    type: 'info' | 'error' | 'warning' | 'success';
  }>;
};

export type TestFlightInvite = {
  email: string;
  name?: string;
  sent: boolean;
  status?: 'pending' | 'accepted' | 'declined';
};
