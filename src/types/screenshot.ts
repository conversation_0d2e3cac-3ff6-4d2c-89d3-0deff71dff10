/**
 * Types for screenshot metadata and storage
 */

/**
 * Metadata for a single screenshot
 */
export interface ScreenshotMetadata {
  id: string;         // Unique identifier for the screenshot
  name: string;       // Name of the screen (e.g., "Home", "Profile")
  description?: string; // Description of what the screen shows
  elements?: string[]; // List of UI elements present in the screenshot
  url: string;        // URL to the screenshot image
  order: number;      // Order of the screenshot in the flow
  tags?: string[];    // Tags for categorizing screenshots
}

/**
 * Simple format for screenshot URLs used in legacy code
 */
export interface ScreenshotUrls {
  metadata: {
    screens: ScreenshotMetadata[];
  };
}
