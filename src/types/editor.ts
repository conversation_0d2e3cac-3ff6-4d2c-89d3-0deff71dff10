import TEMPLATES from "../lib/data/templates.json"

export interface CodeFile {
    name: string;
    content: string;
    language: string;
}

export interface EditorState {
    files: CodeFile[];
    activeFile: string;
}

export const DEFAULT_DEPENDENCIES: { [key: string]: { version: string } } = {
    'expo-status-bar': {version: '*'},
    'expo-linear-gradient': {version: '*'},
    '@expo/vector-icons': {version: '*'},
    '@react-navigation/native': {version: '*'},
    '@react-navigation/native-stack': {version: '*'},
    'react-native-safe-area-context': {version: '*'},
    'react-native-screens': {version: '*'},
    '@react-navigation/bottom-tabs': {version: "*"},
    'expo-camera': {version: '*'},
    'react-native-reanimated': {version: '*'},
    'expo-clipboard': {version: '*'},
    '@shopify/flash-list': {version: '*'},
    'expo-av': {version: '*'},
    'react-native-gesture-handler': {version: '*'},
    'lucide-react-native': {version: '*'},
    'react-native-svg': {version: '15.11.2'},
    '@react-native-async-storage/async-storage': {version: '*'},
    "@supabase/supabase-js": {version: "*"},
    "react-hook-form": {version: "*"},
    "sonner-native": {version: "*"},
    "expo-auth-session": {version: "*"},
    "expo-crypto": {version: "*"},
    "expo-haptics": {version: "*"},
    "@react-navigation/drawer": {version: "*"},
    "@react-native-community/slider": {version: "*"},
    "expo-web-browser": {version: "*"},
    "expo-linking": {version: "*"},
    "expo-constants": {version: "*"},
    "expo-image-picker": {version: "*"},
    "@react-native-community/datetimepicker": {version: "*"},
    "expo-sensors": {version: "*"},
    "@react-native-community/netinfo": {version: "*"},
    "expo-audio": {version: "*"},
    "expo-blur": {version: "*"},
    "expo-file-system": {version: "*"},
    "expo-image-manipulator": {version: "*"},
    "expo-location": {version: "*"},
    "zustand": {version: "*"},
    "date-fns": {version: "*"},
    "react-native-webview": {version: "*"},
    "zustand/middleware": {version: "*"}
}

const DEFAULT_KEY = 'bare'
let code = TEMPLATES.find(template => {
    return template.templateName === DEFAULT_KEY
});

if (!code) {
    code = TEMPLATES[0];
}

export const DEFAULT_CODE: CodeFile[] = code.files;
