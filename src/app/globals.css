@tailwind base;
@tailwind components;
@tailwind utilities;

/* Typography Styles */
@layer components {
  .prose {
    @apply max-w-none;
  }
  
  .prose :where(h1, h2, h3, h4) {
    @apply mt-8 mb-4;
  }

  .prose :where(p, ul, ol) {
    @apply my-5;
  }
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

/* Prevent Mac back gesture from interfering with horizontal scrolling */
.prevent-overscroll {
  overscroll-behavior-x: contain;
  -webkit-overflow-scrolling: touch;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    /* Base dark theme with a subtle purple tint */
    --background: 250 15% 5%;
    --foreground: 0 0% 98%;
    --card: 250 15% 8%;
    --card-foreground: 0 0% 98%;
    --popover: 250 15% 5%;
    --popover-foreground: 0 0% 98%;
    
    /* Vibrant coral/orange */
    --primary: 14 65% 56%;;
    --primary-foreground: 0 0% 100%;
    
    /* Secondary colors */
    --secondary: 260 20% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 260 15% 14.9%;
    --muted-foreground: 0 0% 70%;
    
    /* Vibrant yellow/amber accent */
    --accent: 162 45% 69%;;
    --accent-foreground: 0 0% 10%;
    
    /* Other UI elements */
    --destructive: 0 70% 40%;
    --destructive-foreground: 0 0% 98%;
    --border: 260 15% 14.9%;
    --input: 260 15% 14.9%;
    --ring: 260 70% 50%;
    
    /* Vibrant chart colors */
    --chart-1: 260 70% 50%; /* Purple */
    --chart-2: 30 90% 60%;  /* Amber */
    --chart-3: 200 75% 55%; /* Blue */
    --chart-4: 330 80% 55%; /* Pink */
    --chart-5: 120 60% 45%; /* Green */
    
    /* Sidebar styling - professional black and white */
    --sidebar-background: 260 20% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 20%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 85%;
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 260 10% 15%;
    --sidebar-ring: 0 0% 30%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Animation styles for notifications */
@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out forwards;
}

/* Gradient blob animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Enhanced GPU-accelerated blob animations with substantial movement */
@keyframes blob-move-1 {
  0%, 100% { transform: translate3d(0, 0, 0) scale(1); }
  33% { transform: translate3d(100px, -100px, 0) scale(1.05); }
  66% { transform: translate3d(-60px, 60px, 0) scale(0.95); }
}

@keyframes blob-move-2 {
  0%, 100% { transform: translate3d(0, 0, 0) scale(1); }
  33% { transform: translate3d(-80px, -40px, 0) scale(1.05); }
  66% { transform: translate3d(50px, 30px, 0) scale(0.98); }
}

@keyframes blob-move-3 {
  0%, 100% { transform: translate3d(0, 0, 0) scale(1); }
  33% { transform: translate3d(-50px, 90px, 0) scale(1.02); }
  66% { transform: translate3d(70px, -60px, 0) scale(0.98); }
}

@keyframes fabric-float {
  0%, 100% { transform: translate3d(0, 0, 0); }
  50% { transform: translate3d(0, -5px, 0); }
}

@keyframes particles-drift {
  0% { background-position: 0 0, 15px 15px; }
  100% { background-position: 30px 30px, -15px -15px; }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Mask for gradient borders */
.mask-border-gradient {
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

/* CSS Variables for sidebar width adjustment */
:root {
  --chat-width-adjustment: 100%;
  --preview-width-adjustment: 100%;
}

/* Add smooth transition for width changes */
[style*="--chat-width-adjustment"],
[style*="--preview-width-adjustment"] {
  transition: width 0.3s ease-in-out;
}

@keyframes fabric-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes particles-drift {
  0% { background-position: 0 0, 20px 20px; }
  100% { background-position: 20px 20px, 0 0; }
}

.animate-fabric-float {
  animation: fabric-float 10s ease-in-out infinite;
}

.animate-particles-drift {
  animation: particles-drift 15s linear infinite;
}

@keyframes blob-move-1 {
  0%, 100% { transform: translate3d(0, 0, 0) scale(1); }
  33% { transform: translate3d(100px, -100px, 0) scale(1.05); }
  66% { transform: translate3d(-60px, 60px, 0) scale(0.95); }
}

@keyframes blob-move-2 {
  0%, 100% { transform: translate3d(0, 0, 0) scale(1); }
  33% { transform: translate3d(-25px, -15px, 0) scale(1.05); }
  66% { transform: translate3d(15px, 10px, 0) scale(0.98); }
}

@keyframes blob-move-3 {
  0%, 100% { transform: translate3d(0, 0, 0) scale(1); }
  33% { transform: translate3d(-15px, 30px, 0) scale(1.02); }
  66% { transform: translate3d(25px, -20px, 0) scale(0.98); }
}

@keyframes fabric-float {
  0%, 100% { transform: translate3d(0, 0, 0); }
  50% { transform: translate3d(0, -5px, 0); }
}

@keyframes particles-drift {
  0% { background-position: 0 0, 15px 15px; }
  100% { background-position: 30px 30px, -15px -15px; }
}
