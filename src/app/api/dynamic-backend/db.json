{"deployments": {"any-project:test": {"projectId": "any-project", "deploymentId": "test", "code": "app.get('/', async (request, reply) => { console.log('Root endpoint called'); return { message: 'Hello from dynamic backend!', projectId: request.params.projectId, query: request.query, timestamp: new Date().toISOString() }; }); app.post('/data', async (request, reply) => { console.log('Data endpoint called'); return { message: 'Data received', body: request.body, success: true }; });", "createdAt": "2025-04-18T13:35:00.000Z"}}}