import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { auth } from '@/app/(auth)/auth';
import { getBackendCodeForProject, getBackendCodeForDeployment, processFastifyRoutes } from '@/lib/db/backend-queries';
import { getAllowedPackageMap } from '@/lib/config/allowed-packages';

// Interface for environment variables
interface EnvironmentVariables {
  [key: string]: string;
}

// Function to get environment variables for a project
async function getEnvironmentVariables(projectId: string): Promise<EnvironmentVariables> {
  try {
    // This is a placeholder implementation
    // In a real implementation, you would fetch environment variables from your database
    // based on the project ID
    
    // For now, we'll return an empty object
    // You should replace this with your actual database query
    console.log(`Fetching environment variables for project ${projectId}`);
    
    // TODO: Replace with actual database query to fetch environment variables
    // Example implementation:
    // const result = await db.$queryRaw`SELECT key, value FROM environment_variables WHERE project_id = ${projectId}`;
    // return result.reduce((acc, row) => {
    //   acc[row.key] = row.value;
    //   return acc;
    // }, {});
    
    return {};
  } catch (error) {
    console.error(`Error fetching environment variables for project ${projectId}:`, error);
    return {};
  }
}

// Keep the store for backward compatibility during testing
const { getCodeSnippet } = require('../../../store');

// Use Node.js runtime for local development
export const runtime = 'nodejs';

// Pre-load allowed packages to avoid dynamic import issues
const preloadedPackages: Record<string, any> = {
  'lodash': require('lodash'),
  'date-fns': require('date-fns'),
  'uuid': require('uuid'),
  'axios': require('axios'),
  'joi': require('joi'),
  'validator': require('validator'),
  'marked': require('marked'),
  'json5': require('json5'),
  'crypto-js': require('crypto-js'),
  'jsonwebtoken': require('jsonwebtoken'),
  'dotenv': require('dotenv')
  // Note: Some packages like node-fetch, csv-parser, and papaparse may have ESM-only exports
  // and might need special handling
};

// Helper function to get CORS headers
function getCorsHeaders(request: NextRequest) {
  const origin = request.headers.get('origin');
  const allowedOrigin = origin && 
    (origin.endsWith('.magically.life') || origin === 'https://magically.life' || origin === "http://localhost:3000")
    ? origin 
    : '*';

  return {
    'Access-Control-Allow-Origin': allowedOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, ngrok-skip-browser-warning',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400' // 24 hours
  };
}

// Function to create a mock Fastify environment
function createMockFastifyEnvironment() {
  // Routes storage
  const routes: {
    method: string;
    path: string;
    handler: ((request: any, reply: any) => Promise<any>) | {
      preHandler?: ((request: any, reply: any) => Promise<any>) | ((request: any, reply: any) => Promise<any>)[];
      handler: (request: any, reply: any) => Promise<any>;
    };
  }[] = [];
  
  // Helper function to handle both function and object handlers
  const registerRoute = (method: string, path: string, handler: any) => {
    routes.push({ method, path, handler });
  };
  
  // Mock Fastify app
  const app = {
    get: (path: string, handler: any) => {
      registerRoute('GET', path, handler);
    },
    post: (path: string, handler: any) => {
      registerRoute('POST', path, handler);
    },
    put: (path: string, handler: any) => {
      registerRoute('PUT', path, handler);
    },
    delete: (path: string, handler: any) => {
      registerRoute('DELETE', path, handler);
    },
    patch: (path: string, handler: any) => {
      registerRoute('PATCH', path, handler);
    }
  };
  
  return { app, routes };
}


// Handle OPTIONS request (preflight)
export async function OPTIONS(request: NextRequest, {params}: {
  params: Promise<{ projectId: string; deploymentId: string; path: string[] }>
}) {
  // Return 204 No Content with CORS headers
  return new NextResponse(null, {
    status: 204,
    headers: getCorsHeaders(request)
  });
}

// Handler for all HTTP methods
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; deploymentId: string; path: string[] }> }
) {
  return handleRequest(request, params, 'GET');
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; deploymentId: string; path: string[] }> }
) {
  return handleRequest(request, params, 'POST');
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; deploymentId: string; path: string[] }> }
) {
  return handleRequest(request, params, 'PUT');
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; deploymentId: string; path: string[] }> }
) {
  return handleRequest(request, params, 'DELETE');
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; deploymentId: string; path: string[] }> }
) {
  return handleRequest(request, params, 'PATCH');
}

// Main request handler
async function handleRequest(
  request: NextRequest,
  params: Promise<{ projectId: string; deploymentId: string; path: string[] }>,
  method: string
) {
  const { projectId, deploymentId, path } = await params;
  
  try {
    // First try to get code from the database
    let codeSnippet: any = null;
    let code: any = null;
    
    // Special case for 'test' deploymentId - use the in-memory store
    if (deploymentId === 'test') {
      codeSnippet = getCodeSnippet(projectId, deploymentId);
      code = codeSnippet?.code;
    } else {
      let deploymentCode;
      if (deploymentId !== 'latest') {
        // Try to get code from a specific deployment first
        deploymentCode = await getBackendCodeForDeployment(deploymentId);
      }
      
      if (deploymentCode) {
        code = deploymentCode.code;
        codeSnippet = {
          projectId,
          deploymentId,
          code,
          createdAt: new Date()
        };
      } else {
        // If no specific deployment found, try to get the latest code for the project
        const projectCode = await getBackendCodeForProject(projectId);

        if (projectCode) {
          code = processFastifyRoutes(projectCode.code);
          codeSnippet = {
            projectId,
            deploymentId: 'latest',
            code,
            createdAt: new Date()
          };
        } else {
          // Fallback to in-memory store
          codeSnippet = getCodeSnippet(projectId, deploymentId);
        }
      }
    }
    
    if (!codeSnippet) {
      // Add CORS headers to the error response
      const corsHeaders = getCorsHeaders(request);
      
      return NextResponse.json(
        { error: 'Backend not found', projectId, deploymentId },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }
    
    // Create mock Fastify environment
    const { app, routes } = createMockFastifyEnvironment();
    
    // Execute the code in a safer environment using Function constructor
    try {
      // Fetch environment variables for this project
      const envVars = await getEnvironmentVariables(projectId);
      
      // Create a safe require function that only allows whitelisted packages
      const allowedPackages = getAllowedPackageMap();
      const safeRequire = (packageName: string) => {
        // Check if the package is in our whitelist
        if (allowedPackages[packageName]) {
          try {
            // First check if we have the package preloaded
            if (preloadedPackages[packageName]) {
              return preloadedPackages[packageName];
            }
            
            // Fall back to dynamic require if not preloaded
            return require(packageName);
          } catch (error: any) {
            console.error(`Error requiring package ${packageName}:`, error);
            throw new Error(`Package ${packageName} is allowed but failed to load: ${error?.message || 'Unknown error'}`);
          }
        } else {
          throw new Error(`Package '${packageName}' is not in the allowed list. Only the following packages are allowed: ${Object.keys(allowedPackages).join(', ')}`);
        }
      };
      
      // Create a process.env object with the environment variables
      const processEnv = {
        ...envVars,
        NODE_ENV: process.env.NODE_ENV || 'development',
        PROJECT_ID: projectId,
        DEPLOYMENT_ID: deploymentId
      };
      
      // Create a sandbox environment with controlled access
      const executeCode = new Function(
        'app', 
        'console', 
        'require',
        'process',
        codeSnippet.code
      );
      
      // Execute with controlled console access
      const controlledConsole = {
        log: (...args: any[]) => console.log(`[${projectId}/${deploymentId}]`, ...args),
        error: (...args: any[]) => console.error(`[${projectId}/${deploymentId}]`, ...args),
        warn: (...args: any[]) => console.warn(`[${projectId}/${deploymentId}]`, ...args),
      };
      
      // Create a limited process object with only env
      const limitedProcess = {
        env: processEnv
      };
      
      // Execute the code to register routes
      executeCode(app, controlledConsole, safeRequire, limitedProcess);
    } catch (error) {
      console.error(`Error executing code for ${projectId}/${deploymentId}:`, error);
      // Provide detailed error information for sandbox code execution errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error && process.env.NODE_ENV === 'development' ? error.stack : undefined;
      
      // Add CORS headers to the error response
      const corsHeaders = getCorsHeaders(request);
      
      return NextResponse.json(
        { 
          error: 'Code execution error', 
          message: errorMessage,
          stack: errorStack,
          projectId,
          deploymentId,
          location: 'sandbox_execution'
        },
        { 
          status: 500,
          headers: corsHeaders
        }
      );
    }
    
    // Construct the path from the catch-all segments
    const requestPath = '/' + path.join('/');
    
    // Find matching route
    const matchingRoute = routes.find(route => {
      if (method !== route.method) return false;
      
      // Exact path match
      if (route.path === requestPath) return true;
      
      // Root path special case
      if (requestPath === '/' && (route.path === '/' || route.path === '')) return true;
      
      // Wildcard route
      if (route.path === '*') return true;
      
      // Parameter matching (simple implementation)
      // Convert route path to regex pattern
      // e.g. '/users/:id' becomes /^\/users\/([^\/]+)$/
      if (route.path.includes(':')) {
        const pattern = route.path
          .replace(/:[^\/]+/g, '([^\\/]+)')
          .replace(/\//g, '\\/');
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(requestPath);
      }
      
      return false;
    });
    
    if (!matchingRoute) {
      // Add CORS headers to the error response
      const corsHeaders = getCorsHeaders(request);
      
      return NextResponse.json(
        { error: 'Route not found', path: requestPath, method },
        { 
          status: 404,
          headers: corsHeaders
        }
      );
    }
    
    // Extract path parameters
    const pathParams: Record<string, string> = {};
    if (matchingRoute.path.includes(':')) {
      const routeParts = matchingRoute.path.split('/');
      const requestParts = requestPath.split('/');
      
      routeParts.forEach((part, index) => {
        if (part.startsWith(':') && requestParts[index]) {
          const paramName = part.substring(1);
          pathParams[paramName] = requestParts[index];
        }
      });
    }
    
    // Parse request body
    let body: any = null;
    if (request.body) {
      const contentType = request.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        body = await request.json();
      } else if (contentType?.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData();
        body = Object.fromEntries(formData);
      }
    }
        // Create mock request and reply objects
      const mockRequest = {
        params: {
          projectId,
          deploymentId,
          ...pathParams
        },
        query: Object.fromEntries(new URL(request.url).searchParams),
        body,
        headers: Object.fromEntries(request.headers.entries()),
        url: request.url,
        method: request.method,
        ip: request.headers.get('x-forwarded-for') || '127.0.0.1'
      };
      
      const mockReply = {
        code: (statusCode: number) => {
          mockReply.statusCode = statusCode;
          return mockReply;
        },
        header: (name: string, value: string) => {
          mockReply.headers[name] = value;
          return mockReply;
        },
        send: (payload: any) => {
          // Store the payload for later use
          mockReply.payload = payload;
          mockReply.sent = true;
          return mockReply;
        },
        json: (payload: any) => {
          // Store the payload and set content-type
          mockReply.payload = payload;
          mockReply.headers['content-type'] = 'application/json';
          mockReply.sent = true;
          return mockReply;
        },
        status: (statusCode: number) => {
          mockReply.statusCode = statusCode;
          return mockReply;
        },
        statusCode: 200,
        headers: {} as Record<string, string>,
        payload: undefined as any,
        sent: false
      };

      // Get CORS headers for responses
      const corsHeaders = getCorsHeaders(request);

      // Execute the route handler with proper error handling
      try {
        // Determine if the handler is a function or an object with handler/preHandler
        let handlerFunction;
        let preHandlers: ((request: any, reply: any) => Promise<any>)[] = [];
        
        if (typeof matchingRoute.handler === 'function') {
          // Direct handler function
          handlerFunction = matchingRoute.handler;
        } else if (typeof matchingRoute.handler === 'object' && matchingRoute.handler !== null) {
          // Handler is a route configuration object
          if (typeof matchingRoute.handler.handler === 'function') {
            handlerFunction = matchingRoute.handler.handler;
            
            // Extract preHandlers if they exist
            if (matchingRoute.handler.preHandler) {
              if (Array.isArray(matchingRoute.handler.preHandler)) {
                preHandlers = matchingRoute.handler.preHandler;
              } else {
                preHandlers = [matchingRoute.handler.preHandler];
              }
            }
          } else {
            throw new Error(`Invalid route handler for ${requestPath}: handler.handler is not a function`);
          }
        } else {
          throw new Error(`Invalid route handler for ${requestPath}: handler is not a function or object`);
        }
        
        // Execute preHandlers in sequence
        for (const preHandler of preHandlers) {
          await preHandler(mockRequest, mockReply);
          
          // If preHandler set an error or ended the request, stop processing
          if (mockReply.sent || (mockReply.payload && mockReply.statusCode >= 400)) {
            return NextResponse.json(mockReply.payload || { error: 'Unauthorized' }, {
              status: mockReply.statusCode,
              headers: {
                ...mockReply.headers,
                ...corsHeaders
              }
            });
          }
        }
        
        // Execute the main handler
        const result = await handlerFunction(mockRequest, mockReply);
        
        // If result contains an error property, use it for status code if not already set
        if (result && typeof result === 'object' && 'error' in result && mockReply.statusCode === 200) {
          mockReply.statusCode = 400; // Default error status if not specified
        }
        
        // Return the response with CORS headers
        return NextResponse.json(mockReply.sent ? mockReply.payload : result, {
          status: mockReply.statusCode,
          headers: {
            ...mockReply.headers,
            ...corsHeaders
          }
        });
      } catch (handlerError: any) {
        console.error(`Error in route handler for ${requestPath}:`, handlerError);
        
        // Determine appropriate status code
        const statusCode = mockReply.statusCode !== 200 ? mockReply.statusCode : 500;
        
        // Format the error response
        return NextResponse.json(
          {
            error: 'Route handler error',
            message: handlerError.message || 'Unknown error in route handler',
            path: requestPath,
            method,
            stack: process.env.NODE_ENV === 'development' ? handlerError.stack : undefined
          },
          { 
            status: statusCode,
            headers: {
              ...mockReply.headers,
              ...corsHeaders
            }
          }
        );
      }
    
  } catch (error: any) {
    console.error(`Error executing dynamic backend for ${projectId}/${deploymentId}:`, error);
    
    // Determine if this is a known error type and format accordingly
    let errorStatus = 500;
    let errorResponse: {
      error: string;
      message: string;
      stack?: string;
      path?: string;
      method?: string;
    } = { 
      error: 'Execution error',
      message: error.message || 'Unknown error',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    };
    
    // Check for specific error types
    if (error.code === 'NOT_FOUND' || error.statusCode === 404) {
      errorStatus = 404;
      errorResponse.error = 'Resource not found';
    } else if (error.statusCode && typeof error.statusCode === 'number') {
      errorStatus = error.statusCode;
    } else if (error.code === 'VALIDATION_ERROR') {
      errorStatus = 400;
      errorResponse.error = 'Validation error';
    }
    
    // Add request context to help with debugging
    errorResponse.path = path.join('/');
    errorResponse.method = method;
    
    // Add CORS headers to the error response
    const corsHeaders = getCorsHeaders(request);
    
    return NextResponse.json(errorResponse, { 
      status: errorStatus,
      headers: corsHeaders
    });
  }
}
