// Simple in-memory store for dynamic backend code
// This is a global module that will be shared across requests in local development

// Global store that persists between requests
const codeStore = new Map();

console.log('Store module initialized');

// Debug function to log the current state of the store
function logStoreState() {
  console.log(`Store has ${codeStore.size} items`);
  console.log(`Keys: ${Array.from(codeStore.keys()).join(', ')}`);
}

// Add test deployment
codeStore.set('any-project:test', `
  app.get('/', async (request, reply) => {
    console.log('Root endpoint called');
    return { 
      message: 'Hello from dynamic backend!',
      projectId: request.params.projectId,
      query: request.query,
      timestamp: new Date().toISOString()
    };
  });
  
  app.post('/data', async (request, reply) => {
    console.log('Data endpoint called');
    return { 
      message: 'Data received',
      body: request.body,
      success: true
    };
  });
`);

// Export functions to interact with the store
exports.getCodeSnippet = function(projectId, deploymentId) {
  const key = `${projectId}:${deploymentId}`;
  console.log(`Attempting to retrieve code for ${key}`);
  logStoreState();
  
  const code = codeStore.get(key);
  
  if (code) {
    console.log(`Found code for ${key}`);
    return {
      projectId,
      deploymentId,
      code,
      createdAt: new Date()
    };
  }
  
  console.log(`No code found for ${key}`);
  return null;
};

exports.storeCodeSnippet = function(projectId, deploymentId, code) {
  const key = `${projectId}:${deploymentId}`;
  console.log(`Storing code for ${key}`);
  console.log(`Code: ${code.substring(0, 50)}...`);
  
  codeStore.set(key, code);
  
  console.log(`Stored code for ${key}`);
  logStoreState();
  
  // Verify storage
  const storedCode = codeStore.get(key);
  console.log(`Verification - Code exists: ${!!storedCode}`);
  if (storedCode) {
    console.log(`Verification - Code starts with: ${storedCode.substring(0, 20)}...`);
  }
};

exports.listDeployments = function() {
  return Array.from(codeStore.keys()).map(key => {
    const [projectId, deploymentId] = key.split(':');
    return {
      projectId,
      deploymentId,
      createdAt: new Date()
    };
  });
};
