import { NextResponse } from 'next/server';
import {VARIANT_IDS, verifyWebhookSignature} from '@/lib/lemonsqueezy';
import { LemonSqueezyWebhookEvent } from '@/types/lemonsqueezy';
import { subscriptions, user } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { db } from '@/lib/db';
import { PLANS, PlanTier } from '@/lib/subscription/plans';


export async function POST(request: Request) {
  try {
    const signature = request.headers.get('x-signature');
    if (!signature) {
      return NextResponse.json({ error: 'No signature' }, { status: 400 });
    }

    const body = await request.text();
    console.log('body', body)
    const isValid = await verifyWebhookSignature(body, signature);

    console.log('isValid', isValid)

    if (!isValid) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    const event = JSON.parse(body) as LemonSqueezyWebhookEvent;
    console.log('event', event)
    const { event_name } = event.meta;
    const userId = event.meta.custom_data[0];
    const { user_email, status } = event.data.attributes;

    if(!['subscription_cancelled', 'subscription_updated', 'subscription_created'].includes(event_name)) {
      return NextResponse.json({result: "Event not supported"});
    }
    // Find user by email
    const userResult = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (!userResult.length) {
      console.error('User not found:', user_email);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get plan tier from variant ID
    const variantId = event.data.attributes.variant_id.toString();
    
    // Map variant ID to plan tier
    let planTier: PlanTier = 'starter'; // Default to starter
    if (variantId === VARIANT_IDS.starter) {
      planTier = 'starter';
    } else if (variantId === VARIANT_IDS.pro) {
      planTier = 'pro';
    } else if (variantId === VARIANT_IDS.plus) {
      planTier = 'plus';
    }
    
    // Check if this is a downgrade by comparing with previous subscription
    let isDowngrade = false;
    if (event_name === 'subscription_updated') {
      // Get the previous subscription to check if this is a downgrade
      const previousSubscription = await db
        .select()
        .from(subscriptions)
        .where(eq(subscriptions.userId, userId))
        .orderBy(desc => desc.updatedAt)
        .limit(1)
        .then(results => results[0]);
      
      if (previousSubscription) {
        const previousPlan = previousSubscription.planId as PlanTier;
        const previousPlanIndex = PLANS.findIndex(p => p.tier === previousPlan);
        const newPlanIndex = PLANS.findIndex(p => p.tier === planTier);
        
        // If new plan index is lower than previous, it's a downgrade
        isDowngrade = newPlanIndex < previousPlanIndex;
      }
    }
    
    // Get plan details
    const plan = PLANS.find(p => p.tier === planTier);
    if (!plan) {
      console.error('Plan not found for variant ID:', variantId);
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 });
    }
    
    // Calculate next reset date (1 month from now)
    const resetDate = new Date();
    resetDate.setMonth(resetDate.getMonth() + 1);
    resetDate.setHours(0, 0, 0, 0);
    
    // Handle subscription events
    switch (event_name) {
      case 'subscription_created':
      case 'subscription_updated':
        await db
          .insert(subscriptions)
          .values({
            userId: userId,
            status: status,
            planId: planTier,
            subscriptionId: event.data.id,
            isActive: status === 'active',
            credits: plan.operations,
            creditsUsed: 0, // Reset credits used on new subscription
            lemonSqueezyCustomerId: event.data.attributes.customer_id?.toString(),
            lemonSqueezyOrderId: event.data.attributes.order_id?.toString(),
            lemonSqueezySubscriptionId: event.data.id?.toString(),
            lemonSqueezyVariantId: variantId,
            provider: 'lemonsqueezy',
            resetDate: resetDate,
            isDowngraded: isDowngrade, // Flag downgrades
            createdAt: new Date(event.data.attributes.created_at),
            updatedAt: new Date(event.data.attributes.updated_at),
          })
          .onConflictDoUpdate({
            target: [subscriptions.subscriptionId],
            set: {
              status: status,
              isActive: status === 'active',
              planId: planTier,
              credits: plan.operations,
              // Don't reset creditsUsed on update unless it's a reactivation
              ...(status === 'active' && { creditsUsed: 0 }),
              resetDate: resetDate,
              isDowngraded: isDowngrade, // Flag downgrades
              updatedAt: new Date(event.data.attributes.updated_at),
            },
          });
        break;

      case 'subscription_cancelled':
        // For cancelled subscriptions, we need to check if they're still active until the end of the period
        // The subscription remains active until the ends_at date
        const endsAt = event.data.attributes.ends_at;
        const isStillActive = endsAt ? new Date(endsAt) > new Date() : false;
        
        // Get the current subscription to preserve existing metadata
        const currentSubscription = await db
          .select()
          .from(subscriptions)
          .where(eq(subscriptions.subscriptionId, event.data.id))
          .limit(1)
          .then(results => results[0]);
        
        await db
          .update(subscriptions)
          .set({
            status: 'cancelled',
            // Keep subscription active until the end date
            isActive: isStillActive,
            // Store the end date for reference
            metadata: {
              ...(currentSubscription?.metadata || {}),
              endsAt: endsAt,
              cancelledAt: new Date().toISOString(),
            },
            updatedAt: new Date(),
          })
          .where(eq(subscriptions.subscriptionId, event.data.id));
        break;
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook error' }, { status: 500 });
  }
}
