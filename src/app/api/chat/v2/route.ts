import {
    createDataStreamResponse,
    streamText,
    smoothStream,
    type Message,
    CoreUserMessage,
    FinishReason,
    TextPart
} from 'ai';
import { auth } from "@/app/(auth)/auth";
import { generateUUID } from "@/lib/utils";
import { checkMessageLimit } from '@/lib/subscription';
import { customModel } from "@/lib/ai";
import { DEFAULT_MODEL_NAME, models } from "@/lib/ai/models";
import { MessageHandler } from '@/lib/chat/handlers';
import { enhancerPrompt } from "@/lib/services/prompt-enhancer";
import { FileLineManager } from '@/lib/editor/FileLineManager';
import { FileContentManager } from '@/lib/editor/FileContentManager';
import { PerformanceTracker } from '@/lib/utils/PerformanceTracker';
import { CreditUsageTracker } from "@/lib/credit/CreditUsageTracker";
import { isErrorFixingMessage } from "@/lib/utils/error-detection";
import { MOFileParser } from "@/lib/parser/StreamParser";
import { MODiffParser } from "@/lib/parser/DiffParser";
import { PlaceholderProcessingService } from "@/lib/services/PlaceholderProcessingService";
import { llmMediaService } from '@/lib/services/llm-media-service';
import { getFileContents } from "@/lib/chat/tools/get-file-contents";
import { queryCodebase } from "@/lib/chat/tools/query-codebase";
import { DEFAULT_DEPENDENCIES } from '@/types/editor';

// Mock functions to simulate database operations
// These would be replaced with actual implementations
const mockDbOperations = {
    getChatById: async ({ id }: { id: string }) => ({ id, userId: null, title: 'Mock Chat', projectId: null }),
    saveChat: async (data: any) => ({ ...data }),
    updateChat: async (data: any) => ({ ...data }),
    saveMessages: async (data: { messages: any[] }) => data.messages,
    saveFileStateAndCacheIfNeeded: async (data: any) => true,
    getBaseCacheVersion: async (id: string) => null,
    createOrFetchAnonUser: async (id: string) => ({ id, isAnonymous: true }),
    saveTokenConsumption: async (data: any) => true,
    updateCreditUsage: async (userId: string, operations: any) => true,
    saveProject: async (data: any) => ({ ...data }),
    updateProject: async (data: any) => ({ ...data }),
    getProjectById: async (data: { id: string }) => null,
};

export async function POST(request: Request) {
    // Create a unique request ID for tracking
    const requestId = `chat-api-v2-${Date.now()}`;
    const performanceTracker = new PerformanceTracker();
    performanceTracker.startTimer(requestId, 'Chat API V2 Request');

    // Parse request body - this is a critical operation
    let {
        id,
        messages,
        files,
        activeFile,
        dependencies,
        linkSupabaseProjectId,
        linkSupabaseConnection,
        projectId,
        logs,
        agentModeEnabled,
        isReload,
        isInitial
    }: {
        id: string;
        messages: Array<Message>;
        files: any[];
        activeFile: string;
        dependencies?: Record<string, any>;
        linkSupabaseProjectId?: string;
        linkSupabaseConnection?: string;
        projectId: string;
        logs?: any[];
        agentModeEnabled: boolean;
        isReload: boolean;
        isInitial?: boolean;
    } = await request.json();

    // Start with minimal validation
    const apiModelId = DEFAULT_MODEL_NAME;
    const model = models.find(model => model.id === apiModelId);

    if (!model) {
        return new Response("Model not found", { status: 400 });
    }

    // Create a unique ID for the user message
    const userMessageId = messages.findLast(message => message.role === 'user')?.id || generateUUID();

    // Start the data stream response immediately
    return createDataStreamResponse({
        execute: async (dataStream) => {
            try {
                // Send the user message ID first
                dataStream.writeData({
                    type: 'user-message-id',
                    content: userMessageId,
                });

                // Initialize message handler - critical for processing
                const messageHandler = new MessageHandler();
                const TOTAL_MESSAGES_FOR_TURNS_AND_CACHE = 5;
                await messageHandler.initialize(messages, TOTAL_MESSAGES_FOR_TURNS_AND_CACHE, {
                    projectId,
                    backendEnabled: false,
                    userId: 'dummy-user-id'
                });

                const userMessage = messageHandler.getCurrentUserMessage();
                if (!userMessage) {
                    throw new Error('No user message found');
                }

                // Initialize file managers - critical for processing
                const fileManager = new FileLineManager();
                fileManager.initializeFiles(files || []);

                const contentManager = new FileContentManager();
                files.forEach(file => {
                    const content = fileManager.getFinalContent(file.name);
                    if (content) {
                        contentManager.setFileContent(file.name, content);
                    }
                });

                // Initialize credit tracker
                const creditUsageTracker = new CreditUsageTracker();

                // Start authentication in parallel - non-blocking
                const authPromise = auth().then(session => {
                    const anonymousId = request.headers.get('x-anonymous-id');
                    let userId = session?.user?.id;
                    let isAnonymous = false;

                    // Handle anonymous users in parallel
                    if (!userId && anonymousId) {
                        return mockDbOperations.createOrFetchAnonUser(anonymousId)
                            .then(anonUser => {
                                userId = anonUser.id;
                                isAnonymous = true;
                                return { userId, isAnonymous };
                            });
                    }

                    return { userId: userId || null, isAnonymous };
                });

                // Check if this is the first message - determines if we need prompt enhancement
                const isFirstMessage = !messages.find(message => {
                    return message.role === "assistant" && !message.parts?.some(part => part.type === 'tool-invocation');
                });

                // Process image/video placeholders
                const placeholderService = new PlaceholderProcessingService(llmMediaService);
                const processImagePlaceholders = async (text: string) => {
                    const { processedText } = await placeholderService.processImagePlaceholders(text);
                    return processedText;
                };

                const processVideoPlaceholders = async (text: string) => {
                    const { processedText } = await placeholderService.processVideoPlaceholders(text);
                    return processedText;
                };

                // Initialize parsers
                const parser = new MOFileParser({
                    onFileStart: (meta) => {
                        console.log('File started:', meta.path);
                    },
                    onFileDelta: (path, chunk, file) => {
                        // File update handling
                    },
                    onFileComplete: async (meta) => {
                        fileManager.replaceFile(meta.path, meta.content);
                        contentManager.setFileContent(meta.path, meta.content);

                        let content = meta.content;
                        content = await processImagePlaceholders(content);
                        content = await processVideoPlaceholders(content);

                        // Check if user message contains error fixing criteria
                        const {isErrorFixing, errorUuid} = isErrorFixingMessage(userMessage.content || '');

                        // Only track credit usage if not fixing errors
                        if (!isErrorFixing) {
                            creditUsageTracker.trackOperation('code_write');
                        } else {
                            creditUsageTracker.trackDiscountedOperation('file_change', 'error_fixing', 1);
                        }

                        dataStream.writeData({
                            type: 'file-operation',
                            content: {
                                type: 'create',
                                absolutePath: meta.path,
                                content
                            }
                        });
                    }
                });

                const diffParser = new MODiffParser({
                    onDiffStart: (meta) => {
                        console.log('Diff started:', meta.path);
                    },
                    onDiffComplete: async (meta) => {
                        // Apply the diff to the file content
                        const result = await contentManager.applyDiff(meta, { bestEffort: true });

                        if (!result.success) {
                            console.error('Failed to apply diff:', result.message);
                            return;
                        }

                        // Update the line manager with the new content
                        fileManager.replaceFile(meta.path, result.content as any);

                        // Process placeholders and stream the updated file
                        let content = result.content;
                        content = await processImagePlaceholders(content as any);
                        content = await processVideoPlaceholders(content);

                        // Check if user message contains error fixing criteria
                        const {isErrorFixing, errorUuid} = isErrorFixingMessage(userMessage.content || '');

                        // Only track credit usage if not fixing errors
                        if (!isErrorFixing) {
                            creditUsageTracker.trackOperation('file_change');
                        } else {
                            creditUsageTracker.trackDiscountedOperation('file_change', 'error_fixing', 1);
                        }

                        dataStream.writeData({
                            type: 'file-operation',
                            content: {
                                type: 'edit',
                                absolutePath: meta.path,
                                content
                            }
                        });
                    },
                    onDiffError: (error, path) => {
                        console.error('Diff error:', error, path ? `in ${path}` : '');

                        dataStream.writeData({
                            type: 'diff-error',
                            content: {
                                path: path || 'unknown',
                                error
                            }
                        });
                    }
                });

                // Start parallel non-blocking operations
                const parallelOperations = async () => {
                    try {
                        // Get auth result
                        const { userId, isAnonymous } = await authPromise;

                        if (!userId) {
                            throw new Error('Unauthorized - No valid user ID');
                        }

                        // Check message limits
                        const messageCheck = await checkMessageLimit(userId, isAnonymous);
                        if (!messageCheck.canSendMessage) {
                            throw new Error(JSON.stringify({
                                error: 'Message limit reached',
                                limit: messageCheck.limit,
                                remaining: messageCheck.remaining,
                                isPro: messageCheck.isPro,
                                isAnonymous: isAnonymous
                            }));
                        }

                        // Get or create chat and project
                        const [chat, project] = await Promise.all([
                            mockDbOperations.getChatById({ id }),
                            projectId ? mockDbOperations.getProjectById({ id: projectId }) : null
                        ]);

                        // Save user message if not a reload
                        if (!isReload) {
                            await mockDbOperations.saveMessages({
                                messages: [
                                    messageHandler.createMessageForSaving(
                                        messageHandler.getCurrentUserMessage() || userMessage,
                                        userMessageId,
                                        id,
                                        userId || '',
                                        false
                                    )
                                ],
                            });
                        }

                        // Save initial file state if needed
                        if (!chat) {
                            await mockDbOperations.saveFileStateAndCacheIfNeeded({
                                chatId: id,
                                messageId: userMessageId,
                                files,
                                dependencies: dependencies || DEFAULT_DEPENDENCIES,
                                shouldCache: true
                            });
                        }

                        return { userId, isAnonymous, chat, project };
                    } catch (error) {
                        console.error('Parallel operations error:', error);
                        return { error };
                    }
                };

                // Start parallel operations but don't wait for them
                const operationsPromise = parallelOperations();

                // Handle prompt enhancement for first messages
                let enhancedRequirements = '';
                let initialPromptGuidelines = '';

                if (isFirstMessage) {
                    initialPromptGuidelines = `<INITIAL_PROMPT_GUIDELINES>
Please focus on not adding any errors. Focus on just the primary flows but make the screens/components stunning visually. Take inspiration! Otherwise, we will lose the user. Skip functionality to prioritize visual design if you need. If an errors comes up, we will lose the user forever.
Always start writing code from new files and edit App.tsx right at the end to ensure the app does not break while you make changes.
Focus on stunning visual design and adhering to the ux guidelines.
Do not use an object as Typescript type definition from any file in the app. Use typescript to define the types of the data.

<prototype_mindset>
  The first response to a user should create a high-fidelity visual prototype rather than attempting to build a complete application. This means:

  1. Prioritize visual excellence and interaction design over complete functionality
  2. Focus on creating 1-2 core flows with stunning visuals rather than many incomplete features
  3. Design for the "happy path" first - error states and edge cases will be addressed in subsequent iterations
  4. Create a visual foundation that subsequent queries can build upon
  5. Use placeholder/mock data liberally to showcase the design's potential
  6. Use animations as the backbone of the micro interactions
  7. Ensure that the bottom tab/header is animated and can scroll
  8. Make sure to connect all the screens together without fail
  9. Do not create more than 10 files and plan your primary interactions carefully

  Communicate clearly to the user that this initial output is a visual prototype that establishes the design direction. Subsequent queries will transform this foundation into production-ready code with full functionality, error handling, and optimizations.

  Remember: Users make judgments about quality in the first 50 milliseconds of viewing a design. The initial visual impression is critical for user retention and engagement.
</prototype_mindset>
</INITIAL_PROMPT_GUIDELINES>`;

                    // For first messages, we need to enhance the prompt before sending to AI
                    // This is a critical operation but we'll make it non-blocking by starting the stream
                    // and then updating with the enhanced prompt

                    // First, send a "thinking" message to indicate we're processing
                    dataStream.writeData({
                        type: 'thinking',
                        content: 'Analyzing your request and preparing response...',
                    });

                    // Now enhance the prompt (this would normally block, but we've already started streaming)
                    enhancedRequirements = await enhancerPrompt(userMessage, initialPromptGuidelines, dataStream);

                    // Update the user message with enhanced requirements
                    messageHandler.enhanceUserMessage(
                        `\n<INTERNAL_UX_DESIGNER_GUIDELINES>${enhancedRequirements}</INTERNAL_UX_DESIGNER_GUIDELINES>\n${initialPromptGuidelines}`
                    );
                }

                // If agent mode is enabled and not first message, add agent instructions
                if (agentModeEnabled && !isFirstMessage) {
                    messageHandler.enhanceUserMessage(`
<extended_system_prompt>
You now have access to a powerful context engine tool called 'queryCodebase'. Use this tool FIRST before any other tools to understand the codebase structure and relationships.

Context Engine Usage:
1. Call queryCodebase with a specific question about what you need to understand
2. The tool will return relevant files and their relationships in a single call
3. Use this information to plan your changes without requesting individual files

After using the context engine, make edits using MO_FILE or MO_DIFF or write SQL queries using MO_DATABASE_QUERY.
Please don't get stuck in a tool calling loop. The context engine eliminates the need for multiple getFileContents calls.
Please make sure to make holistic and coherent changes all at once. If fixing bugs, use the context engine to find all relevant files and fix them all at once.

[Critical, nearly, life-threatening]: Before calling any tool, you MUST output your plan in <thinking></thinking> and after fetching, again plan and print a smaller version of <thinking></thinking>.
</extended_system_prompt>
`);
                }

                // Add logs if available
                if (logs && logs.length) {
                    const logMessage = `Here are the logs from the execution environment (expo app console) sorted by oldest to the latest along with the type:
                    ${logs.map(log => {
                        return `Timestamp: ${new Date(log.timestamp).toISOString()} | Source: ${log.source || 'unknown'} - [${log.type || 'info'}]: ${log.message || ''}`;
                    }).join('\n')}`;

                    messageHandler.enhanceUserMessage(logMessage);
                }

                // Add Supabase status (mock for now)
                messageHandler.enhanceUserMessage(
                    `<SUPABASE_STATUS>NOT CONNECTED. Please suggest the action <action type="tool" tool="supabase_integration">Connect Supabase</action> for the user to connect supabase and explicitly mention it in your response.</SUPABASE_STATUS>`
                );

                // Create file message for context
                messageHandler.createFileMessage(files || [], fileManager, false, agentModeEnabled);

                // Global counter to track file requests across the conversation
                let fileRequestCounter = 0;
                let requestedFilesSet = new Set<string>();
                let consecutiveRequestsWithoutAction = 0;

// Reset the circuit breaker counters when action is taken
                const resetCircuitBreaker = () => {
                    consecutiveRequestsWithoutAction = 0;
                    // We don't reset the total counter or the set, as those track the entire conversation
                };

                // Start the AI stream
                const startTime = Date.now();
                const result = streamText({
                    model: customModel(model.apiIdentifier),
                    maxSteps: 10,
                    experimental_activeTools: agentModeEnabled && !isFirstMessage ? ['getFileContents'] : [],
                    experimental_toolCallStreaming: false,
                    toolCallStreaming: false,
                    temperature: 0.3,
                    messages: [
                        ...messageHandler.getMessagesForRequest({agentModeEnabled}).filter(m => !!m),
                    ],
                    experimental_transform: smoothStream({ chunking: 'line' }),
                    maxRetries: 3,
                    abortSignal: request.signal,
                    experimental_continueSteps: true,
                    onError: (error) => {
                        console.log('Error', error);
                        throw error;
                    },
                    onChunk: ({ chunk }) => {
                        if (chunk.type === "text-delta") {
                            try {
                                // First run the file parser
                                const afterFileParser = parser.parse(userMessageId, chunk.textDelta);

                                // Then run the diff parser on the output of the file parser
                                diffParser.parse(userMessageId, afterFileParser);
                            } catch (e) {
                                console.error('Error processing chunk:', e);
                            }
                        }
                    },
                    tools: {
                        getFileContents: getFileContents({
                            files: files || [],
                            fileManager,
                            dataStream,
                            creditUsageTracker,
                            initialPromptGuidelines: initialPromptGuidelines || '',
                            isFirstMessage: isFirstMessage || false,
                        })
                    },
                    onFinish: async ({ response, usage, finishReason }) => {
                        console.log(`[Message:${userMessageId}]: Got response`, response.id, finishReason);

                        // By this point, parallel operations should be complete
                        const operationsResult = await operationsPromise;

                        if (operationsResult.error) {
                            console.error('Background operations failed:', operationsResult.error);
                            // Continue anyway since we've already streamed the response
                        }

                        // In a real implementation, we would save the response messages,
                        // file state, and token consumption here

                        // For now, just log completion
                        console.log(`[Message:${userMessageId}]: Completed streaming response`);

                        // Stop performance tracking
                        performanceTracker.stopTimer(requestId, {
                            status: 'success',
                            messageId: userMessageId,
                            chatId: id
                        });
                    },
                });

                // Merge the AI result into our data stream
                result.mergeIntoDataStream(dataStream, { sendUsage: false, sendReasoning: true });

            } catch (error: unknown) {
                console.error('Error in chat API:', error);
                performanceTracker.stopTimer(requestId, {
                    status: 'error',
                    error: error instanceof Error ? error.message : String(error)
                });
                throw error;
            }
        },
    });
}
