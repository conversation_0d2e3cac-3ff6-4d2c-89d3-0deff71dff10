import {auth} from "@/app/(auth)/auth";
import {
    deleteMessagesByChatIdAfterTimestamp,
    deleteTrailingMessages,
    getChatById,
    getMessageById
} from "@/lib/db/queries";
import {NextResponse} from "next/server";


export async function DELETE(request: Request,
                             {params}: { params: Promise<{ messageId: string, chatId: string }> }
) {
    // Extract the isGroupedMessage query parameter
    const url = new URL(request.url);
    const isGroupedMessage = url.searchParams.get('isGroupedMessage') === 'true';
    const {messageId, chatId} = await params;

    if (!messageId) {
        return new Response('Not Found', {status: 404});
    }

    const session = await auth();

    if (!session || !session.user || !session.user.id) {
        return new Response('Unauthorized', {status: 401});
    }


    try {
        const chat = await getChatById({id: chatId});

        if (chat.userId !== session.user.id && session.user.email !== "<EMAIL>") {
            return new Response('Unauthorized', {status: 401});
        }

        const latestMessageId = await deleteTrailingMessages({id: messageId, isGroupedMessage});

        return NextResponse.json({success: true, latestMessageId})
    } catch (error) {
        console.log('error', error)
        return new Response('An error occurred while processing your request', {
            status: 500,
        });
    }
}
