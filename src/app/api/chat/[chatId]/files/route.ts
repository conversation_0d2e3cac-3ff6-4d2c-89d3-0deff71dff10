import { auth } from '@/app/(auth)/auth';
import {getChatById, getLatestFileState, removeLatestFileState} from '@/lib/db/queries';
import { DEFAULT_DEPENDENCIES } from '@/types/editor';
import {NextResponse} from "next/server";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ chatId: string }> }
) {
  const {chatId} = await params;
  try {
    const chat = await getChatById({ id: chatId });

    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    const anonymousId = request.headers.get('x-anonymous-id');

      // For public chats, allow access without auth
    if (chat.visibility === 'public') {
      // Continue to file state fetch
    } else {
      // For private chats, require auth and verify ownership
      const session = await auth();
      const userId= session?.user.id || anonymousId;

        if (!userId) {
        return new Response('Unauthorized', { status: 401 });
      }
      if (chat.userId !== session?.user?.id && session?.user?.email !== "<EMAIL>") {
            return new Response('Unauthorized', {status: 401});
      }
    }

    const fileState = await getLatestFileState(chatId);
    if (!fileState) {
      // Return default code structure if no state exists
      return new Response(
          JSON.stringify({
            files: [],
            dependencies: DEFAULT_DEPENDENCIES,
          }),
          {
            headers: { 'Content-Type': 'application/json' },
          }
      );
    }

    return new Response(
        JSON.stringify({
          files: fileState.files,
          dependencies: fileState.dependencies || DEFAULT_DEPENDENCIES,
          version: fileState.version
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        }
    );
  } catch (error) {
    console.error('Error fetching files:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

export async function DELETE(request: Request,
                             {params}: { params: Promise<{ messageId: string, chatId: string }> }
) {
    const {chatId} = await params;


    const session = await auth();

    if (!session || !session.user || !session.user.id) {
        return new Response('Unauthorized', {status: 401});
    }

    try {
        const chat = await getChatById({id: chatId});

        if (chat.userId !== session.user.id) {
            return new Response('Unauthorized', {status: 401});
        }

        await removeLatestFileState(chatId);

        return NextResponse.json({ success: true })
    } catch (error) {
        console.log('error', error)
        return new Response('An error occurred while processing your request', {
            status: 500,
        });
    }
}
