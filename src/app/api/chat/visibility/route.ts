import { updateChatVisiblityById } from '@/lib/db/queries';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { chatId, visibility } = await request.json();

    await updateChatVisiblityById({
      chatId,
      visibility,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating chat visibility:', error);
    return NextResponse.json(
      { error: 'Failed to update chat visibility' },
      { status: 500 }
    );
  }
}
