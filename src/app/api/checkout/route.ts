import { NextResponse } from 'next/server';
import { createCheckout, upgradeSubscription, VARIANT_IDS, STORE_ID, PlanVariant, PRODUCT_IDS } from '@/lib/lemonsqueezy';
import { auth } from "@/app/(auth)/auth";
import { PLANS, PlanTier } from '@/lib/subscription/plans';
import { db } from '@/lib/db';
import { subscriptions } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

export async function POST(request: Request) {
  const body = await request.json();
  const { returnUrl = '/', planId = 'starter' } = body;
  const variantId = VARIANT_IDS[planId as PlanVariant];
  const productId = PRODUCT_IDS[planId as PlanVariant];

  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!variantId || !productId) {
      return NextResponse.json(
        { error: 'Variant ID not configured for plan: ' + planId },
        { status: 500 }
      );
    }

    const plan = PLANS.find(plan => plan.id === planId);
    if (!plan) {
      throw new Error('Unable to find plan');
    }

    // Check if user already has an active subscription
    const userSubscription = await db
      .select()
      .from(subscriptions)
      .where(eq(subscriptions.userId, session.user.id))
      .orderBy(desc => desc.updatedAt)
      .limit(1)
      .then(results => results[0]);
    
    // Auto-detect if this is an upgrade
    const isUpgradeEligible = userSubscription?.subscriptionId && 
                            userSubscription.status === 'active' &&
                            userSubscription.planId !== planId;
    
    // If eligible for upgrade, check if new plan is higher tier
    if (isUpgradeEligible) {
      // Get current plan tier
      const currentPlanTier = userSubscription.planId as PlanTier;
      const currentPlanIndex = PLANS.findIndex(p => p.tier === currentPlanTier);
      const newPlanIndex = PLANS.findIndex(p => p.tier === planId);
      
      // Only process as upgrade if new plan is higher tier
      const isUpgrade = newPlanIndex > currentPlanIndex;
      
      if (isUpgrade) {
        try {
          // Update the subscription via LemonSqueezy API
          const result = await upgradeSubscription({
            subscriptionId: userSubscription.subscriptionId as string,
            variantId: variantId,
            productId: productId,
            invoiceImmediately: true, // Charge immediately
            disableProrations: true // Allow prorations for accurate billing
          });
          
          // Extract subscription data from the response
          const subscriptionData = result.data;
          const attributes = subscriptionData.attributes;
          
          // Calculate next reset date (1 month from now)
          const resetDate = new Date(attributes.renews_at || new Date());
          
          // Update the subscription in our database
          await db
            .update(subscriptions)
            .set({
              status: attributes.status,
              isActive: attributes.status === 'active',
              planId: planId,
              credits: plan.operations,
              // Don't reset creditsUsed on upgrade to be fair to users
              lemonSqueezyVariantId: variantId,
              lemonSqueezyOrderId: attributes.order_id?.toString(),
              updatedAt: new Date()
            })
            .where(eq(subscriptions.subscriptionId, userSubscription.subscriptionId as string));
          
          // Return success with no URL since there's no redirect needed
          return NextResponse.json({ 
            success: true, 
            message: `Successfully upgraded to ${plan.name} plan`,
            upgraded: true
          });
        } catch (error: any) {
          console.error('Subscription upgrade error:', error);
          return NextResponse.json({ 
            error: 'Failed to upgrade subscription. Please contact support.',
            message: error?.message || 'Unknown error'
          }, { status: 500 });
        }
      }
    }
    
    // For new subscriptions or if upgrade failed, create a checkout
    if (!STORE_ID) {
      return NextResponse.json(
        { error: 'Store ID not configured' },
        { status: 500 }
      );
    }

    const checkout = await createCheckout({
      store_id: parseInt(STORE_ID),
      variant_id: parseInt(variantId),
      preview: true,
      checkout_data: {
        email: session.user.email,
        name: session.user.name || 'magically User',
        custom_data: [session.user.id],
      },
      product_options: {
        name: `magically ${plan.name} Plan`,
        description: `Get ${plan.operations} credits/month`,
        // Lemon Squeezy will append checkout_id to our success URL
        redirect_url: `${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?return_url=${encodeURIComponent(returnUrl)}`,
      },
    });

    return NextResponse.json({ url: checkout.data.attributes.url });
  } catch (error: any) {
    console.error('Checkout error:', error);
    return NextResponse.json({ error: 'Checkout error', message: error?.message }, { status: 500 });
  }
}
