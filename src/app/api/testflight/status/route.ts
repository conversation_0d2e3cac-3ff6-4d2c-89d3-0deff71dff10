import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import axios from 'axios';
import { auth } from '@/app/(auth)/auth';
import { kv } from '@vercel/kv';

// Schema for status request validation
const statusRequestSchema = z.object({
  buildId: z.string(),
  expoUsername: z.string().email(),
  expoPassword: z.string(),
});

// EAS API base URL
const EAS_API_URL = 'https://expo.dev/api/v2';

// Apple App Store Connect API base URL
const APPLE_API_URL = 'https://api.appstoreconnect.apple.com/v1';

// Function to authenticate with EAS API
async function authenticateWithEAS(username: string, password: string): Promise<string> {
  try {
    const response = await axios.post(`${EAS_API_URL}/auth/login`, {
      username,
      password
    });
    
    return response.data.token;
  } catch (error) {
    console.error('Error authenticating with EAS:', error);
    throw new Error('Failed to authenticate with EAS');
  }
}

// Function to check EAS build status
async function checkEASBuildStatus(easToken: string, buildId: string): Promise<{
  status: 'configuring' | 'preparing' | 'building' | 'submitting' | 'completed' | 'failed';
  progress: number;
  logs: Array<{ timestamp: number; message: string; type: 'info' | 'error' | 'warning' | 'success' }>;
  testFlightLink?: string;
  error?: string;
}> {
  try {
    const response = await axios.get(`${EAS_API_URL}/builds/${buildId}`, {
      headers: {
        'Authorization': `Bearer ${easToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const buildData = response.data;
    
    // Map EAS build status to our status format
    let status: 'configuring' | 'preparing' | 'building' | 'submitting' | 'completed' | 'failed';
    let progress = 0;
    
    switch (buildData.status) {
      case 'in-queue':
      case 'in-progress':
        if (buildData.stage === 'setting-up') {
          status = 'configuring';
          progress = 10;
        } else if (buildData.stage === 'preparing') {
          status = 'preparing';
          progress = 30;
        } else if (buildData.stage === 'building') {
          status = 'building';
          progress = 60;
        } else if (buildData.stage === 'uploading') {
          status = 'submitting';
          progress = 90;
        } else {
          status = 'building';
          progress = 50;
        }
        break;
      case 'finished':
        status = 'completed';
        progress = 100;
        break;
      case 'errored':
      case 'canceled':
        status = 'failed';
        progress = 0;
        break;
      default:
        status = 'configuring';
        progress = 5;
    }
    
    // Extract logs from the build data
    const logs = buildData.logs.map((log: any) => ({
      timestamp: new Date(log.createdAt).getTime(),
      message: log.message,
      type: log.level === 'error' ? 'error' : 
            log.level === 'warn' ? 'warning' : 
            log.level === 'success' ? 'success' : 'info'
    }));
    
    // Get TestFlight link if available
    let testFlightLink;
    if (status === 'completed' && buildData.artifacts && buildData.artifacts.buildUrl) {
      testFlightLink = buildData.artifacts.buildUrl;
    }
    
    // Get error message if failed
    let error;
    if (status === 'failed' && buildData.error) {
      error = buildData.error.message || 'Build failed';
    }
    
    return {
      status,
      progress,
      logs,
      testFlightLink,
      error
    };
  } catch (error) {
    console.error('Error checking EAS build status:', error);
    throw new Error('Failed to check build status with EAS');
  }
}

// Function to store build status in KV store
async function storeBuildStatus(buildId: string, status: any): Promise<void> {
  try {
    await kv.set(`build:${buildId}`, JSON.stringify(status));
  } catch (error) {
    console.error('Error storing build status:', error);
    // Continue even if storage fails
  }
}

// Function to retrieve build status from KV store
async function getBuildStatus(buildId: string): Promise<any | null> {
  try {
    const status = await kv.get(`build:${buildId}`);
    return status ? JSON.parse(status as string) : null;
  } catch (error) {
    console.error('Error retrieving build status:', error);
    return null;
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await req.json();
    
    // Validate request body
    const validatedData = statusRequestSchema.parse(body);
    const { buildId, expoUsername, expoPassword } = validatedData;
    
    // Try to get cached status first
    const cachedStatus = await getBuildStatus(buildId);
    
    // If the build is completed or failed, return the cached status
    if (cachedStatus && (cachedStatus.status === 'completed' || cachedStatus.status === 'failed')) {
      return NextResponse.json({
        success: true,
        ...cachedStatus
      });
    }
    
    // Otherwise, check the current status from EAS
    try {
      // Authenticate with EAS
      const easToken = await authenticateWithEAS(expoUsername, expoPassword);
      
      // Check build status
      const buildStatus = await checkEASBuildStatus(easToken, buildId);
      
      // Store the status in KV store for future requests
      await storeBuildStatus(buildId, buildStatus);
      
      return NextResponse.json({
        success: true,
        ...buildStatus
      });
    } catch (error) {
      // If we have a cached status, return it even if the API call fails
      if (cachedStatus) {
        return NextResponse.json({
          success: true,
          ...cachedStatus,
          // Add a warning that this is cached data
          warning: 'Using cached build status due to API error'
        });
      }
      
      throw error; // Re-throw to be caught by the outer catch block
    }
  } catch (error) {
    console.error('Error checking build status:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, message: 'Invalid request data', errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to check build status' 
      },
      { status: 500 }
    );
  }
}
