import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import axios from 'axios';
import { auth } from '@/app/(auth)/auth';
import jwt from 'jsonwebtoken';

// Schema for invite request validation
const inviteRequestSchema = z.object({
  buildId: z.string(),
  email: z.string().email(),
  name: z.string().optional(),
  appleUsername: z.string().email(),
  applePassword: z.string(),
  appleTeamId: z.string().optional(),
});

// Apple App Store Connect API base URL
const APPLE_API_URL = 'https://api.appstoreconnect.apple.com/v1';

// Function to generate JWT token for Apple API authentication
async function generateAppleJWT(appleTeamId: string): Promise<string> {
  // In a real implementation, you would use your Apple API key to generate a JWT
  // This would require storing the private key securely
  
  // For this implementation, we'll use a simplified approach
  // In production, you would use the actual private key and proper JWT signing
  
  const payload = {
    iss: appleTeamId,
    exp: Math.floor(Date.now() / 1000) + (20 * 60), // Token expires in 20 minutes
    aud: 'appstoreconnect-v1'
  };
  
  // In a real implementation, you would use your actual private key
  // This is a placeholder - in production, use a secure environment variable
  const privateKey = process.env.APPLE_API_PRIVATE_KEY || 'placeholder-key';
  
  return jwt.sign(payload, privateKey, { algorithm: 'ES256' });
}

// Function to get app information from App Store Connect
async function getAppInfo(token: string, bundleId: string): Promise<any> {
  try {
    // First, get the app ID from the bundle ID
    const appsResponse = await axios.get(`${APPLE_API_URL}/apps`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      params: {
        'filter[bundleId]': bundleId
      }
    });
    
    if (!appsResponse.data.data || appsResponse.data.data.length === 0) {
      throw new Error(`No app found with bundle ID ${bundleId}`);
    }
    
    return appsResponse.data.data[0];
  } catch (error) {
    console.error('Error getting app information:', error);
    throw new Error('Failed to get app information from App Store Connect');
  }
}

// Function to get build information from App Store Connect
async function getBuildInfo(token: string, appId: string, buildId: string): Promise<any> {
  try {
    const buildsResponse = await axios.get(`${APPLE_API_URL}/builds/${buildId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!buildsResponse.data.data) {
      throw new Error(`No build found with ID ${buildId}`);
    }
    
    return buildsResponse.data.data;
  } catch (error) {
    console.error('Error getting build information:', error);
    throw new Error('Failed to get build information from App Store Connect');
  }
}

// Function to send TestFlight invite
async function sendTestFlightInvite(
  token: string, 
  appId: string, 
  email: string, 
  firstName?: string, 
  lastName?: string
): Promise<any> {
  try {
    // Create a beta tester if they don't exist
    const betaTesterResponse = await axios.post(
      `${APPLE_API_URL}/betaTesters`, 
      {
        data: {
          type: 'betaTesters',
          attributes: {
            email,
            firstName: firstName || undefined,
            lastName: lastName || undefined
          },
          relationships: {
            apps: {
              data: [
                {
                  id: appId,
                  type: 'apps'
                }
              ]
            }
          }
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const betaTesterId = betaTesterResponse.data.data.id;
    
    // Send the invite
    const inviteResponse = await axios.post(
      `${APPLE_API_URL}/betaTesterInvitations`, 
      {
        data: {
          type: 'betaTesterInvitations',
          relationships: {
            app: {
              data: {
                id: appId,
                type: 'apps'
              }
            },
            betaTester: {
              data: {
                id: betaTesterId,
                type: 'betaTesters'
              }
            }
          }
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return {
      inviteId: inviteResponse.data.data.id,
      betaTesterId
    };
  } catch (error: any) {
    console.error('Error sending TestFlight invite:', error);
    
    // Check if the error is because the tester already exists
    if (error.response && error.response.status === 409) {
      // Try to get the existing beta tester
      try {
        const betaTestersResponse = await axios.get(
          `${APPLE_API_URL}/betaTesters`, 
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            params: {
              'filter[email]': email
            }
          }
        );
        
        if (betaTestersResponse.data.data && betaTestersResponse.data.data.length > 0) {
          const betaTesterId = betaTestersResponse.data.data[0].id;
          
          // Add the tester to this app if not already added
          await axios.post(
            `${APPLE_API_URL}/betaTesters/${betaTesterId}/relationships/apps`,
            {
              data: [
                {
                  id: appId,
                  type: 'apps'
                }
              ]
            },
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );
          
          return {
            inviteId: `existing-${Date.now()}`,
            betaTesterId,
            message: 'Tester already exists, added to this app'
          };
        }
      } catch (secondError) {
        console.error('Error handling existing beta tester:', secondError);
      }
    }
    
    throw new Error('Failed to send TestFlight invite');
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await req.json();
    
    // Validate request body
    const validatedData = inviteRequestSchema.parse(body);
    const { 
      buildId, 
      email, 
      name, 
      appleUsername, 
      applePassword, 
      appleTeamId 
    } = validatedData;
    
    // Split name into first and last name if provided
    let firstName, lastName;
    if (name) {
      const nameParts = name.split(' ');
      firstName = nameParts[0];
      lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : undefined;
    }
    
    try {
      // Generate JWT token for Apple API
      const token = await generateAppleJWT(appleTeamId || '');
      
      // Get app information from the build
      // In a real implementation, you would store the bundle ID with the build
      // For this example, we'll assume we can get it from the App Store Connect API
      const appInfo = await getAppInfo(token, 'com.example.app'); // Replace with actual bundle ID
      const appId = appInfo.id;
      
      // Get build information
      const buildInfo = await getBuildInfo(token, appId, buildId);
      
      // Send the invite
      const inviteResult = await sendTestFlightInvite(token, appId, email, firstName, lastName);
      
      return NextResponse.json({
        success: true,
        message: `Invite sent to ${email}`,
        inviteId: inviteResult.inviteId,
        email,
        name,
        buildId,
        appId
      });
    } catch (apiError) {
      console.error('API error:', apiError);
      
      // If the API call fails, return a more specific error
      return NextResponse.json(
        { 
          success: false, 
          message: apiError instanceof Error ? apiError.message : 'Failed to send invite through Apple API',
          email,
          buildId
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending invite:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, message: 'Invalid request data', errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, message: 'Failed to send invite' },
      { status: 500 }
    );
  }
}
