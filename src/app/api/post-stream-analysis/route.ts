import { NextRequest, NextResponse } from 'next/server';
import { Message } from 'ai';
import { PostStreamAnalysisService, CodeChangeAnalysis } from '@/lib/services/post-stream-analysis-service';
import { FileItem } from '@/types/file';
import { db } from '@/lib/db';
import { message as messageTable } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import * as Sentry from "@sentry/nextjs";

export interface PostStreamAnalysisRequest {
  chatId: string;
  messageId: string;
  files: FileItem[];
  messages: Message[];
}

export interface PostStreamAnalysisResponse {
  analysis: CodeChangeAnalysis;
  userFeedbackMessage: string;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const { chatId, messageId, files, messages }: PostStreamAnalysisRequest = await req.json();

    if (!chatId || !messageId || !files || !messages || messages.length === 0) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Get the latest assistant message
    const latestMessage = messages.find(m => m.id === messageId);
    if (!latestMessage || latestMessage.role !== 'assistant') {
      return NextResponse.json(
        { error: 'Invalid message ID or not an assistant message' },
        { status: 400 }
      );
    }

    // Initialize the analysis service with the current files
    const analysisService = new PostStreamAnalysisService(files);
    
    // Analyze the changes
    const analysis = await analysisService.analyzeChanges(latestMessage, messages);
    
    // Create a user-friendly message from the analysis
    const userFeedbackMessage = createUserFeedbackMessage(analysis);
    
    // Store the analysis with the message
    try {
      // Convert the analysis to a JSON string
      const analysisJson = JSON.stringify(analysis);
      
      // The message schema doesn't have a dedicated metadata field
      // Instead, we'll store the analysis in the content field as additional JSON data
      // First, ensure the content is a valid JSON object
      let contentObj: any;
      
      if (typeof latestMessage.content === 'string') {
        // If it's a string, keep it as is
        contentObj = latestMessage.content;
      } else {
        // If it's already an object, use it
        contentObj = latestMessage.content;
      }
      
      // Update the message with the analysis data
      await db.update(messageTable)
        .set({ 
          // Store the analysis in a custom field that won't interfere with the message content
          componentContexts: JSON.stringify({
            codeAnalysis: analysisJson
          })
        })
        .where(eq(messageTable.id, messageId));
    } catch (dbError) {
      console.error('Failed to update message with analysis:', dbError);
      // Continue even if DB update fails
    }
    
    return NextResponse.json({
      analysis,
      userFeedbackMessage
    });
  } catch (error) {
    console.error('Error in post-stream analysis:', error);
    Sentry.captureException(error);
    
    return NextResponse.json(
      { error: 'Failed to analyze stream' },
      { status: 500 }
    );
  }
}

/**
 * Create a user-friendly message from the analysis
 */
function createUserFeedbackMessage(analysis: CodeChangeAnalysis): string {
  const { userFriendlyDescription, recommendedAction, potentialIssues } = analysis;
  
  let message = `## Changes Summary\n${userFriendlyDescription}\n\n`;
  
  if (potentialIssues.length > 0) {
    const highSeverityIssues = potentialIssues.filter(issue => issue.severity === 'high');
    const otherIssues = potentialIssues.filter(issue => issue.severity !== 'high');
    
    if (highSeverityIssues.length > 0) {
      message += `## ⚠️ Important Issues\n`;
      highSeverityIssues.forEach(issue => {
        message += `- ${issue.description}\n`;
      });
      message += '\n';
    }
    
    if (otherIssues.length > 0) {
      message += `## Other Considerations\n`;
      otherIssues.forEach(issue => {
        message += `- ${issue.description}\n`;
      });
      message += '\n';
    }
  }
  
  message += `## Recommendation\n`;
  switch (recommendedAction) {
    case 'continue':
      message += `✅ These changes look good! You can continue building your app.`;
      break;
    case 'fix':
      message += `🔧 These changes need some adjustments, but the overall direction is good.`;
      break;
    case 'redo':
      message += `🚫 These changes have significant issues. Consider trying a different approach.`;
      break;
  }
  
  return message;
}
