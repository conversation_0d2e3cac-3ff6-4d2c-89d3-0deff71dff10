import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { sendWelcomeEmail } from '@/lib/email';

// This is a test endpoint that should be removed in production
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and is an admin
    // const session = await auth();
    // if (!session?.user) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }
    
    // Get data from request body
    const { email, name } = await request.json();
    
    if (!email || !name) {
      return NextResponse.json({ error: 'Email and name are required' }, { status: 400 });
    }
    
    // Send test welcome email
    const result = await sendWelcomeEmail({
      email,
      name,
    });
    
    return NextResponse.json({ 
      success: true, 
      message: 'Test welcome email sent successfully',
      result
    });
  } catch (error) {
    console.error('Error sending test welcome email:', error);
    return NextResponse.json({ 
      error: 'Failed to send test welcome email',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
