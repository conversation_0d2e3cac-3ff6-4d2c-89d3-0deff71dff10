import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const routePath: string[] = (await params).path // 'a', 'b', or 'c'
  // path will be an array of segments
  const path = routePath.join("/");
  console.log('Proxying request to:', `https://exp.host/${path}`);

  try {
    const response = await fetch(`https://exp.host/${path}`, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Expo API error:', {
        status: response.status,
        statusText: response.statusText,
        path
      });
      return NextResponse.json(
        { error: `Expo API returned ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error proxying request to Expo:', error);
    return NextResponse.json(
      { error: 'Failed to fetch from Expo' },
      { status: 500 }
    );
  }
}
