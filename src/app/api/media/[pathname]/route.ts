import {NextRequest, NextResponse} from 'next/server';
import {Redis} from '@upstash/redis';
import {llmMediaService} from '@/lib/services/llm-media-service';
import axios from 'axios';

// Initialize Redis client
const redis = new Redis({
    url: process.env.UPSTASH_REDIS_REST_URL!,
    token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// Cache TTL in seconds (7 days)
const CACHE_TTL = 7 * 24 * 60 * 60;

// Helper to get cache key for URLs
function getCacheKey(type: 'image' | 'video', query: string): string {
    return `media:${type}:url:${query}`;
}

export async function GET(request: NextRequest,
                          {params}: {
                              params: Promise<{ pathname: string }>
                          }) {
    const {pathname} = await params;
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query');

    if (!query) {
        return NextResponse.json({error: 'Query parameter is required'}, {status: 400});
    }

    // Handle /api/media/image endpoint
    if (pathname === 'image') {
        try {
            const cacheKey = getCacheKey('image', query);
            let imageUrl;

            // We only cache URLs, not content - much more cost-effective

            // If content not in cache, try to get URL from cache
            const cachedUrl = await redis.get<string>(cacheKey);
            if (cachedUrl) {
                console.log(`[CACHE HIT] Image URL for "${query}": ${cachedUrl}`);
                imageUrl = cachedUrl;
            } else {
                // If URL not in cache, fetch and cache
                console.log(`[CACHE MISS] Fetching image for "${query}"`);
                imageUrl = await llmMediaService.searchImage(query);

                // Store URL in cache with TTL
                await redis.set(cacheKey, imageUrl, {ex: CACHE_TTL});
            }

            // Fetch and proxy the image content without caching it
            try {
                const response = await axios.get(imageUrl, {
                    responseType: 'arraybuffer',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                });

                // Determine content type from URL or response
                const contentType = response.headers['content-type'] || 'image/jpeg';

                // Return the image as a response with appropriate headers
                return new NextResponse(response.data, {
                    headers: {
                        'Content-Type': contentType,
                        'Cache-Control': 'public, max-age=86400',
                        'Access-Control-Allow-Origin': '*'
                    }
                });
            } catch (fetchError) {
                console.error('Error fetching image content:', fetchError);
                // If we can't fetch the content, fall back to returning the URL
                return NextResponse.json({url: imageUrl});
            }
        } catch (error) {
            console.error('Error fetching image:', error);
            return NextResponse.json({error: 'Failed to fetch image'}, {status: 500});
        }
    }

    // Handle /api/media/video endpoint
    if (pathname === 'video') {
        try {
            const cacheKey = getCacheKey('video', query);
            let videoUrl;

            // For videos, we'll only cache the URL, not the content
            // This is because video streaming is more complex and requires proper handling of range requests

            // If content not in cache, try to get URL from cache
            const cachedUrl = await redis.get<string>(cacheKey);
            if (cachedUrl) {
                console.log(`[CACHE HIT] Video URL for "${query}": ${cachedUrl}`);
                videoUrl = cachedUrl;
            } else {
                // If URL not in cache, fetch and cache
                console.log(`[CACHE MISS] Fetching video for "${query}"`);
                videoUrl = await llmMediaService.searchVideo(query);

                // Store URL in cache with TTL
                await redis.set(cacheKey, videoUrl, {ex: CACHE_TTL});
            }

            // For videos, we'll redirect to the source URL instead of proxying the content
            // This ensures proper streaming and playback compatibility
            return NextResponse.redirect(videoUrl);

            /* 
            // NOTE: The code below is commented out because direct video streaming is more complex
            // and requires proper handling of range requests and other streaming features.
            // For simplicity and better compatibility, we're using a redirect approach instead.
            try {
                // Instead of downloading the entire video, we'll stream it
                const response = await axios.get(videoUrl, {
                    responseType: 'stream',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Range': request.headers.get('range') || 'bytes=0-'
                    }
                });

                // Get content type and other headers from the response
                const contentType = response.headers['content-type'] || 'video/mp4';
                const contentLength = response.headers['content-length'];
                const contentRange = response.headers['content-range'];
                
                // Create headers for the response
                const headers = {
                    'Content-Type': contentType,
                    'Accept-Ranges': 'bytes',
                    'Cache-Control': 'public, max-age=86400',
                    'Access-Control-Allow-Origin': '*'
                };
                
                // Add content length and range headers if available
                if (contentLength) headers['Content-Length'] = contentLength;
                if (contentRange) headers['Content-Range'] = contentRange;
                
                // Convert the stream to a ReadableStream for NextResponse
                const stream = new ReadableStream({
                    start(controller) {
                        response.data.on('data', (chunk) => {
                            controller.enqueue(chunk);
                        });
                        response.data.on('end', () => {
                            controller.close();
                        });
                        response.data.on('error', (err) => {
                            controller.error(err);
                        });
                    }
                });
                
                // Return the video as a streaming response
                return new NextResponse(stream, {
                    headers: headers,
                    status: response.status
                });
            } catch (fetchError) {
                console.error('Error streaming video content:', fetchError);
                // If we can't stream the content, redirect to the source URL
                return NextResponse.redirect(videoUrl);
            }
            */
        } catch (error) {
            console.error('Error fetching video:', error);
            return NextResponse.json({error: 'Failed to fetch video'}, {status: 500});
        }
    }

    return NextResponse.json({error: 'Not found'}, {status: 404});
}

// Define CORS headers for the prefetch endpoint
const corsHeaders = {
    'Access-Control-Allow-Origin': '*', // You can restrict this to specific origins like 'https://storage.magically.life'
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400', // 24 hours cache for preflight requests
};

// Handle OPTIONS request (preflight)
export async function OPTIONS(request: NextRequest, {params}: {
    params: Promise<{ pathname: string }>
}) {
    const {pathname} = await params;
    
    // Only apply CORS for the prefetch endpoint
    if (pathname === 'prefetch') {
        return new NextResponse(null, {
            status: 204,
            headers: corsHeaders,
        });
    }
    
    return new NextResponse(null, { status: 204 });
}

export async function POST(request: NextRequest, {params}: {
    params: Promise<{ pathname: string }>
}) {
    const {pathname} = await params;

    // Handle /api/media/prefetch endpoint
    if (pathname === 'prefetch') {
        try {
            const body = await request.json();
            const {images = [], videos = []} = body;

            // Prefetch images
            if (images.length > 0) {
                console.log(`Prefetching ${images.length} images`);
                await Promise.all(images.map(async (query: string) => {
                    // We only cache URLs, not content to save Redis costs

                    // If content not cached, check if URL is cached
                    const cacheKey = getCacheKey('image', query);
                    let imageUrl;

                    const cachedUrl = await redis.get<string>(cacheKey);
                    if (cachedUrl) {
                        console.log(`[CACHE HIT] Image URL for "${query}" already cached`);
                        imageUrl = cachedUrl;
                    } else {
                        // Fetch and cache URL
                        try {
                            imageUrl = await llmMediaService.searchImage(query);
                            await redis.set(cacheKey, imageUrl, {ex: CACHE_TTL});
                            console.log(`[PREFETCH] Cached image URL for "${query}"`);
                        } catch (error) {
                            console.error(`[PREFETCH ERROR] Failed to fetch image URL for "${query}":`, error);
                            return;
                        }
                    }

                    // We don't prefetch actual content to save Redis costs
                    console.log(`[PREFETCH] Image URL for "${query}" is cached and ready`);
                }));
            }

            // Prefetch videos
            if (videos.length > 0) {
                console.log(`Prefetching ${videos.length} videos`);
                await Promise.all(videos.map(async (query: string) => {
                    // We only cache URLs, not content to save Redis costs

                    // If content not cached, check if URL is cached
                    const cacheKey = getCacheKey('video', query);
                    let videoUrl;

                    const cachedUrl = await redis.get<string>(cacheKey);
                    if (cachedUrl) {
                        console.log(`[CACHE HIT] Video URL for "${query}" already cached`);
                        videoUrl = cachedUrl;
                    } else {
                        // Fetch and cache URL
                        try {
                            videoUrl = await llmMediaService.searchVideo(query);
                            await redis.set(cacheKey, videoUrl, {ex: CACHE_TTL});
                            console.log(`[PREFETCH] Cached video URL for "${query}"`);
                        } catch (error) {
                            console.error(`[PREFETCH ERROR] Failed to fetch video URL for "${query}":`, error);
                            return;
                        }
                    }

                    // For videos, we only cache the URL, not the content
                    // This is because video streaming is more complex and requires proper handling of range requests
                    console.log(`[PREFETCH] Video URL for "${query}" is ready for streaming`);

                }));
            }

            return new NextResponse(JSON.stringify({success: true}), {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders,
                },
            });
        } catch (error) {
            console.error('Error prefetching media:', error);
            return new NextResponse(JSON.stringify({error: 'Invalid request body'}), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders,
                },
            });
        }
    }

    return NextResponse.json({error: 'Not found'}, {status: 404});
}
