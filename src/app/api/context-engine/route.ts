import { NextRequest, NextResponse } from 'next/server';
import { ContextEngine } from '@/lib/services/context-engine';
import { z } from 'zod';
import { getLatestFileStateByProject } from '@/lib/db/queries';
import { getProjectById } from '@/lib/db/project-queries';
import { FileItem } from "@/types/file";

const QuerySchema = z.object({
  query: z.string().min(1),
  projectId: z.string().min(1),
});

/**
 * API endpoint for querying the context engine
 * Allows the AI to understand the codebase structure and relationships without iterative file requests
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();

    // Validate the request
    const result = QuerySchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: result.error.format() },
        { status: 400 }
      );
    }

    const { query, projectId } = result.data;

    // Get the latest file state and project information
    const [fileState, project] = await Promise.all([
      getLatestFileStateByProject(projectId),
      getProjectById({id: projectId})
    ]);

    const { files } = fileState;
    if (!files || (files as FileItem[]).length === 0) {
      return NextResponse.json(
        { error: 'No files found for project' },
        { status: 404 }
      );
    }

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Initialize the context engine with files and project information
    const contextEngine = new ContextEngine(files as FileItem[], project);

    // Query the context engine
    const contextResult = await contextEngine.query(query);

    // Return the result
    return NextResponse.json(contextResult);
  } catch (error) {
    console.error('Context engine error:', error);
    return NextResponse.json(
      { error: 'Failed to query context engine', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
