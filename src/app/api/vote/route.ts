import { auth } from '@/app/(auth)/auth';
import { getVotesByChatId, voteMessage } from '@/lib/db/queries';
import {NextResponse} from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new Response('chatId is required', { status: 400 });
  }

  const anonymousId = request.headers.get('x-anonymous-id');

  const session = await auth();

  const userId= session?.user.id || anonymousId;

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }

  const votes = await getVotesByChatId({ id: chatId });

  return Response.json(votes, { status: 200 });
}

export async function PATCH(request: Request) {
  const {
    chatId,
    messageId,
    type,
  }: { chatId: string; messageId: string; type: 'up' | 'down' } =
    await request.json();


  if (!chatId || !messageId || !type) {
    return new Response('messageId and type are required', { status: 400 });
  }
  const anonymousId = request.headers.get('x-anonymous-id');

  const session = await auth();

  const userId= session?.user.id || anonymousId;

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  await voteMessage({
    chatId,
    messageId,
    type: type,
  });

  return new Response('Message voted', { status: 200 });
}
