import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getMessagesByChatIdPaginated, getMessagesByChatId } from '@/lib/db/queries';
import { Message } from '@/lib/db/schema';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ chatId: string }> }
) {
  const { chatId } = await params;
  const session = await auth();

  if (!session || !session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const loadAll = searchParams.get('all') === 'true';
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const cursorParam = searchParams.get('cursor');
    // Convert 'null' string to actual null
    const cursor = cursorParam === 'null' ? null : cursorParam;
    const direction = searchParams.get('direction') || 'desc';
    
    console.log('[API] Fetching messages with params:', { loadAll, limit, cursor, direction });

    try {
      // If all=true, fetch all messages without pagination
      if (loadAll) {
        console.log('[API] Loading ALL messages for chat:', chatId);
        const allMessages = await getMessagesByChatId({ id: chatId });
        
        return NextResponse.json({
          messages: allMessages,
          hasMore: false,
          nextCursor: null
        });
      } else {
        // Fetch messages with pagination
        const result = await getMessagesByChatIdPaginated({
          chatId,
          limit,
          cursor,
          direction: direction as 'asc' | 'desc'
        });

        // Return raw DB messages - they will be converted to UI format on the client
        // using convertToUIMessages
        return NextResponse.json({
          messages: result.messages,
          hasMore: result.hasMore,
          nextCursor: result.nextCursor
        });
      }
    } catch (dbError) {
      console.error('Database error fetching messages:', dbError);
      // Return empty result instead of error to prevent cascading failures
      return NextResponse.json({
        messages: [],
        hasMore: false,
        nextCursor: null,
        error: 'Database error occurred, please try again later'
      }, { status: 200 }); // Use 200 to prevent client from retrying immediately
    }
  } catch (error) {
    console.error('Error processing request:', error);
    // Return empty result with error message
    return NextResponse.json({
      messages: [],
      hasMore: false,
      nextCursor: null,
      error: 'Failed to process request'
    }, { status: 200 }); // Use 200 to prevent client from retrying immediately
  }
}
