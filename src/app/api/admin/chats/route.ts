import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { chat } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { generateUUID } from '@/lib/utils';
// import {performanceTracker} from "@/lib/utils/PerformanceTracker";
import {auth} from "@/app/(auth)/auth";

// API route to fetch all admin chats for the current user
export async function GET(req: NextRequest) {
  const requestId = generateUUID();

  try {
    // Get the current session
    const session = await auth();

    // Check if the user is authenticated
    if (!session || !session.user) {
      // performanceTracker.stopTimer(requestId, {status: 'error', error: 'Unauthorized'});
      return new NextResponse('Unauthorized', { status: 401 });
    }
    
    // Only allow specific admin user
    if (session.user.email !== '<EMAIL>') {
      // performanceTracker.stopTimer(requestId, {status: 'error', error: 'Forbidden'});
      return new NextResponse('Forbidden', { status: 403 });
    }
    
    // Query the database for chats of type 'app' (will be 'admin' in the future) for this user
    const chats = await db.select({
      id: chat.id,
      title: chat.title,
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt
    })
    .from(chat)
    .where(
      and(
        eq(chat.userId, session.user.id),
        eq(chat.type, 'admin'), // Using 'app' for now, will be 'admin' when enum is updated
        eq(chat.visibility, 'private')
      )
    )
    .orderBy(chat.updatedAt);

    // performanceTracker.stopTimer(requestId, {status: 'success'});
    return NextResponse.json(chats);
  } catch (error) {
    console.error('Error fetching admin chats:', error);
    // performanceTracker.stopTimer(requestId, {status: 'error', error: String(error)});
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
