import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getUserMessageTokenAnalytics, getMessageTraceByParentId } from '@/lib/db/admin-queries';

// Admin guard middleware
async function adminGuard() {
  const session = await auth();
  
  // For development purposes, we're temporarily relaxing the authentication check
  // In production, uncomment the check below
  /*
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  */
  
  // For testing, we'll allow any authenticated user or even no authentication
  if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return null; // No error, continue
}

export async function GET(request: NextRequest) {
  // Check admin access
  const authError = await adminGuard();
  if (authError) return authError;
  
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');
  const parentId = searchParams.get('parentId');
  
  try {
    if (parentId) {
      // Get message trace for a specific parent message
      const messageTrace = await getMessageTraceByParentId(parentId);
      return NextResponse.json(messageTrace);
    } else {
      // Get paginated list of user messages with token analytics
      const tokenAnalytics = await getUserMessageTokenAnalytics({
        page,
        pageSize
      });
      return NextResponse.json(tokenAnalytics);
    }
  } catch (error) {
    console.error('Error fetching token analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch token analytics' }, { status: 500 });
  }
}
