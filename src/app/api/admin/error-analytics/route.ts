import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getErrorAnalytics } from '@/lib/db/admin-queries';

// Admin guard middleware
async function adminGuard() {
  const session = await auth();
  
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return null; // No error, continue
}

export async function GET(request: NextRequest) {
  try {
    // Check admin access
    const authError = await adminGuard();
    if (authError) return authError;

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const timeRange = parseInt(searchParams.get('timeRange') || '30'); // Default to 30 days
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const category = searchParams.get('category') || '';
    const searchTerm = searchParams.get('searchTerm') || '';
    
    // Use the Drizzle query function to get error analytics
    const errorAnalyticsData = await getErrorAnalytics({
      page,
      pageSize,
      timeRange,
      category,
      searchTerm
    });
    
    // Return the processed data
    return NextResponse.json(errorAnalyticsData);
  } catch (error) {
    console.error('Error in error analytics API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
