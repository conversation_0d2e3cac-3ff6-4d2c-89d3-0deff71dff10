// app/api/[transport]/route.ts
import {createMcpHandler} from '@vercel/mcp-adapter';
import {z} from "zod";

const handler = createMcpHandler(
    server => {
        // server.tool(
        //     'roll_dice',
        //     'Rolls an N-sided die',
        //     {
        //         sides: z.number().int().min(2)
        //     },
        //     async ({sides}) => {
        //         const value = 1 + Math.floor(Math.random() * sides);
        //         return {
        //             content: [{type: 'text', text: `🎲 You rolled a ${value}!`}],
        //         };
        //     }
        // );
    },
    {

    },
    {
        // Optional redis config
        // You need these endpoints
        basePath: '/api/admin', // this needs to match where the [transport] is located.
        maxDuration: 60,
        verboseLogs: false,
        redisUrl: process.env.REDIS_URL
    }
);
export {handler as GET, handler as POST};