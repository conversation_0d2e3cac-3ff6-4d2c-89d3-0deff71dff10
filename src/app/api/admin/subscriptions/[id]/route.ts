import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";
import { eq, sql } from 'drizzle-orm';
import { subscriptions, user } from '@/lib/db/schema';

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

// Admin guard middleware
async function adminGuard() {
  const session = await auth();
  
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return null; // No error, continue
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // Check admin access
  const authError = await adminGuard();
  if (authError) return authError;
  
  const { id } = await params;
  
  try {
    // Fetch the subscription details directly
    const subscription = await db
      .select()
      .from(subscriptions)
      .where(eq(subscriptions.id, id));
    
    if (!subscription || subscription.length === 0) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }
    
    // Calculate remaining credits and fetch user info
    const subscriptionData = subscription[0];
    const remainingCredits = Number(subscriptionData.credits) - Number(subscriptionData.creditsUsed);
    const percentUsed = subscriptionData.credits > 0 
      ? (subscriptionData.creditsUsed / subscriptionData.credits) * 100 
      : 0;
    
    // Fetch actual user information
    const userInfo = await db
      .select({
        name: user.name,
        email: user.email
      })
      .from(user)
      .where(sql`CAST(${user.id} AS VARCHAR) = ${subscriptionData.userId}`)
      .limit(1);
    
    // Return the subscription with additional calculated fields
    return NextResponse.json({
      ...subscriptionData,
      remainingCredits,
      percentUsed: Math.round(percentUsed),
      // Add actual user info if found, otherwise use placeholder
      userName: userInfo.length > 0 ? userInfo[0].name : 'User ' + subscriptionData.userId.substring(0, 8),
      userEmail: userInfo.length > 0 ? userInfo[0].email : subscriptionData.userId,
    });
  } catch (error) {
    console.error('Error fetching subscription details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription details' },
      { status: 500 }
    );
  }
}
