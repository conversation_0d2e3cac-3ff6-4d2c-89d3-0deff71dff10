import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getSubscriptionTokenConsumption } from '@/lib/db/admin-queries';

// Admin guard middleware
async function adminGuard() {
  const session = await auth();
  
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return null; // No error, continue
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // Check admin access
  const authError = await adminGuard();
  if (authError) return authError;
  
  const { id } = await params;
  
  // Parse query parameters
  const searchParams = request.nextUrl.searchParams;
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');
  const sortFieldParam = searchParams.get('sortField') || 'createdAt';
  // Ensure sortField is a valid TokenConsumptionSortField
  const sortField = (['createdAt', 'totalTokens', 'totalCost', 'model'].includes(sortFieldParam) 
    ? sortFieldParam 
    : 'createdAt') as 'createdAt' | 'totalTokens' | 'totalCost' | 'model';
  const sortDirection = (searchParams.get('sortDirection') || 'desc') as 'asc' | 'desc';
  
  // Parse date range if provided
  let startDate: Date | undefined;
  let endDate: Date | undefined;
  
  const startDateParam = searchParams.get('startDate');
  const endDateParam = searchParams.get('endDate');
  
  if (startDateParam) {
    startDate = new Date(startDateParam);
  }
  
  if (endDateParam) {
    endDate = new Date(endDateParam);
  }
  
  try {
    // Fetch token consumption data for this subscription
    const consumptionData = await getSubscriptionTokenConsumption({
      subscriptionId: id,
      page,
      pageSize,
      sortField,
      sortDirection,
      startDate,
      endDate
    });
    
    // Ensure all caching-related fields are included in the response
    const enhancedData = consumptionData.data.map(item => ({
      ...item,
      cachingDiscount: item.cachingDiscount || 0,
      // Use the raw cacheDiscountPercent value from the database (already in percentage)
      cacheDiscountPercent: item.cacheDiscountPercent || 0,
      subtotal: item.subtotal || item.totalCost
    }));
    
    return NextResponse.json({ ...consumptionData, data: enhancedData });
  } catch (error) {
    console.error('Error fetching token consumption:', error);
    return NextResponse.json(
      { error: 'Failed to fetch token consumption data' },
      { status: 500 }
    );
  }
}
