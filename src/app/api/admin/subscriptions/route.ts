import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getAllSubscriptions } from '@/lib/db/admin-queries';

// Admin guard middleware
async function adminGuard() {
  const session = await auth();
  
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return null; // No error, continue
}

export async function GET(request: NextRequest) {
  // Check admin access
  const authError = await adminGuard();
  if (authError) return authError;
  
  // Parse query parameters
  const searchParams = request.nextUrl.searchParams;
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');
  const sortFieldParam = searchParams.get('sortField') || 'createdAt';
  // Ensure sortField is a valid SubscriptionSortField
  const sortField = (['createdAt', 'status', 'planId', 'credits', 'creditsUsed'].includes(sortFieldParam) 
    ? sortFieldParam 
    : 'createdAt') as 'createdAt' | 'status' | 'planId' | 'credits' | 'creditsUsed';
  const sortDirection = (searchParams.get('sortDirection') || 'desc') as 'asc' | 'desc';
  const searchTerm = searchParams.get('searchTerm') || '';
  
  // Parse filters if provided
  let filters: Record<string, string[]> = {};
  const filtersParam = searchParams.get('filters');
  if (filtersParam) {
    try {
      const parsedFilters = JSON.parse(decodeURIComponent(filtersParam));
      
      // Process filters to handle special values
      Object.entries(parsedFilters).forEach(([key, value]) => {
        const values = value as string[];
        // Skip 'all' filter values
        if (values.length > 0 && !values.includes('all_status') && !values.includes('all_plans')) {
          filters[key] = values;
        }
      });
    } catch (error) {
      console.error('Error parsing filters:', error);
    }
  }
  
  try {
    // Fetch subscriptions with pagination, sorting, and filtering
    const subscriptionsData = await getAllSubscriptions({
      page,
      pageSize,
      sortField,
      sortDirection,
      searchTerm,
      filters
    });
    
    return NextResponse.json(subscriptionsData);
  } catch (error) {
    console.error('Error in subscriptions API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscriptions' },
      { status: 500 }
    );
  }
}
