import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getErrorCategoryStats } from '@/lib/db/admin-queries';

// Admin guard middleware
async function adminGuard() {
  const session = await auth();
  
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return null; // No error, continue
}

export async function GET(request: NextRequest) {
  try {
    // Check admin access
    const authError = await adminGuard();
    if (authError) return authError;

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const timeRange = parseInt(searchParams.get('timeRange') || '30'); // Default to 30 days
    
    // Get error category statistics
    const categoryStats = await getErrorCategoryStats(timeRange);
    
    // Return the data
    return NextResponse.json(categoryStats);
  } catch (error) {
    console.error('Error in error categories API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
