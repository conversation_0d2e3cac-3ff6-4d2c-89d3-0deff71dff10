import { auth } from '@/app/(auth)/auth';
import { updateChat, getChatById } from '@/lib/db/queries';
import { z } from 'zod';

// Schema for request validation
const UpdateChatSchema = z.object({
  title: z.string().min(1).max(100).optional(),
  // Add other fields that can be updated here if needed
});

/**
 * GET a specific chat by ID
 * Only accessible to admin users
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Only allow specific admin email
    if (session.user.email !== '<EMAIL>') {
      return new Response('Forbidden', { status: 403 });
    }

    // Get chat ID from params
    const { id } = params;
    if (!id) {
      return new Response('Chat ID is required', { status: 400 });
    }

    // Fetch chat from database
    const chat = await getChatById({ id });
    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    return new Response(JSON.stringify({ chat }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error fetching chat:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

/**
 * PUT to update chat details (including title)
 * Only accessible to admin users
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Only allow specific admin email
    if (session.user.email !== '<EMAIL>') {
      return new Response('Forbidden', { status: 403 });
    }

    // Get chat ID from params
    const { id } = params;
    if (!id) {
      return new Response('Chat ID is required', { status: 400 });
    }

    // Check if chat exists
    const chat = await getChatById({ id });
    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const validationResult = UpdateChatSchema.safeParse(body);

    if (!validationResult.success) {
      return new Response('Invalid request data', { status: 400 });
    }

    const updateData = validationResult.data;
    
    // Update chat with provided data
    await updateChat({
      id,
      ...updateData,
      updatedAt: new Date()
    });

    // Return updated chat
    const updatedChat = await getChatById({ id });

    return new Response(JSON.stringify({ chat: updatedChat }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error updating chat:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
