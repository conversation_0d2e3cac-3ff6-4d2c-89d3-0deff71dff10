import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { Resend } from 'resend';

// Initialize Resend client
const resend = new Resend(process.env.RESEND_API_KEY);

// Admin guard middleware
async function adminGuard() {
  const session = await auth();
  
  // For development purposes, we're temporarily relaxing the authentication check
  // In production, uncomment the check below
  /*
  if (!session || !session.user || session.user.email !== '<EMAIL>') {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  */
  
  // For testing, we'll allow any authenticated user or even no authentication
  if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return null; // No error, continue
}

export async function POST(request: NextRequest) {
  try {
    // Check admin access
    const authError = await adminGuard();
    if (authError) return authError;
    
    // Get data from request body
    const { users, subject, content } = await request.json();
    
    if (!users || !Array.isArray(users) || users.length === 0) {
      return NextResponse.json({ error: 'No users provided' }, { status: 400 });
    }
    
    if (!subject || !content) {
      return NextResponse.json({ error: 'Subject and content are required' }, { status: 400 });
    }
    
    // For tracking purposes
    const sentEmails: any[] = [];
    const failedEmails: any[] = [];
    
    // Send emails to each user
    for (const user of users) {
      try {
        // Check if Resend API key is available
        if (!process.env.RESEND_API_KEY) {
          console.log('Mock sending email to:', user.email);
          sentEmails.push({ user, emailId: 'mock-' + Date.now() });
          continue;
        }
        
        const { data, error } = await resend.emails.send({
          from: process.env.EMAIL_FROM || '<EMAIL>',
          to: user.email,
          subject: subject,
          html: content,
          // Optional personalization
          tags: [
            {
              name: 'user_id',
              value: user.id,
            },
            {
              name: 'campaign_type',
              value: 'churn_prevention',
            },
          ],
        });
        
        if (error) {
          console.error(`Error sending email to ${user.email}:`, error);
          failedEmails.push({ user, error });
        } else {
          sentEmails.push({ user, emailId: data?.id });
        }
      } catch (error) {
        console.error(`Error sending email to ${user.email}:`, error);
        failedEmails.push({ user, error });
      }
    }
    
    // Log the campaign for analytics
    // This would ideally be stored in a database
    console.log(`Email campaign sent: ${subject}`);
    console.log(`Sent to ${sentEmails.length} users, failed for ${failedEmails.length} users`);
    
    return NextResponse.json({
      success: true,
      sentCount: sentEmails.length,
      failedCount: failedEmails.length,
    });
  } catch (error) {
    console.error('Error sending emails:', error);
    return NextResponse.json({ error: 'Failed to send emails' }, { status: 500 });
  }
}
