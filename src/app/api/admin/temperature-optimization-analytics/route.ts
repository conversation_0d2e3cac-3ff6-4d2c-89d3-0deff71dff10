import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db/db';
import { temperatureOptimization } from '@/lib/db/schema';
import { desc, sql, count, avg } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin access
    const session = await auth();
    if (!session?.user?.email || session.user.email !== "<EMAIL>") {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const days = parseInt(searchParams.get('days') || '30');

    // Calculate date filter
    const dateFilter = new Date();
    dateFilter.setDate(dateFilter.getDate() - days);

    // Get total optimizations count
    const [totalResult] = await db
      .select({ count: count() })
      .from(temperatureOptimization);

    const totalOptimizations = Number(totalResult.count);

    // Get success rate
    const [successResult] = await db
      .select({
        total: count(),
        successful: sql<number>`sum(case when ${temperatureOptimization.wasSuccessful} then 1 else 0 end)`
      })
      .from(temperatureOptimization);

    const successRate = Number(successResult.successful) / Number(successResult.total) || 0;

    // Get average temperature
    const [avgTempResult] = await db
      .select({ avgTemp: avg(temperatureOptimization.optimizedTemperature) })
      .from(temperatureOptimization);

    const averageTemperature = Number(avgTempResult.avgTemp) || 0;

    // Get average duration (excluding nulls)
    const [avgDurationResult] = await db
      .select({
        avgDuration: sql<number>`avg(${temperatureOptimization.optimizationDuration})`
      })
      .from(temperatureOptimization)
      .where(sql`${temperatureOptimization.optimizationDuration} IS NOT NULL`);

    const averageDuration = Number(avgDurationResult.avgDuration) || 0;

    // Get average file count
    const [avgFileCountResult] = await db
      .select({
        avgFileCount: sql<number>`avg(${temperatureOptimization.fileCount})`
      })
      .from(temperatureOptimization)
      .where(sql`${temperatureOptimization.fileCount} > 0`);

    const averageFileCount = Number(avgFileCountResult.avgFileCount) || 0;

    // Get most common context factors
    const contextFactorsQuery = await db
      .select({
        factors: temperatureOptimization.contextFactors
      })
      .from(temperatureOptimization);

    const factorCounts: Record<string, number> = {};
    contextFactorsQuery.forEach(row => {
      const factors = row.factors as string[];
      factors.forEach(factor => {
        factorCounts[factor] = (factorCounts[factor] || 0) + 1;
      });
    });

    const mostCommonFactors = Object.entries(factorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([factor, count]) => ({ factor, count }));

    // Get temperature distribution
    const temperatureRanges = [
      { range: '0.0 - 0.2', min: 0.0, max: 0.2 },
      { range: '0.2 - 0.4', min: 0.2, max: 0.4 },
      { range: '0.4 - 0.6', min: 0.4, max: 0.6 },
      { range: '0.6 - 0.8', min: 0.6, max: 0.8 },
      { range: '0.8 - 1.0', min: 0.8, max: 1.0 },
    ];

    const temperatureDistribution = await Promise.all(
      temperatureRanges.map(async ({ range, min, max }) => {
        const [result] = await db
          .select({ count: count() })
          .from(temperatureOptimization)
          .where(sql`${temperatureOptimization.optimizedTemperature} >= ${min} AND ${temperatureOptimization.optimizedTemperature} < ${max}`);

        return { range, count: Number(result.count) };
      })
    );

    // Get progression analytics
    const progressionData = await db
      .select({
        userProgression: temperatureOptimization.userProgression
      })
      .from(temperatureOptimization)
      .where(sql`${temperatureOptimization.userProgression} IS NOT NULL`);

    let progressionAnalytics = {
      progressingRate: 0,
      stuckRate: 0,
      mildStuckRate: 0,
      moderateStuckRate: 0,
      severeStuckRate: 0,
      totalSamples: 0,
    };

    if (progressionData.length > 0) {
      const progressions = progressionData.map(row => row.userProgression as any);
      const total = progressions.length;

      const progressing = progressions.filter(p => p.isProgressing).length;
      const stuck = progressions.filter(p => p.isStuck).length;
      const mildStuck = progressions.filter(p => p.isStuck && p.stuckSeverity === 'mild').length;
      const moderateStuck = progressions.filter(p => p.isStuck && p.stuckSeverity === 'moderate').length;
      const severeStuck = progressions.filter(p => p.isStuck && p.stuckSeverity === 'severe').length;

      progressionAnalytics = {
        progressingRate: progressing / total,
        stuckRate: stuck / total,
        mildStuckRate: mildStuck / total,
        moderateStuckRate: moderateStuck / total,
        severeStuckRate: severeStuck / total,
        totalSamples: total,
      };
    }

    // Get recent optimizations
    const recentOptimizations = await db
      .select()
      .from(temperatureOptimization)
      .orderBy(desc(temperatureOptimization.createdAt))
      .limit(limit);

    const analyticsData = {
      totalOptimizations,
      successRate,
      averageTemperature,
      averageDuration,
      averageFileCount,
      mostCommonFactors,
      temperatureDistribution,
      progressionAnalytics,
      recentOptimizations,
    };

    return NextResponse.json(analyticsData);

  } catch (error) {
    console.error('Error fetching temperature optimization analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}
