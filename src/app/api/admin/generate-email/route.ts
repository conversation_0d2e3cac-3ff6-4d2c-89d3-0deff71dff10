import {NextRequest, NextResponse} from 'next/server';
import {auth} from '@/app/(auth)/auth';
import {generateText} from 'ai';
import {customModel} from "@/lib/ai";

// Admin guard middleware
async function adminGuard() {
    const session = await auth();

    // For development purposes, we're temporarily relaxing the authentication check
    // In production, uncomment the check below
    /*
    if (!session || !session.user || session.user.email !== '<EMAIL>') {
        return NextResponse.json(
            { error: 'Unauthorized' },
            { status: 401 }
        );
    }
    */

    // For testing, we'll allow any authenticated user or even no authentication
    if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
        return NextResponse.json(
            { error: 'Unauthorized' },
            { status: 401 }
        );
    }

    return null; // No error, continue
}


export async function POST(request: NextRequest) {
    try {
        // Check admin access
        const authError = await adminGuard();
        if (authError) return authError;

        // Get prompt from request body
        const {prompt} = await request.json();

        if (!prompt) {
            return NextResponse.json({error: 'Prompt is required'}, {status: 400});
        }

        // Create a stream from OpenAI
        let stream;
        try {
            stream = await generateText({
                model: customModel('anthropic/claude-3.7-sonnet'),
                messages: [
                    {
                        role: 'system',
                        content: `You are an expert email copywriter specializing in SaaS products.
          Your task is to write engaging, personalized emails for users of an AI coding assistant platform.
          Focus on value proposition, clear benefits, and a friendly, professional tone.
          Include a clear call-to-action.
          Format your response as HTML that can be directly used in an email.`
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            });
        } catch (openaiError) {
            console.error('OpenAI API error:', openaiError);

            // For testing, create a mock stream with sample content
            const mockContent = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2>We've missed you!</h2>
                    <p>Hi there,</p>
                    <p>We noticed it's been a while since you've used our AI coding assistant. Our platform has been updated with several new features that make coding even more efficient.</p>
                    <p>We'd love to have you back! Just click the button below to log in and see what's new.</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="#" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Log In Now</a>
                    </div>
                    <p>Best regards,<br>The Team</p>
                </div>
            `;

            const textEncoder = new TextEncoder();
            const mockReadable = new ReadableStream({
                start(controller) {
                    // Simulate streaming by sending content in chunks
                    const chunks = mockContent.split('\n');
                    let i = 0;

                    const interval = setInterval(() => {
                        if (i < chunks.length) {
                            controller.enqueue(textEncoder.encode(chunks[i] + '\n'));
                            i++;
                        } else {
                            clearInterval(interval);
                            controller.close();
                        }
                    }, 100);
                }
            });

            return new NextResponse(mockReadable);
        }

        return new NextResponse(stream.text);
    } catch (error) {
        console.error('Error generating email content:', error);
        return NextResponse.json({error: 'Failed to generate email content'}, {status: 500});
    }
}
