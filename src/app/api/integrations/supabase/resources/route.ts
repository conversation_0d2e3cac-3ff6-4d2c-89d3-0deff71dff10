import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/app/(auth)/auth";
import { SupabaseIntegrationProvider, SupabaseIntegrationError } from '@/lib/integrations/supabase/SupabaseIntegrationProvider';

// Initialize provider
const supabaseProvider = new SupabaseIntegrationProvider();

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(req.url);
    const resourceType = url.searchParams.get('type') || 'functions';
    const projectId = url.searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }
    
    // Use the simplified getProjectResources method
    try {
      const resources = await supabaseProvider.getProjectResources({
        projectId,
        resourceType: resourceType as 'functions' | 'tables' | 'storage'
      });
      
      return NextResponse.json(resources);
    } catch (error: unknown) {
      if (error instanceof SupabaseIntegrationError) {
        return NextResponse.json({ error: error.message }, { status: 400 });
      }
      throw error;
    }
  } catch (error) {
    console.error(`Error fetching Supabase resources:`, error);
    return NextResponse.json(
      { error: `Failed to fetch Supabase resources` },
      { status: 500 }
    );
  }
}
