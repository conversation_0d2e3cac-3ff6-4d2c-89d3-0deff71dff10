import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/app/(auth)/auth";
import { SupabaseIntegrationProvider } from '@/lib/integrations/supabase/SupabaseIntegrationProvider';

// Initialize provider
const supabaseProvider = new SupabaseIntegrationProvider();

/**
 * API route for fetching Supabase logs
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(req.url);
    const service = url.searchParams.get('service') || 'edge-function';
    const projectId = url.searchParams.get('projectId');
    const functionId = url.searchParams.get('functionId');
    const limit = url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit')!) : 100;

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }
    
    // Use the getLogs method from SupabaseIntegrationProvider
    try {
      const logs = await supabaseProvider.getLogs({
        projectId,
        service: service as 'api' | 'branch-action' | 'postgres' | 'edge-function' | 'auth' | 'storage' | 'realtime',
        limit,
        ...(functionId && { functionId })
      });
      
      return NextResponse.json(logs);
    } catch (error: unknown) {
      if (error instanceof Error) {
        return NextResponse.json({ error: error.message }, { status: 400 });
      }
      return NextResponse.json({ error: 'Unknown error occurred' }, { status: 500 });
    }
  } catch (error) {
    console.error(`Error fetching Supabase logs:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
