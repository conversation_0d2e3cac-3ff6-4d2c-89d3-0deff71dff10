import { NextRequest, NextResponse } from 'next/server';
import { SupabaseIntegrationProvider } from '@/lib/integrations/supabase/SupabaseIntegrationProvider';
import { getUserIntegrationByProvider } from '@/lib/integrations/queries';
import {auth} from "@/app/(auth)/auth";

// Initialize providers
const providers = {
  supabase: new SupabaseIntegrationProvider(),
};

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ provider: keyof typeof providers }> },
) {
  const paramsAwaited = await params;
  const providerKey = paramsAwaited.provider;
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const connection = await getUserIntegrationByProvider({
      userId: session.user.id,
      provider: providerKey,
    });

    if (!connection) {
      return new NextResponse('Connection not found', { status: 404 });
    }

    const provider = providers[providerKey];
    if (!provider) {
      return new NextResponse('Invalid provider', { status: 400 });
    }

    const databases = await provider.getDatabases({ connectionId: connection.id });
    return NextResponse.json(databases);
  } catch (error) {
    console.error('Failed to get databases:', error);
    return new NextResponse('Failed to get databases', { status: 500 });
  }
}

// export async function POST(
//   req: NextRequest,
//   { params }: { params: Promise<{ providerKey: keyof typeof providers }> },) {
//   try {
//     const {providerKey} = await params;
//     const session = await auth();
//     if (!session?.user) {
//       return new NextResponse('Unauthorized', { status: 401 });
//     }
//
//     const connection = await getUserIntegrationByProvider({
//       userId: session.user.id,
//       provider: providerKey,
//     });
//
//     if (!connection) {
//       return new NextResponse('Connection not found', { status: 404 });
//     }
//
//     const provider = providers[providerKey];
//     if (!provider) {
//       return new NextResponse('Invalid provider', { status: 400 });
//     }
//
//     const body = await req.json();
//     const { name } = body;
//
//     const database = await provider.createDatabase({ connectionId: connection.id, name });
//     return NextResponse.json(database);
//   } catch (error) {
//     console.error('Failed to create database:', error);
//     return new NextResponse('Failed to create database', { status: 500 });
//   }
// }
