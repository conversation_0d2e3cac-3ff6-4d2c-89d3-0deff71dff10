import { NextRequest, NextResponse } from 'next/server';
import { SupabaseIntegrationProvider } from '@/lib/integrations/supabase/SupabaseIntegrationProvider';
import { getUserIntegrationByProvider } from '@/lib/integrations/queries';
import { auth } from "@/app/(auth)/auth";
import { ConnectionStatus } from '@/lib/db/schema';

const providers = {
  supabase: new SupabaseIntegrationProvider(),
};

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ provider: keyof typeof providers }> },
) {
  try {
    const paramsAwaited = await params;
    const providerKey = paramsAwaited.provider;
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const provider = providers[providerKey];
    if (!provider) {
      return new NextResponse('Invalid provider', { status: 400 });
    }

    const url = new URL(req.url);
    const code = url.searchParams.get('code');
    if (!code) {
      return new NextResponse('No code provided', { status: 400 });
    }

    // Get OAuth token
    const token = await provider.handleOAuthCallback(code);

    // Get or create connection
    let connection = await getUserIntegrationByProvider({
      userId: session.user.id,
      provider: providerKey,
    });

    if (connection) {
      console.log('Updating connection')
      // Update existing connection
      connection = await provider.update({
        id: connection.id,
        metadata: {
          ...connection.metadata,
          oauth: token,
        },
      });
    } else {
      // Create new connection
      connection = await provider.connect({
        userId: session.user.id,
        provider: providerKey,
        metadata: { oauth: token },
      });
    }

    // Return HTML that closes the popup
    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Connection Successful</title>
        </head>
        <body>
          <script>
            window.close();
          </script>
          <div style="display:flex;justify-content:center;align-items:center;height:100vh;font-family:system-ui;">
            <div style="text-align:center;">
              <h1>Connection Successful!</h1>
              <p>You can close this window.</p>
            </div>
          </div>
        </body>
      </html>
      `,
      {
        headers: { 'Content-Type': 'text/html' },
      }
    );
  } catch (error) {
    console.error('Failed to handle OAuth callback:', error);
    
    // Return HTML that shows error and closes the popup
    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Connection Failed</title>
        </head>
        <body>
          <script>
            setTimeout(() => window.close(), 3000);
          </script>
          <div style="display:flex;justify-content:center;align-items:center;height:100vh;font-family:system-ui;">
            <div style="text-align:center;color:#dc2626;">
              <h1>Connection Failed</h1>
              <p>Please try again. This window will close automatically.</p>
            </div>
          </div>
        </body>
      </html>
      `,
      {
        headers: { 'Content-Type': 'text/html' },
      }
    );
  }
}
