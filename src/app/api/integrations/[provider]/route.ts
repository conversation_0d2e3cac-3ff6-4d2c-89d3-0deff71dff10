import {NextRequest, NextResponse} from 'next/server';
import {createClient} from '@supabase/supabase-js';
import {SupabaseIntegrationProvider} from '@/lib/integrations/supabase/SupabaseIntegrationProvider';
import {getUserIntegrationByProvider} from '@/lib/integrations/queries';
import {auth} from "@/app/(auth)/auth";
import {ConnectionStatus} from "@/lib/db/schema";

// Initialize providers
const providers = {
    supabase: new SupabaseIntegrationProvider(),
};

export async function POST(
    req: NextRequest,
    { params }: { params: Promise<{ provider: keyof typeof providers }> },
) {
    try {
        const paramsAwaited = await params;
        const providerKey = paramsAwaited.provider;
        const session = await auth();
        if (!session?.user) {
            return new NextResponse('Unauthorized', {status: 401});
        }

        const provider = providers[providerKey];
        if (!provider) {
            return new NextResponse('Invalid provider', {status: 400});
        }

        const body = await req.json();
        const {metadata} = body;

        const connection = await provider.connect({
            userId: session.user.id,
            provider: providerKey,
            metadata,
        });

        return NextResponse.json(connection);
    } catch (error) {
        console.error('Failed to connect integration:', error);
        return new NextResponse('Failed to connect integration', {status: 500});
    }
}

export async function GET(
    req: NextRequest,
    { params }: { params: Promise<{ provider: keyof typeof providers }> },
) {
    try {
        const paramsAwaited = await params;
        const providerKey = paramsAwaited.provider;
        const session = await auth();
        if (!session?.user) {
            return new NextResponse('Unauthorized', {status: 401});
        }

        const provider = providers[providerKey];
        if (!provider) {
            return new NextResponse('Invalid provider', {status: 400});
        }

        // Check if this is a connection status check or an OAuth initiation
        const url = new URL(req.url);
        const isStatusCheck = url.searchParams.has('status');

        if (isStatusCheck) {
            const connection = await getUserIntegrationByProvider({
                userId: session.user.id,
                provider: providerKey,
            });

            if (!connection) {
                return new NextResponse('Connection not found', {status: 404});
            }

            console.log('connection', connection)
            if(connection.status !== ConnectionStatus.CONNECTED) {
                return new NextResponse('Connection not found', {status: 404});
            }

            return NextResponse.json(connection);
        } else {
            // Initiate OAuth flow
            if (!provider.config?.oauth) {
                return new NextResponse('OAuth not configured for this provider', {status: 400});
            }

            const { clientId, authorizationUrl, redirectUri, scope } = provider.config.oauth;
            const state = Buffer.from(JSON.stringify({ userId: session.user.id })).toString('base64');

            const authUrl = new URL(authorizationUrl);
            authUrl.searchParams.set('client_id', clientId);
            authUrl.searchParams.set('redirect_uri', redirectUri);
            authUrl.searchParams.set('response_type', 'code');
            authUrl.searchParams.set('scope', scope.join(' '));
            authUrl.searchParams.set('state', state);

            return NextResponse.redirect(authUrl.toString());
        }
    } catch (error) {
        console.error('Failed to get integration:', error);
        return new NextResponse('Failed to get integration', {status: 500});
    }
}

export async function DELETE(
    req: NextRequest,
    {params}: { params: Promise<{ provider: keyof typeof providers }> },
) {
    const paramsAwaited = await params;
    const providerKey = paramsAwaited.provider;
    try {
        const session = await auth();
        if (!session?.user) {
            return new NextResponse('Unauthorized', {status: 401});
        }

        const connection = await getUserIntegrationByProvider({
            userId: session.user.id,
            provider: providerKey,
        });

        if (!connection) {
            return new NextResponse('Connection not found', {status: 404});
        }

        const provider = providers[providerKey];
        if (!provider) {
            return new NextResponse('Invalid provider', {status: 400});
        }

        await provider.disconnect({connectionId: connection.id});
        return new NextResponse(null, {status: 204});
    } catch (error) {
        console.error('Failed to delete integration:', error);
        return new NextResponse('Failed to delete integration', {status: 500});
    }
}
