import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getProjectsByUserId, saveProject } from '@/lib/db/queries';
import { randomUUID } from 'crypto';
import { generateProjectAttributes } from "@/app/(project)/projects/actions";
import { Message } from "ai";
import { slugify } from "@/lib/utils";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    const anonymousId = request.headers.get('x-anonymous-id');

    const userId= session?.user.id || anonymousId;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const projects = await getProjectsByUserId({ id: userId });
    
    return NextResponse.json(projects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await request.json();
    const { message } = body;

    // Generate project attributes using AI
    const projectMetadata = await generateProjectAttributes({
      messages: [
        {
          content: message,
          role: 'user'
        } as Message
      ] as Message[]
    });
    
    const projectId = randomUUID();
    
    // Generate a unique slug based on the app name
    const baseSlug = slugify(projectMetadata.appName);
    const timestamp = Date.now().toString().slice(-6); // Use last 6 digits of timestamp for uniqueness
    const uniqueSlug = `${baseSlug}-${timestamp}`;
    
    // Save the project with the unique slug
    await saveProject({
      id: projectId,
      userId: session.user.id,
      slug: uniqueSlug,
      ...projectMetadata
    });
    
    return NextResponse.json({ 
      id: projectId,
      slug: uniqueSlug,
      appName: projectMetadata.appName
    });
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
