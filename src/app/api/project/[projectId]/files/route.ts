import {
  getChatById,
  getLatestFileState,
  getLatestFileStateByProject,
  getProjectById,
  saveFileStateAndCacheIfNeeded,
  getLatestUserMessageByChatId
} from "@/lib/db/queries";
import {auth} from "@/app/(auth)/auth";
import {DEFAULT_CODE, DEFAULT_DEPENDENCIES} from "@/types/editor";
import {validateExpoFiles} from "@/lib/utils/expo-validator";
import {FileItem} from "@/types/file";

export async function POST(
    request: Request,
    { params }: { params: Promise<{ projectId: string }> }
) {
    const {projectId} = await params;
    
    try {
        // Verify authentication
        const session = await auth();
        const anonymousId = request.headers.get('x-anonymous-id');
        const userId = session?.user.id || anonymousId;
        
        if (!userId) {
            return new Response('Unauthorized', { status: 401 });
        }
        
        // Get the project to verify ownership
        const project = await getProjectById({ id: projectId });
        if (!project) {
            return new Response('Project not found', { status: 404 });
        }
        
        // Verify ownership unless public or admin
        if (project.visibility !== 'public' && 
            project.userId !== userId && 
            session?.user?.email !== "<EMAIL>") {
            return new Response('Unauthorized', { status: 401 });
        }
        
        // Parse the request body
        const body = await request.json();
        const { files, dependencies, chatId } = body;
        
        if (!files || !dependencies || !chatId) {
            return new Response('Missing required fields', { status: 400 });
        }
        
        // Get the latest user message ID for this chat
        // This ensures dependencies are associated with the latest user interaction
        let messageId = body.messageId;
        
        if (!messageId || messageId.includes("manual-")) {
            // Try to get the latest user message
            const latestUserMessage = await getLatestUserMessageByChatId({ chatId });
            
            if (latestUserMessage) {
                messageId = latestUserMessage.id;
                console.log('Using latest user message ID:', messageId);
            } else {
                // If no user messages found, generate a random ID
                throw new Error("Could not save snapshot. No messages found.")
            }
        }
        
        // Save the file state
        const savedState = await saveFileStateAndCacheIfNeeded({
            chatId,
            projectId,
            messageId,
            files,
            dependencies,
            shouldCache: false
        });
        
        if (!savedState) {
            return new Response('Failed to save file state', { status: 500 });
        }
        
        return new Response(
            JSON.stringify({
                success: true,
                fileState: savedState
            }),
            { headers: { 'Content-Type': 'application/json' } }
        );
    } catch (error) {
        console.error('Error saving dependencies:', error);
        return new Response('Internal Server Error', { status: 500 });
    }
}

export async function GET(
    request: Request,
    { params }: { params: Promise<{ projectId: string }> }
) {
    const {projectId} = await params;
    try {
        const project = await getProjectById({ id: projectId });

        if (!project) {
            return new Response('Project not found', { status: 404 });
        }

        const anonymousId = request.headers.get('x-anonymous-id');

        // For public chats, allow access without auth
        if (project.visibility === 'public') {
            // Continue to file state fetch
        } else {
            // For private chats, require auth and verify ownership
            const session = await auth();
            const userId= session?.user.id || anonymousId;

            if (!userId) {
                return new Response('Unauthorized', { status: 401 });
            }
            if (project.userId !== userId && session?.user?.email !== "<EMAIL>") {
                return new Response('Unauthorized', {status: 401});
            }
        }

        // Get the latest file state
        const fileState = await getLatestFileStateByProject(projectId);

        if (!fileState) {
            // Return default code structure if no state exists
            return new Response(
                JSON.stringify({
                    files: DEFAULT_CODE,
                    dependencies: DEFAULT_DEPENDENCIES,
                }),
                {
                    headers: { 'Content-Type': 'application/json' },
                }
            );
        }

        return new Response(
            JSON.stringify({
                files: fileState.files,
                dependencies: fileState.dependencies || DEFAULT_DEPENDENCIES,
                version: fileState.version
            }),
            {
                headers: { 'Content-Type': 'application/json' },
            }
        );
    } catch (error) {
        console.error('Error fetching files:', error);
        return new Response('Internal Server Error', { status: 500 });
    }
}
