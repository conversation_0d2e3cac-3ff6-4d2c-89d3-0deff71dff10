import { NextResponse } from 'next/server';
import { getSession, getContentChunks } from '@/lib/store/redis-session-store';
import { getProjectById, getChatById } from '@/lib/db/queries';
import { parseStreamingContent, getInitialLoadingHTML } from '@/lib/design-generator';
import * as HtmlTemplateService from '@/lib/services/html-template-service';

/**
 * API endpoint to render HTML for a design session
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ projectId: string; chatId: string; sessionId: string }> }
) {
  try {
    const { projectId, chatId, sessionId } = await params;
    
    console.log('[DEBUG] design-render route called with:', { projectId, chatId, sessionId });

    // Get project and verify access
    const project = await getProjectById({ id: projectId });
    if (!project) {
      return new Response('Project not found', { status: 404 });
    }

    // Get the chat and verify it belongs to the project
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.projectId !== projectId) {
      return new Response('Chat not found or does not belong to this project', { status: 404 });
    }
    
    // Try to get HTML content with fallback chain:
    // 1. Try Redis first
    // 2. If not in Redis, check chat record
    // 3. If not found anywhere, return fallback HTML
    
    let html: any = null;
    let status = 'complete';
    
    // Get the Redis session for status information and final HTML
    const redisSession = await getSession(chatId);
    console.log(`[DEBUG] Redis session for chat ${chatId}:`, {
      status: redisSession?.status,
      hasFinalHtml: !!redisSession?.finalHtml,
      hasHtml: !!redisSession?.html
    });
    
    if (redisSession) {
      // If status is undefined or null, default to 'complete' to avoid infinite refresh
      status = redisSession.status === 'generating' ? 'generating' : 'complete';
      
      // First check if we have finalHtml (this is the preferred source)
      if (redisSession.finalHtml) {
        console.log(`[DEBUG] Using finalHtml from Redis session for ${chatId}`);
        html = redisSession.finalHtml;
      }
      // If no finalHtml, try regular html
      else if (redisSession.html) {
        console.log(`[DEBUG] Using html from Redis session for ${chatId}`);
        html = redisSession.html;
      }
      // If still no html, try to extract from content chunks
      else {
        // Try to get all content chunks
        const contentChunks = await getContentChunks(chatId);
        console.log(`[DEBUG] Found ${contentChunks.length} content chunks for chat ${chatId}`);
        
        if (contentChunks.length > 0) {
          // Join all chunks to get the full content
          const content = contentChunks.join('');
          
          // Try to extract HTML from the content using our centralized function
          const extractedHtml = parseStreamingContent(content);
          console.log(`[DEBUG] Extracted HTML from content: ${extractedHtml ? 'YES' : 'NO'}`);
          
          if (extractedHtml) {
            html = extractedHtml;
          }
        }
      }
    }
    
    // If we still don't have HTML, check chat record
    if (!html) {
      // Check chat record
      if (chat.designHtml) {
        console.log(`[DEBUG] Using designHtml from chat record for ${chatId}`);
        html = chat.designHtml;
        status = chat.designStatus || 'complete';
      }
      // If not found anywhere, return fallback HTML
      else {
        console.log(`[DEBUG] Using fallback loading HTML for ${chatId}`);
        // Use the centralized initial loading HTML function
        // This now returns a complete HTML document
        html = getInitialLoadingHTML();
        
        // If we're still generating, set status accordingly
        if (redisSession && redisSession.status === 'generating') {
          status = 'generating';
        } else {
          status = 'complete';
        }
      }
    }

    // Check if the HTML is already a complete document
    const isCompleteHtml = html.trim().startsWith('<!DOCTYPE') || 
                          html.includes('<html') || 
                          html.includes('<HTML');

    // If it's already a complete document, just make sure it has the session status meta tag
    if (isCompleteHtml) {
      // If it has a head tag but no session-status meta tag, add it
      if (html.includes('<head>') && !html.includes('meta name="session-status"')) {
        html = html.replace('<head>', `<head>\n<meta name="session-status" content="${status}">`);
      }
      // Return the HTML as is
      return new Response(html, {
        headers: {
          'Content-Type': 'text/html',
        },
      });
    }

    // If it's not a complete document, wrap it
    return new Response(
      `<\!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta name="session-status" content="${status}">
          <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://unpkg.com; img-src 'self' data: blob: https: http:; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://unpkg.com;">
          <script src="https://cdn.tailwindcss.com"></script>
          <style>
            body {
              display: flex;
              overflow-x: auto;
              padding: 20px;
              background-color: #f5f5f5;
              min-height: 100vh;
              align-items: center;
              justify-content: flex-start;
            }

            /* Ensure mobile frames maintain aspect ratio and fit in viewport */
            .mobile-frame {
              height: 80vh;
              width: auto;
              /* Maintain the aspect ratio of 433:882 (0.49093:1) */
              aspect-ratio: 433/882;
              margin: 0 auto;
              max-height: 80vh !important;
              /* Ensure the frame is centered */
              display: block;
            }
            
            /* Center the screen container */
            .screen-container {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              width: 100%;
            }
            
            /* Make sure the screens container takes full width */
            .screens-container {
              width: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            /* Add script to preserve form state */
            input, textarea, select {
              transition: none !important;
            }
          </style>
          <script>
            // Simple form state preservation
            document.addEventListener('DOMContentLoaded', () => {
              // Add event listeners to form elements if needed
              document.querySelectorAll('input, textarea, select').forEach(el => {
                el.addEventListener('change', (e) => {
                  localStorage.setItem(el.id || el.name || 'form-element', el.value);
                });
                
                // Restore any saved values
                const savedValue = localStorage.getItem(el.id || el.name || 'form-element');
                if (savedValue) {
                  el.value = savedValue;
                }
              });
            });
          </script>
        </head>
        <body>
          ${html}
          <script src="https://unpkg.com/lucide@latest"></script>
          <script>lucide.createIcons();</script>
        </body>
      </html>`,
      {
        headers: {
          'Content-Type': 'text/html',
        },
      }
    );
  } catch (error) {
    console.error('Error rendering design:', error);
    return NextResponse.json(
      { error: 'Failed to render design' },
      { status: 500 }
    );
  }
}
