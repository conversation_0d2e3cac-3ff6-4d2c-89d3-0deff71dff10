import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getChatById, getProjectById, updateChat, updateProject } from '@/lib/db/queries';

/**
 * API endpoint to approve a design and save it to the project
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ projectId: string; chatId: string }> }
) {
  const { projectId, chatId } = await params;

  try {
    // Get project and verify access
    const project = await getProjectById({ id: projectId });
    if (!project) {
      return new Response('Project not found', { status: 404 });
    }

    const anonymousId = request.headers.get('x-anonymous-id');

    // For public projects, allow access without auth
    if (project.visibility !== 'public') {
      // For private projects, require auth and verify ownership
      const session = await auth();
      const userId = session?.user.id || anonymousId;

      if (!userId) {
        return new Response('Unauthorized', { status: 401 });
      }
      
      if (project.userId !== userId && session?.user?.email !== "<EMAIL>") {
        return new Response('Unauthorized', { status: 401 });
      }
    }

    // Get the chat and verify it belongs to the project
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.projectId !== projectId) {
      return new Response('Chat not found or does not belong to this project', { status: 404 });
    }

    // Verify that the chat has a completed design
    if (!chat.designHtml || chat.designStatus !== 'complete') {
      return new Response('No completed design found for this chat', { status: 400 });
    }

    // Mark the design as approved in the chat
    await updateChat({
      id: chatId,
      updatedAt: new Date(),
      isDesignApproved: true
    });

    // Save the design HTML to the project
    await updateProject({
      id: projectId,
      approvedDesignHtml: chat.designHtml,
      designChatId: chatId,
      updatedAt: new Date()
    });

    return NextResponse.json({ 
      success: true,
      message: 'Design approved and saved to project'
    });
  } catch (error) {
    console.error('Error approving design:', error);
    return NextResponse.json(
      { error: 'Failed to approve design' },
      { status: 500 }
    );
  }
}
