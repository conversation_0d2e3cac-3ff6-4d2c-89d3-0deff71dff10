import { NextResponse } from 'next/server';
import { updateUserState, getSession } from '@/lib/store/redis-session-store';

/**
 * API endpoint to update user state for a design session
 * This allows preserving user interactions between refreshes
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  try {
    const { sessionId } = await params;

    // Check if session exists
    const session = await getSession(sessionId);
    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { scrollPosition, formValues, activeElement } = body;

    // Update user state in the session
    await updateUserState(sessionId, {
      scrollPosition,
      formValues,
      activeElement
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating session state:', error);
    return NextResponse.json(
      { error: 'Failed to update session state' },
      { status: 500 }
    );
  }
}
