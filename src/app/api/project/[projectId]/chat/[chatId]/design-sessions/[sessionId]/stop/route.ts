import { NextResponse } from 'next/server';
import { getSession, updateSessionStatus } from '@/lib/store/redis-session-store';

/**
 * API endpoint to stop a design generation session
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  try {
    const { sessionId } = await params;

    // Get the session data
    const session = await getSession(sessionId);

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Only stop if the session is still generating
    if (session.status === 'generating' || session.status === 'initializing') {
      // Update the session status to complete to stop generation
      await updateSessionStatus(sessionId, 'complete');
      
      return NextResponse.json({
        status: 'stopped',
        message: 'Design generation stopped successfully'
      });
    }

    // If already complete or error, just return the current status
    return NextResponse.json({
      status: session.status,
      message: 'Session was already in final state'
    });
  } catch (error) {
    console.error('Error stopping design generation:', error);
    return NextResponse.json(
      { error: 'Failed to stop design generation' },
      { status: 500 }
    );
  }
}
