import { NextResponse } from 'next/server';
import { createSession, clearContentChunks } from '@/lib/store/redis-session-store';
import { startDesignGeneration } from '@/lib/design-generator';
import { auth } from '@/app/(auth)/auth';
import { getChatById, getProjectById, updateChat, saveMessages, getMessagesByChatId } from '@/lib/db/queries';
import { generateUUID } from '@/lib/utils';

/**
 * API endpoint to create a new design preview session
 */
export async function POST(
  request: Request,
  { params }: { params: Promise<{ projectId: string; chatId: string, isFirstLoad: boolean }> }
) {
  const { projectId, chatId } = await params;

  try {
    // Parse request body
    const { prompt } = await request.json();

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Get project and verify access
    const project = await getProjectById({ id: projectId });
    if (!project) {
      return new Response('Project not found', { status: 404 });
    }

    const anonymousId = request.headers.get('x-anonymous-id');

    // For public projects, allow access without auth
    if (project.visibility !== 'public') {
      // For private projects, require auth and verify ownership
      const session = await auth();
      const userId = session?.user.id || anonymousId;

      if (!userId) {
        return new Response('Unauthorized', { status: 401 });
      }
      
      if (project.userId !== userId && session?.user?.email !== "<EMAIL>") {
        return new Response('Unauthorized', { status: 401 });
      }
    }

    // Get the chat and verify it belongs to the project
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.projectId !== projectId) {
      return new Response('Chat not found or does not belong to this project', { status: 404 });
    }

    // Check if this is the first time initializing this chat
    if (chat && !chat.isInitialized) {
      // Update the chat to mark it as initialized
      await updateChat({
        id: chatId,
        updatedAt: new Date(),
        isInitialized: true
      });
      console.log(`Chat ${chatId} marked as initialized`);
    }

    // Use the chatId as the sessionId for consistency
    const sessionId = chatId;

    // Clear any existing content chunks for this session
    // This ensures we start fresh with each new design generation
    console.log(`Clearing existing content chunks for session ${sessionId}`);
    await clearContentChunks(sessionId);

    const newMessageId = generateUUID();
    // Save the user's message to the database
    // await saveMessages({
    //   messages: [{
    //     id: newMessageId,
    //     chatId,
    //     projectId,
    //     role: 'user',
    //     content: prompt,
    //     createdAt: new Date(),
    //     userId: project.userId,
    //     remoteProvider: null,
    //     remoteProviderId: null,
    //     componentContexts: null
    //   }]
    // });

    // Get previous messages for context
    const previousMessages = await getMessagesByChatId({ id: chatId });


    // Create a new session in the store
    await createSession(sessionId, prompt);

    // Start the design generation process in the background
    // This will update the session HTML as generation progresses
    // Use Promise.resolve to run this asynchronously without waiting
    Promise.resolve(startDesignGeneration(sessionId, prompt, previousMessages.filter(msg => msg.id !== newMessageId))).catch(error => {
      console.error('Error in background design generation:', error);
    });

    // Return the session ID to the client
    return NextResponse.json({ sessionId });
  } catch (error) {
    console.error('Error creating design session:', error);
    return NextResponse.json(
      { error: 'Failed to create design session' },
      { status: 500 }
    );
  }
}
