import { NextResponse } from 'next/server';
import { getContentChunks, getSession } from "@/lib/store/redis-session-store";
import { parseStreamingContent } from "@/lib/design-generator";

/**
 * API endpoint to stream design updates to the client
 * Uses Server-Sent Events (SSE) for real-time updates
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ sessionId: string, projectId: string, chatId: string }> }
) {
  const { sessionId, projectId, chatId } = await params;

  // Check if session exists
  const session = await getSession(sessionId);
  console.log('session', session)
  if (!session) {
    return NextResponse.json(
      { error: 'Session not found' },
      { status: 404 }
    );
  }

  // Create a readable stream for SSE
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send initial data - use chatId to get content chunks
        const chunks = await getContentChunks(chatId);
        console.log(`[DEBUG] Stream: Found ${chunks.length} chunks for chatId ${chatId}`);
        if (chunks.length > 0) {
          // Join all chunks to get the current content
          const content = chunks.join('');
          
          // Extract HTML if available, but always send the full content
          // Even if parseStreamingContent returns null, we'll send the content
          // so the client can display the raw content while waiting for HTML
          const html = parseStreamingContent(content);
          console.log(`[DEBUG] Stream: Extracted HTML: ${html ? 'YES' : 'NO'}`);
          
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
            status: session.status, 
            lastUpdated: session.lastUpdated,
            content: content,
            html: html || ''
          })}\n\n`));
        } else {
          // If no chunks yet, still send the session status
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
            status: session.status, 
            lastUpdated: session.lastUpdated,
            content: '',
            html: ''
          })}\n\n`));
        }

        // Set up interval to check for updates
        const intervalId = setInterval(async () => {
          try {
            const updatedSession = await getSession(sessionId);
            if (!updatedSession) {
              // Session expired or deleted
              clearInterval(intervalId);
              controller.close();
              return;
            }

            // Get the latest content chunks - use chatId for consistency
            const latestChunks = await getContentChunks(chatId);
            console.log(`[DEBUG] Stream interval: Found ${latestChunks.length} chunks for chatId ${chatId}`);
            const latestContent = latestChunks.join('');
            
            // Extract HTML if available, but always send the full content
            // Even if parseStreamingContent returns null, we'll send the content
            const html = parseStreamingContent(latestContent);
            console.log(`[DEBUG] Stream interval: Extracted HTML: ${html ? 'YES' : 'NO'}`);
            
            // Send updates with content and HTML
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({
              status: updatedSession.status,
              lastUpdated: updatedSession.lastUpdated,
              content: latestContent,
              html: html || ''
            })}\n\n`));

            // If generation is complete, close the stream
            if (updatedSession.status === 'complete' || updatedSession.status === 'error') {
              clearInterval(intervalId);
              controller.close();
            }
          } catch (error) {
            console.error('Error in SSE interval:', error);
            clearInterval(intervalId);
            controller.close();
          }
        }, 1000);

        // Clean up on abort
        request.signal.addEventListener('abort', () => {
          clearInterval(intervalId);
        });
      } catch (error) {
        console.error('Error starting SSE stream:', error);
        controller.close();
      }
    }
  });

  // Return the stream as SSE
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  });
}
