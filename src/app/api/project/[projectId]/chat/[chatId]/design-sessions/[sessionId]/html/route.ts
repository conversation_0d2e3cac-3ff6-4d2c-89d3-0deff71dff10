import { NextResponse } from 'next/server';
import { getSession } from '@/lib/store/redis-session-store';

/**
 * API endpoint to get the HTML content of a design session
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  try {
    const { sessionId } = await params;

    // Get the session data
    const session = await getSession(sessionId);

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Return the HTML content
    return NextResponse.json({
      html: session.html,
      status: session.status,
      lastUpdated: session.lastUpdated
    });
  } catch (error) {
    console.error('Error getting session HTML:', error);
    return NextResponse.json(
      { error: 'Failed to get session HTML' },
      { status: 500 }
    );
  }
}
