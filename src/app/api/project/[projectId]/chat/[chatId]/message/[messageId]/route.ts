import { auth } from "@/app/(auth)/auth";
import { getChatById, getProjectById } from "@/lib/db/queries";
import { db } from "@/lib/db";
import { fileState, message } from "@/lib/db/schema";
import { and, eq } from "drizzle-orm";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ projectId: string; chatId: string; messageId: string }> }
) {
  const { projectId, chatId, messageId } = await params;

  try {
    // Get project and verify access
    const project = await getProjectById({ id: projectId });
    if (!project) {
      return new Response('Project not found', { status: 404 });
    }

    const anonymousId = request.headers.get('x-anonymous-id');

    // For public projects, allow access without auth
    if (project.visibility !== 'public') {
      // For private projects, require auth and verify ownership
      const session = await auth();
      const userId = session?.user.id || anonymousId;

      if (!userId) {
        return new Response('Unauthorized', { status: 401 });
      }
      
      if (project.userId !== userId && session?.user?.email !== "<EMAIL>") {
        return new Response('Unauthorized', { status: 401 });
      }
    }

    // Get the chat and verify it belongs to the project
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.projectId !== projectId) {
      return new Response('Chat not found or does not belong to this project', { status: 404 });
    }

    // Get the message and verify it belongs to the chat
    const msg = await db.select()
      .from(message)
      .where(and(
        eq(message.id, messageId),
        eq(message.chatId, chatId)
      ))
      .limit(1);

    if (!msg || msg.length === 0) {
      return new Response('Message not found or does not belong to this chat', { status: 404 });
    }

    console.log('messageId', messageId);

    // Get the file state for this message
    const fileStateData = await db.select()
      .from(fileState)
      .where(eq(fileState.messageId, messageId))
      .limit(1);

    if (!fileStateData || fileStateData.length === 0) {
      return new Response('File state not found for this message', { status: 404 });
    }
    
    const fileStateRecord = fileStateData[0];

    return new Response(
      JSON.stringify({
        files: fileStateRecord.files,
        dependencies: fileStateRecord.dependencies,
        version: fileStateRecord.version,
        fileStateId: fileStateRecord.id,
        isHistorical: true
      }),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Error fetching file state:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}