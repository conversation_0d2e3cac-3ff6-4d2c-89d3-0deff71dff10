import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getProjectById, getChatsByProjectId, saveChat } from '@/lib/db/queries';
import { randomUUID } from 'crypto';

export async function GET(
    request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const session = await auth();

    const anonymousId = request.headers.get('x-anonymous-id');

    const userId = session?.user.id || anonymousId;

    if (!userId) {
      return NextResponse.json({error: 'Unauthorized'}, {status: 401});
    }

    const { projectId } = await params;
    
    // Get the project to verify ownership
    const project = await getProjectById({ id: projectId });
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Check if user owns the project
    if (project.userId !== userId && session?.user?.email !== "<EMAIL>") {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Get chats for this project
    const chats = await getChatsByProjectId({ id: projectId });
    
    return NextResponse.json(chats);
  } catch (error) {
    console.error('Error fetching project chats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project chats' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { projectId } = await params;
    const body = await request.json();
    const { title } = body;
    
    if (!title) {
      return NextResponse.json(
        { error: 'Chat title is required' },
        { status: 400 }
      );
    }
    
    // Get the project to verify ownership
    const project = await getProjectById({ id: projectId });
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Check if user owns the project
    if (project.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Create a new chat in this project
    const chatId = randomUUID();
    
    await saveChat({
      id: chatId,
      userId: session.user.id,
      projectId,
      title,
      updatedAt: new Date(),
    });
    
    return NextResponse.json({ id: chatId });
  } catch (error) {
    console.error('Error creating chat in project:', error);
    return NextResponse.json(
      { error: 'Failed to create chat in project' },
      { status: 500 }
    );
  }
}