import {NextRequest} from 'next/server';
import {SupabaseIntegrationProvider} from '@/lib/integrations/supabase/SupabaseIntegrationProvider';
import {auth} from "@/app/(auth)/auth";

export async function POST(
    req: NextRequest,
    {params}: { params: Promise<{  projectId: string }> }
) {
    try {
        const {projectId} = await params;
        const session = await auth();
        if (!session?.user) {
            return Response.json({error: 'Unauthorized'}, {status: 401});
        }

        const body = await req.json();
        const {query} = body;

        if (!query) {
            return Response.json(
                {error: 'Missing SQL query'},
                {status: 400}
            );
        }

        const supabase = new SupabaseIntegrationProvider();
        await supabase.executeSQL({
            projectId: projectId,
            query,
        });

        return Response.json({success: true});
    } catch (error: any) {
        return Response.json(
            {error: error.message || 'Internal server error'},
            {status: 500}
        );
    }
}