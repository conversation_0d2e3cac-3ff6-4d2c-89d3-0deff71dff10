import {NextRequest, NextResponse} from 'next/server';
import { ScreenshotMetadata } from '@/types/screenshot';
import {db} from '@/lib/db';
import {chat, designScreens, Message as DBMessage, projects, screenshotState} from '@/lib/db/schema';
import {eq, and} from 'drizzle-orm';
import {getDesignScreensByChatId} from '@/lib/db/design-screens';
import {v4 as uuidv4} from 'uuid';
import {put} from '@vercel/blob';
import {auth} from '@/app/(auth)/auth';
import {CoreUserMessage, generateObject, generateText, ImagePart, Message} from 'ai';
import {z} from 'zod';
import {customModel} from '@/lib/ai';
import {COMPLETE_STREAMLINED_PROMPT, TECHNICAL_REQUIREMENTS} from '@/lib/ai/prompt-directory/first-prompt';
import {DEFAULT_CODE, DEFAULT_DEPENDENCIES} from "@/types/editor";
import {exportData} from "@/lib/server-utils";
import dayjs from "dayjs";
import {wrapScreenInCode} from "@/lib/utils/html-utils";
import {STREAMLINED_AGENT_PROMPT} from "@/lib/ai/prompts/streamlined-agent-prompt";
import {saveChat, saveFileStateAndCacheIfNeeded, saveMessages, saveScreenShotState} from "@/lib/db/queries";
import {generateProjectAttributes} from "@/app/(project)/projects/actions";
import {generateTitleFromUserMessage} from "@/app/(generator)/generator/actions";
import {generateUUID} from "@/lib/utils";

/**
 * API endpoint to approve designs and prepare them for app building
 *
 * This endpoint:
 * 1. Updates the design approval status in the database
 * 2. Takes high-fidelity screenshots of each design screen
 * 3. Prepares design requirements for the app building phase
 */
export async function POST(
    request: NextRequest,
    {params}: { params: Promise<{ projectId: string; chatId: string }> }
) {
    try {
        const session = await auth();
        if (!session?.user) {
            return NextResponse.json({error: 'Unauthorized'}, {status: 401});
        }

        const userId = session.user.id;

        const {projectId, chatId} = await params;

        // 1. Update the chat record to mark designs as approved
        await db
            .update(chat)
            .set({
                isDesignApproved: true,
                updatedAt: new Date(),
            })
            .where(and(eq(chat.id, chatId), eq(chat.projectId, projectId)));

        // 2. Get all design screens for this chat
        const screens = await getDesignScreensByChatId({chatId});

        if (!screens || screens.length === 0) {
            return NextResponse.json(
                {error: 'No design screens found for this chat'},
                {status: 404}
            );
        }

        // 4. Take high-fidelity screenshots of each screen using Screenshotone API and store in Vercel Blob
        const screenshotUrls: Record<string, string> = {};
        const accessKey = process.env.SCREENSHOTONE_ACCESS_KEY;

        if (!accessKey) {
            throw new Error('SCREENSHOTONE_ACCESS_KEY environment variable is not set');
        }
        
        // Prepare screenshot metadata structure for later storage
        const screenshotMetadata: ScreenshotMetadata[] = [];

        const images = [
            "https://iyecjtddy3onkaf5.public.blob.vercel-storage.com/Screenshot%202025-06-01%20at%2012.19.34%E2%80%AFAM-BQEui2JFCGBkp0t0FDhhLPFVacmoRE.png",
            "https://iyecjtddy3onkaf5.public.blob.vercel-storage.com/Screenshot%202025-06-01%20at%2012.19.37%E2%80%AFAM-A5q0gsuAaEY7plCwSfN6CqVw9bbvWh.png",
            "https://iyecjtddy3onkaf5.public.blob.vercel-storage.com/Screenshot%202025-06-01%20at%2012.19.40%E2%80%AFAM-GOCa00NWNfYGbfX3klK259EFD69azx.png",
            "https://iyecjtddy3onkaf5.public.blob.vercel-storage.com/Screenshot%202025-06-01%20at%2012.19.43%E2%80%AFAM-Jy1NvN6XREsXucTXsu0GQ2ZVD3rukn.png",
            "https://iyecjtddy3onkaf5.public.blob.vercel-storage.com/Screenshot%202025-06-01%20at%2012.19.47%E2%80%AFAM-0XPDrEV0NGKoFwg6MX79hHvdCyfTba.png"
        ]

        // Process screens in parallel for faster execution
        await Promise.all(screens.map(async (screen, index) => {
            try {
                // Prepare the HTML content for screenshot
                const screenshotOptions = {
                    access_key: accessKey,
                    html: wrapScreenInCode(screen.html),
                    full_page: true,
                    viewport_width: 375,
                    viewport_height: 812,
                    device_scale_factor: 2,
                    format: 'png',
                    // response_type must be one of [by_format, empty, json]
                    response_type: 'by_format',
                    // wait_until must be an array
                    wait_until: ['networkidle0'],
                    // Add mobile emulation for better mobile rendering
                    user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                };

                // Call the Screenshotone API
                const response = await fetch('https://api.screenshotone.com/take', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(screenshotOptions),
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`Failed to generate screenshot for screen ${screen.id}: ${errorText}`);
                    throw new Error(`Screenshot API returned ${response.status}: ${errorText}`);
                }

                // Get the screenshot image data as a blob
                const imageBlob = new Blob([new Uint8Array(await response.arrayBuffer())], {type: 'image/png'});

                // Upload the screenshot to Vercel Blob
                const filename = `${projectId}/${chatId}/${screen.id}.png`;
                const {url} = await put(filename, imageBlob, {
                    access: 'public',
                    addRandomSuffix: false, // Use exact filename
                    contentType: 'image/png',
                });

                // Store the URL for the response
                // const screenshotUrl = images[index % screens.length] || images[0];
                const screenshotUrl = url;
                screenshotUrls[screen.id] = screenshotUrl;
                
                // Add to screenshot metadata
                screenshotMetadata.push({
                    id: screen.id,
                    name: screen.name || `Screen ${index + 1}`,
                    description: `Design screen ${index + 1}`,
                    elements: [],
                    url: screenshotUrl,
                    order: screen.order || index,
                    tags: []
                });
            } catch (error) {
                console.error(`Error generating screenshot for screen ${screen.name}:`, error);
                throw error;
            }
        }));

        // console.log('screenshotUrls', screenshotUrls)
        // 5. Generate design requirements from HTML for AI processing
        const designRequirements = await generateDesignRequirements(screens, screenshotUrls);

        // 6. Update the project with the approved design information
        await db
            .update(projects)
            .set({
                designChatId: chatId,
                designRequirements: JSON.stringify(designRequirements),
                // Keep designScreenshotUrls for backward compatibility but mark it as deprecated
                designScreenshotUrls: JSON.stringify(screenshotUrls), // Deprecated: Using screenshotState table instead
                designApproved: true,
                updatedAt: new Date(),
            })
            .where(eq(projects.id, projectId));
        
        // Prepare screenshot metadata for storage
        const screenshotMetadataArray = screenshotMetadata;

        // Now we will generate a new chat for the

        const userMessage = {
            role: "user",
            content: designRequirements.userPrompt
        };
        // Generate project attributes using AI
        const [title] = await Promise.all([
            generateTitleFromUserMessage({message: userMessage as CoreUserMessage})
        ])


        const appChatId = generateUUID();
        const savedChat = await saveChat({
            id: appChatId,
            userId,
            title,
            updatedAt: new Date(),
            projectId,
            isInitialized: false,
            type: 'app' // Add the chat type parameter
        });
        const savedMessage = await saveMessages({
            messages: [{
                createdAt: dayjs().toDate(),
                userId: userId,
                projectId,
                chatId: appChatId,
                role: userMessage.role,
                remoteProvider: null,
                remoteProviderId: null,
                // Format content as an array with text and image parts
                content: [
                    // First part is always the text message
                    {
                        type: "text",
                        text: userMessage.content
                    },
                    // Add image parts for each attachment
                    ...Object.entries(screenshotUrls).map(([screen, screenshotUrl]) => {
                        return {
                            type: "image",
                            mimeType: "image/png",
                            image: screenshotUrl
                        } as ImagePart
                    })
                ]
            } as DBMessage]
        });

        // Start with the default template files
        let baseFiles = [...DEFAULT_CODE];

        // Add the generated files from the design requirements
        // If a file with the same name exists, replace it; otherwise, add it
        if (designRequirements.generatedFiles && designRequirements.generatedFiles.length > 0) {
            const generatedFiles = designRequirements.generatedFiles.map(file => ({
                name: file.path,
                content: file.content,
                language: file.path.endsWith('.tsx') || file.path.endsWith('.jsx') ? 'jsx' :
                    file.path.endsWith('.ts') ? 'typescript' :
                        file.path.endsWith('.js') ? 'javascript' :
                            file.path.endsWith('.json') ? 'json' :
                                file.path.endsWith('.md') ? 'markdown' :
                                    'text'
            }));

            // Merge files, replacing any existing ones with the same name
            generatedFiles.forEach(newFile => {
                const existingIndex = baseFiles.findIndex(f => f.name === newFile.name);
                if (existingIndex >= 0) {
                    baseFiles[existingIndex] = newFile;
                } else {
                    baseFiles.push(newFile);
                }
            });
        }

        console.log('Final files to save:', baseFiles.map(f => f.name));

        // Save the merged files
        const savedFileState = await saveFileStateAndCacheIfNeeded({
            chatId: appChatId,
            projectId,
            dependencies: DEFAULT_DEPENDENCIES,
            files: baseFiles,
            messageId: savedMessage[0].id,
            shouldCache: true
        })

        await saveScreenShotState({
            messageId: savedMessage[0].id,
            projectId,
            chatId: appChatId,
            screenshots: screenshotMetadataArray
        })

        // 7. Return success response with screenshot paths and requirements
        return NextResponse.json({
            success: true,
            message: 'Designs approved successfully',
            chatId: savedChat.id
        });
    } catch (error) {
        console.error('Error approving designs:', error);
        return NextResponse.json(
            {error: 'Failed to approve designs'},
            {status: 500}
        );
    }
}

// Define a simple Zod schema for app file structure
const AppFileStructureSchema = z.object({
    // Core files that need to be generated
    files: z.array(z.object({
        path: z.string().describe('File path relative to the project root'),
        content: z.string().describe('The content of the file'),
    })),

    // User prompt for the next LLM
    userPrompt: z.string().describe(`
    Create a prompt for the next LLM that will use the business logic files you\'ve generated. 
    FOCUS ON: 
    (1) The overall idea with the exact flow of each screen. Make sure to document animations, transitions, flows, validation on forms (react-hook-forms), connecting store, bottom navigation, what is scrollable in each screen and what is not and any relevant details in technical requirements.
    (2) Explaining how the generated business logic files should be used and extended, 
    (3) What UI components need to be created based on these files, 
    (4) How the screens should connect to the business logic, and 
    (5) What additional functionality needs to be implemented. DO NOT create a generic app description - instead, focus specifically on connecting YOUR generated files with the next implementation steps. 
    Reference specific file names, functions, and data structures you\'ve created. 
    The next LLM will have access to your generated files but needs guidance on how to use them.
    `),
});

/**
 * Generates design requirements from HTML screens using AI
 *
 * This function:
 * 1. Takes the HTML screens and screenshot URLs
 * 2. Uses a smaller LLM to analyze the design and extract business logic
 * 3. Generates actual file contents and a user prompt for the next LLM
 */
async function generateDesignRequirements(screens: any[], screenshotUrls: Record<string, string>) {
    try {
        // Create a simplified representation of screens for the LLM
        const screenData = screens.map(screen => ({
            id: screen.id,
            name: screen.name,
            html: wrapScreenInCode(screen.html),
        }));

        // Extract design system hints from the HTML
        const colorHints = extractColorHints(screens);
        const layoutHints = extractLayoutHints(screens);
        const navigationHints = extractNavigationHints(screens);

        // Prepare the prompt data
        const promptData = {
            screens: screenData,
            colorHints,
            layoutHints,
            navigationHints
        };

        const images: ImagePart[] = Object.entries(screenshotUrls).map(([screen, screenshotUrl]) => {
            return {
                type: 'image',
                image: new URL(screenshotUrl),
            } as ImagePart
        });

        console.log('images', images)

        console.time('Generation');
        // Use generateText with Claude to analyze the design and generate detailed requirements
        const result = await generateText({
            model: customModel('anthropic/claude-sonnet-4'),
            temperature: 0,
            messages: [
                {
                    role: 'system',
                    content: `You are a specialized AI that analyzes mobile app designs and translates them into comprehensive, detailed requirements documentation.
          
          Your task is to analyze HTML screens and screenshots from a design phase and generate DETAILED REQUIREMENTS (not code) for the app.
          Today's date is ${dayjs().format("DD-MM-YYYY")} and the day of the week is ${dayjs().format("dddd")}
          
          [CRITICAL]: The requirements document you generate is EXTREMELY IMPORTANT. It will be passed to another LLM that will implement the actual code. Your requirements should be concise yet comprehensive.

          [CORE OBJECTIVE]: Create requirements that enable the implementation LLM to build an app that delivers VISIBLE VALUE with EVERY UPDATE. Users need to see their app evolving meaningfully with each iteration.
            - Ensure you guide on end to end functionality even accounting for missing/hidden features to make the app truly production grade
            - Any button/component on the screen must be fully functional with dummy data using the stores. No ghosted buttons or empty redirects
            - Read between the lines and add more screen/features needed to connect the functionality
            - Adjust requirements accordingly below to make sure the user gets a true production grade experience
            
       
          
          [IMPLEMENTATION PRINCIPLES]
          - PRIORITIZE USER VALUE: Each implementation update should deliver visible, functional progress
          - BUILD INCREMENTALLY: Start with core functionality and expand outward
          - MAINTAIN COHERENCE: Ensure each update builds logically on previous work
          - FOCUS ON COMPLETENESS: Each feature should be fully functional, not partially implemented
          - PRESERVE CONTEXT: Design decisions should be preserved throughout implementation
          - MODULAR ARCHITECTURE: Keep files under 300 lines by breaking into smaller modular components
          - PROGRESSIVE ENHANCEMENT: Implement features in a way that shows immediate visual progress
          - IMPLEMENTATION SEQUENCE: Follow the implementation order in the agent prompt (mocks → constants → components → store → screens → navigation → App.tsx)
          
          [IMPORTANT GUIDELINES]
          - DO NOT write any actual code - focus entirely on detailed specifications
          - Be specific about implementation details while allowing for creative solutions
          - Provide clear naming conventions and type structures
          - Detail user flows and interactions with a focus on user experience
          - Include styling guidance with specific colors and design elements
          - Explain how screens connect and interact with each other
          - Specify animations and transitions that enhance user experience
          - DO NOT include testing as it's not supported in the current environment
          - Balance detail with conciseness to optimize token usage
          
          The next LLM will use your requirements document as the basis for generating the actual code implementation, so it must be comprehensive and precise.
           
           [FINAL REMINDER]
           Remember that the implementation LLM has limited context (only sees the last 5 messages) and works with non-technical users. Your requirements should enable the implementation to show visible progress with EACH update, creating a satisfying experience where users can see their app evolving meaningfully with every interaction. Focus on enabling incremental, visible success rather than perfect theoretical architecture.
          
          [IMPORTANT]: Below is the system prompt available to the next LLM. Understand its constraints and generate requirements that work within it:
    // Build files in this exact order. You can only create React Native Web compatible code and nothing else. No backend, no server side code. Nothing else whatsoever.
    [DO NOT CREATE THE theme.ts file, instead single file for colors.ts]
    1. mocks/* - Add mock data
    2. constants/* - Add constants/color.ts and other constant files
    3. components/ui, components/* - Individual compoenents
    4. store/* - Implement state management with Zustand
    5. screens/* - Build screen components
     [Build HomeScreen last and make sure to add the correct navigations]
    6. navigation/* - Set up navigation
    7. App.tsx - Connect everything
    
    ----- [IMPORTANT]: DO NOT USE THE BELOW MENTIONED PROMPT to alter your response. Its the system prompt for another LLM that will take your output and generate the app on the basis of it.
          ${STREAMLINED_AGENT_PROMPT}
    ----- [IMPORTANT]: DO NOT USE THE ABOVE MENTIONED PROMPT to alter your response. Its the system prompt for another LLM that will take your output and generate the app on the basis of it.
          DO NOT OUTPUT YOUR <thinking>.
          `
                },
                {
                    role: 'system',
                    content: `
          For reference, these are the base files that will be available in the codebase:
          ${DEFAULT_CODE.map(code => {
                        return `-------------
File Name: ${code.name}\n
-------------`
                    })}
          
          You should reference these files in your requirements where relevant, but do not include their contents in your response.
          `
                },
                {
                    role: 'user',
                    content: [
                        {
                            type: 'text',
                            text: `Analyze these designs and create a COMPREHENSIVE REQUIREMENTS DOCUMENT for an Expo/React Native application.

Your requirements document must follow this exact structure and be extremely detailed in each section:

At the end of the message, please make sure to add "Please start building the entire app in sequence and make sure to look at REQUIREMENT.md (Do not overwrite it) and the other relevant markdown file operations to ensure you are on right track. Remember, the screenshots are only available for a few messages."
Remember: Be extremely specific and detailed in every section. Do not leave any details for interpretation. Your requirements document will be used by an AI code generator to implement the actual application, so it must be comprehensive and precise.

${JSON.stringify(promptData)}`
                        },
                        ...images
                    ]
                }
            ],
        });
        console.timeEnd('Generation');

        // await exportData(result.object, 'response-messages');

        return {
            userPrompt: result.text,
            generatedFiles: [
                {
                    path: 'REQUIREMENTS.md',
                    content: result.text
                }
            ]
        };
    } catch (error) {
        console.error('Error generating design requirements:', error);
        throw new Error('Failed to analyze design and generate requirements');
    }
}

/**
 * Extract color hints from HTML screens
 */
function extractColorHints(screens: any[]) {
    // Extract common colors from the screens
    const colorRegex = /#[0-9A-Fa-f]{3,8}|rgba?\([^)]+\)|hsla?\([^)]+\)/g;
    const colorMap = new Map<string, number>();

    screens.forEach(screen => {
        const matches = screen.html.match(colorRegex) || [];
        matches.forEach(color => {
            colorMap.set(color, (colorMap.get(color) || 0) + 1);
        });
    });

    // Sort colors by frequency
    return Array.from(colorMap.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([color]) => color);
}

/**
 * Extract layout hints from HTML screens
 */
function extractLayoutHints(screens: any[]) {
    // Look for common layout patterns
    const patterns = {
        hasScrollView: screens.some(screen => screen.html.includes('ScrollView') || screen.html.includes('overflow-y') || screen.html.includes('overflow: auto')),
        hasGrid: screens.some(screen => screen.html.includes('grid') || screen.html.includes('flex-wrap')),
        hasBottomNav: screens.some(screen => screen.html.includes('position: fixed') && screen.html.includes('bottom')),
        hasHeader: screens.some(screen => screen.html.includes('header') || (screen.html.includes('position: fixed') && screen.html.includes('top'))),
        hasCards: screens.some(screen => screen.html.includes('card') || screen.html.includes('shadow') || screen.html.includes('border-radius')),
        hasForm: screens.some(screen => screen.html.includes('input') || screen.html.includes('form') || screen.html.includes('button')),
    };

    return patterns;
}

/**
 * Extract navigation hints from HTML screens
 */
function extractNavigationHints(screens: any[]) {
    // Analyze screen names and content to infer navigation structure
    const screenNames = screens.map(screen => screen.name);

    const hasHomeScreen = screenNames.some(name =>
        name.toLowerCase().includes('home') ||
        name.toLowerCase().includes('main') ||
        name.toLowerCase().includes('dashboard')
    );

    const hasDetailScreens = screenNames.some(name =>
        name.toLowerCase().includes('detail') ||
        name.toLowerCase().includes('view') ||
        name.toLowerCase().includes('profile')
    );

    const hasTabPattern = screenNames.some(name =>
        name.toLowerCase().includes('tab') ||
        screenNames.filter(n => n.toLowerCase().includes('screen')).length >= 3
    );

    return {
        hasHomeScreen,
        hasDetailScreens,
        hasTabPattern,
        screenCount: screenNames.length,
        screenNames,
    };
}

// No placeholder functions needed - all analysis is now done by the AI
