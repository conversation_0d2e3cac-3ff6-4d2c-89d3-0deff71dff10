import {
    type Message,
    convertToCoreMessages,
    createDataStreamResponse,
    CoreMessage,
    TextStreamPart,
    CoreUserMessage,
    DataStreamWriter,
    createDataStream,
    LanguageModelUsage,
    tool, streamText, smoothStream, TextPart, CoreAssistantMessage, ImagePart, UserContent,
} from 'ai';
import {after, NextResponse} from "next/server";
import {StreamService} from "@/lib/services/stream-service";
import {MOFileParser} from "@/lib/parser/StreamParser";
import {MODiffParser} from "@/lib/parser/DiffParser";
import {customModel} from "@/lib/ai";
import {auth} from "@/app/(auth)/auth";
import {
    getChatById,
    saveMessages,
    updateChat,
    getMessagesByChatId,
    getProjectById,
    markChatAsInitialized,
    createStreamId,
    getStreamIdsByChatId
} from "@/lib/db/queries";
import {generateUUID, sanitizeResponseMessages} from "@/lib/utils";
import {updateCreditUsage} from "@/lib/subscription/credit-usage";
import {saveTokenConsumption} from "@/lib/db/token-consumption.queries";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";
import {isErrorFixingMessage} from "@/lib/utils/error-detection";
import {z} from 'zod';
import {PerformanceTracker, performanceTracker} from '@/lib/utils/PerformanceTracker';
import {
    DESIGN_PLANNER_SYSTEM_PROMPT
} from "@/lib/ai/prompt-directory/design-generator-prompt";
import {generateDesign} from "@/lib/chat/tools/generate-design.tool";
import {
    createResumableStreamContext,
    type ResumableStreamContext,
} from 'resumable-stream';
import {getSupabaseInstructions} from "@/lib/chat/tools";
import {getDesignScreensByChatId, saveDesignScreen, updateDesignScreen} from "@/lib/db/design-screens";
// HTML utils now imported in the generate-design tool
import dayjs from "dayjs";
import {differenceInSeconds} from 'date-fns';
import {fetchFromOpenRouter} from "@/lib/openrouter/get-message-details";
import {exportData} from "@/lib/server-utils";
import {checkMessageLimit} from "@/lib/subscription";

// Schema for request validation


const RequestSchema = z.object({
    messages: z.array(z.any()),
    chatId: z.string(),
    projectId: z.string(),
    isReload: z.boolean().optional()
});

// Create a resumable stream context for handling stream resumption
let globalStreamContext: ResumableStreamContext | null = null;

function getStreamContext() {
    if (!globalStreamContext) {
        try {
            globalStreamContext = createResumableStreamContext({
                waitUntil: after,
            });
        } catch (error: any) {
            if (error.message.includes('REDIS_URL')) {
                console.log(' > Resumable streams are disabled due to missing REDIS_URL');
            } else {
                console.error(error);
            }
        }
    }
    return globalStreamContext;
}

function getMostRecentUserMessage(messages: CoreMessage[]): CoreMessage | null {
    for (let i = messages.length - 1; i >= 0; i--) {
        if (messages[i].role === 'user') {
            return messages[i];
        }
    }
    return null;
}

function getMessagesWithCompleteTurns(messages: Array<Message>, minMessages: number = 10): Array<Message> {
    if (messages.length <= minMessages) {
        return messages;
    }

    const recentMessages = messages.slice(-minMessages);
    const remainingMessages = messages.slice(0, -minMessages);
    const oldestUserMessageIndex = remainingMessages.map(m => m.role).lastIndexOf('user');

    if (oldestUserMessageIndex === -1) {
        return recentMessages;
    }

    const additionalMessages = remainingMessages.slice(oldestUserMessageIndex);
    return [...additionalMessages, ...recentMessages];
}

function prepareUserMessage(userMessage: CoreMessage): CoreMessage {
    if (!Array.isArray(userMessage.content)) {
        return userMessage;
    }

    // Process array content
    const processedContent = userMessage.content.map(content => {
        if (content.type === "image") {
            return {
                type: 'image',
                mimeType: "image/png",
                image: content.image
            } as ImagePart;
        }
        return content;
    }) as UserContent;

    return {
        ...userMessage,
        content: processedContent
    } as CoreMessage;
}

export async function POST(request: Request, {params}: { params: Promise<{ projectId: string, chatId: string }> }) {
    const requestId = generateUUID();
    performanceTracker.startTimer(requestId, 'Design Chat API Request');

    try {
        const session = await auth();
        if (!session?.user) {
            performanceTracker.stopTimer(requestId, {status: 'error', error: 'Unauthorized'});
            return new Response('Unauthorized', {status: 401});
        }

        // Extract parameters from the URL
        const {projectId, chatId} = await params;

        // Validate project and chat existence and ownership
        const [project, chat] = await Promise.all([
            getProjectById({id: projectId}),
            getChatById({id: chatId})
        ]);

        if (!project || !chat) {
            performanceTracker.stopTimer(requestId, {status: 'error', error: 'Not Found'});
            return new Response('Project or chat not found', {status: 404});
        }

        if ((project.userId !== session.user.id || chat.userId !== session.user.id) && session?.user?.email !== "<EMAIL>") {
            performanceTracker.stopTimer(requestId, {status: 'error', error: 'Forbidden'});
            return new Response('Forbidden', {status: 403});
        }

        // Parse and validate the request body
        const body = await request.json();
        const validationResult = RequestSchema.safeParse(body);

        if (!validationResult.success) {
            performanceTracker.stopTimer(requestId, {status: 'error', error: 'Bad Request'});
            return new Response('Invalid request format', {status: 400});
        }

        const {messages, isReload} = validationResult.data;

        const messageCheck = await checkMessageLimit(session?.user.id!, false);
        const limitCheckTimerId = `${requestId}-limit-check`;
        performanceTracker.stopTimer(limitCheckTimerId);
        if (!messageCheck.canSendMessage) {
            return new Response(JSON.stringify({
                error: 'Message limit reached',
                limit: messageCheck.limit,
                remaining: messageCheck.remaining,
                isPro: messageCheck.isPro,
                isAnonymous: false
            }), {
                status: 429,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Initialize credit usage tracker
        const creditUsageTracker = new CreditUsageTracker();

        // Create a resumable stream for potential reconnections
        const streamId = generateUUID();
        await createStreamId({streamId: streamId, chatId});
        // Get the stream context for potential resumable stream

        // Mark the chat as initialized if it's not already
        if (!chat.isInitialized) {
            await markChatAsInitialized({id: chatId});
        }

        // Get existing messages for context
        // const existingMessages = await getMessagesByChatId({ id: chatId });
        const messagesWithTurns = getMessagesWithCompleteTurns(messages, 10);
        const coreMessages = convertToCoreMessages(messagesWithTurns);
        const userMessage = getMostRecentUserMessage(coreMessages);

        // Add the system prompt for design generation
        const systemMessage: CoreMessage = {
            role: 'system',
            content: DESIGN_PLANNER_SYSTEM_PROMPT + '\n Please do not generate screens yourself. Use the generateDesign tool.\n\nIMPORTANT: There is a total limit of 6 screens per project (not just per request). This includes screens created in previous sessions. If the user tries to create more screens than allowed, the tool will return an error with details about the current count. Please help the user prioritize which screens are most important or suggest removing existing screens if they need to create new ones.'
        };

        const userMessageId = generateUUID();
        if (!isReload && userMessage) {
            await saveMessages({
                messages: [{
                    ...prepareUserMessage(userMessage),
                    id: userMessageId,
                    createdAt: new Date(),
                    chatId: chatId,
                    userId: session?.user?.id,
                    componentContexts: [],
                    autoFixed: false,
                    hidden: false,
                    version: 1
                } as any],
            });
        }


        const existingScreens = await getDesignScreensByChatId({chatId});

        const messageId = generateUUID();
        let accumulatedContent = '';
        const stream = createDataStream({
            execute: async (dataStream) => {
                dataStream.writeData({
                    type: 'user-message-id',
                    content: userMessageId,
                });

                const streamService = new StreamService({
                    isFirstMessage: true,
                    isSupabaseConnected: true,
                    agentModeEnabled: false,
                    transformChunking: "word",
                    dataStream,
                    diffParser: new MODiffParser(),
                    enableValidation: false,
                    generateMessageId: generateUUID,
                    isConnectingSupabase: false,
                    parser: new MOFileParser(),
                    userMessageId,
                    modelId: 'anthropic/claude-sonnet-4',
                    maxSteps: 2,
                    enabledTools: ['generateDesign'],
                    temperature: 0.7,
                    messages: [
                        systemMessage,
                        ...messages
                    ],
                    // experimental_transform: [
                    //     smoothStream({chunking: 'word'}),
                    //     ({ tools, stopStream }) => {
                    //         console.log('tools', tools)
                    //         return new TransformStream({
                    //             transform(chunk, controller) {
                    //                 // Example: Only pass through text chunks, filter out other types
                    //                 console.log('chunk.type', chunk.type)
                    //                 if (chunk.type === 'text-delta') {
                    //                     controller.enqueue(chunk);
                    //                 }
                    //             }
                    //         });
                    //     }
                    // ],
                    abortSignal: request.signal,
                    tools: {
                        generateDesign: generateDesign({
                            dataStream,
                            chatId,
                            projectId,
                            existingScreens,
                            maxParallelScreens: 4,
                            maxTotalScreens: process.env.MAX_DESIGN_SCREENS ? parseInt(process.env.MAX_DESIGN_SCREENS) : 12 // Allow configuration via environment variable
                        }),
                    },
                    // Handle chunks to accumulate content
                    onFinish: async ({response, finishReason}) => {
                        // console.log('response', JSON.stringify(response.messages, null, 2))
                        // Store the accumulated content from the response

                        const responseMessagesWithoutIncompleteToolCalls =
                            sanitizeResponseMessages(response.messages);
                        const finalMessages = responseMessagesWithoutIncompleteToolCalls.map(
                            (message, index) => {
                                const typedMessage = (message as CoreAssistantMessage & { id: string });
                                return {
                                    id: typedMessage.id,
                                    chatId: chatId,
                                    role: message.role,
                                    content: message.content,
                                    userId: session?.user.id,
                                    projectId: chat.projectId,
                                    remoteProviderId: response.id,
                                    remoteProvider: "open-router",
                                    componentContexts: [],
                                    autoFixed: false,
                                    hidden: false,
                                    finishReason: finishReason,
                                    parentUserMessageId: userMessageId || null,
                                    isAssistantGroupHead: false,
                                    parentAssistantMessageId: null,
                                    // Hack to ensure the messages are written in sequence
                                    createdAt: dayjs().add(index, "second").toDate(),
                                };
                            },
                        );
                        await saveMessages({
                            messages: finalMessages as any
                        });

                        // Update chat status to indicate design is complete
                        await updateChat({
                            id: chatId,
                            updatedAt: new Date(),
                            designStatus: 'complete',
                            designHtml: accumulatedContent // Store the full HTML response
                        });

                        // Track token usage for billing
                        if (session?.user?.id) {
                            // Get usage data from the stream service result
                            const usage = await result.usage;

                            await saveTokenConsumption({
                                userId: session.user.id,
                                projectId,
                                chatId,
                                messageId,
                                promptTokens: usage?.promptTokens || 0,
                                completionTokens: usage?.completionTokens || 0,
                                totalTokens: usage?.totalTokens || 0,
                                model: response.modelId,
                                totalTimeToken: 0,
                                isAnonymous: false,
                                remoteProviderId: "",
                                remoteProvider: "anthropic",
                                creditsConsumed: 1
                            });

                            // Log token usage for debugging
                            console.log(`Design generation for chat ${chatId} completed. Token usage:`, {
                                promptTokens: usage?.promptTokens || 0,
                                completionTokens: usage?.completionTokens || 0,
                                totalTokens: usage?.totalTokens || 0
                            });

                            // Update credit usage - now using message-based tracking as per memory 7d4ca384
                            await updateCreditUsage(session.user.id, []);
                        }

                        dataStream.writeData({
                            type: 'ai-message-id',
                            content: (messages[0] as Message).id,
                        });

                        performanceTracker.stopTimer(requestId, {status: 'success'});
                        console.log(streamService.getToolCallCostReport());
                    }
                })

                const result = streamService.startStream();
                result.mergeIntoDataStream(dataStream, {sendUsage: false, sendReasoning: false});
            }
        })
        const streamContext = getStreamContext();
        // Return the stream response with resumable stream support
        if (streamContext) {
            return new Response(
                // Update the stream with the final content
                await streamContext.resumableStream(streamId, () => stream)
            );
        } else {
            return new Response(stream);
        }
    } catch (error: any) {
        console.error('Error in design chat API:', error);
        performanceTracker.stopTimer(requestId, {status: 'error', error: error.message});
        return new Response(error.message || 'An error occurred', {status: 500});
    }
}

export async function GET(request: Request, {params}: { params: { projectId: string, chatId: string } }) {
    const streamContext = getStreamContext();
    const resumeRequestedAt = new Date();

    if (!streamContext) {
        return new Response(null, {status: 204});
    }

    const {chatId} = params;
    const session = await auth();

    if (!session?.user) {
        return new Response('Unauthorized', {status: 401});
    }

    try {
        const chat = await getChatById({id: chatId});

        if (!chat) {
            return new Response('Chat not found', {status: 404});
        }

        if (chat.userId !== session.user.id) {
            return new Response('Forbidden', {status: 403});
        }

        // Get the most recent stream ID for this chat
        const streamIds = await getStreamIdsByChatId({chatId});

        if (!streamIds.length) {
            return new Response('No stream found', {status: 404});
        }

        const recentStreamId = streamIds[streamIds.length - 1];

        // Create an empty data stream as fallback
        const emptyDataStream = createDataStream({
            execute: () => {
            },
        });

        // Try to resume the stream
        const stream = streamContext ?
            await streamContext.resumableStream(
                recentStreamId,
                () => emptyDataStream
            ) : null;


        if (!stream) {
            const messages = await getMessagesByChatId({id: chatId});
            const mostRecentMessage = messages.at(-1);

            if (!mostRecentMessage) {
                return new Response(emptyDataStream, {status: 200});
            }

            if (mostRecentMessage.role !== 'assistant') {
                return new Response(emptyDataStream, {status: 200});
            }

            const messageCreatedAt = new Date(mostRecentMessage.createdAt);

            if (differenceInSeconds(resumeRequestedAt, messageCreatedAt) > 15) {
                return new Response(emptyDataStream, {status: 200});
            }

            const restoredStream = createDataStream({
                execute: (buffer) => {
                    buffer.writeData({
                        type: 'append-message',
                        message: JSON.stringify(mostRecentMessage),
                    });
                },
            });

            return new Response(restoredStream, {status: 200});
        }

        return new Response(stream, {status: 200});
    } catch (error: any) {
        console.error('Error in GET design chat:', error);
        return new Response(error.message || 'An error occurred', {status: 500});
    }
}

// Note: We're using the imported getStreamIdsByChatId function from queries.ts