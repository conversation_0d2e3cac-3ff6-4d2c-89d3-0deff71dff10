import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getDesignScreensByChatId } from '@/lib/db/design-screens';
import { getChatById } from '@/lib/db/queries';

export async function GET(
  req: Request,
  { params }: { params: Promise<{ projectId: string; chatId: string }> }
) {
  try {
    const { projectId, chatId } = await params;
    const session = await auth();

    if (!session?.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Verify chat ownership
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.userId !== session.user.id) {
      return new Response('Forbidden', { status: 403 });
    }

    // Get all screens for this chat
    const screens = await getDesignScreensByChatId({ chatId });
    
    // Return the screens as JSON
    return NextResponse.json({
      screens: screens.map(screen => ({
        id: screen.id,
        name: screen.name,
        status: screen.status,
        order: screen.order,
        createdAt: screen.createdAt
      }))
    });
  } catch (error: any) {
    console.error('Error fetching design screens:', error);
    return new Response(error.message || 'An error occurred', { status: 500 });
  }
}
