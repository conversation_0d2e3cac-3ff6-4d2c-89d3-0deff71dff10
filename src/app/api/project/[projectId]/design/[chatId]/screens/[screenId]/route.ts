import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getDesignScreenById, deleteDesignScreen, getDesignScreensByChatId, updateDesignScreen } from '@/lib/db/design-screens';
import { getChatById } from '@/lib/db/queries';
import {extractHtmlContent, wrapScreenInCode} from '@/lib/utils/html-utils';

export async function GET(
  req: Request,
  { params }: { params: Promise<{ projectId: string; chatId: string; screenId: string }> }
) {
  try {
    const {projectId, chatId, screenId} = await params;
    const session = await auth();

    if (!session?.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Verify chat ownership
    const chat = await getChatById({ id: chatId });
    if (!chat || (chat.userId !== session.user.id && session?.user?.email !== "<EMAIL>")) {
      return new Response('Forbidden', { status: 403 });
    }

    // Get the screen
    const screen = await getDesignScreenById({ id: screenId });
    if (!screen || screen.chatId !== chatId || screen.projectId !== projectId) {
      return new Response('Screen not found', { status: 404 });
    }

    // Extract the HTML content and wrap it in the proper HTML structure
    const extractedHtml = screen.html || '';
    
    const fullHtml = wrapScreenInCode(extractedHtml);

    // Return the HTML content with appropriate headers
    return new Response(fullHtml, {
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'max-age=60',
      },
    });
  } catch (error: any) {
    console.error('Error serving design screen:', error);
    return new Response(error.message || 'An error occurred', { status: 500 });
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ projectId: string; chatId: string; screenId: string }> }
) {
  try {
    const {projectId, chatId, screenId} = await params;
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify chat ownership
    const chat = await getChatById({ id: chatId });
    if (!chat || chat.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the screen to verify it exists and belongs to this chat/project
    const screen = await getDesignScreenById({ id: screenId });
    if (!screen || screen.chatId !== chatId || screen.projectId !== projectId) {
      return NextResponse.json({ error: 'Screen not found' }, { status: 404 });
    }

    // Delete the screen
    await deleteDesignScreen({ id: screenId });
    
    // Get remaining screens to update their order if needed
    const remainingScreens = await getDesignScreensByChatId({ chatId });
    
    // Update the order of remaining screens to ensure continuity
    if (remainingScreens.length > 0) {
      // Sort by current order
      remainingScreens.sort((a, b) => a.order - b.order);
      
      // Update orders to be sequential
      for (let i = 0; i < remainingScreens.length; i++) {
        if (remainingScreens[i].order !== i) {
          await updateDesignScreen({
            id: remainingScreens[i].id,
            order: i
          });
        }
      }
    }

    return NextResponse.json({ success: true, message: 'Screen deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting design screen:', error);
    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 });
  }
}
