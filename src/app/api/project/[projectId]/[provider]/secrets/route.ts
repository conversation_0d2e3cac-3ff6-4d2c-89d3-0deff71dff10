import {NextRequest, NextResponse} from 'next/server';
import {SupabaseIntegrationProvider} from '@/lib/integrations/supabase/SupabaseIntegrationProvider';
import {getUserIntegrationByProvider} from '@/lib/integrations/queries';
import {auth} from "@/app/(auth)/auth";
import {getProjectById} from "@/lib/db/queries";

// Initialize providers
const providers = {
    supabase: new SupabaseIntegrationProvider(),
};

export async function POST(
    req: NextRequest,
    {params}: { params: Promise<{ provider: keyof typeof providers, projectId: string }> },
) {
    try {
        const paramsAwaited = await params;
        const providerKey = paramsAwaited.provider;
        const projectId = paramsAwaited.projectId;
        const session = await auth();

        if (!session?.user) {
            return new NextResponse('Unauthorized', {status: 401});
        }

        const project = await getProjectById({id: projectId});

        if (!project) {
            return new Response('Project not found', {status: 404});
        }

        if (
            project.userId !== session.user.id &&
            session?.user?.email !== "<EMAIL>") {
            return new Response('Unauthorized', {status: 401});
        }

        if (!project.supabaseProjectId) {
            return new Response('Supabase project not found. Please reconnect supabase.', {status: 404});
        }

        const connection = await getUserIntegrationByProvider({
            userId: session.user.id,
            provider: 'supabase',
        });

        if (!connection) {
            return new NextResponse('Connection not found', {status: 404});
        }

        const provider = providers[providerKey];
        if (!provider) {
            return new NextResponse('Invalid provider', {status: 400});
        }

        // Parse request body
        const body = await req.json();
        
        // Define the secret type
        interface SecretInput {
            name: string;
            value: string;
            scope?: string;
            environment?: string;
        }
        
        // Handle both single secret and multiple secrets formats
        let secretsArray: SecretInput[] = [];
        
        // Check if the request contains a secrets array
        if (body.secrets && Array.isArray(body.secrets)) {
            // Multiple secrets format: { secrets: [{name: 'key1', value: 'val1'}, {name: 'key2', value: 'val2'}] }
            secretsArray = body.secrets;
            
            // Validate each secret has name and value
            if (secretsArray.some((secret: SecretInput) => !secret.name || !secret.value)) {
                return new NextResponse('Each secret must have name and value fields', {status: 400});
            }
        } else {
            // Single secret format: { name: 'key', value: 'val' }
            const { name, value } = body;
            const scope = body.scope || 'project';
            const environment = body.environment || 'all';
            
            if (!name || !value) {
                return new NextResponse('Missing required fields: name, value', {status: 400});
            }
            
            secretsArray = [{ name, value, scope, environment }];
        }
        
        // Format the secrets as expected by the Supabase API
        // The Supabase API expects an array of objects with name and value properties
        const secrets = secretsArray.map(({ name, value }: SecretInput) => ({
            name,
            value
        }));

        // Create the secret
        await provider.createSecrets({
            connectionId: connection.id,
            projectRef: project.supabaseProjectId as string,
            secrets
        });

        return NextResponse.json({
            success: true, 
            count: secrets.length,
            names: secrets.map((s: { name: string; value: string }) => s.name)
        });
    } catch (error) {
        console.error('Failed to create secret:', error);
        return new NextResponse(
            `Failed to create secret: ${error instanceof Error ? error.message : 'Unknown error'}`,
            {status: 500}
        );
    }
}
