import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { 
  getProjectById, 
  saveProject, 
  saveChat, 
  saveMessages, 
  getMessagesByChatId,
  getChatsByProjectId,
  getFileStatesByProject,
  saveFileState
} from '@/lib/db/queries';
import { randomUUID } from 'crypto';
import { Message } from '@/lib/db/schema';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

/**
 * POST handler for forking a project
 * 
 * Supports two modes:
 * 1. Complete fork - Clones the project with all its chats, messages, and file states
 * 2. Fresh fork - Clones the project with a new empty chat and a single file state
 * 
 * Request body:
 * {
 *   mode: 'complete' | 'fresh',  // Fork mode
 *   name?: string                // Optional new name for the forked project
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Authenticate user
    const session = await auth();
    
    const anonymousId = request.headers.get('x-anonymous-id');
    const userId = session?.user?.id || anonymousId;
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { projectId } = await params;
    
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }
    const body = await request.json();
    const { mode = 'complete', name } = body;
    
    // Validate mode
    if (mode !== 'complete' && mode !== 'fresh') {
      return NextResponse.json(
        { error: 'Invalid mode. Must be "complete" or "fresh"' },
        { status: 400 }
      );
    }
    
    // Get the source project
    const sourceProject = await getProjectById({ id: projectId });
    
    if (!sourceProject) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Generate new project ID
    const newProjectId = randomUUID();
    
    // Create forked project with new ID but same details
    const forkedProject = await saveProject({
      id: newProjectId,
      userId: userId,
      appName: name || `${sourceProject.appName || 'Untitled'} (Fork)`,
      slug: `${sourceProject.slug || 'app'}-fork-${Date.now().toString().slice(-6)}`,
      scheme: sourceProject.scheme,
      bundleIdentifier: sourceProject.bundleIdentifier,
      packageName: sourceProject.packageName,
      icon: sourceProject.icon,
      splashImage: sourceProject.splashImage,
      primaryColor: sourceProject.primaryColor,
      description: sourceProject.description,
      privacyPolicyUrl: sourceProject.privacyPolicyUrl,
      termsOfServiceUrl: sourceProject.termsOfServiceUrl,
      visibility: sourceProject.visibility,
      prompt: sourceProject.prompt,
      initialUXGuidelines: sourceProject.initialUXGuidelines,
      knowledgeBase: sourceProject.knowledgeBase,
      aiGeneratedMemory: sourceProject.aiGeneratedMemory,
      // Don't copy design-related fields
      // Don't copy Supabase fields
    });

    if (mode === 'complete') {
      // Get all chats from the source project
      const sourceChats = await getChatsByProjectId({ id: projectId });
      
      // For each chat, create a new chat and copy messages
      for (const sourceChat of sourceChats) {
        const newChatId = randomUUID();
        
        // Create new chat
        await saveChat({
          id: newChatId,
          userId: userId,
          projectId: newProjectId,
          title: sourceChat.title,
          updatedAt: new Date(),
          isInitialized: !!sourceChat.isInitialized,
          type: sourceChat.type
        });
        
        // Get messages from source chat
        const sourceMessages = await getMessagesByChatId({ id: sourceChat.id });
        
        // Create new messages with new IDs but same content
        if (sourceMessages.length > 0) {
          // Create new messages with required fields to match the schema
          const newMessages = sourceMessages.map(msg => ({
            id: randomUUID(),
            chatId: newChatId,
            projectId: newProjectId,
            role: msg.role,
            content: msg.content,
            userId: userId,
            createdAt: new Date(),
            remoteProvider: msg.remoteProvider,
            remoteProviderId: msg.remoteProviderId,
            componentContexts: msg.componentContexts,
            autoFixed: msg.autoFixed || false,
            hidden: msg.hidden || false
          }));
          
          await saveMessages({ messages: newMessages });
        }
        
        // Get file states from source project
        const sourceFileStates = await getFileStatesByProject({ id: projectId });
        
        // Copy file states to new project
        for (const sourceFileState of sourceFileStates) {
          // Only copy file states associated with this chat
          if (sourceFileState.chatId === sourceChat.id) {
            await saveFileState({
              chatId: newChatId,
              projectId: newProjectId,
              messageId: sourceFileState.messageId || randomUUID(), // Generate new ID if null
              files: sourceFileState.files,
              dependencies: sourceFileState.dependencies || {}
            });
          }
        }
      }
    } else {
      // Fresh mode - create a new empty chat and a single file state
      const newChatId = randomUUID();
      const newMessageId = randomUUID();
      
      // Create new chat
      await saveChat({
        id: newChatId,
        userId: userId,
        projectId: newProjectId,
        title: 'Main Chat',
        updatedAt: new Date(),
        isInitialized: true,
        type: 'app'
      });
      
      // Get the latest file state from source project to use as a template
      const sourceFileStates = await getFileStatesByProject({ id: projectId });
      
      if (sourceFileStates.length > 0) {
        // Use the most recent file state as a template
        const latestFileState = sourceFileStates[0];
        
        // Create a new file state with the same files but new IDs
        await saveFileState({
          chatId: newChatId,
          projectId: newProjectId,
          messageId: newMessageId,
          files: latestFileState.files || {},
          dependencies: latestFileState.dependencies || {}
        });
      } else {
        // If no file states exist, create an empty one
        await saveFileState({
          chatId: newChatId,
          projectId: newProjectId,
          messageId: newMessageId,
          files: {},
          dependencies: {}
        });
      }
    }
    
    return NextResponse.json({ 
      success: true,
      projectId: newProjectId,
      mode
    });
  } catch (error) {
    console.error('Error forking project:', error);
    return NextResponse.json(
      { error: 'Failed to fork project' },
      { status: 500 }
    );
  }
}
