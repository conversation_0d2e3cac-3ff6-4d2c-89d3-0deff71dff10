import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { auth } from "@/app/(auth)/auth";
import { deployments } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { deploymentId, status, url, error } = await request.json();
    
    if (!deploymentId || !status) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    // Prepare update data
    const updateData: any = {
      status,
      updatedAt: new Date()
    };
    
    if (url) updateData.url = url;
    if (error) updateData.metadata = { ...updateData.metadata, error };
    
    // Update the deployment record
    const result = await db.update(deployments)
      .set(updateData)
      .where(eq(deployments.id, deploymentId))
      .returning();
    
    if (!result.length) {
      return NextResponse.json(
        { error: 'Deployment not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      deployment: result[0]
    });
  } catch (error) {
    console.error('Error updating deployment:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update deployment' },
      { status: 500 }
    );
  }
}
