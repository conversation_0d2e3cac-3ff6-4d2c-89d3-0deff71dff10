import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import {apkBuilds, deployments} from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { updateApkBuildStatus, updateDeploymentStatus } from '@/lib/db/deployment-queries';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// API endpoint for build service
const BUILD_SERVICE_URL = process.env.BUILD_SERVICE_URL || 'https://apk-builder.fly.dev';

export async function GET(request: NextRequest) {
  try {
    // Get the buildId from the query parameters
    const { searchParams } = new URL(request.url);
    const buildId = searchParams.get('buildId');
    
    if (!buildId) {
      return NextResponse.json(
        { error: 'Missing buildId parameter' },
        { status: 400 }
      );
    }
    
    // First check if we have this build in our database
    const buildRecords = await db.select().from(deployments).where(eq(deployments.buildId, buildId));
    const buildRecord = buildRecords[0];
    
    if (!buildRecord) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      );
    }
    
    // If the build is already in a final state, return the stored status
    if (buildRecord.status === 'completed' || buildRecord.status === 'failed') {
      return NextResponse.json({
        id: buildRecord.id,
        status: buildRecord.status,
        url: buildRecord.url,
        error: buildRecord.metadata?.error || 'Unknown error',
        metadata: buildRecord.metadata
      });
    }
    
    // Check build status from the build service
    const response = await fetch(`${BUILD_SERVICE_URL}/build/${buildId}`);
    
    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to retrieve build status' },
        { status: response.status }
      );
    }
    
    // Get the build status data
    const buildStatus = await response.json();
    
    // // Update our database with the latest status
    // if (buildStatus.status !== buildRecord.status ||
    //     buildStatus.apkUrl !== buildRecord.apkUrl) {
    //
    //   await updateApkBuildStatus(
    //     db,
    //     buildId,
    //     buildStatus.status,
    //     buildStatus.apkUrl,
    //     buildStatus.error,
    //     buildStatus.metadata
    //   );
    //
    //   // If there's a deployment associated with this build, update it too
    //   if (buildRecord.metadata && buildRecord.metadata.deploymentId) {
    //     const deploymentId = buildRecord.metadata.deploymentId;
    //     await updateDeploymentStatus(
    //       db,
    //       deploymentId,
    //       buildStatus.status === 'completed' ? 'completed' : buildStatus.status,
    //       buildStatus.apkUrl
    //     );
    //   }
    // }
    
    return NextResponse.json(buildStatus);
  } catch (error) {
    console.error('Error checking build status:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to check build status' },
      { status: 500 }
    );
  }
}
