import {NextRequest, NextResponse} from 'next/server';
import {db} from '@/lib/db';
import {auth} from "@/app/(auth)/auth";
import {getDeploymentsByProjectId} from '@/lib/db/deployment-queries';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest,
                          {params}: { params: Promise<{ projectId: string }> }
) {
    try {
        const {projectId} = await params;

        if (!projectId) {
            return NextResponse.json(
                {error: 'Missing projectId parameter'},
                {status: 400}
            );
        }
        // Check authentication
        const session = await auth();
        if (!session?.user?.id) {
            return new Response('Unauthorized', {status: 401});
        }

        // Get all deployments for the chat
        const deployments = await getDeploymentsByProjectId(db, projectId);

        return NextResponse.json({
            deployments
        });
    } catch (error) {
        console.error('Error retrieving deployments:', error);
        return NextResponse.json(
            {error: error instanceof Error ? error.message : 'Failed to retrieve deployments'},
            {status: 500}
        );
    }
}
