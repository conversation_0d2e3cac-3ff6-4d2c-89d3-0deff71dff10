import { NextRequest, NextResponse } from 'next/server';
import {getChatById, getLatestFileState, getLatestFileStateByProject, getProjectById} from "@/lib/db/queries";
import { auth } from "@/app/(auth)/auth";
import { prepareProject } from "@/lib/terminal/helpers";
import { db } from "@/lib/db";
import {deployments, Project} from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { createDeployment, createApkBuild, getLatestVersion, incrementVersion, getLatestVersionCode } from "@/lib/db/deployment-queries";
import {slugify} from "@/lib/utils";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// API endpoint for build service
const BUILD_SERVICE_URL = process.env.BUILD_SERVICE_URL || 'https://apk-builder.fly.dev';

export async function POST(
    request: NextRequest,
    { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const {projectId} = await params;
    const { platform } = await request.json();
    
    if (!platform) {
      return NextResponse.json(
        { error: 'Missing required parameter: platform' },
        { status: 400 }
      );
    }

    // Auto-increment version if not provided or use the requested version
    const latestVersion = await getLatestVersion(db, projectId);
    let version = incrementVersion(latestVersion, 'patch');
    
    // Get the latest versionCode and increment it
    const latestVersionCode = await getLatestVersionCode(db, projectId);
    const versionCode = latestVersionCode + 1;
    
    // Get chat details
    const project: Project = await getProjectById({ id: projectId });
    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }
    
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }
    if (project.userId !== session?.user?.id && session?.user?.email !== "<EMAIL>") {
      return new Response('Unauthorized', { status: 401 });
    }
    
    // Get latest file state
    const fileState = await getLatestFileStateByProject(projectId);
    if (!fileState) {
      return NextResponse.json(
        { error: 'No file state found for this project' },
        { status: 404 }
      );
    }

    const baseSlug = slugify(project.slug || project.bundleIdentifier || projectId);
    const timestamp = Date.now().toString().slice(-8); // Use last 6 digits of timestamp for uniqueness
    const uniqueSlug = `${baseSlug}-${timestamp}`;

    // Create a deployment record in the database
    const deploymentData = {
      projectId,
      userId: session.user.id,
      platform: platform as 'web' | 'android' | 'ios',
      version,
      versionCode,
      fileStateId: fileState.id,
      status: 'queued' as const,
      slug: uniqueSlug
    };

    if(platform === 'web' || platform === 'android') {
      const [deploymentRecord] = await createDeployment(db, deploymentData);

      // // Prepare project files
      // const filesMap = await prepareProject(fileState, {
      //   projectId: projectId,
      //   title: project.appName as string,
      //   packageName: project.packageName || 'life.magically.project',
      //   bundleIdentifier: project.bundleIdentifier || 'life.magically.project',
      //   slug: project.slug || project.appName || 'magically',
      //   scheme: project.scheme|| project.appName || 'magically',
      //   versionCode, // Use the auto-incremented versionCode
      //   versionName: version // Use the semantic version
      // });

      // // Convert filesMap to the format expected by the build service
      // const projectFiles: Record<string, Uint8Array<ArrayBufferLike>> = {};
      // for (const [path, content] of Object.entries(filesMap)) {
      //   // Convert Buffer to string if needed
      //   console.log('encode(content)', encode(content))
      //   projectFiles[path] = encode(content);
      // }

      const buildId = crypto.randomUUID();

      console.log('deploymentRecord.id', deploymentRecord.id)
      // Queue an Android APK build using the JSON endpoint
      const buildResponse = await fetch(`${BUILD_SERVICE_URL}/build/json`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // projectFiles,
          buildId,
          projectId,
          deploymentId: deploymentRecord.id,
          platform: platform,
          fileStateId: fileState.id,
          buildOptions: {
            platform: platform,
            method: 'fast',
            dev: false,
            useHermes: true,
            packageName: project.packageName,
            appName: project.appName,
            scheme: project.scheme,
            versionName:version,
            versionCode,
            bundleIdentifier: project.bundleIdentifier,
            slug: project.slug
          },
          metadata: {
            buildId,
            deploymentId: deploymentRecord.id,
            version
          }
        }),
      });

      if (!buildResponse.ok) {
        const errorData = await buildResponse.json();
        // Update the deployment status to failed
        await db.update(deployments)
            .set({ status: 'failed' })
            .where(eq(deployments.id, deploymentRecord.id));
        throw new Error(errorData.error || 'Android build failed');
      }

      const buildData = await buildResponse.json();

      return NextResponse.json({
        success: true,
        buildId,
        dbRecordId: deploymentRecord.id,
        status: 'queued',
        fileStateId: fileState.id,
        platform,
        message: `${platform} build queued`
      });
    } else {
      // iOS deployment is coming soon
      return NextResponse.json({
        success: true,
        status: 'coming_soon',
        dbRecordId: 'coming_soon',
        fileStateId: fileState.id
      });
    }
  } catch (error) {
    console.error('Deployment error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to process deployment request' },
      { status: 500 }
    );
  }
}

// Helper function to poll build status
async function pollBuildStatus(buildId: string, maxAttempts = 10, interval = 3000) {
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    const response = await fetch(`${BUILD_SERVICE_URL}/build/${buildId}`);
    
    if (!response.ok) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, interval));
      continue;
    }
    
    const data = await response.json();
    
    if (data.status === 'completed' || data.status === 'failed') {
      return data;
    }
    
    attempts++;
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error('Build timed out');
}
