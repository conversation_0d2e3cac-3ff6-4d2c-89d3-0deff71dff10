import {NextRequest, NextResponse} from 'next/server';
import { checkMessageLimit, checkSubscriptionStatus } from '@/lib/subscription';
import { auth } from "@/app/(auth)/auth";
import { getPlanByTier } from '@/lib/subscription/plans';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    // Get anonymous ID from header
    const anonymousId = request.headers.get('x-anonymous-id');

    // Determine user ID - either authenticated or anonymous
    const userId = session?.user.id || anonymousId;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check message limits and subscription status
    const messageCheck = await checkMessageLimit(userId, !session?.user.id);
    const subscriptionStatus = await checkSubscriptionStatus(userId, !session?.user.id);
    const plan = getPlanByTier(subscriptionStatus.plan);

    return NextResponse.json({
      // Basic status
      subscriptionId: subscriptionStatus.subscriptionId,
      isActive: subscriptionStatus.isActive,
      isPro: messageCheck.isPro,
      isAnonymous: messageCheck.isAnonymous,
      planTier: subscriptionStatus.plan,
      planName: plan.name,
      status: subscriptionStatus.status,

      // Credit information
      credits: {
        total: subscriptionStatus.credits,
        used: subscriptionStatus.creditsUsed,
        remaining: subscriptionStatus.creditsRemaining,
      },

      // Daily credit limits
      dailyLimit: messageCheck.dailyLimit,
      dailyRemaining: messageCheck.dailyRemaining,

      // Message limits
      // messageLimit: messageCheck.messageLimit || -1,
      // messageRemaining: messageCheck.messageRemaining || -1,
      // dailyMessageLimit: messageCheck.dailyMessageLimit || -1,
      // dailyMessageRemaining: messageCheck.dailyMessageRemaining || -1,

      // Legacy fields for backward compatibility
      messageLimit: messageCheck.limit,
      messagesRemaining: messageCheck.remaining,

      // Features
      features: plan.features,
      allowedFeatures: plan.allowedFeatures || [],
    });
  } catch (error) {
    console.error('Subscription status error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription status' },
      { status: 500 }
    );
  }
}
