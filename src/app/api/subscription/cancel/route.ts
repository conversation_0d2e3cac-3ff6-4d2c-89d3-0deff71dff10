import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getSubscriptionById, updateSubscriptionOnCancel } from '@/lib/db/queries';
import { cancelSubscription, getSubscription } from '@/lib/lemonsqueezy';

// Define the shape of the feedback data
interface CancellationFeedback {
  signupGoal: string;
  stopReason: string;
  recommendScore: number;
  recommendReason?: string;
  requestedFeedbackCall?: boolean; // Added to match client
}

// Define the request body shape
interface CancelSubscriptionRequest {
  subscriptionId: string;
  feedback: CancellationFeedback;
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the request body
    const body = await request.json() as CancelSubscriptionRequest;
    const { subscriptionId, feedback } = body;

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Get the user's subscription from the database
    const userSubscription = await getSubscriptionById(subscriptionId);

    if (!userSubscription) {
      return NextResponse.json({ error: 'Subscription not found' }, { status: 404 });
    }

    // Verify the subscription belongs to the current user
    if (userSubscription.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    if(!userSubscription.lemonSqueezySubscriptionId) {
      return NextResponse.json({ error: 'Subscription in payment provider not found. Please contact the support team.' }, { status: 404 });
    }

    const updatedLsSubscription = await getSubscription(userSubscription.lemonSqueezySubscriptionId);

    // Cancel the subscription with LemonSqueezy
    const lemonSqueezyCancelResponse = await cancelSubscription({
      subscriptionId: userSubscription.lemonSqueezySubscriptionId,
      productId: updatedLsSubscription.data.attributes.product_id,
      variantId: updatedLsSubscription.data.attributes.variant_id
    });

    if (!lemonSqueezyCancelResponse || lemonSqueezyCancelResponse.errors) {
      console.error('LemonSqueezy cancellation failed:', lemonSqueezyCancelResponse?.errors);
      throw new Error('Failed to cancel subscription with payment provider.');
    }

    // Get the updated subscription details from LemonSqueezy to get the accurate ends_at date
    const endsAtString = updatedLsSubscription?.data?.attributes?.ends_at;
    const endsAtDate = endsAtString ? new Date(endsAtString) : null;
    const isStillActive = endsAtDate ? endsAtDate > new Date() : false;

    // Update the subscription in the database
    const newMetadata = {
      cancellationFeedback: feedback,
      cancellationDate: new Date().toISOString(),
      lemonsqueezyEndsAt: endsAtString, // Store the original string from LS
    };
    await updateSubscriptionOnCancel(subscriptionId, isStillActive, newMetadata, userSubscription.metadata);

    return NextResponse.json({
      success: true,
      message: 'Subscription cancelled successfully.',
      endsAt: endsAtString, // Return the string representation for client
      isActive: isStillActive
    });
  } catch (error: any) {
    console.error('Error cancelling subscription:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
}
