import { auth } from '@/app/(auth)/auth';
import { getProjectsByUserId } from '@/lib/db/queries';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic'; // Ensure route is not cached

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return Response.json('Unauthorized!', { status: 401 });
    }

    // Create a timeout promise with a longer duration (30 seconds)
    const TIMEOUT_DURATION = 30000; // 30 seconds
    console.log(`Setting timeout for ${TIMEOUT_DURATION}ms`);
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database query timeout')), TIMEOUT_DURATION);
    });

    try {
      // Race between the actual query and the timeout
      // biome-ignore lint: Forbidden non-null assertion.
      const projects = await Promise.race([
        getProjectsByUserId({ id: session.user.id! }),
        timeoutPromise
      ]) as any;

      return NextResponse.json(projects);
    } catch (error: any) {
      console.error('Error fetching projects:', error);
      
      if (error.message === 'Database query timeout') {
        return NextResponse.json(
          { error: 'Request timed out, please try again' }, 
          { status: 504 }
        );
      }
      
      if (error.message?.includes('CONNECTION_CLOSED')) {
        return NextResponse.json(
          { error: 'Database connection error, please try again' }, 
          { status: 503 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch projects' }, 
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unhandled error in history API:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}
