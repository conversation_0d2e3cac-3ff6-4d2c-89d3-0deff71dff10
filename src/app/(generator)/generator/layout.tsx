'use client';

import { RouteGuard } from '@/components/auth/RouteGuard';
import { HeaderProvider } from '@/components/generator/HeaderProvider';
import { SidebarProvider } from '@/components/ui/sidebar';
import { GeneratorSidebar } from '@/components/generator/GeneratorSidebar';
import { usePathname } from 'next/navigation';
import React from 'react';

export default function GeneratorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // const pathname = usePathname();
  // console.log('pathname', pathname?.split('/'))
  // const projectId = pathname?.split('/')[1];
  // const chatId = pathname?.split('/')[2]; // Extract chatId from URL
  
  return (
    <RouteGuard>
      <SidebarProvider defaultOpen={true}>
        <div className="flex h-screen w-screen overflow-hidden">
          {/* Custom Generator Sidebar */}
          {/*<GeneratorSidebar chatId={chatId} projectId={projectId} />*/}
          
          {/* Main Content */}
          <div className="flex-1 overflow-auto">
              {children}
          </div>
        </div>
      </SidebarProvider>
    </RouteGuard>
  );
}
