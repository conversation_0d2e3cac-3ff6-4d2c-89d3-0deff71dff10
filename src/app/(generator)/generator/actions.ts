import {customModel} from "@/lib/ai";
import {CoreUserMessage, generateText} from "ai";
import {
    deleteFileStateByMessageId,
    deleteMessagesByChatIdAfterTimestamp,
    getMessageById,
    getMessagesByTimestamp,
    updateChatVisiblityById
} from "@/lib/db/queries";
import { VisibilityType } from "@/components/generator/VisibilitySelector";

export async function generateTitleFromUserMessage({
                                                       message,
                                                   }: {
    message: CoreUserMessage;
}) {
    const {text: title} = await generateText({
        model: customModel('gpt-4o-mini'),
        system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - the user is trying to build a mobile app, so name it accordingly
    - ensure it is not more than 80 characters long
    - do not use quotes or colons`,
        prompt: JSON.stringify(message),
    });

    return title;
}

export async function deleteTrailingMessages({id}: { id: string }) {
    const [message] = await getMessageById({id});

    await deleteMessagesByChatIdAfterTimestamp({
        chatId: message.chatId,
        timestamp: message.createdAt,
    });
}

export async function updateChatVisibility({
                                               chatId,
                                               visibility,
                                           }: {
    chatId: string;
    visibility: VisibilityType;
}) {
    await updateChatVisiblityById({ chatId, visibility });
}
