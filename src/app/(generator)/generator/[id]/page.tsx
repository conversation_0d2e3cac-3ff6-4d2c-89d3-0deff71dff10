import {auth} from "@/app/(auth)/auth";
import GeneratorPage from "@/components/generator/GeneratorPage";
import {getChatById, getMessagesByChatId} from "@/lib/db/queries";
import {notFound, redirect} from "next/navigation";
import {Metadata} from "next";
import {ChatAuthCheck} from '@/components/generator/ChatAuthCheck';
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import { Loader2 } from "lucide-react";

export async function generateMetadata(
    props: { params: Promise<{ id: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {id} = params;

    const suffix = "magically - Create mobile apps in minutes";

    if (id) {
        // fetch data
        const chat = await getChatById({id});
        return {
            title: chat?.title + " | " + suffix
        }
    } else {
        return {
            title: suffix
        }
    }
}


export default async function Page(props: {
    searchParams: Promise<{ template: string, prompt: string }>,
    params: Promise<{ id: string }>
}) {
    const params = await props.params;
    const {id} = params;
    const chat = await getChatById({id});
    const session = await auth();

    if (!chat) {
        return notFound();
    }
    
    // Redirect to the new route structure
    if (chat.projectId) {
        redirect(`/projects/${chat.projectId}/chats/${id}`);
    }

    // Show loader while redirecting
    return (
        <div className="flex items-center justify-center h-screen">
            <div className="flex flex-col items-center gap-4">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
                <p className="text-muted-foreground">Redirecting to new chat location...</p>
            </div>
        </div>
    )
}