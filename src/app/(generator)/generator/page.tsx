import GeneratorPage from "@/components/generator/GeneratorPage";
import {generateUUID} from "@/lib/utils";
import {Metadata} from "next";
import {getChatById} from "@/lib/db/queries";
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import {LinkButton} from "@/components/ui/link-button";

export async function generateMetadata(
    props: { params: Promise<{ id: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {id} = params;

    const suffix = "magically - Create mobile apps in minutes";

    if (id) {
        // fetch data
        const chat = await getChatById({id});
        return {
            title: chat?.title + " | " + suffix
        }
    } else {
        return {
            title: suffix
        }
    }
}


export default async function Page(props: { searchParams: Promise<{ template: string, prompt: string }> }) {
    const {prompt, template} = await props.searchParams;
    // console.log('prompt', prompt, template)
    const id = generateUUID();

    return (
        <div className="w-full h-full flex flex-col justify-center items-center">
            This page is no longer valid. Please create a new project from the homepage.

            <LinkButton href={'/'}>
                Go to home
            </LinkButton>
        </div>
    )
    // return (
    //     <HeaderProvider chatId={id} isInitial={true}>
    //         <GeneratorPage
    //             isInitial={true}
    //             initialChatId={id}
    //             prompt={prompt}
    //             template={template}
    //             messagesFromDb={[]}
    //         />
    //     </HeaderProvider>
    //
    // )
}