"use client";
import {useRouter} from "next/navigation";
import {useActionState, useEffect} from "react";
import {resetPassword, ResetPasswordState} from "@/app/(auth)/actions";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";

export default function PasswordTokenComponent({token}: { token: string }) {
    const router = useRouter();
    const [state, formAction] = useActionState<ResetPasswordState, FormData>(resetPassword, {
        status: 'idle',
    });

    useEffect(() => {
        if (state.status === 'success') {
            router.push('/login?reset=success');
        }
    }, [state.status, router]);

    return (
        <div className="flex min-h-screen items-center justify-center">
            <div className="w-full max-w-md space-y-8 px-4 py-12">
                <div className="space-y-2 text-center">
                    <h1 className="text-3xl font-bold">Reset Password</h1>
                    <p className="text-gray-500">Enter your new password below.</p>
                </div>

                <form action={formAction} className="space-y-4">
                    <input type="hidden" name="token" value={token}/>

                    <div className="space-y-2">
                        <Input
                            id="password"
                            name="password"
                            type="password"
                            placeholder="New Password"
                            required
                            minLength={6}
                            className="w-full"
                        />
                    </div>

                    {state.status === 'invalid_token' && (
                        <p className="text-sm text-red-500">
                            This reset link is invalid or has already been used.
                        </p>
                    )}

                    {state.status === 'token_expired' && (
                        <p className="text-sm text-red-500">
                            This reset link has expired. Please request a new one.
                        </p>
                    )}

                    {state.status === 'invalid_data' && (
                        <p className="text-sm text-red-500">
                            Password must be at least 6 characters long.
                        </p>
                    )}

                    {state.status === 'failed' && (
                        <p className="text-sm text-red-500">
                            Something went wrong. Please try again.
                        </p>
                    )}

                    <Button
                        type="submit"
                        className="w-full"
                        disabled={state.status === 'in_progress'}
                    >
                        {state.status === 'in_progress' ? 'Resetting...' : 'Reset Password'}
                    </Button>
                </form>
            </div>
        </div>
    );
}