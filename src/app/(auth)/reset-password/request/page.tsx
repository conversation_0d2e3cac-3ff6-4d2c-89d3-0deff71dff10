'use client';

import { useFormState } from 'react-dom';
import { useRouter } from 'next/navigation';
import {useActionState, useEffect} from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {requestPasswordReset, ResetPasswordRequestState} from '@/app/(auth)/actions';

export default function RequestPasswordResetPage() {
  const router = useRouter();
  const [state, formAction] = useActionState<ResetPasswordRequestState, FormData>(requestPasswordReset, {
    status: 'idle',
  });

  useEffect(() => {
    if (state.status === 'success') {
      router.push('/login?reset=requested');
    }
  }, [state.status, router]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md space-y-8 px-4 py-12">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Reset Password</h1>
          <p className="text-gray-500">
            Enter your email address and we&apos;ll send you a link to reset your
            password.
          </p>
        </div>

        <form action={formAction} className="space-y-4">
          <div className="space-y-2">
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="Email"
              required
              className="w-full"
            />
          </div>

          {state.status === 'user_not_found' && (
            <p className="text-sm text-red-500">
              No account found with this email address.
            </p>
          )}

          {state.status === 'rate_limited' && (
            <p className="text-sm text-red-500">
              Too many reset attempts. Please try again in 24 hours.
            </p>
          )}

          {state.status === 'invalid_data' && (
            <p className="text-sm text-red-500">Please enter a valid email address.</p>
          )}

          {state.status === 'failed' && (
            <p className="text-sm text-red-500">
              Something went wrong. Please try again.
            </p>
          )}

          <Button
            type="submit"
            className="w-full"
            disabled={state.status === 'in_progress'}
          >
            {state.status === 'in_progress' ? 'Sending...' : 'Send Reset Link'}
          </Button>
        </form>
      </div>
    </div>
  );
}
