import type {NextAuthConfig} from 'next-auth';

export const authConfig: NextAuthConfig = {
    pages: {
        signIn: '/login',
        newUser: '/',
    },
    providers: [
        // added later in auth.ts since it requires bcrypt which is only compatible with Node.js
        // while this file is also used in non-Node.js environments
    ],
    callbacks: {
        authorized({auth, request: {nextUrl}}) {
            const isLoggedIn = !!auth?.user;
            
            // Check if this is a media API route - these should be public
            if (nextUrl.pathname.startsWith('/api/media')) {
                return true; // Always allow access to media API routes
            }

            // Protected Routes that require auth
            const protectedPaths = [
                '/dashboard'
                // '/generator' removed to allow anonymous access
            ];

            // Auth Routes (login/register)
            const authPaths = [
                '/login',
                '/register'
            ];

            const isProtectedRoute = protectedPaths.some(path =>
                nextUrl.pathname.startsWith(path)
            );
            const isAuthRoute = authPaths.some(path =>
                nextUrl.pathname.startsWith(path)
            );

            // Redirect logged-in users away from auth pages
            if (isLoggedIn && isAuthRoute) {
                return Response.redirect(new URL('/', nextUrl));
            }

            // Allow access to auth pages if not logged in
            if (isAuthRoute) {
                return true;
            }

            // Protect routes that require auth
            if (isProtectedRoute) {
                return isLoggedIn;
            }

            // Allow public access to all other routes (including landing page)
            return true;
        },
    },
} satisfies NextAuthConfig;
