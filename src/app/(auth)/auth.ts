import NextAuth, { type User, type Session } from 'next-auth';
import { JWT } from '@auth/core/jwt';
import Google from 'next-auth/providers/google';
import Credentials from 'next-auth/providers/credentials';
import { compare } from 'bcrypt-ts';

import {createUser, getUser} from '@/lib/db/queries';
import { authConfig } from './auth.config';
import { sendWelcomeEmail } from '@/lib/email';

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "select_account"
        }
      }
    }),
    Credentials({
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize({ email, password }: any) {
        const users = await getUser(email);
        if (users.length === 0) return null;

        const passwordsMatch = await compare(password, users[0].password!);
        if (!passwordsMatch) return null;

        // Return user without password
        const { password: _, ...userWithoutPassword } = users[0];
        return userWithoutPassword as User;
      },
    })
  ],
  callbacks: {
    async jwt({ token, user, account }: { token: any, user: User | null, account: any }) {
      if (user) {
        if (account?.provider === 'google') {
          try {
            // Check if user exists
            const existingUsers = await getUser(user.email!);
            let userId: string;
            let isNewUser = false;

            if (existingUsers.length === 0) {
              // Create new user for Google OAuth
              const userName = user.name || user.email!.split('@')[0]; // Use name or email prefix as name
              const createdUser = await createUser(
                user.email!,
                '', // No password for Google OAuth
                userName,
                'google' // Set provider as google
              );
              userId = createdUser.id;
              isNewUser = true;

              // Send welcome email for new Google OAuth users
              try {
                await sendWelcomeEmail({
                  email: user.email!,
                  name: userName,
                });
              } catch (emailError) {
                // Log the error but don't fail the authentication process
                console.error('Failed to send welcome email for Google OAuth user:', emailError);
              }
            } else {
              // User exists, treat as login
              userId = existingUsers[0].id;
            }

            // Set token properties for Google user
            token.id = userId;
            token.isNewUser = isNewUser;
            token.email = user.email!;
            token.name = user.name;
            token.picture = user.image;
            token.provider = account.provider;
          } catch (error) {
            console.error('Error in Google OAuth flow:', error);
            throw error;
          }
        } else {
          // Credentials provider sign in
          token.id = user.id as string;
          token.email = user.email!;
          token.name = user.name;
          token.provider = account?.provider || 'credentials';
          token.isNewUser = false;
        }
      }
      return token;
    },
    // @ts-ignore - Bypassing type check for build
    async session({
      session,
      token,
    }) {
      if (session.user) {
        // @ts-ignore
        session.user.id = token.id;
        // @ts-ignore
        session.user.email = token.email;
        // @ts-ignore
        session.user.name = token.name;
        // @ts-ignore
        session.user.image = token.picture;
        // @ts-ignore
        session.user.provider = token.provider;
        // @ts-ignore
        session.user.isNewUser = token.isNewUser;
      }
      return session;
    },
  },
});
