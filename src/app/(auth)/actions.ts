'use server';

import {genSaltSync, hash, hashSync} from 'bcrypt-ts';
import { z } from 'zod';
import { createUser, getUser, createPasswordResetToken, getPasswordResetToken, updateUserPassword, deletePasswordResetToken } from '@/lib/db/queries';
import { signIn } from './auth';
import { sendPasswordResetEmail, sendWelcomeEmail } from '@/lib/email';
import { checkPasswordResetRateLimit } from '@/lib/rate-limit';

const authFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

const signupFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().min(4)
});

export interface LoginActionState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data';
}

export const login = async (
  _: LoginActionState,
  formData: FormData,
): Promise<LoginActionState> => {
  try {
    const validatedData = authFormSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
    });

    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }

    return { status: 'failed' };
  }
};

export interface RegisterActionState {
  status:
    | 'idle'
    | 'in_progress'
    | 'success'
    | 'failed'
    | 'user_exists'
    | 'invalid_data';
}

export const register = async (
  _: RegisterActionState,
  formData: FormData,
): Promise<RegisterActionState> => {
  try {
    const validatedData = signupFormSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
      name: formData.get("name")
    });

    const [existingUser] = await getUser(validatedData.email);

    if (existingUser) {
      return { status: 'user_exists' };
    }

    // Create the user first
    const newUser = await createUser(
      validatedData.email,
      validatedData.password,
      validatedData.name,
        'credentials'
    );

    // Then sign them in
    const signInResult = await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    if (signInResult?.error) {
      console.error('Failed to sign in after registration:', signInResult.error);
      return { status: 'failed' };
    }

    // Send welcome email
    try {
      await sendWelcomeEmail({
        email: validatedData.email,
        name: validatedData.name,
      });
    } catch (emailError) {
      // Log the error but don't fail the registration process
      console.error('Failed to send welcome email:', emailError);
    }

    return { status: 'success' };
  } catch (error) {
    console.error('Registration error:', error);
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    return { status: 'failed' };
  }
};

export interface ResetPasswordRequestState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data' | 'user_not_found' | 'rate_limited';
}

export interface ResetPasswordState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data' | 'invalid_token' | 'token_expired';
}

const resetPasswordRequestSchema = z.object({
  email: z.string().email(),
});

const resetPasswordSchema = z.object({
  token: z.string().min(1),
  password: z.string().min(6),
});

export const requestPasswordReset = async (
  _: ResetPasswordRequestState,
  formData: FormData,
): Promise<ResetPasswordRequestState> => {
  try {
    const validatedData = resetPasswordRequestSchema.parse({
      email: formData.get('email'),
    });

    // Check rate limit before proceeding
    const rateLimitAllowed = await checkPasswordResetRateLimit(validatedData.email);
    if (!rateLimitAllowed) {
      return { status: 'rate_limited' };
    }

    const users = await getUser(validatedData.email);
    if (users.length === 0) {
      return { status: 'user_not_found' };
    }

    const user = users[0];
    const token = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await createPasswordResetToken({
      userId: user.id,
      token,
      expiresAt,
    });

    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password/${token}`;

    try {
      await sendPasswordResetEmail({
        email: user.email,
        resetLink,
      });
    } catch (error) {
      console.error('Failed to send reset email:', error);
      // Delete the token if email fails
      await deletePasswordResetToken(token);
      return { status: 'failed' };
    }

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    return { status: 'failed' };
  }
};

export const resetPassword = async (
  _: ResetPasswordState,
  formData: FormData,
): Promise<ResetPasswordState> => {
  try {
    const validatedData = resetPasswordSchema.parse({
      token: formData.get('token'),
      password: formData.get('password'),
    });

    const tokens = await getPasswordResetToken(validatedData.token);
    if (tokens.length === 0) {
      return { status: 'invalid_token' };
    }

    const token = tokens[0];
    if (token.expiresAt < new Date()) {
      return { status: 'token_expired' };
    }

    const salt = genSaltSync(10);
    const hashedPassword = hashSync(validatedData.password, salt);
    await updateUserPassword({
      userId: token.userId,
      hashedPassword,
    });

    await deletePasswordResetToken(token.token);

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    return { status: 'failed' };
  }
};
