'use client';

import Link from 'next/link';
import {useRouter, useSearchParams} from 'next/navigation';
import { useActionState, useEffect, useState, Suspense } from 'react';
import { toast } from 'sonner';
import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';
import { GoogleButton } from '@/components/auth/google-button';
import { login, type LoginActionState } from '../actions';
import { trackUserEvent } from '@/lib/analytics/track';

function LoginForm() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const searchParams = useSearchParams();

  const [state, formAction] = useActionState<LoginActionState, FormData>(
    login,
    {
      status: 'idle',
    },
  );

  const callbackUrl = searchParams?.get('callbackUrl') || '/';

  useEffect(() => {
    if (state.status === 'failed') {
      toast.error('Invalid credentials!');
      // Track failed login attempt
      trackUserEvent('SIGNED_IN', {
        auth_method: 'email',
        error_type: 'invalid_credentials'
      });
    } else if (state.status === 'invalid_data') {
      toast.error('Failed validating your submission!');
      // Track validation failure
      trackUserEvent('SIGNED_IN', {
        auth_method: 'email',
        error_type: 'invalid_data'
      });
    } else if (state.status === 'success') {
      // Track successful login
      trackUserEvent('SIGNED_IN', {
        auth_method: 'email',
        time_to_complete: 0 // We don't have timing info here, but could be added
      });
      setIsSuccessful(true);
      router.refresh();
    }
  }, [state.status, router]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Sign In</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            Continue with your preferred method
          </p>
        </div>
        <div className="px-4 sm:px-16">
          <GoogleButton callbackUrl={callbackUrl} />
          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-muted" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or continue with email</span>
            </div>
          </div>
        </div>
        <AuthForm action={handleSubmit} defaultEmail={email}>
          <div className="space-y-4">
            <SubmitButton isSuccessful={isSuccessful}>Sign in</SubmitButton>

            {/*<div className="text-center text-sm">*/}
            {/*  <Link href="/reset-password/request" className="text-blue-600 hover:underline">*/}
            {/*    Forgot your password?*/}
            {/*  </Link>*/}
            {/*</div>*/}
          </div>
          <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
            {"Don't have an account? "}
            <Link
              href={`/register?callbackUrl=${encodeURIComponent(callbackUrl)}}`}
              className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
            >
              Sign up
            </Link>
            {' for free.'}
          </p>
        </AuthForm>
      </div>
    </div>
  );
}

// Loading fallback component
function LoginSkeleton() {
  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <div className="h-7 w-24 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="px-4 sm:px-16">
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-muted" />
            </div>
          </div>
        </div>
        <div className="px-4 sm:px-16 space-y-4">
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}

export default function Page() {
  return (
    <Suspense fallback={<LoginSkeleton />}>
      <LoginForm />
    </Suspense>
  );
}
