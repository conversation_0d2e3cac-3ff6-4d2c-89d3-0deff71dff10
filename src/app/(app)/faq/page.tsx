'use client';

import {useState} from 'react';
import {faqs} from '@/data/faqs';
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from '@/components/ui/accordion';
import {Input} from '@/components/ui/input';
import {Badge} from '@/components/ui/badge';
import {Search, MessagesSquare} from 'lucide-react';
import {Button} from '@/components/ui/button';
import Link from 'next/link';

type Category = 'all' | 'general' | 'technical' | 'billing' | 'security';

export default function FAQPage() {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<Category>('all');

    const categories: { value: Category; label: string }[] = [
        {value: 'all', label: 'All'},
        {value: 'general', label: 'General'},
        {value: 'technical', label: 'Technical'},
        {value: 'billing', label: 'Billing'},
        {value: 'security', label: 'Security'},
    ];

    const filteredFaqs = faqs.filter((faq) => {
        const matchesSearch =
            faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
            faq.answer.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;

        return matchesSearch && matchesCategory;
    });

    return (
        <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
            <div className="container max-w-5xl py-12 mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header Section */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                        Frequently Asked Questions
                    </h1>
                    <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                        Find answers to common questions about Magically. Can't find what you're looking for?
                        <Link href="/contact"
                              className="text-primary hover:text-primary/80 ml-1 inline-flex items-center gap-1">
                            Contact our support team
                            <MessagesSquare className="h-4 w-4"/>
                        </Link>
                    </p>
                </div>

                {/* Search and Filter */}
                <div className="mb-12 space-y-6 max-w-2xl mx-auto">
                    <div className="relative">
                        <Search className="absolute left-4 top-3 h-5 w-5 text-muted-foreground"/>
                        <Input
                            type="search"
                            placeholder="Search FAQs..."
                            className="pl-12 h-12 text-base bg-background/50 backdrop-blur-sm border-muted-foreground/20 hover:border-primary/30 focus-visible:ring-primary/20"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>

                    <div className="flex flex-wrap gap-2 justify-center">
                        {categories.map((category) => (
                            <Badge
                                key={category.value}
                                variant={selectedCategory === category.value ? "default" : "outline"}
                                className={`cursor-pointer px-4 py-1.5 text-sm transition-all ${selectedCategory === category.value ? 'shadow-sm' : 'hover:border-primary/30'}`}
                                onClick={() => setSelectedCategory(category.value)}
                            >
                                {category.label}
                            </Badge>
                        ))}
                    </div>
                </div>

                {/* FAQ List */}
                <div className="max-w-3xl mx-auto">
                    <Accordion type="single" collapsible className="space-y-4">
                        {filteredFaqs.map((faq) => (
                            <AccordionItem
                                key={faq.id}
                                value={faq.id}
                                className="border border-muted-foreground/20 rounded-xl px-6 py-2 backdrop-blur-sm bg-background/50 hover:border-primary/30 transition-all duration-300"
                            >
                                <AccordionTrigger className="text-left hover:no-underline">
                                    <div className="flex items-start gap-2">
                                        <span className="flex-1 text-lg font-medium">{faq.question}</span>
                                        <Badge
                                            variant="secondary"
                                            className="mt-1 capitalize bg-primary/10 text-primary hover:bg-primary/20"
                                        >
                                            {faq.category}
                                        </Badge>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="text-muted-foreground text-base leading-relaxed pt-2">
                                    {faq.answer}
                                </AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>

                    {filteredFaqs.length === 0 && (
                        <div className="text-center py-12 space-y-4">
                            <p className="text-muted-foreground text-lg">
                                No FAQs found matching your search criteria.
                            </p>
                            <Button variant="outline" onClick={() => {
                                setSearchQuery('');
                                setSelectedCategory('all');
                            }}>
                                Reset Filters
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
