import {BentoShowcase} from '@/components/home/<USER>';
import {HowItWorks} from '@/components/home/<USER>';
import {CommunityShowcase} from '@/components/home/<USER>';
import {CTASection} from '@/components/home/<USER>';
import {Footer} from '@/components/base/footer';
import {HeroSection} from "@/components/home/<USER>";
import {GradientBackground} from "@/components/ui/gradient-background";
import {HomePageClientLogic} from "@/components/home/<USER>";

export default async function Home() {
    // Check if the user has seen the intro before using the cookie name
    // Using a simple check for existence rather than the .has() method

    return (
        <>
            <HomePageClientLogic initialShowFullIntroSequence={true}/>

            {/* These components will be shown after the intro sequence */}
            <BentoShowcase/>
            {/*<HowItWorks/>*/}
            {/*<CommunityShowcase/>*/}
            <CTASection/>
        </>
    );
}
