'use client';

import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import { RedirectHandler } from './redirect-handler';

export default function CheckoutSuccessPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <h1 className="text-xl font-semibold mb-2">Processing your payment...</h1>
      <p className="text-muted-foreground">Please wait while we verify your payment.</p>
      <Suspense>
        <RedirectHandler />
      </Suspense>
    </div>
  );
}
