'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {sendGTMEvent, } from "@next/third-parties/google";
import {trackSubscriptionEvent} from "@/lib/analytics/track";

export function RedirectHandler() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const returnUrl = searchParams?.get('return_url') || '/';
    sendGTMEvent({event: 'conversion', value: {
        'send_to': 'AW-17019881839/oyqgCMPu47oaEO-S27M_',
        'value': 15,
        'currency': 'USD',
        'transaction_id': '',
      }})

      trackSubscriptionEvent('SUBSCRIPTION_COMPLETED', {
          currency: 'USD',
          entry_point: 'upgrade_dialog' // TODO: Update this
      });
    router.push(returnUrl);
  }, [router, searchParams]);

  return null;
}
