import { Suspense } from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { auth } from '@/app/(auth)/auth';
import { getUserProjects } from '@/lib/db/project-queries';
import { Button } from '@/components/ui/button';
import { PlusCircle, Settings, Smartphone, AlertCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { LinkButton } from '@/components/ui/link-button';
import ProjectAppIcon from "@/components/project/ProjectAppIcon";
import ProjectsLoading from "@/app/(app)/projects/loading";

export const metadata: Metadata = {
  title: 'My Projects | magically - Create mobile apps in minutes',
  description: 'View and manage all your mobile app projects created with magically',
};

interface ProjectCardProps {
  project: any;
}

function ProjectCard({ project }: ProjectCardProps) {
  const updatedTimeAgo = project.updatedAt 
    ? formatDistanceToNow(new Date(project.updatedAt), { addSuffix: true })
    : 'Recently';

  return (
    <div className="group relative border rounded-lg overflow-hidden shadow-sm bg-card transition-all hover:shadow-xl hover:border-primary/80 hover:bg-primary/10">
      <Link 
        href={`/projects/${project.id}`}
        className="absolute inset-0 z-10"
        aria-label={`View ${project.appName || 'project'}`}
      />
      <div className="p-5">
        <div className="flex items-center gap-4 mb-3">
            <ProjectAppIcon project={project}/>

            <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg truncate transition-colors">
              {project.appName || 'Untitled Project'}
            </h3>
            <p className="text-xs text-muted-foreground truncate">
              {project.slug && `life.magically.${project.slug}`}
            </p>
          </div>
        </div>
        
        <p className="text-muted-foreground text-xs line-clamp-2 mb-4">
          {project.description || 'No description provided'}
        </p>
        
        <div className="flex justify-between items-center">
          <span className="text-xs text-muted-foreground">
            Updated {updatedTimeAgo}
          </span>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/projects/${project.id}`}>
                View Project
              </Link>
            </Button>
            <Button variant="ghost" size="icon" asChild className="h-8 w-8">
              <Link href={`/projects/${project.id}/settings`}>
                <Settings className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 border rounded-lg bg-muted/30">
      <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
        <Smartphone className="h-8 w-8 text-primary" />
      </div>
      <h3 className="text-xl font-medium mb-2">No projects yet</h3>
      <p className="text-muted-foreground text-center max-w-md mb-6">
        Create your first mobile app project and bring your ideas to life with magically.
      </p>
      <Button asChild>
        <Link href="/">
          <PlusCircle className="mr-2 h-4 w-4" />
          Create New Project
        </Link>
      </Button>
    </div>
  );
}

function ErrorState() {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 border rounded-lg bg-destructive/10">
      <AlertCircle className="h-12 w-12 text-destructive mb-4" />
      <h3 className="text-xl font-medium mb-2">Something went wrong</h3>
      <p className="text-muted-foreground text-center max-w-md mb-6">
        We couldn't load your projects. Please try again later.
      </p>
      <Button variant="outline" asChild>
        <Link href="/">
          Go Home
        </Link>
      </Button>
    </div>
  );
}

async function ProjectsList() {
  const session = await auth();
  
  if (!session?.user?.id) {
    return notFound();
  }
  
  try {
    const projects = await getUserProjects(session.user.id);
    
    if (!projects || projects.length === 0) {
      return <EmptyState />;
    }
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map((project) => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>
    );
  } catch (error) {
    console.error('Error fetching projects:', error);
    return <ErrorState />;
  }
}

export default async function ProjectsPage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl min-h-screen">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">My Projects</h1>
          <p className="text-muted-foreground">
            View and manage all your mobile app projects
          </p>
        </div>
        <Button asChild>
          <Link href="/">
            <PlusCircle className="mr-2 h-4 w-4" />
            New Project
          </Link>
        </Button>
      </div>
      
      <Suspense fallback={<div className="h-[500px] flex items-center justify-center">
          <ProjectsLoading/>
      </div>}>
        <ProjectsList />
      </Suspense>
    </div>
  );
}