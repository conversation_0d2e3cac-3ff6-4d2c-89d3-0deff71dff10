'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Wand2 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/use-auth';

const ProjectCreator = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const prompt = searchParams?.get('prompt') || '';
  const { isAuthenticated } = useAuth();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [tipIndex, setTipIndex] = useState(0);
  const [isCreating, setIsCreating] = useState(true);
  
  const steps = [
    "Analyzing your idea...",
    "Designing app architecture...",
    "Setting up project...",
    "Almost ready..."
  ];
  
  const tips = [
    "You can customize your app's colors in the settings",
    "Use the chat to ask for UI improvements",
    "Export your code anytime from the project settings",
    "Share your project with teammates using the share button"
  ];
  
  useEffect(() => {
    // Rotate through tips
    const tipInterval = setInterval(() => {
      setTipIndex(prev => (prev + 1) % tips.length);
    }, 4000);
    
    // Simulate progress with timed step changes
    const stepInterval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < steps.length - 1) return prev + 1;
        return prev;
      });
    }, 1500);
    
    // Create the project
    const createProject = async () => {
      if (!prompt) {
        toast.error('No prompt provided');
        router.push('/');
        return;
      }
      
      if (!isAuthenticated) {
        router.replace(`/login?callbackUrl?prompt=${encodeURIComponent(prompt)}`);
        return;
      }
      
      try {
        const response = await fetch('/api/project', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message: prompt })
        });
        
        if (!response.ok) throw new Error('Failed to create project');
        
        const project = await response.json();
        
        // Complete the progress animation before redirecting
        setTimeout(() => {
          router.replace(`/projects/${project.id}/chats/new?prompt=${encodeURIComponent(prompt)}`);
        }, 1000);
      } catch (error) {
        console.error('Error creating project:', error);
        toast.error('Failed to create project');
        setIsCreating(false);
        // Redirect to home after error
        setTimeout(() => {
          router.push('/');
        }, 3000);
      }
    };
    
    createProject();
    
    return () => {
      clearInterval(tipInterval);
      clearInterval(stepInterval);
    };
  }, [prompt, router, isAuthenticated, tips.length, steps.length]);
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-background to-background/80 p-4">
      <div className="max-w-md w-full space-y-12">
        <div className="space-y-4 text-center">
          <div className="inline-block relative">
            <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping"></div>
            <div className="relative z-10 rounded-full bg-primary p-4">
              <Wand2 className="h-8 w-8 text-white" />
            </div>
          </div>
          
          <h2 className="text-2xl font-bold">Creating Your Project</h2>
          <p className="text-muted-foreground">We're turning your idea into reality</p>
        </div>
        
        <div className="space-y-6">
          {steps.map((step, index) => (
            <div 
              key={index} 
              className={`flex items-center space-x-3 transition-all duration-500 ${
                index <= currentStep ? 'opacity-100' : 'opacity-30'
              }`}
            >
              <div className={`h-6 w-6 rounded-full flex items-center justify-center ${
                index < currentStep ? 'bg-green-500' : 
                index === currentStep ? 'bg-primary animate-pulse' : 'bg-gray-300'
              }`}>
                {index < currentStep && (
                  <svg className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
              <span className="text-sm">{step}</span>
            </div>
          ))}
        </div>
        
        <div className="h-24 flex items-center justify-center">
          <div className="transition-all duration-1000 ease-in-out">
            <p className="text-sm italic text-center">
              <span className="text-primary font-medium">Tip:</span> {tips[tipIndex]}
            </p>
          </div>
        </div>
        
        <div className="flex justify-center">
          <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-500 ease-out"
              style={{ 
                width: `${Math.min(((currentStep + 1) / steps.length) * 100, 95)}%` 
              }}
            ></div>
          </div>
        </div>
        
        {!isCreating && (
          <div className="text-center text-red-500">
            <p>There was a problem creating your project. Redirecting...</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Loading fallback component
function CreatingProjectSkeleton() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-background to-background/80 p-4">
      <div className="max-w-md w-full space-y-12">
        <div className="space-y-4 text-center">
          <div className="inline-block relative">
            <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping"></div>
            <div className="relative z-10 rounded-full bg-primary p-4">
              <Wand2 className="h-8 w-8 text-white" />
            </div>
          </div>
          
          <h2 className="text-2xl font-bold">Creating Your Project</h2>
          <p className="text-muted-foreground">We're turning your idea into reality</p>
        </div>
        
        <div className="space-y-6">
          {[0, 1, 2, 3].map((index) => (
            <div 
              key={index} 
              className="flex items-center space-x-3 transition-all duration-500 opacity-30"
            >
              <div className="h-6 w-6 rounded-full flex items-center justify-center bg-gray-300"></div>
              <span className="text-sm">Loading...</span>
            </div>
          ))}
        </div>
        
        <div className="h-24 flex items-center justify-center">
          <div className="transition-all duration-1000 ease-in-out">
            <p className="text-sm italic text-center">
              <span className="text-primary font-medium">Tip:</span> Loading tips...
            </p>
          </div>
        </div>
        
        <div className="flex justify-center">
          <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-500 ease-out animate-pulse"
              style={{ width: '30%' }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
const CreatingProjectPage = () => {
  return (
    <Suspense fallback={<CreatingProjectSkeleton />}>
      <ProjectCreator />
    </Suspense>
  );
};

export default CreatingProjectPage;
