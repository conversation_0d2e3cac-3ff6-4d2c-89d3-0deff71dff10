'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import {toast} from "sonner";

/**
 * Test page for the context engine
 * Allows testing the context engine with a specific project ID
 */
export default function TestContextEnginePage() {
  const [query, setQuery] = useState('');
  const [projectId, setProjectId] = useState('4ca650dc-00a6-4e52-b5ce-bec7df235599');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Handle form submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      // Call the context engine API
      const response = await fetch('/api/context-engine', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          projectId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to query context engine');
      }

      const data = await response.json();
      setResult(data);
      toast.info(`Query successful. Found ${data.relevantFiles?.length || 0} relevant files and ${data.snippets?.length || 0} code snippets`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      toast.error(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-8 max-w-7xl mx-auto">
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Context Engine Test</h1>
          <p className="text-muted-foreground mt-2">
            This page allows you to test the context engine with a specific project ID.
            The context engine provides comprehensive codebase understanding in a single query.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Query the Codebase</CardTitle>
            <CardDescription>
              Enter a natural language query to understand the codebase structure and relationships.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="projectId">Project ID</Label>
                <Input
                  id="projectId"
                  value={projectId}
                  onChange={(e) => setProjectId(e.target.value)}
                  placeholder="Enter project ID"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="query">Query</Label>
                <Textarea
                  id="query"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="Enter your query about the codebase (e.g., 'How is authentication implemented?')"
                  rows={4}
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={!query || !projectId || isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading ? 'Querying...' : 'Query Context Engine'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {error && (
          <Card className="border-destructive/50 bg-destructive/5">
            <CardHeader className="pb-2">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-destructive mr-2" />
                <CardTitle className="text-destructive">Error</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-destructive">{error}</p>
            </CardContent>
          </Card>
        )}

        {isLoading && !error && (
          <Card>
            <CardContent className="flex items-center justify-center py-6">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <p>Analyzing codebase and retrieving context...</p>
            </CardContent>
          </Card>
        )}

        {result && (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Results</h2>
            </div>

            <Tabs defaultValue="snippets" className="w-full">
              <TabsList className="grid w-full grid-cols-7">
                <TabsTrigger value="snippets">Code Snippets ({result.snippets?.length || 0})</TabsTrigger>
                <TabsTrigger value="files">Relevant Files ({result.relevantFiles?.length || 0})</TabsTrigger>
                <TabsTrigger value="structure">Codebase Structure</TabsTrigger>
                <TabsTrigger value="relationships">Relationships</TabsTrigger>
                <TabsTrigger value="plan">Action Plan</TabsTrigger>
                <TabsTrigger value="supabase">Supabase</TabsTrigger>
                <TabsTrigger value="llm">LLM Format</TabsTrigger>
              </TabsList>

              <TabsContent value="snippets" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Code Snippets</CardTitle>
                    <CardDescription>Relevant code snippets extracted from the codebase</CardDescription>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ScrollArea className="h-[600px]">
                      <div className="p-6 space-y-6">
                        {result.snippets?.length > 0 ? (
                          result.snippets.map((snippet: any, index: number) => (
                            <div key={index} className="border rounded-lg p-4">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <h4 className="font-semibold">{snippet.filePath}</h4>
                                  <Badge variant="outline" className="capitalize">{snippet.type}</Badge>
                                </div>
                                <Badge variant="secondary">Lines {snippet.startLine}-{snippet.endLine}</Badge>
                              </div>

                              {snippet.symbols && snippet.symbols.length > 0 && (
                                <div className="mb-2">
                                  <p className="text-sm font-semibold">Symbols:</p>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {snippet.symbols.map((symbol: string, symIndex: number) => (
                                      <Badge key={symIndex} variant="outline">{symbol}</Badge>
                                    ))}
                                  </div>
                                </div>
                              )}

                              <Separator className="my-2" />

                              <div className="mt-2">
                                <p className="text-sm font-semibold">Code:</p>
                                <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre mt-1">
                                  {snippet.content}
                                </pre>
                              </div>

                              {snippet.context && (
                                <div className="mt-2">
                                  <p className="text-sm font-semibold">Context:</p>
                                  <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre mt-1">
                                    {snippet.context}
                                  </pre>
                                </div>
                              )}

                              {snippet.score !== undefined && (
                                <div className="mt-2 text-right">
                                  <Badge variant="outline">Score: {snippet.score.toFixed(2)}</Badge>
                                </div>
                              )}
                            </div>
                          ))
                        ) : (
                          <div className="flex items-center justify-center py-8 text-muted-foreground">
                            No code snippets found
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="llm" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>LLM Format</CardTitle>
                    <CardDescription>How the context is formatted for the LLM</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[600px]">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Query</h3>
                          <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre">
                            {result.query}
                          </pre>
                        </div>

                        <Separator />

                        <div>
                          <h3 className="text-lg font-semibold mb-2">Code Snippets</h3>
                          <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre">
                            {result.snippets?.map((snippet: any) => (
                              `File: ${snippet.filePath} (Lines ${snippet.startLine}-${snippet.endLine})\n` +
                              `Type: ${snippet.type}\n` +
                              `${snippet.symbols?.length ? `Symbols: ${snippet.symbols.join(', ')}\n` : ''}` +
                              `\n${snippet.content}\n\n`
                            )).join('---\n\n') || 'No snippets available'}
                          </pre>
                        </div>

                        <Separator />

                        <div>
                          <h3 className="text-lg font-semibold mb-2">Codebase Structure</h3>
                          <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre">
                            {Object.entries(result.codebaseStructure || {}).map(([key, value]: [string, any]) => (
                              `${key.toUpperCase()}:\n${(value as string[])?.join('\n')}\n`
                            )).join('\n') || 'No structure available'}
                          </pre>
                        </div>

                        {result.supabaseSchema && (
                          <>
                            <Separator />

                            <div>
                              <h3 className="text-lg font-semibold mb-2">Supabase Schema</h3>
                              <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre">
                                {`TABLES:\n${result.supabaseSchema.tables.map((table: any) => (
                                  `- ${table.table_name}\n  Columns: ${table.columns.map((col: any) =>
                                    `${col.column_name} (${col.data_type})`
                                  ).join(', ')}`
                                )).join('\n')}\n\n` +
                                `FUNCTIONS:\n${result.supabaseSchema.functions.map((func: any) =>
                                  `- ${func.name || func.slug || 'Unknown'}`
                                ).join('\n') || 'None'}\n`}
                              </pre>
                            </div>
                          </>
                        )}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="query" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Query</CardTitle>
                    <CardDescription>The natural language query that was processed</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="font-medium">{result.query}</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="plan" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Action Plan</CardTitle>
                    <CardDescription>AI-generated plan for implementing the requested changes</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {result.actionPlan ? (
                      <div className="space-y-6">
                        <div className="space-y-2">
                          <h3 className="text-lg font-semibold">Summary</h3>
                          <p>{result.actionPlan.summary}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant={result.actionPlan.complexity === 'simple' ? 'outline' :
                                   result.actionPlan.complexity === 'moderate' ? 'secondary' : 'destructive'}>
                              {result.actionPlan.complexity} complexity
                            </Badge>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h3 className="text-lg font-semibold">Implementation Steps</h3>
                          {result.actionPlan.steps.map((step: any, index: number) => (
                            <div key={index} className="border rounded-lg p-4">
                              <h4 className="font-medium mb-2">Step {index + 1}: {step.description}</h4>
                              <div className="space-y-2">
                                <h5 className="text-sm font-medium text-muted-foreground">File Changes:</h5>
                                <div className="grid grid-cols-1 gap-2">
                                  {step.fileChanges.map((change: any, changeIndex: number) => (
                                    <div key={changeIndex} className="flex items-center gap-2 text-sm">
                                      <Badge variant={change.action === 'create' ? 'default' :
                                             change.action === 'modify' ? 'secondary' : 'destructive'}>
                                        {change.action}
                                      </Badge>
                                      <span className="font-mono">{change.path}</span>
                                      <span className="text-muted-foreground">- {change.purpose}</span>
                                      <Badge variant="outline" className="ml-auto">
                                        {change.priority} priority
                                      </Badge>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        {result.actionPlan.considerations && result.actionPlan.considerations.length > 0 && (
                          <div className="space-y-2">
                            <h3 className="text-lg font-semibold">Considerations</h3>
                            <ul className="list-disc pl-5 space-y-1">
                              {result.actionPlan.considerations.map((consideration: any, index: number) => (
                                <li key={index}>{consideration}</li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {result.actionPlan.supabaseChanges && result.actionPlan.supabaseChanges.length > 0 && (
                          <div className="space-y-2">
                            <h3 className="text-lg font-semibold">Supabase Changes</h3>
                            <div className="space-y-2">
                              {result.actionPlan.supabaseChanges.map((change: any, index: number) => (
                                <div key={index} className="flex items-center gap-2 text-sm">
                                  <Badge variant={change.type === 'table' ? 'default' :
                                         change.type === 'function' ? 'secondary' : 'outline'}>
                                    {change.type}
                                  </Badge>
                                  <span>{change.description}</span>
                                  <Badge variant="outline" className="ml-auto">
                                    {change.priority} priority
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center py-8 text-muted-foreground">
                        No action plan generated
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="supabase" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Supabase Schema</CardTitle>
                    <CardDescription>Database tables, functions, and secrets from Supabase integration</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {result.supabaseSchema ? (
                      <Tabs defaultValue="tables">
                        <TabsList>
                          <TabsTrigger value="tables">Tables ({result.supabaseSchema.tables.length})</TabsTrigger>
                          <TabsTrigger value="functions">Functions ({result.supabaseSchema.functions.length})</TabsTrigger>
                          <TabsTrigger value="secrets">Secrets ({result.supabaseSchema.secrets.length})</TabsTrigger>
                        </TabsList>

                        <TabsContent value="tables" className="mt-4">
                          <ScrollArea className="h-[400px]">
                            <div className="space-y-4">
                              {result.supabaseSchema.tables.map((table: any, index: number) => (
                                <div key={index} className="border rounded-lg p-4">
                                  <h3 className="font-semibold mb-2">{table.table_name}</h3>
                                  <div className="space-y-1">
                                    {table.columns && table.columns.map((column: any, colIndex: number) => (
                                      <div key={colIndex} className="grid grid-cols-3 text-sm">
                                        <span className="font-mono">{column.column_name}</span>
                                        <span className="text-muted-foreground">{column.data_type}</span>
                                        <span className="text-muted-foreground">
                                          {column.constraints || column.is_nullable === 'NO' ? 'NOT NULL' : ''}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        </TabsContent>

                        <TabsContent value="functions" className="mt-4">
                          <ScrollArea className="h-[400px]">
                            <div className="space-y-4">
                              {result.supabaseSchema.functions.length > 0 ? (
                                result.supabaseSchema.functions.map((func: any, index: number) => (
                                  <div key={index} className="border rounded-lg p-4">
                                    <h3 className="font-semibold mb-2">{func.name || func.slug || 'Unknown function'}</h3>
                                    <div className="text-sm text-muted-foreground">
                                      {func.status && <div>Status: {func.status}</div>}
                                      {func.version && <div>Version: {func.version}</div>}
                                      {func.entrypoint && <div>Entrypoint: {func.entrypoint}</div>}
                                    </div>
                                  </div>
                                ))
                              ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                  No functions found
                                </div>
                              )}
                            </div>
                          </ScrollArea>
                        </TabsContent>

                        <TabsContent value="secrets" className="mt-4">
                          <ScrollArea className="h-[400px]">
                            <div className="space-y-4">
                              {result.supabaseSchema.secrets.length > 0 ? (
                                result.supabaseSchema.secrets.map((secret: any, index: number) => (
                                  <div key={index} className="border rounded-lg p-4">
                                    <h3 className="font-semibold mb-2">{secret.name}</h3>
                                    <div className="text-sm text-muted-foreground">
                                      Value: <span className="bg-muted px-2 py-1 rounded">REDACTED</span>
                                    </div>
                                  </div>
                                ))
                              ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                  No secrets found
                                </div>
                              )}
                            </div>
                          </ScrollArea>
                        </TabsContent>
                      </Tabs>
                    ) : (
                      <div className="flex items-center justify-center py-8 text-muted-foreground">
                        No Supabase integration detected
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="files" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Relevant Files</CardTitle>
                    <CardDescription>Files that are most relevant to your query</CardDescription>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ScrollArea className="h-[600px]">
                      <div className="p-6 space-y-6">
                        {result.relevantFiles?.map((file: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="font-semibold">{file.name}</h4>
                              <Badge variant="outline" className="capitalize">{file.metadata?.type}</Badge>
                            </div>

                            <div className="flex flex-wrap gap-2 mb-2">
                              <div className="text-sm">
                                <span className="font-semibold">Lines:</span> {file.metadata?.lineCount || 'N/A'}
                              </div>
                              {file.metadata?.hasJSX && (
                                <Badge variant="secondary">JSX</Badge>
                              )}
                              {file.metadata?.hasHooks && (
                                <Badge variant="secondary">Hooks</Badge>
                              )}
                              {file.metadata?.hasContext && (
                                <Badge variant="secondary">Context</Badge>
                              )}
                              {file.metadata?.hasStyles && (
                                <Badge variant="secondary">Styled</Badge>
                              )}
                            </div>

                            <Separator className="my-2" />

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
                              <div>
                                <p className="text-sm font-semibold">Exports:</p>
                                <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre-wrap">
                                  {file.metadata?.exports?.join(', ') || 'None'}
                                </pre>
                              </div>

                              <div>
                                <p className="text-sm font-semibold">Imports:</p>
                                <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre-wrap">
                                  {file.metadata?.imports?.slice(0, 5)?.join(', ') || 'None'}
                                  {(file.metadata?.imports?.length || 0) > 5 && '...'}
                                </pre>
                              </div>
                            </div>

                            <Separator className="my-2" />

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm font-semibold">Dependencies:</p>
                                <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre-wrap">
                                  {file.dependencies?.join(', ') || 'None'}
                                </pre>
                              </div>

                              <div>
                                <p className="text-sm font-semibold">Dependents:</p>
                                <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre-wrap">
                                  {file.dependents?.join(', ') || 'None'}
                                </pre>
                              </div>
                            </div>

                            <Separator className="my-2" />

                            <div className="mt-2">
                              <p className="text-sm font-semibold">Content Preview:</p>
                              <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre-wrap">
                                {file.content?.substring(0, 200)}
                                {file.content?.length > 200 ? '...' : ''}
                              </pre>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="structure" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Codebase Structure</CardTitle>
                    <CardDescription>Overview of the codebase organization</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {Object.entries(result.codebaseStructure || {}).map(([key, value]: [string, any]) => (
                        <div key={key} className="border rounded-lg p-4">
                          <h4 className="font-semibold capitalize mb-2">
                            {key} ({(value as string[])?.length || 0})
                          </h4>
                          <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto whitespace-pre-wrap h-[150px] overflow-y-auto">
                            {(value as string[])?.slice(0, 5)?.join('\n') || 'None'}
                            {(value as string[])?.length > 5 ? '\n...' : ''}
                          </pre>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="relationships" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>File Relationships</CardTitle>
                    <CardDescription>
                      How files are connected through imports, exports, dependencies, and dependents
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[400px] w-full">
                      <pre className="text-xs p-2 rounded-md overflow-x-auto whitespace-pre-wrap">
                        {JSON.stringify(result.relationships, null, 2)}
                      </pre>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
}
