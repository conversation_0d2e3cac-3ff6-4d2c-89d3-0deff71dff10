import {getMessagesByChatId} from "@/lib/db/queries";
import {convertToUIMessages} from "@/lib/utils";
import AdminChat from "@/components/admin/AdminChat";

export default async function AdminChatPage(props: {
    params: Promise<{ id: string }>
}) {

    const {id} = await props.params;

    const messages = await getMessagesByChatId({id})

    return (
        <div className="h-screen w-full overflow-hidden">
            <AdminChat initialMessages={convertToUIMessages(messages)} chatId={id}/>
        </div>
    );
}
