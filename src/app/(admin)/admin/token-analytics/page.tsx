import { redirect } from 'next/navigation';
import { auth } from '@/app/(auth)/auth';
import TokenAnalyticsDashboard from '@/components/admin/token-analytics/TokenAnalyticsDashboard';
import { getTokenAnalytics } from '@/app/actions/admin-token-actions';

export default async function TokenAnalyticsPage() {
  // Auth guard - only allow authenticated users in production
  const session = await auth();
  
  if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
    redirect('/');
  }
  
  // Pre-fetch initial data on the server
  const initialData = await getTokenAnalytics({
    page: 1,
    pageSize: 10
  });

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Token Analytics Dashboard</h1>
      <TokenAnalyticsDashboard initialData={initialData} />
    </div>
  );
}
