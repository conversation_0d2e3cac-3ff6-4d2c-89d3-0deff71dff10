import { redirect } from 'next/navigation';
import { auth } from '@/app/(auth)/auth';
import MessageTraceViewer from '@/components/admin/token-analytics/MessageTraceViewer';
import { getMessageTrace } from '@/app/actions/admin-token-actions';

interface MessageTracePageProps {
  params: {
    id: string;
  };
}

export default async function MessageTracePage({ params }: MessageTracePageProps) {
  // Auth guard - only allow authenticated users in production
  const session = await auth();
  
  if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
    redirect('/');
  }
  
  // Pre-fetch message trace data on the server
  const messageTrace = await getMessageTrace(params.id);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Message Trace</h1>
      <MessageTraceViewer messageTrace={messageTrace} />
    </div>
  );
}
