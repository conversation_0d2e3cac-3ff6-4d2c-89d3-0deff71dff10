'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft, ChevronRight, Search, Filter, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { getAllSubscriptions } from '@/lib/db/admin-queries';

// Types
interface Subscription {
  id: string;
  userId: string;
  status: string;
  planId: string;
  subscriptionId: string;
  credits: number;
  creditsUsed: number;
  isActive: boolean;
  provider: string;
  resetDate: string;
  createdAt: string;
  updatedAt: string;
  userName: string;
  userEmail: string;
  remainingCredits: number;
  percentUsed: number;
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  pageCount: number;
  totalCount: number;
}

export default function SubscriptionsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  
  // State
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 10,
    pageCount: 1,
    totalCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filters, setFilters] = useState<Record<string, string[]>>({});
  
  // Get current page from URL or default to 1
  const currentPage = Number(searchParams?.get('page') || 1);

  // Fetch subscriptions data
  const fetchSubscriptions = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/subscriptions?page=${currentPage}&pageSize=10&sortField=${sortField}&sortDirection=${sortDirection}&searchTerm=${searchTerm}${
        Object.keys(filters).length > 0 
          ? `&filters=${encodeURIComponent(JSON.stringify(filters))}` 
          : ''
      }`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscriptions');
      }
      
      const data = await response.json();
      setSubscriptions(data.data);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Initial fetch
  useEffect(() => {
    fetchSubscriptions();
  }, [currentPage, sortField, sortDirection, searchTerm, filters]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    router.push(`/admin/subscriptions?page=1`);
  };
  
  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Handle pagination
  const goToPage = (page: number) => {
    router.push(`/admin/subscriptions?page=${page}`);
  };
  
  // Handle filter change
  const handleFilterChange = (key: string, value: string) => {
    // If the value is one of our "all" options, remove the filter
    if (value === 'all_status' || value === 'all_plans') {
      const newFilters = { ...filters };
      delete newFilters[key];
      setFilters(newFilters);
    } else {
      setFilters(prev => ({
        ...prev,
        [key]: [value]
      }));
    }
  };
  
  // View token consumption for a subscription
  const viewTokenConsumption = (subscriptionId: string) => {
    router.push(`/admin/subscriptions/${subscriptionId}`);
  };
  
  // Render status badge
  const renderStatusBadge = (status: string) => {
    let variant = 'default';
    
    switch (status) {
      case 'active':
        variant = 'success';
        break;
      case 'inactive':
        variant = 'secondary';
        break;
      case 'cancelled':
        variant = 'destructive';
        break;
      case 'pending':
        variant = 'warning';
        break;
      default:
        variant = 'default';
    }
    
    return (
      <Badge variant={variant as any}>{status}</Badge>
    );
  };
  
  // Loading skeleton
  if (loading && subscriptions.length === 0) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Subscriptions</CardTitle>
          <CardDescription>
            Manage and view all user subscriptions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and filters */}
          <div className="flex items-center justify-between mb-4">
            <form onSubmit={handleSearch} className="flex items-center space-x-2">
              <Input
                placeholder="Search by name, email, or plan..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Button type="submit" size="sm" variant="secondary">
                <Search className="h-4 w-4 mr-1" /> Search
              </Button>
            </form>
            
            <div className="flex items-center space-x-2">
              <Select
                value={filters.status?.[0] || 'all_status'}
                onValueChange={(value) => handleFilterChange('status', value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_status">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
              
              <Select
                value={filters.planId?.[0] || 'all_plans'}
                onValueChange={(value) => handleFilterChange('planId', value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Plan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_plans">All Plans</SelectItem>
                  <SelectItem value="free">Free</SelectItem>
                  <SelectItem value="pro">Pro</SelectItem>
                  <SelectItem value="team">Team</SelectItem>
                </SelectContent>
              </Select>
              
              <Button onClick={fetchSubscriptions} size="sm" variant="outline">
                <RefreshCw className="h-4 w-4 mr-1" /> Refresh
              </Button>
            </div>
          </div>
          
          {/* Subscriptions table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('userName')}
                  >
                    User
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('planId')}
                  >
                    Plan
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('status')}
                  >
                    Status
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('credits')}
                  >
                    Credits
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('createdAt')}
                  >
                    Created
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('resetDate')}
                  >
                    Reset Date
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {subscriptions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      No subscriptions found
                    </TableCell>
                  </TableRow>
                ) : (
                  subscriptions.map((subscription) => (
                    <TableRow key={subscription.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {subscription.userName || 'Unknown'}
                            {subscription.userName && subscription.userEmail && (
                              <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full">
                                User
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">{subscription.userEmail}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{subscription.planId}</Badge>
                      </TableCell>
                      <TableCell>
                        {renderStatusBadge(subscription.status)}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            {subscription.creditsUsed} / {subscription.credits}
                          </div>
                          <Progress value={subscription.percentUsed} className="h-2" />
                        </div>
                      </TableCell>
                      <TableCell>
                        {subscription.createdAt 
                          ? format(new Date(subscription.createdAt), 'MMM d, yyyy') 
                          : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {subscription.resetDate 
                          ? format(new Date(subscription.resetDate), 'MMM d, yyyy') 
                          : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <Button 
                          size="sm" 
                          onClick={() => viewTokenConsumption(subscription.id)}
                        >
                          View Usage
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Pagination */}
          {pagination.pageCount > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} subscriptions
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-sm">
                  Page {pagination.page} of {pagination.pageCount}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(pagination.page + 1)}
                  disabled={pagination.page === pagination.pageCount}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
