'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft, ChevronRight, ArrowLeft, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { subDays } from 'date-fns';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

// Custom components
import { FinancialSummaryCard } from '@/components/admin/financial-summary-card';
import { TokenConsumptionTable } from '@/components/admin/token-consumption-table';
import { CachingBreakdown } from '@/components/admin/caching-breakdown';

// Types
interface TokenConsumption {
  id: string;
  model: string;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  chatId: string;
  projectId: string;
  messageId: string;
  userId: string;
  createdAt: string;
  inputCost: number;
  outputCost: number;
  totalCost: number;
  creditsConsumed: number;
  discountedCredits: number;
  discountReason: string;
  cachingDiscount?: number;
  cacheDiscountPercent?: number;
  subtotal?: number;
}

interface CachingBreakdown {
  hasCaching: string;
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  cachingSavings: number;
}

interface ConsumptionSummary {
  totalPromptTokens: number;
  totalCompletionTokens: number;
  totalTokensSum: number;
  totalCostSum: number;
  totalCreditsConsumed: number;
  totalDiscountedCredits: number;
  totalCachingDiscount: number;
  requestCount: number;
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  pageCount: number;
  totalCount: number;
}

interface SubscriptionDetails {
  id: string;
  userId: string;
  status: string;
  planId: string;
  subscriptionId: string;
  credits: number;
  creditsUsed: number;
  isActive: boolean;
  provider: string;
  resetDate: string;
  createdAt: string;
  updatedAt: string;
  userName: string;
  userEmail: string;
  remainingCredits: number;
  percentUsed: number;
}

export default function SubscriptionDetailPage() {
  const router = useRouter();
  const params = useParams() || {};
  const { data: session } = useSession();
  const subscriptionId = params.id as string;
  
  // State
  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null);
  const [tokenConsumption, setTokenConsumption] = useState<TokenConsumption[]>([]);
  const [summary, setSummary] = useState<ConsumptionSummary | null>(null);
  const [cachingBreakdown, setCachingBreakdown] = useState<CachingBreakdown[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 10,
    pageCount: 1,
    totalCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 30),
    to: new Date(),
  });
  
  // Fetch subscription details
  const fetchSubscriptionDetails = async () => {
    try {
      const response = await fetch(`/api/admin/subscriptions/${subscriptionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscription details');
      }
      
      const data = await response.json();
      setSubscription(data);
    } catch (error) {
      console.error('Error fetching subscription details:', error);
    }
  };
  
  // Fetch token consumption data
  const fetchTokenConsumption = async (page = 1) => {
    setLoading(true);
    try {
      // Prepare query parameters
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('pageSize', pagination.pageSize.toString());
      params.append('sortField', sortField);
      params.append('sortDirection', sortDirection);
      
      if (dateRange.from) {
        params.append('startDate', dateRange.from.toISOString());
      }
      
      if (dateRange.to) {
        params.append('endDate', dateRange.to.toISOString());
      }
      
      const response = await fetch(`/api/admin/subscriptions/${subscriptionId}/consumption?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch token consumption');
      }
      
      const data = await response.json();
      setTokenConsumption(data.data);
      setSummary(data.summary);
      setCachingBreakdown(data.cachingBreakdown);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching token consumption:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Initial data fetch
  useEffect(() => {
    fetchSubscriptionDetails();
    fetchTokenConsumption();
  }, [subscriptionId]);
  
  // Handle sort
  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
    fetchTokenConsumption(1);
  };
  
  // Handle pagination
  const goToPage = (page: number) => {
    fetchTokenConsumption(page);
  };
  
  // Go back to subscriptions list
  const goBack = () => {
    router.push('/admin/subscriptions');
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };
  
  // Format number with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };
  
  // Format cost as currency
  const formatCost = (cost: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(cost);
  };
  
  // Handle date range selection
  const handleDateRangeSelect = (days: number) => {
    const to = new Date();
    const from = subDays(to, days);
    setDateRange({ from, to });
    fetchTokenConsumption(1);
  };
  
  return (
    <div className="container max-w-7xl mx-auto py-4 space-y-4">
      {/* Subscription Header Card */}
      <Card className="shadow-sm">
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={goBack} 
              className="mb-2"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Subscriptions
            </Button>
            <CardTitle className="text-xl flex items-center gap-2">
              {subscription ? (
                <>
                  <span>{subscription.userName || 'User'}</span>
                  {subscription.isActive ? (
                    <Badge className="ml-2">Active</Badge>
                  ) : (
                    <Badge variant="outline" className="ml-2">Inactive</Badge>
                  )}
                </>
              ) : (
                <Skeleton className="h-6 w-32" />
              )}
            </CardTitle>
            {subscription ? (
              <CardDescription className="text-xs flex flex-col sm:flex-row sm:gap-4 mt-1">
                <span><strong>Email:</strong> {subscription.userEmail}</span>
                <span><strong>Plan:</strong> {subscription.planId}</span>
                <span><strong>Provider:</strong> {subscription.provider}</span>
                <span><strong>Created:</strong> {formatDate(subscription.createdAt)}</span>
              </CardDescription>
            ) : (
              <Skeleton className="h-4 w-64 mt-2" />
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => fetchTokenConsumption(pagination.page)}
            >
              <RefreshCw className="mr-2 h-3 w-3" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        
        {/* Date Range Selector */}
        <CardContent className="pb-3 pt-0">
          <div className="flex flex-wrap items-center gap-2 text-xs">
            <span className="font-medium">Date Range:</span>
            <Button 
              variant={dateRange.from && dateRange.to && (dateRange.to.getTime() - dateRange.from.getTime()) === 7 * 24 * 60 * 60 * 1000 ? "default" : "outline"} 
              size="sm"
              onClick={() => handleDateRangeSelect(7)}
              className="h-7 text-xs"
            >
              7 Days
            </Button>
            <Button 
              variant={dateRange.from && dateRange.to && (dateRange.to.getTime() - dateRange.from.getTime()) === 30 * 24 * 60 * 60 * 1000 ? "default" : "outline"} 
              size="sm"
              onClick={() => handleDateRangeSelect(30)}
              className="h-7 text-xs"
            >
              30 Days
            </Button>
            <Button 
              variant={dateRange.from && dateRange.to && (dateRange.to.getTime() - dateRange.from.getTime()) === 90 * 24 * 60 * 60 * 1000 ? "default" : "outline"} 
              size="sm"
              onClick={() => handleDateRangeSelect(90)}
              className="h-7 text-xs"
            >
              90 Days
            </Button>
            
            <div className="ml-auto flex items-center gap-2">
              <Select
                value={pagination.pageSize.toString()}
                onValueChange={(value) => {
                  setPagination(prev => ({
                    ...prev,
                    pageSize: parseInt(value),
                    page: 1
                  }));
                  fetchTokenConsumption(1);
                }}
              >
                <SelectTrigger className="h-7 w-[70px] text-xs">
                  <SelectValue placeholder="10" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-xs text-muted-foreground">per page</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Financial Summary */}
      {summary && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
          <FinancialSummaryCard
            title="Total Requests"
            value={formatNumber(summary.requestCount)}
          />
          <FinancialSummaryCard
            title="Total Tokens"
            value={formatNumber(summary.totalTokensSum)}
            subValue={`Input: ${formatNumber(summary.totalPromptTokens)} | Output: ${formatNumber(summary.totalCompletionTokens)}`}
          />
          <FinancialSummaryCard
            title="Credits Used"
            value={formatNumber(summary.totalCreditsConsumed)}
          />
          <FinancialSummaryCard
            title="Credits Saved"
            value={formatNumber(summary.totalDiscountedCredits)}
            subValue={summary.totalDiscountedCredits > 0 ? 
              `${Math.round((summary.totalDiscountedCredits / (summary.totalCreditsConsumed + summary.totalDiscountedCredits)) * 100)}% savings ratio` : undefined}
            subValueColor="text-red-500 text-xs"
            isNegative={true}
          />
          <FinancialSummaryCard
            title="Cache Savings"
            value={formatCost(summary.totalCachingDiscount)}
            isNegative={true}
          />
          <FinancialSummaryCard
            title="Total Cost"
            value={formatCost(summary.totalCostSum)}
          />
        </div>
      )}
      
      {/* Main Content Tabs */}
      <Card>
        <CardContent className="p-0">
          <Tabs defaultValue="history" className="w-full">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center px-4 pt-4 pb-2 border-b">
              <TabsList className="mb-2 sm:mb-0">
                <TabsTrigger value="history">Usage History</TabsTrigger>
                <TabsTrigger value="caching">Caching Analysis</TabsTrigger>
              </TabsList>
              
              <div className="flex items-center gap-2">
                <Select
                  value={sortField}
                  onValueChange={(value) => {
                    setSortField(value);
                    setSortDirection('desc');
                    fetchTokenConsumption(1);
                  }}
                >
                  <SelectTrigger className="h-8 w-[110px] text-xs">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="createdAt">Date</SelectItem>
                    <SelectItem value="totalTokens">Tokens</SelectItem>
                    <SelectItem value="totalCost">Cost</SelectItem>
                    <SelectItem value="cacheDiscountPercent">Cache %</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select
                  value={sortDirection}
                  onValueChange={(value) => {
                    setSortDirection(value as 'asc' | 'desc');
                    fetchTokenConsumption(1);
                  }}
                >
                  <SelectTrigger className="h-8 w-[80px] text-xs">
                    <SelectValue placeholder="Order" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Ascending</SelectItem>
                    <SelectItem value="desc">Descending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <TabsContent value="history" className="p-0 sm:p-4">
              {loading ? (
                <div className="p-4">
                  <Skeleton className="h-64 w-full" />
                </div>
              ) : (
                <>
                  <TokenConsumptionTable 
                    data={tokenConsumption} 
                    onSort={handleSort}
                    sortField={sortField}
                    sortDirection={sortDirection}
                  />
                  
                  {/* Pagination */}
                  {pagination.pageCount > 1 && (
                    <div className="flex items-center justify-between p-4 text-xs">
                      <div className="text-muted-foreground">
                        Showing {((pagination.page - 1) * pagination.pageSize) + 1} to {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} records
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(1)}
                          disabled={pagination.page === 1}
                          className="h-7 w-7 p-0"
                        >
                          <span className="sr-only">First page</span>
                          <ChevronLeft className="h-4 w-4" />
                          <ChevronLeft className="h-4 w-4 -ml-2" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(pagination.page - 1)}
                          disabled={pagination.page === 1}
                          className="h-7 w-7 p-0"
                        >
                          <span className="sr-only">Previous page</span>
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <span className="text-xs">
                          Page {pagination.page} of {pagination.pageCount}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(pagination.page + 1)}
                          disabled={pagination.page === pagination.pageCount}
                          className="h-7 w-7 p-0"
                        >
                          <span className="sr-only">Next page</span>
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => goToPage(pagination.pageCount)}
                          disabled={pagination.page === pagination.pageCount}
                          className="h-7 w-7 p-0"
                        >
                          <span className="sr-only">Last page</span>
                          <ChevronRight className="h-4 w-4" />
                          <ChevronRight className="h-4 w-4 -ml-2" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </TabsContent>
            
            <TabsContent value="caching" className="p-4">
              <CachingBreakdown 
                data={cachingBreakdown} 
                loading={loading} 
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
