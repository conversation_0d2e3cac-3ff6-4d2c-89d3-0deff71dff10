'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Search, Send, RefreshCw, Plus, Trash2, Edit, Mail } from 'lucide-react';
import { EmailTemplateEditor } from '@/components/admin/email-template-editor';

interface User {
  id: string;
  name: string;
  email: string;
  provider: string;
  createdAt: string;
  lastActive?: string;
  subscriptionStatus?: string;
  hasSubscription?: boolean;
  selected?: boolean;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  createdAt: string;
}

export default function EmailCampaignsPage() {
  // const { session } = useAuth();
  // Using Sonner toast directly
  
  // State for users
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Advanced filtering options
  const [activityFilter, setActivityFilter] = useState('all');
  const [providerFilter, setProviderFilter] = useState('all');
  const [subscriptionFilter, setSubscriptionFilter] = useState('all');
  const [filterMenuOpen, setFilterMenuOpen] = useState(false);
  
  // State for email
  const [subject, setSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [sendingEmail, setSendingEmail] = useState(false);
  
  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
    fetchTemplates();
  }, []);
  
  // Fetch users with filter
  const fetchUsers = async () => {
    setLoadingUsers(true);
    try {
      // Build query URL with all filter parameters
      const queryParams = new URLSearchParams();
      if (activityFilter !== 'all') queryParams.append('activity', activityFilter);
      if (providerFilter !== 'all') queryParams.append('provider', providerFilter);
      if (subscriptionFilter !== 'all') queryParams.append('subscription', subscriptionFilter);
      if (searchTerm) queryParams.append('search', searchTerm);
      
      const queryString = queryParams.toString();
      const url = `/api/admin/users${queryString ? `?${queryString}` : ''}`;
      
      // Log the request URL for debugging
      console.log('Fetching users with URL:', url);
      console.log('Filter parameters:', {
        activity: activityFilter,
        provider: providerFilter,
        subscription: subscriptionFilter,
        search: searchTerm
      });
      
      const response = await fetch(url, {
        // Add cache: 'no-store' to prevent caching issues
        cache: 'no-store'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.status}`);
      }
      
      const data = await response.json();
      if (!data.users || !Array.isArray(data.users)) {
        throw new Error('Invalid response format');
      }
      
      setUsers(data.users);
      toast.success(`Loaded ${data.users.length} users`);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error(`Failed to fetch users: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Set empty array to prevent undefined errors
      setUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  };
  
  // Fetch email templates
  const fetchTemplates = async () => {
    setLoadingTemplates(true);
    try {
      const response = await fetch('/api/admin/email-templates');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch email templates: ${response.status}`);
      }
      
      const data = await response.json();
      if (!data.templates || !Array.isArray(data.templates)) {
        throw new Error('Invalid template response format');
      }
      
      setTemplates(data.templates);
    } catch (error) {
      console.error('Error fetching email templates:', error);
      toast.error(`Failed to fetch templates: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Set default templates to prevent errors
      setTemplates([
        {
          id: 'default-template-1',
          name: 'Welcome Back',
          subject: 'We miss you!',
          content: '<p>Hi there, we noticed you haven\'t been using our platform lately...</p>',
          createdAt: new Date().toISOString()
        }
      ]);
    } finally {
      setLoadingTemplates(false);
    }
  };
  
  // Handle template selection
  const handleTemplateChange = (templateId: string) => {
    if (!templateId) {
      console.warn('Empty template ID received');
      return;
    }
    
    setSelectedTemplate(templateId);
    
    if (templateId && templateId !== 'custom') {
      const template = templates.find(t => t.id === templateId);
      if (template) {
        setSubject(template.subject || '');
        setEmailContent(template.content || '');
        toast.info(`Loaded template: ${template.name}`);
      } else {
        console.warn(`Template with ID ${templateId} not found`);
        toast.error('Template not found');
      }
    } else if (templateId === 'custom') {
      // Reset content for custom email
      setSubject('');
      setEmailContent('');
      toast.info('Using custom template');
    }
  };
  
  // Handle user selection
  const handleUserSelection = (userId: string) => {
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.id === userId 
          ? { ...user, selected: !user.selected } 
          : user
      )
    );
    
    // Update selected users
    const user = users.find(u => u.id === userId);
    if (user) {
      if (user.selected) {
        setSelectedUsers(prev => prev.filter(u => u.id !== userId));
      } else {
        setSelectedUsers(prev => [...prev, { ...user, selected: true }]);
      }
    }
  };
  
  // Handle select all users
  const handleSelectAllUsers = () => {
    const allSelected = users.every(user => user.selected);
    
    setUsers(prevUsers => 
      prevUsers.map(user => ({ ...user, selected: !allSelected }))
    );
    
    if (allSelected) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers([...users.map(user => ({ ...user, selected: true }))]);
    }
  };
  
  // Users are already filtered by the API based on searchTerm
  // This is just for any additional client-side filtering if needed
  const filteredUsers = users;
  
  // Debug the current state
  useEffect(() => {
    console.log('Current state:', {
      userCount: users.length,
      selectedCount: selectedUsers.length,
      filters: {
        activity: activityFilter,
        provider: providerFilter,
        subscription: subscriptionFilter,
        search: searchTerm
      }
    });
  }, [users, selectedUsers, activityFilter, providerFilter, subscriptionFilter, searchTerm]);
  
  // Send email to selected users
  const sendEmail = async () => {
    if (selectedUsers.length === 0) {
      toast.error('Please select at least one user');
      return;
    }
    
    if (!subject || !emailContent) {
      toast.error('Please provide both subject and content');
      return;
    }
    
    setSendingEmail(true);
    toast.info(`Sending emails to ${selectedUsers.length} users...`);
    
    try {
      const response = await fetch('/api/admin/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          users: selectedUsers,
          subject,
          content: emailContent,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Failed to send emails: ${response.status} ${errorData.error || ''}`
        );
      }
      
      const data = await response.json();
      toast.success(`Successfully sent ${data.sent || 0} emails`);
      
      // Reset selection
      setUsers(prevUsers => prevUsers.map(user => ({ ...user, selected: false })));
      setSelectedUsers([]);
      
      // Reset content if using custom template
      if (selectedTemplate === 'custom') {
        setSubject('');
        setEmailContent('');
      }
    } catch (error) {
      console.error('Error sending emails:', error);
      toast.error(`Failed to send emails: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setSendingEmail(false);
    }
  };
  
  return (
    <div className="container max-w-7xl mx-auto py-4 space-y-4">
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-xl">Email Campaigns</CardTitle>
          <CardDescription>
            Send targeted emails to users to reduce churn and increase engagement
          </CardDescription>
        </CardHeader>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* User Selection Panel */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-lg">Select Recipients</CardTitle>
                <CardDescription>
                  Choose users to send the email to
                </CardDescription>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => setFilterMenuOpen(!filterMenuOpen)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon></svg>
                  Filters
                </Button>
                <Button 
                  variant="outline" 
                  onClick={fetchUsers}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"></path><path d="M16 21h5v-5"></path></svg>
                  Refresh
                </Button>
              </div>
            </div>
            
            {filterMenuOpen && (
              <div className="mt-4 p-4 border rounded-md bg-muted/20 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Activity Filter */}
                  <div>
                    <Label htmlFor="activity-filter">User Activity</Label>
                    <Select
                      value={activityFilter}
                      onValueChange={setActivityFilter}
                    >
                      <SelectTrigger id="activity-filter">
                        <SelectValue placeholder="Filter by activity" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="active">Active Users</SelectItem>
                        <SelectItem value="inactive">Inactive Users</SelectItem>
                        <SelectItem value="churned">Churned Users</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Provider Filter */}
                  <div>
                    <Label htmlFor="provider-filter">Authentication</Label>
                    <Select
                      value={providerFilter}
                      onValueChange={(value) => {
                        console.log('Provider filter changed to:', value);
                        setProviderFilter(value);
                      }}
                    >
                      <SelectTrigger id="provider-filter">
                        <SelectValue placeholder="Filter by auth provider" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Providers</SelectItem>
                        <SelectItem value="google">Google Auth</SelectItem>
                        <SelectItem value="credentials">Email Auth</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Subscription Filter */}
                  <div>
                    <Label htmlFor="subscription-filter">Subscription</Label>
                    <Select
                      value={subscriptionFilter}
                      onValueChange={setSubscriptionFilter}
                    >
                      <SelectTrigger id="subscription-filter">
                        <SelectValue placeholder="Filter by subscription" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="subscribed">Subscribed</SelectItem>
                        <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Label htmlFor="search" className="flex-shrink-0">Search:</Label>
                  <div className="relative w-full">
                    <Input
                      id="search"
                      placeholder="Search by name or email"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pr-10"
                    />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="absolute right-0 top-0 h-full"
                      onClick={() => setSearchTerm('')}
                      disabled={!searchTerm}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 6 6 18"></path><path d="M6 6 18 18"></path></svg>
                    </Button>
                  </div>
                  <Button 
                    onClick={() => {
                      console.log('Search button clicked with filters:', {
                        activity: activityFilter,
                        provider: providerFilter,
                        subscription: subscriptionFilter,
                        search: searchTerm
                      });
                      // Force a fresh fetch with current filters
                      fetchUsers();
                      // Show toast to confirm filters are being applied
                      toast.info(`Applying filters: ${providerFilter !== 'all' ? providerFilter + ' auth' : 'all auth types'}${activityFilter !== 'all' ? ', ' + activityFilter + ' users' : ''}`);
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
                    Search
                  </Button>
                </div>
                
                <div className="flex justify-between">
                  <div className="text-sm text-muted-foreground">
                    {users.length} users found
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      setActivityFilter('all');
                      setProviderFilter('all');
                      setSubscriptionFilter('all');
                      setSearchTerm('');
                      fetchUsers();
                    }}
                  >
                    Reset Filters
                  </Button>
                </div>
              </div>
            )}
          </CardHeader>
          
          <CardContent className="p-0">
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-10">
                      <Checkbox 
                        checked={users.length > 0 && users.every(user => user.selected)}
                        onCheckedChange={handleSelectAllUsers}
                      />
                    </TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loadingUsers ? (
                    Array.from({ length: 5 }).map((_, i) => (
                      <TableRow key={i}>
                        <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Skeleton className="h-4 w-32" />
                            <Skeleton className="h-3 w-24" />
                          </div>
                        </TableCell>
                        <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      </TableRow>
                    ))
                  ) : filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-4 text-sm text-muted-foreground">
                        No users found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map(user => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Checkbox 
                            checked={user.selected}
                            onCheckedChange={() => handleUserSelection(user.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{user.name || 'Unnamed User'}</div>
                          <div className="text-xs text-muted-foreground">{user.email}</div>
                          <div className="text-xs mt-1">
                            <Badge 
                              variant={user.provider === 'google' ? 'default' : 'outline'} 
                              className="mr-1"
                              style={{
                                backgroundColor: user.provider === 'google' ? '#4285F4' : undefined,
                                color: user.provider === 'google' ? 'white' : undefined
                              }}
                            >
                              {user.provider === 'google' ? 'Google' : 'Email'}
                            </Badge>
                            {user.hasSubscription && (
                              <Badge variant="secondary" className="mr-1">
                                Subscribed
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={user.subscriptionStatus === 'active' ? 'default' : 'outline'}
                          >
                            {user.subscriptionStatus || 'Unknown'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
        
        {/* Email Composition Panel */}
        <Card className="md:col-span-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Compose Email</CardTitle>
            <CardDescription>
              Create your email or select a template
            </CardDescription>
            
            <Select
              value={selectedTemplate}
              onValueChange={handleTemplateChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem key="custom-option" value="custom">Custom Email</SelectItem>
                {templates.map(template => (
                  <SelectItem 
                    key={template.id} 
                    value={template.id || `template-${template.name}`}
                  >
                    {template.name || 'Unnamed Template'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                placeholder="Email subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="content">Email Content</Label>
              <EmailTemplateEditor 
                value={emailContent} 
                onChange={setEmailContent} 
              />
            </div>
            
            <div className="pt-4 flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                Sending to {selectedUsers.length} recipient(s)
              </div>
              
              <Button 
                onClick={sendEmail}
                disabled={sendingEmail || selectedUsers.length === 0 || !subject || !emailContent}
              >
                {sendingEmail ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Send Email
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
