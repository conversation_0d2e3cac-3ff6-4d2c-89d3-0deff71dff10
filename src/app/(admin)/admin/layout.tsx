'use client';

import { useSession } from 'next-auth/react';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

  useEffect(() => {
    // Only make the authorization decision once the session is loaded
    if (status === 'loading') return;

    // Check if user is authenticated and has the correct email
    const authorized = 
      status === 'authenticated' && 
      session?.user?.email === '<EMAIL>';
    
    setIsAuthorized(authorized);
    
    // Redirect to home if not authorized
    if (!authorized) {
      router.push('/');
    }
  }, [session, status, router, pathname]);

  // Show loading state while checking authorization
  if (status === 'loading' || isAuthorized === null) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Checking authorization...</p>
        </div>
      </div>
    );
  }

  // If authorized, render the children
  if (isAuthorized) {
    return <>{children}</>;
  }

  // This should not be visible as we redirect, but as a fallback
  return null;
}
