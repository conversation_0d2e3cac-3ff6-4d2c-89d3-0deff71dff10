'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Loader2, Search } from 'lucide-react';
import Link from 'next/link';
import ErrorCategoriesChart from './error-categories-chart';

// Types for error data
interface ErrorData {
  id: string;
  chatId: string;
  projectId: string;
  userId: string;
  createdAt: string;
  category: string;
  content: any;
  snippet: string;
}

interface ErrorAnalyticsData {
  errors: ErrorData[];
  categoryCounts: Record<string, number>;
  errorsByDate: Record<string, number>;
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// Format category name for display
function formatCategoryName(category: string): string {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

// Generate a consistent hue value from a string for coloring
function hashStringToHue(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  return hash % 360; // Hue value between 0-359
}

export default function ErrorAnalyticsPage() {
  // State for filters and data
  const [timeRange, setTimeRange] = useState('30');
  const [category, setCategory] = useState('ALL');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  
  // State for data and loading
  const [data, setData] = useState<ErrorAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch data function
  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams({
        timeRange,
        page: currentPage.toString(),
        pageSize: pageSize.toString(),
      });
      
      if (category && category !== 'ALL') {
        queryParams.append('category', category);
      }
      
      if (searchQuery) {
        queryParams.append('search', searchQuery);
      }
      
      const response = await fetch(`/api/admin/error-analytics?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.statusText}`);
      }
      
      const result = await response.json();
      setData(result);
    } catch (err) {
      console.error('Error fetching error analytics data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch data on initial load and when filters change
  useEffect(() => {
    fetchData();
  }, [timeRange, category, currentPage, pageSize]);
  
  // Handle search
  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    fetchData();
  };
  
  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };
  
  // Render loading state
  if (loading && !data) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading error analytics...</p>
        </div>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Card className="bg-destructive/10">
          <CardHeader>
            <CardTitle>Error Loading Data</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={fetchData} className="mt-4">Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Error Analytics Dashboard</h1>
      
      {/* Error Categories Chart */}
      <ErrorCategoriesChart timeRange={timeRange} />
      
      {/* Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter error data by time range, category, or search term</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="w-full md:w-auto">
              <label className="text-sm font-medium mb-1 block">Time Range</label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="14">Last 14 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="w-full md:w-auto">
              <label className="text-sm font-medium mb-1 block">Error Category</label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className="w-[220px]">
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All categories</SelectItem>
                  <SelectItem value="DATABASE_ERROR">Database Error</SelectItem>
                  <SelectItem value="UI_ERROR">UI Error</SelectItem>
                  <SelectItem value="MODULE_ERROR">Module Error</SelectItem>
                  <SelectItem value="CODE_ERROR">Code Error</SelectItem>
                  <SelectItem value="OTHER_ERROR">Other Errors</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="w-full md:w-auto flex-1">
              <label className="text-sm font-medium mb-1 block">Search</label>
              <div className="flex gap-2">
                <Input 
                  placeholder="Search error messages..." 
                  value={searchQuery} 
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button onClick={handleSearch}>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Error Messages Table */}
      <Card>
        <CardHeader>
          <CardTitle>Error Messages</CardTitle>
          <CardDescription>
            Showing {data?.errors.length || 0} of {data?.pagination.total || 0} errors
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table className="text-xs">
              <TableHeader>
                <TableRow>
                  <TableHead className="py-2">Date</TableHead>
                  <TableHead className="py-2">Category</TableHead>
                  <TableHead className="py-2 w-1/2">Error Message</TableHead>
                  <TableHead className="py-2">Project</TableHead>
                  <TableHead className="py-2">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.errors.map((error) => (
                  <TableRow key={error.id} className="hover:bg-muted/50">
                    <TableCell>
                      {new Date(error.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <span className="px-2 py-1 rounded text-xs font-medium" 
                        style={{
                          backgroundColor: `hsl(${hashStringToHue(error.category)}, 70%, 90%)`,
                          color: `hsl(${hashStringToHue(error.category)}, 70%, 30%)`
                        }}>
                        {formatCategoryName(error.category)}
                      </span>
                    </TableCell>
                    <TableCell className="max-w-2xl whitespace-normal break-words">
                      {error.snippet}
                    </TableCell>
                    <TableCell>
                      <span>{error.projectId.substring(0, 8)}...</span>
                    </TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/projects/${error.projectId}/chats/${error.chatId}`}>
                          View Chat
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                
                {(!data?.errors || data.errors.length === 0) && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      No errors found matching your criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Pagination and Page Size Controls */}
          {data && (
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm">Show</span>
                <Select value={pageSize.toString()} onValueChange={(value) => {
                  setPageSize(parseInt(value));
                  setCurrentPage(1); // Reset to first page when changing page size
                }}>
                  <SelectTrigger className="w-[80px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm">entries</span>
              </div>
              
              {data.pagination.totalPages > 1 && (
                <div className="flex items-center space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {currentPage} of {data.pagination.totalPages}
                  </span>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === data.pagination.totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
          
          {/* Summary */}
          {data && (
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Total Errors</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{data.pagination.total || 0}</p>
                  <p className="text-sm text-muted-foreground">
                    In the last {timeRange} days
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Most Common Error</CardTitle>
                </CardHeader>
                <CardContent>
                  {data && Object.entries(data.categoryCounts).length > 0 ? (
                    <>
                      <p className="text-xl font-bold">
                        {formatCategoryName(
                          Object.entries(data.categoryCounts)
                            .sort((a, b) => b[1] - a[1])[0][0]
                        )}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {Object.entries(data.categoryCounts)
                          .sort((a, b) => b[1] - a[1])[0][1]} occurrences
                      </p>
                    </>
                  ) : (
                    <p className="text-muted-foreground">No data available</p>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Error Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">
                    {data && data.pagination.total > 0
                      ? `${((data.pagination.total / parseInt(timeRange)) * 100 / 100).toFixed(1)}`
                      : '0'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Avg. errors per day
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
