import { z } from 'zod';
import {generateObject, Message} from 'ai';
import { customModel } from "@/lib/ai";
import type { CoreUserMessage } from 'ai';

// Define schema for project attributes
const ProjectAttributesSchema = z.object({
    appName: z.string().describe("Name of the mobile app (e.g., 'Fitness Tracker', 'Recipe Master')"),
    appIdentifier: z.string().describe("Unique identifier for the app, lowercase with no spaces (e.g., 'fitnesstracker', 'recipemaster')"),
    description: z.string().describe("Brief description of the app, under 150 characters"),
    primaryColor: z.string().describe("Primary brand color in hex format (e.g., #FF5733)"),
});

/**
 * Generates project attributes based on user input using AI
 * @param message - The user message containing project requirements
 * @returns Generated project attributes
 */
export async function generateProjectAttributes({
    messages,
}: {
    messages: Message[];
}): Promise<z.infer<typeof ProjectAttributesSchema> & { bundleIdentifier: string, packageName: string }> {
    const result = await generateObject({
        schema: ProjectAttributesSchema,
        model: customModel('gpt-4o-mini'),
        system: `
            You will generate project attributes for a mobile app based on the user's description.
            
            RULES FOR APP NAME:
            - The appName should be concise (2-3 words), memorable, and relevant to the app's purpose
            - Use title case (e.g., "Fitness Tracker", "Recipe Master", "Task Flow")
            - Avoid generic names like "My App" or "Cool App"
            - Do not include words like "App", "Mobile", or "Application" in the name
            - Examples of good app names: "Meal Planner", "Budget Buddy", "Workout Pro", "Mind Journal"
            
            RULES FOR APP IDENTIFIER:
            - The appIdentifier should be a simple, lowercase string with no spaces or special characters
            - It should be derived from the app name but simplified
            - Examples: 
              * For "Fitness Tracker" → "fitnesstracker"
              * For "Recipe Master" → "recipemaster"
              * For "Task Flow" → "taskflow"
            
            OTHER RULES:
            - The description should be clear and under 150 characters
            - The primaryColor should be a valid hex color code that matches the app's theme and purpose
            - For fitness apps, consider greens (#4CAF50) or blues (#2196F3)
            - For food apps, consider reds (#F44336) or oranges (#FF9800)
            - For productivity apps, consider blues (#2196F3) or purples (#9C27B0)
        `,
        prompt: JSON.stringify(messages.slice(0, 20).map(message => message.content).join('\n')),
    });

    // Construct bundle identifier and package name using life.magically as the base
    const appIdentifier = result.object.appIdentifier;
    const bundleIdentifier = `life.magically.${appIdentifier}`;
    const packageName = bundleIdentifier;

    return {
        ...result.object,
        bundleIdentifier,
        packageName
    };
}
