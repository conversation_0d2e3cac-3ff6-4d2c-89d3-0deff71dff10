import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {PlusIcon, SettingsIcon, Smartphone} from "lucide-react";
import Image from "next/image";
import ProjectAppIcon from "@/components/project/ProjectAppIcon";

interface ProjectHeaderProps {
  project: any;
  projectId: string;
}

export const ProjectHeader = ({ project, projectId }: ProjectHeaderProps) => (
  <div className="flex flex-col space-y-2 md:flex-row md:justify-between md:items-center pb-4 border-b">
    <div className="flex gap-x-4">
      <ProjectAppIcon project={project}/>
      <div>
        <h1 className="text-2xl font-semibold">{project.appName}</h1>
        <p className="text-sm text-muted-foreground">{project.slug}</p>
      </div>
    </div>
    <div className="flex items-center space-x-2">
      <Button asChild size="sm" variant="outline">
        <Link href={`/projects/${projectId}/settings`}>
          <SettingsIcon className="h-4 w-4 mr-1" />
          Settings
        </Link>
      </Button>
      <Button asChild size="sm">
        <Link href={`/projects/${projectId}/chats/new`}>
          <PlusIcon className="h-4 w-4 mr-1" />
          New Chat
        </Link>
      </Button>
    </div>
  </div>
);

export default ProjectHeader;
