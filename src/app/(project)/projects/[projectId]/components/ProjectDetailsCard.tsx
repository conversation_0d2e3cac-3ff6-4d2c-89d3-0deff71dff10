import { formatDistanceToNow } from "date-fns";

interface ProjectDetailsCardProps {
  project: any;
}

export const ProjectDetailsCard = ({ project }: ProjectDetailsCardProps) => (
  <div className="bg-card rounded-md border p-4">
    <h3 className="text-sm font-medium mb-2">Project Details</h3>
    <div className="text-sm space-y-2">
      <div className="flex justify-between">
        <span className="text-muted-foreground">Bundle ID</span>
        <span className="font-mono text-xs">{project.bundleIdentifier}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-muted-foreground">Package Name</span>
        <span className="font-mono text-xs">{project.packageName}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-muted-foreground">Created</span>
        <span>{formatDistanceToNow(new Date(project.createdAt), {addSuffix: true})}</span>
      </div>
      {project.updatedAt && (
        <div className="flex justify-between">
          <span className="text-muted-foreground">Last Updated</span>
          <span>{formatDistanceToNow(new Date(project.updatedAt), {addSuffix: true})}</span>
        </div>
      )}
    </div>
  </div>
);

export default ProjectDetailsCard;
