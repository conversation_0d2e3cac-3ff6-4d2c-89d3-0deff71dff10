import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { RocketIcon } from "lucide-react";

interface DeploymentSectionProps {
  projectId: string;
}

export const DeploymentSection = ({ projectId }: DeploymentSectionProps) => (
  <div className="rounded-md border">
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center">
        <RocketIcon className="h-4 w-4 mr-2 text-muted-foreground" />
        <h3 className="text-sm font-medium">Production Deployment</h3>
      </div>
      <Button asChild size="sm" variant="ghost">
        <Link href={`/projects/${projectId}/deployments`}>
          View All
        </Link>
      </Button>
    </div>
    
    <div className="p-8 text-center border-t">
      <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-4">
        <RocketIcon className="h-6 w-6 text-primary" />
      </div>
      <h3 className="text-sm font-medium mb-1">No deployments yet</h3>
      <p className="text-sm text-muted-foreground mb-4">Deploy your app to make it available online</p>
      <Button asChild size="sm">
        <Link href={`/projects/${projectId}/deployments`}>
          Go to Deployments
        </Link>
      </Button>
    </div>
  </div>
);

export default DeploymentSection;
