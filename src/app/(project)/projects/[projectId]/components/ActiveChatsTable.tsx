"use client";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PlusIcon, GitBranchIcon, ExternalLinkIcon } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import {Chat} from "@/lib/db/schema";
import {useRouter} from "next/navigation";

interface ActiveChatsTableProps {
  chats: any[];
  projectId: string;
}

export const ActiveChatsTable = ({ chats, projectId }: ActiveChatsTableProps) => {

  const router = useRouter();

  const projectLink = (chat: Chat) => {
    router.push(`/projects/${projectId}/${chat.type === "app" ? "chats": "design"}/${chat.id}`);
  }

  return (
      <div className="rounded-md border">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center">
            <GitBranchIcon className="h-4 w-4 mr-2 text-muted-foreground" />
            <h3 className="text-sm font-medium">Last 3 Active Chats</h3>
          </div>
          <Button asChild size="sm" variant="ghost">
            <Link href={`/projects/${projectId}/chats`}>
              View All
            </Link>
          </Button>
        </div>

        {chats && chats.length > 0 ? (
            <div className="border-t">
              <table className="w-full">
                <thead>
                <tr className="border-b bg-muted/50">
                  <th className="text-xs font-medium text-left p-3">Name</th>
                  <th className="text-xs font-medium text-left p-3 hidden md:table-cell">Status</th>
                  <th className="text-xs font-medium text-left p-3 hidden md:table-cell">Last Updated</th>
                  <th className="text-xs font-medium text-right p-3">Actions</th>
                </tr>
                </thead>
                <tbody>
                {chats.map((chat) => (
                    <tr key={chat.id} className="border-b last:border-0 cursor-pointer hover:bg-muted/30" onClick={() => projectLink(chat)}>
                      <td className="p-3">
                        <div className="font-medium">{chat.title || 'Untitled Chat'}</div>
                        <div className="text-xs text-muted-foreground">{chat.id.substring(0, 8)}</div>
                      </td>
                      <td className="p-3 hidden md:table-cell">
                        <Badge variant={chat.status === 'completed' ? 'outline' : 'default'} className={chat.status === 'completed' ? 'bg-green-50 text-green-700 hover:bg-green-50 border-green-200' : ''}>
                          {chat.status || 'Active'}
                        </Badge>
                      </td>
                      <td className="p-3 text-sm hidden md:table-cell">
                        {formatDistanceToNow(new Date(chat.updatedAt || chat.createdAt), {addSuffix: true})}
                      </td>
                      <td className="p-3 text-right">
                        <Button asChild size="sm" variant="ghost">
                          <Link href={`/projects/${projectId}/${chat.type === "app" ? "chats": "design"}/${chat.id}`}>
                            <ExternalLinkIcon className="h-4 w-4" />
                            <span className="sr-only">Open</span>
                          </Link>
                        </Button>
                      </td>
                    </tr>
                ))}
                </tbody>
              </table>
            </div>
        ) : (
            <div className="p-8 text-center border-t">
              <p className="text-sm text-muted-foreground mb-4">No chats created yet</p>
              <Button asChild size="sm">
                <Link href={`/projects/${projectId}/chats/new`}>
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Create First Chat
                </Link>
              </Button>
            </div>
        )}
      </div>
  );
}

export default ActiveChatsTable;
