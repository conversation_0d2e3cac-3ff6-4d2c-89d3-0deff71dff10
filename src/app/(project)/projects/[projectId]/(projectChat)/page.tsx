import {auth} from "@/app/(auth)/auth";
import {getChatsByProjectId, getLatestChatsByProjectId, getProjectById} from "@/lib/db/queries";
import {notFound} from "next/navigation";
import {Metadata} from "next";
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import {
  ProjectHeader,
  ProjectDetailsCard,
  ActiveChatsTable,
  DeploymentSection,
} from "../components";

export async function generateMetadata(
  props: { params: Promise<{ projectId: string }> }
): Promise<Metadata> {
  // read route params
  const params = await props.params;
  const { projectId } = params;

  const suffix = "magically - Create mobile apps in minutes";

  if (projectId) {
    // fetch data
    const project = await getProjectById({ id: projectId });
    return {
      title: project?.appName + " | " + suffix,
    };
  } else {
    return {
      title: suffix,
    };
  }
}

export default async function Page(props: {
  params: Promise<{ projectId: string }>;
}) {
  const params = await props.params;
  const { projectId } = params;
  const project = await getProjectById({ id: projectId });
  const session = await auth();

    if (!project) {
        return notFound();
    }

    // Fetch recent chats for this project
    const chats = await getLatestChatsByProjectId({id: projectId});

    return (
        <HeaderProvider projectId={projectId}>
            <div className="container py-6 px-4 max-w-7xl mx-auto">
                <ProjectHeader project={project} projectId={projectId} />
                
                <div className="mt-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="md:col-span-2 space-y-6">
                            <ActiveChatsTable chats={chats} projectId={projectId} />
                            <DeploymentSection projectId={projectId} />
                        </div>
                        <div>
                            <ProjectDetailsCard project={project} />
                        </div>
                    </div>
                </div>
            </div>
        </HeaderProvider>
    );
}