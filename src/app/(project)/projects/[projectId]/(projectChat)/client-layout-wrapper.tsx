'use client';

import { usePathname } from 'next/navigation';
import React from 'react';
import { GeneratorSidebar } from '@/components/generator/GeneratorSidebar';
import { LinkButton } from "@/components/ui/link-button";

export function ClientSideLayoutWrapper({
    children,
}: {
    children: React.ReactNode;
}) {
    const pathname = usePathname();
    const projectId = pathname?.split('/')[2];
    const chatId = pathname?.split('/')[4]; // Extract chatId from URL

    if (!projectId) {
        return (
            <div className="w-full h-full flex flex-col justify-center items-center">
                Looks like this url is invalid.
                <LinkButton href={"/"}>
                    Go to home
                </LinkButton>
            </div>
        )
    }

    return (
        <div className="flex h-dvh w-screen overflow-hidden">
            {/* Custom Generator Sidebar */}
            <GeneratorSidebar chatId={chatId} projectId={projectId}/>

            {/* Main Content */}
            <div className="flex-1 overflow-auto">
                {children}
            </div>
        </div>
    );
}
