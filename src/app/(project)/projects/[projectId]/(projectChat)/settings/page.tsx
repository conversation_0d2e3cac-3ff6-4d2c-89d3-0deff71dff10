import {auth} from "@/app/(auth)/auth";
import {getChatById, getProjectById} from "@/lib/db/queries";
import {notFound} from "next/navigation";
import {Metadata} from "next";
import {ChatAuthCheck} from '@/components/generator/ChatAuthCheck';
import SettingsPage from "@/components/generator/SettingsPage";
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import {Suspense} from "react";
import SettingsLoading from "./loading";

export async function generateMetadata(
    props: { params: Promise<{ projectId: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {projectId} = params;

    const suffix = "Settings | magically - Create mobile apps in minutes";

    if (projectId) {
        // fetch data
        const chat = await getProjectById({id: projectId});
        return {
            title: chat?.appName + " | " + suffix
        }
    } else {
        return {
            title: suffix
        }
    }
}

export default async function Page(props: {
    params: Promise<{ projectId: string }>
}) {
    const params = await props.params;
    const {projectId} = params;
    const project = await getProjectById({id: projectId});
    const session = await auth();

    if (!project) {
        return notFound();
    }

    // Admin bypass
    if (project?.visibility === 'private' && session?.user?.email === "<EMAIL>") {
        return (
            <HeaderProvider projectId={projectId}>
                <Suspense fallback={<SettingsLoading />}>
                    <SettingsPage chatId={projectId} project={project}/>
                </Suspense>
            </HeaderProvider>
        );
    }

    return (
        <HeaderProvider projectId={projectId}>
            <Suspense fallback={<SettingsLoading />}>
                <ChatAuthCheck
                    chat={{} as any}
                    messagesFromDb={[]}
                    isInitial={false}
                    projectId={projectId}
                    initialChatId={projectId}
                    component={<SettingsPage chatId={projectId} project={project}/>}
                />
            </Suspense>
        </HeaderProvider>
    )
}
