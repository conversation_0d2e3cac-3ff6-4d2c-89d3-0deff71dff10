import {RouteGuard} from '@/components/auth/RouteGuard';
import {SidebarProvider} from '@/components/ui/sidebar';
import React from 'react';
import {ClientSideLayoutWrapper} from './client-layout-wrapper';

export default function GeneratorLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <RouteGuard>
            <SidebarProvider defaultOpen={true}>
                <ClientSideLayoutWrapper>
                    {children}
                </ClientSideLayoutWrapper>
            </SidebarProvider>
        </RouteGuard>
    )
}
