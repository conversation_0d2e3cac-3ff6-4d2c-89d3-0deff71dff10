import GeneratorPage from "@/components/generator/GeneratorPage";
import {generateUUID} from "@/lib/utils";
import {Metadata} from "next";
import {getChatById} from "@/lib/db/queries";
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import {Chat, Project} from "@/lib/db/schema";
import { getSanitizedProjectById} from "@/lib/db/project-queries";

export async function generateMetadata(
    props: { params: Promise<{ id: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {id} = params;

    const suffix = "magically - Create mobile apps in minutes";

    if (id) {
        // fetch data
        const chat = await getChatById({id});
        return {
            title: chat?.title + " | " + suffix
        }
    } else {
        return {
            title: suffix
        }
    }
}


export default async function Page(props: { searchParams: Promise<{ template: string, prompt: string }>, params: Promise<{ chatId: string, projectId: string }> }) {
    const {prompt, template} = await props.searchParams;
    // console.log('prompt', prompt, template)
    const id = generateUUID();
    const params = await props.params;
    const {projectId} = params;

    const [project]: [Project] = await Promise.all([
        getSanitizedProjectById({id: projectId})
    ]);

    return (
        <HeaderProvider chatId={id} isInitial={true} projectId={projectId}>
            <GeneratorPage
                isInitial={true}
                initialChatId={id}
                prompt={prompt}
                template={template}
                messagesFromDb={[]}
                projectId={projectId}
                project={project}
            />
        </HeaderProvider>

    )
}