import {notFound} from 'next/navigation';
import {auth} from '@/app/(auth)/auth';
import {getChatsByProjectId, getProjectById} from '@/lib/db/queries';
import Link from 'next/link';
import {Button} from '@/components/ui/button';
import {Badge} from '@/components/ui/badge';
import {PlusIcon, ExternalLinkIcon} from 'lucide-react';
import {formatDistanceToNow} from 'date-fns';
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import {ChatRow} from "@/app/(project)/projects/[projectId]/(projectChat)/chats/components/ChatRow";
import {Suspense} from 'react';
import ProjectsLoading from "@/app/(app)/projects/loading";

// Pagination component
const Pagination = ({currentPage, totalPages, projectId}: {
    currentPage: number;
    totalPages: number;
    projectId: string
}) => {
    return (
        <div className="flex justify-between items-center mt-4 px-4 py-2">
            <Button
                variant="outline"
                size="sm"
                disabled={currentPage <= 1}
                asChild
            >
                <Link href={`/projects/${projectId}/chats?page=${currentPage - 1}`}>
                    Previous
                </Link>
            </Button>

            <span className="text-sm text-muted-foreground">
        Page {currentPage} of {totalPages || 1}
      </span>

            <Button
                variant="outline"
                size="sm"
                disabled={currentPage >= totalPages}
                asChild
            >
                <Link href={`/projects/${projectId}/chats?page=${currentPage + 1}`}>
                    Next
                </Link>
            </Button>
        </div>
    );
};



export default async function ChatsPage(props: {
    params: Promise<{ projectId: string }>;
    searchParams?: Promise<{ page?: string }>;
}) {
    const params = await props.params;
    const searchParams = await props.searchParams;
    const {projectId} = params;
    const pageNumber = parseInt(searchParams?.page || '1', 10);
    const pageSize = 10; // Number of chats per page

    const project = await getProjectById({id: projectId});
    const session = await auth();

    if (!project) {
        return notFound();
    }

    // Fetch all chats for the project
    const allChats = await getChatsByProjectId({id: projectId});

    // Calculate pagination
    const totalChats = allChats.length;
    const totalPages = Math.ceil(totalChats / pageSize);
    const currentPage = Math.min(Math.max(1, pageNumber), totalPages || 1);

    // Get chats for current page
    const startIndex = (currentPage - 1) * pageSize;
    const paginatedChats = allChats.slice(startIndex, startIndex + pageSize);

    return (
        <HeaderProvider projectId={projectId}>
            <Suspense fallback={<ProjectsLoading />}>
                <div className="container py-6 px-4 max-w-7xl mx-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">Project Chats</h1>
                        <Button asChild>
                            <Link href={`/projects/${projectId}/chats/new`}>
                                <PlusIcon className="h-4 w-4 mr-2"/>
                                New Chat
                            </Link>
                        </Button>
                    </div>

                    <div className="rounded-md border">
                        {paginatedChats && paginatedChats.length > 0 ? (
                            <>
                                <table className="w-full">
                                    <thead>
                                    <tr className="border-b bg-muted/50">
                                        <th className="text-xs font-medium text-left p-4">Name</th>
                                        <th className="text-xs font-medium text-left p-4 hidden md:table-cell">Status</th>
                                        <th className="text-xs font-medium text-left p-4 hidden md:table-cell">Created</th>
                                        <th className="text-xs font-medium text-left p-4 hidden md:table-cell">Last
                                            Updated
                                        </th>
                                        <th className="text-xs font-medium text-right p-4">Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {paginatedChats.map((chat) => (
                                        <ChatRow chat={chat} projectId={projectId} key={chat.id}/>
                                    ))}
                                    </tbody>
                                </table>

                                {totalPages > 1 && (
                                    <Pagination
                                        currentPage={currentPage}
                                        totalPages={totalPages}
                                        projectId={projectId}
                                    />
                                )}
                            </>
                        ) : (
                            <div className="p-8 text-center">
                                <p className="text-sm text-muted-foreground mb-4">No chats created yet</p>
                                <Button asChild size="sm">
                                    <Link href={`/projects/${projectId}/chats/new`}>
                                        <PlusIcon className="h-4 w-4 mr-1"/>
                                        Create First Chat
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </Suspense>
        </HeaderProvider>
    );
}
