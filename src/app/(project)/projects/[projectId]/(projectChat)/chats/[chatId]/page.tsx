import {auth} from "@/app/(auth)/auth";
import GeneratorPage from "@/components/generator/GeneratorPage";
import {getChatById, getInitialMessagesByChatId, getMessagesByChatIdPaginated} from "@/lib/db/queries";
import {notFound} from "next/navigation";
import {Metadata} from "next";
import {ChatAuthCheck} from '@/components/generator/ChatAuthCheck';
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import {Suspense} from 'react';
import ProjectsLoading from "@/app/(app)/projects/loading";
import { getSanitizedProjectById} from "@/lib/db/project-queries";
import {Chat, Project} from "@/lib/db/schema";
import {convertToUIMessages} from "@/lib/utils";

export async function generateMetadata(
    props: { params: Promise<{ chatId: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {chatId} = params;

    const suffix = "magically - Create mobile apps in minutes";

    if (chatId) {
        // fetch data
        const chat = await getChatById({id: chatId});
        return {
            title: chat?.title + " | " + suffix
        }
    } else {
        return {
            title: suffix
        }
    }
}


export default async function Page(props: {
    searchParams: Promise<{ template: string, prompt: string }>,
    params: Promise<{ chatId: string, projectId: string }>
}) {
    const params = await props.params;
    const {chatId, projectId} = params;

    // Fetch chat, project, and initial messages in parallel
    const [chat, project, messagesData] = await Promise.all([
        getChatById({id: chatId}),
        getSanitizedProjectById({id: projectId}),
        getInitialMessagesByChatId({id: chatId})
    ]);
    
    // Extract messages and metadata
    const initialMessages = messagesData.messages || [];
    const totalUserMessages = messagesData.totalUserMessages || 0;
    const hasMoreMessages = messagesData.hasMoreMessages || false;
    
    console.log(`[Page] Loaded ${initialMessages.length} messages for chat ${chatId}. Total user messages: ${totalUserMessages}, Has more: ${hasMoreMessages}`);

    const session = await auth();

    if (!chat) {
        return notFound();
    }

    // Admin bypass
    if (chat?.visibility === 'private' && session?.user?.email === "<EMAIL>") {
        // Convert DB messages to UI format
        // const uiMessages = convertToUIMessages(initialMessages);

        return (
            <HeaderProvider chatId={chatId} chat={chat} projectId={projectId} showActions={true}>
                <Suspense fallback={<ProjectsLoading />}>
                    <GeneratorPage
                        chat={chat}
                        messagesFromDb={initialMessages}
                        isInitial={false}
                        activeTab="chat"
                        projectId={projectId}
                        project={project}
                        hasMoreMessages={hasMoreMessages}
                        totalUserMessages={totalUserMessages}
                    />
                </Suspense>
            </HeaderProvider>
        );
    }

    // Convert DB messages to UI format
    // const uiMessages = convertToUIMessages(initialMessages);
    const {prompt, template} = await props.searchParams;

    return (
        <HeaderProvider chatId={chatId} chat={chat} projectId={projectId} showActions={true}>
            <Suspense fallback={<ProjectsLoading />}>
                <ChatAuthCheck
                    projectId={projectId}
                    chat={chat}
                    messagesFromDb={initialMessages}
                    isInitial={false}
                    initialChatId={chatId}
                    prompt={prompt}
                    template={template}
                    activeTab="chat"
                    project={project}
                />
            </Suspense>
        </HeaderProvider>
    )
}