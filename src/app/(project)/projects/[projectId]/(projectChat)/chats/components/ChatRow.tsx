"use client"
import {Cha<PERSON>} from "@/lib/db/schema";
import {Badge} from "@/components/ui/badge";
import {formatDistanceToNow} from "date-fns";
import {LinkButton} from "@/components/ui/link-button";
import {ExternalLinkIcon} from "lucide-react";
import {useRouter} from "next/navigation";

export const ChatRow = ({chat, projectId}: { chat: Chat, projectId: string }) => {

    const router = useRouter();
    const projectLink = () => {
        router.push(`/projects/${projectId}/${chat.type === "app" ? "chats": "design"}/${chat.id}`);
    }
    return (
        <tr key={chat.id} className="border-b last:border-0 hover:bg-muted/30 cursor-pointer "
            onClick={() => projectLink()}>
            <td className="p-4">
                <div className="font-medium">{chat.title || 'Untitled Chat'}</div>
                <div
                    className="text-xs text-muted-foreground">{chat.id.substring(0, 8)}</div>
            </td>
            <td className="p-4 hidden md:table-cell">
                <Badge variant={'default'}
                       className={chat.visibility === 'public' ? 'bg-green-50 text-green-700 hover:bg-green-50 border-green-200' : ''}>
                    {chat.visibility === 'public' ? 'Public' : 'Private'}
                </Badge>
            </td>
            <td className="p-4 text-sm hidden md:table-cell">
                {formatDistanceToNow(new Date(chat.createdAt), {addSuffix: true})}
            </td>
            <td className="p-4 text-sm hidden md:table-cell">
                {formatDistanceToNow(new Date(chat.updatedAt), {addSuffix: true})}
            </td>
            <td className="p-4 text-right">
                <LinkButton size="sm" variant="ghost"
                            href={`/projects/${projectId}/${chat.type === "app" ? "chats": "design"}/${chat.id}`}>
                    <ExternalLinkIcon className="h-4 w-4"/>
                    <span className="sr-only">Open</span>
                </LinkButton>
            </td>
        </tr>
    )
}