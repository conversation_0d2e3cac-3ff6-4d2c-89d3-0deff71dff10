import {auth} from "@/app/(auth)/auth";
import {getChatById, getChatsByProjectId, getProjectById} from "@/lib/db/queries";
import {notFound} from "next/navigation";
import {Metadata} from "next";
import {ChatAuthCheck} from '@/components/generator/ChatAuthCheck';
import DeploymentsPage from "@/components/generator/DeploymentsPage";
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import { RocketIcon } from "lucide-react";

export async function generateMetadata(
    props: { params: Promise<{ projectId: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {projectId} = params;

    const suffix = "Deployments | magically - Create mobile apps in minutes";

    if (projectId) {
        // fetch data
        const project = await getProjectById({id: projectId});
        return {
            title: project?.appName + " | " + suffix
        }
    } else {
        return {
            title: suffix
        }
    }
}

export default async function Page(props: {
    params: Promise<{ projectId: string }>
}) {
    const params = await props.params;
    const {projectId} = params;
    const project = await getProjectById({id: projectId});
    const session = await auth();

    if (!project) {
        return notFound();
    }

    // Admin bypass
    if (project?.visibility === 'private' && session?.user?.email === "<EMAIL>") {
        return (
            <HeaderProvider projectId={projectId}>
                <div className="relative w-full h-full">
                    <DeploymentsPage chatId={projectId} project={project}/>
                    
                    {/*/!* Simple Coming Soon Overlay *!/*/}
                    {/*<div className="absolute inset-0 bg-background/90 backdrop-blur-sm flex flex-col items-center justify-center z-50">*/}
                    {/*    <div className="flex flex-col items-center justify-center max-w-md text-center p-6 rounded-lg">*/}
                    {/*        <div className="bg-primary/10 p-3 rounded-full mb-4">*/}
                    {/*            <RocketIcon className="h-10 w-10 text-primary" />*/}
                    {/*        </div>*/}
                    {/*        <h2 className="text-2xl font-bold mb-2">Coming Soon</h2>*/}
                    {/*        <p className="text-muted-foreground mb-2">*/}
                    {/*            We're currently testing one-click deploy, web deploy, and APK build features.*/}
                    {/*        </p>*/}
                    {/*        <p className="text-sm text-muted-foreground font-medium">*/}
                    {/*            Rollout expected later this week!*/}
                    {/*        </p>*/}
                    {/*    </div>*/}
                    {/*</div>*/}
                </div>
            </HeaderProvider>
        );
    }

    return (
        <HeaderProvider projectId={projectId}>
            <div className="relative w-full h-full">
                <ChatAuthCheck
                    chat={{} as any}
                    messagesFromDb={[]}
                    isInitial={false}
                    projectId={projectId}
                    initialChatId={projectId}
                    component={<DeploymentsPage chatId={projectId} project={project}/>}
                />
                
                {/*/!* Simple Coming Soon Overlay *!/*/}
                {/*<div className="absolute inset-0 bg-background/90 backdrop-blur-sm flex flex-col items-center justify-center z-50">*/}
                {/*    <div className="flex flex-col items-center justify-center max-w-md text-center p-6 rounded-lg">*/}
                {/*        <div className="bg-primary/10 p-3 rounded-full mb-4">*/}
                {/*            <RocketIcon className="h-10 w-10 text-primary" />*/}
                {/*        </div>*/}
                {/*        <h2 className="text-2xl font-bold mb-2">Coming Soon</h2>*/}
                {/*        <p className="text-muted-foreground mb-2">*/}
                {/*            We're currently testing one-click deploy, web deploy, and APK build features.*/}
                {/*        </p>*/}
                {/*        <p className="text-sm text-muted-foreground font-medium">*/}
                {/*            Rollout expected later this week!*/}
                {/*        </p>*/}
                {/*    </div>*/}
                {/*</div>*/}
            </div>
        </HeaderProvider>
    )
}
