import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  ChevronDown, 
  ChevronUp, 
  ArrowRight,
  Sparkles,
  Zap,
  Shield,
  DollarSign,
  Palette,
  Loader2
} from 'lucide-react';
import { Platform } from '@/stores/AppStoreReviewStore';

interface ActionItemsStepProps {
  platform: Platform;
  onContinue: () => void;
  onBack: () => void;
}

interface ActionItem {
  id: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: 'privacy' | 'performance' | 'design' | 'business';
  status: 'pending' | 'in-progress' | 'fixed' | 'ignored';
  expanded: boolean;
  autoFixAvailable: boolean;
  autoFixInProgress: boolean;
  autoFixComplete: boolean;
}

const categoryLabels = {
  privacy: 'Privacy & Safety',
  performance: 'Performance & Stability',
  design: 'Design & User Experience',
  business: 'Business & Monetization'
};

const severityLabels = {
  critical: 'Critical',
  high: 'High',
  medium: 'Medium',
  low: 'Low'
};

const ActionItemsStep: React.FC<ActionItemsStepProps> = ({
  platform,
  onContinue,
  onBack
}) => {
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'fixed'>('all');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'privacy' | 'performance' | 'design' | 'business'>('all');
  const [fixingAllInProgress, setFixingAllInProgress] = useState(false);
  
  // Toggle item status between pending and in-progress
  const handleToggleStatus = useCallback((id: string) => {
    setActionItems(prev => 
      prev.map(item => {
        if (item.id === id) {
          const newStatus = 
            item.status === 'pending' ? 'in-progress' : 
            item.status === 'in-progress' ? 'fixed' : 
            item.status === 'fixed' ? 'pending' : 
            'pending';
          
          return { ...item, status: newStatus };
        }
        return item;
      })
    );
    
    // Update progress
    updateProgress();
  }, []);
  
  // Mark item as fixed
  const markAsFixed = (itemId: string) => {
    setActionItems(prev => 
      prev.map(item => 
        item.id === itemId 
          ? { ...item, status: 'fixed' } 
          : item
      )
    );
  };
  
  // Mark item as ignored
  const markAsIgnored = (itemId: string) => {
    setActionItems(prev => 
      prev.map(item => 
        item.id === itemId 
          ? { ...item, status: 'ignored' } 
          : item
      )
    );
  };
  
  // Auto-fix a single item
  const autoFixItem = (itemId: string) => {
    // Find the item
    const item = actionItems.find(item => item.id === itemId);
    if (!item || !item.autoFixAvailable) return;
    
    // Update item to show progress
    setActionItems(prev => 
      prev.map(item => 
        item.id === itemId 
          ? { ...item, autoFixInProgress: true, status: 'in-progress' } 
          : item
      )
    );
    
    // Initialize without simulated delay
    setActionItems(prev => 
      prev.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              autoFixInProgress: false, 
              autoFixComplete: true,
              status: 'fixed'
            } 
          : item
      )
    );
  };
  
  // Auto-fix all available items
  const autoFixAll = () => {
    // Check if there are items that can be auto-fixed
    const fixableItems = actionItems.filter(
      item => item.autoFixAvailable && item.status === 'pending'
    );
    
    if (fixableItems.length === 0) return;
    
    setFixingAllInProgress(true);
    
    // Mark all fixable items as in-progress
    setActionItems(prev => 
      prev.map(item => 
        item.autoFixAvailable && item.status === 'pending'
          ? { ...item, autoFixInProgress: true, status: 'in-progress' } 
          : item
      )
    );
    
    // Initialize without simulated delay
    setActionItems(prev => 
      prev.map(item => 
        item.autoFixAvailable && item.status === 'in-progress'
          ? { 
              ...item, 
              autoFixInProgress: false, 
              autoFixComplete: true,
              status: 'fixed'
            } 
          : item
      )
    );
    
    setFixingAllInProgress(false);
  };
  
  // Filter items based on selected filters
  const filteredItems = useMemo(() => actionItems.filter(item => {
    // Filter by status
    if (selectedFilter === 'pending' && (item.status === 'fixed' || item.status === 'ignored')) {
      return false;
    }
    if (selectedFilter === 'fixed' && item.status !== 'fixed') {
      return false;
    }
    
    // Filter by category
    if (selectedCategory !== 'all' && item.category !== selectedCategory) {
      return false;
    }
    
    return true;
  }), [actionItems, selectedFilter, selectedCategory]);
  
  // Sort items by severity (critical first)
  const sortedItems = useMemo(() => [...filteredItems].sort((a, b) => {
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    return severityOrder[a.severity] - severityOrder[b.severity];
  }), [filteredItems]);
  
  // Calculate stats
  const totalItems = actionItems.length;
  const fixedItems = actionItems.filter(item => item.status === 'fixed').length;
  const pendingItems = actionItems.filter(item => item.status === 'pending' || item.status === 'in-progress').length;
  const ignoredItems = actionItems.filter(item => item.status === 'ignored').length;
  const fixProgress = useMemo(() => totalItems > 0 ? Math.round((fixedItems / totalItems) * 100) : 0, [totalItems, fixedItems]);
  
  // Update progress based on fixed items - memoized for performance
  const updateProgress = useCallback(() => {
    const totalItems = actionItems.length;
    const fixedItems = actionItems.filter(item => item.status === 'fixed').length;
    const inProgressItems = actionItems.filter(item => item.status === 'in-progress').length;
    
    const newProgress = totalItems > 0 
      ? Math.round(((fixedItems + (inProgressItems * 0.5)) / totalItems) * 100) 
      : 0;
    
    // Update progress without simulated delay
    setProgress(newProgress);
  }, [actionItems]);
  
  // Load action items
  useEffect(() => {
    setIsLoading(false);
  }, []);
  
  // Generate sample action items based on platform
  const generateActionItems = (platform: Platform): ActionItem[] => {
    const commonItems: ActionItem[] = [
      {
        id: "1",
        title: "Update Privacy Policy",
        description: "Your privacy policy needs to be updated to include information about data collection and usage.",
        severity: "high",
        category: "privacy",
        status: "pending",
        expanded: false,
        autoFixAvailable: true,
        autoFixInProgress: false,
        autoFixComplete: false
      },
      {
        id: "2",
        title: "Add App Tracking Transparency",
        description: "Your app needs to implement App Tracking Transparency to request permission for user tracking.",
        severity: "high",
        category: "privacy",
        status: "pending",
        expanded: false,
        autoFixAvailable: true,
        autoFixInProgress: false,
        autoFixComplete: false
      },
      {
        id: "3",
        title: "Optimize Memory Usage",
        description: "Your app is using excessive memory which may lead to crashes on older devices. Optimize memory allocation.",
        severity: "medium",
        category: "performance",
        status: "pending",
        expanded: false,
        autoFixAvailable: false,
        autoFixInProgress: false,
        autoFixComplete: false
      },
      {
        id: 'accessibility-labels',
        title: 'Missing Accessibility Labels',
        description: 'Many UI elements in your app are missing accessibility labels, making it difficult for users with disabilities.',
        severity: 'medium',
        category: 'design',
        status: 'pending',
        expanded: false,
        autoFixAvailable: true,
        autoFixInProgress: false,
        autoFixComplete: false
      }
    ];
    
    const iosSpecificItems: ActionItem[] = [
      {
        id: 'app-tracking-transparency',
        title: 'Missing App Tracking Transparency',
        description: "Your app collects user or device data but doesn't implement App Tracking Transparency to request permission.",
        severity: 'critical',
        category: 'privacy',
        status: 'pending',
        expanded: false,
        autoFixAvailable: true,
        autoFixInProgress: false,
        autoFixComplete: false
      },
      {
        id: 'iap-implementation',
        title: 'Incomplete In-App Purchase Implementation',
        description: "Your app includes in-app purchases but doesn't fully comply with Apple's guidelines.",
        severity: 'high',
        category: 'business',
        status: 'pending',
        expanded: false,
        autoFixAvailable: false,
        autoFixInProgress: false,
        autoFixComplete: false
      },
      {
        id: 'ipv6-compatibility',
        title: 'IPv6 Compatibility Issues',
        description: 'Your app may not work properly on IPv6-only networks, which is required by App Store guidelines.',
        severity: 'medium',
        category: 'performance',
        status: 'pending',
        expanded: false,
        autoFixAvailable: false,
        autoFixInProgress: false,
        autoFixComplete: false
      }
    ];
    
    const androidSpecificItems: ActionItem[] = [
      {
        id: 'target-api-level',
        title: 'Outdated Target API Level',
        description: 'Your app targets an outdated Android API level. Google Play requires targeting recent API levels.',
        severity: 'critical',
        category: 'performance',
        status: 'pending',
        expanded: false,
        autoFixAvailable: true,
        autoFixInProgress: false,
        autoFixComplete: false
      },
      {
        id: 'google-play-billing',
        title: 'Non-compliant Billing Implementation',
        description: "Your app sells digital goods but doesn't use Google Play Billing for all transactions.",
        severity: 'high',
        category: 'business',
        status: 'pending',
        expanded: false,
        autoFixAvailable: false,
        autoFixInProgress: false,
        autoFixComplete: false
      },
      {
        id: 'notification-channels',
        title: 'Missing Notification Channels',
        description: "Your app sends notifications but doesn't implement notification channels for Android 8.0+.",
        severity: 'medium',
        category: 'design',
        status: 'pending',
        expanded: false,
        autoFixAvailable: true,
        autoFixInProgress: false,
        autoFixComplete: false
      }
    ];
    
    return [
      ...commonItems,
      ...(platform === 'ios' ? iosSpecificItems : androidSpecificItems)
    ];
  };
  
  // Get severity badge color
  const getSeverityColor = (severity: ActionItem['severity']) => {
    switch (severity) {
      case 'critical': return 'bg-rose-500';
      case 'high': return 'bg-amber-500';
      case 'medium': return 'bg-blue-500';
      case 'low': return 'bg-green-500';
    }
  };
  
  // Get category icon
  const getCategoryIcon = (category: ActionItem['category']) => {
    switch (category) {
      case 'privacy':
        return <Shield size={16} className="text-purple-400" />;
      case 'performance':
        return <Zap size={16} className="text-amber-400" />;
      case 'design':
        return <Palette size={16} className="text-blue-400" />;
      case 'business':
        return <DollarSign size={16} className="text-green-400" />;
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: ActionItem['status']) => {
    switch (status) {
      case 'fixed':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'ignored':
        return <XCircle size={16} className="text-gray-400" />;
      case 'in-progress':
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Loader2 size={16} className="text-blue-400" />
          </motion.div>
        );
      default:
        return <AlertCircle size={16} className="text-amber-400" />;
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-4xl mx-auto"
    >
      <div className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 border border-white/20">
        <div className="mb-8 text-center">
          <h3 className="text-2xl font-medium text-white mb-2">Action Items</h3>
          <p className="text-white/70">
            Review and fix issues to improve your app's chances of approval
          </p>
        </div>
        
        {isLoading ? (
          // Loading skeleton - simplified animation
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map(i => (
              <div 
                key={i}
                className="bg-gray-800/50 rounded-lg p-4 h-24 animate-pulse"
              />
            ))}
          </div>
        ) : (
          <>
            {/* Progress summary */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/10">
              <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
                <h4 className="text-white text-lg font-medium mb-2 sm:mb-0">Fix Progress</h4>
                <div className="flex space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`
                      px-4 py-2 rounded-full text-sm font-medium flex items-center
                      ${fixingAllInProgress ? 'bg-indigo-500/50 text-white cursor-not-allowed' : 'bg-indigo-600 text-white hover:bg-indigo-500 cursor-pointer'}
                    `}
                    onClick={autoFixAll}
                    disabled={fixingAllInProgress}
                  >
                    {fixingAllInProgress ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="mr-2"
                        >
                          <Loader2 size={14} />
                        </motion.div>
                        <span>Auto-fixing...</span>
                      </>
                    ) : (
                      <>
                        <Sparkles size={14} className="mr-2" />
                        <span>Auto-fix All</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </div>
              
              <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden mb-4">
                <motion.div 
                  className="h-full bg-indigo-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${fixProgress}%` }}
                  transition={{ duration: 0.8 }}
                />
              </div>
              
              <div className="flex justify-between text-sm">
                <div className="flex items-center">
                  <CheckCircle size={14} className="text-green-400 mr-1.5" />
                  <span className="text-white/70">{fixedItems} Fixed</span>
                </div>
                <div className="flex items-center">
                  <AlertCircle size={14} className="text-amber-400 mr-1.5" />
                  <span className="text-white/70">{pendingItems} Pending</span>
                </div>
                <div className="flex items-center">
                  <XCircle size={14} className="text-gray-400 mr-1.5" />
                  <span className="text-white/70">{ignoredItems} Ignored</span>
                </div>
                <div className="flex items-center">
                  <span className="text-white font-medium">{fixProgress}% Complete</span>
                </div>
              </div>
            </div>
            
            {/* Filters */}
            <div className="flex flex-wrap gap-2 mb-6">
              <div className="flex rounded-full bg-white/5 p-1 mr-2">
                <button
                  className={`px-3 py-1 text-sm rounded-full ${selectedFilter === 'all' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'}`}
                  onClick={() => setSelectedFilter('all')}
                >
                  All
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-full ${selectedFilter === 'pending' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'}`}
                  onClick={() => setSelectedFilter('pending')}
                >
                  Pending
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-full ${selectedFilter === 'fixed' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'}`}
                  onClick={() => setSelectedFilter('fixed')}
                >
                  Fixed
                </button>
              </div>
              
              <div className="flex rounded-full bg-white/5 p-1">
                <button
                  className={`px-3 py-1 text-sm rounded-full ${selectedCategory === 'all' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'}`}
                  onClick={() => setSelectedCategory('all')}
                >
                  All Categories
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-full ${selectedCategory === 'privacy' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'}`}
                  onClick={() => setSelectedCategory('privacy')}
                >
                  Privacy
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-full ${selectedCategory === 'performance' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'}`}
                  onClick={() => setSelectedCategory('performance')}
                >
                  Performance
                </button>
              </div>
            </div>
            
            {/* Action items list */}
            {sortedItems.length > 0 ? (
              <div className="mt-6 space-y-4">
                {sortedItems.map(item => (
                  <div
                    key={item.id}
                    className={`
                      bg-gray-800/40 backdrop-blur-sm rounded-lg overflow-hidden
                      border border-gray-700/50
                      ${item.status === 'fixed' ? 'border-green-500/30' : ''}
                      ${item.status === 'in-progress' ? 'border-blue-500/30' : ''}
                    `}
                  >
                    {/* Item header */}
                    <div 
                      className="p-4 flex items-center justify-between cursor-pointer"
                      onClick={() => handleToggleStatus(item.id)}
                    >
                      <div className="flex items-center">
                        <div className="mr-3">
                          {getStatusIcon(item.status)}
                        </div>
                        <div>
                          <div className="flex items-center mb-1">
                            <span className={`text-xs font-medium px-2 py-0.5 rounded-full mr-2 ${getSeverityColor(item.severity)} text-white`}>
                              {severityLabels[item.severity]}
                            </span>
                            <span className="text-xs text-white/60 flex items-center">
                              {getCategoryIcon(item.category)}
                              <span className="ml-1">{categoryLabels[item.category]}</span>
                            </span>
                          </div>
                          <h5 className="text-white font-medium">{item.title}</h5>
                        </div>
                      </div>
                      <div>
                        {item.expanded ? (
                          <ChevronUp size={18} className="text-white/70" />
                        ) : (
                          <ChevronDown size={18} className="text-white/70" />
                        )}
                      </div>
                    </div>
                    
                    {/* Item details */}
                    {item.expanded && (
                      <div className="px-4 pb-4">
                        <div className="pl-8 border-l border-white/10">
                          <p className="text-white/70 mb-4">{item.description}</p>
                          
                          {item.status !== 'fixed' && item.status !== 'ignored' && (
                            <div className="flex flex-wrap gap-2">
                              {item.autoFixAvailable && (
                                <motion.button
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  className={`
                                    px-3 py-1.5 rounded-lg text-sm font-medium flex items-center
                                    ${item.autoFixInProgress ? 'bg-indigo-500/50 text-white cursor-not-allowed' : 'bg-indigo-600 text-white hover:bg-indigo-500 cursor-pointer'}
                                  `}
                                  onClick={() => autoFixItem(item.id)}
                                  disabled={item.autoFixInProgress}
                                >
                                  {item.autoFixInProgress ? (
                                    <>
                                      <motion.div
                                        animate={{ rotate: 360 }}
                                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                        className="mr-1.5"
                                      >
                                        <Loader2 size={14} />
                                      </motion.div>
                                      <span>Auto-fixing...</span>
                                    </>
                                  ) : (
                                    <>
                                      <Sparkles size={14} className="mr-1.5" />
                                      <span>Auto-fix</span>
                                    </>
                                  )}
                                </motion.button>
                              )}
                              
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className="px-3 py-1.5 rounded-lg bg-white/10 text-white hover:bg-white/20 text-sm"
                                onClick={() => markAsFixed(item.id)}
                              >
                                Mark as Fixed
                              </motion.button>
                              
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className="px-3 py-1.5 rounded-lg bg-white/10 text-white/70 hover:bg-white/20 hover:text-white text-sm"
                                onClick={() => markAsIgnored(item.id)}
                              >
                                Ignore
                              </motion.button>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 bg-white/5 rounded-xl border border-white/10 mb-8">
                <p className="text-white/70">No action items match your current filters.</p>
              </div>
            )}
          </>
        )}
        
        {/* Action buttons */}
        <div className="flex justify-center space-x-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-6 py-2 rounded-full bg-white/10 text-white hover:bg-white/20"
            onClick={onBack}
          >
            Back
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-3 rounded-full bg-white text-indigo-600 hover:bg-white/90 font-medium"
            onClick={onContinue}
          >
            Continue
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};



export default ActionItemsStep;
