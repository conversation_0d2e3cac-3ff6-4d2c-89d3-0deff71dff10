import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Shield, CheckCircle, AlertCircle, XCircle, ChevronDown, ChevronUp, ExternalLink } from 'lucide-react';
import { Platform } from '@/stores/AppStoreReviewStore';

interface ComplianceCheckStepProps {
  platform: Platform;
  onContinue: () => void;
  onBack: () => void;
}

interface ComplianceCategory {
  id: string;
  name: string;
  description: string;
  items: ComplianceItem[];
  expanded: boolean;
  score: number;
  maxScore: number;
}

interface ComplianceItem {
  id: string;
  title: string;
  description: string;
  status: 'pass' | 'warning' | 'fail' | 'checking';
  details?: string;
  guidelineLink?: string;
}

const ComplianceCheckStep: React.FC<ComplianceCheckStepProps> = ({
  platform,
  onContinue,
  onBack
}) => {
  const [categories, setCategories] = useState<ComplianceCategory[]>([]);
  const [isChecking, setIsChecking] = useState(true);
  const [progress, setProgress] = useState(0);
  const [overallScore, setOverallScore] = useState(0);
  const [maxPossibleScore, setMaxPossibleScore] = useState(0);
  
  // Initialize compliance categories based on platform - memoized
  const initialCategories = useMemo(() => {
    return platform === 'ios' ? getIOSCategories() : getAndroidCategories();
  }, [platform]);
  
  // Calculate max possible score - memoized
  const maxScore = useMemo(() => {
    return initialCategories.reduce((total, category) => total + category.maxScore, 0);
  }, [initialCategories]);
  
  // Toggle category - optimized with useCallback
  const toggleCategory = useCallback((categoryId: string) => {
    setCategories(prev => 
      prev.map(category => 
        category.id === categoryId 
          ? { ...category, expanded: !category.expanded } 
          : category
      )
    );
  }, []);

  // Initialize compliance check
  useEffect(() => {
    setCategories(initialCategories);
    setMaxPossibleScore(maxScore);
    
    // Simulate compliance check with faster updates and less animation
    let currentProgress = 0;
    let currentCategoryIndex = 0;
    let currentItemIndex = 0;
    
    // Process multiple items in each update to reduce animation overhead
    const processItems = (count: number) => {
      const updatedCategories = [...initialCategories];
      
      for (let i = 0; i < count; i++) {
        if (currentCategoryIndex >= initialCategories.length) break;
        
        const category = initialCategories[currentCategoryIndex];
        if (currentItemIndex >= category.items.length) {
          currentCategoryIndex++;
          currentItemIndex = 0;
          if (currentCategoryIndex >= initialCategories.length) break;
          continue;
        }
        
        const item = updatedCategories[currentCategoryIndex].items[currentItemIndex];
        
        // Randomly determine status (weighted toward passing)
        const rand = Math.random();
        let status: 'pass' | 'warning' | 'fail';
        
        if (rand < 0.7) {
          status = 'pass';
        } else if (rand < 0.9) {
          status = 'warning';
        } else {
          status = 'fail';
        }
        
        // Update item status
        updatedCategories[currentCategoryIndex].items[currentItemIndex] = {
          ...item,
          status,
          details: getStatusDetails(item.id, status, platform)
        };
        
        // Update category score
        if (status === 'pass') {
          updatedCategories[currentCategoryIndex].score += 1;
        } else if (status === 'warning') {
          updatedCategories[currentCategoryIndex].score += 0.5;
        }
        
        currentItemIndex++;
      }
      
      return updatedCategories;
    };
    
    const interval = setInterval(() => {
      if (currentProgress < 100) {
        // Process multiple items at once for better performance
        const itemsToProcess = 3;
        const progressIncrement = 5;
        
        currentProgress += progressIncrement;
        currentProgress = Math.min(currentProgress, 100);
        setProgress(currentProgress);
        
        // Update multiple items at once
        const updatedCategories = processItems(itemsToProcess);
        setCategories(updatedCategories);
        
        // Calculate overall score
        const score = updatedCategories.reduce((total, category) => total + category.score, 0);
        setOverallScore(score);
      } else {
        clearInterval(interval);
        setIsChecking(false);
      }
    }, 100); // Faster updates with more work done per update
    
    return () => clearInterval(interval);
  }, [initialCategories, maxScore, platform]);
  
  // Get iOS compliance categories
  const getIOSCategories = (): ComplianceCategory[] => [
    {
      id: 'safety',
      name: 'Safety & Privacy',
      description: "User data protection and content safety",
      expanded: true,
      score: 0,
      maxScore: 5,
      items: [
        {
          id: 'privacy-policy',
          title: 'Privacy Policy',
          description: "App includes a privacy policy compliant with App Store guidelines",
          status: 'checking'
        },
        {
          id: 'data-collection',
          title: 'Data Collection Disclosure',
          description: "App accurately discloses data types collected",
          status: 'checking',
          guidelineLink: 'https://developer.apple.com/app-store/app-privacy-details/'
        },
        {
          id: 'tracking-permission',
          title: 'App Tracking Transparency',
          description: "App requests permission before tracking across apps and websites",
          status: 'checking',
          guidelineLink: 'https://developer.apple.com/documentation/apptrackingtransparency'
        },
        {
          id: 'child-safety',
          title: 'Child Safety',
          description: "App complies with children's online privacy protection rules if applicable",
          status: 'checking'
        },
        {
          id: 'data-minimization',
          title: 'Data Minimization',
          description: "App only collects data necessary for its core functionality",
          status: 'checking'
        }
      ]
    },
    {
      id: 'performance',
      name: 'Performance & Stability',
      description: "App functionality and technical performance",
      expanded: false,
      score: 0,
      maxScore: 4,
      items: [
        {
          id: 'crash-free',
          title: 'Crash-Free Operation',
          description: "App operates without crashes or serious bugs",
          status: 'checking'
        },
        {
          id: 'memory-usage',
          title: 'Memory Usage',
          description: "App uses memory efficiently without excessive consumption",
          status: 'checking'
        },
        {
          id: 'battery-usage',
          title: 'Battery Usage',
          description: "App doesn't drain battery excessively",
          status: 'checking'
        },
        {
          id: 'background-processes',
          title: 'Background Processes',
          description: "App uses background processes appropriately",
          status: 'checking'
        }
      ]
    },
    {
      id: 'design',
      name: 'Design & User Experience',
      description: "UI/UX compliance with Apple guidelines",
      expanded: false,
      score: 0,
      maxScore: 4,
      items: [
        {
          id: 'human-interface',
          title: 'Human Interface Guidelines',
          description: "App follows Apple's Human Interface Guidelines",
          status: 'checking',
          guidelineLink: 'https://developer.apple.com/design/human-interface-guidelines/'
        },
        {
          id: 'accessibility',
          title: 'Accessibility',
          description: "App includes accessibility features",
          status: 'checking'
        },
        {
          id: 'device-compatibility',
          title: 'Device Compatibility',
          description: "App works properly on all supported devices and iOS versions",
          status: 'checking'
        },
        {
          id: 'navigation',
          title: 'Navigation & Controls',
          description: "App uses standard iOS navigation patterns and controls",
          status: 'checking'
        }
      ]
    },
    {
      id: 'business',
      name: 'Business & Monetization',
      description: "Payment processing and subscription models",
      expanded: false,
      score: 0,
      maxScore: 3,
      items: [
        {
          id: 'in-app-purchase',
          title: 'In-App Purchases',
          description: "App uses Apple's in-app purchase system for digital goods",
          status: 'checking',
          guidelineLink: 'https://developer.apple.com/app-store/review/guidelines/#in-app-purchase'
        },
        {
          id: 'subscription',
          title: 'Subscription Models',
          description: "App follows subscription guidelines and renewal transparency",
          status: 'checking'
        },
        {
          id: 'external-purchase',
          title: 'External Purchase Links',
          description: "App doesn't include links to external purchase mechanisms",
          status: 'checking'
        }
      ]
    }
  ];
  
  // Get Android compliance categories
  const getAndroidCategories = (): ComplianceCategory[] => [
    {
      id: 'safety',
      name: 'User Data & Privacy',
      description: "Data security and privacy practices",
      expanded: true,
      score: 0,
      maxScore: 5,
      items: [
        {
          id: 'privacy-policy',
          title: 'Privacy Policy',
          description: "App includes a compliant privacy policy",
          status: 'checking'
        },
        {
          id: 'data-safety',
          title: 'Data Safety Section',
          description: "App accurately discloses data collection in Data Safety section",
          status: 'checking',
          guidelineLink: 'https://support.google.com/googleplay/android-developer/answer/10787469'
        },
        {
          id: 'permission-usage',
          title: 'Permission Usage',
          description: "App only requests permissions necessary for core functionality",
          status: 'checking'
        },
        {
          id: 'sensitive-permissions',
          title: 'Sensitive Permissions',
          description: "App properly handles sensitive permissions like location",
          status: 'checking'
        },
        {
          id: 'child-safety',
          title: 'Families Policy',
          description: "App complies with Families Policy if targeting children",
          status: 'checking',
          guidelineLink: 'https://support.google.com/googleplay/android-developer/answer/9893335'
        }
      ]
    },
    {
      id: 'performance',
      name: 'Performance & Stability',
      description: "Technical performance and reliability",
      expanded: false,
      score: 0,
      maxScore: 4,
      items: [
        {
          id: 'crash-free',
          title: 'Crash-Free Operation',
          description: "App operates without crashes or ANRs (Application Not Responding)",
          status: 'checking'
        },
        {
          id: 'battery-usage',
          title: 'Battery Usage',
          description: "App doesn't drain battery excessively",
          status: 'checking'
        },
        {
          id: 'loading-time',
          title: 'Loading Time',
          description: "App loads and responds quickly",
          status: 'checking'
        },
        {
          id: 'background-behavior',
          title: 'Background Behavior',
          description: "App follows background execution limits",
          status: 'checking'
        }
      ]
    },
    {
      id: 'design',
      name: 'Design & User Experience',
      description: "UI/UX compliance with Material Design",
      expanded: false,
      score: 0,
      maxScore: 4,
      items: [
        {
          id: 'material-design',
          title: 'Material Design',
          description: "App follows Material Design guidelines",
          status: 'checking',
          guidelineLink: 'https://m3.material.io/'
        },
        {
          id: 'accessibility',
          title: 'Accessibility',
          description: "App includes accessibility features",
          status: 'checking'
        },
        {
          id: 'device-compatibility',
          title: 'Device Compatibility',
          description: "App works properly on various Android devices and versions",
          status: 'checking'
        },
        {
          id: 'navigation',
          title: 'Navigation & Controls',
          description: "App uses standard Android navigation patterns",
          status: 'checking'
        }
      ]
    },
    {
      id: 'business',
      name: 'Monetization & Payments',
      description: "Payment processing and subscription models",
      expanded: false,
      score: 0,
      maxScore: 3,
      items: [
        {
          id: 'google-play-billing',
          title: 'Google Play Billing',
          description: "App uses Google Play Billing for digital goods and services",
          status: 'checking',
          guidelineLink: 'https://support.google.com/googleplay/android-developer/answer/9858738'
        },
        {
          id: 'subscription',
          title: 'Subscription Transparency',
          description: "App clearly discloses subscription terms",
          status: 'checking'
        },
        {
          id: 'ads-policy',
          title: 'Ads Policy',
          description: "App follows Google Play's ads policies",
          status: 'checking'
        }
      ]
    }
  ];
  
  // Get status details based on item id and status
  const getStatusDetails = (itemId: string, status: 'pass' | 'warning' | 'fail', platform: Platform): string => {
    if (status === 'pass') {
      return 'This requirement is met.';
    }
    
    // Common warnings and failures
    const details: Record<string, Record<'warning' | 'fail', string>> = {
      'privacy-policy': {
        warning: 'Your privacy policy needs more details about data retention and user rights.',
        fail: "No privacy policy found or it doesn't meet minimum requirements."
      },
      'data-collection': {
        warning: platform === 'ios' 
          ? 'Some data types may not be accurately disclosed in App Privacy Details.'
          : 'Data Safety section is incomplete or contains discrepancies.',
        fail: platform === 'ios'
          ? 'App Privacy Details are missing or significantly inaccurate.'
          : 'Data Safety section is missing or contains major inaccuracies.'
      },
      'tracking-permission': {
        warning: 'App Tracking Transparency implementation needs improvement.',
        fail: 'App tracks users without obtaining proper consent.'
      },
      'permission-usage': {
        warning: 'Some permissions requested may not be essential for core functionality.',
        fail: 'App requests excessive permissions not related to its functionality.'
      },
      'in-app-purchase': {
        warning: 'In-app purchase implementation needs improvements for better user experience.',
        fail: 'App uses alternative payment methods for digital goods, violating store policies.'
      },
      'google-play-billing': {
        warning: 'Google Play Billing implementation needs improvements.',
        fail: 'App uses alternative payment methods for digital goods, violating store policies.'
      },
      'human-interface': {
        warning: "Some UI elements don't follow Apple's Human Interface Guidelines.",
        fail:"App significantly deviates from Apple's Human Interface Guidelines."
      },
      'material-design': {
        warning: "Some UI elements don't follow Material Design guidelines.",
        fail: 'App significantly deviates from Material Design guidelines.'
      },
      'accessibility': {
        warning: 'App has limited accessibility features.',
        fail: 'App lacks essential accessibility features.'
      }
    };
    
    // Return specific detail if available, otherwise generic message
    if (itemId in details) {
      return details[itemId][status];
    }
    
    return status === 'warning'
      ? 'This requirement is partially met but needs improvement.'
      : 'This requirement is not met and needs attention.';
  };
  
  // Calculate compliance score percentage
  const scorePercentage = maxPossibleScore > 0 
    ? Math.round((overallScore / maxPossibleScore) * 100) 
    : 0;
  
  // Determine overall status
  const getOverallStatus = () => {
    if (isChecking) return 'checking';
    if (scorePercentage >= 90) return 'excellent';
    if (scorePercentage >= 75) return 'good';
    if (scorePercentage >= 60) return 'fair';
    return 'poor';
  };
  
  const overallStatus = getOverallStatus();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-4xl mx-auto"
    >
      <div className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 border border-white/20">
        <div className="mb-8 text-center">
          <h3 className="text-2xl font-medium text-white mb-2">Compliance Check</h3>
          <p className="text-white/70">
            Analyzing your app against the latest 
            {platform === 'ios' ? ' App Store ' : ' Google Play '} 
            guidelines
          </p>
        </div>
        
        {/* Progress indicator */}
        {isChecking ? (
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-white/70 text-sm">Analyzing compliance...</span>
              <span className="text-white/70 text-sm">{progress}%</span>
            </div>
            <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
              <motion.div 
                className="h-full bg-white rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        ) : (
          <div className="mb-8 flex flex-col items-center">
            <div className="relative mb-4">
              <svg className="w-32 h-32">
                <circle
                  className="text-white/10"
                  strokeWidth="6"
                  stroke="currentColor"
                  fill="transparent"
                  r="58"
                  cx="64"
                  cy="64"
                />
                <motion.circle
                  className={`
                    ${overallStatus === 'excellent' ? 'text-green-400' : 
                      overallStatus === 'good' ? 'text-blue-400' : 
                      overallStatus === 'fair' ? 'text-amber-400' : 
                      'text-rose-400'}
                  `}
                  strokeWidth="6"
                  strokeLinecap="round"
                  stroke="currentColor"
                  fill="transparent"
                  r="58"
                  cx="64"
                  cy="64"
                  initial={{ strokeDasharray: 365, strokeDashoffset: 365 }}
                  animate={{ 
                    strokeDashoffset: 365 - (365 * scorePercentage / 100) 
                  }}
                  transition={{ duration: 1, ease: "easeOut" }}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-3xl font-bold text-white">{scorePercentage}%</span>
              </div>
            </div>
            
            <h4 className={`
              text-xl font-medium mb-2
              ${overallStatus === 'excellent' ? 'text-green-400' : 
                overallStatus === 'good' ? 'text-blue-400' : 
                overallStatus === 'fair' ? 'text-amber-400' : 
                'text-rose-400'}
            `}>
              {overallStatus === 'excellent' ? 'Excellent Compliance' : 
                overallStatus === 'good' ? 'Good Compliance' : 
                overallStatus === 'fair' ? 'Fair Compliance' : 
                'Poor Compliance'}
            </h4>
            
            <p className="text-white/70 text-center max-w-md">
              {overallStatus === 'excellent' ? 
                'Your app meets almost all compliance requirements. It should be ready for submission!' : 
                overallStatus === 'good' ? 
                'Your app meets most compliance requirements. Address the warnings before submission.' : 
                overallStatus === 'fair' ? 
                'Your app meets some compliance requirements but has several issues to fix.' : 
                'Your app has significant compliance issues that need to be addressed.'}
            </p>
          </div>
        )}
        
        {/* Category sections */}
        <div className="space-y-4 mb-8">
          {categories.map(category => (
            <div 
              key={category.id}
              className="backdrop-blur-sm bg-white/5 rounded-xl border border-white/10 overflow-hidden"
            >
              {/* Category header */}
              <div 
                className="p-4 flex items-center justify-between cursor-pointer"
                onClick={() => toggleCategory(category.id)}
              >
                <div className="flex items-center">
                  <div className="mr-3">
                    {isChecking ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        <Shield size={20} className="text-white/70" />
                      </motion.div>
                    ) : (
                      <Shield 
                        size={20} 
                        className={`
                          ${category.score / category.maxScore >= 0.9 ? 'text-green-400' : 
                            category.score / category.maxScore >= 0.7 ? 'text-blue-400' : 
                            category.score / category.maxScore >= 0.5 ? 'text-amber-400' : 
                            'text-rose-400'}
                        `} 
                      />
                    )}
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{category.name}</h4>
                    <p className="text-white/70 text-sm">{category.description}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  {!isChecking && (
                    <span className="text-white/70 mr-3">
                      {Math.round((category.score / category.maxScore) * 100)}%
                    </span>
                  )}
                  {category.expanded ? (
                    <ChevronUp size={18} className="text-white/70" />
                  ) : (
                    <ChevronDown size={18} className="text-white/70" />
                  )}
                </div>
              </div>
              
              {/* Category items */}
              {category.expanded && (
                <div className="px-4 pb-4 space-y-3">
                  {category.items.map(item => (
                    <div 
                      key={item.id}
                      className="p-3 rounded-lg bg-white/5 border border-white/10"
                    >
                      <div className="flex items-start">
                        <div className="mt-0.5 mr-3">
                          {item.status === 'pass' ? (
                            <CheckCircle size={16} className="text-green-400" />
                          ) : item.status === 'warning' ? (
                            <AlertCircle size={16} className="text-amber-400" />
                          ) : item.status === 'fail' ? (
                            <XCircle size={16} className="text-rose-400" />
                          ) : (
                            <div className="animate-spin">
                              <Shield size={16} className="text-white/70" />
                            </div>
                          )}
                        </div>
                        <div>
                          <h5 className="text-white text-sm font-medium">{item.title}</h5>
                          <p className="text-white/70 text-xs">{item.description}</p>
                          
                          {item.details && item.status !== 'checking' && (
                            <p className={`text-xs mt-2 ${
                              item.status === 'pass' ? 'text-green-300' : 
                              item.status === 'warning' ? 'text-amber-300' : 
                              'text-rose-300'
                            }`}>
                              {item.details}
                            </p>
                          )}
                          
                          {item.guidelineLink && (
                            <a 
                              href={item.guidelineLink}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center text-xs text-blue-400 hover:text-blue-300 mt-2"
                            >
                              <span>View Guidelines</span>
                              <ExternalLink size={12} className="ml-1" />
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* Action buttons */}
        <div className="flex justify-center space-x-4">
          <button
            className="px-6 py-2 rounded-full bg-white/10 text-white hover:bg-white/20 transition-colors"
            onClick={onBack}
            disabled={isChecking}
          >
            Back
          </button>
          
          <button
            className={`px-8 py-3 rounded-full font-medium transition-colors
              ${!isChecking 
                ? 'bg-white text-indigo-600 hover:bg-white/90 cursor-pointer' 
                : 'bg-white/30 text-white/50 cursor-not-allowed'
              }`}
            onClick={!isChecking ? onContinue : undefined}
            disabled={isChecking}
          >
            {isChecking ? 'Checking Compliance...' : 'Continue'}
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default ComplianceCheckStep;
