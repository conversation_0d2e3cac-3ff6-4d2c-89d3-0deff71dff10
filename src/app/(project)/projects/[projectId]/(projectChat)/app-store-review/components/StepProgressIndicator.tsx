import React from 'react';
import { motion } from 'framer-motion';

interface StepProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  steps: Array<{
    title: string;
    description: string;
  }>;
}

const StepProgressIndicator: React.FC<StepProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  steps
}) => {
  return (
    <div className="w-full max-w-4xl mx-auto mb-8">
      {/* Step Title and Description */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        key={`step-title-${currentStep}`}
        className="text-center mb-6"
      >
        <h2 className="text-2xl font-bold text-white mb-2">
          {steps[currentStep - 1].title}
        </h2>
        <p className="text-white/80">
          {steps[currentStep - 1].description}
        </p>
      </motion.div>

      {/* Progress Bar */}
      <div className="relative h-2 bg-white/20 rounded-full overflow-hidden backdrop-blur-sm">
        <motion.div
          className="absolute top-0 left-0 h-full bg-white rounded-full"
          initial={{ width: `${((currentStep - 1) / totalSteps) * 100}%` }}
          animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
        />
      </div>

      {/* Step Indicators */}
      <div className="flex justify-between mt-2">
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isCompleted = stepNumber < currentStep;
          const isCurrent = stepNumber === currentStep;
          
          return (
            <div key={`step-${stepNumber}`} className="flex flex-col items-center">
              <motion.div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  isCompleted 
                    ? 'bg-white text-indigo-600' 
                    : isCurrent 
                      ? 'bg-white/90 text-indigo-600 ring-4 ring-white/30' 
                      : 'bg-white/30 text-white/70'
                }`}
                initial={false}
                animate={isCurrent ? { scale: [1, 1.1, 1] } : { scale: 1 }}
                transition={{ duration: 0.5, repeat: isCurrent ? Infinity : 0, repeatDelay: 2 }}
              >
                {stepNumber}
              </motion.div>
              <span className={`text-xs mt-1 ${isCurrent ? 'text-white' : 'text-white/70'} hidden md:block`}>
                {step.title.split(' ')[0]}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StepProgressIndicator;
