import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, XCircle, AlertCircle, FileCheck } from 'lucide-react';
import { Platform } from '@/stores/AppStoreReviewStore';

interface InitialVerificationStepProps {
  platform: Platform;
  onContinue: () => void;
  onBack: () => void;
}

interface VerificationItem {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  details?: string;
}

const InitialVerificationStep: React.FC<InitialVerificationStepProps> = ({
  platform,
  onContinue,
  onBack
}) => {
  const [verificationItems, setVerificationItems] = useState<VerificationItem[]>([]);
  const [isVerifying, setIsVerifying] = useState(true);
  const [progress, setProgress] = useState(0);
  const [currentItem, setCurrentItem] = useState('');
  
  // Simulate verification process
  useEffect(() => {
    const items: VerificationItem[] = [
      {
        id: 'app-json',
        name: 'App Configuration',
        description: 'Checking app.json configuration',
        status: 'pending'
      },
      {
        id: 'app-icon',
        name: 'App Icon',
        description: 'Verifying app icon dimensions and format',
        status: 'pending'
      },
      {
        id: 'bundle-id',
        name: 'Bundle Identifier',
        description: platform === 'ios' ? 'Validating iOS bundle identifier' : 'Validating Android package name',
        status: 'pending'
      },
      {
        id: 'version-code',
        name: 'Version Information',
        description: 'Checking version name and code/build number',
        status: 'pending'
      },
      {
        id: 'permissions',
        name: 'Required Permissions',
        description: 'Analyzing required app permissions',
        status: 'pending'
      }
    ];
    
    setVerificationItems(items);
    
    // Simulate verification process
    let currentIndex = 0;
    let currentProgress = 0;
    
    const interval = setInterval(() => {
      if (currentIndex < items.length) {
        setCurrentItem(items[currentIndex].name);
        
        // Update progress
        currentProgress += 5;
        setProgress(Math.min(currentProgress, 100));
        
        // After a delay, update the item status
        if (currentProgress % 20 === 0) {
          const updatedItems = [...items];
          
          // Randomly assign status (mostly success, some warnings, few errors)
          const rand = Math.random();
          let status: 'success' | 'warning' | 'error' = 'success';
          let details = '';
          
          if (rand < 0.7) {
            status = 'success';
          } else if (rand < 0.9) {
            status = 'warning';
            details = getWarningDetails(items[currentIndex].id, platform);
          } else {
            status = 'error';
            details = getErrorDetails(items[currentIndex].id, platform);
          }
          
          updatedItems[currentIndex] = {
            ...updatedItems[currentIndex],
            status,
            details
          };
          
          setVerificationItems(updatedItems);
          currentIndex++;
        }
      } else if (currentProgress < 100) {
        // Complete the progress bar
        currentProgress += 5;
        setProgress(Math.min(currentProgress, 100));
      } else {
        // Verification complete
        setIsVerifying(false);
        clearInterval(interval);
      }
    }, 300);
    
    return () => clearInterval(interval);
  }, [platform]);
  
  // Get warning details based on item id
  const getWarningDetails = (id: string, platform: Platform): string => {
    switch (id) {
      case 'app-icon':
        return platform === 'ios' 
          ? 'App icon transparency is not recommended for iOS. Consider using a solid background.'
          : 'App icon should use the adaptive icon format for better display on different Android devices.';
      case 'permissions':
        return 'Your app is requesting location permissions. Make sure to provide a clear explanation for this in your store listing.';
      default:
        return 'This item may need attention before submission.';
    }
  };
  
  // Get error details based on item id
  const getErrorDetails = (id: string, platform: Platform): string => {
    switch (id) {
      case 'app-json':
        return 'Missing required fields in app configuration. Please update your app.json file.';
      case 'bundle-id':
        return platform === 'ios'
          ? 'Invalid bundle identifier format. Must be in reverse domain notation (e.g., com.company.app).'
          : 'Invalid package name format. Must be in reverse domain notation (e.g., com.company.app).';
      default:
        return 'This item requires attention before submission.';
    }
  };
  
  // Calculate verification status
  const getVerificationStatus = () => {
    if (isVerifying) return 'verifying';
    
    const hasErrors = verificationItems.some(item => item.status === 'error');
    const hasWarnings = verificationItems.some(item => item.status === 'warning');
    
    if (hasErrors) return 'error';
    if (hasWarnings) return 'warning';
    return 'success';
  };
  
  const status = getVerificationStatus();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-3xl mx-auto"
    >
      <div className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 border border-white/20">
        <div className="mb-8 text-center">
          <h3 className="text-2xl font-medium text-white mb-2">Initial Verification</h3>
          <p className="text-white/70">Verifying your app configuration and requirements</p>
        </div>
        
        {/* Progress indicator */}
        {isVerifying && (
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-white/70 text-sm">Verifying...</span>
              <span className="text-white/70 text-sm">{progress}%</span>
            </div>
            <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
              <motion.div 
                className="h-full bg-white rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
            <p className="text-white/70 text-sm mt-2">
              {currentItem ? `Checking: ${currentItem}` : 'Preparing verification...'}
            </p>
          </div>
        )}
        
        {/* Verification results */}
        <div className="space-y-4 mb-8">
          {verificationItems.map(item => (
            <div 
              key={item.id}
              className={`
                p-4 rounded-xl backdrop-blur-sm border
                ${item.status === 'success' ? 'bg-green-500/10 border-green-500/30' : 
                  item.status === 'warning' ? 'bg-amber-500/10 border-amber-500/30' : 
                  item.status === 'error' ? 'bg-rose-500/10 border-rose-500/30' : 
                  'bg-white/5 border-white/10'}
              `}
            >
              <div className="flex items-start">
                <div className="mt-0.5 mr-3">
                  {item.status === 'success' ? (
                    <CheckCircle size={18} className="text-green-400" />
                  ) : item.status === 'warning' ? (
                    <AlertCircle size={18} className="text-amber-400" />
                  ) : item.status === 'error' ? (
                    <XCircle size={18} className="text-rose-400" />
                  ) : (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                      <FileCheck size={18} className="text-white/70" />
                    </motion.div>
                  )}
                </div>
                <div>
                  <h4 className="text-white font-medium">{item.name}</h4>
                  <p className="text-white/70 text-sm">{item.description}</p>
                  {item.details && (
                    <p className={`text-sm mt-2 ${
                      item.status === 'warning' ? 'text-amber-300' : 
                      item.status === 'error' ? 'text-rose-300' : 'text-white/70'
                    }`}>
                      {item.details}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Status summary and actions */}
        {!isVerifying && (
          <div className="text-center">
            <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center">
              {status === 'success' ? (
                <CheckCircle size={40} className="text-green-400" />
              ) : status === 'warning' ? (
                <AlertCircle size={40} className="text-amber-400" />
              ) : (
                <XCircle size={40} className="text-rose-400" />
              )}
            </div>
            
            <h4 className="text-xl font-medium text-white mb-2">
              {status === 'success' 
                ? 'Verification Complete' 
                : status === 'warning'
                ? 'Verification Complete with Warnings'
                : 'Verification Failed'}
            </h4>
            
            <p className="text-white/70 mb-8">
              {status === 'success' 
                ? 'Your app configuration looks good! You can proceed to the next step.' 
                : status === 'warning'
                ? 'Your app has some warnings that you may want to address before submission.'
                : 'Please fix the errors before proceeding to the next step.'}
            </p>
            
            <div className="flex justify-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 rounded-full bg-white/10 text-white hover:bg-white/20"
                onClick={onBack}
              >
                Back
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`px-8 py-3 rounded-full font-medium
                  ${status !== 'error' 
                    ? 'bg-white text-indigo-600 hover:bg-white/90 cursor-pointer' 
                    : 'bg-white/30 text-white/50 cursor-not-allowed'
                  }`}
                onClick={status !== 'error' ? onContinue : undefined}
                disabled={status === 'error'}
              >
                {status === 'error' ? 'Fix Errors to Continue' : 'Continue'}
              </motion.button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default InitialVerificationStep;
