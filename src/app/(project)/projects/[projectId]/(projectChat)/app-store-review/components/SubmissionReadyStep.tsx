import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, ArrowRight, ExternalLink } from 'lucide-react';
import { Platform } from '@/stores/AppStoreReviewStore';

interface SubmissionReadyStepProps {
  platform: Platform;
  onBack: () => void;
}

const SubmissionReadyStep: React.FC<SubmissionReadyStepProps> = ({
  platform,
  onBack
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-3xl mx-auto"
    >
      <div className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 border border-white/20 text-center">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="w-24 h-24 rounded-full bg-gradient-to-br from-green-400 to-emerald-600 mx-auto flex items-center justify-center mb-6"
        >
          <CheckCircle size={48} className="text-white" />
        </motion.div>
        
        <motion.h3 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="text-3xl font-bold text-white mb-4"
        >
          Your App Is Ready for Submission!
        </motion.h3>
        
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7, duration: 0.5 }}
          className="text-white/70 text-lg mb-8 max-w-xl mx-auto"
        >
          Congratulations! You've completed all the necessary steps to prepare your app for the 
          {platform === 'ios' ? ' App Store' : ' Google Play Store'}. Your app is now ready to be submitted.
        </motion.p>
        
        <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/10 text-left">
          <h4 className="text-white text-lg font-medium mb-4">Next Steps:</h4>
          
          <ul className="space-y-3">
            <li className="flex items-start">
              <div className="mr-3 mt-0.5">
                <ArrowRight size={16} className="text-green-400" />
              </div>
              <p className="text-white/70">
                <span className="text-white font-medium">Submit your app</span> through the 
                {platform === 'ios' ? ' App Store Connect' : ' Google Play Console'} dashboard.
              </p>
            </li>
            <li className="flex items-start">
              <div className="mr-3 mt-0.5">
                <ArrowRight size={16} className="text-green-400" />
              </div>
              <p className="text-white/70">
                <span className="text-white font-medium">Monitor the review process</span> and be prepared to 
                address any feedback from reviewers.
              </p>
            </li>
            <li className="flex items-start">
              <div className="mr-3 mt-0.5">
                <ArrowRight size={16} className="text-green-400" />
              </div>
              <p className="text-white/70">
                <span className="text-white font-medium">Prepare for launch</span> by setting up your marketing 
                materials and promotional activities.
              </p>
            </li>
          </ul>
        </div>
        
        <div className="flex justify-center space-x-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-6 py-2 rounded-full bg-white/10 text-white hover:bg-white/20"
            onClick={onBack}
          >
            Back
          </motion.button>
          
          <motion.a
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            href={platform === 'ios' ? 'https://appstoreconnect.apple.com/' : 'https://play.google.com/console/about/'}
            target="_blank"
            rel="noopener noreferrer"
            className="px-8 py-3 rounded-full bg-white text-indigo-600 hover:bg-white/90 font-medium flex items-center"
          >
            Go to {platform === 'ios' ? 'App Store Connect' : 'Google Play Console'}
            <ExternalLink size={16} className="ml-2" />
          </motion.a>
        </div>
      </div>
    </motion.div>
  );
};

export default SubmissionReadyStep;
