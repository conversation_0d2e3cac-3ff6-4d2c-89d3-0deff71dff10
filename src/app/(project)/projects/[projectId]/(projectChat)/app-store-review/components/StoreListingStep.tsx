import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Edit, Star, Globe, Languages, Users, Calendar, ArrowRight } from 'lucide-react';
import { Platform } from '@/stores/AppStoreReviewStore';

interface StoreListingStepProps {
  platform: Platform;
  onContinue: () => void;
  onBack: () => void;
}

interface ListingField {
  id: string;
  name: string;
  description: string;
  value: string;
  placeholder: string;
  maxLength: number;
  required: boolean;
  completed: boolean;
  editing: boolean;
  platform?: 'ios' | 'android' | 'both';
}

interface CategoryOption {
  id: string;
  name: string;
}

const StoreListingStep: React.FC<StoreListingStepProps> = ({
  platform,
  onContinue,
  onBack
}) => {
  const [fields, setFields] = useState<ListingField[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [contentRating, setContentRating] = useState<string>('4+');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [initialized, setInitialized] = useState(false);

  // Initialize fields based on platform without simulated delay
  useEffect(() => {
    setFields(getListingFields(platform));
    setInitialized(true);
  }, [platform]);

  // Update completion percentage when fields change
  useEffect(() => {
    if (fields.length === 0) return;

    const completedFields = fields.filter(field => field.completed).length;
    const percentage = Math.round((completedFields / fields.length) * 100);
    setCompletionPercentage(percentage);
  }, [fields]);

  // Filter fields based on platform - memoized to prevent recalculation on every render
  const filteredFields = useMemo(() => {
    return fields.filter(field =>
      !field.platform || field.platform === platform || field.platform === 'both'
    );
  }, [fields, platform]);

  // Toggle field editing state
  const toggleFieldEdit = (fieldId: string) => {
    setFields(prev =>
      prev.map(field =>
        field.id === fieldId
          ? { ...field, editing: !field.editing }
          : field
      )
    );
  };

  // Update field value - optimized with useCallback
  const updateFieldValue = useCallback((fieldId: string, value: string) => {
    setFields(prev =>
      prev.map(field =>
        field.id === fieldId
          ? {
              ...field,
              value,
              completed: value.trim().length > 0
            }
          : field
      )
    );
    updateProgress();
  }, []);

  // Save field value - optimized with useCallback
  const saveField = useCallback((fieldId: string) => {
    setFields(prev =>
      prev.map(field =>
        field.id === fieldId
          ? { ...field, editing: false }
          : field
      )
    );
  }, []);

  // Handle field change with useCallback for better performance
  const handleFieldChange = useCallback((id: string, value: string) => {
    setFields(prev =>
      prev.map(field =>
        field.id === id
          ? { ...field, value, edited: true }
          : field
      )
    );

    // Update progress
    updateProgress();
  }, []);

  // Update progress based on filled fields - memoized for performance
  const updateProgress = useCallback(() => {
    const totalRequiredFields = fields.filter(f => f.required && (!f.platform || f.platform === platform)).length;
    const filledRequiredFields = fields.filter(f =>
      f.required &&
      (!f.platform || f.platform === platform) &&
      f.value.trim().length > 0
    ).length;

    const newProgress = totalRequiredFields > 0
      ? Math.round((filledRequiredFields / totalRequiredFields) * 100)
      : 0;

    setCompletionPercentage(newProgress);
  }, [fields, platform]);

  // Handle category change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  // Handle content rating change
  const handleContentRatingChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setContentRating(e.target.value);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Check if all required fields are completed
    const allRequiredFieldsCompleted = fields
      .filter(field => field.required)
      .every(field => field.completed);

    if (!allRequiredFieldsCompleted) {
      // Highlight incomplete required fields
      setFields(prev =>
        prev.map(field =>
          field.required && !field.completed
            ? { ...field, editing: true }
            : field
        )
      );
      return;
    }

    // Simulate submission
    setIsSubmitting(true);

    setTimeout(() => {
      setIsSubmitting(false);
      onContinue();
    }, 2000);
  };

  // Get iOS store listing fields
  const getIOSFields = (): ListingField[] => [
    {
      id: 'app-name',
      name: 'App Name',
      description: 'The name of your app as it will appear in the App Store (30 characters max)',
      value: 'My Awesome App',
      placeholder: 'Enter app name',
      maxLength: 30,
      required: true,
      completed: true,
      editing: false
    },
    // ...
  ];

  // Get Android store listing fields
  const getAndroidFields = (): ListingField[] => [
    {
      id: 'app-name',
      name: 'App Name',
      description: 'The name of your app as it will appear in Google Play (50 characters max)',
      value: 'My Awesome App',
      placeholder: 'Enter app name',
      maxLength: 50,
      required: true,
      completed: true,
      editing: false
    },
    // ...
  ];

  // Get listing fields based on platform
  const getListingFields = (platform: Platform): ListingField[] => {
    return platform === 'ios' ? getIOSFields() : getAndroidFields();
  };

  // Get category options based on platform
  const getCategoryOptions = (): CategoryOption[] => {
    if (platform === 'ios') {
      return [
        { id: 'books', name: 'Books' },
        // ...
      ];
    } else {
      return [
        { id: 'applications', name: 'Applications' },
        // ...
      ];
    }
  };

  // Get content rating options based on platform
  const getContentRatingOptions = (): { id: string; name: string }[] => {
    if (platform === 'ios') {
      return [
        { id: '4+', name: '4+ (No objectionable material)' },
        // ...
      ];
    } else {
      return [
        { id: 'everyone', name: 'Everyone' },
        // ...
      ];
    }
  };

  // Get icon for field
  const getFieldIcon = (fieldId: string) => {
    switch (fieldId) {
      case 'app-name':
        return <Star size={18} className="text-amber-400" />;
      // ...
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-4xl mx-auto"
    >
      <div className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 border border-white/20">
        <div className="mb-8 text-center">
          <h3 className="text-2xl font-medium text-white mb-2">Store Listing</h3>
          <p className="text-white/70">
            Prepare your app's listing information for the
            {platform === 'ios' ? ' App Store' : ' Google Play Store'}
          </p>
        </div>

        {isLoading ? (
          // Loading state
          <div className="flex flex-col items-center justify-center py-12">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="mb-4"
            >
              <Loader size={40} className="text-white/70" />
            </motion.div>
            <p className="text-white/70">Loading store listing information...</p>
          </div>
        ) : (
          <>
            {/* Progress indicator */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-2">
                <span className="text-white/70 text-sm">Completion Progress</span>
                <span className="text-white/70 text-sm">{completionPercentage}%</span>
              </div>
              <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-indigo-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${completionPercentage}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>

            {/* Category and content rating */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="backdrop-blur-sm bg-white/5 rounded-xl p-4 border border-white/10">
                <label className="block text-white font-medium mb-2">Category</label>
                <p className="text-white/70 text-sm mb-3">Choose the category that best describes your app</p>
                <select
                  value={selectedCategory}
                  onChange={handleCategoryChange}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  {getCategoryOptions().map(option => (
                    <option key={option.id} value={option.id} className="bg-gray-800">
                      {option.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="backdrop-blur-sm bg-white/5 rounded-xl p-4 border border-white/10">
                <label className="block text-white font-medium mb-2">Content Rating</label>
                <p className="text-white/70 text-sm mb-3">Select the appropriate age rating for your app</p>
                <select
                  value={contentRating}
                  onChange={handleContentRatingChange}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  {getContentRatingOptions().map(option => (
                    <option key={option.id} value={option.id} className="bg-gray-800">
                      {option.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Fields sections - simplified animations */}
            <div className="space-y-8 mt-6">
              {filteredFields.map(field => (
                <div
                  key={field.id}
                  className={`
                    backdrop-blur-sm rounded-xl border p-4
                    ${field.editing
                      ? 'bg-white/10 border-indigo-500/50'
                      : field.completed
                        ? 'bg-green-500/5 border-green-500/30'
                        : 'bg-white/5 border-white/10'}
                    ${field.required && !field.completed && !field.editing ? 'border-amber-500/30' : ''}
                  `}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center">
                      <div className="mr-3 mt-1">
                        {field.completed ? (
                          <CheckCircle size={18} className="text-green-400" />
                        ) : (
                          getFieldIcon(field.id)
                        )}
                      </div>
                      <div>
                        <div className="flex items-center">
                          <h4 className="text-white font-medium">{field.name}</h4>
                          {field.required && (
                            <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-amber-500/20 text-amber-300">
                              Required
                            </span>
                          )}
                        </div>
                        <p className="text-white/70 text-sm">{field.description}</p>
                      </div>
                    </div>
                    
                    {!field.editing && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-3 py-1 rounded-lg bg-white/10 text-white hover:bg-white/20 text-sm"
                        onClick={() => toggleFieldEdit(field.id)}
                      >
                        {field.completed ? 'Edit' : 'Add'}
                      </motion.button>
                    )}
                  </div>
                  
                  {field.editing ? (
                    <div className="mt-4">
                      {field.id === 'description' || field.id === 'full-description' ? (
                        <textarea
                          value={field.value}
                          onChange={(e) => updateFieldValue(field.id, e.target.value)}
                          placeholder={field.placeholder}
                          rows={6}
                          maxLength={field.maxLength}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      ) : (
                        <input
                          type="text"
                          value={field.value}
                          onChange={(e) => updateFieldValue(field.id, e.target.value)}
                          placeholder={field.placeholder}
                          maxLength={field.maxLength}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      )}
                      
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-white/50 text-xs">
                          {field.value.length}/{field.maxLength} characters
                        </span>
                        
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="px-4 py-1.5 rounded-lg bg-indigo-600 text-white hover:bg-indigo-500 text-sm"
                          onClick={() => saveField(field.id)}
                        >
                          Save
                        </motion.button>
                      </div>
                    </div>
                  ) : field.completed ? (
                    <div className="mt-3 bg-white/5 rounded-lg p-3 text-white/90 text-sm">
                      {field.value.length > 100 
                        ? field.value.substring(0, 100) + '...' 
                        : field.value}
                    </div>
                  ) : null}
                </div>
              ))}
            </div>
            
            {/* Action buttons */}
            <div className="flex justify-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 rounded-full bg-white/10 text-white hover:bg-white/20"
                onClick={onBack}
                disabled={isSubmitting}
              >
                Back
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`
                  px-8 py-3 rounded-full font-medium flex items-center
                  ${isSubmitting 
                    ? 'bg-white/30 text-white/50 cursor-not-allowed' 
                    : 'bg-white text-indigo-600 hover:bg-white/90 cursor-pointer'}
                `}
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-2"
                    >
                      <Loader size={16} />
                    </motion.div>
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <span>Save & Continue</span>
                    <ArrowRight size={16} className="ml-2" />
                  </>
                )}
              </motion.button>
            </div>
          </>
        )}
      </div>
    </motion.div>
  );
};

// Missing icon components
const Hash = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.size || 24}
    height={props.size || 24}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={props.className}
  >
    <line x1="4" y1="9" x2="20" y2="9" />
    <line x1="4" y1="15" x2="20" y2="15" />
    <line x1="10" y1="3" x2="8" y2="21" />
    <line x1="16" y1="3" x2="14" y2="21" />
  </svg>
);

const Loader = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.size || 24}
    height={props.size || 24}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={props.className}
  >
    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
  </svg>
);

export default StoreListingStep;
