import React from 'react';
import { motion } from 'framer-motion';
import { Platform } from '@/stores/AppStoreReviewStore';
import { Apple, Smartphone } from 'lucide-react';

interface PlatformSelectionStepProps {
  selectedPlatform: Platform | null;
  onSelectPlatform: (platform: Platform) => void;
  onContinue: () => void;
}

const PlatformSelectionStep: React.FC<PlatformSelectionStepProps> = ({
  selectedPlatform,
  onSelectPlatform,
  onContinue
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-4xl mx-auto"
    >
      <div className="flex flex-col items-center">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          {/* iOS Platform Card */}
          <PlatformCard
            platform="ios"
            icon={<Apple size={40} />}
            title="iOS App"
            description="Publish your app to the Apple App Store"
            isSelected={selectedPlatform === 'ios'}
            onClick={() => onSelectPlatform('ios')}
          />

          {/* Android Platform Card */}
          <PlatformCard
            platform="android"
            icon={<Smartphone size={40} />}
            title="Android App"
            description="Publish your app to the Google Play Store"
            isSelected={selectedPlatform === 'android'}
            onClick={() => onSelectPlatform('android')}
          />
        </div>

        {/* Continue Button */}
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: selectedPlatform ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          className={`mt-12 px-8 py-3 rounded-full font-medium text-lg 
            ${selectedPlatform 
              ? 'bg-white text-indigo-600 hover:bg-white/90 cursor-pointer' 
              : 'bg-white/30 text-white/50 cursor-not-allowed'
            } 
            transition-all duration-300 ease-in-out transform hover:scale-105`}
          onClick={selectedPlatform ? onContinue : undefined}
          disabled={!selectedPlatform}
        >
          Continue to Next Step
        </motion.button>
      </div>
    </motion.div>
  );
};

interface PlatformCardProps {
  platform: Platform;
  icon: React.ReactNode;
  title: string;
  description: string;
  isSelected: boolean;
  onClick: () => void;
}

const PlatformCard: React.FC<PlatformCardProps> = ({
  platform,
  icon,
  title,
  description,
  isSelected,
  onClick
}) => {
  // Define platform-specific gradient colors
  const gradientColors = {
    ios: 'from-blue-500/20 to-indigo-600/20',
    android: 'from-green-500/20 to-emerald-600/20'
  };

  // Define platform-specific glow colors
  const glowColors = {
    ios: 'shadow-blue-500/30',
    android: 'shadow-green-500/30'
  };

  return (
    <motion.div
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.98 }}
      animate={{ 
        boxShadow: isSelected 
          ? '0 0 25px 5px rgba(255, 255, 255, 0.3)' 
          : '0 0 0px 0px rgba(255, 255, 255, 0)'
      }}
      onClick={onClick}
      className={`
        relative overflow-hidden rounded-2xl cursor-pointer
        backdrop-blur-md bg-white/10 border border-white/20
        transition-all duration-300 ease-in-out
        ${isSelected ? `${glowColors[platform]} ring-2 ring-white/50` : ''}
      `}
    >
      {/* Background Gradient */}
      <div className={`absolute inset-0 bg-gradient-to-br ${gradientColors[platform]} opacity-50`} />
      
      {/* Selection Indicator */}
      {isSelected && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute top-3 right-3 w-6 h-6 rounded-full bg-white flex items-center justify-center"
        >
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 3L4.5 8.5L2 6" stroke="#4F46E5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </motion.div>
      )}

      {/* Card Content */}
      <div className="relative z-10 p-6 h-full flex flex-col">
        <div className="mb-4 text-white">{icon}</div>
        <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
        <p className="text-white/80 text-sm">{description}</p>
        
        {/* Platform-specific details */}
        <div className="mt-6 pt-4 border-t border-white/10">
          <div className="flex items-center text-white/70 text-sm">
            {platform === 'ios' ? (
              <>
                <span className="flex items-center">
                  <span className="w-2 h-2 rounded-full bg-blue-400 mr-2"></span>
                  App Store Connect
                </span>
              </>
            ) : (
              <>
                <span className="flex items-center">
                  <span className="w-2 h-2 rounded-full bg-green-400 mr-2"></span>
                  Google Play Console
                </span>
              </>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default PlatformSelectionStep;
