import React, { useMemo } from 'react';

interface MeshGradientBackgroundProps {
  children: React.ReactNode;
  step: number;
  totalSteps: number;
}

const MeshGradientBackground: React.FC<MeshGradientBackgroundProps> = ({ 
  children, 
  step,
  totalSteps 
}) => {
  // Color palettes for different steps
  const colorPalettes = [
    ['#FF3CAC', '#784BA0', '#2B86C5'], // Step 1: Platform Selection (Purple to Blue)
    ['#FF3CAC', '#FF9A8B', '#FF5CAB'], // Step 2: Asset Upload (Pink to Orange)
    ['#8BC6EC', '#9599E2', '#8661C1'], // Step 3: Initial Verification (Blue to Purple)
    ['#8BC6EC', '#36EAEF', '#6DDFC8'], // Step 4: Codebase Analysis (Blue to Teal)
    ['#FF9A8B', '#FF6B95', '#FF3CAC'], // Step 5: Compliance Check (Orange to Pink)
    ['#6DDFC8', '#3CDFFF', '#2B86C5'], // Step 6: Action Items (Teal to Blue)
    ['#784BA0', '#8661C1', '#9599E2']  // Step 7: Store Listing (Purple to Lavender)
  ];

  // Get current color palette based on step - memoized for performance
  const currentPalette = useMemo(() => {
    const normalizedStep = Math.min(Math.max(step - 1, 0), totalSteps - 1);
    return colorPalettes[normalizedStep % colorPalettes.length];
  }, [step, totalSteps]);

  // Create CSS gradient style - memoized for performance
  const gradientStyle = useMemo(() => {
    const [color1, color2, color3] = currentPalette;
    
    return {
      background: `radial-gradient(circle at 50% 50%, ${color1} 0%, ${color2} 50%, ${color3} 100%)`,
      opacity: 0.9,
    };
  }, [currentPalette]);

  return (
    <div className="relative w-full h-full overflow-hidden">
      <div 
        className="absolute top-0 left-0 w-full h-full"
        style={{ 
          ...gradientStyle,
          zIndex: -1,
        }}
      />
      <div className="relative z-10 w-full h-full">
        {children}
      </div>
    </div>
  );
};

export default MeshGradientBackground;
