import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Upload, Check, Image, X, AlertCircle } from 'lucide-react';
import { Platform } from '@/stores/AppStoreReviewStore';

interface AssetUploadStepProps {
  platform: Platform;
  onContinue: () => void;
  onBack: () => void;
}

const AssetUploadStep: React.FC<AssetUploadStepProps> = ({
  platform,
  onContinue,
  onBack
}) => {
  const [uploadedAssets, setUploadedAssets] = useState<{
    appIcon: File | null;
    screenshots: File[];
    featureGraphic: File | null;
  }>({
    appIcon: null,
    screenshots: [],
    featureGraphic: null
  });

  const [dragActive, setDragActive] = useState<string | null>(null);

  const handleDrag = (e: React.DragEvent, type: string, active: boolean) => {
    e.preventDefault();
    e.stopPropagation();
    if (active) setDragActive(type);
    else setDragActive(null);
  };

  const handleDrop = (e: React.DragEvent, type: string) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(null);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files, type);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>, type: string) => {
    e.preventDefault();
    if (e.target.files) {
      handleFiles(e.target.files, type);
    }
  };

  const handleFiles = (files: FileList, type: string) => {
    if (type === 'appIcon' && files[0]) {
      setUploadedAssets(prev => ({ ...prev, appIcon: files[0] }));
    } else if (type === 'featureGraphic' && files[0]) {
      setUploadedAssets(prev => ({ ...prev, featureGraphic: files[0] }));
    } else if (type === 'screenshots') {
      const newScreenshots = Array.from(files);
      setUploadedAssets(prev => ({ 
        ...prev, 
        screenshots: [...prev.screenshots, ...newScreenshots].slice(0, 5) 
      }));
    }
  };

  const removeFile = (type: string, index?: number) => {
    if (type === 'appIcon') {
      setUploadedAssets(prev => ({ ...prev, appIcon: null }));
    } else if (type === 'featureGraphic') {
      setUploadedAssets(prev => ({ ...prev, featureGraphic: null }));
    } else if (type === 'screenshots' && typeof index === 'number') {
      setUploadedAssets(prev => ({
        ...prev,
        screenshots: prev.screenshots.filter((_, i) => i !== index)
      }));
    }
  };

  const isReadyToContinue = () => {
    return uploadedAssets.appIcon && 
           uploadedAssets.screenshots.length >= 1 && 
           (platform === 'ios' || uploadedAssets.featureGraphic);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-4xl mx-auto"
    >
      <div className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 border border-white/20">
        <h3 className="text-xl font-medium text-white mb-6">
          Upload Your App Assets
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* App Icon Upload */}
          <div className="col-span-1">
            <UploadCard
              title="App Icon"
              description={platform === 'ios' ? "1024×1024px PNG" : "512×512px PNG"}
              type="appIcon"
              file={uploadedAssets.appIcon}
              onDrag={handleDrag}
              onDrop={handleDrop}
              onFileInput={handleFileInput}
              onRemove={removeFile}
              dragActive={dragActive === 'appIcon'}
              required
            />
          </div>

          {/* Feature Graphic (Android Only) */}
          {platform === 'android' && (
            <div className="col-span-1">
              <UploadCard
                title="Feature Graphic"
                description="1024×500px JPG or PNG"
                type="featureGraphic"
                file={uploadedAssets.featureGraphic}
                onDrag={handleDrag}
                onDrop={handleDrop}
                onFileInput={handleFileInput}
                onRemove={removeFile}
                dragActive={dragActive === 'featureGraphic'}
                required
              />
            </div>
          )}

          {/* Screenshots Upload */}
          <div className="col-span-2">
            <ScreenshotsUpload
              files={uploadedAssets.screenshots}
              onDrag={handleDrag}
              onDrop={handleDrop}
              onFileInput={handleFileInput}
              onRemove={removeFile}
              dragActive={dragActive === 'screenshots'}
              platform={platform}
            />
          </div>
        </div>

        {/* Asset Requirements */}
        <div className="mt-8 p-4 rounded-xl bg-white/5 border border-white/10">
          <h4 className="text-white flex items-center text-sm font-medium mb-2">
            <AlertCircle size={16} className="mr-2 text-amber-400" />
            Asset Requirements
          </h4>
          <ul className="text-white/70 text-sm space-y-1">
            <li>• App icon must be a square PNG with transparency support</li>
            <li>• Screenshots should show your app's key features</li>
            {platform === 'android' && (
              <li>• Feature graphic appears at the top of your store listing</li>
            )}
            <li>• All images should be high quality and without text overlay</li>
          </ul>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="px-6 py-2 rounded-full bg-white/10 text-white/80 hover:bg-white/20 transition-all duration-300"
            onClick={onBack}
          >
            Back
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`px-8 py-3 rounded-full font-medium 
              ${isReadyToContinue() 
                ? 'bg-white text-indigo-600 hover:bg-white/90 cursor-pointer' 
                : 'bg-white/30 text-white/50 cursor-not-allowed'
              } 
              transition-all duration-300`}
            onClick={isReadyToContinue() ? onContinue : undefined}
            disabled={!isReadyToContinue()}
          >
            Continue
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

interface UploadCardProps {
  title: string;
  description: string;
  type: string;
  file: File | null;
  onDrag: (e: React.DragEvent, type: string, active: boolean) => void;
  onDrop: (e: React.DragEvent, type: string) => void;
  onFileInput: (e: React.ChangeEvent<HTMLInputElement>, type: string) => void;
  onRemove: (type: string) => void;
  dragActive: boolean;
  required?: boolean;
}

const UploadCard: React.FC<UploadCardProps> = ({
  title,
  description,
  type,
  file,
  onDrag,
  onDrop,
  onFileInput,
  onRemove,
  dragActive,
  required
}) => {
  // Preview image if file exists
  const [preview, setPreview] = useState<string | null>(null);

  React.useEffect(() => {
    if (!file) {
      setPreview(null);
      return;
    }

    const objectUrl = URL.createObjectURL(file);
    setPreview(objectUrl);

    return () => URL.revokeObjectURL(objectUrl);
  }, [file]);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-2">
        <label className="text-white text-sm font-medium">
          {title} {required && <span className="text-rose-400">*</span>}
        </label>
        <span className="text-white/60 text-xs">{description}</span>
      </div>

      {!file ? (
        <div
          className={`
            relative h-40 rounded-xl border-2 border-dashed 
            flex flex-col items-center justify-center cursor-pointer
            transition-all duration-300
            ${dragActive 
              ? 'border-white bg-white/20' 
              : 'border-white/30 bg-white/5 hover:bg-white/10'
            }
          `}
          onDragEnter={(e) => onDrag(e, type, true)}
          onDragLeave={(e) => onDrag(e, type, false)}
          onDragOver={(e) => onDrag(e, type, true)}
          onDrop={(e) => onDrop(e, type)}
          onClick={() => document.getElementById(`file-${type}`)?.click()}
        >
          <input
            id={`file-${type}`}
            type="file"
            className="hidden"
            accept="image/*"
            onChange={(e) => onFileInput(e, type)}
          />
          <Upload size={24} className="text-white/70 mb-2" />
          <p className="text-white/70 text-sm text-center">
            Drag & drop or click to upload
          </p>
        </div>
      ) : (
        <div className="relative h-40 rounded-xl overflow-hidden border border-white/20">
          {preview && (
            <img 
              src={preview} 
              alt={title} 
              className="w-full h-full object-contain bg-black/20 backdrop-blur-sm"
            />
          )}
          <button
            className="absolute top-2 right-2 p-1 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            onClick={() => onRemove(type)}
          >
            <X size={16} />
          </button>
          <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm py-1 px-2">
            <p className="text-white text-xs truncate">{file.name}</p>
          </div>
        </div>
      )}
    </div>
  );
};

interface ScreenshotsUploadProps {
  files: File[];
  onDrag: (e: React.DragEvent, type: string, active: boolean) => void;
  onDrop: (e: React.DragEvent, type: string) => void;
  onFileInput: (e: React.ChangeEvent<HTMLInputElement>, type: string) => void;
  onRemove: (type: string, index?: number) => void;
  dragActive: boolean;
  platform: Platform;
}

const ScreenshotsUpload: React.FC<ScreenshotsUploadProps> = ({
  files,
  onDrag,
  onDrop,
  onFileInput,
  onRemove,
  dragActive,
  platform
}) => {
  const [previews, setPreviews] = useState<string[]>([]);

  React.useEffect(() => {
    // Clear old previews
    previews.forEach(url => URL.revokeObjectURL(url));
    
    // Create new previews
    const newPreviews = files.map(file => URL.createObjectURL(file));
    setPreviews(newPreviews);

    return () => {
      newPreviews.forEach(url => URL.revokeObjectURL(url));
    };
  }, [files]);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-2">
        <label className="text-white text-sm font-medium">
          Screenshots <span className="text-rose-400">*</span>
        </label>
        <span className="text-white/60 text-xs">
          {platform === 'ios' ? 'iPhone & iPad screenshots' : 'Phone & tablet screenshots'}
        </span>
      </div>

      <div className="grid grid-cols-5 gap-3">
        {/* Existing Screenshots */}
        {previews.map((preview, index) => (
          <div key={`screenshot-${index}`} className="relative aspect-[9/16] rounded-lg overflow-hidden border border-white/20">
            <img 
              src={preview} 
              alt={`Screenshot ${index + 1}`} 
              className="w-full h-full object-cover"
            />
            <button
              className="absolute top-1 right-1 p-1 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
              onClick={() => onRemove('screenshots', index)}
            >
              <X size={12} />
            </button>
          </div>
        ))}

        {/* Upload More (if less than 5) */}
        {files.length < 5 && (
          <div
            className={`
              relative aspect-[9/16] rounded-lg border-2 border-dashed 
              flex flex-col items-center justify-center cursor-pointer
              transition-all duration-300
              ${dragActive 
                ? 'border-white bg-white/20' 
                : 'border-white/30 bg-white/5 hover:bg-white/10'
              }
            `}
            onDragEnter={(e) => onDrag(e, 'screenshots', true)}
            onDragLeave={(e) => onDrag(e, 'screenshots', false)}
            onDragOver={(e) => onDrag(e, 'screenshots', true)}
            onDrop={(e) => onDrop(e, 'screenshots')}
            onClick={() => document.getElementById('file-screenshots')?.click()}
          >
            <input
              id="file-screenshots"
              type="file"
              multiple
              className="hidden"
              accept="image/*"
              onChange={(e) => onFileInput(e, 'screenshots')}
            />
            <Image size={20} className="text-white/70 mb-1" />
            <p className="text-white/70 text-[10px] text-center">
              Add Screenshot
            </p>
          </div>
        )}

        {/* Placeholder slots */}
        {Array.from({ length: Math.max(0, 4 - files.length) }).map((_, index) => (
          <div 
            key={`placeholder-${index}`}
            className="aspect-[9/16] rounded-lg border border-dashed border-white/10 bg-white/5"
          />
        ))}
      </div>

      <p className="mt-2 text-white/50 text-xs">
        Upload up to 5 screenshots showing your app's key features
      </p>
    </div>
  );
};

export default AssetUploadStep;
