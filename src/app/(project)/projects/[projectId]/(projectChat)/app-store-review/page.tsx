'use client';

import React, { useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Platform } from '@/stores/AppStoreReviewStore';
import { useStores } from "@/stores/utils/useStores";
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, AlertCircle, Loader2, Zap, FileCheck, Shield, Upload, ListChecks, Store } from 'lucide-react';
import dynamic from 'next/dynamic';

// Custom components
import MeshGradientBackground from './components/MeshGradientBackground';
import StepProgressIndicator from './components/StepProgressIndicator';
import PlatformSelectionStep from './components/PlatformSelectionStep';
import AssetUploadStep from './components/AssetUploadStep';

// Implement code splitting with dynamic imports
const InitialVerificationStep = dynamic(
  () => import('./components/InitialVerificationStep'),
  { loading: () => <StepLoadingPlaceholder /> }
);

const ComplianceCheckStep = dynamic(
  () => import('./components/ComplianceCheckStep'),
  { loading: () => <StepLoadingPlaceholder /> }
);

const ActionItemsStep = dynamic(
  () => import('./components/ActionItemsStep'),
  { loading: () => <StepLoadingPlaceholder /> }
);

const StoreListingStep = dynamic(
  () => import('./components/StoreListingStep'),
  { loading: () => <StepLoadingPlaceholder /> }
);

const SubmissionReadyStep = dynamic(
  () => import('./components/SubmissionReadyStep'),
  { loading: () => <StepLoadingPlaceholder /> }
);

// Loading placeholder for dynamic imports
const StepLoadingPlaceholder = () => (
  <div className="flex items-center justify-center w-full h-64 bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-xl animate-pulse">
    <div className="text-white/70">Loading...</div>
  </div>
);

const AppStoreReviewPage: React.FC = observer(() => {
  const { appStoreReviewStore } = useStores();
  
  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  
  // Analysis states
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisStep, setAnalysisStep] = useState('');
  
  // Step definitions - memoized to prevent unnecessary re-renders
  const steps = useMemo(() => [
    { id: 1, title: 'Platform Selection', description: 'Choose your target platform' },
    { id: 2, title: 'Asset Upload', description: 'Upload your app icons and screenshots' },
    { id: 3, title: 'Initial Verification', description: 'Verify basic app configuration' },
    { id: 4, title: 'Codebase Analysis', description: 'Analyze your app code and configuration' },
    { id: 5, title: 'Compliance Check', description: 'Check compliance with store guidelines' },
    { id: 6, title: 'Action Items', description: 'Review and fix compliance issues' },
    { id: 7, title: 'Store Listing', description: 'Prepare your app store listing' },
    { id: 8, title: 'Submission Ready', description: 'Your app is ready for submission' },
  ], []);

  // Navigation handlers
  const goToNextStep = () => {
    if (currentStep < steps.length) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(prev => prev + 1);
        setCompletedSteps(prev => [...prev, currentStep]);
        setIsAnimating(false);
      }, 500);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(prev => prev - 1);
        setIsAnimating(false);
      }, 500);
    }
  };

  // Platform selection handler
  const handlePlatformSelect = (platform: any) => {
    appStoreReviewStore.setSelectedPlatform(platform);
  };

  // Simulated analysis for steps 3 and 4
  const runAnalysis = (stepNumber: number, duration: number = 3000) => {
    setIsAnalyzing(true);
    setAnalysisProgress(0);
    
    const analysisSteps = [
      "Initializing analysis...",
      "Scanning project files...",
      "Checking configurations...",
      "Validating requirements...",
      "Finalizing results..."
    ];
    
    let currentProgress = 0;
    const interval = duration / (analysisSteps.length * 5);
    
    const timer = setInterval(() => {
      currentProgress += 1;
      setAnalysisProgress(currentProgress);
      
      const stepIndex = Math.min(Math.floor(currentProgress / 20), analysisSteps.length - 1);
      setAnalysisStep(analysisSteps[stepIndex]);
      
      if (currentProgress >= 100) {
        clearInterval(timer);
        setIsAnalyzing(false);
        goToNextStep();
      }
    }, interval);
    
    return () => clearInterval(timer);
  };

  // Memoized step rendering to prevent unnecessary re-renders
  const currentStepComponent = useMemo(() => {
    switch (currentStep) {
      case 1:
        return (
          <PlatformSelectionStep
            selectedPlatform={appStoreReviewStore.selectedPlatform}
            onSelectPlatform={handlePlatformSelect}
            onContinue={goToNextStep}
          />
        );
      case 2:
        return (
          <AssetUploadStep
            platform={appStoreReviewStore.selectedPlatform}
            onContinue={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      case 3:
        return (
          <InitialVerificationStep
            platform={appStoreReviewStore.selectedPlatform}
            onContinue={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      case 4:
        return (
          <AnalysisStep
            title="Code Analysis"
            description="Analyzing your codebase for app store compliance"
            isAnalyzing={isAnalyzing}
            progress={analysisProgress}
            currentStep={analysisStep}
            onStart={() => runAnalysis(4, 8000)}
            onBack={goToPreviousStep}
          />
        );
      case 5:
        return (
          <ComplianceCheckStep
            platform={appStoreReviewStore.selectedPlatform}
            onContinue={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      case 6:
        return (
          <ActionItemsStep
            platform={appStoreReviewStore.selectedPlatform}
            onContinue={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      case 7:
        return (
          <StoreListingStep
            platform={appStoreReviewStore.selectedPlatform}
            onContinue={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      case 8:
        return (
          <SubmissionReadyStep
            platform={appStoreReviewStore.selectedPlatform}
            onBack={goToPreviousStep}
          />
        );
      default:
        return (
          <div className="text-white text-center p-8">
            <h3 className="text-xl font-medium mb-4">Step {currentStep}</h3>
            <p>This step is under development</p>
            <div className="mt-8 flex justify-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 rounded-full bg-white/10 text-white hover:bg-white/20"
                onClick={goToPreviousStep}
              >
                Back
              </motion.button>
              {currentStep < steps.length && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-2 rounded-full bg-white text-indigo-600 hover:bg-white/90"
                  onClick={goToNextStep}
                >
                  Continue
                </motion.button>
              )}
            </div>
          </div>
        );
    }
  }, [currentStep, appStoreReviewStore.selectedPlatform, goToNextStep, goToPreviousStep]);

  // Simplified background gradient - static instead of animated for better performance
  const gradientStyle = {
    background: 'linear-gradient(to right bottom, rgba(49, 46, 129, 0.7), rgba(76, 29, 149, 0.7))'
  };

  return (
    <div className="relative w-full min-h-screen overflow-hidden bg-black">
      <div
        className="fixed inset-0 -z-10"
        style={gradientStyle}
      />
      <div className="container mx-auto px-4 py-12 flex flex-col items-center min-h-screen">
        {/* Header with logo and title */}
        <div className="w-full max-w-4xl mb-12 text-center">
          <motion.h1 
            className="text-3xl md:text-4xl font-bold text-white mb-2"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            App Store Review
          </motion.h1>
          <motion.p 
            className="text-white/70"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Prepare your app for submission with our guided process
          </motion.p>
        </div>
        
        {/* Step Progress Indicator */}
        <StepProgressIndicator 
          currentStep={currentStep} 
          totalSteps={steps.length}
          steps={steps.map(step => ({
            title: step.title,
            description: step.description
          }))}
        />
        
        {/* Main Content Area */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`step-${currentStep}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="w-full"
          >
            {currentStepComponent}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
});

// Analysis Step Component
interface AnalysisStepProps {
  title: string;
  description: string;
  isAnalyzing: boolean;
  progress: number;
  currentStep: string;
  onStart: () => void;
  onBack: () => void;
}

const AnalysisStep: React.FC<AnalysisStepProps> = ({
  title,
  description,
  isAnalyzing,
  progress,
  currentStep,
  onStart,
  onBack
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full max-w-2xl mx-auto"
    >
      <div className="backdrop-blur-lg bg-white/10 rounded-3xl p-8 border border-white/20 text-center">
        <div className="mb-8">
          <h3 className="text-2xl font-medium text-white mb-2">{title}</h3>
          <p className="text-white/70">{description}</p>
        </div>

        {!isAnalyzing ? (
          <div className="flex flex-col items-center">
            <div className="w-24 h-24 rounded-full bg-white/10 flex items-center justify-center mb-6">
              <Zap size={40} className="text-white" />
            </div>
            
            <p className="text-white mb-8">
              We'll scan your project and analyze it for compliance with the latest app store guidelines.
            </p>
            
            <div className="flex space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 rounded-full bg-white/10 text-white hover:bg-white/20"
                onClick={onBack}
              >
                Back
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-3 rounded-full bg-white text-indigo-600 hover:bg-white/90 font-medium"
                onClick={onStart}
              >
                Start Analysis
              </motion.button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className="w-24 h-24 rounded-full bg-white/10 flex items-center justify-center mb-6">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Loader2 size={40} className="text-white" />
              </motion.div>
            </div>
            
            <h4 className="text-white text-lg mb-2">{currentStep}</h4>
            
            <div className="w-full max-w-md h-2 bg-white/20 rounded-full overflow-hidden mb-8">
              <motion.div 
                className="h-full bg-white rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            
            <p className="text-white/70 text-sm">
              Please wait while we analyze your project. This may take a few moments.
            </p>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default AppStoreReviewPage;
