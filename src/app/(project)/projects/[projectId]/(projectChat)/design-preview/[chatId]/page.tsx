import React from 'react';
import DesignPreviewPage from '@/components/design-preview/DesignPreviewPage';
import {auth} from "@/app/(auth)/auth";
import {getChatById, getMessagesByChatId, getProjectById} from "@/lib/db/queries";
import {notFound} from "next/navigation";
import {Metadata} from "next";
import {HeaderProvider} from "@/components/generator/HeaderProvider";
import {Suspense} from 'react';
import ProjectsLoading from "@/app/(app)/projects/loading";
import {convertToUIMessages} from "@/lib/utils";

export async function generateMetadata(
    props: { params: Promise<{ chatId: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {chatId} = params;

    const suffix = "Design Preview | magically";

    if (chatId) {
        // fetch data
        const chat = await getChatById({id: chatId});
        return {
            title: chat?.title + " | " + suffix
        }
    } else {
        return {
            title: suffix
        }
    }
}


export default async function DesignPreviewRoute(props: {
    params: Promise<{ chatId: string, projectId: string }>
}) {
    const params = await props.params;
    const {chatId, projectId} = params;
    const chat = await getChatById({id: chatId});
    const session = await auth();
    
    if (!chat) {
        return notFound();
    }
    
    // Verify this is a design chat
    if (chat.type !== 'design') {
        return notFound();
    }
    
    // Get project details
    const project = await getProjectById({ id: projectId });
    
    if (!project) {
        return notFound();
    }
    
    // Fetch messages for this chat
    const messages = await getMessagesByChatId({ id: chatId });

    return (
        <HeaderProvider chatId={chatId} chat={chat} projectId={projectId}>
            <Suspense fallback={<ProjectsLoading />}>
                <DesignPreviewPage
                    projectId={projectId}
                    chatId={chatId}
                    messagesFromDb={convertToUIMessages(messages)}
                    chat={chat}
                />
            </Suspense>
        </HeaderProvider>
    );
}
