import {getProjectById} from "@/lib/db/queries";
import {notFound} from "next/navigation";
import {Metadata} from "next";
import OAuthCallbackHandler from "./components/OAuthCallbackHandler";

export async function generateMetadata(
    props: { params: Promise<{ projectId: string, provider: string }> }
): Promise<Metadata> {
    // read route params
    const params = await props.params;
    const {projectId} = params;

    const suffix = "magically - Create mobile apps in minutes";

    if (projectId) {
        // fetch data
        const project = await getProjectById({id: projectId});
        return {
            title: `Authentication Callback | ${project?.appName || 'Project'} | ${suffix}`,
        };
    } else {
        return {
            title: `Authentication Callback | ${suffix}`,
        };
    }
}

export default async function Page(props: {
    params: Promise<{ projectId: string, provider: string }>;
}) {
    const params = await props.params;
    const {projectId, provider} = params;
    const project = await getProjectById({id: projectId});

    if (!project) {
        return notFound();
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
            <div className="max-w-md w-full bg-card p-6 rounded-lg shadow-lg">
                <h1 className="text-2xl font-bold mb-4">Authentication Complete</h1>
                <p className="mb-6 text-muted-foreground">
                    You have successfully authenticated with {provider}.
                    This window will automatically close and return you to your app.
                </p>
                
                {/* Client component to handle token extraction and communication */}
                <OAuthCallbackHandler projectId={projectId} provider={provider} />
                
                <div className="mt-6 text-sm text-muted-foreground">
                    <p>Project: {project.appName || projectId}</p>
                    <p>If this window doesn't close automatically, you can close it manually.</p>
                </div>
            </div>
        </div>
    );
}