'use client';

import { useEffect, useState } from 'react';

interface OAuthCallbackHandlerProps {
  projectId: string;
  provider: string;
}

export default function OAuthCallbackHandler({ projectId, provider }: OAuthCallbackHandlerProps) {
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
        // Extract tokens from URL
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const queryParams = new URLSearchParams(window.location.search);
        
        // Check for tokens in hash (implicit flow) or query params (code flow)
        const accessToken = hashParams.get('access_token') || queryParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token') || queryParams.get('refresh_token');
        const error = hashParams.get('error') || queryParams.get('error');
        
        if (error) {
          console.error('OAuth error:', error);
          setStatus('error');
          setErrorMessage(error);
          return;
        }
        
        if (!accessToken) {
          console.log('No access token found, this might be the initial redirect');
          return;
        }
        
        console.log('Tokens extracted successfully');
        
        // Send tokens to parent window if this is a popup
        if (window.opener) {
          window.opener.postMessage({
            type: 'supabase-auth-callback',
            projectId,
            provider,
            accessToken,
            refreshToken
          }, '*'); // In production, specify exact origin for security
          
          setStatus('success');
          
          // Close the popup after a short delay
          setTimeout(() => {
            window.close();
          }, 1500);
        } else {
          // If not in a popup, we're likely in the main window or iframe
          // Try to communicate with parent frames
          window.parent.postMessage({
            type: 'supabase-auth-callback',
            projectId,
            provider,
            accessToken,
            refreshToken
          }, '*');
          
          setStatus('success');
          
          // Redirect back to the project page after a delay
          setTimeout(() => {
            window.location.href = `/projects/${projectId}`;
          }, 3000);
        }
      } catch (error) {
        console.error('Error processing OAuth callback:', error);
        setStatus('error');
        setErrorMessage(error instanceof Error ? error.message : 'Unknown error');
      }
    };
    
    processOAuthCallback();
  }, [projectId, provider]);
  
  return (
    <div className="my-4">
      {status === 'processing' && (
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
          <p>Processing authentication...</p>
        </div>
      )}
      
      {status === 'success' && (
        <div className="text-green-500 flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <p>Authentication successful!</p>
          <p className="text-sm text-muted-foreground mt-1">Returning to application...</p>
        </div>
      )}
      
      {status === 'error' && (
        <div className="text-red-500 flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          <p>Authentication failed</p>
          {errorMessage && <p className="text-sm mt-1">{errorMessage}</p>}
          <button 
            className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors"
            onClick={() => window.close()}
          >
            Close Window
          </button>
        </div>
      )}
    </div>
  );
}
