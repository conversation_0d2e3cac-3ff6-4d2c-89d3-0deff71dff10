'use server';

import { auth } from '@/app/(auth)/auth';

/**
 * Get token analytics data for the admin dashboard
 */
export async function getTokenAnalytics(params: { page: number; pageSize: number }) {
  // Auth guard - only allow specific email in production
  const session = await auth();
  
  if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
    throw new Error('Unauthorized');
  }
  
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/admin/token-analytics?page=${params.page}&pageSize=${params.pageSize}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch token analytics');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching token analytics:', error);
    throw new Error('Failed to fetch token analytics');
  }
}

/**
 * Get message trace for a specific parent message
 */
export async function getMessageTrace(parentId: string) {
  // Auth guard - only allow specific email in production
  const session = await auth();
  
  if (process.env.NODE_ENV === 'production' && (!session || !session.user)) {
    throw new Error('Unauthorized');
  }
  
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/admin/token-analytics?parentId=${parentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch message trace');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching message trace:', error);
    throw new Error('Failed to fetch message trace');
  }
}
