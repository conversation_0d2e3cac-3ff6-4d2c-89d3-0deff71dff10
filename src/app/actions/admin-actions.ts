'use server';

import { 
  getAllProjectsWithStats, 
  getLatestProjectChat, 
  SortDirection, 
  SortField,
  getSystemAnalytics,
  searchMessages,
  getAllUsers
} from '@/lib/db/admin-queries';

export interface ProjectsParams {
  page: number;
  pageSize: number;
  sortField: SortField;
  sortDirection: SortDirection;
  searchTerm?: string;
}

/**
 * Server action to get paginated and sorted projects
 */
export async function getProjects(params: ProjectsParams) {
  try {
    return await getAllProjectsWithStats(params);
  } catch (error) {
    console.error('Failed to fetch projects:', error);
    throw new Error('Failed to fetch projects. Please try again.');
  }
}

/**
 * Server action to get the latest chat for a project
 */
export async function getProjectLatestChat(projectId: string) {
  try {
    return await getLatestProjectChat(projectId);
  } catch (error) {
    console.error('Failed to fetch latest chat:', error);
    throw new Error('Failed to fetch latest chat. Please try again.');
  }
}

/**
 * Server action to get system analytics
 */
export async function getAnalytics() {
  try {
    return await getSystemAnalytics();
  } catch (error) {
    console.error('Failed to fetch analytics:', error);
    throw new Error('Failed to fetch analytics. Please try again.');
  }
}

/**
 * Server action to search messages
 */
export async function searchMessageContent(query: string) {
  try {
    return await searchMessages(query);
  } catch (error) {
    console.error('Failed to search messages:', error);
    throw new Error('Failed to search messages. Please try again.');
  }
}

export interface UsersParams {
  page: number;
  pageSize: number;
  sortField: SortField;
  sortDirection: SortDirection;
  searchTerm?: string;
  filters?: Record<string, string[]>;
}

/**
 * Server action to get all users with their stats
 */
export async function getUsers(params: UsersParams) {
  try {
    return await getAllUsers(params);
  } catch (error) {
    console.error('Failed to fetch users:', error);
    throw new Error('Failed to fetch users. Please try again.');
  }
}
