import { Socket, io } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';

export interface TerminalSession {
  sessionId: string;
  isConnected: boolean;
  isInitialized: boolean;
}

export type TerminalOutputListener = (data: string) => void;
export type TerminalErrorListener = (error: string) => void;
export type TerminalStatusListener = (status: { connected: boolean; initialized: boolean }) => void;

class TerminalService {
  private socket: Socket | null = null;
  private sessions: Map<string, TerminalSession> = new Map();
  private outputListeners: Map<string, TerminalOutputListener[]> = new Map();
  private errorListeners: Map<string, TerminalErrorListener[]> = new Map();
  private statusListeners: Map<string, TerminalStatusListener[]> = new Map();
  private isConnecting = false;

  /**
   * Initialize the WebSocket connection to the terminal backend
   */
  public async connect(): Promise<void> {
    if (this.socket?.connected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      // Create socket connection
      this.socket = io({
        path: '/api/terminal-ws',
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      // Set up event listeners
      this.socket.on('connect', this.handleConnect);
      this.socket.on('disconnect', this.handleDisconnect);
      this.socket.on('TERMINAL_OUTPUT', this.handleTerminalOutput);
      this.socket.on('SESSION_INITIALIZED', this.handleSessionInitialized);
      this.socket.on('SESSION_CLOSED', this.handleSessionClosed);
      this.socket.on('ERROR', this.handleError);

      // Wait for connection
      await new Promise<void>((resolve, reject) => {
        if (!this.socket) {
          reject(new Error('Socket not initialized'));
          return;
        }

        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 5000);

        this.socket.once('connect', () => {
          clearTimeout(timeout);
          resolve();
        });

        this.socket.once('connect_error', (err) => {
          clearTimeout(timeout);
          reject(err);
        });
      });
    } catch (error) {
      console.error('Failed to connect to terminal service:', error);
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Initialize a new terminal session
   */
  public async createSession(existingSessionId?: string): Promise<string> {
    await this.ensureConnected();

    const sessionId = existingSessionId || uuidv4();
    
    // Initialize session tracking
    this.sessions.set(sessionId, {
      sessionId,
      isConnected: true,
      isInitialized: false,
    });
    
    this.outputListeners.set(sessionId, []);
    this.errorListeners.set(sessionId, []);
    this.statusListeners.set(sessionId, []);

    // Request session initialization from the server
    this.socket!.emit('INIT_SESSION', { sessionId });

    // Wait for session to be initialized
    await new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Session initialization timeout'));
      }, 10000);

      const onInitialized = (data: { sessionId: string }) => {
        if (data.sessionId === sessionId) {
          clearTimeout(timeout);
          this.socket!.off('SESSION_INITIALIZED', onInitialized);
          this.socket!.off('ERROR', onError);
          resolve();
        }
      };

      const onError = (data: { error: string }) => {
        clearTimeout(timeout);
        this.socket!.off('SESSION_INITIALIZED', onInitialized);
        this.socket!.off('ERROR', onError);
        reject(new Error(data.error));
      };

      this.socket!.on('SESSION_INITIALIZED', onInitialized);
      this.socket!.on('ERROR', onError);
    });

    return sessionId;
  }

  /**
   * Execute a command in the terminal
   */
  public async executeCommand(sessionId: string, command: string): Promise<void> {
    await this.ensureConnected();

    if (!this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} not found`);
    }

    this.socket!.emit('EXECUTE_COMMAND', { sessionId, command });
  }

  /**
   * Send raw input directly to the terminal
   * This is used for interactive terminal input
   */
  public sendRawInput(sessionId: string, data: string): void {
    if (!this.socket?.connected) {
      console.error('Socket not connected');
      return;
    }

    if (!this.sessions.has(sessionId)) {
      console.error(`Session ${sessionId} not found`);
      return;
    }

    // Send the raw input to the server
    this.socket.emit('TERMINAL_INPUT', { sessionId, data });
  }
  
  /**
   * Get direct access to the socket
   * This is used for components that need direct access to socket events
   */
  public getSocket(): Socket | null {
    return this.socket;
  }

  /**
   * Close a terminal session
   */
  public async closeSession(sessionId: string): Promise<void> {
    if (!this.socket?.connected || !this.sessions.has(sessionId)) {
      return;
    }

    this.socket.emit('CLOSE_SESSION', { sessionId });

    // Clean up local resources
    this.sessions.delete(sessionId);
    this.outputListeners.delete(sessionId);
    this.errorListeners.delete(sessionId);
    this.statusListeners.delete(sessionId);
  }

  /**
   * Disconnect from the terminal service
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.sessions.clear();
    this.outputListeners.clear();
    this.errorListeners.clear();
    this.statusListeners.clear();
  }
  
  /**
   * Check if a session exists on the server
   * @param sessionId The ID of the session to check
   * @returns Promise that resolves to true if the session exists, false otherwise
   */
  public async checkSession(sessionId: string): Promise<boolean> {
    if (!sessionId) {
      return false;
    }
    
    try {
      await this.ensureConnected();
      
      return new Promise<boolean>((resolve) => {
        if (!this.socket) {
          resolve(false);
          return;
        }
        
        // Set a timeout in case the server doesn't respond
        const timeout = setTimeout(() => {
          console.warn('Session check timed out');
          resolve(false);
        }, 5000);
        
        // Ask the server if the session exists
        this.socket.emit('CHECK_SESSION', { sessionId }, (response: { exists: boolean }) => {
          clearTimeout(timeout);
          resolve(response.exists);
        });
        
        // Add a one-time error handler
        const errorHandler = () => {
          clearTimeout(timeout);
          resolve(false);
        };
        
        this.socket.once('error', errorHandler);
        
        // Remove the error handler after the timeout
        setTimeout(() => {
          if (this.socket) {
            this.socket.off('error', errorHandler);
          }
        }, 5000);
      });
    } catch (error) {
      console.error('Error checking session:', error);
      return false;
    }
  }

  /**
   * Add a listener for terminal output
   */
  public addOutputListener(sessionId: string, listener: TerminalOutputListener): void {
    const listeners = this.outputListeners.get(sessionId) || [];
    listeners.push(listener);
    this.outputListeners.set(sessionId, listeners);
  }

  /**
   * Remove a listener for terminal output
   */
  public removeOutputListener(sessionId: string, listener: TerminalOutputListener): void {
    const listeners = this.outputListeners.get(sessionId) || [];
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
      this.outputListeners.set(sessionId, listeners);
    }
  }

  /**
   * Add a listener for terminal errors
   */
  public addErrorListener(sessionId: string, listener: TerminalErrorListener): void {
    const listeners = this.errorListeners.get(sessionId) || [];
    listeners.push(listener);
    this.errorListeners.set(sessionId, listeners);
  }

  /**
   * Remove a listener for terminal errors
   */
  public removeErrorListener(sessionId: string, listener: TerminalErrorListener): void {
    const listeners = this.errorListeners.get(sessionId) || [];
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
      this.errorListeners.set(sessionId, listeners);
    }
  }

  /**
   * Add a listener for terminal status changes
   */
  public addStatusListener(sessionId: string, listener: TerminalStatusListener): void {
    const listeners = this.statusListeners.get(sessionId) || [];
    listeners.push(listener);
    this.statusListeners.set(sessionId, listeners);
  }

  /**
   * Remove a listener for terminal status changes
   */
  public removeStatusListener(sessionId: string, listener: TerminalStatusListener): void {
    const listeners = this.statusListeners.get(sessionId) || [];
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
      this.statusListeners.set(sessionId, listeners);
    }
  }

  /**
   * Ensure that the socket is connected
   */
  private async ensureConnected(): Promise<void> {
    if (!this.socket?.connected) {
      await this.connect();
    }
  }

  /**
   * Handle socket connection
   */
  private handleConnect = (): void => {
    console.log('Connected to terminal service');
    
    // Update all sessions
    this.sessions.forEach((session, sessionId) => {
      session.isConnected = true;
      this.notifyStatusListeners(sessionId);
    });
  };

  /**
   * Handle socket disconnection
   */
  private handleDisconnect = (): void => {
    console.log('Disconnected from terminal service');
    
    // Update all sessions
    this.sessions.forEach((session, sessionId) => {
      session.isConnected = false;
      this.notifyStatusListeners(sessionId);
    });
  };

  /**
   * Handle terminal output
   */
  private handleTerminalOutput = (data: { sessionId: string; data: string }): void => {
    const { sessionId, data: output } = data;
    console.log(`TerminalService received output for session ${sessionId}:`, output);
    
    // Emit the raw event to all listeners
    if (this.socket) {
      // Re-emit the event to ensure it's propagated to all components
      // This is a critical step to ensure direct socket listeners receive the event
      console.log(`Re-emitting TERMINAL_OUTPUT event for session ${sessionId}`);
      this.socket.emit('TERMINAL_OUTPUT', { sessionId, data: output });
    }
    
    if (this.sessions.has(sessionId)) {
      const listeners = this.outputListeners.get(sessionId) || [];
      console.log(`Notifying ${listeners.length} listeners for session ${sessionId}`);
      listeners.forEach((listener) => {
        try {
          listener(output);
        } catch (error) {
          console.error('Error in terminal output listener:', error);
        }
      });
    } else {
      console.warn(`No session found for sessionId: ${sessionId}`);
    }
  };

  /**
   * Handle session initialization
   */
  private handleSessionInitialized = (data: { sessionId: string; new: boolean }): void => {
    const { sessionId } = data;
    
    if (this.sessions.has(sessionId)) {
      const session = this.sessions.get(sessionId)!;
      session.isInitialized = true;
      this.notifyStatusListeners(sessionId);
    }
  };

  /**
   * Handle session closure
   */
  private handleSessionClosed = (data: { sessionId: string }): void => {
    const { sessionId } = data;
    
    if (this.sessions.has(sessionId)) {
      this.sessions.delete(sessionId);
      this.outputListeners.delete(sessionId);
      this.errorListeners.delete(sessionId);
      this.statusListeners.delete(sessionId);
    }
  };

  /**
   * Handle errors
   */
  private handleError = (data: { error: string; sessionId?: string }): void => {
    const { error, sessionId } = data;
    
    console.error('Terminal service error:', error);
    
    if (sessionId && this.sessions.has(sessionId)) {
      const listeners = this.errorListeners.get(sessionId) || [];
      listeners.forEach((listener) => {
        try {
          listener(error);
        } catch (err) {
          console.error('Error in terminal error listener:', err);
        }
      });
    }
  };

  /**
   * Notify status listeners for a session
   */
  private notifyStatusListeners(sessionId: string): void {
    if (!this.sessions.has(sessionId)) {
      return;
    }
    
    const session = this.sessions.get(sessionId)!;
    const listeners = this.statusListeners.get(sessionId) || [];
    
    listeners.forEach((listener) => {
      try {
        listener({
          connected: session.isConnected,
          initialized: session.isInitialized,
        });
      } catch (error) {
        console.error('Error in terminal status listener:', error);
      }
    });
  }
}

// Export singleton instance
export const terminalService = new TerminalService();
