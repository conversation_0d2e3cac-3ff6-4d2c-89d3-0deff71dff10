import {toast} from "sonner";

/**
 * Convert a base64 string to a File object
 * @param base64String The base64 string (data URL format)
 * @param filename The filename to use
 * @returns A File object that can be used with FormData
 */
export const base64ToFile = (base64String: string, filename: string): File => {
    // Extract the content type and base64 data
    const [dataType, base64Data] = base64String.split(',');
    const contentType = dataType.match(/:(.*?);/)?.[1] || 'image/jpeg';

    // Decode the base64 string
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    // Create a Blob and then a File
    const blob = new Blob([bytes], {type: contentType});
    return new File([blob], filename, {type: contentType});
};

/**
 * Upload a screenshot from base64 data
 * @param base64Screenshot The base64 screenshot data
 * @param fileName
 * @returns The upload result with URL and other metadata
 */
export const uploadScreenshot = async (base64Screenshot: string, fileName?: string) => {
    // Generate a unique filename with timestamp
    const timestamp = new Date().getTime();
    const filename = (fileName || `component-screenshot-${timestamp}`) + '.jpg';

    // Convert base64 to File
    const file = base64ToFile(base64Screenshot, filename);

    // Use the existing uploadFile function
    return await uploadFile(file);
};

export const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/files/upload', {
            method: 'POST',
            body: formData,
        });

        if (response.ok) {
            const data = await response.json();
            const {url, pathname, contentType} = data;

            return {
                url,
                name: pathname,
                contentType: contentType,
            };
        }
        const {error} = await response.json();
        toast.error(error);
    } catch (error) {
        toast.error('Failed to upload file, please try again!');
    }
};
