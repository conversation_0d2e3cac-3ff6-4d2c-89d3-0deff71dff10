import { terminalService, TerminalOutputListener } from './TerminalService';

/**
 * <PERSON><PERSON> for detecting common prompts in the terminal
 */
export interface PromptPattern {
  // The regex pattern to match
  pattern: RegExp;
  // The response to send when the pattern is matched
  response: string | ((match: RegExpMatchArray) => string);
  // Whether to continue matching after this pattern is matched
  // If false, no more patterns will be matched until new output is received
  continueMatching?: boolean;
  // Description of what this pattern is for (used for logging)
  description: string;
}

/**
 * Default patterns for common EAS build prompts
 */
export const DEFAULT_PROMPT_PATTERNS: PromptPattern[] = [
  {
    pattern: /Would you like to log in to your Expo account\? \(Y\/n\)/i,
    response: 'Y\n',
    description: 'EAS login confirmation'
  },
  {
    pattern: /Generate a new Apple Distribution Certificate\? \(Y\/n\)/i,
    response: 'Y\n',
    description: 'Generate Apple Distribution Certificate'
  },
  {
    pattern: /Generate a new Apple Push Notifications service key\? \(Y\/n\)/i,
    response: 'Y\n',
    description: 'Generate Apple Push Notifications key'
  },
  {
    pattern: /Generate a new Apple Provisioning Profile\? \(Y\/n\)/i,
    response: 'Y\n',
    description: 'Generate Apple Provisioning Profile'
  },
  {
    pattern: /Would you like to automatically create an iOS App ID on Apple Developer Portal\? \(Y\/n\)/i,
    response: 'Y\n',
    description: 'Create iOS App ID'
  },
  {
    pattern: /Do you want to continue\? \(Y\/n\)/i,
    response: 'Y\n',
    description: 'Generic continue confirmation'
  }
];

export interface AutomationOptions {
  // Whether automation is enabled
  enabled: boolean;
  // Custom prompt patterns to use in addition to the default ones
  customPatterns?: PromptPattern[];
  // Whether to use the default patterns
  useDefaultPatterns?: boolean;
  // Callback for when a prompt is detected and automated
  onPromptAutomated?: (pattern: PromptPattern, match: RegExpMatchArray, response: string) => void;
  // Callback for when a prompt is detected but not automated (manual mode)
  onPromptDetected?: (pattern: PromptPattern, match: RegExpMatchArray) => void;
}

export class TerminalAutomationService {
  private sessionId: string | null = null;
  private options: AutomationOptions = {
    enabled: false,
    useDefaultPatterns: true
  };
  private patterns: PromptPattern[] = [];
  private outputBuffer: string = '';
  private outputListener: TerminalOutputListener | null = null;
  private lastMatchedTime: number = 0;
  private isWaitingForInput: boolean = false;
  private lastPromptPattern: PromptPattern | null = null;
  private lastPromptMatch: RegExpMatchArray | null = null;

  /**
   * Initialize the automation service for a specific terminal session
   * @param sessionId The terminal session ID
   * @param options Automation options
   */
  public initialize(sessionId: string, options: AutomationOptions): void {
    this.sessionId = sessionId;
    this.options = {
      ...this.options,
      ...options
    };

    // Set up patterns
    this.patterns = [];
    
    if (this.options.useDefaultPatterns !== false) {
      this.patterns.push(...DEFAULT_PROMPT_PATTERNS);
    }
    
    if (this.options.customPatterns) {
      this.patterns.push(...this.options.customPatterns);
    }

    // Set up output listener
    this.setupOutputListener();
  }

  /**
   * Clean up the automation service
   */
  public cleanup(): void {
    if (this.sessionId && this.outputListener) {
      terminalService.removeOutputListener(this.sessionId, this.outputListener);
      this.outputListener = null;
    }
    
    this.sessionId = null;
    this.outputBuffer = '';
    this.isWaitingForInput = false;
    this.lastPromptPattern = null;
    this.lastPromptMatch = null;
  }

  /**
   * Set whether automation is enabled
   * @param enabled Whether automation is enabled
   */
  public setEnabled(enabled: boolean): void {
    this.options.enabled = enabled;
  }

  /**
   * Check if automation is enabled
   * @returns Whether automation is enabled
   */
  public isEnabled(): boolean {
    return this.options.enabled;
  }

  /**
   * Check if the terminal is currently waiting for input
   * @returns Whether the terminal is waiting for input
   */
  public isWaitingForUserInput(): boolean {
    return this.isWaitingForInput;
  }

  /**
   * Get the last detected prompt pattern
   * @returns The last detected prompt pattern, or null if none
   */
  public getLastPromptPattern(): PromptPattern | null {
    return this.lastPromptPattern;
  }

  /**
   * Get the last detected prompt match
   * @returns The last detected prompt match, or null if none
   */
  public getLastPromptMatch(): RegExpMatchArray | null {
    return this.lastPromptMatch;
  }

  /**
   * Add a custom prompt pattern
   * @param pattern The pattern to add
   */
  public addPattern(pattern: PromptPattern): void {
    this.patterns.push(pattern);
  }

  /**
   * Remove a prompt pattern
   * @param patternToRemove The pattern to remove
   */
  public removePattern(patternToRemove: PromptPattern): void {
    this.patterns = this.patterns.filter(pattern => pattern !== patternToRemove);
  }

  /**
   * Clear all custom patterns
   */
  public clearPatterns(): void {
    this.patterns = this.options.useDefaultPatterns !== false 
      ? [...DEFAULT_PROMPT_PATTERNS]
      : [];
  }

  /**
   * Manually respond to the current prompt
   * @param response The response to send
   */
  public respondToPrompt(response: string): void {
    if (!this.sessionId || !this.isWaitingForInput) {
      console.warn('No active prompt to respond to');
      return;
    }

    // Ensure the response ends with a newline
    if (!response.endsWith('\n')) {
      response += '\n';
    }

    // Send the response
    terminalService.sendRawInput(this.sessionId, response);
    
    // Reset waiting state
    this.isWaitingForInput = false;
    this.lastPromptPattern = null;
    this.lastPromptMatch = null;
  }

  /**
   * Set up the output listener
   */
  private setupOutputListener(): void {
    if (!this.sessionId) {
      return;
    }

    // Remove existing listener if any
    if (this.outputListener) {
      terminalService.removeOutputListener(this.sessionId, this.outputListener);
    }

    // Create new listener
    this.outputListener = (data: string) => {
      // Add to buffer
      this.outputBuffer += data;
      
      // Check for prompts
      this.checkForPrompts();
      
      // Trim buffer if it gets too large
      if (this.outputBuffer.length > 10000) {
        this.outputBuffer = this.outputBuffer.slice(-5000);
      }
    };

    // Add listener
    terminalService.addOutputListener(this.sessionId, this.outputListener);
  }

  /**
   * Check for prompts in the output buffer
   */
  private checkForPrompts(): void {
    // Don't check if automation is disabled
    if (!this.options.enabled) {
      return;
    }

    // Don't check too frequently
    const now = Date.now();
    if (now - this.lastMatchedTime < 500) {
      return;
    }

    // Don't check if already waiting for input
    if (this.isWaitingForInput) {
      return;
    }

    // Check each pattern
    for (const pattern of this.patterns) {
      const match = this.outputBuffer.match(pattern.pattern);
      
      if (match) {
        console.log(`Detected prompt: ${pattern.description}`);
        this.lastMatchedTime = now;
        this.lastPromptPattern = pattern;
        this.lastPromptMatch = match;
        this.isWaitingForInput = true;
        
        // Notify that a prompt was detected
        if (this.options.onPromptDetected) {
          this.options.onPromptDetected(pattern, match);
        }
        
        // If automation is enabled, respond automatically
        if (this.options.enabled) {
          // Generate response
          const response = typeof pattern.response === 'function'
            ? pattern.response(match)
            : pattern.response;
          
          // Send the response
          if (this.sessionId) {
            console.log(`Automating response: ${response.trim()}`);
            terminalService.sendRawInput(this.sessionId, response);
            
            // Notify that a prompt was automated
            if (this.options.onPromptAutomated) {
              this.options.onPromptAutomated(pattern, match, response);
            }
          }
          
          // Reset waiting state
          this.isWaitingForInput = false;
          
          // Clear last prompt if we're not continuing matching
          if (pattern.continueMatching !== true) {
            this.lastPromptPattern = null;
            this.lastPromptMatch = null;
            break;
          }
        }
      }
    }
  }
}

// Export singleton instance
export const terminalAutomationService = new TerminalAutomationService();
