/**
 * ApiClient - A wrapper around fetch that automatically includes anonymous ID from localStorage
 * in request headers when available.
 */

interface ApiClientOptions extends RequestInit {
  skipAuth?: boolean;
}

class ApiClient {
  /**
   * Makes an HTTP request with the same signature as fetch, but automatically
   * includes the anonymous ID from localStorage in the headers if available.
   * 
   * @param url - The URL to request
   * @param options - Request options (same as fetch)
   * @returns Promise with the fetch response
   */
  static async request(url: string, options: ApiClientOptions = {}): Promise<Response> {
    // Create a new headers object from the provided headers or create a new one
    const headers = new Headers(options.headers || {});
    
    // Check if localStorage is available (will not be in SSR context)
    if (!options.skipAuth && typeof window !== 'undefined' && window.localStorage) {
      try {
        // Try to get the anonymous ID from localStorage
        const anonId = localStorage.getItem('anon_id');
        
        // If anonymous ID exists, add it to the headers
        if (anonId) {
          headers.set('X-Anonymous-ID', anonId);
        }
      } catch (error) {
        // Silently handle localStorage errors (e.g., in private browsing mode)
        console.warn('Failed to access localStorage:', error);
      }
    }
    
    // Create the final request options with the updated headers
    const requestOptions: RequestInit = {
      ...options,
      headers
    };
    
    // Make the request using the native fetch
    return fetch(url, requestOptions);
  }
  
  /**
   * Convenience method for GET requests
   */
  static async get(url: string, options: ApiClientOptions = {}): Promise<Response> {
    return ApiClient.request(url, {
      ...options,
      method: 'GET'
    });
  }
  
  /**
   * Convenience method for POST requests
   */
  static async post(url: string, data: any, options: ApiClientOptions = {}): Promise<Response> {
    const contentType = options.headers instanceof Headers 
      ? options.headers.get('Content-Type')
      : options.headers && typeof options.headers === 'object'
        ? (options.headers as any)['Content-Type']
        : null;
    
    // If no content type is set and data is an object, default to JSON
    const isJSON = !contentType && data && typeof data === 'object';
    
    return ApiClient.request(url, {
      ...options,
      method: 'POST',
      headers: {
        ...(isJSON ? { 'Content-Type': 'application/json' } : {}),
        ...(options.headers || {})
      },
      body: isJSON ? JSON.stringify(data) : data
    });
  }
  
  /**
   * Convenience method for PUT requests
   */
  static async put(url: string, data: any, options: ApiClientOptions = {}): Promise<Response> {
    const contentType = options.headers instanceof Headers 
      ? options.headers.get('Content-Type')
      : options.headers && typeof options.headers === 'object'
            ? (options.headers as any)['Content-Type']
        : null;
    
    // If no content type is set and data is an object, default to JSON
    const isJSON = !contentType && data && typeof data === 'object';
    
    return ApiClient.request(url, {
      ...options,
      method: 'PUT',
      headers: {
        ...(isJSON ? { 'Content-Type': 'application/json' } : {}),
        ...(options.headers || {})
      },
      body: isJSON ? JSON.stringify(data) : data
    });
  }
  
  /**
   * Convenience method for DELETE requests
   */
  static async delete(url: string, options: ApiClientOptions = {}): Promise<Response> {
    return ApiClient.request(url, {
      ...options,
      method: 'DELETE'
    });
  }
}

export default ApiClient;