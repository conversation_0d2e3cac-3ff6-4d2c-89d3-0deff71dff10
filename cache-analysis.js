const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

async function analyzeCachePatterns() {
  console.log('Starting cache pattern analysis for May 25th...');
  
  // Path to the CSV file
  const csvFilePath = path.resolve(__dirname, 'cost.csv');
  
  // Store May 25th entries
  const entries = [];
  
  // Read the CSV file
  await new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        if (row.created_at && row.created_at.startsWith('2025-05-25')) {
          entries.push(row);
        }
      })
      .on('end', () => {
        resolve();
      })
      .on('error', (err) => {
        reject(err);
      });
  });
  
  console.log(`Total May 25th entries loaded: ${entries.length}`);
  
  // Sort entries by creation time
  entries.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  
  // Filter for Claude Sonnet 4 entries
  const sonnetEntries = entries.filter(entry => entry.model_permaslug === 'anthropic/claude-4-sonnet-20250522');
  console.log(`Claude Sonnet 4 entries: ${sonnetEntries.length}`);
  
  // Group entries into conversation chains
  const conversationChains = [];
  let currentChain = [];
  let previousTokens = 0;
  
  for (let i = 0; i < sonnetEntries.length; i++) {
    const entry = sonnetEntries[i];
    const promptTokens = parseInt(entry.tokens_prompt || 0);
    
    // Start a new chain if:
    // 1. This is the first entry
    // 2. Current chain already has 10 entries
    // 3. Prompt tokens decreased (indicating a new conversation)
    // 4. Previous entry had a finish reason of "stop" (conversation ended)
    // 5. Time gap > 5 minutes (300000ms)
    const timeGap = i > 0 ? 
      new Date(entry.created_at) - new Date(sonnetEntries[i-1].created_at) : 0;
    
    if (
      currentChain.length === 0 ||
      currentChain.length >= 10 ||
      promptTokens < previousTokens ||
      (currentChain.length > 0 && 
       (currentChain[currentChain.length - 1].finish_reason_normalized === 'stop' ||
        timeGap > 300000))
    ) {
      if (currentChain.length > 0) {
        conversationChains.push([...currentChain]);
      }
      currentChain = [entry];
    } else {
      currentChain.push(entry);
    }
    
    previousTokens = promptTokens;
  }
  
  // Add the last chain if it exists
  if (currentChain.length > 0) {
    conversationChains.push(currentChain);
  }
  
  console.log(`Identified ${conversationChains.length} conversation chains`);
  
  // Analyze cache opportunities
  const cacheAnalysis = {
    totalCost: 0,
    totalTokens: 0,
    cacheBustingPatterns: {
      beginningOfConversation: 0,
      midConversation: 0
    },
    timeGapDistribution: {},
    promptTokenGrowth: [],
    redundantApiCalls: 0,
    potentialSavings: {
      cacheOptimization: 0,
      promptCompression: 0,
      redundantCallElimination: 0,
      tokenCapping: 0
    }
  };
  
  // Analyze each conversation chain
  conversationChains.forEach((chain, chainIndex) => {
    let chainCost = 0;
    let chainTokens = 0;
    let previousPromptTokens = 0;
    let cacheBusts = 0;
    
    chain.forEach((entry, entryIndex) => {
      const cost = parseFloat(entry.cost_total || 0);
      const promptTokens = parseInt(entry.tokens_prompt || 0);
      const completionTokens = parseInt(entry.tokens_completion || 0);
      const totalTokens = promptTokens + completionTokens;
      
      chainCost += cost;
      chainTokens += totalTokens;
      
      // Check for cache cost/credit
      const cacheCost = parseFloat(entry.cost_cache || 0);
      if (cacheCost < 0) {
        // This is a cache hit (credit)
        // No action needed for this analysis
      } else if (cacheCost > 0) {
        // This is a cache miss (cost)
        // Should not happen in normal operation
      }
      
      // Analyze time gaps for cache busting
      if (entryIndex > 0) {
        const timeGap = new Date(entry.created_at) - new Date(chain[entryIndex - 1].created_at);
        const timeGapSeconds = Math.floor(timeGap / 1000);
        const timeGapCategory = timeGapSeconds <= 60 ? "0-60s" :
                               timeGapSeconds <= 120 ? "61-120s" :
                               timeGapSeconds <= 180 ? "121-180s" :
                               timeGapSeconds <= 240 ? "181-240s" :
                               timeGapSeconds <= 300 ? "241-300s" : "300s+";
        
        cacheAnalysis.timeGapDistribution[timeGapCategory] = 
          (cacheAnalysis.timeGapDistribution[timeGapCategory] || 0) + 1;
        
        // Check if this is a potential cache bust
        if (timeGapSeconds <= 300 && promptTokens > previousPromptTokens) {
          // This could have been cached
          if (entryIndex === 1) {
            cacheAnalysis.cacheBustingPatterns.beginningOfConversation++;
          } else {
            cacheAnalysis.cacheBustingPatterns.midConversation++;
          }
          cacheBusts++;
        }
        
        // Calculate prompt token growth
        const tokenGrowth = promptTokens - previousPromptTokens;
        if (tokenGrowth > 0) {
          cacheAnalysis.promptTokenGrowth.push({
            chainIndex,
            entryIndex,
            previousTokens: previousPromptTokens,
            currentTokens: promptTokens,
            growth: tokenGrowth,
            timeGapSeconds
          });
        }
      }
      
      previousPromptTokens = promptTokens;
      
      // Check for redundant API calls (very small completion tokens)
      if (completionTokens < 50 && entryIndex < chain.length - 1) {
        cacheAnalysis.redundantApiCalls++;
        cacheAnalysis.potentialSavings.redundantCallElimination += cost;
      }
      
      // Check for extremely large prompt tokens (potential for compression)
      if (promptTokens > 50000) {
        const potentialReduction = Math.floor(promptTokens * 0.3); // Assume 30% reduction possible
        const potentialSavings = (potentialReduction / totalTokens) * cost;
        cacheAnalysis.potentialSavings.promptCompression += potentialSavings;
      }
    });
    
    cacheAnalysis.totalCost += chainCost;
    cacheAnalysis.totalTokens += chainTokens;
    
    // Calculate potential cache optimization savings
    if (cacheBusts > 0) {
      // Estimate savings from better caching (assume 50% of cache busts could be avoided)
      const potentialCacheSavings = (chainCost / chain.length) * cacheBusts * 0.5;
      cacheAnalysis.potentialSavings.cacheOptimization += potentialCacheSavings;
    }
    
    // Calculate potential token capping savings
    if (chain.length > 5) {
      // For long chains, estimate savings from capping token growth
      const lastFewEntries = chain.slice(-3);
      const avgCostPerEntry = lastFewEntries.reduce((sum, entry) => 
        sum + parseFloat(entry.cost_total || 0), 0) / lastFewEntries.length;
      
      // Assume we could cap tokens to reduce cost by 20% for long chains
      const potentialTokenCapSavings = avgCostPerEntry * chain.length * 0.2;
      cacheAnalysis.potentialSavings.tokenCapping += potentialTokenCapSavings;
    }
  });
  
  // Sort prompt token growth by magnitude
  cacheAnalysis.promptTokenGrowth.sort((a, b) => b.growth - a.growth);
  
  // Calculate total potential savings
  const totalPotentialSavings = Object.values(cacheAnalysis.potentialSavings)
    .reduce((sum, value) => sum + value, 0);
  
  // Print analysis results
  console.log('\n===== CACHE PATTERN ANALYSIS =====');
  console.log(`Total cost: $${cacheAnalysis.totalCost.toFixed(2)}`);
  console.log(`Total tokens: ${cacheAnalysis.totalTokens.toLocaleString()}`);
  
  console.log('\nCache Busting Patterns:');
  console.log(`  Beginning of conversation: ${cacheAnalysis.cacheBustingPatterns.beginningOfConversation}`);
  console.log(`  Mid-conversation: ${cacheAnalysis.cacheBustingPatterns.midConversation}`);
  
  console.log('\nTime Gap Distribution:');
  Object.entries(cacheAnalysis.timeGapDistribution)
    .sort((a, b) => {
      const timeA = parseInt(a[0]);
      const timeB = parseInt(b[0]);
      return timeA - timeB;
    })
    .forEach(([timeGap, count]) => {
      console.log(`  ${timeGap}: ${count} occurrences`);
    });
  
  console.log('\nRedundant API Calls:');
  console.log(`  Count: ${cacheAnalysis.redundantApiCalls}`);
  
  console.log('\nTop 10 Largest Prompt Token Growth:');
  cacheAnalysis.promptTokenGrowth.slice(0, 10).forEach((growth, index) => {
    console.log(`  #${index + 1}: +${growth.growth.toLocaleString()} tokens (${growth.previousTokens.toLocaleString()} → ${growth.currentTokens.toLocaleString()}) [Chain ${growth.chainIndex}, Entry ${growth.entryIndex}]`);
  });
  
  console.log('\n===== POTENTIAL COST SAVINGS =====');
  
  // Sort savings by magnitude
  const sortedSavings = Object.entries(cacheAnalysis.potentialSavings)
    .sort((a, b) => b[1] - a[1]);
  
  sortedSavings.forEach(([category, savings]) => {
    const percentOfTotal = (savings / cacheAnalysis.totalCost * 100).toFixed(2);
    console.log(`${category}: $${savings.toFixed(2)} (${percentOfTotal}% of total cost)`);
  });
  
  console.log(`\nTotal potential savings: $${totalPotentialSavings.toFixed(2)} (${(totalPotentialSavings / cacheAnalysis.totalCost * 100).toFixed(2)}% of total cost)`);
  
  // Simulate what the reduction would mean
  const simulatedCost = cacheAnalysis.totalCost - totalPotentialSavings;
  const simulatedCostPerChain = simulatedCost / conversationChains.length;
  
  console.log('\n===== COST REDUCTION SIMULATION =====');
  console.log(`Current cost: $${cacheAnalysis.totalCost.toFixed(2)}`);
  console.log(`Simulated cost after optimizations: $${simulatedCost.toFixed(2)}`);
  console.log(`Current cost per conversation: $${(cacheAnalysis.totalCost / conversationChains.length).toFixed(2)}`);
  console.log(`Simulated cost per conversation: $${simulatedCostPerChain.toFixed(2)}`);
  
  return {
    cacheAnalysis,
    conversationChains,
    totalPotentialSavings,
    simulatedCost
  };
}

// Run the analysis
analyzeCachePatterns().catch(console.error);
