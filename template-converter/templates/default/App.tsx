
import React from 'react';
import {DarkTheme, DefaultTheme, NavigationContainer} from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { SafeAreaProvider } from "react-native-safe-area-context";
import { Toaster } from 'sonner-native';
import { ThemeProvider } from './providers/ThemeContext';
import TabNavigator from './navigation/TabNavigator';
import { View, StyleSheet, useColorScheme } from 'react-native';

const Stack = createNativeStackNavigator();

function RootStack() {
  return (
    <Stack.Navigator screenOptions={{
      headerShown: false
    }}>
      <Stack.Screen name="MainTabs" component={TabNavigator} />
    </Stack.Navigator>
  );
}

export default function App() {

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Always extend the base theme from react.navigation.
  // Otherwise, error such as cannot read property 'n.medium' of undefined will occur which basically means the fonts property is missing from the theme.
  const navigationTheme = {
    ...(isDark ? DarkTheme : DefaultTheme),
    colors: {
      // Change this to match the app's theme. Either use Dark or light. Add conditional only when theme switching is required.
      ... DefaultTheme.colors
      // isDark ? DarkTheme.colors : DefaultTheme.colors
    },
  };

  return (
    <ThemeProvider>
      <SafeAreaProvider style={styles.container}>
        <Toaster />
        <NavigationContainer theme={navigationTheme}>
          <RootStack />
        </NavigationContainer>
      </SafeAreaProvider>
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  }
});
