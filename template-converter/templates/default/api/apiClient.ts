
// A flexible API client with built-in caching, error handling, and retry logic

import AsyncStorage from '@react-native-async-storage/async-storage';

// Types
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

interface RequestOptions {
  method?: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
  cache?: boolean;
  cacheTTL?: number; // Time to live in seconds
  retries?: number;
  retryDelay?: number; // Delay between retries in ms
}

interface ApiResponse<T> {
  data: T | null;
  error: Error | null;
  status: number;
  headers: Headers;
  cached?: boolean;
}

// Default configuration
const DEFAULT_CONFIG = {
  baseUrl: '',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  cache: true,
  cacheTTL: 300, // 5 minutes
  retries: 2,
  retryDelay: 1000,
};

// Cache helpers
const getCacheKey = (url: string, options: RequestOptions) => {
  const method = options.method || 'GET';
  const body = options.body ? JSON.stringify(options.body) : '';
  return `api_cache_${method}_${url}_${body}`;
};

const getCachedResponse = async <T>(cacheKey: string): Promise<ApiResponse<T> | null> => {
  try {
    const cachedData = await AsyncStorage.getItem(cacheKey);
    if (cachedData) {
      const { data, expiry } = JSON.parse(cachedData);
      if (expiry > Date.now()) {
        return { ...data, cached: true };
      }
    }
    return null;
  } catch (error) {
    console.warn('Cache retrieval error:', error);
    return null;
  }
};

const setCachedResponse = async <T>(
  cacheKey: string, 
  response: ApiResponse<T>, 
  ttl: number
): Promise<void> => {
  try {
    const expiry = Date.now() + ttl * 1000;
    await AsyncStorage.setItem(
      cacheKey,
      JSON.stringify({
        data: response,
        expiry,
      })
    );
  } catch (error) {
    console.warn('Cache storage error:', error);
  }
};

// API client class
export class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private defaultOptions: Omit<RequestOptions, 'method' | 'body' | 'headers'>;
  
  constructor(config: Partial<typeof DEFAULT_CONFIG> = {}) {
    const mergedConfig = { ...DEFAULT_CONFIG, ...config };
    this.baseUrl = mergedConfig.baseUrl;
    this.defaultHeaders = mergedConfig.headers;
    this.defaultOptions = {
      cache: mergedConfig.cache,
      cacheTTL: mergedConfig.cacheTTL,
      retries: mergedConfig.retries,
      retryDelay: mergedConfig.retryDelay,
    };
  }
  
  // Set auth token
  setAuthToken(token: string | null): void {
    if (token) {
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.defaultHeaders['Authorization'];
    }
  }
  
  // Update base URL
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }
  
  // Main request method
  async request<T = any>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
    const method = options.method || 'GET';
    
    // Merge default options with provided options
    const mergedOptions: RequestOptions = {
      ...this.defaultOptions,
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };
    
    // Check cache for GET requests
    if (method === 'GET' && mergedOptions.cache) {
      const cacheKey = getCacheKey(url, mergedOptions);
      const cachedResponse = await getCachedResponse<T>(cacheKey);
      if (cachedResponse) return cachedResponse;
    }
    
    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method,
      headers: mergedOptions.headers,
    };
    
    // Add body for non-GET requests
    if (method !== 'GET' && mergedOptions.body) {
      fetchOptions.body = JSON.stringify(mergedOptions.body);
    }
    
    // Execute request with retry logic
    let lastError: Error | null = null;
    let attempts = 0;
    const maxAttempts = (mergedOptions.retries || 0) + 1;
    
    while (attempts < maxAttempts) {
      try {
        const response = await fetch(url, fetchOptions);
        const responseHeaders = response.headers;
        const status = response.status;
        
        // Parse response
        let data: T | null = null;
        let error: Error | null = null;
        
        try {
          // Check if response has JSON content
          const contentType = responseHeaders.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            data = await response.json();
          } else {
            // Handle non-JSON responses
            const text = await response.text();
            data = text as unknown as T;
          }
        } catch (e) {
          error = new Error('Failed to parse response');
        }
        
        // Handle HTTP errors
        if (!response.ok) {
          error = new Error(
            typeof data === 'object' && data !== null && 'message' in data
              ? String(data.message)
              : `Request failed with status ${status}`
          );
        }
        
        const apiResponse: ApiResponse<T> = {
          data,
          error,
          status,
          headers: responseHeaders,
        };
        
        // Cache successful GET responses
        if (method === 'GET' && mergedOptions.cache && !error) {
          const cacheKey = getCacheKey(url, mergedOptions);
          await setCachedResponse(cacheKey, apiResponse, mergedOptions.cacheTTL || 300);
        }
        
        return apiResponse;
      } catch (e) {
        lastError = e instanceof Error ? e : new Error(String(e));
        attempts++;
        
        if (attempts < maxAttempts) {
          // Wait before retry
          await new Promise(resolve => 
            setTimeout(resolve, mergedOptions.retryDelay || 1000)
          );
        }
      }
    }
    
    // Return error response after all retries failed
    return {
      data: null,
      error: lastError || new Error('Request failed'),
      status: 0,
      headers: new Headers(),
    };
  }
  
  // Convenience methods
  async get<T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }
  
  async post<T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body: data });
  }
  
  async put<T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body: data });
  }
  
  async patch<T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body: data });
  }
  
  async delete<T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }
  
  // Cache management
  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('api_cache_'));
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      console.error('Failed to clear API cache', error);
    }
  }
}

// Create and export a default instance
export const api = new ApiClient();
