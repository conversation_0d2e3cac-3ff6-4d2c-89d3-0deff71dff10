import React, { createContext, useContext, useState } from 'react';
import { createTheme, ColorMode, Theme } from '../theme';

type ThemeContextType = {
  theme: Theme;
  colorMode: ColorMode;
  setColorMode: (mode: ColorMode) => void;
  toggleColorMode: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [colorMode] = useState<ColorMode>('light');
  const [theme] = useState<Theme>(createTheme('light'));

  // These functions are kept for API compatibility but don't change the theme
  const setColorMode = () => {
    // No-op. Implement if needed
  };

  const toggleColorMode = () => {
    // No-op. Implement if needed
  };

  const contextValue: ThemeContextType = {
    theme,
    colorMode: 'light',
    setColorMode,
    toggleColorMode,
  };

  return (
      <ThemeContext.Provider value={contextValue}>
        {children}
      </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};