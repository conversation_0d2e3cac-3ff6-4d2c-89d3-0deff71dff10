
import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

export default function HomeScreen() {

  return (
      <SafeAreaView style={[styles.container]}>
        <StatusBar />

        <View style={styles.content}>
          <Text>Profile</Text>
          <Text>Idea to mobile app in minutes.</Text>
        </View>
      </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    marginBottom: 16,
    fontSize: 20,
    fontWeight: '600',
  },
});
