import React from "react";
import {View, StyleSheet, Text} from "react-native";
import {useTheme} from "../../../providers/ThemeContext";

export const Title = () => {
    const { theme } = useTheme();

    return (
        <View style={styles.content}>
            <Text style={[styles.title, { color: theme.colors.foreground }]}>Explore</Text>
            <Text style={{ color: theme.colors.foreground }}>Prompt, edit, deploy</Text>
        </View>
    )
}

const styles = StyleSheet.create({
    content: {
        flex: 1,
        padding: 16,
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        marginBottom: 16,
        fontSize: 20,
        fontWeight: '600',
    },
});
