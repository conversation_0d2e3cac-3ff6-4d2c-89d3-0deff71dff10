
// A simplified theme system with a single design system

// Color palette with semantic naming
export const palette = {
  // Primary colors
  primary50: '#EBF5FF',
  primary100: '#D1E9FF',
  primary200: '#A4CDFE',
  primary300: '#76B0FB',
  primary400: '#4992F7',
  primary500: '#2563EB', // Primary brand color
  primary600: '#1D4ED8',
  primary700: '#1E40AF',
  primary800: '#1E3A8A',
  primary900: '#172554',

  // Neutral colors
  neutral50: '#F9FAFB',
  neutral100: '#F3F4F6',
  neutral200: '#E5E7EB',
  neutral300: '#D1D5DB',
  neutral400: '#9CA3AF',
  neutral500: '#6B7280',
  neutral600: '#4B5563',
  neutral700: '#374151',
  neutral800: '#1F2937',
  neutral900: '#111827',

  // Success colors
  success50: '#ECFDF5',
  success500: '#10B981',
  success700: '#047857',

  // Warning colors
  warning50: '#FFFBEB',
  warning500: '#F59E0B',
  warning700: '#B45309',

  // Error colors
  error50: '#FEF2F2',
  error500: '#EF4444',
  error700: '#B91C1C',

  // Info colors
  info50: '#EFF6FF',
  info500: '#3B82F6',
  info700: '#1D4ED8',

  // Pure colors
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
};


// Shadows
export const shadows = {
  none: {
    shadowColor: palette.transparent,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: palette.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: palette.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  lg: {
    shadowColor: palette.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  xl: {
    shadowColor: palette.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
};

// Animation durations
export const animation = {
  duration: {
    fastest: 100,
    fast: 200,
    normal: 300,
    slow: 500,
    slowest: 700,
  },
};


// Define light theme
export const lightTheme = {
  colors: {
    // Semantic colors
    background: palette.white,
    foreground: palette.neutral900,
    card: palette.white,
    cardForeground: palette.neutral900,
    popover: palette.white,
    popoverForeground: palette.neutral900,
    primary: palette.primary500,
    primaryForeground: palette.white,
    secondary: palette.neutral100,
    secondaryForeground: palette.neutral900,
    muted: palette.neutral200,
    mutedForeground: palette.neutral500,
    accent: palette.primary100,
    accentForeground: palette.primary900,
    destructive: palette.error500,
    destructiveForeground: palette.white,
    border: palette.neutral200,
    input: palette.neutral200,
    ring: palette.primary500,

    // Status colors
    success: palette.success500,
    warning: palette.warning500,
    error: palette.error500,
    info: palette.info500,
  },
};

// Define dark theme
export const darkTheme = {
  colors: {
    // Semantic colors
    background: palette.neutral900,
    foreground: palette.neutral100,
    card: palette.neutral800,
    cardForeground: palette.neutral100,
    popover: palette.neutral800,
    popoverForeground: palette.neutral100,
    primary: palette.primary400,
    primaryForeground: palette.neutral900,
    secondary: palette.neutral800,
    secondaryForeground: palette.neutral100,
    muted: palette.neutral700,
    mutedForeground: palette.neutral400,
    accent: palette.primary800,
    accentForeground: palette.primary100,
    destructive: palette.error500,
    destructiveForeground: palette.white,
    border: palette.neutral700,
    input: palette.neutral700,
    ring: palette.primary400,

    // Status colors
    success: palette.success500,
    warning: palette.warning500,
    error: palette.error500,
    info: palette.info500,
  }
};

// The default theme is the light theme
export const defaultTheme = lightTheme;

// Color mode type
export type ColorMode = 'light' | 'dark';

// Create theme function
export const createTheme = (mode: ColorMode) => {
  return mode === 'dark' ? darkTheme : lightTheme;
};

// Export types for theme
export type Theme = typeof lightTheme;
