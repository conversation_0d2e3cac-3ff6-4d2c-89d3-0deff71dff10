
import { useState, useCallback } from 'react';

type ValidationRule<T> = {
  validate: (value: T, formValues: Record<string, any>) => boolean;
  message: string;
};

type FieldConfig<T> = {
  initialValue: T;
  required?: boolean;
  requiredMessage?: string;
  rules?: ValidationRule<T>[];
};

type FormConfig = Record<string, FieldConfig<any>>;

type FormState<T extends FormConfig> = {
  values: { [K in keyof T]: T[K]['initialValue'] };
  errors: { [K in keyof T]?: string };
  touched: { [K in keyof T]: boolean };
  isValid: boolean;
  isDirty: boolean;
};

export function useForm<T extends FormConfig>(config: T) {
  // Initialize form state
  const initialValues = Object.entries(config).reduce((acc, [key, field]) => {
    acc[key] = field.initialValue;
    return acc;
  }, {} as Record<string, any>);
  
  const initialTouched = Object.keys(config).reduce((acc, key) => {
    acc[key] = false;
    return acc;
  }, {} as Record<string, boolean>);
  
  const [formState, setFormState] = useState<FormState<T>>({
    values: initialValues as FormState<T>['values'],
    errors: {},
    touched: initialTouched as FormState<T>['touched'],
    isValid: false,
    isDirty: false,
  });
  
  // Validate a single field
  const validateField = useCallback((name: keyof T, value: any) => {
    const fieldConfig = config[name];
    
    // Check if required
    if (fieldConfig.required && 
        (value === undefined || value === null || value === '')) {
      return fieldConfig.requiredMessage || 'This field is required';
    }
    
    // Check validation rules
    if (fieldConfig.rules) {
      for (const rule of fieldConfig.rules) {
        if (!rule.validate(value, formState.values)) {
          return rule.message;
        }
      }
    }
    
    return undefined;
  }, [config, formState.values]);
  
  // Validate all fields
  const validateForm = useCallback(() => {
    const errors: Record<string, string> = {};
    let isValid = true;
    
    Object.keys(config).forEach(key => {
      const error = validateField(key, formState.values[key]);
      if (error) {
        errors[key] = error;
        isValid = false;
      }
    });
    
    return { errors, isValid };
  }, [config, formState.values, validateField]);
  
  // Update form state when a field changes
  const handleChange = useCallback((name: keyof T, value: any) => {
    setFormState(prev => {
      const newValues = { ...prev.values, [name]: value };
      const error = validateField(name, value);
      const newErrors = { ...prev.errors };
      
      if (error) {
        newErrors[name] = error;
      } else {
        delete newErrors[name];
      }
      
      return {
        values: newValues,
        errors: newErrors,
        touched: { ...prev.touched, [name]: true },
        isValid: Object.keys(newErrors).length === 0,
        isDirty: JSON.stringify(newValues) !== JSON.stringify(initialValues),
      };
    });
  }, [initialValues, validateField]);
  
  // Mark a field as touched (e.g., on blur)
  const handleBlur = useCallback((name: keyof T) => {
    setFormState(prev => {
      if (prev.touched[name]) return prev;
      
      const error = validateField(name, prev.values[name]);
      const newErrors = { ...prev.errors };
      
      if (error) {
        newErrors[name] = error;
      } else {
        delete newErrors[name];
      }
      
      return {
        ...prev,
        errors: newErrors,
        touched: { ...prev.touched, [name]: true },
        isValid: Object.keys(newErrors).length === 0,
      };
    });
  }, [validateField]);
  
  // Reset the form to initial values
  const resetForm = useCallback(() => {
    setFormState({
      values: initialValues as FormState<T>['values'],
      errors: {},
      touched: initialTouched as FormState<T>['touched'],
      isValid: false,
      isDirty: false,
    });
  }, [initialValues, initialTouched]);
  
  // Submit handler
  const handleSubmit = useCallback((onSubmit: (values: FormState<T>['values']) => void) => {
    return () => {
      const { errors, isValid } = validateForm();
      
      // Mark all fields as touched
      const allTouched = Object.keys(config).reduce((acc, key) => {
        acc[key] = true;
        return acc;
      }, {} as Record<string, boolean>);
      
      setFormState(prev => ({
        ...prev,
        errors,
        touched: allTouched as FormState<T>['touched'],
        isValid,
      }));
      
      if (isValid) {
        onSubmit(formState.values);
      }
    };
  }, [config, formState.values, validateForm]);
  
  return {
    values: formState.values,
    errors: formState.errors,
    touched: formState.touched,
    isValid: formState.isValid,
    isDirty: formState.isDirty,
    handleChange,
    handleBlur,
    handleSubmit,
    resetForm,
    validateForm,
  };
}
