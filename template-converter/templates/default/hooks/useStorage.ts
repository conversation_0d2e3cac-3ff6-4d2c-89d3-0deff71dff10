
import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface StorageOptions<T> {
  defaultValue: T;
  serialize?: (value: T) => string;
  deserialize?: (value: string) => T;
}

export function useStorage<T>(
  key: string,
  options: StorageOptions<T>
) {
  const { 
    defaultValue, 
    serialize = JSON.stringify, 
    deserialize = JSON.parse 
  } = options;
  
  // State to hold the current value
  const [storedValue, setStoredValue] = useState<T>(defaultValue);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Load the initial value from storage
  useEffect(() => {
    const loadStoredValue = async () => {
      try {
        setIsLoading(true);
        const item = await AsyncStorage.getItem(key);
        
        if (item !== null) {
          try {
            const value = deserialize(item);
            setStoredValue(value);
          } catch (parseError) {
            console.warn(`Failed to parse stored value for key "${key}"`, parseError);
            setStoredValue(defaultValue);
          }
        }
        setError(null);
      } catch (e) {
        console.error(`Error reading from AsyncStorage for key "${key}"`, e);
        setError(e instanceof Error ? e : new Error(String(e)));
      } finally {
        setIsLoading(false);
      }
    };
    
    loadStoredValue();
  }, [key, defaultValue, deserialize]);
  
  // Update the stored value
  const setValue = useCallback(async (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function for previous state updates
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to AsyncStorage
      const serialized = serialize(valueToStore);
      await AsyncStorage.setItem(key, serialized);
      setError(null);
    } catch (e) {
      console.error(`Error saving to AsyncStorage for key "${key}"`, e);
      setError(e instanceof Error ? e : new Error(String(e)));
    }
  }, [key, storedValue, serialize]);
  
  // Remove the item from storage
  const removeValue = useCallback(async () => {
    try {
      await AsyncStorage.removeItem(key);
      setStoredValue(defaultValue);
      setError(null);
    } catch (e) {
      console.error(`Error removing from AsyncStorage for key "${key}"`, e);
      setError(e instanceof Error ? e : new Error(String(e)));
    }
  }, [key, defaultValue]);
  
  return {
    value: storedValue,
    setValue,
    removeValue,
    isLoading,
    error,
  };
}
