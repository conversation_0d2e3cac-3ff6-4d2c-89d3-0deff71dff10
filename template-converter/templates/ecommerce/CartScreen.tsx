import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Image,
    Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useCart } from './CartContext';

const { width } = Dimensions.get('window');

interface CartScreenProps {
    onClose: () => void;
}

export const CartScreen: React.FC<CartScreenProps> = ({ onClose }) => {
    const { items, removeFromCart, updateQuantity, totalItems, totalAmount } = useCart();

    if (items.length === 0) {
        return (
            <View style={styles.container}>
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                    <Text style={styles.closeButtonText}>×</Text>
                </TouchableOpacity>
                <View style={styles.emptyContainer}>
                    <Text style={styles.brandText}>MAISON</Text>
                    <Text style={styles.emptyTitle}>Your cart is empty</Text>
                    <Text style={styles.emptySubtitle}>Start shopping to add items to your cart</Text>
                    <TouchableOpacity style={styles.continueShopping} onPress={onClose}>
                        <Text style={styles.continueShoppingText}>Continue Shopping</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>

            <Text style={styles.brandText}>MAISON</Text>
            <Text style={styles.title}>Shopping Cart</Text>
            <Text style={styles.subtitle}>{totalItems} items</Text>

            <ScrollView style={styles.itemsContainer}>
                {items.map((item, index) => (
                    <View key={`${item.id}-${item.selectedSize}-${item.selectedColor}-${index}`} style={styles.cartItem}>
                        <Image source={{ uri: item.imageUrl }} style={styles.itemImage} />
                        <View style={styles.itemDetails}>
                            <Text style={styles.itemName}>{item.name}</Text>
                            <Text style={styles.itemSize}>Size: {item.selectedSize}</Text>
                            <View style={styles.colorContainer}>
                                <Text style={styles.itemColor}>Color: </Text>
                                <View
                                    style={[styles.colorDot, { backgroundColor: item.selectedColor }]}
                                />
                            </View>
                            <Text style={styles.itemPrice}>${(item.price * item.quantity).toFixed(2)}</Text>

                            <View style={styles.quantityContainer}>
                                <TouchableOpacity
                                    style={styles.quantityButton}
                                    onPress={() => {
                                        if (item.quantity > 1) {
                                            updateQuantity(item.id, item.selectedSize, item.selectedColor, item.quantity - 1);
                                        }
                                    }}
                                >
                                    <Text style={styles.quantityButtonText}>−</Text>
                                </TouchableOpacity>
                                <Text style={styles.quantity}>{item.quantity}</Text>
                                <TouchableOpacity
                                    style={styles.quantityButton}
                                    onPress={() => updateQuantity(item.id, item.selectedSize, item.selectedColor, item.quantity + 1)}
                                >
                                    <Text style={styles.quantityButtonText}>+</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <TouchableOpacity
                            style={styles.removeButton}
                            onPress={() => removeFromCart(item.id, item.selectedSize, item.selectedColor)}
                        >
                            <Text style={styles.removeButtonText}>×</Text>
                        </TouchableOpacity>
                    </View>
                ))}
            </ScrollView>

            <LinearGradient
                colors={['rgba(255,255,255,0.8)', '#ffffff']}
                style={styles.summary}
            >
                <View style={styles.summaryRow}>
                    <Text style={styles.summaryText}>Subtotal</Text>
                    <Text style={styles.summaryAmount}>${totalAmount.toFixed(2)}</Text>
                </View>
                <View style={styles.summaryRow}>
                    <Text style={styles.summaryText}>Shipping</Text>
                    <Text style={styles.summaryAmount}>Free</Text>
                </View>
                <View style={[styles.summaryRow, styles.totalRow]}>
                    <Text style={styles.totalText}>Total</Text>
                    <Text style={styles.totalAmount}>${totalAmount.toFixed(2)}</Text>
                </View>
                <TouchableOpacity style={styles.checkoutButton}>
                    <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
                </TouchableOpacity>
            </LinearGradient>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    closeButton: {
        position: 'absolute',
        top: 50,
        right: 20,
        zIndex: 10,
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    closeButtonText: {
        color: '#fff',
        fontSize: 24,
        lineHeight: 24,
    },
    brandText: {
        fontSize: 20,
        fontWeight: '300',
        letterSpacing: 4,
        color: '#1a1a1a',
        marginTop: 60,
        marginBottom: 16,
        textAlign: 'center',
    },
    title: {
        fontSize: 24,
        fontWeight: '600',
        marginLeft: 20,
        color: '#000',
    },
    subtitle: {
        fontSize: 14,
        color: '#666',
        marginLeft: 20,
        marginBottom: 20,
    },
    itemsContainer: {
        flex: 1,
        paddingHorizontal: 20,
    },
    cartItem: {
        flexDirection: 'row',
        marginBottom: 20,
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    itemImage: {
        width: 100,
        height: 120,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
    },
    itemDetails: {
        flex: 1,
        marginLeft: 15,
    },
    itemName: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 4,
    },
    itemSize: {
        fontSize: 14,
        color: '#666',
        marginBottom: 4,
    },
    colorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
    },
    itemColor: {
        fontSize: 14,
        color: '#666',
    },
    colorDot: {
        width: 16,
        height: 16,
        borderRadius: 8,
        marginLeft: 4,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    itemPrice: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
    },
    quantityContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    quantityButton: {
        width: 28,
        height: 28,
        borderRadius: 14,
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    quantityButtonText: {
        fontSize: 16,
        color: '#000',
    },
    quantity: {
        marginHorizontal: 12,
        fontSize: 16,
    },
    removeButton: {
        padding: 4,
    },
    removeButtonText: {
        fontSize: 20,
        color: '#999',
    },
    summary: {
        padding: 20,
        paddingBottom: 40,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 10,
    },
    summaryText: {
        fontSize: 14,
        color: '#666',
    },
    summaryAmount: {
        fontSize: 14,
        color: '#000',
    },
    totalRow: {
        marginTop: 10,
        paddingTop: 10,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    totalText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#000',
    },
    totalAmount: {
        fontSize: 18,
        fontWeight: '600',
        color: '#000',
    },
    checkoutButton: {
        backgroundColor: '#000',
        paddingVertical: 16,
        borderRadius: 30,
        alignItems: 'center',
        marginTop: 20,
    },
    checkoutButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 40,
    },
    emptyTitle: {
        fontSize: 24,
        fontWeight: '600',
        marginBottom: 12,
        color: '#000',
    },
    emptySubtitle: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
        marginBottom: 30,
    },
    continueShopping: {
        paddingHorizontal: 30,
        paddingVertical: 12,
        borderRadius: 25,
        backgroundColor: '#000',
    },
    continueShoppingText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '500',
    },
});