import React, { createContext, useContext, useState, useCallback } from 'react';
import { Product } from './types';

interface CartItem extends Product {
    quantity: number;
    selectedSize: string;
    selectedColor: string;
}

interface CartContextType {
    items: CartItem[];
    addToCart: (product: Product, size: string, color: string) => void;
    removeFromCart: (productId: string, size: string, color: string) => void;
    updateQuantity: (productId: string, size: string, color: string, quantity: number) => void;
    clearCart: () => void;
    totalItems: number;
    totalAmount: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [items, setItems] = useState<CartItem[]>([]);

    const addToCart = useCallback((product: Product, size: string, color: string) => {
        setItems((currentItems) => {
            const existingItemIndex = currentItems.findIndex(
                (item) =>
                    item.id === product.id &&
                    item.selectedSize === size &&
                    item.selectedColor === color
            );

            if (existingItemIndex > -1) {
                return currentItems.map((item, index) =>
                    index === existingItemIndex
                        ? { ...item, quantity: item.quantity + 1 }
                        : item
                );
            }

            return [...currentItems, { ...product, quantity: 1, selectedSize: size, selectedColor: color }];
        });
    }, []);

    const removeFromCart = useCallback((productId: string, size: string, color: string) => {
        setItems((currentItems) =>
            currentItems.filter(
                (item) =>
                    !(item.id === productId &&
                        item.selectedSize === size &&
                        item.selectedColor === color)
            )
        );
    }, []);

    const updateQuantity = useCallback((productId: string, size: string, color: string, quantity: number) => {
        setItems((currentItems) =>
            currentItems.map((item) =>
                item.id === productId &&
                item.selectedSize === size &&
                item.selectedColor === color
                    ? { ...item, quantity }
                    : item
            )
        );
    }, []);

    const clearCart = useCallback(() => {
        setItems([]);
    }, []);

    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
    const totalAmount = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

    return (
        <CartContext.Provider
            value={{
                items,
                addToCart,
                removeFromCart,
                updateQuantity,
                clearCart,
                totalItems,
                totalAmount,
            }}
        >
            {children}
        </CartContext.Provider>
    );
};

export const useCart = () => {
    const context = useContext(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
};