/**
 * @magic_module: NavigationUtils
 * @magic_purpose: Utility functions for navigation and URL handling
 * @magic_category: Utils/Navigation
 * @magic_keywords: navigation,url,linking,web
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import { Platform } from 'react-native';
import { Linking } from 'react-native';
import { logger } from './logger';

/**
 * Get the URL for a specific screen
 * 
 * This is useful for sharing links, especially on web
 * 
 * @param screen The screen name
 * @param params The screen parameters
 * @returns The URL for the screen
 */
export function getScreenURL(screen: string, params?: Record<string, any>): string {
  try {
    // Create a basic URL for the screen using standard URL construction
    const baseUrl = 'https://example.com';
    const path = `/${screen}`;
    const url = new URL(path, baseUrl);
    
    // Add query parameters if provided
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }
    
    logger.info('Generated screen URL', 'NAVIGATION', { screen, params, url: url.toString() });
    
    return url.toString();
  } catch (error) {
    logger.error('Failed to generate screen URL', 'NAVIGATION', { screen, params, error });
    return '';
  }
}

/**
 * Open a URL in the browser or in the app
 * 
 * @param url The URL to open
 * @returns A promise that resolves when the URL is opened
 */
export async function openURL(url: string): Promise<void> {
  try {
    logger.info('Opening URL', 'NAVIGATION', { url });
    await Linking.openURL(url);
  } catch (error) {
    logger.error('Failed to open URL', 'NAVIGATION', { url, error });
  }
}

/**
 * Share a deep link to a specific screen
 * 
 * @param screen The screen name
 * @param params The screen parameters
 * @param title The title for the share dialog
 * @returns A promise that resolves when the sharing is complete
 */
export async function shareScreenLink(screen: string, params?: Record<string, any>, title?: string): Promise<void> {
  try {
    // Get the URL for the screen
    const url = getScreenURL(screen, params);
    
    // If on web, use the Web Share API if available
    if (Platform.OS === 'web' && typeof navigator !== 'undefined' && navigator.share) {
      await navigator.share({
        title: title || 'Check this out!',
        url,
      });
      
      logger.info('Shared screen link using Web Share API', 'NAVIGATION', { screen, url });
    } else {
      // Otherwise, just copy to clipboard or use native share
      // This would need additional implementation for native platforms
      logger.info('Share functionality not fully implemented for this platform', 'NAVIGATION', { screen, url });
      
      // For now, just open the URL
      await openURL(url);
    }
  } catch (error) {
    logger.error('Failed to share screen link', 'NAVIGATION', { screen, params, error });
  }
}

/**
 * Parse URL parameters from a URL string
 * 
 * @param url The URL to parse
 * @returns An object with the URL parameters
 */
export function parseURLParams(url: string): Record<string, string> {
  try {
    // Create a URL object
    const urlObj = new URL(url);
    
    // Get the search params
    const searchParams = urlObj.searchParams;
    
    // Convert to object
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
    
    logger.info('Parsed URL parameters', 'NAVIGATION', { url, params });
    
    return params;
  } catch (error) {
    logger.error('Failed to parse URL parameters', 'NAVIGATION', { url, error });
    return {};
  }
}
