/**
 * @magic_module: Logger
 * @magic_purpose: Structured logging utility for the application
 * @magic_category: Utils
 * @magic_keywords: logger,logging,debug,error,info,navigation
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */

// Log levels
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

// Log categories
export type LogCategory = 
  | 'GENERAL'
  | 'NAVIGATION'
  | 'NETWORK'
  | 'UI'
  | 'STATE'
  | 'PERFORMANCE';

/**
 * Log entry structure
 * 
 * IMPORTANT: Do not use union types or typeof in this interface as they can break the app
 */
export interface LogEntry {
  timestamp: string;
  level: string; // Use string instead of union type
  category: string; // Use string instead of union type
  message: string;
  data: Record<string, any> | null;
  source: string;
}

/**
 * Structured logger for the application
 * 
 * IMPORTANT: This logger outputs logs in a standard format that can be parsed
 * by the Magically platform. Always use this logger instead of console.log directly.
 * 
 * IMPORTANT: Do not use union types or typeof in this code as they can break the app
 */
class Logger {
  /**
   * Log a debug message
   */
  debug(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {
    this.log(LogLevel.DEBUG, category, message, data, source);
  }

  /**
   * Log an info message
   */
  info(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {
    this.log(LogLevel.INFO, category, message, data, source);
  }

  /**
   * Log a warning message
   */
  warn(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {
    this.log(LogLevel.WARN, category, message, data, source);
  }

  /**
   * Log an error message
   */
  error(message: string, category: LogCategory = 'GENERAL', data: Record<string, any> | null = null, source: string = 'App') {
    this.log(LogLevel.ERROR, category, message, data, source);
  }

  /**
   * Log a navigation event
   */
  navigation(message: string, category: LogCategory = 'NAVIGATION', data: Record<string, any> | null = null) {
    this.log(LogLevel.INFO, category, message, data, 'Navigation');
  }

  /**
   * Log a network request
   */
  network(message: string, data: Record<string, any> | null = null) {
    this.log(LogLevel.INFO, 'NETWORK', message, data, 'Network');
  }

  /**
   * Log a UI event
   */
  ui(message: string, data: Record<string, any> | null = null) {
    this.log(LogLevel.INFO, 'UI', message, data, 'UI');
  }

  /**
   * Internal log method
   */
  private log(level: string, category: string, message: string, data: Record<string, any> | null, source: string) {
    const timestamp = new Date().toISOString();
    
    const logEntry: LogEntry = {
      timestamp,
      level,
      category,
      message,
      data,
      source,
    };
    
    // Format the log entry as JSON for easy parsing
    const formattedLog = JSON.stringify(logEntry);
    
    // Output to console with appropriate styling
    switch (level) {
      case LogLevel.DEBUG:
        console.log(`%c${formattedLog}`, 'color: gray');
        break;
      case LogLevel.INFO:
        console.log(`%c${formattedLog}`, 'color: blue');
        break;
      case LogLevel.WARN:
        console.warn(`%c${formattedLog}`, 'color: orange');
        break;
      case LogLevel.ERROR:
        console.error(`%c${formattedLog}`, 'color: red');
        break;
      default:
        console.log(formattedLog);
    }
    
    // In a real app, you might also send logs to a server or analytics service
  }
}

// Export a singleton instance of the logger
export const logger = new Logger();

// Export a convenience function to get the logger instance
export function getLogger(): Logger {
  return logger;
}
