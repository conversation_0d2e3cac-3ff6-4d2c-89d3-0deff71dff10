/**
 * @magic_component: AppContext
 * @magic_purpose: Global application context for managing app-wide state with persistence
 * @magic_category: Context/Global
 * @magic_keywords: context,state,global,persistence
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '../utils/logger';

/**
 * App state interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface AppState {
  // Whether this is the first launch of the app
  isFirstLaunch: boolean;
  
  // Add more app state properties here as needed
  // Note: Dark mode is now managed by ThemeContext
}

/**
 * App context type interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface AppContextType {
  // Current app state
  appState: AppState;
  
  // Whether the app state is loading
  loading: boolean;
  
  // Error message if any
  error: string | null;
  
  // Set whether this is the first launch
  setFirstLaunch: (isFirst: boolean) => Promise<void>;
}

// Create the context with undefined as default value
const AppContext = createContext<AppContextType | undefined>(undefined);

// Initial state
const initialState: AppState = {
  isFirstLaunch: true,
};

// Storage key
const STORAGE_KEY = 'app_state';

/**
 * App context provider component
 * @magic_component: AppProvider
 * @magic_purpose: Provides global app state with persistence using AsyncStorage
 */
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [appState, setAppState] = useState<AppState>(initialState);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load state from AsyncStorage on mount
  useEffect(() => {
    const loadState = async () => {
      try {
        setLoading(true);
        const storedState = await AsyncStorage.getItem(STORAGE_KEY);
        
        if (storedState) {
          setAppState(JSON.parse(storedState));
        }
      } catch (e) {
        setError('Failed to load app state');
        console.error('Failed to load app state:', e);
      } finally {
        setLoading(false);
      }
    };

    loadState();
  }, []);

  // Save state to AsyncStorage whenever it changes
  useEffect(() => {
    const saveState = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(appState));
      } catch (e) {
        setError('Failed to save app state');
        console.error('Failed to save app state:', e);
      }
    };

    if (!loading) {
      saveState();
    }
  }, [appState, loading]);

  // Note: Dark mode is now managed by ThemeContext

  // Update first launch setting
  const setFirstLaunch = async (isFirst: boolean) => {
    setAppState(prev => ({ ...prev, isFirstLaunch: isFirst }));
  };

  // Context value
  const value: AppContextType = {
    appState,
    loading,
    error,
    setFirstLaunch,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

/**
 * Custom hook to use the app context
 * @magic_hook: useApp
 * @magic_purpose: Provides access to global app state and methods
 */
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  
  return context;
};
