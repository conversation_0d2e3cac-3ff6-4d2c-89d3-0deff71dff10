/**
 * @magic_component: ThemeContext
 * @magic_purpose: Provides theme management for the application
 * @magic_category: Context/Theme
 * @magic_keywords: theme,dark mode,light mode,context,provider
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { COLORS } from '../constants/theme';
import { logger } from '../utils/logger';

// Storage key for theme preference
const THEME_STORAGE_KEY = 'theme_preference';

/**
 * Theme options
 * 
 * IMPORTANT: We use string constants instead of union types to avoid breaking the app
 */
export const ThemeMode = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
};

/**
 * Theme context state interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface ThemeContextState {
  // Current theme mode (light, dark, or system)
  themeMode: string;
  
  // Whether the current appearance is dark (true) or light (false)
  isDarkMode: boolean;
  
  // Theme colors based on current mode
  colors: {
    primary: string;
    primaryLight: string;
    primaryDark: string;
    secondary: string;
    secondaryLight: string;
    secondaryDark: string;
    background: string;
    cardBackground: string;
    text: string;
    textSecondary: string;
    textTertiary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

/**
 * Theme context interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface ThemeContextValue extends ThemeContextState {
  // Set the theme mode (light, dark, or system)
  setThemeMode: (mode: string) => void;
  
  // Toggle between light and dark mode
  toggleTheme: () => void;
}

// Create the context with a default value
const ThemeContext = createContext<ThemeContextValue | null>(null);

/**
 * Theme provider component
 * 
 * Provides theme management for the application
 */
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State for the theme mode
  const [themeMode, setThemeModeState] = useState<string>(ThemeMode.SYSTEM);
  
  // State for whether the device is in dark mode
  const [isDeviceDarkMode, setIsDeviceDarkMode] = useState<boolean>(false);
  
  // Calculate whether we're in dark mode based on theme mode and device setting
  const isDarkMode = 
    themeMode === ThemeMode.DARK || 
    (themeMode === ThemeMode.SYSTEM && isDeviceDarkMode);
  
  // Load theme preference from storage on mount
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (storedTheme) {
          setThemeModeState(storedTheme);
          logger.info('Theme preference loaded', 'STATE', { themeMode: storedTheme });
        }
      } catch (error) {
        logger.error('Failed to load theme preference', 'STATE', { error });
      }
    };
    
    loadThemePreference();
  }, []);
  
  // Save theme preference when it changes
  const setThemeMode = async (mode: string) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
      setThemeModeState(mode);
      logger.info('Theme preference saved', 'STATE', { themeMode: mode });
    } catch (error) {
      logger.error('Failed to save theme preference', 'STATE', { error });
    }
  };
  
  // Toggle between light and dark mode
  const toggleTheme = () => {
    const newMode = isDarkMode ? ThemeMode.LIGHT : ThemeMode.DARK;
    setThemeMode(newMode);
    logger.ui('Theme toggled', { from: themeMode, to: newMode });
  };
  
  // Get colors based on current theme
  const colors = isDarkMode ? {
    // Dark theme colors
    primary: COLORS.primary,
    primaryLight: COLORS.primaryLight,
    primaryDark: COLORS.primaryDark,
    secondary: COLORS.secondary,
    secondaryLight: COLORS.secondaryLight,
    secondaryDark: COLORS.secondaryDark,
    background: '#121212',
    cardBackground: '#1E1E1E',
    text: '#FFFFFF',
    textSecondary: '#A0A0A0',
    textTertiary: '#6C6C6C',
    border: '#2A2A2A',
    success: COLORS.success,
    warning: COLORS.warning,
    error: COLORS.error,
    info: COLORS.info,
  } : {
    // Light theme colors
    primary: COLORS.primary,
    primaryLight: COLORS.primaryLight,
    primaryDark: COLORS.primaryDark,
    secondary: COLORS.secondary,
    secondaryLight: COLORS.secondaryLight,
    secondaryDark: COLORS.secondaryDark,
    background: COLORS.background,
    cardBackground: COLORS.cardBackground,
    text: COLORS.text,
    textSecondary: COLORS.textSecondary,
    textTertiary: COLORS.textTertiary,
    border: COLORS.border,
    success: COLORS.success,
    warning: COLORS.warning,
    error: COLORS.error,
    info: COLORS.info,
  };
  
  // Context value
  const contextValue: ThemeContextValue = {
    themeMode,
    isDarkMode,
    colors,
    setThemeMode,
    toggleTheme,
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use the theme context
 * 
 * IMPORTANT: This must be used within a ThemeProvider
 */
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};
