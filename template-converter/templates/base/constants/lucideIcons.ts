/**
 * @magic_module: LucideIcons
 * @magic_purpose: List of supported Lucide React icons for use in the application
 * @magic_category: Constants
 * @magic_keywords: icons,lucide,svg,ui
 */

/**
 * List of supported Lucide React icons
 * 
 * IMPORTANT: When using icons in the application, only use icons from this list
 * to ensure compatibility and consistent styling.
 * 
 * For more information, visit: https://lucide.dev/icons/
 */
export const LUCIDE_ICONS = [
  // Essential UI icons
  'activity',
  'alert-circle',
  'alert-triangle',
  'archive',
  'arrow-down',
  'arrow-left',
  'arrow-right',
  'arrow-up',
  'bell',
  'bookmark',
  'calendar',
  'check',
  'check-circle',
  'chevron-down',
  'chevron-left',
  'chevron-right',
  'chevron-up',
  'clipboard',
  'clock',
  'copy',
  'edit',
  'external-link',
  'eye',
  'eye-off',
  'file',
  'filter',
  'flag',
  'folder',
  'heart',
  'help-circle',
  'home',
  'image',
  'info',
  'link',
  'list',
  'lock',
  'log-in',
  'log-out',
  'mail',
  'map',
  'map-pin',
  'menu',
  'message-circle',
  'message-square',
  'minus',
  'minus-circle',
  'more-horizontal',
  'more-vertical',
  'paperclip',
  'plus',
  'plus-circle',
  'refresh-cw',
  'save',
  'search',
  'send',
  'settings',
  'share',
  'shopping-bag',
  'shopping-cart',
  'star',
  'sun',
  'moon',
  'tag',
  'trash',
  'trash-2',
  'upload',
  'user',
  'users',
  'x',
  'x-circle',
  'zap',
  
  // Device and tech icons
  'battery',
  'bluetooth',
  'camera',
  'cast',
  'cloud',
  'code',
  'database',
  'download',
  'hard-drive',
  'headphones',
  'mic',
  'monitor',
  'phone',
  'printer',
  'server',
  'smartphone',
  'tablet',
  'tv',
  'wifi',
  
  // Media and content icons
  'fast-forward',
  'film',
  'music',
  'pause',
  'play',
  'play-circle',
  'rewind',
  'skip-back',
  'skip-forward',
  'stop-circle',
  'video',
  'volume',
  'volume-1',
  'volume-2',
  'volume-x',
  
  // Social and communication icons
  'facebook',
  'github',
  'instagram',
  'linkedin',
  'twitter',
  'youtube',
] as const;

/**
 * Type for Lucide icon names
 * Use this type for icon props to ensure type safety
 */
export type LucideIconName = typeof LUCIDE_ICONS[number];

/**
 * Helper function to check if an icon name is a valid Lucide icon
 * @param name The icon name to check
 * @returns Whether the icon is valid
 */
export const isValidLucideIcon = (name: string): name is LucideIconName => {
  return LUCIDE_ICONS.includes(name as LucideIconName);
};
