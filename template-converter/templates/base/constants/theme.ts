/**
 * @magic_module: Theme
 * @magic_purpose: Defines the application's design system including colors, typography, spacing, and other UI constants
 * @magic_category: Constants
 * @magic_keywords: theme,colors,typography,spacing,design
 */
import { TextStyle } from 'react-native';

// Color palette
export const COLORS = {
  // Primary colors
  primary: '#4A6FFF',
  primaryLight: '#7B93FF',
  primaryDark: '#3257E8',
  
  // Secondary colors
  secondary: '#FF6B6B',
  secondaryLight: '#FF9B9B',
  secondaryDark: '#E54F4F',
  
  // Background colors
  background: '#F9FAFC',
  cardBackground: '#FFFFFF',
  
  // Text colors
  text: '#1A1D26',
  textSecondary: '#767A8A',
  textTertiary: '#A0A4B8',
  
  // Status colors
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336',
  info: '#2196F3',
  
  // Utility colors
  border: '#E5E9F2',
  shadow: 'rgba(0, 0, 0, 0.05)',
  overlay: 'rgba(0, 0, 0, 0.5)',
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
};

// Font family
export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
};

// Typography
export const TYPOGRAPHY: Record<string, TextStyle> = {
  h1: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 34,
    fontFamily: FONTS.bold,
  },
  h2: {
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 30,
    fontFamily: FONTS.bold,
  },
  h3: {
    fontSize: 20,
    fontWeight: 'bold',
    lineHeight: 26,
    fontFamily: FONTS.bold,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22,
    fontFamily: FONTS.medium,
  },
  body: {
    fontSize: 14,
    fontWeight: 'normal',
    lineHeight: 20,
    fontFamily: FONTS.regular,
  },
  caption: {
    fontSize: 12,
    fontWeight: 'normal',
    lineHeight: 18,
    fontFamily: FONTS.regular,
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22,
    fontFamily: FONTS.medium,
  },
};

// Spacing scale
export const SPACING = {
  tiny: 4,
  small: 8,
  medium: 16,
  large: 24,
  xlarge: 32,
  xxlarge: 48,
};

// Border radius
export const BORDER_RADIUS = {
  small: 4,
  medium: 8,
  large: 12,
  xlarge: 16,
  round: 999,
};

// Shadows
export const createShadow = (elevation: number = 1) => ({
  shadowColor: COLORS.shadow,
  shadowOffset: { width: 0, height: elevation * 2 },
  shadowOpacity: 0.1,
  shadowRadius: elevation * 3,
  elevation,
});

// Common style mixins
export const MIXINS = {
  row: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  center: {
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  shadow: createShadow(2),
};
