/**
 * @magic_component: HomeTab
 * @magic_purpose: Home tab screen showing the main content
 * @magic_category: Screens/Tabs
 * @magic_keywords: home,tab,screen,main
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  RefreshControl,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { logger } from '../../utils/logger';

// Import components and utilities
import HomeHeader from '../../components/screens/HomeScreen/HomeHeader';
import Button from '../../components/ui/Button';
import Skeleton from '../../components/ui/Skeleton';
import { useApp } from '../../contexts/AppContext';
import { formatDate } from '../../utils/helpers';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';
import { HomeScreenNavigationProp } from '../../navigation/types';
import { useTheme } from '../../contexts/ThemeContext';

// Get screen dimensions for responsive layouts
const { width: screenWidth } = Dimensions.get('window');

/**
 * HomeTab component showing the main content of the app
 */
export default function HomeTab() {
  // Navigation
  const navigation = useNavigation<HomeScreenNavigationProp>();
  
  // Use theme from ThemeContext
  const { isDarkMode, colors } = useTheme();
  
  // App context - used for non-theme related state
  const { appState } = useApp();
  
  // State
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // Log theme state for debugging
  useEffect(() => {
    logger.info('Theme state in HomeTab', 'UI', {
      isDarkMode,
      themeMode: 'Using ThemeContext directly'
    });
  }, [isDarkMode]);
  
  // Simulate loading data
  useEffect(() => {
    // Simulate API loading delay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };
  
  // Handle modal open
  const handleOpenModal = () => {
    // Use React Navigation to open a modal screen
    navigation.navigate('ModalExample');
  };
  
  // Handle bottom sheet open
  const handleOpenBottomSheet = () => {
    // Use React Navigation to open a bottom sheet screen
    navigation.navigate('BottomSheetExample');
  };
  
  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      {/* Home Header */}
      <HomeHeader title="Home" />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <View style={styles.tabContent}>
          <Text style={[styles.sectionTitle, isDarkMode && styles.darkText]}>
            Welcome to Magically
          </Text>
          
          <Text style={[styles.sectionDescription, isDarkMode && styles.darkTextSecondary]}>
            This is a starter template for your next amazing app. Explore the features and components available.
          </Text>
          
          {/* Buttons to open modal and bottom sheet screens - vertical alignment */}
          <View style={styles.buttonContainer}>
            <Button
              title="Open Modal"
              onPress={handleOpenModal}
              style={styles.actionButton}
              variant="primary"
              icon="layers"
            />
            
            <Button
              title="Open Bottom Sheet"
              onPress={handleOpenBottomSheet}
              style={styles.actionButton}
              variant="secondary"
              icon="arrow-up"
            />
          </View>
          
          {/* Content */}
          {isLoading ? (
            <View style={styles.skeletonContainer}>
              <Skeleton
                style={styles.skeletonCard}
                width="100%"
                height={200}
              />
              <View style={styles.skeletonContent}>
                <Skeleton width="70%" height={24} />
                <Skeleton width="100%" height={16} style={{ marginTop: 8 }} />
                <Skeleton width="100%" height={16} style={{ marginTop: 4 }} />
                <Skeleton width="60%" height={16} style={{ marginTop: 4 }} />
              </View>
            </View>
          ) : (
            <View style={styles.contentContainer}>
              <Text style={[styles.sectionTitle, isDarkMode && styles.darkText]}>
                Content Loaded
              </Text>
              <Text style={[styles.dateText, isDarkMode && styles.darkTextSecondary]}>
                Last updated: {formatDate(new Date())}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  tabContent: {
    padding: SPACING.large,
    flex: 1,
  },
  sectionTitle: {
    ...TYPOGRAPHY.h2,
    color: COLORS.text,
    marginBottom: SPACING.small,
  },
  sectionDescription: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    marginBottom: SPACING.large,
  },
  contentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xlarge,
  },
  dateText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textTertiary,
    marginTop: SPACING.large,
  },
  // Skeleton styles
  skeletonContainer: {
    marginTop: SPACING.medium,
  },
  skeletonCard: {
    borderRadius: 12,
  },
  skeletonContent: {
    padding: SPACING.medium,
  },
  // Dark mode text styles
  darkText: {
    color: COLORS.white,
  },
  darkTextSecondary: {
    color: '#A0A0A0',
  },
  buttonContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: SPACING.large,
    paddingHorizontal: SPACING.medium,
    gap: SPACING.medium,
  },
  actionButton: {
    width: '80%',  // Wider buttons for vertical layout
    maxWidth: 300,  // Increased max width
  },
});
