/**
 * @magic_component: ExploreTab
 * @magic_purpose: Explore tab screen showing discovery content
 * @magic_category: Screens/Tabs
 * @magic_keywords: explore,discover,tab,screen
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  RefreshControl,
  Dimensions
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { logger } from '../../utils/logger';

// Import components and utilities
import HomeHeader from '../../components/screens/HomeScreen/HomeHeader';
import Button from '../../components/ui/Button';
import Skeleton from '../../components/ui/Skeleton';
import { useApp } from '../../contexts/AppContext';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';
import { ExploreScreenNavigationProp } from '../../navigation/types';
import { useTheme } from '../../contexts/ThemeContext';

// Get screen dimensions for responsive layouts
const { width: screenWidth } = Dimensions.get('window');

/**
 * ExploreTab component showing discovery content
 */
export default function ExploreTab() {
  // Navigation
  const navigation = useNavigation<ExploreScreenNavigationProp>();
  
  // Use theme from ThemeContext
  const { isDarkMode, colors } = useTheme();
  
  // App context - used for non-theme related state
  const { appState } = useApp();
  
  // State
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // Simulate loading data
  useEffect(() => {
    // Simulate API loading delay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };
  
  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      {/* Header */}
      <HomeHeader title="Explore" />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <View style={styles.tabContent}>
          <Text style={[styles.sectionTitle, isDarkMode && styles.darkText]}>
            Discover
          </Text>
          
          <Text style={[styles.sectionDescription, isDarkMode && styles.darkTextSecondary]}>
            Explore new content and features in this section.
          </Text>
          
          {/* Content */}
          {isLoading ? (
            <View style={styles.skeletonGrid}>
              {[...Array(4)].map((_, index) => (
                <View key={index} style={styles.skeletonGridItem}>
                  <Skeleton width="100%" height={120} />
                  <View style={styles.skeletonContent}>
                    <Skeleton width="80%" height={16} />
                    <Skeleton width="50%" height={12} style={{ marginTop: 4 }} />
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.exploreContent}>
              <Feather 
                name="map" 
                size={64} 
                color={isDarkMode ? colors.textSecondary : colors.textTertiary} 
              />
              <Text style={[styles.emptyStateText, isDarkMode && styles.darkText]}>
                No items to explore yet
              </Text>
              <Button
                title="Refresh"
                onPress={handleRefresh}
                style={{ marginTop: SPACING.medium }}
                icon="refresh-cw"
              />
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  tabContent: {
    padding: SPACING.large,
    flex: 1,
  },
  sectionTitle: {
    ...TYPOGRAPHY.h2,
    color: COLORS.text,
    marginBottom: SPACING.small,
  },
  sectionDescription: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    marginBottom: SPACING.large,
  },
  // Skeleton styles
  skeletonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: SPACING.medium,
  },
  skeletonGridItem: {
    width: (screenWidth - SPACING.large * 2 - SPACING.medium) / 2,
    marginBottom: SPACING.medium,
    borderRadius: 8,
    overflow: 'hidden',
  },
  skeletonContent: {
    padding: SPACING.small,
  },
  // Explore tab styles
  exploreContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xlarge,
  },
  emptyStateText: {
    ...TYPOGRAPHY.subtitle,
    color: COLORS.text,
    marginTop: SPACING.medium,
  },
  // Dark mode text styles
  darkText: {
    color: COLORS.white,
  },
  darkTextSecondary: {
    color: '#A0A0A0',
  },
});
