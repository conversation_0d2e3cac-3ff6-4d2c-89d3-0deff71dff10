/**
 * @magic_component: ProfileTab
 * @magic_purpose: Profile tab screen showing user information and settings
 * @magic_category: Screens/Tabs
 * @magic_keywords: profile,user,account,settings,tab,screen
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  Platform,
  Share
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { logger } from '../../utils/logger';


// Import components and utilities
import HomeHeader from '../../components/screens/HomeScreen/HomeHeader';
import Button from '../../components/ui/Button';
import Skeleton from '../../components/ui/Skeleton';
import { useApp } from '../../contexts/AppContext';
import { useTheme } from '../../contexts/ThemeContext';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';
import { ProfileScreenNavigationProp } from '../../navigation/types';

/**
 * ProfileTab component showing user information and settings
 */
export default function ProfileTab() {
  // Navigation
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  
  // Use theme from ThemeContext
  const { isDarkMode, colors, toggleTheme } = useTheme();
  
  // App context - used for non-theme related state
  const { appState } = useApp();
  

  
  // State
  const [isLoading, setIsLoading] = useState(true);
  
  // Simulate loading data
  useEffect(() => {
    // Simulate API loading delay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);
  

  
  // Handle sharing profile link
  const handleShareProfile = async () => {
    try {
      logger.ui('Share profile button pressed', { screen: 'Profile' });
      
      // Use React Native's Share API
      await Share.share({
        title: 'Check out my profile',
        message: 'Check out my profile!'
      });
      
      logger.info('Profile shared successfully', 'NAVIGATION');
    } catch (error) {
      logger.error('Failed to share profile', 'NAVIGATION', { error });
    }
  };
  
  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      {/* Header */}
      <HomeHeader title="Profile" />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
      >
        {isLoading ? (
          <View style={styles.profileSkeleton}>
            <Skeleton
              style={styles.avatarSkeleton}
              width={100}
              height={100}
              borderRadius={50}
            />
            <Skeleton width={150} height={24} style={{ marginTop: 16 }} />
            <Skeleton width={200} height={16} style={{ marginTop: 8 }} />
            
            <Skeleton
              style={styles.settingsSkeleton}
              width="100%"
              height={50}
              borderRadius={8}
            />
            <Skeleton
              style={styles.settingsSkeleton}
              width="100%"
              height={50}
              borderRadius={8}
            />
            <Skeleton
              style={styles.settingsSkeleton}
              width="100%"
              height={50}
              borderRadius={8}
            />
          </View>
        ) : (
          <View style={styles.profileContent}>
            {/* Avatar */}
            <View style={styles.avatarContainer}>
              <Feather
                name="user"
                size={40}
                color={isDarkMode ? '#FFFFFF' : COLORS.text}
                style={styles.avatarIcon}
              />
            </View>
            
            {/* User Info */}
            <Text style={[styles.profileName, isDarkMode && styles.darkText]}>
              John Doe
            </Text>
            <Text style={[styles.profileEmail, isDarkMode && styles.darkTextSecondary]}>
              <EMAIL>
            </Text>
            
            {/* Actions - Vertical button layout */}
            <View style={styles.buttonGroup}>
              <Button
                title="Edit Profile"
                onPress={() => {
                  logger.ui('Edit profile button pressed', { screen: 'Profile' });
                }}
                style={styles.profileButton}
                variant="primary"
                icon="edit-2"
              />
              
              <Button
                title="Toggle Dark Mode"
                onPress={() => {
                  toggleTheme();
                  logger.ui('Theme toggled', { isDarkMode: !isDarkMode });
                }}
                style={styles.profileButton}
                variant="secondary"
                icon={isDarkMode ? 'sun' : 'moon'}
              />
              
              <Button
                title="Share Profile"
                onPress={handleShareProfile}
                style={styles.profileButton}
                variant="outline"
                icon="share-2"
              />
            </View>
            

            

          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: SPACING.large,
  },
  // Profile tab styles
  profileContent: {
    alignItems: 'center',
    paddingTop: SPACING.large,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarIcon: {
    opacity: 0.7,
  },
  profileName: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
    marginTop: SPACING.medium,
  },
  profileEmail: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    marginTop: SPACING.tiny,
  },
  buttonGroup: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.large,
    width: '100%',
    paddingHorizontal: SPACING.medium,
    gap: SPACING.medium,
  },
  profileButton: {
    width: '80%',
    maxWidth: 300,
    height: 48,
  },

  // Skeleton styles
  profileSkeleton: {
    alignItems: 'center',
    paddingTop: SPACING.large,
  },
  avatarSkeleton: {
    alignSelf: 'center',
  },
  settingsSkeleton: {
    width: '100%',
    marginTop: SPACING.large,
  },
  // Dark mode text styles
  darkText: {
    color: COLORS.white,
  },
  darkTextSecondary: {
    color: '#A0A0A0',
  },

});
