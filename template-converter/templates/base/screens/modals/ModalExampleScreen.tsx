/**
 * @magic_component: ModalExampleScreen
 * @magic_purpose: Example modal screen using React Navigation's modal presentation
 * @magic_category: Screens/Modals
 * @magic_keywords: modal,screen,example
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView,
  TouchableOpacity
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { logger } from '../../utils/logger';

// Import components and utilities
import Button from '../../components/ui/Button';
import { COLORS, SPACING, TYPOGRAPHY } from '../../constants/theme';
import { useTheme } from '../../contexts/ThemeContext';

/**
 * ModalExampleScreen component
 * 
 * This is a proper modal screen using React Navigation's modal presentation
 * instead of a custom modal component.
 */
export default function ModalExampleScreen() {
  // Navigation
  const navigation = useNavigation();
  
  // Use theme from ThemeContext
  const { isDarkMode, colors } = useTheme();
  
  // Handle close
  const handleClose = () => {
    logger.ui('Modal screen closed', { screen: 'ModalExample' });
    navigation.goBack();
  };
  
  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.darkContainer]}>
      <View style={styles.header}>
        <Text style={[styles.title, isDarkMode && styles.darkText]}>
          Modal Example
        </Text>
        
        <TouchableOpacity
          onPress={handleClose}
          style={styles.closeButton}
          hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
        >
          <Feather
            name="x"
            size={24}
            color={isDarkMode ? colors.textSecondary : colors.text}
          />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <Text style={[styles.description, isDarkMode && styles.darkTextSecondary]}>
          This is a modal screen using React Navigation's modal presentation.
          It's a full screen that slides up from the bottom, following platform conventions.
        </Text>
        
        <View style={styles.iconContainer}>
          <Feather
            name="layers"
            size={64}
            color={colors.primary}
          />
        </View>
        
        <Text style={[styles.infoText, isDarkMode && styles.darkTextSecondary]}>
          Modal screens are great for temporary focused tasks that don't fit into the main navigation flow.
        </Text>
        
        <Button
          title="Close Modal"
          onPress={handleClose}
          style={styles.button}
          icon="x"
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    ...TYPOGRAPHY.h2,
    color: COLORS.text,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: SPACING.large,
    alignItems: 'center',
  },
  description: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.large,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.cardBackground,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: SPACING.large,
  },
  infoText: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xlarge,
  },
  button: {
    width: 200,
  },
  darkText: {
    color: COLORS.white,
  },
  darkTextSecondary: {
    color: '#A0A0A0',
  },
});
