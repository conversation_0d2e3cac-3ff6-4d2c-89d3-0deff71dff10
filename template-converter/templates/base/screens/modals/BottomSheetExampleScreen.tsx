/**
 * @magic_component: BottomSheetExampleScreen
 * @magic_purpose: Example bottom sheet screen using React Navigation's modal presentation
 * @magic_category: Screens/Modals
 * @magic_keywords: bottom sheet,modal,screen,example
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity,
  Pressable,
  Animated,
  Dimensions,
  Platform
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { logger } from '../../utils/logger';

// Import components and utilities
import Button from '../../components/ui/Button';
import { COLORS, SPACING, TYPOGRAPHY } from '../../constants/theme';
import { useTheme } from '../../contexts/ThemeContext';

// Get screen dimensions
const { height: screenHeight } = Dimensions.get('window');

/**
 * BottomSheetExampleScreen component
 * 
 * This is a proper bottom sheet screen using React Navigation's modal presentation
 * instead of a custom bottom sheet component.
 */
export default function BottomSheetExampleScreen() {
  // Navigation
  const navigation = useNavigation();
  
  // Use theme from ThemeContext
  const { isDarkMode, colors } = useTheme();
  
  // Get safe area insets
  const insets = useSafeAreaInsets();
  
  // Animation value for the bottom sheet
  const translateY = useRef(new Animated.Value(screenHeight)).current;
  
  // Animate in on mount
  useEffect(() => {
    logger.ui('Bottom sheet screen opened', { screen: 'BottomSheetExample' });
    
    Animated.timing(translateY, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);
  
  // Handle close
  const handleClose = () => {
    logger.ui('Bottom sheet screen closed', { screen: 'BottomSheetExample' });
    
    Animated.timing(translateY, {
      toValue: screenHeight,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      navigation.goBack();
    });
  };
  
  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      {/* Semi-transparent backdrop - press to close */}
      <Pressable 
        style={styles.backdrop} 
        onPress={handleClose}
      />
      
      {/* Bottom Sheet */}
      <Animated.View 
        style={[
          styles.bottomSheet, 
          isDarkMode && styles.darkBottomSheet,
          { transform: [{ translateY }] },
          { paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0 }
        ]}
      >
        {/* Drag Handle */}
        <View style={styles.dragHandleContainer}>
          <View style={[styles.dragHandle, isDarkMode && styles.darkDragHandle]} />
        </View>
        
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, isDarkMode && styles.darkText]}>
            Bottom Sheet Example
          </Text>
          
          <TouchableOpacity
            onPress={handleClose}
            style={styles.closeButton}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <Feather
              name="x"
              size={24}
              color={isDarkMode ? colors.textSecondary : colors.text}
            />
          </TouchableOpacity>
        </View>
        
        {/* Content */}
        <View style={styles.content}>
          <Text style={[styles.description, isDarkMode && styles.darkTextSecondary]}>
            This is a bottom sheet screen using React Navigation's modal presentation.
            It slides up from the bottom and can be dismissed by tapping the backdrop or close button.
          </Text>
          
          <View style={styles.iconContainer}>
            <Feather
              name="arrow-up"
              size={64}
              color={colors.primary}
            />
          </View>
          
          <Text style={[styles.infoText, isDarkMode && styles.darkTextSecondary]}>
            Bottom sheets are perfect for contextual actions, filters, or additional information
            that doesn't need a full screen.
          </Text>
          
          <Button
            title="Close Bottom Sheet"
            onPress={handleClose}
            style={styles.button}
            icon="x"
          />
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'transparent',
  },
  darkContainer: {
    backgroundColor: 'transparent',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheet: {
    backgroundColor: COLORS.cardBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: 300,
    maxHeight: screenHeight * 0.85,
    width: '100%',
  },
  darkBottomSheet: {
    backgroundColor: '#1E1E1E',
  },
  dragHandleContainer: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 10,
  },
  dragHandle: {
    width: 40,
    height: 5,
    borderRadius: 3,
    backgroundColor: COLORS.border,
  },
  darkDragHandle: {
    backgroundColor: '#3E3E3E',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: SPACING.large,
    alignItems: 'center',
  },
  description: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.large,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.cardBackground,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: SPACING.large,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  infoText: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xlarge,
  },
  button: {
    width: 200,
  },
  darkText: {
    color: COLORS.white,
  },
  darkTextSecondary: {
    color: '#A0A0A0',
  },
});
