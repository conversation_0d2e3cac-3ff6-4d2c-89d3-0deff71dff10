/**
 * @magic_module: MockData
 * @magic_purpose: Provides mock data for the application to use during development
 * @magic_category: Data
 * @magic_keywords: mock,data,sample,development
 */

// Product type definition
export interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  imageUrl: string;
  category: string;
  rating: number;
  reviews: number;
  isNew?: boolean;
  isFeatured?: boolean;
}

// User type definition
export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  preferences: {
    darkMode: boolean;
    notifications: boolean;
  };
}

// Mock products data
export const mockProducts: Product[] = [
  {
    id: 'p1',
    title: 'Modern Sofa',
    description: 'A comfortable modern sofa with premium fabric and sturdy construction.',
    price: 599.99,
    imageUrl: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc',
    category: 'furniture',
    rating: 4.5,
    reviews: 128,
    isNew: true,
    isFeatured: true,
  },
  {
    id: 'p2',
    title: 'Wooden Coffee Table',
    description: 'Handcrafted wooden coffee table with a natural finish.',
    price: 249.99,
    imageUrl: 'https://images.unsplash.com/photo-1532372576444-dda954194ad0',
    category: 'furniture',
    rating: 4.3,
    reviews: 86,
    isNew: true,
  },
  {
    id: 'p3',
    title: 'Pendant Light',
    description: 'Modern pendant light with adjustable height and warm lighting.',
    price: 129.99,
    imageUrl: 'https://images.unsplash.com/photo-1507473885765-e6ed057f782c',
    category: 'lighting',
    rating: 4.7,
    reviews: 54,
    isFeatured: true,
  },
  {
    id: 'p4',
    title: 'Ceramic Vase',
    description: 'Handmade ceramic vase with a unique pattern and glossy finish.',
    price: 79.99,
    imageUrl: 'https://images.unsplash.com/photo-1612196808214-b7e239e5f6b7',
    category: 'decor',
    rating: 4.2,
    reviews: 32,
  },
  {
    id: 'p5',
    title: 'Wool Rug',
    description: 'Soft wool rug with a geometric pattern, perfect for living rooms.',
    price: 349.99,
    imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7',
    category: 'textiles',
    rating: 4.8,
    reviews: 95,
    isFeatured: true,
  },
];

// Mock user data
export const mockUser: User = {
  id: 'u1',
  name: 'Alex Johnson',
  email: '<EMAIL>',
  avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde',
  preferences: {
    darkMode: false,
    notifications: true,
  },
};

// Function to get mock products
export const getMockProducts = (): Product[] => {
  return mockProducts;
};

// Function to get a mock product by ID
export const getMockProductById = (id: string): Product | undefined => {
  return mockProducts.find(product => product.id === id);
};

// Function to get featured products
export const getFeaturedProducts = (): Product[] => {
  return mockProducts.filter(product => product.isFeatured);
};

// Function to get new products
export const getNewProducts = (): Product[] => {
  return mockProducts.filter(product => product.isNew);
};

// Function to get mock user
export const getMockUser = (): User => {
  return mockUser;
};
