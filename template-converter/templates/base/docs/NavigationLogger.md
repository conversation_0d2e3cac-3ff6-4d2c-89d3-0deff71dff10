# Navigation Logger

This document explains how to use the navigation logger in the Magically app.

## Overview

The navigation logger is a custom hook that tracks and logs navigation changes throughout the app. It captures:

- Screen transitions
- Navigation parameters
- Time spent on each screen
- Navigation timing metrics

## Implementation

The navigation logger is implemented as a custom hook called `useNavigationLogger` that can be attached to a React Navigation container.

### Key Features

1. **Screen Transition Tracking**: Logs when users navigate between screens
2. **Parameter Tracking**: Captures parameters passed during navigation
3. **Timing Metrics**: Measures how long users spend on each screen
4. **Initialization Logging**: Logs when the navigation container is first initialized

## Usage

The navigation logger is already integrated into the main navigation container in `src/navigation/index.tsx`. It's configured to track all navigation events in the app.

### Example Integration

```tsx
// In your navigation container component:
import React, { useRef } from 'react';
import { NavigationContainer, NavigationContainerRef } from '@react-navigation/native';
import useNavigationLogger from '../hooks/useNavigationLogger';

const Navigation = () => {
  // Create a navigation reference
  const navigationRef = useRef<NavigationContainerRef>(null);
  
  // Initialize the navigation logger
  useNavigationLogger(navigationRef, {
    containerId: 'main-navigator',
    metadata: {
      appVersion: '1.0.0'
    }
  });
  
  return (
    <NavigationContainer ref={navigationRef}>
      {/* Your navigation structure here */}
    </NavigationContainer>
  );
};
```

## Log Output

The navigation logger produces structured logs with the following information:

```json
{
  "level": "INFO",
  "category": "NAVIGATION",
  "message": "Screen changed",
  "data": {
    "from": "Home",
    "to": "FeatureRequest",
    "params": { "featureName": "Chat Feature" },
    "timeSpentOnPreviousScreen": 12500,
    "timestamp": 1619417957000,
    "containerId": "main-navigator",
    "appVersion": "1.0.0"
  },
  "timestamp": "2025-04-26T04:35:57.000Z"
}
```

## Extending the Logger

To add additional tracking capabilities:

1. Update the `useNavigationLogger.ts` hook to capture more data
2. Add new log categories in the `logger.ts` utility if needed
3. Implement custom event tracking in specific screens as required

## Troubleshooting

If navigation events aren't being logged:

1. Ensure the navigation container has a valid ref
2. Check that the logger is properly initialized
3. Verify that the logger is enabled (`enabled: true` in options)
