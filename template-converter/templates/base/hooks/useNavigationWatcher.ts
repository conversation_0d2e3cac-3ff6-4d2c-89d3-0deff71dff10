/**
 * @magic_hook: useNavigationWatcher
 * @magic_purpose: Simplified centralized navigation tracking
 */
import { useRef, useEffect } from 'react';
import { NavigationContainerRef, NavigationState } from '@react-navigation/native';
import { logger } from '../utils/logger';

/**
 * A simplified hook for tracking navigation state changes
 * 
 * @param navigationRef Reference to the navigation container
 * @param options Optional configuration
 */
export function useNavigationWatcher(
  navigationRef: React.RefObject<NavigationContainerRef<any>>,
  options: { disabled?: boolean } = {}
): void {
  const { disabled = false } = options;
  const prevRouteRef = useRef<string | null>(null);
  
  useEffect(() => {
    if (disabled || !navigationRef.current) {
      return;
    }
    
    // Log initial navigation state
    const handleReady = () => {
      if (!navigationRef.current) return;
      
      const state = navigationRef.current.getRootState();
      const currentRouteName = getCurrentRouteName(state);
      
      if (currentRouteName) {
        logger.info(`Navigation initialized at: ${currentRouteName}`, 'NAVIGATION');
        prevRouteRef.current = currentRouteName;
      }
    };
    
    // Log navigation state changes
    const handleStateChange = () => {
      if (!navigationRef.current) return;
      
      const state = navigationRef.current.getRootState();
      const currentRouteName = getCurrentRouteName(state);
      
      if (currentRouteName && prevRouteRef.current !== currentRouteName) {
        logger.info(`Screen changed: ${prevRouteRef.current || 'Unknown'} → ${currentRouteName}`, 'NAVIGATION');
        prevRouteRef.current = currentRouteName;
      }
    };
    
    // Helper to extract current route name from state
    function getCurrentRouteName(state: NavigationState | undefined): string | null {
      if (!state) return null;
      
      const route = state.routes[state.index || 0];
      if (route.state) {
        return getCurrentRouteName(route.state as NavigationState);
      }
      return route.name;
    }
    
    // Register listeners
    const unsubscribeReady = navigationRef.current.addListener('ready', handleReady);
    const unsubscribeStateChange = navigationRef.current.addListener('state', handleStateChange);
    
    // Clean up listeners
    return () => {
      unsubscribeReady();
      unsubscribeStateChange();
    };
  }, [disabled, navigationRef]);
}

export default useNavigationWatcher;
