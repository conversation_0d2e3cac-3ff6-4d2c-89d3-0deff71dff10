/**
 * @magic_hook: useFetch
 * @magic_purpose: Custom hook for data fetching with loading, error states and caching
 * @magic_category: Hooks/Data
 * @magic_keywords: fetch,api,data,loading,error,cache
 */
import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UseFetchOptions {
  cacheKey?: string;
  cacheDuration?: number; // in milliseconds
  initialData?: any;
  dependencies?: any[];
}

interface UseFetchResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for data fetching with loading and error states
 * @param url The URL to fetch data from
 * @param options Configuration options for the fetch operation
 * @returns Object containing data, loading state, error state, and refetch function
 */
function useFetch<T>(
  url: string,
  options: UseFetchOptions = {}
): UseFetchResult<T> {
  const {
    cacheKey,
    cacheDuration = 5 * 60 * 1000, // 5 minutes default
    initialData = null,
    dependencies = [],
  } = options;

  const [data, setData] = useState<T | null>(initialData);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Function to fetch data
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Check cache first if cacheKey is provided
      if (cacheKey) {
        const cachedData = await AsyncStorage.getItem(cacheKey);
        if (cachedData) {
          const { data: cacheData, timestamp } = JSON.parse(cachedData);
          const isExpired = Date.now() - timestamp > cacheDuration;
          
          if (!isExpired) {
            setData(cacheData);
            setLoading(false);
            return;
          }
        }
      }

      // Fetch fresh data
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const result = await response.json();
      setData(result);
      
      // Cache the data if cacheKey is provided
      if (cacheKey) {
        await AsyncStorage.setItem(
          cacheKey,
          JSON.stringify({
            data: result,
            timestamp: Date.now(),
          })
        );
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [url, cacheKey, cacheDuration, ...dependencies]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Function to manually refetch data
  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
}

export default useFetch;
