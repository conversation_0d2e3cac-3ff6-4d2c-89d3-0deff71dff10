/**
 * @magic_hook: useNavigationLogger
 * @magic_purpose: Tracks and logs navigation changes throughout the app
 * @magic_category: Hooks/Navigation
 * @magic_keywords: navigation,logger,tracking,analytics,events
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import { useRef, useEffect } from 'react';
import { NavigationContainerRef, NavigationState, PartialState } from '@react-navigation/native';
import { logger } from '../utils/logger';

/**
 * Route information interface
 */
interface RouteInfo {
  routeName: string;
  params?: Record<string, any>;
  key: string;
  childRoute?: RouteInfo | null;
}

/**
 * Options for the navigation logger
 */
interface NavigationLoggerOptions {
  // Enable or disable navigation logging
  enabled?: boolean;
  
  // Custom identifier for this navigation container
  containerId?: string;
  
  // Additional metadata to include with all navigation logs
  metadata?: Record<string, any>;
}

/**
 * Hook that tracks and logs navigation changes
 * 
 * This hook registers listeners for navigation state changes and logs them
 * using the structured logger. It captures screen transitions, params, and timing.
 * 
 * @param navigationRef Reference to the navigation container
 * @param options Configuration options for the logger
 */
export function useNavigationLogger(
  navigationRef: React.RefObject<NavigationContainerRef<any>>,
  options: NavigationLoggerOptions = {}
): void {
  // Default options
  const {
    enabled = true,
    containerId = 'main',
    metadata = {}
  } = options;
  
  // Store the previous state for comparison
  const previousStateRef = useRef<NavigationState | PartialState<NavigationState> | undefined>(undefined);
  
  // Track navigation timing
  const navigationTimingRef = useRef<Record<string, number>>({});
  
  useEffect(() => {
    if (!enabled || !navigationRef.current) {
      return;
    }
    
    // Function to extract route information
    const getRouteInfo = (state: NavigationState | PartialState<NavigationState> | undefined): RouteInfo | null => {
      if (!state) return null;
      
      const routes = state.routes;
      const index = state.index ?? 0;
      
      if (!routes || index >= routes.length) return null;
      
      const currentRoute = routes[index];
      const routeKey = currentRoute.key || `route-${currentRoute.name}-${Date.now()}`;
      
      const result: RouteInfo = {
        routeName: currentRoute.name,
        params: currentRoute.params,
        key: routeKey
      };
      
      // If the route has a nested navigator, recursively get its active route
      if (currentRoute.state) {
        result.childRoute = getRouteInfo(currentRoute.state);
      }
      
      return result;
    };
    
    // Log navigation state changes
    const handleStateChange = () => {
      if (!navigationRef.current) return;
      
      const currentState = navigationRef.current.getRootState();
      const previousState = previousStateRef.current;
      
      const currentRouteInfo = getRouteInfo(currentState);
      const previousRouteInfo = getRouteInfo(previousState);
      
      if (!currentRouteInfo) return;
      
      const timestamp = Date.now();
      const routeName = currentRouteInfo.routeName;
      
      // Calculate time spent on previous screen
      let timeSpent = 0;
      if (previousRouteInfo && navigationTimingRef.current[previousRouteInfo.key]) {
        timeSpent = timestamp - navigationTimingRef.current[previousRouteInfo.key];
      }
      
      // Store timestamp for current route
      navigationTimingRef.current[currentRouteInfo.key] = timestamp;
      
      // Log the navigation event
      logger.navigation('Screen changed', 'NAVIGATION', {
        from: previousRouteInfo?.routeName || 'Unknown',
        to: routeName,
        params: currentRouteInfo.params || {},
        timeSpentOnPreviousScreen: timeSpent,
        timestamp,
        containerId,
        ...metadata
      });
      
      // Update previous state
      previousStateRef.current = currentState;
    };
    
    // Log initial state
    const handleReady = () => {
      if (!navigationRef.current) return;
      
      const currentState = navigationRef.current.getRootState();
      const currentRouteInfo = getRouteInfo(currentState);
      
      if (currentRouteInfo) {
        navigationTimingRef.current[currentRouteInfo.key] = Date.now();
        
        logger.navigation('Navigation initialized', 'NAVIGATION', {
          initialRoute: currentRouteInfo.routeName,
          params: currentRouteInfo.params || {},
          timestamp: Date.now(),
          containerId,
          ...metadata
        });
        
        previousStateRef.current = currentState;
      }
    };
    
    // Register listeners
    const unsubscribeReady = navigationRef.current.addListener('ready', handleReady);
    const unsubscribeStateChange = navigationRef.current.addListener('state', handleStateChange);
    
    // Clean up listeners
    return () => {
      unsubscribeReady();
      unsubscribeStateChange();
    };
  }, [enabled, navigationRef, containerId, metadata]);
}

export default useNavigationLogger;
