/**
 * @magic_component: Button
 * @magic_platform: all
 * @magic_purpose: A customizable button component with different variants and states
 * @magic_category: UI/Inputs
 * @magic_keywords: button,press,action,input,touch
 * @magic_props: onPress,title,variant,disabled,loading,icon
 */
import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  View,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../constants/theme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof Feather.glyphMap;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  /**
   * Whether to show ellipsis for long text (defaults to true)
   */
  ellipsis?: boolean;
}

/**
 * Button component that supports different variants, loading states, and icons
 * 
 * @example
 * <Button 
 *   title="Press Me" 
 *   onPress={() => {}} 
 *   variant="primary"
 *   icon="arrow-right"
 * />
 */
const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
  loading = false,
  icon,
  style,
  textStyle,
  ellipsis = true,
}) => {
  // Determine button style based on variant
  const getButtonStyle = () => {
    switch (variant) {
      case 'primary':
        return styles.primaryButton;
      case 'secondary':
        return styles.secondaryButton;
      case 'outline':
        return styles.outlineButton;
      case 'ghost':
        return styles.ghostButton;
      default:
        return styles.primaryButton;
    }
  };

  // Determine text style based on variant
  const getTextStyle = () => {
    switch (variant) {
      case 'primary':
        return styles.primaryText;
      case 'secondary':
        return styles.secondaryText;
      case 'outline':
        return styles.outlineText;
      case 'ghost':
        return styles.ghostText;
      default:
        return styles.primaryText;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        disabled && styles.disabledButton,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' ? COLORS.white : COLORS.primary}
        />
      ) : (
        <View style={styles.content}>
          {icon && (
            <Feather
              name={icon}
              size={18}
              color={
                variant === 'primary'
                  ? COLORS.white
                  : variant === 'secondary'
                  ? COLORS.white
                  : COLORS.primary
              }
              style={styles.icon}
            />
          )}
          <Text 
            style={[getTextStyle(), disabled && styles.disabledText, textStyle]}
            numberOfLines={ellipsis ? 1 : undefined}
            ellipsizeMode={ellipsis ? "tail" : undefined}
          >
            {title}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: SPACING.medium,
    paddingHorizontal: SPACING.large,
    borderRadius: BORDER_RADIUS.medium,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    backgroundColor: COLORS.secondary,
  },
  outlineButton: {
    backgroundColor: COLORS.transparent,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  ghostButton: {
    backgroundColor: COLORS.transparent,
  },
  disabledButton: {
    backgroundColor: COLORS.textTertiary,
    borderColor: COLORS.textTertiary,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryText: {
    ...TYPOGRAPHY.button,
    color: COLORS.white,
  },
  secondaryText: {
    ...TYPOGRAPHY.button,
    color: COLORS.white,
  },
  outlineText: {
    ...TYPOGRAPHY.button,
    color: COLORS.primary,
  },
  ghostText: {
    ...TYPOGRAPHY.button,
    color: COLORS.primary,
  },
  disabledText: {
    color: COLORS.white,
  },
  icon: {
    marginRight: SPACING.small,
  },
});

export default Button;
