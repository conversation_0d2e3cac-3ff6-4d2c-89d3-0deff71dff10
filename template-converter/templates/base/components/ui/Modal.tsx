/**
 * @magic_component: Modal
 * @magic_platform: all
 * @magic_purpose: A customizable modal component for displaying content on top of the screen
 * @magic_category: UI/Feedback
 * @magic_keywords: modal,dialog,popup,overlay
 * @magic_props: visible,onClose,title,children
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal as RNModal,
  TouchableOpacity,
  Pressable,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../constants/theme';
import { useTheme } from '../../contexts/ThemeContext';
import { logger } from '../../utils/logger';

// Get screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * Modal props interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface ModalProps {
  // Whether the modal is visible
  visible: boolean;
  
  // Function to call when the modal is closed
  onClose: () => void;
  
  // Title of the modal
  title?: string;
  
  // Content of the modal
  children: React.ReactNode;
  
  // Whether to close the modal when clicking outside
  closeOnOverlayPress?: boolean;
  
  // Whether to show the close button
  showCloseButton?: boolean;
  
  // Custom styles for the modal content
  contentStyle?: any;
  
  // Custom styles for the modal container
  containerStyle?: any;
  
  // Whether the modal content is scrollable
  scrollable?: boolean;
  
  // Footer content for the modal
  footer?: React.ReactNode;
}

/**
 * Modal component for displaying content on top of the screen
 * 
 * @example
 * <Modal
 *   visible={isModalVisible}
 *   onClose={() => setIsModalVisible(false)}
 *   title="Modal Title"
 * >
 *   <Text>Modal content goes here</Text>
 * </Modal>
 */
const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  closeOnOverlayPress = true,
  showCloseButton = true,
  contentStyle,
  containerStyle,
  scrollable = true,
  footer,
}) => {
  // Get theme from context
  const { isDarkMode, colors } = useTheme();
  
  // Handle closing the modal
  const handleClose = () => {
    logger.ui('Modal closed', { title });
    onClose();
  };
  
  // Handle overlay press
  const handleOverlayPress = () => {
    if (closeOnOverlayPress) {
      handleClose();
    }
  };
  
  // Prevent propagation of touches on the modal content
  const handleContentPress = (e: any) => {
    e.stopPropagation();
  };
  
  // Render the modal content
  const renderContent = () => (
    <View style={[
      styles.content,
      isDarkMode && styles.darkContent,
      contentStyle,
    ]}>
      {/* Header */}
      {(title || showCloseButton) && (
        <View style={[styles.header, isDarkMode && styles.darkHeader]}>
          {title && (
            <Text style={[
              styles.title,
              isDarkMode && styles.darkText,
            ]}>
              {title}
            </Text>
          )}
          
          {showCloseButton && (
            <TouchableOpacity
              onPress={handleClose}
              style={styles.closeButton}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <Feather
                name="x"
                size={24}
                color={isDarkMode ? colors.textSecondary : colors.text}
              />
            </TouchableOpacity>
          )}
        </View>
      )}
      
      {/* Body */}
      {scrollable ? (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.scrollViewContent, isDarkMode && styles.darkBody]}
        >
          {children}
        </ScrollView>
      ) : (
        <View style={[styles.body, isDarkMode && styles.darkBody]}>
          {children}
        </View>
      )}
      
      {/* Footer */}
      {footer && (
        <View style={[
          styles.footer,
          isDarkMode && styles.darkFooter,
        ]}>
          {footer}
        </View>
      )}
    </View>
  );
  
  return (
    <RNModal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <Pressable onPress={handleOverlayPress} style={({ pressed }) => [
        styles.container,
        isDarkMode && styles.darkContainer,
        containerStyle,
      ]}>
        <Pressable onPress={handleContentPress}>
          {renderContent()}
        </Pressable>
      </Pressable>
    </RNModal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: SPACING.large,
  },
  darkContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  content: {
    width: '90%',
    maxWidth: Math.min(500, screenWidth * 0.9),
    maxHeight: screenHeight * 0.8,
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    overflow: 'hidden',
    ...MIXINS.shadow,
  },
  darkContent: {
    backgroundColor: '#1E1E1E',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  darkHeader: {
    borderBottomColor: '#2A2A2A',
  },
  title: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
    flex: 1,
  },
  darkText: {
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 4,
  },
  scrollView: {
    maxHeight: 400,
  },
  scrollViewContent: {
    padding: SPACING.medium,
  },
  body: {
    padding: SPACING.medium,
  },
  darkBody: {
    backgroundColor: '#1E1E1E',
  },
  footer: {
    padding: SPACING.medium,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  darkFooter: {
    borderTopColor: '#2A2A2A',
  },
});

export default Modal;
