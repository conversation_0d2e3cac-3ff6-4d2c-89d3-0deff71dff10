/**
 * @magic_component: BottomSheet
 * @magic_platform: all
 * @magic_purpose: A customizable bottom sheet component that slides up from the bottom of the screen
 * @magic_category: UI/Feedback
 * @magic_keywords: bottomsheet,sheet,drawer,modal,slide
 * @magic_props: visible,onClose,title,children
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  Pressable,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../constants/theme';
import { useTheme } from '../../contexts/ThemeContext';
import { logger } from '../../utils/logger';

// Get screen dimensions
const { height: screenHeight } = Dimensions.get('window');

/**
 * BottomSheet props interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface BottomSheetProps {
  // Whether the bottom sheet is visible
  visible: boolean;
  
  // Function to call when the bottom sheet is closed
  onClose: () => void;
  
  // Title of the bottom sheet
  title?: string;
  
  // Content of the bottom sheet
  children: React.ReactNode;
  
  // Whether to close the bottom sheet when clicking outside
  closeOnOverlayPress?: boolean;
  
  // Whether to show the close button
  showCloseButton?: boolean;
  
  // Height of the bottom sheet (percentage of screen height)
  height?: number;
  
  // Custom styles for the bottom sheet content
  contentStyle?: any;
  
  // Whether the bottom sheet content is scrollable
  scrollable?: boolean;
  
  // Footer content for the bottom sheet
  footer?: React.ReactNode;
  
  // Whether to show a drag handle at the top of the bottom sheet
  showDragHandle?: boolean;
}

/**
 * BottomSheet component that slides up from the bottom of the screen
 * 
 * @example
 * <BottomSheet
 *   visible={isBottomSheetVisible}
 *   onClose={() => setIsBottomSheetVisible(false)}
 *   title="Bottom Sheet Title"
 * >
 *   <Text>Bottom sheet content goes here</Text>
 * </BottomSheet>
 */
const BottomSheet: React.FC<BottomSheetProps> = ({
  visible,
  onClose,
  title,
  children,
  closeOnOverlayPress = true,
  showCloseButton = true,
  height = 50,
  contentStyle,
  scrollable = true,
  footer,
  showDragHandle = true,
}) => {
  // Get theme from context
  const { isDarkMode, colors } = useTheme();
  
  // Animation value for the bottom sheet
  const translateY = useRef(new Animated.Value(screenHeight)).current;
  
  // State to track if the sheet is fully visible
  const [isSheetVisible, setIsSheetVisible] = useState(false);
  
  // Get safe area insets for handling notches and home indicators
  const insets = useSafeAreaInsets();
  
  // Handle animation when visibility changes
  useEffect(() => {
    if (visible) {
      logger.ui('Bottom sheet opened', { title });
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setIsSheetVisible(true);
      });
    } else {
      Animated.timing(translateY, {
        toValue: screenHeight,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setIsSheetVisible(false);
      });
    }
  }, [visible, translateY]);
  
  // Handle closing the bottom sheet
  const handleClose = () => {
    logger.ui('Bottom sheet closed', { title });
    onClose();
  };
  
  // Handle overlay press
  const handleOverlayPress = () => {
    if (closeOnOverlayPress) {
      handleClose();
    }
  };
  
  // Prevent propagation of touches on the bottom sheet content
  const handleContentPress = (e: any) => {
    e.stopPropagation();
  };
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <Pressable 
        onPress={handleOverlayPress}
        style={[
          styles.container,
          isDarkMode && styles.darkContainer,
        ]}
      >
        <Pressable onPress={handleContentPress}>
          <Animated.View
            style={[
              styles.sheetContainer,
              {
                transform: [{ translateY }],
                paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0, // Dynamic padding based on safe area
              },
              isDarkMode && styles.darkSheetContainer,
            ]}
          >
            {/* Drag Handle */}
            {showDragHandle && (
              <View style={styles.dragHandleContainer}>
                <View style={[
                  styles.dragHandle,
                  isDarkMode && styles.darkDragHandle,
                ]} />
              </View>
            )}
            
            {/* Header */}
            {(title || showCloseButton) && (
              <View style={styles.header}>
                {title && (
                  <Text style={[
                    styles.title,
                    isDarkMode && styles.darkText,
                  ]}>
                    {title}
                  </Text>
                )}
                
                {showCloseButton && (
                  <TouchableOpacity
                    onPress={handleClose}
                    style={styles.closeButton}
                    hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                  >
                    <Feather
                      name="x"
                      size={24}
                      color={isDarkMode ? colors.textSecondary : colors.text}
                    />
                  </TouchableOpacity>
                )}
              </View>
            )}
            
            {/* Body */}
            {scrollable ? (
              <ScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
              >
                {children}
              </ScrollView>
            ) : (
              <View style={styles.body}>
                {children}
              </View>
            )}
            
            {/* Footer */}
            {footer && (
              <View style={[
                styles.footer,
                isDarkMode && styles.darkFooter,
              ]}>
                {footer}
              </View>
            )}
          </Animated.View>
        </Pressable>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  darkContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  sheetContainer: {
    backgroundColor: COLORS.cardBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: 200,
    maxHeight: screenHeight * 0.85, // Slightly reduced to account for safe areas
    width: '100%',
    paddingBottom: Platform.OS === 'ios' ? 20 : 0, // Add padding for iOS home indicator
  },
  darkSheetContainer: {
    backgroundColor: '#1E1E1E',
  },
  darkContent: {
    backgroundColor: '#1E1E1E',
  },
  dragHandleContainer: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 10,
  },
  dragHandle: {
    width: 40,
    height: 5,
    borderRadius: 3,
    backgroundColor: COLORS.border,
  },
  darkDragHandle: {
    backgroundColor: '#3E3E3E',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
    flex: 1,
  },
  darkText: {
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: SPACING.medium,
  },
  body: {
    flex: 1,
    padding: SPACING.medium,
  },
  footer: {
    padding: SPACING.medium,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  darkFooter: {
    borderTopColor: '#2A2A2A',
  },
});

export default BottomSheet;
