/**
 * @magic_component: Skeleton
 * @magic_platform: all
 * @magic_purpose: A customizable skeleton loader for showing loading states
 * @magic_category: UI/Feedback
 * @magic_keywords: skeleton,loader,loading,placeholder,shimmer
 * @magic_props: width,height,borderRadius,style
 */
import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, ViewStyle, StyleProp } from 'react-native';
import { COLORS } from '../../constants/theme';

interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: StyleProp<ViewStyle>;
  /**
   * Whether to show the shimmer effect
   * @default true
   */
  shimmer?: boolean;
}

/**
 * Skeleton component for displaying loading states
 * 
 * Use this component to show loading placeholders before content is loaded
 * 
 * @example
 * // Basic usage
 * <Skeleton width={200} height={20} />
 * 
 * // Card skeleton
 * <View style={styles.card}>
 *   <Skeleton width="100%" height={200} borderRadius={8} />
 *   <View style={styles.cardContent}>
 *     <Skeleton width="70%" height={24} />
 *     <Skeleton width="100%" height={16} style={{ marginTop: 8 }} />
 *     <Skeleton width="100%" height={16} style={{ marginTop: 4 }} />
 *   </View>
 * </View>
 */
const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
  shimmer = true,
}) => {
  // Animation value for shimmer effect
  const shimmerAnimation = useRef(new Animated.Value(0)).current;

  // Start shimmer animation when component mounts
  useEffect(() => {
    if (shimmer) {
      const shimmerLoop = Animated.loop(
        Animated.timing(shimmerAnimation, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: false,
        })
      );
      
      shimmerLoop.start();
      
      return () => {
        shimmerLoop.stop();
      };
    }
  }, [shimmer, shimmerAnimation]);

  // Interpolate animation value for shimmer gradient
  const shimmerTranslate = shimmerAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [typeof width === 'number' ? -width : -100, typeof width === 'number' ? width : 100],
  });

  return (
    <View
      style={[
        styles.container,
        {
          width: width as any, // Cast to any to avoid type issues
          height: height as any, // Cast to any to avoid type issues
          borderRadius,
        },
        style,
      ]}
    >
      {shimmer && (
        <Animated.View
          style={[
            styles.shimmer,
            {
              transform: [{ translateX: shimmerTranslate }],
            },
          ]}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.border,
    overflow: 'hidden',
    position: 'relative',
  },
  shimmer: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    transform: [{ translateX: -100 }],
  },
});

export default Skeleton;
