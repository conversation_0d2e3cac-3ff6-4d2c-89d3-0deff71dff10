/**
 * @magic_component: PlaceholderScreen
 * @magic_platform: all
 * @magic_purpose: A placeholder screen that informs users they can ask the AI to implement features
 * @magic_category: Screens/Placeholder
 * @magic_keywords: placeholder,empty,feature,prompt
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Clipboard,
  ToastAndroid,
  Platform,
  Alert,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../constants/theme';
import { useTheme } from '../../contexts/ThemeContext';
import { logger } from '../../utils/logger';

/**
 * Feature prompt interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface FeaturePrompt {
  title: string;
  description: string;
  prompt: string;
  icon: string; // Feather icon name
}

/**
 * PlaceholderScreen props interface
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
interface PlaceholderScreenProps {
  // Title of the placeholder screen
  title?: string;
  
  // Description of the placeholder screen
  description?: string;
  
  // Feature name to display in the placeholder
  featureName?: string;
  
  // Custom feature prompts to display
  customPrompts?: FeaturePrompt[];
}

/**
 * Placeholder screen that informs users they can ask the AI to implement features
 * 
 * This screen displays a message to the user that they can ask the AI to implement
 * a specific feature, along with example prompts they can use.
 * 
 * @example
 * <PlaceholderScreen
 *   title="User Profile"
 *   description="This screen will display user profile information"
 *   featureName="User Profile"
 * />
 */
const PlaceholderScreen: React.FC<PlaceholderScreenProps> = ({
  title = 'Feature Not Implemented',
  description = 'This feature has not been implemented yet. You can ask the AI to implement it for you.',
  featureName = 'this feature',
  customPrompts,
}) => {
  // Get theme from context
  const { isDarkMode, colors } = useTheme();
  
  // State for showing copy confirmation
  const [copiedPromptIndex, setCopiedPromptIndex] = useState<number | null>(null);
  
  // Default feature prompts
  const defaultPrompts: FeaturePrompt[] = [
    {
      title: 'Basic Implementation',
      description: 'Ask for a basic implementation of the feature',
      prompt: `Please implement ${featureName} with basic functionality.`,
      icon: 'code',
    },
    {
      title: 'Detailed Implementation',
      description: 'Ask for a detailed implementation with specific requirements',
      prompt: `Please implement ${featureName} with the following requirements:\n- User-friendly interface\n- Data persistence\n- Error handling\n- Loading states`,
      icon: 'layers',
    },
    {
      title: 'Advanced Implementation',
      description: 'Ask for an advanced implementation with specific design and functionality',
      prompt: `Please implement ${featureName} with a modern design and the following features:\n- Clean, responsive UI\n- Data validation\n- Offline support\n- Animations and transitions\n- Comprehensive error handling`,
      icon: 'star',
    },
  ];
  
  // Use custom prompts if provided, otherwise use default prompts
  const prompts = customPrompts || defaultPrompts;
  
  // Copy prompt to clipboard
  const copyToClipboard = (prompt: string, index: number) => {
    Clipboard.setString(prompt);
    setCopiedPromptIndex(index);
    
    // Show toast or alert based on platform
    if (Platform.OS === 'android') {
      ToastAndroid.show('Prompt copied to clipboard', ToastAndroid.SHORT);
    } else {
      Alert.alert('Copied', 'Prompt copied to clipboard');
    }
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopiedPromptIndex(null);
    }, 2000);
    
    logger.ui('Prompt copied to clipboard', { promptIndex: index });
  };
  
  return (
    <ScrollView
      style={[
        styles.container,
        isDarkMode && styles.darkContainer,
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Header */}
      <View style={styles.header}>
        <Feather
          name="code"
          size={48}
          color={colors.primary}
          style={styles.icon}
        />
        <Text style={[
          styles.title,
          isDarkMode && styles.darkText,
        ]}>
          {title}
        </Text>
        <Text style={[
          styles.description,
          isDarkMode && styles.darkTextSecondary,
        ]}>
          {description}
        </Text>
      </View>
      
      {/* Prompts */}
      <View style={styles.promptsContainer}>
        <Text style={[
          styles.promptsTitle,
          isDarkMode && styles.darkText,
        ]}>
          Example Prompts
        </Text>
        
        {prompts.map((prompt, index) => (
          <View
            key={index}
            style={[
              styles.promptCard,
              isDarkMode && styles.darkPromptCard,
            ]}
          >
            <View style={styles.promptHeader}>
              <Feather
                name={prompt.icon as any}
                size={24}
                color={colors.primary}
                style={styles.promptIcon}
              />
              <Text style={[
                styles.promptTitle,
                isDarkMode && styles.darkText,
              ]}>
                {prompt.title}
              </Text>
            </View>
            
            <Text style={[
              styles.promptDescription,
              isDarkMode && styles.darkTextSecondary,
            ]}>
              {prompt.description}
            </Text>
            
            <View style={[styles.promptTextContainer, isDarkMode && styles.darkPromptTextContainer]}>
              <Text style={[
                styles.promptText,
                isDarkMode && styles.darkPromptText,
              ]}>
                {prompt.prompt}
              </Text>
            </View>
            
            <TouchableOpacity
              style={[
                styles.copyButton,
                copiedPromptIndex === index && styles.copiedButton,
              ]}
              onPress={() => copyToClipboard(prompt.prompt, index)}
            >
              <Feather
                name={copiedPromptIndex === index ? 'check' : 'copy'}
                size={16}
                color={copiedPromptIndex === index ? COLORS.success : colors.primary}
                style={styles.copyIcon}
              />
              <Text style={[
                styles.copyText,
                copiedPromptIndex === index && styles.copiedText,
              ]}>
                {copiedPromptIndex === index ? 'Copied!' : 'Copy to Clipboard'}
              </Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>
      
      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[
          styles.footerText,
          isDarkMode && styles.darkTextSecondary,
        ]}>
          Copy a prompt and ask the AI to implement {featureName} for you.
        </Text>
      </View>
    </ScrollView>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  contentContainer: {
    padding: SPACING.large,
    paddingBottom: SPACING.xlarge,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xlarge,
  },
  icon: {
    marginBottom: SPACING.medium,
  },
  title: {
    ...TYPOGRAPHY.h2,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.small,
  },
  description: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  promptsContainer: {
    marginBottom: SPACING.xlarge,
  },
  promptsTitle: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
    marginBottom: SPACING.medium,
  },
  promptCard: {
    backgroundColor: COLORS.cardBackground,
    borderRadius: 12,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
    ...MIXINS.shadow,
  },
  darkPromptCard: {
    backgroundColor: '#1E1E1E',
  },
  promptHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.small,
  },
  promptIcon: {
    marginRight: SPACING.small,
  },
  promptTitle: {
    ...TYPOGRAPHY.subtitle,
    color: COLORS.text,
  },
  promptDescription: {
    ...TYPOGRAPHY.body,
    color: COLORS.textSecondary,
    marginBottom: SPACING.medium,
  },
  promptTextContainer: {
    backgroundColor: '#F5F5F5', // Light mode default
    borderRadius: 8,
    padding: SPACING.medium,
    marginBottom: SPACING.medium,
  },
  darkPromptTextContainer: {
    backgroundColor: '#2A2A2A', // Dark mode background
  },
  promptText: {
    ...TYPOGRAPHY.body,
    color: COLORS.text,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  darkPromptText: {
    color: '#E0E0E0',
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 8,
    padding: SPACING.small,
  },
  copiedButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderColor: COLORS.success,
  },
  copyIcon: {
    marginRight: SPACING.small,
  },
  copyText: {
    ...TYPOGRAPHY.button,
    color: COLORS.primary,
  },
  copiedText: {
    color: COLORS.success,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textTertiary,
    textAlign: 'center',
  },
  darkText: {
    color: '#FFFFFF',
  },
  darkTextSecondary: {
    color: '#A0A0A0',
  },
});

export default PlaceholderScreen;
