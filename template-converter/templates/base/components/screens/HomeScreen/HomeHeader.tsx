/**
 * @magic_component: HomeHeader
 * @magic_platform: all
 * @magic_purpose: Header component for the home screen with status bar handling and theme toggle
 * @magic_category: Components/Headers
 * @magic_keywords: header,status bar,theme,toggle,navigation
 * @magic_connects: ThemeContext
 */
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  StatusBar as RNStatusBar,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../../../contexts/ThemeContext';
import { COLORS, SPACING, TYPOGRAPHY, MIXINS } from '../../../constants/theme';
import { logger } from '../../../utils/logger';

interface HomeHeaderProps {
  title?: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
}

/**
 * Header component for the home screen that handles status bar and safe area
 * 
 * This component automatically adjusts for the status bar and includes a theme toggle.
 * It can optionally display a back button and custom right component.
 * 
 * @example
 * <HomeHeader 
 *   title="Home" 
 *   showBackButton={false}
 * />
 */
const HomeHeader: React.FC<HomeHeaderProps> = ({
  title = 'Home',
  showBackButton = false,
  onBackPress,
  rightComponent,
}) => {
  // Get safe area insets to handle notches and status bars
  const insets = useSafeAreaInsets();
  
  // Get theme context for dark mode toggle
  const { isDarkMode, toggleTheme, colors } = useTheme();
  
  // Calculate statusBarHeight based on platform and insets
  const statusBarHeight = Platform.OS === 'ios' ? insets.top : RNStatusBar.currentHeight || 0;
  
  return (
    <View style={[
      styles.container, 
      { paddingTop: statusBarHeight },
      isDarkMode && styles.darkContainer
    ]}>
      {/* Status bar with proper style based on theme */}
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.content}>
        {/* Left section with optional back button */}
        <View style={styles.leftSection}>
          {showBackButton && (
            <TouchableOpacity 
              onPress={onBackPress} 
              style={styles.backButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Feather 
                name="chevron-left" 
                size={24} 
                color={isDarkMode ? colors.text : COLORS.text} 
              />
            </TouchableOpacity>
          )}
          <Text 
            style={[
              styles.title, 
              isDarkMode && styles.darkText,
              showBackButton && styles.titleWithBack
            ]}
          >
            {title}
          </Text>
        </View>
        
        {/* Right section with theme toggle and optional custom component */}
        <View style={styles.rightSection}>
          {/* Theme toggle button */}
          <TouchableOpacity 
            onPress={toggleTheme} 
            style={styles.iconButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Feather 
              name={isDarkMode ? 'sun' : 'moon'} 
              size={20} 
              color={isDarkMode ? colors.text : COLORS.text} 
            />
          </TouchableOpacity>
          
          {/* Optional right component */}
          {rightComponent}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    zIndex: 10,
  },
  darkContainer: {
    backgroundColor: '#121212',
    borderBottomColor: '#2A2A2A',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 56,
    paddingHorizontal: SPACING.medium,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    ...TYPOGRAPHY.h3,
    color: COLORS.text,
  },
  darkText: {
    color: COLORS.white,
  },
  titleWithBack: {
    marginLeft: SPACING.small,
  },
  backButton: {
    padding: 4,
  },
  iconButton: {
    padding: 4,
    marginLeft: SPACING.medium,
  },
});

export default HomeHeader;
