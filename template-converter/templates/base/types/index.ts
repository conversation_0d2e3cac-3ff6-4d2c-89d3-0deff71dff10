/**
 * @magic_module: Types
 * @magic_purpose: Defines common TypeScript types used throughout the application
 * @magic_category: Types
 * @magic_keywords: types,interfaces,typescript,definitions
 */

// Common response type for API calls
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
  success: boolean;
}

// Error type for API errors
export interface ApiError {
  status: number;
  message: string;
  code?: string;
}

// Image type for the Image component
export interface ImageSource {
  uri: string;
  width?: number;
  height?: number;
  cache?: 'default' | 'reload' | 'force-cache' | 'only-if-cached';
}

// Theme type for the app's theme
export interface Theme {
  dark: boolean;
  colors: {
    primary: string;
    background: string;
    card: string;
    text: string;
    border: string;
    notification: string;
    error: string;
    success: string;
    warning: string;
  };
}

// Form field validation
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  validate?: (value: any) => boolean | string;
  message?: string;
}

// Form field
export interface FormField<T = string> {
  name: string;
  label: string;
  value: T;
  error?: string;
  touched?: boolean;
  validation?: ValidationRule;
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
  total?: number;
  hasMore?: boolean;
}

// Sort options
export type SortDirection = 'asc' | 'desc';

export interface SortOption {
  field: string;
  direction: SortDirection;
  label: string;
}

// Filter options
export interface FilterOption {
  field: string;
  value: string | number | boolean | string[] | number[];
  operator?: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains';
}

// Search params
export interface SearchParams {
  query?: string;
  filters?: FilterOption[];
  sort?: SortOption;
  pagination?: PaginationParams;
}
