/**
 * @magic_component: App
 * @magic_purpose: Root component that sets up providers and navigation
 * @magic_category: Core
 * @magic_keywords: app,root,providers,navigation
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useColorScheme } from 'react-native';

// Import navigation
import Navigation from './navigation';

// Import context providers
import { AppProvider } from './contexts/AppContext';
import { ThemeProvider } from './contexts/ThemeContext';

// Import utilities
import { logger } from './utils/logger';

/**
 * Root App component that sets up the application with all required providers
 * and the navigation structure
 * 
 * IMPORTANT: We avoid using union types and typeof as they can break the app
 */
export default function App() {
  // Log app startup
  useEffect(() => {
    logger.info('Application started', 'GENERAL', { timestamp: new Date().toISOString() });
  }, []);

  return (
    <SafeAreaProvider
      initialMetrics={{
        frame: { x: 0, y: 0, width: 0, height: 0 },
        insets: { top: 0, left: 0, right: 0, bottom: 0 },
      }}
    >
      {/* Theme provider for consistent theming across the app */}
      <ThemeProvider>
        {/* Global app state provider */}
        <AppProvider>
          {/* Main navigation container */}
          <Navigation />
        </AppProvider>
      </ThemeProvider>
      {/* Status bar with adaptive style */}
      <StatusBar style="auto" />
    </SafeAreaProvider>
  );
}