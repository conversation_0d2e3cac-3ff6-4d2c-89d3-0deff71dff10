/**
 * @magic_module: NavigationTypes
 * @magic_purpose: Defines TypeScript types for the navigation system
 * @magic_category: Navigation
 * @magic_keywords: navigation,types,params,routes
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import { NavigatorScreenParams } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp } from '@react-navigation/native';

// Main tab navigation parameters
export type TabParamList = {
  Home: undefined;
  Explore: undefined;
  Profile: undefined;
};

// Main stack navigation parameters
export type RootStackParamList = {
  // Main tab navigator
  Main: undefined;
  
  // Modal screens
  ModalExample: undefined;
  BottomSheetExample: undefined;
  ProductDetail: { productId: string; title: string };
  Settings: undefined;
};

// Type for useNavigation hook - use this for the HomeScreen
export type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

export type ExploreScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<TabParamList, 'Explore'>,
  NativeStackNavigationProp<RootStackParamList>
>;

export type ProfileScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<TabParamList, 'Profile'>,
  NativeStackNavigationProp<RootStackParamList>
>;

// Type for useNavigation hook - use this for stack screens
export type RootStackNavigationProp = NativeStackNavigationProp<RootStackParamList>;
