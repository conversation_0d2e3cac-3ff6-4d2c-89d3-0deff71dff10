/**
 * @magic_component: TabNavigator
 * @magic_purpose: Bottom tab navigation for the main app screens
 * @magic_category: Navigation
 * @magic_keywords: tabs,navigation,bottom tabs
 *
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Feather } from '@expo/vector-icons';

import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Import screens
import HomeTab from '../screens/tabs/HomeTab';
import ExploreTab from '../screens/tabs/ExploreTab';
import ProfileTab from '../screens/tabs/ProfileTab';

// Import types and theme
import { TabParamList } from './types';
import { COLORS } from '../constants/theme';
import { useTheme } from '../contexts/ThemeContext';
import { logger } from '../utils/logger';

// Create tab navigator
const Tab = createBottomTabNavigator<TabParamList>();

/**
 * TabNavigator component that handles the bottom tab navigation
 *
 * This navigator includes:
 * - Home tab
 * - Explore tab
 * - Profile tab
 *
 * Each tab has its own icon and label, and the active tab is highlighted.
 */
export default function TabNavigator() {
    // Get theme from context
    const { isDarkMode, colors } = useTheme();

    // We don't need to manually handle insets - React Navigation does this for us

    // Log when tab navigator is created
    React.useEffect(() => {
        logger.info('Tab navigator created', 'NAVIGATION', {
            timestamp: new Date().toISOString(),
            theme: isDarkMode ? 'dark' : 'light'
        });
    }, [isDarkMode]);

    return (
        <Tab.Navigator
            screenOptions={({ route }) => ({
                tabBarIcon: ({ focused, color, size }) => {
                    // Set icon based on route name
                    if (route.name === 'Home') {
                        return <Feather name="home" size={size} color={color} />;
                    } else if (route.name === 'Explore') {
                        return <Feather name="map" size={size} color={color} />;
                    } else if (route.name === 'Profile') {
                        return <Feather name="user" size={size} color={color} />;
                    }

                    // Default icon
                    return <Feather name="circle" size={size} color={color} />;
                },
                tabBarActiveTintColor: colors.primary,
                tabBarInactiveTintColor: isDarkMode ? colors.textSecondary : colors.textTertiary,
                // Let React Navigation handle the tab bar styling and safe areas
                // This matches how Airbnb and other professional apps handle it
                headerShown: false,
            })}
        >
            <Tab.Screen
                name="Home"
                component={HomeTab}
            />
            <Tab.Screen
                name="Explore"
                component={ExploreTab}
            />
            <Tab.Screen
                name="Profile"
                component={ProfileTab}
            />
        </Tab.Navigator>
    );
}
