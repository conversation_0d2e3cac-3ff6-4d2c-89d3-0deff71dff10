/**
 * @magic_component: Navigation
 * @magic_purpose: Main navigation container that sets up the app's navigation structure
 * @magic_category: Navigation
 * @magic_keywords: navigation,router,stack
 * 
 * IMPORTANT: Do not use union types or typeof in this file as they can break the app
 */
import React, { useRef, useEffect } from 'react';
import { NavigationContainer, DefaultTheme, DarkTheme, NavigationContainerRef } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { Platform, Text, ActivityIndicator, View } from 'react-native';

// Import navigators
import TabNavigator from './TabNavigator';

// Import screens

import ModalExampleScreen from '../screens/modals/ModalExampleScreen';
import BottomSheetExampleScreen from '../screens/modals/BottomSheetExampleScreen';

// Import types and utilities
import { RootStackParamList } from './types';
import { COLORS } from '../constants/theme';
import { useTheme } from '../contexts/ThemeContext';


// Import hooks
import useNavigationWatcher from '../hooks/useNavigationWatcher';
import { logger } from '../utils/logger';

// Create navigator
const Stack = createNativeStackNavigator<RootStackParamList>();

/**
 * Main navigation component that sets up the app's navigation structure
 * 
 * We're using a simple stack navigator with a single screen (HomeScreen)
 * that implements its own custom tab navigation UI.
 * 
 * IMPORTANT: We avoid union types to prevent app breakage
 */
const Navigation = () => {
  // Use the theme from ThemeContext
  const { isDarkMode, colors } = useTheme();
  
  // Create a navigation reference for the navigation logger
  const navigationRef = useRef<NavigationContainerRef<RootStackParamList>>(null);
  
  // Create a theme that extends the default navigation theme
  const navigationTheme = {
    ...(isDarkMode ? DarkTheme : DefaultTheme),
    colors: {
      ...(isDarkMode ? DarkTheme.colors : DefaultTheme.colors),
      primary: colors.primary,
      background: colors.background,
      card: colors.cardBackground,
      text: colors.text,
      border: colors.border,
    },
  };

  // Initialize the simplified navigation watcher
  useNavigationWatcher(navigationRef);
  
  // Log when navigation container is created
  logger.info('Navigation container created', 'NAVIGATION', {
    timestamp: new Date().toISOString(),
    theme: isDarkMode ? 'dark' : 'light'
  });
  
  // Handle URL changes for web platform is now managed by the navigation watcher
  
  return (
    <NavigationContainer 
      ref={navigationRef}
      theme={navigationTheme}

      // This is the key fix for iOS tab bar spacing
      documentTitle={{
        formatter: (options, route) =>
          options?.title ?? route?.name ?? 'Magically',
      }}

      fallback={<View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><ActivityIndicator size="large" color={colors.primary} /><Text style={{ marginTop: 10, color: colors.text }}>Loading...</Text></View>}
    >
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        {/* Main Tab Navigator */}
        <Stack.Screen name="Main" component={TabNavigator} />
        
        {/* Modal Screens */}

        <Stack.Screen 
          name="ModalExample" 
          component={ModalExampleScreen} 
          options={{
            presentation: 'modal',
            animation: 'slide_from_bottom',
          }}
        />
        <Stack.Screen 
          name="BottomSheetExample" 
          component={BottomSheetExampleScreen} 
          options={{
            presentation: 'transparentModal',
            animation: 'slide_from_bottom',
            contentStyle: { backgroundColor: 'transparent' },
          }}
        />
        {/* Add additional screens here when they are created */}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default Navigation;
