import React from 'react';
import { Platform, useColorScheme } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Feather } from '@expo/vector-icons';
import HomeScreen from '../screens/HomeScreen';

// Define types for navigation
export type MainTabsParamList = {
    Home: undefined;
    Home1: undefined;
    Home2: undefined;
};

const Tab = createBottomTabNavigator<MainTabsParamList>();

// Main tab navigator
export const MainTabNavigator = () => {
    const colorScheme = useColorScheme();
    const isDark = colorScheme === 'dark';

    return (
        <Tab.Navigator
            screenOptions={({route}) => ({
                headerShown: false,
                tabBarIcon: ({focused, color, size}) => {
                    let iconName: keyof typeof Feather.glyphMap = 'circle';

                    if (route.name === 'Home') {
                        iconName = 'home';
                    } else if (route.name === 'Home1') {
                        iconName = 'compass';
                    } else if (route.name === 'Home2') {
                        iconName = 'user';
                    } else {
                        iconName = 'circle';
                    }

                    return <Feather name={iconName} size={20} color={color}/>;
                },
                tabBarStyle: {
                    // Always use this configuration to ensure the tab text does not get cut and has enough space for the text and the icon
                    height: Platform.OS === 'ios' ? 72 : 60,
                    paddingBottom: 8,
                    borderTopWidth: 0,
                    elevation: 0,
                    shadowOpacity: 0,
                    // Ensure tab bar doesn't overlap with bottom notch
                    ...(Platform.OS === 'ios' ? {paddingBottom: 0} : {}),
                },
                tabBarLabelStyle: {
                    fontSize: 12,
                    fontWeight: '500',
                }
            })}
        >
            <Tab.Screen name="Home" component={HomeScreen}/>
            <Tab.Screen name="Home1" component={() => null}/>
            <Tab.Screen name="Home2" component={() => null}/>
        </Tab.Navigator>
    );
};

export default MainTabNavigator;
