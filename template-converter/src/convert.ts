import fs from 'fs/promises';
import path from 'path';

interface CodeFile {
  name: string;
  content: string;
  language: string;
}

interface Template {
  templateName: string;
  files: CodeFile[];
}

function getLanguageFromExtension(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case '.ts':
      return 'typescript';
    case '.tsx':
      return 'typescript';
    case '.js':
      return 'javascript';
    case '.jsx':
      return 'javascript';
    case '.css':
      return 'css';
    case '.scss':
      return 'scss';
    case '.json':
      return 'json';
    default:
      return 'plaintext';
  }
}

async function readFileContent(filePath: string): Promise<string> {
  try {
    return await fs.readFile(filePath, 'utf-8');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    throw error;
  }
}

async function getFiles(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function traverse(currentDir: string) {
    const entries = await fs.readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        await traverse(fullPath);
      } else {
        // Only include TypeScript/TSX files and other relevant files
        if (entry.name.match(/\.(tsx?|jsx?|json|css|scss)$/)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  await traverse(dir);
  return files;
}

async function convertTemplate(templateDir: string): Promise<Template> {
  const templateName = path.basename(templateDir);
  const files = await getFiles(templateDir);
  
  const templateFiles: CodeFile[] = await Promise.all(
    files.map(async (filePath) => ({
      name: path.relative(templateDir, filePath),
      content: await readFileContent(filePath),
      language: getLanguageFromExtension(filePath)
    }))
  );
  
  return {
    templateName,
    files: templateFiles,
  };
}

async function main() {
  try {
    const templatesDir = path.join(__dirname, '..', 'templates');
    const templateDirs = await fs.readdir(templatesDir);
    
    const templates: Template[] = await Promise.all(
      templateDirs.map(async (dir) => {
        const templatePath = path.join(templatesDir, dir);
        const stat = await fs.stat(templatePath);
        
        if (stat.isDirectory()) {
          return await convertTemplate(templatePath);
        }
        throw new Error(`${templatePath} is not a directory`);
      })
    );
    
    const outputPath = path.join(__dirname, '..', '..', 'src', 'lib', 'data', 'templates.json');
    await fs.writeFile(outputPath, JSON.stringify(templates, null, 2));
    console.log(`Successfully converted templates to ${outputPath}`);
  } catch (error) {
    console.error('Error converting templates:', error);
    process.exit(1);
  }
}

main();
