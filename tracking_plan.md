# Magically Analytics Tracking Plan

## Overview
This document outlines the analytics tracking strategy for Magically, focusing on capturing key user interactions across all primary flows. The goal is to identify conversion leaks, understand user behavior, and improve the overall product experience.

## Implementation Approach
- Use string-based event names for MVP simplicity
- Leverage PostHog for analytics, heatmaps, and session replays
- Focus on critical user journeys first
- Add common properties to all events (user type, timestamp, etc.)

## Core User Flows

### 1. Idea to Implementation Flow
| Event Name | Description | Key Properties | Location |
|------------|-------------|----------------|----------|
| `idea_form_viewed` | User views the idea input form | `source`, `referrer` | IdeaForm.tsx |
| `idea_form_focused` | User focuses on the idea input | `source` | IdeaForm.tsx |
| `image_attached` | User attaches an image to prompt | `file_type`, `file_size` | IdeaForm.tsx |
| `image_removed` | User removes an attached image | `file_type` | IdeaForm.tsx |
| `idea_submitted` | User submits their app idea | `prompt_length`, `has_attachments`, `attachment_count` | IdeaForm.tsx |
| `project_created` | New project is created | `project_id`, `time_to_create` | IdeaForm.tsx (handleSubmit) |
| `project_creation_failed` | Project creation fails | `error_type`, `error_message` | IdeaForm.tsx (handleSubmit) |
| `first_preview_loaded` | First preview of implementation shown | `time_to_first_preview` | PreviewPanel.tsx |

### 2. Chat Interaction Flow
| Event Name | Description | Key Properties | Location |
|------------|-------------|----------------|----------|
| `chat_started` | New chat session begins | `chat_id`, `project_id` | chat.tsx |
| `chat_resumed` | User returns to existing chat | `chat_id`, `time_since_last_activity` | chat.tsx |
| `message_sent` | User sends a message | `message_type`, `message_length`, `has_attachments` | chat.tsx, multimodal-input.tsx |
| `message_edited` | User edits a message before sending | `original_length`, `new_length` | multimodal-input.tsx |
| `ai_response_streaming` | AI begins streaming a response | `chat_id`, `message_id` | chat.tsx |
| `ai_response_received` | AI completes a response | `response_time`, `token_count` | chat.tsx |
| `ai_response_interrupted` | User stops an AI response | `time_elapsed`, `completion_percentage` | chat.tsx |
| `message_error` | Error during message processing | `error_type`, `error_message` | chat.tsx |
| `code_snippet_copied` | User copies generated code | `snippet_length`, `language` | PreviewPanel.tsx |
| `conversation_abandoned` | User leaves mid-conversation | `messages_count`, `time_in_conversation` | chat.tsx |
| `message_voted` | User votes on an AI response | `vote_type`, `message_id`, `chat_id`, `project_id` | message-actions.tsx |
| `message_checkpoint_restored` | User reverts to a previous checkpoint | `message_id`, `chat_id`, `project_id`, `messages_deleted` | message-actions.tsx |
| `message_regenerated` | User retries generating a response | `message_id`, `chat_id`, `project_id`, `trigger_source` | message-actions.tsx |

### 3. Authentication and Conversion Flow
| Event Name | Description | Key Properties | Location |
|------------|-------------|----------------|----------|
| `anonymous_id_generated` | New anonymous user created | `anonymous_id` | anonymous-provider.tsx |
| `user_signed_in` | User signs in or attempts to sign in | `auth_method`, `referral_source`, `error_type` | google-button.tsx, login-dialog.tsx |
| `login_prompted` | Login dialog is shown | `trigger_reason`, `auth_method` | login-dialog.tsx |
| `login_completed` | User successfully logs in | `auth_method`, `time_to_complete` | login/page.tsx |
| `login_failed` | Login attempt fails | `error_type`, `auth_method` | login/page.tsx |
| `credit_limit_reached` | User hits credit/operation limit | `limit_type`, `current_usage`, `message_limit_remaining` | chat.tsx, upgrade-dialog.tsx |
| `upgrade_dialog_viewed` | Upgrade dialog is shown | `trigger_reason`, `current_plan`, `message_limit_remaining` | upgrade-dialog.tsx |
| `upgrade_initiated` | User begins upgrade process | `current_plan`, `plan_type`, `price`, `currency`, `entry_point` | upgrade-dialog.tsx, pricing/page.tsx |
| `upgrade_completed` | Upgrade purchase complete | `current_plan`, `plan_type`, `price`, `currency` | upgrade-dialog.tsx, pricing/page.tsx |
| `upgrade_failed` | Upgrade purchase fails | `current_plan`, `plan_type`, `error_message`, `trigger_source` | upgrade-dialog.tsx, pricing/page.tsx |
| `upgrade_dismissed` | User dismisses upgrade dialog | `time_viewed`, `current_plan` | upgrade-dialog.tsx |
| `pricing_viewed` | User views pricing options | `entry_point`, `current_plan` | pricing/page.tsx |
| `subscription_started` | User begins subscription process | `plan_selected`, `price` | upgrade-dialog.tsx |
| `subscription_completed` | Subscription purchase complete | `plan_type`, `payment_method` | upgrade-dialog.tsx |
| `subscription_failed` | Subscription purchase fails | `error_type`, `plan_type` | upgrade-dialog.tsx |

### 4. Feature Usage and Engagement Flow
| Event Name | Description | Key Properties | Location |
|------------|-------------|----------------|----------|
| `file_tree_navigated` | User navigates file structure | `file_count`, `directory_depth`, `previous_state`, `new_state` | Generator components, PreviewPanel.tsx |
| `file_selected` | User selects a specific file | `file_type`, `file_path`, `trigger_source` | Generator components, Header.tsx |
| `code_edited` | User manually edits generated code | `file_type`, `edit_size`, `feature_name`, `project_id`, `trigger_source`, `deployment_time` | PreviewPanel.tsx |
| `preview_refreshed` | Preview is manually refreshed | `trigger_source`, `feature_name`, `project_id` | PreviewPanel.tsx, Header.tsx |
| `agent_mode_toggled` | User toggles AI Agent Mode | `new_state`, `previous_state`, `trigger_source` | agent-mode-settings.tsx |
| `supabase_connection_initiated` | User begins Supabase connection | `project_id`, `platform_type`, `feature_name` | Header.tsx |
| `supabase_connection_completed` | Supabase connection established | `project_id`, `time_to_connect`, `platform_type` | Integration components |
| `deployment_initiated` | User attempts to deploy app | `platform_type`, `feature_name`, `project_id` | Header.tsx, Generator components |
| `deployment_completed` | App deployment completes | `platform_type`, `deployment_time`, `feature_name`, `project_id` | Header.tsx, Generator components |
| `deployment_failed` | App deployment fails | `platform_type`, `trigger_source`, `project_id` | Header.tsx, Generator components |

### 5. Onboarding and Learning Flow
| Event Name | Description | Key Properties | Location |
|------------|-------------|----------------|----------|
| `first_time_user_identified` | New user detected | `referral_source` | Various entry points |
| `onboarding_started` | User begins onboarding | `entry_point` | Onboarding components |
| `onboarding_step_completed` | Onboarding step completed | `step_number`, `time_on_step` | Onboarding components |
| `onboarding_completed` | User finishes onboarding | `total_time`, `steps_completed` | Onboarding components |
| `onboarding_skipped` | User skips onboarding | `step_skipped_at` | Onboarding components |
| `help_resource_accessed` | User accesses help/docs | `feature_name`, `trigger_source`, `project_id` | Header.tsx, Various components |
| `tooltip_shown` | Feature tooltip displayed | `feature_name`, `tooltip_id` | Various components |
| `tooltip_dismissed` | User dismisses tooltip | `tooltip_id`, `time_visible` | Various components |
| `mobile_view_instructions` | User views mobile testing instructions | `feature_name`, `trigger_source`, `project_id` | ViewInMobileButton.tsx |

### 6. Performance and Error Tracking
| Event Name | Description | Key Properties | Location |
|------------|-------------|----------------|----------|
| `page_load_completed` | Page fully loads | `page_url`, `load_time_ms` | Various pages |
| `api_request_initiated` | API request begins | `endpoint`, `request_size` | API calls |
| `api_request_completed` | API request completes | `endpoint`, `response_time_ms` | API calls |
| `api_request_failed` | API request fails | `endpoint`, `error_type` | API calls |
| `rendering_error` | UI rendering error | `component`, `error_message` | Various components |
| `resource_loading_failed` | Resource fails to load | `resource_type`, `url` | Various components |
| `credit_calculation_performed` | Operation credits calculated | `operation_count`, `credit_cost` | Subscription components |

## Common Properties for All Events
- `user_id` - User identifier (anonymous or authenticated)
- `user_type` - Anonymous, free, or paid tier
- `timestamp` - When the event occurred
- `session_id` - Current session identifier
- `device_type` - Desktop, mobile, tablet
- `browser_info` - Browser type and version
- `referrer` - Where the user came from
- `page_url` - Current page URL

## PostHog-Specific Implementation
- Enable autocapture for basic interactions
- Configure session recordings with appropriate privacy settings
- Set up heatmaps for key pages (home, generator, settings)
- Use feature flags for A/B testing new features

## Data Quality Guidelines
- Use consistent naming conventions
- Validate events in development
- Document new events in this plan
- Review analytics implementation quarterly

## Next Steps
1. Implement core events in highest-priority flows
2. Set up basic dashboards in PostHog
3. Monitor data quality
4. Iterate based on initial insights
