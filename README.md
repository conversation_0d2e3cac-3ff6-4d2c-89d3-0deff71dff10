This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## MO_DIFF Format

The MO_DIFF format is used to make changes to files. It supports both partial edits and full file replacements.

### Basic Format

```
<MO_DIFF lang="typescript" path="path/to/file.ts">
<SEARCH>
// Code to search for
</SEARCH>
<REPLACE>
// Code to replace it with
</REPLACE>
</MO_DIFF>
```

### Full File Replacement

For full file replacements, use an empty search block:

```
<MO_DIFF lang="typescript" path="path/to/file.ts">
<SEARCH>
</SEARCH>
<REPLACE>
// Entire new file content goes here
</REPLACE>
</MO_DIFF>
```

### Multiple Edits

You can make multiple edits to the same file by including multiple search/replace pairs:

```
<MO_DIFF lang="typescript" path="path/to/file.ts">
<SEARCH>
// First code to search for
</SEARCH>
<REPLACE>
// First replacement
</REPLACE>
<SEARCH>
// Second code to search for
</SEARCH>
<REPLACE>
// Second replacement
</REPLACE>
</MO_DIFF>
```

### Important Notes

1. Each `<SEARCH>` must have a corresponding `<REPLACE>`.
2. For full file replacements, use an empty `<SEARCH>` block.
3. Search patterns must be unique within the file to avoid ambiguity.
4. Include enough context in your search patterns to ensure they match exactly one location.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
