import { Server as SocketIOServer } from 'socket.io';
import { Sandbox } from '@e2b/code-interpreter';
import { createPty, handleTerminalCommand, PROJECT_DIR, uploadFilesToSandbox } from '@/lib/terminal/helpers';
import { NextApiRequest, NextApiResponse } from 'next';
import { v4 as uuidv4 } from 'uuid';
import { CommandHandle } from '@e2b/code-interpreter';
import { TextEncoder } from 'util';

const wait = (ms: number) => new Promise(resolve => {
  console.log(`Waiting for ${ms}ms`)
  return setTimeout(resolve, ms);
});
// Terminal session manager class
class TerminalSessionManager {
  private sessions: Map<string, {
    sandbox: Sandbox;
    terminalId: number | null;
    socketId?: string;
    terminal?: CommandHandle;
  }> = new Map();

  // Get or create a session
  async getOrCreateSession(sessionId: string, socketId: string): Promise<{
    sessionId: string;
    isNew: boolean;
  }> {
    // Check if session exists
    if (this.sessions.has(sessionId)) {
      const session = this.sessions.get(sessionId)!;
      session.socketId = socketId;
      return { sessionId, isNew: false };
    }

    // Create new sandbox
    console.log('Creating new sandbox for session:', sessionId);
    const sandbox = await Sandbox.create("eas-base",{
      apiKey: process.env.E2B_API_KEY,
      requestTimeoutMs: 0,
      timeoutMs: 60 * 1000 * 10
    });
    
    // Get socket from socketMap
    const io = (global as any).socketIO;
    const socket = io?.sockets?.sockets?.get(socketId);
    
    // Notify client that we're starting file upload
    if (socket) {
      socket.emit('FILE_UPLOAD_PROGRESS', { 
        sessionId, 
        progress: 0, 
        status: 'Starting file upload...' 
      });
    }
    
    // Store session first
    this.sessions.set(sessionId, {
      sandbox,
      terminalId: null,
      socketId
    });
    
    // Start file upload and wait for it to complete
    try {
      const uploadResult = await uploadFilesToSandbox(sandbox, sessionId, (progress, status) => {
        // Send progress updates
        if (socket) {
          socket.emit('FILE_UPLOAD_PROGRESS', { sessionId, progress, status });
        }
      });

      if (!uploadResult) {
        console.error(`File upload failed for session ${sessionId}`);
        if (socket) {
          socket.emit('ERROR', { error: 'File upload failed' });
        }
      }
      
      // Initialize terminal only after file upload completes
      await this.initializeTerminal(sessionId);
    } catch (error) {
      console.error(`Error during file upload for session ${sessionId}:`, error);
      if (socket) {
        socket.emit('ERROR', { error: `File upload error: ${(error as Error).message}` });
      }
    }

    return { sessionId, isNew: true };
  }

  // Initialize terminal for a session
  async initializeTerminal(sessionId: string): Promise<void> {
    console.log(`Initializing terminal for session ${sessionId}`);
    const session = this.sessions.get(sessionId);
    if (!session) throw new Error(`Session ${sessionId} not found`);

    try {
      await session.sandbox.files.makeDir(PROJECT_DIR);

      // await uploadFilesToSandbox(session.sandbox, sessionId);
      // Create PTY terminal
      console.log(`Creating PTY for session ${sessionId} in directory ${PROJECT_DIR}`);
      await wait(1000);
      const handle = await createPty(
        { sandbox: session.sandbox },
        {
          cwd: PROJECT_DIR,
          onData: (data) => {
            // If socket ID exists, emit data to that socket
            if (session.socketId) {
              const io = global.io as SocketIOServer;
              const socket = io.sockets.sockets.get(session.socketId);
              if (socket) {
                console.log(`Sending terminal output to client for session ${sessionId}:`, data);
                // Ensure data is properly formatted as a string
                const outputData = typeof data === 'string' ? data : String(data);
                socket.emit('TERMINAL_OUTPUT', { data: outputData, sessionId });
              } else {
                console.error(`Socket not found for session ${sessionId} with socketId ${session.socketId}`);
              }
            } else {
              console.error(`No socketId associated with session ${sessionId}`);
            }
          }
        }
      );

      // Update session with terminal ID
      session.terminal = handle;
      session.terminalId = handle.pid;
      console.log(`Terminal initialized for session ${sessionId} with PID ${handle.pid}`);
    } catch (error) {
      console.error(`Error initializing terminal for session ${sessionId}:`, error);
      throw error;
    }
  }

  // Execute command in terminal
  async executeCommand(sessionId: string, command: string): Promise<void> {
    console.log(`Executing command in session ${sessionId}: ${command}`);
    const session = this.sessions.get(sessionId);
    if (!session) throw new Error(`Session ${sessionId} not found`);

    console.log('session', session)
    // Ensure terminal is initialized
    if (!session.terminalId) {
      console.log(`Terminal not initialized for session ${sessionId}, initializing now`);
      await this.initializeTerminal(sessionId);
    }

    // Execute command
    console.log(`Sending command to terminal ${session.terminalId}`);
    await handleTerminalCommand(session, command);
  }
  
  // Send raw input to terminal
  async sendRawInput(sessionId: string, data: string): Promise<void> {
    console.log(`Sending raw input to terminal for session ${sessionId}`);
    const session = this.sessions.get(sessionId);
    if (!session) throw new Error(`Session ${sessionId} not found`);
    
    // Ensure terminal is initialized
    if (!session.terminalId) {
      console.log(`Terminal not initialized for session ${sessionId}, initializing now`);
      await this.initializeTerminal(sessionId);
    }
    
    // Send raw input to terminal
    if (session.terminalId && session.sandbox) {
      // Use the sandbox.pty.sendInput method to send data to the terminal
      await session.sandbox.pty.sendInput(
        session.terminalId,
        new TextEncoder().encode(data),
          {
            requestTimeoutMs: 0
          }
      );
    }
  }

  // Close session
  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    try {
      // Use the correct method to close the sandbox
      await session.sandbox.kill();
    } catch (error) {
      console.error('Error closing sandbox:', error);
    }

    this.sessions.delete(sessionId);
  }

  // Get all session IDs
  getSessionIds(): string[] {
    return Array.from(this.sessions.keys());
  }
  
  // Get a specific session by ID
  getSession(sessionId: string) {
    return this.sessions.get(sessionId);
  }
}

// Create singleton instance
const sessionManager = new TerminalSessionManager();

// Store io instance globally
declare global {
  var io: SocketIOServer | undefined;
}

// This is needed because Next.js Edge runtime doesn't fully support socket.io
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check if socket.io server is already running
  if ((res.socket as any).server.io) {
    console.log('Socket.io server already running');
    res.end();
    return;
  }

  // Set up Socket.io server
  const io = new SocketIOServer((res.socket as any).server, {
    path: '/api/terminal-ws',
    addTrailingSlash: false,
    cors: {
      origin: '*',
      methods: ['GET', 'POST'],
    },
    // Increase timeouts to prevent disconnections
    pingTimeout: 60000, // 60 seconds
    pingInterval: 25000, // 25 seconds
    connectTimeout: 30000, // 30 seconds
  });
  
  // Store io instance on the server and globally
  (res.socket as any).server.io = io;
  global.io = io;

  // Handle terminal connections
  io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);
    
    // Handle session initialization
    socket.on('INIT_SESSION', async (data: { sessionId?: string }) => {
      try {
        console.log('INIT_SESSION received with data:', data);
        const sessionId = data.sessionId || uuidv4();
        console.log(`Using session ID: ${sessionId}`);
        
        // Initialize session
        const result = await sessionManager.getOrCreateSession(sessionId, socket.id);
        
        // Send confirmation back to client
        console.log(`Sending SESSION_INITIALIZED for ${result.sessionId} (new: ${result.isNew})`);
        socket.emit('SESSION_INITIALIZED', { sessionId: result.sessionId, new: result.isNew });
      } catch (error) {
        console.error('Error initializing sessiona:', error);
        socket.emit('ERROR', { error: (error as Error).message });
      }
    });
    
    // Handle command execution
    socket.on('EXECUTE_COMMAND', async (data: { sessionId: string, command: string }) => {
      try {
        const { sessionId, command } = data;
        
        if (!sessionId || !command) {
          socket.emit('ERROR', { error: 'Session ID and command are required' });
          return;
        }
        
        console.log(`Executing command in session ${sessionId}: ${command}`);
        await sessionManager.executeCommand(sessionId, command);
      } catch (error) {
        console.error('Error executing command:', error);
        
        // Check for timeout errors
        const errorMessage = (error as Error).message;
        if (errorMessage.includes('timeout') || errorMessage.includes('HTTP 502')) {
          // Send a specific timeout error message
          socket.emit('SANDBOX_TIMEOUT', { 
            error: 'Sandbox timeout occurred. The terminal session needs to be restarted.', 
            sessionId: data.sessionId 
          });
        } else {
          // Send regular error for non-timeout issues
          socket.emit('ERROR', { error: errorMessage });
        }
      }
    });
    
    // Handle raw terminal input
    socket.on('TERMINAL_INPUT', async (data: { sessionId: string, data: string }) => {
      try {
        const { sessionId, data: inputData } = data;
        
        if (!sessionId) {
          socket.emit('ERROR', { error: 'Session ID is required' });
          return;
        }
        
        // Use the sendRawInput method to handle terminal input
        await sessionManager.sendRawInput(sessionId, inputData);
      } catch (error) {
        console.error('Error handling terminal input:', error);
        
        // Check for timeout errors
        const errorMessage = (error as Error).message;
        if (errorMessage.includes('timeout') || errorMessage.includes('HTTP 502')) {
          // Send a specific timeout error message
          socket.emit('SANDBOX_TIMEOUT', { 
            error: 'Sandbox timeout occurred. The terminal session needs to be restarted.', 
            sessionId: data.sessionId 
          });
          
          // Mark the session as invalid
          const session = sessionManager.getSession(data.sessionId);
          if (session) {
            // Clean up the existing terminal if it exists
            try {
              if (session.terminal && session.terminalId) {
                // Attempt to close the terminal gracefully
                // We can't directly call handleKill as it's private
                // Instead, mark the session for cleanup
                session.terminal = undefined;
                session.terminalId = null;
              }
            } catch (cleanupError) {
              console.error('Error cleaning up terminal after timeout:', cleanupError);
            }
          }
        } else {
          // Send regular error for non-timeout issues
          socket.emit('ERROR', { error: errorMessage });
        }
      }
    });
    
    // Handle heartbeat to keep connection alive
    socket.on('HEARTBEAT', (data: { sessionId: string }) => {
      // Just acknowledge the heartbeat, no need to do anything else
      console.log(`Received heartbeat for session ${data.sessionId}`);
      socket.emit('HEARTBEAT_ACK', { sessionId: data.sessionId, timestamp: Date.now() });
    });
    
    // Handle terminal resize events
    socket.on('RESIZE_TERMINAL', async (data: { sessionId: string, cols: number, rows: number }) => {
      try {
        const { sessionId, cols, rows } = data;
        console.log(`Resizing terminal for session ${sessionId} to ${cols}x${rows}`);
        
        const session = sessionManager.getSession(sessionId);
        if (!session) {
          throw new Error(`Session ${sessionId} not found`);
        }
        
        if (!session.terminal || !session.terminalId) {
          throw new Error(`Terminal not initialized for session ${sessionId}`);
        }
        
        // Resize the terminal
        await session.sandbox.pty.resize(session.terminalId, { cols, rows });
        console.log(`Successfully resized terminal for session ${sessionId}`);
      } catch (error) {
        console.error('Error resizing terminal:', error);
        socket.emit('ERROR', { error: `Failed to resize terminal: ${(error as Error).message}` });
      }
    });
    
    // Handle session check request
    socket.on('CHECK_SESSION', async (data: { sessionId: string }, callback) => {
      try {
        const { sessionId } = data;
        console.log(`Checking if session ${sessionId} exists`);
        
        const session = sessionManager.getOrCreateSession(sessionId, socket.id);
        const exists = !!session;
        
        console.log(`Session ${sessionId} exists: ${exists}`);
        
        // Return the result through the callback
        if (typeof callback === 'function') {
          callback({ exists });
        } else {
          // Fallback if callback is not provided
          socket.emit('SESSION_CHECK_RESULT', { sessionId, exists });
        }
      } catch (error) {
        console.error('Error checking session:', error);
        
        // Return false through the callback if available
        if (typeof callback === 'function') {
          callback({ exists: false });
        } else {
          socket.emit('ERROR', { error: (error as Error).message });
        }
      }
    });
    
    // Handle joining an existing session with a new socket
    socket.on('JOIN_SESSION', async (data: { sessionId: string }) => {
      try {
        const { sessionId } = data;
        console.log(`Socket ${socket.id} joining existing session: ${sessionId}`);

        // Get the existing session
        const session = sessionManager.getSession(sessionId);
        
        if (!session) {
          throw new Error(`Session ${sessionId} not found`);
        }
        
        // Update the socket ID for this session
        // This allows the new socket to receive terminal output
        session.socketId = socket.id;
        
        console.log(`Socket ${socket.id} successfully joined session ${sessionId}`);
        socket.emit('SESSION_JOINED', { sessionId });
      } catch (error) {
        console.error(`Error joining session:`, error);
        socket.emit('ERROR', { error: `Failed to join session: ${(error as Error).message}` });
      }
    });
    
    // Handle session closure
    socket.on('CLOSE_SESSION', async (data: { sessionId: string }) => {
      try {
        const { sessionId } = data;
        
        if (!sessionId) {
          socket.emit('ERROR', { error: 'Session ID is required' });
          return;
        }
        
        console.log(`Closing session: ${sessionId}`);
        await sessionManager.closeSession(sessionId);
        socket.emit('SESSION_CLOSED', { sessionId });
      } catch (error) {
        console.error('Error closing session:', error);
        socket.emit('ERROR', { error: (error as Error).message });
      }
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
      // We don't close sessions on disconnect, just log it
      // Sessions will be cleaned up when explicitly closed or server restarts
    });
  });

  console.log('Socket.io server started');
  res.end();
}
