import type {NextApiRequest, NextApiResponse} from 'next';
import {Snack} from 'snack-sdk';
import {DEFAULT_CODE} from '@/types/editor';

// Using pages/api instead of app/api to avoid bundling issues
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        return res.status(405).json({error: 'Method not allowed'});
    }

    try {
        const snackFiles = DEFAULT_CODE.reduce((acc, file) => {
            acc[file.name] = {
                type: 'CODE' as const,
                contents: file.content
            };
            return acc;
        }, {} as Record<string, { type: 'CODE', contents: string }>);

        const snack = new Snack({
            online: true,
            files: snackFiles,
            dependencies: {
                'expo-status-bar': {version: '*'},
                'expo-linear-gradient': {version: '*'},
                '@expo/vector-icons': {version: '*'},
                'react-native-paper': {version: '*'},
                '@react-navigation/native': {version: '*'},
                '@react-navigation/native-stack': {version: '*'},
                'react-native-safe-area-context': {version: '*'},
                'react-native-screens': {version: '*'}
            },
            sdkVersion: '52.0.0'
        });

        const { id, url, snackId, accountSnackId } = await snack.saveAsync();
        const {channel, online, webPlayerURL, webPreviewURL} = await snack.getStateAsync();
        snack.addLogListener(({connectedClient, type, message}) => {
            console.log('asd', connectedClient, type, message)
        })

        console.log('km', id, url, snackId, accountSnackId, online, webPlayerURL, webPreviewURL )
        // Return both id and channel
        res.status(200).json({
            url: `${url}&snack-channel=${channel}`,
            id,
            snackId,
            accountSnackId
        });
    } catch (error) {
        console.error('Failed to create snack:', error);
        res.status(500).json({error: 'Failed to create snack'});
    }
}
