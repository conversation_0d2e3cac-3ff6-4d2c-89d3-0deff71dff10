export interface Product {
    id: string;
    name: string;
    price: number;
    description: string;
    imageUrl: string;
    sizes: string[];
    colors: string[];
    category: string;
    isNew: boolean;
    isFavorite: boolean;
}

export type SortOption = 'price-asc' | 'price-desc' | 'name-asc' | 'name-desc' | 'newest';

export interface FilterOptions {
    categories: string[];
    priceRange: [number, number];
    sizes: string[];
    colors: string[];
    onlyNew: boolean;
}