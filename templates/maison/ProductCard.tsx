import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Product } from './types';

type ProductCardProps = Product & {
    onPress: () => void;
    onFavoritePress: () => void;
};

const { width } = Dimensions.get('window');
const cardWidth = width / 2 - 24;

export const ProductCard: React.FC<ProductCardProps> = ({
                                                            name,
                                                            price,
                                                            description,
                                                            imageUrl,
                                                            onPress,
                                                            onFavoritePress,
                                                            isNew,
                                                            isFavorite,
                                                            sizes,
                                                            colors,
                                                        }) => {
    return (
        <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.9}>
            <View style={styles.imageContainer}>
                <Image
                    source={{ uri: imageUrl }}
                    style={styles.image}
                    resizeMode="cover"
                />
                <TouchableOpacity
                    style={styles.favoriteButton}
                    onPress={onFavoritePress}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                    <Text style={styles.favoriteIcon}>{isFavorite ? '♥' : '♡'}</Text>
                </TouchableOpacity>
                {isNew && (
                    <LinearGradient
                        colors={['#000', '#333']}
                        style={styles.newBadge}
                    >
                        <Text style={styles.newBadgeText}>NEW</Text>
                    </LinearGradient>
                )}
            </View>
            <View style={styles.content}>
                <Text style={styles.name} numberOfLines={1}>{name}</Text>
                <Text style={styles.description} numberOfLines={2}>
                    {description}
                </Text>
                <View style={styles.detailsRow}>
                    <Text style={styles.price}>${price.toFixed(2)}</Text>
                    <View style={styles.sizesContainer}>
                        {sizes.slice(0, 3).map((size, index) => (
                            <Text key={size} style={styles.size}>
                                {size}{index < Math.min(sizes.length - 1, 2) && ', '}
                            </Text>
                        ))}
                        {sizes.length > 3 && <Text style={styles.size}>...</Text>}
                    </View>
                </View>
                <View style={styles.colorsContainer}>
                    {colors.map((color) => (
                        <View
                            key={color}
                            style={[styles.colorDot, { backgroundColor: color }]}
                        />
                    ))}
                </View>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: '#fff',
        borderRadius: 12,
        margin: 8,
        width: cardWidth,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        overflow: 'hidden',
    },
    imageContainer: {
        position: 'relative',
    },
    image: {
        width: '100%',
        height: cardWidth * 1.3,
        backgroundColor: '#f5f5f5',
    },
    favoriteButton: {
        position: 'absolute',
        top: 8,
        right: 8,
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderRadius: 15,
        width: 30,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
    },
    favoriteIcon: {
        fontSize: 18,
        color: '#ff4d4d',
    },
    newBadge: {
        position: 'absolute',
        top: 8,
        left: 8,
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
    },
    newBadgeText: {
        color: '#fff',
        fontSize: 10,
        fontWeight: '600',
    },
    content: {
        padding: 12,
    },
    name: {
        fontSize: 13,
        fontWeight: '600',
        color: '#1a1a1a',
        marginBottom: 4,
    },
    description: {
        fontSize: 11,
        color: '#666',
        marginBottom: 8,
        lineHeight: 16,
    },
    detailsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    price: {
        fontSize: 14,
        fontWeight: '700',
        color: '#1a1a1a',
    },
    sizesContainer: {
        flexDirection: 'row',
    },
    size: {
        fontSize: 11,
        color: '#666',
    },
    colorsContainer: {
        flexDirection: 'row',
        gap: 4,
    },
    colorDot: {
        width: 12,
        height: 12,
        borderRadius: 6,
        borderWidth: 1,
        borderColor: '#f0f0f0',
    },
});