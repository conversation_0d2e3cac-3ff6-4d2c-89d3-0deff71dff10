import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SortOption } from './types';

interface FilterBarProps {
    selectedCategory: string;
    onCategoryChange: (category: string) => void;
    sortOption: SortOption;
    onSortChange: (sort: SortOption) => void;
    onFilterPress: () => void;
}

const categories = ['All', 'Dresses', 'Tops', 'Pants', 'Blazers', 'Accessories'];

export const FilterBar: React.FC<FilterBarProps> = ({
                                                        selectedCategory,
                                                        onCategoryChange,
                                                        sortOption,
                                                        onSortChange,
                                                        onFilterPress,
                                                    }) => {
    return (
        <View style={styles.container}>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.categoriesContainer}
            >
                {categories.map((category) => (
                    <TouchableOpacity
                        key={category}
                        style={[
                            styles.categoryButton,
                            selectedCategory === category && styles.selectedCategory,
                        ]}
                        onPress={() => onCategoryChange(category)}
                    >
                        <Text
                            style={[
                                styles.categoryText,
                                selectedCategory === category && styles.selectedCategoryText,
                            ]}
                        >
                            {category}
                        </Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>

            <View style={styles.actionButtons}>
                <TouchableOpacity
                    style={styles.sortButton}
                    onPress={() => {
                        const sorts: SortOption[] = ['price-asc', 'price-desc', 'name-asc', 'name-desc', 'newest'];
                        const currentIndex = sorts.indexOf(sortOption);
                        const nextSort = sorts[(currentIndex + 1) % sorts.length];
                        onSortChange(nextSort);
                    }}
                >
                    <Text style={styles.actionButtonText}>
                        {sortOption === 'price-asc' && '↑ Price'}
                        {sortOption === 'price-desc' && '↓ Price'}
                        {sortOption === 'name-asc' && '↑ Name'}
                        {sortOption === 'name-desc' && '↓ Name'}
                        {sortOption === 'newest' && 'Newest'}
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.filterButton} onPress={onFilterPress}>
                    <Text style={styles.actionButtonText}>Filters</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        paddingVertical: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    categoriesContainer: {
        paddingHorizontal: 16,
        gap: 8,
    },
    categoryButton: {
        paddingHorizontal: 16,
        paddingVertical: 6,
        borderRadius: 20,
        backgroundColor: '#f8f8f8',
    },
    selectedCategory: {
        backgroundColor: '#000',
    },
    categoryText: {
        fontSize: 13,
        color: '#666',
    },
    selectedCategoryText: {
        color: '#fff',
    },
    actionButtons: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingHorizontal: 16,
        marginTop: 8,
        gap: 8,
    },
    sortButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        backgroundColor: '#f0f0f0',
    },
    filterButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        backgroundColor: '#000',
    },
    actionButtonText: {
        fontSize: 12,
        color: '#000',
        fontWeight: '500',
    },
});