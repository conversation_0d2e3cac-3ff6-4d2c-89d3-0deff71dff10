import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface SideMenuProps {
    isVisible: boolean;
    onClose: () => void;
    translateX: Animated.Value;
}

export const SideMenu: React.FC<SideMenuProps> = ({ isVisible, onClose, translateX }) => {
    const menuItems = [
        { title: 'New Arrivals', subtitle: 'Fall/Winter 2024' },
        { title: 'Collections', subtitle: 'Curated Elegance' },
        { title: 'Atelier', subtitle: 'Made to Measure' },
        { title: 'Personal Stylist', subtitle: 'Book Appointment' },
        { title: 'Private Events', subtitle: 'Exclusive Access' },
        { title: 'Heritage', subtitle: 'Our Story' },
        { title: 'Sustainability', subtitle: 'Our Commitment' },
        { title: 'Care Guide', subtitle: 'Product Care' },
    ];

    if (!isVisible) return null;

    return (
        <Animated.View
            style={[
                styles.container,
                {
                    transform: [{ translateX }]
                }
            ]}
        >
            <LinearGradient
                colors={['#ffffff', '#f8f9fa']}
                style={styles.content}
            >
                <View style={styles.header}>
                    <Text style={styles.brandName}>MAISON</Text>
                    <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                        <Text style={styles.closeText}>×</Text>
                    </TouchableOpacity>
                </View>

                <ScrollView style={styles.menuItems} showsVerticalScrollIndicator={false}>
                    {menuItems.map((item, index) => (
                        <TouchableOpacity
                            key={item.title}
                            style={[
                                styles.menuItem,
                                index === 0 && styles.firstMenuItem
                            ]}
                        >
                            <Text style={styles.menuTitle}>{item.title}</Text>
                            <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>

                <View style={styles.footer}>
                    <TouchableOpacity style={styles.footerItem}>
                        <Text style={styles.footerTitle}>Concierge Service</Text>
                        <Text style={styles.footerSubtitle}>Available 24/7</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.footerItem}>
                        <Text style={styles.footerTitle}>Exclusive Preview</Text>
                        <Text style={styles.footerSubtitle}>Join Waitlist</Text>
                    </TouchableOpacity>
                </View>

                <View style={styles.seasonLabel}>
                    <Text style={styles.seasonText}>FALL/WINTER 2024</Text>
                </View>
            </LinearGradient>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        width: '80%',
        backgroundColor: '#fff',
        zIndex: 1000,
        shadowColor: '#000',
        shadowOffset: {
            width: 2,
            height: 0,
        },
        shadowOpacity: 0.15,
        shadowRadius: 15,
        elevation: 10,
    },
    content: {
        flex: 1,
        paddingTop: 60,
    },
    header: {
        paddingHorizontal: 24,
        paddingBottom: 40,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    brandName: {
        fontSize: 24,
        fontWeight: '300',
        letterSpacing: 4,
        color: '#1a1a1a',
    },
    closeButton: {
        width: 32,
        height: 32,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeText: {
        fontSize: 24,
        color: '#1a1a1a',
        lineHeight: 24,
    },
    menuItems: {
        flex: 1,
    },
    menuItem: {
        paddingVertical: 20,
        paddingHorizontal: 24,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.05)',
    },
    firstMenuItem: {
        borderTopWidth: 0,
    },
    menuTitle: {
        fontSize: 16,
        fontWeight: '400',
        color: '#1a1a1a',
        marginBottom: 4,
    },
    menuSubtitle: {
        fontSize: 12,
        color: '#666',
    },
    footer: {
        padding: 24,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.05)',
    },
    footerItem: {
        marginBottom: 20,
    },
    footerTitle: {
        fontSize: 14,
        fontWeight: '500',
        color: '#1a1a1a',
        marginBottom: 2,
    },
    footerSubtitle: {
        fontSize: 12,
        color: '#666',
    },
    seasonLabel: {
        position: 'absolute',
        bottom: 20,
        right: 20,
        transform: [{ rotate: '-90deg' }],
        transformOrigin: 'right bottom',
    },
    seasonText: {
        fontSize: 10,
        letterSpacing: 1,
        color: '#999',
    },
});