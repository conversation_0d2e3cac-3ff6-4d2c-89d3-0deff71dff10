import React, { useState, useCallback, useRef } from 'react';
import {
    View,
    StyleSheet,
    FlatList,
    Text,
    RefreshControl,
    Modal,
    TouchableOpacity,
    Image,
    Animated,
    Dimensions,
    TouchableWithoutFeedback
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { ProductCard } from './ProductCard';
import { FilterBar } from './FilterBar';
import { Product, SortOption } from './types';
import { ProductDetailsScreen } from './ProductDetailsScreen';
import { CartScreen } from './CartScreen';
import { SideMenu } from './SideMenu';
import { useCart } from './CartContext';

const initialProducts: Product[] = [
    {
        id: '1',
        name: 'Silk Evening Gown',
        price: 2899.99,
        description: 'Exquisite silk evening gown with hand-embroidered crystals and a flowing silhouette. Features a delicate sweetheart neckline and an elegant train. Made in our Paris atelier with the finest Italian silk.',
        imageUrl: 'https://images.pexels.com/photos/12633692/pexels-photo-12633692.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        sizes: ['XS', 'S', 'M', 'L'],
        colors: ['#F8F0E3', '#000000', '#8B0000'],
        category: 'Dresses',
        isNew: true,
        isFavorite: false,
    },
    {
        id: '2',
        name: 'Cashmere Wrap Coat',
        price: 3499.99,
        description: 'Luxurious double-faced cashmere coat with a relaxed silhouette. Hand-finished edges and horn buttons. Made from the finest Mongolian cashmere, expertly tailored in our Milano atelier.',
        imageUrl: 'https://images.pexels.com/photos/6800942/pexels-photo-6800942.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        sizes: ['S', 'M', 'L', 'XL'],
        colors: ['#DEB887', '#1A1A1A', '#F5F5DC'],
        category: 'Outerwear',
        isNew: true,
        isFavorite: true,
    },
    {
        id: '3',
        name: 'Leather Palazzo Pants',
        price: 1899.99,
        description: 'High-waisted palazzo pants in butter-soft leather. Features a wide-leg silhouette and expert pleating. Handcrafted from the finest Italian leather with a silk lining.',
        imageUrl: 'https://images.pexels.com/photos/11559288/pexels-photo-11559288.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        sizes: ['S', 'M', 'L'],
        colors: ['#000000', '#8B4513', '#F5F5F5'],
        category: 'Pants',
        isNew: false,
        isFavorite: false,
    },
    {
        id: '4',
        name: 'Embellished Silk Blouse',
        price: 1299.99,
        description: 'Delicate silk crepe blouse with hand-sewn pearl embellishments. Features a high neck and flowing sleeves. Each pearl is individually selected and placed by our artisans.',
        imageUrl: 'https://images.pexels.com/photos/17879064/pexels-photo-17879064.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        sizes: ['XS', 'S', 'M', 'L'],
        colors: ['#FFFFFF', '#FFF5EE', '#000000'],
        category: 'Tops',
        isNew: true,
        isFavorite: false,
    },
    {
        id: '5',
        name: 'Structured Wool Blazer',
        price: 2499.99,
        description: 'Impeccably tailored wool blazer with silk lining. Features hand-stitched peaked lapels and genuine horn buttons. Made from the finest Italian wool in our signature cut.',
        imageUrl: 'https://images.pexels.com/photos/10651182/pexels-photo-10651182.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        sizes: ['S', 'M', 'L', 'XL'],
        colors: ['#000000', '#1A1A1A', '#2F4F4F'],
        category: 'Blazers',
        isNew: false,
        isFavorite: true,
    },
    {
        id: '6',
        name: 'Crystal Evening Clutch',
        price: 1799.99,
        description: 'Hand-embellished evening clutch with Swarovski crystals. Features a vintage-inspired clasp and Italian leather lining. Each piece takes over 40 hours to craft.',
        imageUrl: 'https://images.pexels.com/photos/1989164/pexels-photo-1989164.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        sizes: ['ONE SIZE'],
        colors: ['#FFD700', '#C0C0C0', '#000000'],
        category: 'Accessories',
        isNew: true,
        isFavorite: false,
    }
];

export const ProductListingScreen = () => {
    const [products, setProducts] = useState<Product[]>(initialProducts);
    const [selectedCategory, setSelectedCategory] = useState('All');
    const [sortOption, setSortOption] = useState<SortOption>('newest');
    const [refreshing, setRefreshing] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    const [isCartVisible, setIsCartVisible] = useState(false);
    const [isMenuVisible, setIsMenuVisible] = useState(false);
    const { totalItems } = useCart();

    const menuTranslateX = useRef(new Animated.Value(-Dimensions.get('window').width)).current;

    const toggleMenu = (show: boolean) => {
        Animated.spring(menuTranslateX, {
            toValue: show ? 0 : -Dimensions.get('window').width,
            useNativeDriver: true,
            damping: 20,
            mass: 1,
            stiffness: 100,
        }).start();
        setIsMenuVisible(show);
    };

    const handleFavoritePress = useCallback((productId: string) => {
        setProducts((prevProducts) =>
            prevProducts.map((product) =>
                product.id === productId
                    ? { ...product, isFavorite: !product.isFavorite }
                    : product
            )
        );
    }, []);

    const filteredAndSortedProducts = React.useMemo(() => {
        let result = [...products];

        if (selectedCategory !== 'All') {
            result = result.filter((product) => product.category === selectedCategory);
        }

        switch (sortOption) {
            case 'price-asc':
                result.sort((a, b) => a.price - b.price);
                break;
            case 'price-desc':
                result.sort((a, b) => b.price - a.price);
                break;
            case 'name-asc':
                result.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name-desc':
                result.sort((a, b) => b.name.localeCompare(a.name));
                break;
            case 'newest':
                result.sort((a, b) => (a.isNew === b.isNew ? 0 : a.isNew ? -1 : 1));
                break;
        }

        return result;
    }, [products, selectedCategory, sortOption]);

    const onRefresh = useCallback(() => {
        setRefreshing(true);
        setTimeout(() => {
            setRefreshing(false);
        }, 1500);
    }, []);

    return (
        <View style={styles.container}>
            <StatusBar style="dark" />

            {/* Overlay */}
            {isMenuVisible && (
                <TouchableWithoutFeedback onPress={() => toggleMenu(false)}>
                    <View style={styles.overlay} />
                </TouchableWithoutFeedback>
            )}

            {/* Side Menu */}
            <SideMenu
                isVisible={isMenuVisible}
                onClose={() => toggleMenu(false)}
                translateX={menuTranslateX}
            />

            <LinearGradient
                colors={['#f8f9fa', '#ffffff']}
                style={styles.header}
            >
                <View style={styles.logoContainer}>
                    <TouchableOpacity
                        style={styles.menuButton}
                        onPress={() => toggleMenu(true)}
                    >
                        <View style={styles.menuIcon}>
                            <View style={styles.menuLine} />
                            <View style={[styles.menuLine, styles.menuLineShort]} />
                            <View style={styles.menuLine} />
                        </View>
                    </TouchableOpacity>
                    <View style={styles.brandContainer}>
                        <Text style={styles.title}>MAISON</Text>
                        <Text style={styles.subtitle}>PARIS • MILANO • NEW YORK</Text>
                    </View>
                    <TouchableOpacity
                        style={styles.cartButton}
                        onPress={() => setIsCartVisible(true)}
                    >
                        <Text style={styles.cartIcon}>🛍️</Text>
                        {totalItems > 0 && (
                            <View style={styles.badge}>
                                <Text style={styles.badgeText}>{totalItems}</Text>
                            </View>
                        )}
                    </TouchableOpacity>
                </View>
                <View style={styles.seasonTag}>
                    <Text style={styles.seasonText}>FALL/WINTER 2024</Text>
                </View>
            </LinearGradient>

            <FilterBar
                selectedCategory={selectedCategory}
                onCategoryChange={setSelectedCategory}
                sortOption={sortOption}
                onSortChange={setSortOption}
                onFilterPress={() => console.log('Filter pressed')}
            />

            <FlatList
                data={filteredAndSortedProducts}
                renderItem={({ item }) => (
                    <ProductCard
                        {...item}
                        onPress={() => setSelectedProduct(item)}
                        onFavoritePress={() => handleFavoritePress(item.id)}
                    />
                )}
                keyExtractor={(item) => item.id}
                numColumns={2}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.listContent}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>No products found</Text>
                    </View>
                }
            />

            <Modal
                visible={selectedProduct !== null}
                animationType="slide"
                presentationStyle="fullScreen"
            >
                {selectedProduct && (
                    <ProductDetailsScreen
                        product={selectedProduct}
                        onClose={() => setSelectedProduct(null)}
                    />
                )}
            </Modal>

            <Modal
                visible={isCartVisible}
                animationType="slide"
                presentationStyle="fullScreen"
            >
                <CartScreen onClose={() => setIsCartVisible(false)} />
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.3)',
        zIndex: 999,
    },
    header: {
        paddingTop: 60,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    logoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    menuButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    menuIcon: {
        width: 24,
        height: 20,
        justifyContent: 'space-between',
    },
    menuLine: {
        width: '100%',
        height: 1,
        backgroundColor: '#1a1a1a',
    },
    menuLineShort: {
        width: '70%',
    },
    brandContainer: {
        alignItems: 'center',
    },
    title: {
        fontSize: 28,
        fontWeight: '300',
        color: '#1a1a1a',
        letterSpacing: 6,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 9,
        color: '#666',
        letterSpacing: 2,
        marginTop: 4,
        fontWeight: '500',
    },
    seasonTag: {
        alignItems: 'center',
        marginTop: 8,
    },
    seasonText: {
        fontSize: 11,
        color: '#888',
        letterSpacing: 1.5,
        fontWeight: '400',
    },
    cartButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderRadius: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },
    cartIcon: {
        fontSize: 20,
    },
    badge: {
        position: 'absolute',
        top: -5,
        right: -5,
        backgroundColor: '#1a1a1a',
        borderRadius: 10,
        minWidth: 20,
        height: 20,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 4,
    },
    badgeText: {
        color: '#fff',
        fontSize: 11,
        fontWeight: '600',
    },
    listContent: {
        padding: 8,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    emptyText: {
        fontSize: 16,
        color: '#666',
    },
});