import React, { useState } from 'react';
import {
    View,
    Text,
    ScrollView,
    StyleSheet,
    Dimensions,
    TouchableOpacity,
    Animated,
    Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Product } from './types';
import { useCart } from './CartContext';

const { width, height } = Dimensions.get('window');

interface ProductDetailsScreenProps {
    product: Product;
    onClose: () => void;
}

export const ProductDetailsScreen: React.FC<ProductDetailsScreenProps> = ({
                                                                              product,
                                                                              onClose,
                                                                          }) => {
    const [selectedSize, setSelectedSize] = useState<string>('');
    const [selectedColor, setSelectedColor] = useState<string>('');
    const buttonAnimation = new Animated.Value(0);
    const [isAdding, setIsAdding] = useState(false);
    const { addToCart } = useCart();

    const handleAddToCart = () => {
        if (!selectedSize || !selectedColor) return;

        setIsAdding(true);
        addToCart(product, selectedSize, selectedColor);

        Animated.sequence([
            Animated.timing(buttonAnimation, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.timing(buttonAnimation, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }),
        ]).start(() => {
            setTimeout(() => {
                setIsAdding(false);
                onClose();
            }, 500);
        });
    };

    const buttonScale = buttonAnimation.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [1, 0.9, 1],
    });

    const additionalImages = [
        'https://images.pexels.com/photos/6786894/pexels-photo-6786894.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        'https://images.pexels.com/photos/7870762/pexels-photo-7870762.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        'https://images.pexels.com/photos/6786894/pexels-photo-6786894.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        'https://images.pexels.com/photos/4046567/pexels-photo-4046567.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
    ];

    return (
        <View style={styles.container}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>

            <View style={styles.brandTag}>
                <Text style={styles.brandText}>MAISON</Text>
            </View>

            <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
                stickyHeaderIndices={[1]}
            >
                <View style={styles.gallery}>
                    <Image
                        source={{ uri: product.imageUrl }}
                        style={styles.mainImage}
                        resizeMode="cover"
                    />
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        style={styles.thumbnailScroll}
                    >
                        {additionalImages.map((image, index) => (
                            <Image
                                key={index}
                                source={{ uri: image }}
                                style={styles.thumbnail}
                                resizeMode="cover"
                            />
                        ))}
                    </ScrollView>
                </View>

                <LinearGradient
                    colors={['#fff', 'rgba(255,255,255,0.9)']}
                    style={styles.stickyHeader}
                >
                    <Text style={styles.productName}>{product.name}</Text>
                    <Text style={styles.price}>${product.price.toFixed(2)}</Text>
                </LinearGradient>

                <View style={styles.details}>
                    <Text style={styles.description}>{product.description}</Text>

                    <Text style={styles.sectionTitle}>Size</Text>
                    <View style={styles.sizesContainer}>
                        {product.sizes.map((size) => (
                            <TouchableOpacity
                                key={size}
                                style={[
                                    styles.sizeButton,
                                    selectedSize === size && styles.selectedOption,
                                ]}
                                onPress={() => setSelectedSize(size)}
                            >
                                <Text
                                    style={[
                                        styles.sizeButtonText,
                                        selectedSize === size && styles.selectedOptionText,
                                    ]}
                                >
                                    {size}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>

                    <Text style={styles.sectionTitle}>Color</Text>
                    <View style={styles.colorsContainer}>
                        {product.colors.map((color) => (
                            <TouchableOpacity
                                key={color}
                                style={[
                                    styles.colorButton,
                                    { backgroundColor: color },
                                    selectedColor === color && styles.selectedColorButton,
                                ]}
                                onPress={() => setSelectedColor(color)}
                            />
                        ))}
                    </View>

                    <View style={styles.spacer} />
                </View>
            </ScrollView>

            <LinearGradient
                colors={['rgba(255,255,255,0.9)', '#fff']}
                style={styles.bottomBar}
            >
                <Animated.View
                    style={[
                        styles.addToCartButton,
                        { transform: [{ scale: buttonScale }] },
                    ]}
                >
                    <TouchableOpacity
                        onPress={handleAddToCart}
                        disabled={!selectedSize || !selectedColor || isAdding}
                        style={[
                            styles.addToCartTouchable,
                            (!selectedSize || !selectedColor) && styles.disabledButton,
                        ]}
                    >
                        <Text style={styles.addToCartText}>
                            {isAdding ? 'Added!' : 'Add to Cart'}
                        </Text>
                    </TouchableOpacity>
                </Animated.View>
            </LinearGradient>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    closeButton: {
        position: 'absolute',
        top: 50,
        right: 20,
        zIndex: 10,
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    closeButtonText: {
        color: '#fff',
        fontSize: 24,
        lineHeight: 24,
    },
    brandTag: {
        position: 'absolute',
        top: 50,
        left: 20,
        zIndex: 10,
    },
    brandText: {
        fontSize: 16,
        fontWeight: '300',
        letterSpacing: 3,
        color: '#1a1a1a',
    },
    scrollView: {
        flex: 1,
    },
    gallery: {
        width: width,
    },
    mainImage: {
        width: width,
        height: height * 0.6,
    },
    thumbnailScroll: {
        padding: 8,
        backgroundColor: '#fff',
    },
    thumbnail: {
        width: 60,
        height: 80,
        marginRight: 8,
        borderRadius: 4,
    },
    stickyHeader: {
        padding: 16,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    productName: {
        fontSize: 20,
        fontWeight: '600',
        color: '#000',
    },
    price: {
        fontSize: 18,
        color: '#666',
        marginTop: 4,
    },
    details: {
        padding: 16,
    },
    description: {
        fontSize: 14,
        lineHeight: 22,
        color: '#444',
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#000',
        marginBottom: 12,
    },
    sizesContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
        marginBottom: 24,
    },
    sizeButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    selectedOption: {
        backgroundColor: '#000',
        borderColor: '#000',
    },
    sizeButtonText: {
        fontSize: 14,
        color: '#000',
    },
    selectedOptionText: {
        color: '#fff',
    },
    colorsContainer: {
        flexDirection: 'row',
        gap: 12,
        marginBottom: 24,
    },
    colorButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    selectedColorButton: {
        borderWidth: 2,
        borderColor: '#000',
    },
    spacer: {
        height: 100,
    },
    bottomBar: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: 16,
        paddingBottom: 32,
    },
    addToCartButton: {
        width: '100%',
    },
    addToCartTouchable: {
        backgroundColor: '#000',
        paddingVertical: 16,
        borderRadius: 30,
        alignItems: 'center',
    },
    disabledButton: {
        backgroundColor: '#ccc',
    },
    addToCartText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});