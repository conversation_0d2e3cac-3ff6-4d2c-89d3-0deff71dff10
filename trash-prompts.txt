
Can we make @Header.tsx double header as it two rows. One for project related settings and the breadcrumbs and the second for the chat related settings. Also, make both the headers compact enough to fit within 112px. Make it resemble an excel or google docs or figma header while having clean and efficient design. Use components from shadcn in @src/components/ui and icons from lucide-react if needed.

Make it very very compact and everything smaller but with clear labels as this is a new product and possibly tooltips. Connect everything to existing fuinctionality.

The same header is used in all the pages withing @src/app/(project)/projects. So add more menu items that make sense for the project.


Look at @apps/expo-go and @apps/eas-expo-go and other related folders to try to plan the following.

We are building an AI based mobile app generator. We want to release our own native apps similar to expo go so that people can preivew the apps that they are building on our app. We want it to connect to our database hosting the code but we currently utilize the expo snack on our web platform for live previews. We want the native app to connect to the same infra to be able to run the apps people are building in their accounts.

Plan in details exactly what and how can we solve this. Is there any other features we can add on top of this?


in the forked app, what will and what will not be present in terms of folders from the current @apps/expo-go app?

How to go about doing this quickly for an MVP in a single night?


pinpoint the exact screen, component, api, code that loads the expo app in to the expo-go?


I have added the @apps/magically-app folder which is an exact fork of the expo go currently. can you make the changes quickly and make it work with magically. Create service files for api requests so that I can just add the apis and change the routes while everything remaining the same.

