[{"id": "c9467bfb-bc33-4e08-bc8b-f0c26134ad9d", "chatId": "a7c579ff-b5df-4246-9684-5a6b83f7cd1f", "projectId": "9a862520-7f33-4594-ba2e-ed7f17a8efda", "name": "Welcome", "html": "<div class=\"flex flex-col h-screen bg-gray-50\">\n  <!-- Main Content -->\n  <div class=\"flex-1 flex flex-col items-center justify-center px-8\">\n    <!-- Logo Section -->\n    <div class=\"mb-12\">\n      <div class=\"w-32 h-32 bg-yellow-200 rounded-full flex items-center justify-center mb-6 shadow-lg\">\n        <i data-lucide=\"citrus\" class=\"w-20 h-20 text-yellow-600\"></i>\n      </div>\n      <h1 class=\"text-4xl font-bold text-gray-800 text-center mb-2\">LemonSqueezy</h1>\n      <p class=\"text-lg text-gray-600 text-center leading-relaxed\">Sell your digital products with ease</p>\n    </div>\n\n    <!-- Feature Highlights -->\n    <div class=\"w-full max-w-sm mb-12\">\n      <div class=\"flex items-center mb-4\">\n        <div class=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3\">\n          <i data-lucide=\"zap\" class=\"w-4 h-4 text-yellow-600\"></i>\n        </div>\n        <span class=\"text-gray-700\">Lightning fast setup</span>\n      </div>\n      <div class=\"flex items-center mb-4\">\n        <div class=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3\">\n          <i data-lucide=\"shield-check\" class=\"w-4 h-4 text-yellow-600\"></i>\n        </div>\n        <span class=\"text-gray-700\">Secure payments</span>\n      </div>\n      <div class=\"flex items-center\">\n        <div class=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3\">\n          <i data-lucide=\"trending-up\" class=\"w-4 h-4 text-yellow-600\"></i>\n        </div>\n        <span class=\"text-gray-700\">Real-time analytics</span>\n      </div>\n    </div>\n\n    <!-- Action Buttons -->\n    <div class=\"w-full max-w-sm space-y-4\">\n      <button class=\"w-full bg-yellow-300 text-gray-800 py-4 rounded-2xl font-semibold text-lg shadow-md transition-opacity active:opacity-70\">\n        Get Started\n      </button>\n      <button class=\"w-full border-2 border-gray-300 text-gray-700 py-4 rounded-2xl font-semibold text-lg transition-opacity active:opacity-70\">\n        I already have an account\n      </button>\n    </div>\n  </div>\n\n  <!-- Bottom Text -->\n  <div class=\"px-8 pb-12\">\n    <p class=\"text-center text-sm text-gray-500 leading-relaxed\">\n      By continuing, you agree to our Terms of Service and Privacy Policy\n    </p>\n  </div>\n</div>\n\n<script>\ndocument.querySelectorAll('button').forEach(button => {\n  button.addEventListener('click', function() {\n    this.style.transform = 'scale(0.98)';\n    setTimeout(() => {\n      this.style.transform = 'scale(1)';\n    }, 100);\n  });\n});\n</script>", "order": 1, "status": "complete", "createdAt": "2025-06-08T15:39:26.425Z", "updatedAt": "2025-06-08T15:40:15.052Z"}, {"id": "8d35b243-1121-4f92-9b93-e853d6bfca0f", "chatId": "a7c579ff-b5df-4246-9684-5a6b83f7cd1f", "projectId": "9a862520-7f33-4594-ba2e-ed7f17a8efda", "name": "Home Dashboard", "html": "<div class=\"flex flex-col h-full bg-gray-50 overflow-x-hidden\">\n  <!-- Header -->\n  <div class=\"bg-white px-4 py-6 shadow-sm\">\n    <div class=\"flex items-center justify-between mb-4\">\n      <div class=\"flex items-center gap-3\">\n        <div class=\"w-8 h-8 bg-yellow-300 rounded-full flex items-center justify-center\">\n          <i data-lucide=\"citrus\" class=\"w-5 h-5 text-yellow-700\"></i>\n        </div>\n        <span class=\"text-xl font-semibold text-gray-800\">LemonSqueezy</span>\n      </div>\n      <i data-lucide=\"bell\" class=\"w-6 h-6 text-gray-600\"></i>\n    </div>\n    \n    <!-- Search Bar -->\n    <div class=\"relative\">\n      <i data-lucide=\"search\" class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"></i>\n      <div class=\"bg-gray-100 rounded-xl pl-10 pr-4 py-3 text-gray-600\">\n        Search products, customers...\n      </div>\n    </div>\n  </div>\n\n  <!-- Scrollable Content -->\n  <div class=\"flex-1 px-4 py-6 pb-24 overflow-y-auto\">\n    <!-- Greeting -->\n    <div class=\"mb-6\">\n      <h1 class=\"text-2xl font-bold text-gray-800 mb-1\">Good morning, Alex</h1>\n      <p class=\"text-gray-600\">Here's what's happening with your store today</p>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-2 gap-4 mb-6\">\n      <div class=\"bg-white rounded-2xl p-4 shadow-sm border border-gray-100\">\n        <div class=\"flex items-center gap-3 mb-2\">\n          <div class=\"w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center\">\n            <i data-lucide=\"dollar-sign\" class=\"w-4 h-4 text-yellow-600\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-600\">Revenue</span>\n        </div>\n        <p class=\"text-2xl font-bold text-gray-800\">$2,847</p>\n        <p class=\"text-xs text-green-600 mt-1\">+12% from last week</p>\n      </div>\n\n      <div class=\"bg-white rounded-2xl p-4 shadow-sm border border-gray-100\">\n        <div class=\"flex items-center gap-3 mb-2\">\n          <div class=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n            <i data-lucide=\"shopping-cart\" class=\"w-4 h-4 text-blue-600\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-600\">Sales</span>\n        </div>\n        <p class=\"text-2xl font-bold text-gray-800\">127</p>\n        <p class=\"text-xs text-green-600 mt-1\">+8% from last week</p>\n      </div>\n\n      <div class=\"bg-white rounded-2xl p-4 shadow-sm border border-gray-100\">\n        <div class=\"flex items-center gap-3 mb-2\">\n          <div class=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center\">\n            <i data-lucide=\"users\" class=\"w-4 h-4 text-purple-600\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-600\">Customers</span>\n        </div>\n        <p class=\"text-2xl font-bold text-gray-800\">89</p>\n        <p class=\"text-xs text-green-600 mt-1\">+5% from last week</p>\n      </div>\n\n      <div class=\"bg-white rounded-2xl p-4 shadow-sm border border-gray-100\">\n        <div class=\"flex items-center gap-3 mb-2\">\n          <div class=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center\">\n            <i data-lucide=\"trending-up\" class=\"w-4 h-4 text-green-600\"></i>\n          </div>\n          <span class=\"text-sm font-medium text-gray-600\">Conversion</span>\n        </div>\n        <p class=\"text-2xl font-bold text-gray-800\">3.2%</p>\n        <p class=\"text-xs text-green-600 mt-1\">+0.5% from last week</p>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold text-gray-800 mb-4\">Quick Actions</h2>\n      <div class=\"grid grid-cols-2 gap-3\">\n        <div class=\"bg-yellow-50 rounded-2xl p-4 border border-yellow-100 active:opacity-70 transition-opacity\" id=\"addProduct\">\n          <div class=\"w-10 h-10 bg-yellow-200 rounded-xl flex items-center justify-center mb-3\">\n            <i data-lucide=\"plus\" class=\"w-5 h-5 text-yellow-700\"></i>\n          </div>\n          <h3 class=\"font-semibold text-gray-800 mb-1\">Add Product</h3>\n          <p class=\"text-sm text-gray-600\">Create a new digital product</p>\n        </div>\n\n        <div class=\"bg-blue-50 rounded-2xl p-4 border border-blue-100 active:opacity-70 transition-opacity\" id=\"viewOrders\">\n          <div class=\"w-10 h-10 bg-blue-200 rounded-xl flex items-center justify-center mb-3\">\n            <i data-lucide=\"package\" class=\"w-5 h-5 text-blue-700\"></i>\n          </div>\n          <h3 class=\"font-semibold text-gray-800 mb-1\">View Orders</h3>\n          <p class=\"text-sm text-gray-600\">Manage recent orders</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Recent Activity -->\n    <div class=\"mb-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h2 class=\"text-lg font-semibold text-gray-800\">Recent Activity</h2>\n        <span class=\"text-sm text-gray-600\">View all</span>\n      </div>\n      \n      <div class=\"space-y-3\">\n        <div class=\"bg-white rounded-2xl p-4 shadow-sm border border-gray-100\">\n          <div class=\"flex items-center gap-3\">\n            <div class=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n              <i data-lucide=\"check\" class=\"w-5 h-5 text-green-600\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <p class=\"font-medium text-gray-800\">New sale: UI Kit Bundle</p>\n              <p class=\"text-sm text-gray-600\">$49.00 • 2 minutes ago</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-2xl p-4 shadow-sm border border-gray-100\">\n          <div class=\"flex items-center gap-3\">\n            <div class=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n              <i data-lucide=\"user-plus\" class=\"w-5 h-5 text-blue-600\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <p class=\"font-medium text-gray-800\">New customer registered</p>\n              <p class=\"text-sm text-gray-600\"><EMAIL> • 1 hour ago</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-2xl p-4 shadow-sm border border-gray-100\">\n          <div class=\"flex items-center gap-3\">\n            <div class=\"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center\">\n              <i data-lucide=\"star\" class=\"w-5 h-5 text-yellow-600\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <p class=\"font-medium text-gray-800\">5-star review received</p>\n              <p class=\"text-sm text-gray-600\">Mobile App Template • 3 hours ago</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Bottom Navigation -->\n  <div class=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3\">\n    <div class=\"flex items-center justify-around\">\n      <div class=\"flex flex-col items-center gap-1 active:opacity-70 transition-opacity\" id=\"homeTab\">\n        <div class=\"w-6 h-6 bg-yellow-300 rounded-lg flex items-center justify-center\">\n          <i data-lucide=\"home\" class=\"w-4 h-4 text-yellow-700\"></i>\n        </div>\n        <span class=\"text-xs font-medium text-yellow-700\">Home</span>\n      </div>\n\n      <div class=\"flex flex-col items-center gap-1 active:opacity-70 transition-opacity\" id=\"storeTab\">\n        <i data-lucide=\"store\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-500\">Store</span>\n      </div>\n\n      <div class=\"flex flex-col items-center gap-1 active:opacity-70 transition-opacity\" id=\"analyticsTab\">\n        <i data-lucide=\"bar-chart-3\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-500\">Analytics</span>\n      </div>\n\n      <div class=\"flex flex-col items-center gap-1 active:opacity-70 transition-opacity\" id=\"profileTab\">\n        <i data-lucide=\"user\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-500\">Profile</span>\n      </div>\n    </div>\n  </div>\n</div>\n\n<script>\ndocument.getElementById('addProduct').addEventListener('click', function() {\n  this.style.transform = 'scale(0.98)';\n  setTimeout(() => {\n    this.style.transform = 'scale(1)';\n  }, 150);\n});\n\ndocument.getElementById('viewOrders').addEventListener('click', function() {\n  this.style.transform = 'scale(0.98)';\n  setTimeout(() => {\n    this.style.transform = 'scale(1)';\n  }, 150);\n});\n\n['homeTab', 'storeTab', 'analyticsTab', 'profileTab'].forEach(id => {\n  document.getElementById(id).addEventListener('click', function() {\n    this.style.transform = 'scale(0.95)';\n    setTimeout(() => {\n      this.style.transform = 'scale(1)';\n    }, 150);\n  });\n});\n</script>", "order": 2, "status": "complete", "createdAt": "2025-06-08T15:39:26.426Z", "updatedAt": "2025-06-08T15:40:17.165Z"}, {"id": "19b8545d-25eb-4ce0-b9f4-85cec48619ca", "chatId": "a7c579ff-b5df-4246-9684-5a6b83f7cd1f", "projectId": "9a862520-7f33-4594-ba2e-ed7f17a8efda", "name": "Product Store", "html": "<div class=\"flex flex-col h-full bg-gray-50\">\n    <!-- Header -->\n    <div class=\"bg-white px-4 py-4 border-b border-gray-200\">\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center gap-2\">\n          <div class=\"w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center\">\n            <i data-lucide=\"zap\" class=\"w-5 h-5 text-yellow-600\"></i>\n          </div>\n          <span class=\"text-xl font-bold text-slate-800\">LemonSqueezy</span>\n        </div>\n        <div class=\"flex items-center gap-3\">\n          <i data-lucide=\"search\" class=\"w-6 h-6 text-slate-600\"></i>\n          <i data-lucide=\"shopping-cart\" class=\"w-6 h-6 text-slate-600\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Search Bar -->\n    <div class=\"px-4 py-3 bg-white border-b border-gray-100\">\n      <div class=\"bg-gray-100 rounded-xl px-4 py-3 flex items-center gap-3\">\n        <i data-lucide=\"search\" class=\"w-5 h-5 text-gray-500\"></i>\n        <span class=\"text-gray-500 flex-1\">Search digital products...</span>\n      </div>\n    </div>\n\n    <!-- Main Content -->\n    <div class=\"flex-1 overflow-y-auto pb-20\">\n      <!-- Category Filters -->\n      <div class=\"px-4 py-4 bg-white\">\n        <div class=\"flex gap-2 overflow-x-auto\">\n          <div class=\"bg-yellow-200 px-4 py-2 rounded-full min-w-fit\">\n            <span class=\"text-slate-800 font-medium text-sm\">All</span>\n          </div>\n          <div class=\"bg-gray-100 px-4 py-2 rounded-full min-w-fit\">\n            <span class=\"text-slate-600 font-medium text-sm\">Templates</span>\n          </div>\n          <div class=\"bg-gray-100 px-4 py-2 rounded-full min-w-fit\">\n            <span class=\"text-slate-600 font-medium text-sm\">Courses</span>\n          </div>\n          <div class=\"bg-gray-100 px-4 py-2 rounded-full min-w-fit\">\n            <span class=\"text-slate-600 font-medium text-sm\">Software</span>\n          </div>\n          <div class=\"bg-gray-100 px-4 py-2 rounded-full min-w-fit\">\n            <span class=\"text-slate-600 font-medium text-sm\">eBooks</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Featured Products -->\n      <div class=\"px-4 py-4\">\n        <h2 class=\"text-xl font-bold text-slate-800 mb-4\">Featured Products</h2>\n        <div class=\"bg-white rounded-2xl p-4 mb-6 shadow-sm border border-gray-100\">\n          <img src=\"https://magically.life/api/media/image?query=modern%20digital%20design%20template%20mockup\" class=\"w-full h-48 object-cover rounded-xl mb-4\" alt=\"Featured Product\">\n          <div class=\"flex items-start justify-between mb-2\">\n            <h3 class=\"text-lg font-semibold text-slate-800 flex-1\">Premium UI Kit Bundle</h3>\n            <div class=\"flex items-center gap-1\">\n              <i data-lucide=\"star\" class=\"w-4 h-4 text-yellow-500 fill-current\"></i>\n              <span class=\"text-sm text-slate-600\">4.9</span>\n            </div>\n          </div>\n          <p class=\"text-slate-600 text-sm mb-3\">Complete design system with 200+ components</p>\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-2xl font-bold text-slate-800\">$49</span>\n            <div class=\"bg-yellow-200 px-6 py-2 rounded-xl\">\n              <span class=\"text-slate-800 font-medium\">Add to Cart</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Product Grid -->\n      <div class=\"px-4\">\n        <h2 class=\"text-xl font-bold text-slate-800 mb-4\">All Products</h2>\n        <div class=\"grid grid-cols-2 gap-4\">\n          <!-- Product 1 -->\n          <div class=\"bg-white rounded-xl p-3 shadow-sm border border-gray-100\">\n            <img src=\"https://magically.life/api/media/image?query=react%20course%20thumbnail%20programming\" class=\"w-full h-32 object-cover rounded-lg mb-3\" alt=\"React Course\">\n            <h3 class=\"font-semibold text-slate-800 text-sm mb-1\">React Mastery Course</h3>\n            <div class=\"flex items-center gap-1 mb-2\">\n              <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-500 fill-current\"></i>\n              <span class=\"text-xs text-slate-600\">4.8</span>\n            </div>\n            <span class=\"text-lg font-bold text-slate-800\">$89</span>\n          </div>\n\n          <!-- Product 2 -->\n          <div class=\"bg-white rounded-xl p-3 shadow-sm border border-gray-100\">\n            <img src=\"https://magically.life/api/media/image?query=mobile%20app%20template%20design%20mockup\" class=\"w-full h-32 object-cover rounded-lg mb-3\" alt=\"App Template\">\n            <h3 class=\"font-semibold text-slate-800 text-sm mb-1\">Mobile App Template</h3>\n            <div class=\"flex items-center gap-1 mb-2\">\n              <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-500 fill-current\"></i>\n              <span class=\"text-xs text-slate-600\">4.7</span>\n            </div>\n            <span class=\"text-lg font-bold text-slate-800\">$29</span>\n          </div>\n\n          <!-- Product 3 -->\n          <div class=\"bg-white rounded-xl p-3 shadow-sm border border-gray-100\">\n            <img src=\"https://magically.life/api/media/image?query=business%20ebook%20cover%20professional\" class=\"w-full h-32 object-cover rounded-lg mb-3\" alt=\"Business eBook\">\n            <h3 class=\"font-semibold text-slate-800 text-sm mb-1\">Business Growth eBook</h3>\n            <div class=\"flex items-center gap-1 mb-2\">\n              <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-500 fill-current\"></i>\n              <span class=\"text-xs text-slate-600\">4.6</span>\n            </div>\n            <span class=\"text-lg font-bold text-slate-800\">$19</span>\n          </div>\n\n          <!-- Product 4 -->\n          <div class=\"bg-white rounded-xl p-3 shadow-sm border border-gray-100\">\n            <img src=\"https://magically.life/api/media/image?query=photography%20presets%20lightroom%20filters\" class=\"w-full h-32 object-cover rounded-lg mb-3\" alt=\"Photo Presets\">\n            <h3 class=\"font-semibold text-slate-800 text-sm mb-1\">Photo Presets Pack</h3>\n            <div class=\"flex items-center gap-1 mb-2\">\n              <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-500 fill-current\"></i>\n              <span class=\"text-xs text-slate-600\">4.9</span>\n            </div>\n            <span class=\"text-lg font-bold text-slate-800\">$15</span>\n          </div>\n\n          <!-- Product 5 -->\n          <div class=\"bg-white rounded-xl p-3 shadow-sm border border-gray-100\">\n            <img src=\"https://magically.life/api/media/image?query=web%20development%20software%20tool\" class=\"w-full h-32 object-cover rounded-lg mb-3\" alt=\"Dev Tool\">\n            <h3 class=\"font-semibold text-slate-800 text-sm mb-1\">Dev Tools Suite</h3>\n            <div class=\"flex items-center gap-1 mb-2\">\n              <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-500 fill-current\"></i>\n              <span class=\"text-xs text-slate-600\">4.5</span>\n            </div>\n            <span class=\"text-lg font-bold text-slate-800\">$79</span>\n          </div>\n\n          <!-- Product 6 -->\n          <div class=\"bg-white rounded-xl p-3 shadow-sm border border-gray-100\">\n            <img src=\"https://magically.life/api/media/image?query=marketing%20template%20social%20media\" class=\"w-full h-32 object-cover rounded-lg mb-3\" alt=\"Marketing Templates\">\n            <h3 class=\"font-semibold text-slate-800 text-sm mb-1\">Marketing Templates</h3>\n            <div class=\"flex items-center gap-1 mb-2\">\n              <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-500 fill-current\"></i>\n              <span class=\"text-xs text-slate-600\">4.8</span>\n            </div>\n            <span class=\"text-lg font-bold text-slate-800\">$25</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Bottom Navigation -->\n    <div class=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2\">\n      <div class=\"flex justify-around items-center\">\n        <div class=\"flex flex-col items-center py-2\">\n          <i data-lucide=\"home\" class=\"w-6 h-6 text-gray-400\"></i>\n          <span class=\"text-xs text-gray-400 mt-1\">Home</span>\n        </div>\n        <div class=\"flex flex-col items-center py-2\">\n          <i data-lucide=\"store\" class=\"w-6 h-6 text-yellow-600\"></i>\n          <span class=\"text-xs text-yellow-600 mt-1 font-medium\">Store</span>\n        </div>\n        <div class=\"flex flex-col items-center py-2\">\n          <i data-lucide=\"bar-chart-3\" class=\"w-6 h-6 text-gray-400\"></i>\n          <span class=\"text-xs text-gray-400 mt-1\">Analytics</span>\n        </div>\n        <div class=\"flex flex-col items-center py-2\">\n          <i data-lucide=\"user\" class=\"w-6 h-6 text-gray-400\"></i>\n          <span class=\"text-xs text-gray-400 mt-1\">Profile</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <script>\n    document.querySelectorAll('.bg-yellow-200, .bg-gray-100').forEach(button => {\n      button.addEventListener('click', function() {\n        this.style.opacity = '0.7';\n        setTimeout(() => {\n          this.style.opacity = '1';\n        }, 100);\n      });\n    });\n\n    document.querySelectorAll('.bg-white').forEach(card => {\n      card.addEventListener('click', function() {\n        this.style.transform = 'scale(0.98)';\n        setTimeout(() => {\n          this.style.transform = 'scale(1)';\n        }, 100);\n      });\n    });\n  </script>", "order": 3, "status": "complete", "createdAt": "2025-06-08T15:39:26.426Z", "updatedAt": "2025-06-08T15:40:15.665Z"}, {"id": "7ec67f23-fa8e-4c39-9e17-8f2d85ab0ea1", "chatId": "a7c579ff-b5df-4246-9684-5a6b83f7cd1f", "projectId": "9a862520-7f33-4594-ba2e-ed7f17a8efda", "name": "Product Details", "html": "<div class=\"flex flex-col bg-gray-50 min-h-screen\">\n  <!-- Header -->\n  <div class=\"bg-white px-4 py-3 flex items-center justify-between border-b border-gray-100\">\n    <div class=\"w-8 h-8 flex items-center justify-center\">\n      <i data-lucide=\"arrow-left\" class=\"w-6 h-6 text-gray-600\"></i>\n    </div>\n    <div class=\"flex items-center gap-2\">\n      <div class=\"w-6 h-6 bg-yellow-300 rounded-full flex items-center justify-center\">\n        <i data-lucide=\"lemon\" class=\"w-4 h-4 text-yellow-800\"></i>\n      </div>\n      <span class=\"text-lg font-semibold text-gray-800\">LemonSqueezy</span>\n    </div>\n    <div class=\"w-8 h-8 flex items-center justify-center\">\n      <i data-lucide=\"share\" class=\"w-6 h-6 text-gray-600\"></i>\n    </div>\n  </div>\n\n  <!-- Scrollable Content -->\n  <div class=\"flex-1 overflow-hidden\">\n    <div class=\"h-full overflow-y-auto pb-24\">\n      <!-- Product Image -->\n      <div class=\"bg-white p-4\">\n        <div class=\"w-full h-64 bg-gray-100 rounded-xl overflow-hidden\">\n          <img \n            src=\"https://magically.life/api/media/image?query=modern%20digital%20design%20course%20thumbnail%20professional%20clean\"\n            alt=\"Product preview\"\n            class=\"w-full h-full object-cover\"\n          />\n        </div>\n      </div>\n\n      <!-- Product Info -->\n      <div class=\"bg-white mx-4 rounded-xl p-4 mb-4\">\n        <div class=\"flex items-start justify-between mb-3\">\n          <div class=\"flex-1\">\n            <h1 class=\"text-xl font-bold text-gray-800 mb-1\">Complete UI/UX Design Course</h1>\n            <p class=\"text-sm text-gray-500\">Digital Course</p>\n          </div>\n          <div class=\"text-right\">\n            <div class=\"text-2xl font-bold text-gray-800\">$79</div>\n            <div class=\"text-sm text-gray-400 line-through\">$129</div>\n          </div>\n        </div>\n        \n        <!-- Rating -->\n        <div class=\"flex items-center gap-2 mb-4\">\n          <div class=\"flex items-center\">\n            <i data-lucide=\"star\" class=\"w-4 h-4 text-yellow-400 fill-current\"></i>\n            <i data-lucide=\"star\" class=\"w-4 h-4 text-yellow-400 fill-current\"></i>\n            <i data-lucide=\"star\" class=\"w-4 h-4 text-yellow-400 fill-current\"></i>\n            <i data-lucide=\"star\" class=\"w-4 h-4 text-yellow-400 fill-current\"></i>\n            <i data-lucide=\"star\" class=\"w-4 h-4 text-gray-300\"></i>\n          </div>\n          <span class=\"text-sm text-gray-600\">4.8 (124 reviews)</span>\n        </div>\n\n        <!-- Description -->\n        <div class=\"mb-4\">\n          <h3 class=\"text-base font-semibold text-gray-800 mb-2\">Description</h3>\n          <p class=\"text-sm text-gray-600 leading-relaxed\">\n            Master the fundamentals of UI/UX design with this comprehensive course. Learn design thinking, prototyping, user research, and create stunning interfaces that users love.\n          </p>\n        </div>\n\n        <!-- What's Included -->\n        <div class=\"mb-4\">\n          <h3 class=\"text-base font-semibold text-gray-800 mb-3\">What's Included</h3>\n          <div class=\"space-y-2\">\n            <div class=\"flex items-center gap-3\">\n              <i data-lucide=\"play-circle\" class=\"w-4 h-4 text-gray-500\"></i>\n              <span class=\"text-sm text-gray-600\">12 hours of video content</span>\n            </div>\n            <div class=\"flex items-center gap-3\">\n              <i data-lucide=\"file-text\" class=\"w-4 h-4 text-gray-500\"></i>\n              <span class=\"text-sm text-gray-600\">Design templates & resources</span>\n            </div>\n            <div class=\"flex items-center gap-3\">\n              <i data-lucide=\"award\" class=\"w-4 h-4 text-gray-500\"></i>\n              <span class=\"text-sm text-gray-600\">Certificate of completion</span>\n            </div>\n            <div class=\"flex items-center gap-3\">\n              <i data-lucide=\"infinity\" class=\"w-4 h-4 text-gray-500\"></i>\n              <span class=\"text-sm text-gray-600\">Lifetime access</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Seller Info -->\n      <div class=\"bg-white mx-4 rounded-xl p-4 mb-4\">\n        <h3 class=\"text-base font-semibold text-gray-800 mb-3\">About the Creator</h3>\n        <div class=\"flex items-center gap-3\">\n          <div class=\"w-12 h-12 bg-gray-200 rounded-full overflow-hidden\">\n            <img \n              src=\"https://magically.life/api/media/image?query=professional%20designer%20portrait%20friendly%20modern\"\n              alt=\"Creator\"\n              class=\"w-full h-full object-cover\"\n            />\n          </div>\n          <div class=\"flex-1\">\n            <div class=\"font-medium text-gray-800\">Sarah Johnson</div>\n            <div class=\"text-sm text-gray-500\">Senior UI/UX Designer at Google</div>\n          </div>\n          <div class=\"text-sm text-gray-400\">\n            <i data-lucide=\"users\" class=\"w-4 h-4 inline mr-1\"></i>\n            2.3k students\n          </div>\n        </div>\n      </div>\n\n      <!-- Reviews -->\n      <div class=\"bg-white mx-4 rounded-xl p-4 mb-4\">\n        <div class=\"flex items-center justify-between mb-3\">\n          <h3 class=\"text-base font-semibold text-gray-800\">Reviews</h3>\n          <button class=\"text-sm text-blue-600 font-medium\">See all</button>\n        </div>\n        \n        <div class=\"space-y-4\">\n          <div class=\"border-b border-gray-100 pb-4\">\n            <div class=\"flex items-start gap-3\">\n              <div class=\"w-8 h-8 bg-gray-200 rounded-full\"></div>\n              <div class=\"flex-1\">\n                <div class=\"flex items-center gap-2 mb-1\">\n                  <span class=\"font-medium text-gray-800 text-sm\">Mike Chen</span>\n                  <div class=\"flex\">\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                  </div>\n                </div>\n                <p class=\"text-sm text-gray-600\">Excellent course! Very detailed and practical. The templates are super useful.</p>\n              </div>\n            </div>\n          </div>\n          \n          <div>\n            <div class=\"flex items-start gap-3\">\n              <div class=\"w-8 h-8 bg-gray-200 rounded-full\"></div>\n              <div class=\"flex-1\">\n                <div class=\"flex items-center gap-2 mb-1\">\n                  <span class=\"font-medium text-gray-800 text-sm\">Emma Davis</span>\n                  <div class=\"flex\">\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-yellow-400 fill-current\"></i>\n                    <i data-lucide=\"star\" class=\"w-3 h-3 text-gray-300\"></i>\n                  </div>\n                </div>\n                <p class=\"text-sm text-gray-600\">Great content and well structured. Perfect for beginners.</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Related Products -->\n      <div class=\"bg-white mx-4 rounded-xl p-4 mb-4\">\n        <h3 class=\"text-base font-semibold text-gray-800 mb-3\">You might also like</h3>\n        <div class=\"flex gap-3 overflow-x-auto\">\n          <div class=\"flex-shrink-0 w-32\">\n            <div class=\"w-full h-20 bg-gray-100 rounded-lg mb-2 overflow-hidden\">\n              <img \n                src=\"https://magically.life/api/media/image?query=web%20development%20course%20coding%20tutorial\"\n                alt=\"Related product\"\n                class=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div class=\"text-xs font-medium text-gray-800\">Web Dev Bootcamp</div>\n            <div class=\"text-xs text-gray-500\">$99</div>\n          </div>\n          \n          <div class=\"flex-shrink-0 w-32\">\n            <div class=\"w-full h-20 bg-gray-100 rounded-lg mb-2 overflow-hidden\">\n              <img \n                src=\"https://magically.life/api/media/image?query=mobile%20app%20design%20course%20smartphone%20ui\"\n                alt=\"Related product\"\n                class=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div class=\"text-xs font-medium text-gray-800\">Mobile Design</div>\n            <div class=\"text-xs text-gray-500\">$69</div>\n          </div>\n          \n          <div class=\"flex-shrink-0 w-32\">\n            <div class=\"w-full h-20 bg-gray-100 rounded-lg mb-2 overflow-hidden\">\n              <img \n                src=\"https://magically.life/api/media/image?query=graphic%20design%20course%20creative%20portfolio\"\n                alt=\"Related product\"\n                class=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div class=\"text-xs font-medium text-gray-800\">Graphic Design</div>\n            <div class=\"text-xs text-gray-500\">$89</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Purchase Button -->\n  <div class=\"fixed bottom-16 left-0 right-0 bg-white border-t border-gray-100 p-4\">\n    <button class=\"w-full bg-yellow-300 text-gray-800 font-semibold py-4 rounded-xl active:opacity-80 transition-opacity\">\n      Purchase for $79\n    </button>\n  </div>\n\n  <!-- Bottom Navigation -->\n  <div class=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100\">\n    <div class=\"flex items-center\">\n      <button class=\"flex-1 flex flex-col items-center py-2 px-4 active:opacity-60 transition-opacity\">\n        <i data-lucide=\"home\" class=\"w-6 h-6 text-gray-400 mb-1\"></i>\n        <span class=\"text-xs text-gray-400\">Home</span>\n      </button>\n      <button class=\"flex-1 flex flex-col items-center py-2 px-4 active:opacity-60 transition-opacity\">\n        <i data-lucide=\"shopping-bag\" class=\"w-6 h-6 text-gray-800 mb-1\"></i>\n        <span class=\"text-xs text-gray-800 font-medium\">Store</span>\n      </button>\n      <button class=\"flex-1 flex flex-col items-center py-2 px-4 active:opacity-60 transition-opacity\">\n        <i data-lucide=\"bar-chart-3\" class=\"w-6 h-6 text-gray-400 mb-1\"></i>\n        <span class=\"text-xs text-gray-400\">Analytics</span>\n      </button>\n      <button class=\"flex-1 flex flex-col items-center py-2 px-4 active:opacity-60 transition-opacity\">\n        <i data-lucide=\"user\" class=\"w-6 h-6 text-gray-400 mb-1\"></i>\n        <span class=\"text-xs text-gray-400\">Profile</span>\n      </button>\n    </div>\n  </div>\n</div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n  const buttons = document.querySelectorAll('button');\n  buttons.forEach(button => {\n    button.addEventListener('click', function(e) {\n      e.preventDefault();\n      this.style.opacity = '0.6';\n      setTimeout(() => {\n        this.style.opacity = '';\n      }, 150);\n    });\n  });\n});\n</script>", "order": 4, "status": "complete", "createdAt": "2025-06-08T15:39:26.426Z", "updatedAt": "2025-06-08T15:40:18.700Z"}, {"id": "39bbb4d8-8872-4a14-92fe-899474b51970", "chatId": "a7c579ff-b5df-4246-9684-5a6b83f7cd1f", "projectId": "9a862520-7f33-4594-ba2e-ed7f17a8efda", "name": "Analytics", "html": "<div class=\"flex-1 bg-gray-50 pb-20\">\n  <!-- Header -->\n  <div class=\"bg-white border-b border-gray-100 px-4 py-4\">\n    <div class=\"flex items-center justify-between\">\n      <div class=\"flex items-center space-x-3\">\n        <div class=\"w-8 h-8 bg-yellow-300 rounded-full flex items-center justify-center\">\n          <i data-lucide=\"lemon\" class=\"w-5 h-5 text-yellow-700\"></i>\n        </div>\n        <h1 class=\"text-xl font-semibold text-gray-900\">Analytics</h1>\n      </div>\n      <div class=\"flex items-center space-x-2\">\n        <button class=\"p-2 rounded-lg bg-gray-50 active:opacity-70 transition-opacity\">\n          <i data-lucide=\"calendar\" class=\"w-5 h-5 text-gray-600\"></i>\n        </button>\n        <button class=\"p-2 rounded-lg bg-gray-50 active:opacity-70 transition-opacity\">\n          <i data-lucide=\"download\" class=\"w-5 h-5 text-gray-600\"></i>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Content -->\n  <div class=\"flex-1 overflow-y-auto\">\n    <!-- Date Range Selector -->\n    <div class=\"px-4 py-4 bg-white border-b border-gray-100\">\n      <div class=\"flex items-center justify-between\">\n        <button class=\"flex items-center space-x-2 px-4 py-2 bg-gray-50 rounded-xl active:opacity-70 transition-opacity\">\n          <i data-lucide=\"calendar-days\" class=\"w-4 h-4 text-gray-600\"></i>\n          <span class=\"text-sm font-medium text-gray-700\">Last 30 days</span>\n          <i data-lucide=\"chevron-down\" class=\"w-4 h-4 text-gray-400\"></i>\n        </button>\n        <div class=\"flex items-center space-x-2\">\n          <span class=\"text-xs text-gray-500\">vs previous period</span>\n          <span class=\"text-xs font-medium text-green-600\">+12.5%</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Key Metrics -->\n    <div class=\"px-4 py-6 bg-white\">\n      <div class=\"grid grid-cols-2 gap-4\">\n        <div class=\"bg-gray-50 rounded-2xl p-4\">\n          <div class=\"flex items-center justify-between mb-2\">\n            <span class=\"text-sm text-gray-600\">Total Revenue</span>\n            <i data-lucide=\"trending-up\" class=\"w-4 h-4 text-green-500\"></i>\n          </div>\n          <div class=\"text-2xl font-bold text-gray-900\">$24,680</div>\n          <div class=\"text-xs text-green-600 mt-1\">+18.2% from last month</div>\n        </div>\n        \n        <div class=\"bg-gray-50 rounded-2xl p-4\">\n          <div class=\"flex items-center justify-between mb-2\">\n            <span class=\"text-sm text-gray-600\">Orders</span>\n            <i data-lucide=\"shopping-cart\" class=\"w-4 h-4 text-blue-500\"></i>\n          </div>\n          <div class=\"text-2xl font-bold text-gray-900\">1,247</div>\n          <div class=\"text-xs text-green-600 mt-1\">+8.7% from last month</div>\n        </div>\n        \n        <div class=\"bg-gray-50 rounded-2xl p-4\">\n          <div class=\"flex items-center justify-between mb-2\">\n            <span class=\"text-sm text-gray-600\">Customers</span>\n            <i data-lucide=\"users\" class=\"w-4 h-4 text-purple-500\"></i>\n          </div>\n          <div class=\"text-2xl font-bold text-gray-900\">892</div>\n          <div class=\"text-xs text-green-600 mt-1\">+15.3% from last month</div>\n        </div>\n        \n        <div class=\"bg-gray-50 rounded-2xl p-4\">\n          <div class=\"flex items-center justify-between mb-2\">\n            <span class=\"text-sm text-gray-600\">Avg. Order</span>\n            <i data-lucide=\"dollar-sign\" class=\"w-4 h-4 text-yellow-500\"></i>\n          </div>\n          <div class=\"text-2xl font-bold text-gray-900\">$19.78</div>\n          <div class=\"text-xs text-red-500 mt-1\">-2.1% from last month</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Revenue Chart -->\n    <div class=\"mx-4 my-6 bg-white rounded-2xl p-4 border border-gray-100\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-semibold text-gray-900\">Revenue Trend</h3>\n        <div class=\"flex items-center space-x-2\">\n          <button class=\"px-3 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-lg active:opacity-70 transition-opacity\">Daily</button>\n          <button class=\"px-3 py-1 text-xs font-medium text-gray-500 rounded-lg active:opacity-70 transition-opacity\">Weekly</button>\n          <button class=\"px-3 py-1 text-xs font-medium text-gray-500 rounded-lg active:opacity-70 transition-opacity\">Monthly</button>\n        </div>\n      </div>\n      \n      <div class=\"h-48 relative\">\n        <svg class=\"w-full h-full\" viewBox=\"0 0 320 192\">\n          <polyline\n            fill=\"none\"\n            stroke=\"#F7DC6F\"\n            stroke-width=\"3\"\n            stroke-linecap=\"round\"\n            stroke-linejoin=\"round\"\n            points=\"20,160 60,140 100,120 140,100 180,90 220,80 260,60 300,40\"\n          />\n          <circle cx=\"300\" cy=\"40\" r=\"4\" fill=\"#F7DC6F\"/>\n        </svg>\n      </div>\n    </div>\n\n    <!-- Top Products -->\n    <div class=\"mx-4 mb-6 bg-white rounded-2xl border border-gray-100\">\n      <div class=\"p-4 border-b border-gray-100\">\n        <h3 class=\"text-lg font-semibold text-gray-900\">Top Selling Products</h3>\n      </div>\n      \n      <div class=\"divide-y divide-gray-100\">\n        <div class=\"p-4 flex items-center justify-between\">\n          <div class=\"flex items-center space-x-3\">\n            <div class=\"w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center\">\n              <i data-lucide=\"file-text\" class=\"w-5 h-5 text-blue-600\"></i>\n            </div>\n            <div>\n              <div class=\"font-medium text-gray-900\">UI Kit Pro</div>\n              <div class=\"text-sm text-gray-500\">187 sales</div>\n            </div>\n          </div>\n          <div class=\"text-right\">\n            <div class=\"font-semibold text-gray-900\">$5,610</div>\n            <div class=\"text-sm text-green-600\">+12%</div>\n          </div>\n        </div>\n        \n        <div class=\"p-4 flex items-center justify-between\">\n          <div class=\"flex items-center space-x-3\">\n            <div class=\"w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center\">\n              <i data-lucide=\"code\" class=\"w-5 h-5 text-purple-600\"></i>\n            </div>\n            <div>\n              <div class=\"font-medium text-gray-900\">React Course</div>\n              <div class=\"text-sm text-gray-500\">143 sales</div>\n            </div>\n          </div>\n          <div class=\"text-right\">\n            <div class=\"font-semibold text-gray-900\">$4,290</div>\n            <div class=\"text-sm text-green-600\">+8%</div>\n          </div>\n        </div>\n        \n        <div class=\"p-4 flex items-center justify-between\">\n          <div class=\"flex items-center space-x-3\">\n            <div class=\"w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center\">\n              <i data-lucide=\"book\" class=\"w-5 h-5 text-green-600\"></i>\n            </div>\n            <div>\n              <div class=\"font-medium text-gray-900\">Design Guide</div>\n              <div class=\"text-sm text-gray-500\">98 sales</div>\n            </div>\n          </div>\n          <div class=\"text-right\">\n            <div class=\"font-semibold text-gray-900\">$2,940</div>\n            <div class=\"text-sm text-red-500\">-3%</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Customer Insights -->\n    <div class=\"mx-4 mb-6 bg-white rounded-2xl border border-gray-100\">\n      <div class=\"p-4 border-b border-gray-100\">\n        <h3 class=\"text-lg font-semibold text-gray-900\">Customer Insights</h3>\n      </div>\n      \n      <div class=\"p-4 space-y-4\">\n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-sm text-gray-600\">New Customers</span>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-24 h-2 bg-gray-200 rounded-full\">\n              <div class=\"w-16 h-2 bg-yellow-300 rounded-full\"></div>\n            </div>\n            <span class=\"text-sm font-medium text-gray-900\">67%</span>\n          </div>\n        </div>\n        \n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-sm text-gray-600\">Returning Customers</span>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-24 h-2 bg-gray-200 rounded-full\">\n              <div class=\"w-8 h-2 bg-gray-700 rounded-full\"></div>\n            </div>\n            <span class=\"text-sm font-medium text-gray-900\">33%</span>\n          </div>\n        </div>\n        \n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-sm text-gray-600\">Customer Satisfaction</span>\n          <div class=\"flex items-center space-x-2\">\n            <div class=\"w-24 h-2 bg-gray-200 rounded-full\">\n              <div class=\"w-20 h-2 bg-green-500 rounded-full\"></div>\n            </div>\n            <span class=\"text-sm font-medium text-gray-900\">84%</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Bottom Navigation -->\n  <div class=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-4 py-2\">\n    <div class=\"flex items-center justify-around\">\n      <button class=\"flex flex-col items-center py-2 active:opacity-70 transition-opacity\">\n        <i data-lucide=\"home\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-400 mt-1\">Home</span>\n      </button>\n      \n      <button class=\"flex flex-col items-center py-2 active:opacity-70 transition-opacity\">\n        <i data-lucide=\"store\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-400 mt-1\">Store</span>\n      </button>\n      \n      <button class=\"flex flex-col items-center py-2 active:opacity-70 transition-opacity\">\n        <i data-lucide=\"bar-chart-3\" class=\"w-6 h-6 text-yellow-600\"></i>\n        <span class=\"text-xs text-yellow-600 mt-1 font-medium\">Analytics</span>\n      </button>\n      \n      <button class=\"flex flex-col items-center py-2 active:opacity-70 transition-opacity\">\n        <i data-lucide=\"user\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-400 mt-1\">Profile</span>\n      </button>\n    </div>\n  </div>\n</div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n  const buttons = document.querySelectorAll('button');\n  \n  buttons.forEach(button => {\n    button.addEventListener('touchstart', function() {\n      this.style.opacity = '0.7';\n    });\n    \n    button.addEventListener('touchend', function() {\n      this.style.opacity = '1';\n    });\n    \n    button.addEventListener('mousedown', function() {\n      this.style.opacity = '0.7';\n    });\n    \n    button.addEventListener('mouseup', function() {\n      this.style.opacity = '1';\n    });\n  });\n});\n</script>", "order": 5, "status": "complete", "createdAt": "2025-06-08T15:39:26.426Z", "updatedAt": "2025-06-08T15:40:19.260Z"}, {"id": "ce6cf83e-1f10-4f95-a087-33d923f97805", "chatId": "a7c579ff-b5df-4246-9684-5a6b83f7cd1f", "projectId": "9a862520-7f33-4594-ba2e-ed7f17a8efda", "name": "User Profile", "html": "<div class=\"flex flex-col min-h-screen bg-gray-50\">\n  <!-- Header -->\n  <div class=\"flex items-center justify-between px-4 py-4 bg-white border-b border-gray-200\">\n    <i data-lucide=\"arrow-left\" class=\"w-6 h-6 text-gray-700\"></i>\n    <div class=\"flex items-center gap-2\">\n      <div class=\"w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center\">\n        <i data-lucide=\"zap\" class=\"w-4 h-4 text-white\"></i>\n      </div>\n      <span class=\"text-lg font-semibold text-gray-900\">Profile</span>\n    </div>\n    <i data-lucide=\"settings\" class=\"w-6 h-6 text-gray-700\"></i>\n  </div>\n\n  <!-- Scrollable Content -->\n  <div class=\"flex-1 overflow-y-auto pb-20\">\n    <!-- Profile Header -->\n    <div class=\"bg-white px-4 py-6 border-b border-gray-100\">\n      <div class=\"flex items-center gap-4\">\n        <div class=\"relative\">\n          <img \n            src=\"https://magically.life/api/media/image?query=professional%20profile%20photo%20of%20a%20person%20smiling\" \n            alt=\"Profile\" \n            class=\"w-20 h-20 rounded-full object-cover border-2 border-yellow-300\"\n          />\n          <div class=\"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white\"></div>\n        </div>\n        <div class=\"flex-1\">\n          <h2 class=\"text-xl font-bold text-gray-900\">Sarah Mitchell</h2>\n          <p class=\"text-gray-600\"><EMAIL></p>\n          <div class=\"flex items-center gap-1 mt-1\">\n            <i data-lucide=\"star\" class=\"w-4 h-4 text-yellow-500\"></i>\n            <span class=\"text-sm text-gray-700\">Premium Member</span>\n          </div>\n        </div>\n        <button class=\"px-4 py-2 bg-yellow-400 rounded-lg font-medium text-gray-900 active:opacity-70\">\n          Edit\n        </button>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"px-4 py-4\">\n      <div class=\"flex gap-3\">\n        <div class=\"flex-1 bg-white rounded-xl p-4 border border-gray-100\">\n          <div class=\"text-2xl font-bold text-gray-900\">24</div>\n          <div class=\"text-sm text-gray-600\">Products</div>\n        </div>\n        <div class=\"flex-1 bg-white rounded-xl p-4 border border-gray-100\">\n          <div class=\"text-2xl font-bold text-gray-900\">$2,840</div>\n          <div class=\"text-sm text-gray-600\">Spent</div>\n        </div>\n        <div class=\"flex-1 bg-white rounded-xl p-4 border border-gray-100\">\n          <div class=\"text-2xl font-bold text-gray-900\">18</div>\n          <div class=\"text-sm text-gray-600\">Reviews</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Menu Sections -->\n    <div class=\"px-4 space-y-4\">\n      <!-- Account Section -->\n      <div class=\"bg-white rounded-xl overflow-hidden border border-gray-100\">\n        <div class=\"px-4 py-3 border-b border-gray-100\">\n          <h3 class=\"font-semibold text-gray-900\">Account</h3>\n        </div>\n        <div class=\"divide-y divide-gray-100\">\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"shopping-bag\" class=\"w-4 h-4 text-blue-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Purchase History</span>\n            </div>\n            <i data-lucide=\"chevron-right\" class=\"w-5 h-5 text-gray-400\"></i>\n          </button>\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"credit-card\" class=\"w-4 h-4 text-green-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Payment Methods</span>\n            </div>\n            <i data-lucide=\"chevron-right\" class=\"w-5 h-5 text-gray-400\"></i>\n          </button>\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"repeat\" class=\"w-4 h-4 text-purple-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Subscriptions</span>\n            </div>\n            <div class=\"flex items-center gap-2\">\n              <span class=\"text-sm text-orange-600 font-medium\">3 Active</span>\n              <i data-lucide=\"chevron-right\" class=\"w-5 h-5 text-gray-400\"></i>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      <!-- Preferences Section -->\n      <div class=\"bg-white rounded-xl overflow-hidden border border-gray-100\">\n        <div class=\"px-4 py-3 border-b border-gray-100\">\n          <h3 class=\"font-semibold text-gray-900\">Preferences</h3>\n        </div>\n        <div class=\"divide-y divide-gray-100\">\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"bell\" class=\"w-4 h-4 text-yellow-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Notifications</span>\n            </div>\n            <i data-lucide=\"chevron-right\" class=\"w-5 h-5 text-gray-400\"></i>\n          </button>\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"shield\" class=\"w-4 h-4 text-indigo-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Privacy & Security</span>\n            </div>\n            <i data-lucide=\"chevron-right\" class=\"w-5 h-5 text-gray-400\"></i>\n          </button>\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"moon\" class=\"w-4 h-4 text-gray-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Dark Mode</span>\n            </div>\n            <div class=\"w-10 h-6 bg-gray-200 rounded-full relative\">\n              <div class=\"w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 shadow-sm\"></div>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      <!-- Support Section -->\n      <div class=\"bg-white rounded-xl overflow-hidden border border-gray-100\">\n        <div class=\"px-4 py-3 border-b border-gray-100\">\n          <h3 class=\"font-semibold text-gray-900\">Support</h3>\n        </div>\n        <div class=\"divide-y divide-gray-100\">\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"help-circle\" class=\"w-4 h-4 text-orange-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Help Center</span>\n            </div>\n            <i data-lucide=\"chevron-right\" class=\"w-5 h-5 text-gray-400\"></i>\n          </button>\n          <button class=\"w-full flex items-center justify-between px-4 py-4 active:opacity-70\">\n            <div class=\"flex items-center gap-3\">\n              <div class=\"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center\">\n                <i data-lucide=\"message-circle\" class=\"w-4 h-4 text-red-600\"></i>\n              </div>\n              <span class=\"text-gray-900\">Contact Support</span>\n            </div>\n            <i data-lucide=\"chevron-right\" class=\"w-5 h-5 text-gray-400\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Logout Button -->\n      <div class=\"pt-4 pb-8\">\n        <button class=\"w-full flex items-center justify-center gap-3 px-4 py-4 bg-red-50 rounded-xl border border-red-200 active:opacity-70\">\n          <i data-lucide=\"log-out\" class=\"w-5 h-5 text-red-600\"></i>\n          <span class=\"font-medium text-red-600\">Sign Out</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Bottom Navigation -->\n  <div class=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200\">\n    <div class=\"flex items-center justify-around py-2\">\n      <button class=\"flex flex-col items-center py-2 px-4 active:opacity-70\">\n        <i data-lucide=\"home\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-400 mt-1\">Home</span>\n      </button>\n      <button class=\"flex flex-col items-center py-2 px-4 active:opacity-70\">\n        <i data-lucide=\"store\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-400 mt-1\">Store</span>\n      </button>\n      <button class=\"flex flex-col items-center py-2 px-4 active:opacity-70\">\n        <i data-lucide=\"bar-chart-3\" class=\"w-6 h-6 text-gray-400\"></i>\n        <span class=\"text-xs text-gray-400 mt-1\">Analytics</span>\n      </button>\n      <button class=\"flex flex-col items-center py-2 px-4 active:opacity-70\">\n        <i data-lucide=\"user\" class=\"w-6 h-6 text-yellow-500\"></i>\n        <span class=\"text-xs text-yellow-500 mt-1 font-medium\">Profile</span>\n      </button>\n    </div>\n  </div>\n</div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n  const darkModeToggle = document.querySelector('.w-10.h-6');\n  const darkModeButton = darkModeToggle.parentElement;\n  let isDarkMode = false;\n\n  darkModeButton.addEventListener('click', function() {\n    isDarkMode = !isDarkMode;\n    const toggle = darkModeToggle.querySelector('div');\n    \n    if (isDarkMode) {\n      darkModeToggle.classList.remove('bg-gray-200');\n      darkModeToggle.classList.add('bg-yellow-400');\n      toggle.classList.remove('left-0.5');\n      toggle.classList.add('left-4');\n    } else {\n      darkModeToggle.classList.remove('bg-yellow-400');\n      darkModeToggle.classList.add('bg-gray-200');\n      toggle.classList.remove('left-4');\n      toggle.classList.add('left-0.5');\n    }\n  });\n\n  const buttons = document.querySelectorAll('button');\n  buttons.forEach(button => {\n    button.addEventListener('touchstart', function() {\n      this.style.opacity = '0.7';\n    });\n    \n    button.addEventListener('touchend', function() {\n      this.style.opacity = '1';\n    });\n  });\n});\n</script>", "order": 6, "status": "complete", "createdAt": "2025-06-08T15:39:26.426Z", "updatedAt": "2025-06-08T15:40:19.365Z"}]