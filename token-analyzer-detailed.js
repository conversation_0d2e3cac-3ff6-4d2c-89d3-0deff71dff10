const fs = require('fs');
const path = require('path');

// Read the request JSON file
const requestFile = path.join(__dirname, 'req.json');
const requestData = JSON.parse(fs.readFileSync(requestFile, 'utf8'));

// Token pricing
const INPUT_COST_PER_MILLION = 3; // $3 per million tokens
const OUTPUT_COST_PER_MILLION = 15; // $15 per million tokens

// Function to count tokens (rough estimate - 4 characters per token)
function estimateTokens(text) {
    return Math.ceil(text.length / 4);
}

// Function to analyze message content
function analyzeMessage(message) {
    if (!message) return { tokens: 0, details: {} };
    
    let totalTokens = 0;
    const details = {};
    
    // Handle different message formats
    if (typeof message === 'string') {
        totalTokens = estimateTokens(message);
        details.text = totalTokens;
    } else if (Array.isArray(message)) {
        message.forEach((item, index) => {
            if (item.type === 'text') {
                const textTokens = estimateTokens(item.text);
                totalTokens += textTokens;
                details[`text_${index}`] = textTokens;
            } else if (item.type) {
                const itemTokens = estimateTokens(JSON.stringify(item));
                totalTokens += itemTokens;
                details[`${item.type}_${index}`] = itemTokens;
            }
        });
    } else if (typeof message === 'object') {
        if (message.content) {
            if (typeof message.content === 'string') {
                const contentTokens = estimateTokens(message.content);
                totalTokens += contentTokens;
                details.content = contentTokens;
            } else if (Array.isArray(message.content)) {
                message.content.forEach((item, index) => {
                    if (item.type === 'text') {
                        const textTokens = estimateTokens(item.text);
                        totalTokens += textTokens;
                        details[`content_text_${index}`] = textTokens;
                    } else if (item.type) {
                        const itemTokens = estimateTokens(JSON.stringify(item));
                        totalTokens += itemTokens;
                        details[`content_${item.type}_${index}`] = itemTokens;
                    }
                });
            }
        }
        
        // Add tokens for other properties
        const otherProps = { ...message };
        delete otherProps.content;
        const otherTokens = estimateTokens(JSON.stringify(otherProps));
        totalTokens += otherTokens;
        details.other_properties = otherTokens;
    }
    
    return { tokens: totalTokens, details };
}

// Analyze the request
function analyzeRequest(request) {
    const results = {
        model: request.model,
        totalTokens: 0,
        inputTokens: 0,
        outputTokens: 0,
        messageBreakdown: [],
        messageTypeTokens: {
            system: 0,
            user: 0,
            assistant: 0,
            tool: 0
        },
        largeContentItems: []
    };
    
    // Analyze each message
    if (request.messages && Array.isArray(request.messages)) {
        request.messages.forEach((message, index) => {
            const analysis = analyzeMessage(message.content);
            
            results.totalTokens += analysis.tokens;
            
            // Categorize tokens as input or output
            if (message.role === 'user' || message.role === 'system') {
                results.inputTokens += analysis.tokens;
            } else {
                results.outputTokens += analysis.tokens;
            }
            
            // Track tokens by message type
            results.messageTypeTokens[message.role] = 
                (results.messageTypeTokens[message.role] || 0) + analysis.tokens;
            
            // Find large content items (more than 1000 tokens)
            if (message.content && Array.isArray(message.content)) {
                message.content.forEach((item, itemIndex) => {
                    if (item.type === 'text') {
                        const itemTokens = estimateTokens(item.text);
                        if (itemTokens > 1000) {
                            results.largeContentItems.push({
                                messageIndex: index,
                                itemIndex,
                                type: item.type,
                                tokens: itemTokens,
                                preview: item.text.substring(0, 100) + '...'
                            });
                        }
                    }
                });
            }
            
            // Analyze MO_FILE tags in this message
            const fileAnalysis = analyzeFileContentInMessage(message);
            
            results.messageBreakdown.push({
                index,
                role: message.role,
                tokens: analysis.tokens,
                details: analysis.details,
                fileAnalysis
            });
        });
    }
    
    // Find the largest messages
    results.largestMessages = [...results.messageBreakdown]
        .sort((a, b) => b.tokens - a.tokens)
        .slice(0, 5);
    
    return results;
}

// Analyze file content in a single message
function analyzeFileContentInMessage(message) {
    const fileAnalysis = {
        totalFiles: 0,
        totalFileTokens: 0,
        fileTypes: {}
    };
    
    let content = '';
    if (typeof message.content === 'string') {
        content = message.content;
    } else if (Array.isArray(message.content)) {
        content = message.content
            .filter(item => item.type === 'text')
            .map(item => item.text)
            .join('\n');
    }
    
    // Count MO_FILE occurrences
    const moFileMatches = content.match(/<MO_FILE[^>]*>/g) || [];
    fileAnalysis.totalFiles = moFileMatches.length;
    
    // Extract file types
    moFileMatches.forEach(match => {
        const typeMatch = match.match(/type="([^"]+)"/);
        if (typeMatch && typeMatch[1]) {
            const fileType = typeMatch[1];
            fileAnalysis.fileTypes[fileType] = (fileAnalysis.fileTypes[fileType] || 0) + 1;
        }
        
        // Estimate file size
        const startIdx = content.indexOf(match);
        if (startIdx !== -1) {
            const endTag = '</MO_FILE>';
            const endIdx = content.indexOf(endTag, startIdx);
            if (endIdx !== -1) {
                const fileContent = content.substring(startIdx, endIdx + endTag.length);
                const tokens = estimateTokens(fileContent);
                fileAnalysis.totalFileTokens += tokens;
            }
        }
    });
    
    return fileAnalysis;
}

// Analyze the file content patterns across all messages
function analyzeFileContent(request) {
    const filePatterns = {
        totalFileMessages: 0,
        fileMessageSizes: [],
        averageFileSize: 0,
        totalFileTokens: 0,
        fileTypes: {}
    };
    
    // Look for MO_FILE tags in all messages
    if (request.messages && Array.isArray(request.messages)) {
        request.messages.forEach(message => {
            let content = '';
            if (typeof message.content === 'string') {
                content = message.content;
            } else if (Array.isArray(message.content)) {
                content = message.content
                    .filter(item => item.type === 'text')
                    .map(item => item.text)
                    .join('\n');
            }
            
            // Count MO_FILE occurrences
            const moFileMatches = content.match(/<MO_FILE[^>]*>/g) || [];
            filePatterns.totalFileMessages += moFileMatches.length;
            
            // Extract file types
            moFileMatches.forEach(match => {
                const typeMatch = match.match(/type="([^"]+)"/);
                if (typeMatch && typeMatch[1]) {
                    const fileType = typeMatch[1];
                    filePatterns.fileTypes[fileType] = (filePatterns.fileTypes[fileType] || 0) + 1;
                }
                
                // Estimate file size
                const startIdx = content.indexOf(match);
                if (startIdx !== -1) {
                    const endTag = '</MO_FILE>';
                    const endIdx = content.indexOf(endTag, startIdx);
                    if (endIdx !== -1) {
                        const fileContent = content.substring(startIdx, endIdx + endTag.length);
                        const tokens = estimateTokens(fileContent);
                        filePatterns.fileMessageSizes.push(tokens);
                        filePatterns.totalFileTokens += tokens;
                    }
                }
            });
        });
    }
    
    // Calculate average file size
    if (filePatterns.fileMessageSizes.length > 0) {
        filePatterns.averageFileSize = Math.round(
            filePatterns.totalFileTokens / filePatterns.fileMessageSizes.length
        );
    }
    
    return filePatterns;
}

// Calculate costs
function calculateCosts(analysis) {
    const inputCost = (analysis.inputTokens / 1000000) * INPUT_COST_PER_MILLION;
    const outputCost = (analysis.outputTokens / 1000000) * OUTPUT_COST_PER_MILLION;
    const totalCost = inputCost + outputCost;
    
    return {
        inputCost: inputCost.toFixed(6),
        outputCost: outputCost.toFixed(6),
        totalCost: totalCost.toFixed(6)
    };
}

// Main analysis
const analysis = analyzeRequest(requestData);
const fileAnalysis = analyzeFileContent(requestData);
const costs = calculateCosts(analysis);

// Combine results
const results = {
    model: analysis.model,
    totalEstimatedTokens: analysis.totalTokens,
    inputTokens: analysis.inputTokens,
    outputTokens: analysis.outputTokens,
    messageCount: requestData.messages.length,
    messageTypeBreakdown: analysis.messageTypeTokens,
    costs,
    messageBreakdown: analysis.messageBreakdown.map(msg => ({
        index: msg.index,
        role: msg.role,
        tokens: msg.tokens,
        fileCount: msg.fileAnalysis.totalFiles,
        fileTokens: msg.fileAnalysis.totalFileTokens
    })),
    largestMessages: analysis.largestMessages.map(msg => ({
        index: msg.index,
        role: msg.role,
        tokens: msg.tokens,
        fileCount: msg.fileAnalysis.totalFiles,
        fileTokens: msg.fileAnalysis.totalFileTokens
    })),
    fileAnalysis
};

// Output results
console.log('=== TOKEN USAGE ANALYSIS ===');
console.log(`Model: ${results.model}`);
console.log(`Total Estimated Tokens: ${results.totalEstimatedTokens}`);
console.log(`- Input Tokens: ${results.inputTokens}`);
console.log(`- Output Tokens: ${results.outputTokens}`);
console.log(`Total Messages: ${results.messageCount}`);

console.log('\n=== COST ANALYSIS ===');
console.log(`Input Cost ($${INPUT_COST_PER_MILLION}/million): $${costs.inputCost}`);
console.log(`Output Cost ($${OUTPUT_COST_PER_MILLION}/million): $${costs.outputCost}`);
console.log(`Total Estimated Cost: $${costs.totalCost}`);
console.log(`Actual Billed Cost: $0.386`);
console.log(`Cache Savings: $0.321`);
console.log(`Final Cost: $0.0654`);

console.log('\n=== TOKEN USAGE BY MESSAGE TYPE ===');
Object.entries(results.messageTypeBreakdown).forEach(([role, tokens]) => {
    const percentage = ((tokens / results.totalEstimatedTokens) * 100).toFixed(1);
    console.log(`${role}: ${tokens} tokens (${percentage}%)`);
});

console.log('\n=== TOP 10 LARGEST MESSAGES ===');
results.messageBreakdown
    .sort((a, b) => b.tokens - a.tokens)
    .slice(0, 10)
    .forEach((msg, i) => {
        const percentage = ((msg.tokens / results.totalEstimatedTokens) * 100).toFixed(1);
        console.log(`${i+1}. Message ${msg.index} (${msg.role}): ${msg.tokens} tokens (${percentage}%)`);
        if (msg.fileCount > 0) {
            console.log(`   Contains ${msg.fileCount} files using ${msg.fileTokens} tokens`);
        }
    });

console.log('\n=== FILE CONTENT ANALYSIS ===');
console.log(`Total File Messages: ${fileAnalysis.totalFileMessages}`);
console.log(`Average File Size (tokens): ${fileAnalysis.averageFileSize}`);
console.log(`Total File Tokens: ${fileAnalysis.totalFileTokens}`);
console.log(`Percentage of Total: ${((fileAnalysis.totalFileTokens / results.totalEstimatedTokens) * 100).toFixed(1)}%`);
console.log('File Types:');
Object.entries(fileAnalysis.fileTypes).forEach(([type, count]) => {
    console.log(`  - ${type}: ${count}`);
});

// Write detailed analysis to file
fs.writeFileSync(
    path.join(__dirname, 'token-analysis-detailed.json'), 
    JSON.stringify(results, null, 2)
);

console.log('\nDetailed analysis written to token-analysis-detailed.json');
