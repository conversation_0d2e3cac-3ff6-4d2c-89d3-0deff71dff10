# MO_DIFF Tag Documentation

## Overview

The `MO_DIFF` tag provides a powerful mechanism for making partial edits to files through search and replace operations. This approach avoids the brittleness of line number-based edits by focusing on content patterns instead.

## Key Features

- Make targeted changes to specific parts of files
- Apply multiple edits to the same file in a single operation
- Replace entire files when needed
- Clear error messaging for ambiguous or failed edits

## Basic Usage

### Partial File Edits

To make partial edits to a file, use the `MO_DIFF` tag with nested `SEARCH` and `REPLACE` blocks:

```
<MO_DIFF lang="typescript" path="/path/to/file.ts">
<SEARCH>
// Original code segment to find
function example() {
  return "old value";
}
</SEARCH>
<REPLACE>
// Updated code
function example() {
  return "new value";
}
</REPLACE>
</MO_DIFF>
```

### Multiple Edits to the Same File

You can include multiple `SEARCH`/`REPLACE` pairs within a single `MO_DIFF` tag:

```
<MO_DIFF lang="typescript" path="/path/to/file.ts">
<SEARCH>
// First code segment to replace
</SEARCH>
<REPLACE>
// First replacement
</REPLACE>

<SEARCH>
// Second code segment to replace
</SEARCH>
<REPLACE>
// Second replacement
</REPLACE>
</MO_DIFF>
```

### Full File Replacement

To replace an entire file, use the `full="true"` attribute and provide the complete replacement content without `SEARCH`/`REPLACE` blocks:

```
<MO_DIFF lang="typescript" path="/path/to/file.ts" full="true">
// Complete new file content goes here
import React from 'react';

function Component() {
  return <div>New Content</div>;
}

export default Component;
</MO_DIFF>
```

## Tag Attributes

The `MO_DIFF` tag supports the following attributes:

| Attribute | Description | Required |
|-----------|-------------|----------|
| `lang`    | Language of the code (e.g., "typescript", "javascript") | Yes |
| `path`    | Absolute path to the file to modify | Yes |
| `full`    | Set to "true" to replace the entire file | No |

## Best Practices

1. **Provide Sufficient Context**: Include enough surrounding code in your `SEARCH` blocks to ensure unique matches.

2. **Use Complete Blocks**: Each `REPLACE` block should include complete, valid code - not just the changed lines.

3. **Handle Multiple Edits Carefully**: When making multiple edits to the same file, ensure later edits don't depend on earlier edits in ways that could create ambiguity.

4. **Proper Nesting**: Ensure all tags are properly nested and closed.

5. **Error Handling**: Check for error messages in case a pattern can't be found or is ambiguous.

## Implementation Details

The MO_DIFF system consists of two main components:

1. **MODiffParser**: Parses the `MO_DIFF` tags and extracts search/replace pairs.

2. **FileContentManager**: Applies the search/replace operations to the actual file content.

The system handles errors gracefully when:
- A file doesn't exist
- A search pattern can't be found
- A search pattern matches multiple locations (ambiguous)
- The number of search and replace blocks doesn't match

## Example Usage

### Adding a Loading State to a Button Component

```
<MO_DIFF lang="typescript" path="/components/Button.tsx">
<SEARCH>
import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
}
</SEARCH>
<REPLACE>
import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
  loading?: boolean;
}
</REPLACE>

<SEARCH>
export const Button = ({ title, onPress }: ButtonProps) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <Text>{title}</Text>
    </TouchableOpacity>
  );
};
</SEARCH>
<REPLACE>
export const Button = ({ title, onPress, loading = false }: ButtonProps) => {
  return (
    <TouchableOpacity onPress={loading ? undefined : onPress} disabled={loading}>
      {loading ? (
        <>
          <ActivityIndicator size="small" color="#ffffff" />
          <Text>{title}</Text>
        </>
      ) : (
        <Text>{title}</Text>
      )}
    </TouchableOpacity>
  );
};
</REPLACE>
</MO_DIFF>
```

## Troubleshooting

### Common Issues

1. **Pattern Not Found**: Ensure your search pattern exists exactly as specified in the file.

2. **Ambiguous Patterns**: If you get an "ambiguous pattern" error, your search pattern appears multiple times in the file. Add more context to make it unique.

3. **Nested Tags**: Be careful with HTML/JSX content that might include angled brackets that could be confused with tag delimiters.

### Testing

Use the provided test utilities to validate your `MO_DIFF` operations:

```typescript
// Run the test script
import { runDiffTests } from '../scripts/test-mo-diff';
const results = runDiffTests();
console.log(results);
```
