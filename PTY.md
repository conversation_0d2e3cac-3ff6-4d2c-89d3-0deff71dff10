# TestFlight Build Process

This document outlines the complete technical steps required to execute the TestFlight build process for publishing your React Native app to Apple TestFlight, including file uploads and specific commands for each stage.

## Prerequisites

Before starting the build process, ensure you have the following:

1. **Apple Developer Account** with an active membership
2. **App Store Connect** access with appropriate permissions
3. **Apple App-Specific Password** for CI/CD authentication
4. **Valid App Bundle ID** registered in your Apple Developer account
5. **Valid Provisioning Profiles** for your app
6. **E2B API Key** configured in your environment variables

## Environment Setup

The build process runs in an isolated sandbox environment using E2B. Make sure your environment has:

```
E2B_API_KEY=your_api_key_here
```

## Build Process Overview

The TestFlight build process consists of the following steps:

1. **Configuration**: Set up app metadata, version, and build number
2. **Sandbox Creation**: Create an isolated build environment
3. **Project Preparation**: <PERSON>lone and configure the project
4. **Dependencies Installation**: Install required npm packages
5. **EAS Build**: Run the Expo Application Services build process
6. **TestFlight Submission**: Submit the build to Apple TestFlight

## Complete Build Process Steps

### 1. Configuration and Initialization

**UI Steps:**
1. Navigate to the TestFlight tab in your project
2. Fill in the required configuration details:
    - App Name
    - Bundle ID
    - Version
    - Build Number
    - Apple Team ID
3. Click "Start Publishing" to begin the build process

**Technical Steps:**
```javascript
// 1. Create build configuration
const buildConfig = {
  appName: 'MyApp',
  bundleId: 'com.example.myapp',
  version: '1.0.0',
  buildNumber: '1',
  appleTeamId: 'ABCDE12345'
};

// 2. Initialize build in database
const buildId = await db.builds.create({
  ...buildConfig,
  status: 'configuring',
  progress: 0,
  userId: user.id,
  chatId: chatId
});

// 3. Create sandbox environment
const sandbox = await Sandbox.create({
  apiKey: process.env.E2B_API_KEY,
  template: 'base',
});

// 4. Create terminal session
const ptySession = await sandbox.pty.create({
  cols: 100,
  rows: 40,
});

// 5. Register terminal session
const terminalSessionId = registerTerminalSession(sandbox, ptySession.pid, true);

// 6. Generate WebSocket URL
const wsUrl = `${wsProtocol}://${host}/api/terminal-ws?sessionId=${terminalSessionId}`;
```

### 2. Project Setup and File Upload

**Commands Run in Sandbox:**
```bash
# 1. Create project directory
mkdir -p /app
cd /app

# 2. Initialize npm project
npm init -y

# 3. Install required dependencies
npm install -g eas-cli
npm install expo expo-dev-client react-native
```

**File Upload Process:**
```javascript
// 1. Generate package.json
const packageJson = {
  name: buildConfig.appName.toLowerCase().replace(/\s+/g, '-'),
  version: buildConfig.version,
  main: 'node_modules/expo/AppEntry.js',
  scripts: {
    start: 'expo start',
    build: 'eas build'
  },
  dependencies: {
    expo: '^47.0.0',
    'react': '18.1.0',
    'react-native': '0.70.5'
  }
};

// 2. Generate app.json
const appJson = {
  expo: {
    name: buildConfig.appName,
    slug: buildConfig.appName.toLowerCase().replace(/\s+/g, '-'),
    version: buildConfig.version,
    orientation: 'portrait',
    icon: './assets/icon.png',
    ios: {
      bundleIdentifier: buildConfig.bundleId,
      buildNumber: buildConfig.buildNumber
    }
  }
};

// 3. Generate eas.json
const easJson = {
  build: {
    production: {
      distribution: 'internal',
      ios: {
        resourceClass: 'default'
      }
    }
  },
  submit: {
    production: {
      ios: {
        appleId: process.env.APPLE_ID,
        ascAppId: process.env.ASC_APP_ID,
        appleTeamId: buildConfig.appleTeamId
      }
    }
  }
};

// 4. Upload files to sandbox
await sandbox.filesystem.write('/app/package.json', JSON.stringify(packageJson, null, 2));
await sandbox.filesystem.write('/app/app.json', JSON.stringify(appJson, null, 2));
await sandbox.filesystem.write('/app/eas.json', JSON.stringify(easJson, null, 2));

// 5. Create assets directory and upload icon if provided
await sandbox.filesystem.mkdir('/app/assets');
if (buildConfig.icon) {
  const iconBuffer = Buffer.from(buildConfig.icon, 'base64');
  await sandbox.filesystem.write('/app/assets/icon.png', iconBuffer);
} else {
  // Use default icon
  await sandbox.filesystem.write('/app/assets/icon.png', defaultIconBuffer);
}

// 6. Create minimal App.js
const appJs = `
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function App() {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>${buildConfig.appName}</Text>
      <Text style={styles.version}>Version ${buildConfig.version} (${buildConfig.buildNumber})</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  version: {
    marginTop: 10,
    color: '#888',
  },
});
`;
await sandbox.filesystem.write('/app/App.js', appJs);
```

### 3. Build Execution

**Commands Run in Sandbox:**
```bash
# 1. Navigate to project directory
cd /app

# 2. Initialize git repository (required by EAS)
git init
git add .
git config --global user.email "<EMAIL>"
git config --global user.name "EAS Build"
git commit -m "Initial commit"

# 3. Log in to Expo (automated via pattern matching)
eas login

# 4. Configure EAS build
eas build:configure

# 5. Start the build process
eas build --platform ios --profile production --non-interactive --no-wait

# 6. Submit to TestFlight
eas submit -p ios --latest
```

**Pattern-Based Automation:**
The following prompts are automatically handled:

1. Expo login credentials
2. Apple ID login
3. Apple App-Specific Password
4. Git initialization confirmations
5. Build configuration confirmations
6. TestFlight submission confirmations

### 4. Real-time Terminal Interaction

**WebSocket Connection:**
```javascript
// Client-side connection
const socket = new WebSocket(wsUrl);

// Handle terminal data
socket.onmessage = (event) => {
  terminal.write(event.data);
  
  // Update build status based on terminal output
  if (event.data.includes('Successfully uploaded the build')) {
    updateBuildStatus('completed');
  } else if (event.data.includes('Build failed')) {
    updateBuildStatus('failed');
  }
};

// Send user input to terminal
terminal.onData((data) => {
  if (socket.readyState === WebSocket.OPEN) {
    socket.send(data);
  }
});
```

**Manual Interaction Mode:**
Users can toggle between automatic and manual modes:

```javascript
// Toggle automation
function toggleAutoMode() {
  const newMode = !autoMode;
  setAutoMode(newMode);
  
  // Send control message to server
  if (socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({
      type: 'control',
      action: 'setAutoMode',
      autoMode: newMode
    }));
  }
}
```

### 5. Build Status Monitoring

**Polling for Status Updates:**
```javascript
async function pollBuildStatus() {
  try {
    const response = await fetch(`/api/testflight/status?buildId=${buildId}`);
    const data = await response.json();
    
    if (data.status) {
      setBuildProgress(data);
      
      // Continue polling if build is still in progress
      if (['configuring', 'preparing', 'building', 'submitting'].includes(data.status)) {
        setTimeout(pollBuildStatus, 5000);
      }
    }
  } catch (error) {
    console.error('Error polling build status:', error);
    setTimeout(pollBuildStatus, 10000); // Retry with longer delay on error
  }
}
```

### 6. Build Completion and TestFlight Link

**Final Steps:**
```javascript
// Extract TestFlight link from build output
function extractTestFlightLink(buildOutput) {
  const match = buildOutput.match(/TestFlight link: (https:\/\/testflight\.apple\.com\/[a-zA-Z0-9]+)/);
  return match ? match[1] : null;
}

// Update build record with TestFlight link
async function finalizeBuild(buildId, testFlightLink) {
  await db.builds.update(buildId, {
    status: 'completed',
    progress: 100,
    testFlightLink,
    completedAt: new Date()
  });
}
```

## Troubleshooting

### Common Issues

1. **Apple Authentication Failures**:
    - Ensure your Apple ID and app-specific password are correct
    - Check that your Apple ID has appropriate permissions

2. **Build Failures**:
    - Check the terminal output for specific error messages
    - Verify that your project dependencies are compatible
    - Ensure your app configuration is valid

3. **WebSocket Connection Issues**:
    - If the terminal doesn't connect, try refreshing the page
    - Check browser console for WebSocket errors

### Debugging

The terminal view provides real-time access to the build process. You can:

1. Pause automation to manually investigate issues
2. Send custom commands to the terminal
3. View the complete build logs

## Detailed Technical Implementation

### 1. Sandbox Initialization Process

```javascript
// Create a new sandbox with the code-interpreter template
const sandbox = await Sandbox.create({
  apiKey: process.env.E2B_API_KEY,
  template: 'base',
});

// Create a PTY session for terminal interaction
const ptySession = await sandbox.pty.create({
  cols: 100,
  rows: 40,
});

// Register the terminal session for WebSocket tunneling
const terminalSessionId = registerTerminalSession(sandbox, ptySession.pid, autoMode);
```

### 2. File Upload Process

Files are uploaded to the sandbox using the E2B SDK's filesystem API:

```javascript
// Upload package.json
await sandbox.filesystem.write('/app/package.json', JSON.stringify(packageJson, null, 2));

// Upload app.json with Expo configuration
await sandbox.filesystem.write('/app/app.json', JSON.stringify(appJson, null, 2));

// Upload eas.json with build configuration
await sandbox.filesystem.write('/app/eas.json', JSON.stringify(easConfig, null, 2));

// Upload app icon if provided
if (icon) {
  const iconBuffer = Buffer.from(icon, 'base64');
  await sandbox.filesystem.write('/app/assets/icon.png', iconBuffer);
}
```

### 3. WebSocket Terminal Tunneling

The WebSocket connection is established between the browser and the PTY session:

```javascript
// Server-side WebSocket handling
socket.on('connection', (ws) => {
  // Add client to the session
  session.clients.add(ws);
  
  // Handle messages from client to PTY
  ws.on('message', (message) => {
    const data = message.toString();
    sandbox.pty.sendInput(ptyPid, new TextEncoder().encode(data));
  });
  
  // Handle client disconnect
  ws.on('close', () => {
    session.clients.delete(ws);
  });
});

// Handle data from PTY to clients
sandbox.pty.onData = (pid, data) => {
  if (pid === ptyPid) {
    const dataStr = new TextDecoder().decode(data);
    // Send to all connected clients
    for (const client of session.clients) {
      if (client.readyState === WebSocket.OPEN) {
        client.send(dataStr);
      }
    }
  }
};
```

### 4. Pattern-Based Automation

The pattern-based automation system monitors terminal output and responds to prompts:

```javascript
const automation = new PatternBasedAutomation({
  patterns: [
    {
      pattern: /Apple ID:/,
      response: `${appleId}\n`,
      description: 'Apple ID login'
    },
    {
      pattern: /Password:/,
      response: `${applePassword}\n`,
      description: 'Apple password'
    },
    {
      pattern: /Do you want to continue\? \(Y\/n\)/i,
      response: 'Y\n',
      description: 'Confirm continue'
    },
    // Additional patterns for other prompts
  ],
  completionPatterns: [
    /Successfully uploaded the build/
  ],
  errorPatterns: [
    /Error: /,
    /Build failed/
  ]
});

// Process terminal output through automation
function processTerminalOutput(data) {
  if (autoMode && !automationPaused) {
    const response = automation.processOutput(data);
    if (response) {
      sandbox.pty.sendInput(ptyPid, new TextEncoder().encode(response));
    }
  }
}
```

## File Structure and Implementation Details

```
src/
├── app/
│   └── api/
│       ├── testflight/
│       │   ├── route.ts                # Main API endpoints for TestFlight builds
│       │   ├── status/route.ts         # Build status polling endpoint
│       │   └── terminal-build/route.ts # Terminal build initialization endpoint
│       └── terminal-ws/
│           └── route.ts                # WebSocket handler for terminal tunneling
├── components/
│   └── testflight/
│       ├── PublishTab.tsx              # UI for publishing configuration and progress
│       ├── TerminalView.tsx            # Terminal UI component with xterm.js
│       ├── TestFlightPublisher.tsx     # Main publisher component with tabs
│       └── README.md                   # This documentation file
├── lib/
│   ├── pty/
│   │   ├── pattern-based-automation.ts # Pattern-based terminal automation
│   │   ├── terminal-session.ts         # Terminal session management
│   │   └── terminal-tunnel.ts          # WebSocket tunneling implementation
│   └── testflight/
│       ├── eas-build-pattern.ts        # EAS build automation patterns
│       ├── file-templates.ts           # Templates for generated files
│       └── sandbox-utils.ts            # Utilities for sandbox management
├── stores/
│   └── TestFlightStore.ts              # MobX state management for TestFlight
└── types/
    └── testflight.ts                   # TypeScript type definitions
```

### Key File Implementations

#### 1. `src/app/api/testflight/terminal-build/route.ts`
Handles the creation of a sandbox environment, sets up the PTY session, and registers it for WebSocket tunneling.

```typescript
export async function POST(req: NextRequest) {
  try {
    const { chatId, buildConfig, autoMode = true } = await req.json();
    
    // Create sandbox environment
    const sandbox = await Sandbox.create({
      apiKey: process.env.E2B_API_KEY,
      template: 'base',
    });
    
    // Create PTY session
    const ptySession = await sandbox.pty.create({
      cols: 100,
      rows: 40,
    });
    
    // Register terminal session for WebSocket tunneling
    const terminalSessionId = registerTerminalSession(sandbox, ptySession.pid, autoMode);
    
    // Generate WebSocket URL
    const protocol = req.headers.get('x-forwarded-proto') || 'http';
    const host = req.headers.get('host') || 'localhost:3000';
    const wsProtocol = protocol === 'https' ? 'wss' : 'ws';
    const wsUrl = `${wsProtocol}://${host}/api/terminal-ws?sessionId=${terminalSessionId}`;
    
    // Upload configuration files to sandbox
    await uploadConfigFiles(sandbox, buildConfig);
    
    // Start build process in background
    startBuildProcess(sandbox, ptySession.pid, buildConfig);
    
    return NextResponse.json({
      success: true,
      terminalSessionId,
      wsUrl,
      ptyPid: ptySession.pid,
      buildId: uuidv4()
    });
  } catch (error: any) {
    console.error('Error creating sandbox:', error);
    return NextResponse.json(
      { success: false, message: error.message || 'Failed to create sandbox' },
      { status: 500 }
    );
  }
}
```

#### 2. `src/lib/pty/terminal-session.ts`
Manages terminal sessions and client connections.

```typescript
export interface TerminalSession {
  sandbox: Sandbox;
  ptyPid: number;
  lastActivity: number;
  clients: Set<WebSocket>;
  autoMode: boolean;
  automationPaused: boolean;
  outputBuffer: string[];
}

const terminalSessions = new Map<string, TerminalSession>();

export function registerTerminalSession(
  sandbox: Sandbox,
  ptyPid: number,
  autoMode: boolean = true
): string {
  const sessionId = uuidv4();
  
  const session: TerminalSession = {
    sandbox,
    ptyPid,
    lastActivity: Date.now(),
    clients: new Set(),
    autoMode,
    automationPaused: false,
    outputBuffer: []
  };
  
  terminalSessions.set(sessionId, session);
  
  // Set up data handler from PTY to clients
  sandbox.pty.onData = async (pid: number, data: Uint8Array) => {
    if (pid === ptyPid) {
      const dataStr = new TextDecoder().decode(data);
      
      // Process through automation if enabled
      if (session.autoMode && !session.automationPaused) {
        const automation = getAutomationForSession(sessionId);
        const response = automation.processOutput(dataStr);
        if (response) {
          await sandbox.pty.sendInput(ptyPid, new TextEncoder().encode(response));
        }
      }
      
      // Send to all connected clients
      for (const client of session.clients) {
        if (client.readyState === WebSocket.OPEN) {
          client.send(dataStr);
        }
      }
      
      // Store in buffer for late-joining clients
      session.outputBuffer.push(dataStr);
      if (session.outputBuffer.length > 1000) {
        session.outputBuffer.shift();
      }
    }
  };
  
  return sessionId;
}
```

#### 3. `src/lib/testflight/eas-build-pattern.ts`
Defines patterns for automating the EAS build process.

```typescript
export function getEasBuildPatterns(config: BuildConfig): TerminalPattern[] {
  return [
    // Git initialization patterns
    {
      pattern: /Initialize a git repository\?/i,
      response: 'y\n',
      description: 'Initialize git repository'
    },
    {
      pattern: /Commit all changes to git\?/i,
      response: 'y\n',
      description: 'Commit changes to git'
    },
    
    // Expo login patterns
    {
      pattern: /Log in with an existing Expo account/i,
      response: '\n',
      description: 'Log in with existing Expo account'
    },
    {
      pattern: /✔ Email or username/i,
      response: `${process.env.EXPO_USERNAME}\n`,
      description: 'Enter Expo username'
    },
    {
      pattern: /✔ Password/i,
      response: `${process.env.EXPO_PASSWORD}\n`,
      description: 'Enter Expo password'
    },
    
    // Apple ID patterns
    {
      pattern: /Apple ID:/i,
      response: `${process.env.APPLE_ID}\n`,
      description: 'Enter Apple ID'
    },
    {
      pattern: /App-specific Password:/i,
      response: `${process.env.APPLE_APP_PASSWORD}\n`,
      description: 'Enter Apple app-specific password'
    },
    
    // Build confirmation patterns
    {
      pattern: /Would you like to proceed\?/i,
      response: 'y\n',
      description: 'Confirm build process'
    },
    {
      pattern: /Do you want to continue\?/i,
      response: 'y\n',
      description: 'Continue with build'
    },
  ];
}
```

#### 4. `src/stores/TestFlightStore.ts`
Manages the state and WebSocket connections for the TestFlight build process.

```typescript
export class TestFlightStore {
  @observable buildProgress: BuildProgress = {
    status: 'idle',
    message: '',
    progress: 0,
    logs: []
  };
  
  @observable terminalSessions = new Map<string, {
    socket: WebSocket | null;
    autoMode: boolean;
  }>();
  
  // Start a new build
  @action
  async startBuild(chatId: string, buildConfig: BuildConfig) {
    try {
      this.setBuildProgress({
        status: 'configuring',
        message: 'Preparing build environment...',
        progress: 5,
        logs: [{
          timestamp: Date.now(),
          message: 'Starting build process',
          type: 'info'
        }]
      });
      
      // Call the terminal-build API to create sandbox and PTY
      const response = await fetch('/api/testflight/terminal-build', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          chatId,
          buildConfig,
          autoMode: true
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Update build progress with terminal info
        this.setBuildProgress({
          ...this.buildProgress,
          buildId: data.buildId,
          terminalSessionId: data.terminalSessionId,
          wsUrl: data.wsUrl,
          ptyPid: data.ptyPid,
          status: 'preparing',
          message: 'Connecting to build terminal...',
          progress: 10
        });
        
        // Connect to the terminal WebSocket
        this.connectTerminal(chatId, {
          onData: this.handleTerminalData,
          onConnect: this.handleTerminalConnect,
          onDisconnect: this.handleTerminalDisconnect,
          onError: this.handleTerminalError
        }, data.wsUrl);
        
        // Start polling for build status
        this.pollBuildStatus(data.buildId);
        
        return data.buildId;
      } else {
        throw new Error(data.message || 'Failed to start build');
      }
    } catch (error: any) {
      this.setBuildProgress({
        ...this.buildProgress,
        status: 'failed',
        message: error.message || 'Failed to start build',
        progress: 0,
        error: error.message
      });
      return null;
    }
  }
  
  // Connect to terminal WebSocket
  @action
  connectTerminal(chatId: string, handlers: TerminalEventHandlers, wsUrl: string) {
    // Close existing connection if any
    this.disconnectTerminal(chatId);
    
    // Create new WebSocket connection
    const socket = new WebSocket(wsUrl);
    
    socket.onopen = () => {
      console.log('Terminal WebSocket connected');
      handlers.onConnect();
    };
    
    socket.onmessage = (event) => {
      handlers.onData(event.data);
    };
    
    socket.onclose = () => {
      console.log('Terminal WebSocket disconnected');
      handlers.onDisconnect();
    };
    
    socket.onerror = (error) => {
      console.error('Terminal WebSocket error:', error);
      handlers.onError('WebSocket connection error');
    };
    
    // Store the socket
    this.terminalSessions.set(chatId, {
      socket,
      autoMode: true
    });
  }
}
```

## Next Steps

After a successful build:

1. Invite testers through the TestFlight tab
2. Monitor tester feedback
3. Iterate on your app based on feedback
4. When ready, submit to the App Store for public release
