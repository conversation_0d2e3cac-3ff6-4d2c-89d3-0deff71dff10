#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to analyze the OTHER_REFERENCE category in more detail
 * Usage: npx tsx scripts/analyze-other-references.ts
 */

// Load environment variables
require('dotenv').config({path: ".env.local"});

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { message } from '../src/lib/db/schema';
import { eq, desc } from 'drizzle-orm';
import * as fs from 'fs';
import chalk from 'chalk';

// Define patterns for the OTHER_REFERENCE category
const OTHER_REFERENCE_PATTERNS = {
  patterns: [
    'missing',
    'not found'
  ]
};

// Define potential subcategories to look for
const POTENTIAL_SUBCATEGORIES = [
  {
    name: 'NAVIGATION_REFERENCE',
    patterns: [
      'navigation',
      'navigator',
      'route',
      'screen',
      'navigationcontainer'
    ]
  },
  {
    name: 'API_REFERENCE',
    patterns: [
      'api',
      'fetch',
      'request',
      'response',
      'endpoint',
      'fetching'
    ]
  },
  {
    name: 'FILE_REFERENCE',
    patterns: [
      'file',
      'path',
      'directory',
      'import',
      'export',
      'module'
    ]
  },
  {
    name: 'DEPENDENCY_REFERENCE',
    patterns: [
      'dependency',
      'package',
      'library',
      'node_modules',
      'npm',
      'yarn'
    ]
  },
  {
    name: 'RESOURCE_REFERENCE',
    patterns: [
      'image',
      'asset',
      'icon',
      'resource',
      'media',
      'font'
    ]
  }
];

/**
 * Extract text from message content
 */
function extractText(content: any): string {
  try {
    if (typeof content === 'string') return content;
    if (Array.isArray(content)) {
      return content
        .filter(item => item && typeof item === 'object' && 'text' in item)
        .map(item => item.text)
        .join(' ');
    }
    if (typeof content === 'object') return JSON.stringify(content);
    return String(content);
  } catch (e) {
    return String(content);
  }
}

/**
 * Determine if a message belongs to the OTHER_REFERENCE category
 */
function isOtherReferenceError(text: string): boolean {
  text = text.toLowerCase();
  
  // Check if it contains any of the OTHER_REFERENCE patterns
  for (const pattern of OTHER_REFERENCE_PATTERNS.patterns) {
    if (text.includes(pattern.toLowerCase())) {
      return true;
    }
  }
  
  return false;
}

/**
 * Categorize an OTHER_REFERENCE error into a potential subcategory
 */
function categorizeOtherReference(text: string): string {
  text = text.toLowerCase();
  
  for (const subcategory of POTENTIAL_SUBCATEGORIES) {
    for (const pattern of subcategory.patterns) {
      if (text.includes(pattern.toLowerCase())) {
        return subcategory.name;
      }
    }
  }
  
  return 'UNCATEGORIZED_REFERENCE';
}

/**
 * Extract common words from a set of texts
 */
function extractCommonWords(texts: string[]): string[] {
  // Count word frequencies
  const wordCounts: Record<string, number> = {};
  
  texts.forEach(text => {
    // Clean and normalize text
    const cleanText = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')  // Replace punctuation with spaces
      .replace(/\s+/g, ' ')      // Normalize whitespace
      .trim();
    
    // Split into words
    const words = cleanText.split(' ');
    
    // Count unique words in this text
    const uniqueWords = new Set(words);
    uniqueWords.forEach(word => {
      // Only count words of reasonable length
      if (word.length > 3 && word.length < 20) {
        wordCounts[word] = (wordCounts[word] || 0) + 1;
      }
    });
  });
  
  // Find words that appear in multiple texts
  return Object.entries(wordCounts)
    .filter(([word, count]) => {
      // Filter out common English words and programming terms
      const commonWords = [
        'error', 'the', 'and', 'that', 'have', 'for', 'not', 'with', 'this',
        'but', 'from', 'they', 'would', 'what', 'when', 'there', 'been',
        'encountered', 'facing', 'issue', 'problem', 'getting', 'please', 'help',
        'fix', 'code', 'function', 'variable', 'object', 'array', 'string',
        'number', 'boolean', 'null', 'undefined', 'true', 'false'
      ];
      
      return !commonWords.includes(word) && count >= Math.max(2, texts.length * 0.2);
    })
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20)
    .map(([word]) => word);
}

/**
 * Extract error details from text
 */
function extractErrorDetails(text: string): string {
  // Try to extract the most relevant part of the error
  
  // Look for quoted error messages
  const quotedErrors = text.match(/"([^"]*error[^"]*)"/gi) || [];
  if (quotedErrors.length > 0) {
    return quotedErrors.join('\n');
  }
  
  // Look for error: followed by text
  const errorColonPattern = /error:?\s+([^.!?\n]+)/gi;
  const errorColonMatches = text.match(errorColonPattern) || [];
  if (errorColonMatches.length > 0) {
    return errorColonMatches.join('\n');
  }
  
  // Look for common error phrases
  const errorPhrases = [
    /failed to\s+([^.!?\n]+)/gi,
    /cannot\s+([^.!?\n]+)/gi,
    /unable to\s+([^.!?\n]+)/gi,
    /not found:?\s+([^.!?\n]+)/gi,
    /missing\s+([^.!?\n]+)/gi
  ];
  
  for (const pattern of errorPhrases) {
    const matches = text.match(pattern) || [];
    if (matches.length > 0) {
      return matches.join('\n');
    }
  }
  
  // If we couldn't extract a specific part, return a snippet
  return text.substring(0, 300) + (text.length > 300 ? '...' : '');
}

/**
 * Main function to analyze OTHER_REFERENCE errors
 */
async function analyzeOtherReferences() {
  console.log(chalk.blue('Analyzing OTHER_REFERENCE errors in detail...'));

  // Check for DATABASE_URL
  const DATABASE_URL = process.env.POSTGRES_URL;
  if (!DATABASE_URL) {
    console.error(chalk.red('POSTGRES_URL environment variable is not set'));
    process.exit(1);
  }

  // Create database connection
  const client = postgres(DATABASE_URL);
  const db = drizzle(client);

  try {
    // Fetch error messages
    console.log(chalk.yellow('Fetching error messages from database...'));
    
    const errorMessages = await db
      .select({
        id: message.id,
        content: message.content,
        createdAt: message.createdAt
      })
      .from(message)
      .where(eq(message.role, 'user'))
      .orderBy(desc(message.createdAt))
      .limit(1000);
    
    // Filter messages with OTHER_REFERENCE error content
    const otherReferenceErrors = errorMessages.filter(msg => {
      const text = extractText(msg.content);
      return isOtherReferenceError(text);
    });
    
    console.log(chalk.green(`Found ${otherReferenceErrors.length} OTHER_REFERENCE errors out of ${errorMessages.length} total messages`));
    
    // Process the OTHER_REFERENCE errors
    console.log(chalk.yellow('Analyzing OTHER_REFERENCE errors...'));
    
    // Group by potential subcategories
    const subcategoryGroups: Record<string, { count: number, examples: any[] }> = {};
    
    // Initialize groups
    POTENTIAL_SUBCATEGORIES.forEach(subcategory => {
      subcategoryGroups[subcategory.name] = { count: 0, examples: [] };
    });
    subcategoryGroups['UNCATEGORIZED_REFERENCE'] = { count: 0, examples: [] };
    
    // Categorize each error
    otherReferenceErrors.forEach(msg => {
      const text = extractText(msg.content);
      const subcategory = categorizeOtherReference(text);
      const details = extractErrorDetails(text);
      const date = new Date(msg.createdAt).toISOString().split('T')[0];
      
      // Increment count
      subcategoryGroups[subcategory].count++;
      
      // Add to examples (up to 5 per subcategory)
      if (subcategoryGroups[subcategory].examples.length < 5) {
        subcategoryGroups[subcategory].examples.push({
          id: msg.id,
          date,
          details,
          text
        });
      }
    });
    
    // Output subcategory counts
    console.log('\n' + chalk.bold.blue('=== OTHER_REFERENCE Subcategories ==='));
    
    const sortedSubcategories = Object.entries(subcategoryGroups)
      .sort((a, b) => b[1].count - a[1].count);
    
    sortedSubcategories.forEach(([subcategory, data]) => {
      const percentage = ((data.count / otherReferenceErrors.length) * 100).toFixed(1);
      console.log(chalk.bold(`${subcategory}: ${data.count} (${percentage}%)`));
    });
    
    // Analyze each subcategory for common words
    console.log('\n' + chalk.bold.blue('=== Common Terms by Subcategory ==='));
    
    for (const [subcategory, data] of sortedSubcategories) {
      if (data.count === 0) continue;
      
      console.log(chalk.bold(`\n${subcategory}:`));
      
      // Extract all texts for this subcategory
      const texts = data.examples.map(ex => ex.text);
      
      // Find common words
      const commonWords = extractCommonWords(texts);
      
      if (commonWords.length > 0) {
        console.log(chalk.yellow(`Common terms: ${commonWords.join(', ')}`));
      } else {
        console.log(chalk.yellow('No common terms identified'));
      }
      
      // Show examples
      console.log(chalk.gray('Examples:'));
      data.examples.slice(0, 3).forEach((example, i) => {
        console.log(chalk.gray(`  ${i+1}. [${example.date}] ${example.details.substring(0, 100)}...`));
      });
    }
    
    // Prepare output content
    let outputContent = '# OTHER_REFERENCE Error Analysis\n\n';
    outputContent += `Total OTHER_REFERENCE Errors: ${otherReferenceErrors.length}\n\n`;
    
    sortedSubcategories.forEach(([subcategory, data]) => {
      if (data.count === 0) return;
      
      const percentage = ((data.count / otherReferenceErrors.length) * 100).toFixed(1);
      outputContent += `## ${subcategory}: ${data.count} (${percentage}%)\n\n`;
      
      // Add common terms
      const texts = data.examples.map(ex => ex.text);
      const commonWords = extractCommonWords(texts);
      
      if (commonWords.length > 0) {
        outputContent += '### Common Terms:\n';
        outputContent += `${commonWords.join(', ')}\n\n`;
      }
      
      // Add examples
      outputContent += '### Examples:\n';
      data.examples.forEach((example, index) => {
        outputContent += `#### Example ${index + 1} [${example.date}]:\n\`\`\`\n${example.details}\n\`\`\`\n\n`;
      });
      outputContent += '\n';
    });
    
    // Write to file
    const outputPath = './other-reference-breakdown.md';
    fs.writeFileSync(outputPath, outputContent);
    console.log(chalk.green(`\nDetailed breakdown saved to ${outputPath}`));
    
    // Also save as JSON for potential programmatic use
    const jsonOutput = {
      totalErrors: otherReferenceErrors.length,
      subcategories: Object.entries(subcategoryGroups)
        .filter(([_, data]) => data.count > 0)
        .map(([name, data]) => ({
          name,
          count: data.count,
          percentage: ((data.count / otherReferenceErrors.length) * 100).toFixed(1),
          examples: data.examples.map(ex => ({
            id: ex.id,
            date: ex.date,
            details: ex.details
          }))
        }))
    };
    
    const jsonPath = './other-reference-breakdown.json';
    fs.writeFileSync(jsonPath, JSON.stringify(jsonOutput, null, 2));
    console.log(chalk.green(`JSON data saved to ${jsonPath}`));
    
    console.log(chalk.bold.green('\nAnalysis complete!'));
    
  } catch (error) {
    console.error(chalk.red('Error during analysis:'), error);
    process.exit(1);
  } finally {
    // Close database connection
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the analysis
analyzeOtherReferences();
