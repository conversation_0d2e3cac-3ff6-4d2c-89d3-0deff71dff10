// #!/usr/bin/env node
//
// /**
//  * <PERSON><PERSON><PERSON> to analyze chat messages and sentiment for a given chat ID
//  * Usage: npx tsx scripts/analyze-chat-sentiment.ts --chatId <chat-id>
//  *
//  * This script:
//  * 1. Retrieves all messages for a given chat ID
//  * 2. Analyzes sentiment using natural language processing
//  * 3. Maps the conversation flow and identifies potential frustration points
//  * 4. Generates recommendations for improving AI responses
//  */
//
// // Load environment variables
// require('dotenv').config({path: ".env.local"});
//
// import { drizzle } from 'drizzle-orm/postgres-js';
// import postgres from 'postgres';
// import { chat, message } from '../src/lib/db/schema';
// import { eq, desc, asc } from 'drizzle-orm';
// import * as natural from 'natural';
// import * as fs from 'fs';
// import * as yargs from 'yargs';
// import { hideBin } from 'yargs/helpers';
// import chalk from 'chalk';
//
// // Define sentiment analyzer
// const analyzer = new natural.SentimentAnalyzer('English', natural.PorterStemmer, 'afinn');
//
// // Parse command line arguments
// const argv = yargs.default(hideBin(process.argv))
//   .option('chatId', {
//     alias: 'c',
//     description: 'Chat ID to analyze',
//     type: 'string',
//     demandOption: true
//   })
//   .option('threshold', {
//     alias: 't',
//     description: 'Frustration threshold score (negative value, default: -0.3)',
//     type: 'number',
//     default: -0.3
//   })
//   .option('window', {
//     alias: 'w',
//     description: 'Number of messages to analyze for trend detection',
//     type: 'number',
//     default: 5
//   })
//   .help()
//   .alias('help', 'h')
//   .parseSync();
//
// // Frustration indicators in user messages
// const frustrationIndicators = [
//   'not working', 'doesn\'t work', 'still not', 'error', 'issue', 'problem',
//   'wrong', 'incorrect', 'failed', 'doesn\'t fix', 'didn\'t fix', 'same issue',
//   'again', 'still', 'frustrated', 'annoying', 'annoyed', 'fix', 'broken'
// ];
//
// // Technical complexity indicators
// const complexityIndicators = [
//   'platform', 'web', 'ios', 'android', 'compatibility', 'cross-platform',
//   'layout', 'styling', 'component', 'context', 'provider', 'hook', 'useEffect',
//   'useState', 'ref', 'animation', 'modal', 'dropdown', 'responsive'
// ];
//
// // Types for our analysis
// interface SentimentResult {
//   score: number;
//   comparative: number;
//   assessment: 'positive' | 'negative' | 'neutral' | 'frustrated';
//   frustrationIndicators: number;
//   complexityIndicators: number;
// }
//
// interface ProcessedMessage {
//   id: string;
//   chatId: string;
//   role: string;
//   message_text: string;
//   createdAt: Date;
//   userId: string | null;
//   remoteProvider: string | null;
//   sentiment: SentimentResult;
// }
//
// interface SentimentTrend {
//   startIndex: number;
//   endIndex: number;
//   avgSentiment: number;
//   sentimentDelta: number;
//   messages: ProcessedMessage[];
//   severity: 'high' | 'medium' | 'low';
// }
//
// interface Recommendation {
//   type: 'general' | 'specific';
//   priority: 'high' | 'medium' | 'low';
//   issue: string;
//   recommendation: string;
//   messageIndexes?: number[];
// }
//
// /**
//  * Analyzes text sentiment using AFINN lexicon
//  * @param text - Text to analyze
//  * @returns Sentiment analysis results
//  */
// function analyzeSentiment(text: string | null): SentimentResult {
//   if (!text) return { score: 0, comparative: 0, assessment: 'neutral', frustrationIndicators: 0, complexityIndicators: 0 };
//
//   // Clean text for analysis
//   const cleanText = text
//     .replace(/```[\s\S]*?```/g, '') // Remove code blocks
//     .replace(/<.*?>/g, '')          // Remove HTML tags
//     .replace(/\s+/g, ' ')           // Normalize whitespace
//     .trim();
//
//   // Calculate raw sentiment score
//   const tokens = cleanText.split(' ');
//   const score = analyzer.getSentiment(tokens);
//
//   // Calculate comparative score (normalized by text length)
//   const comparative = tokens.length > 0 ? score / tokens.length : 0;
//
//   // Check for frustration indicators
//   const frustrationCount = frustrationIndicators.reduce((count, indicator) => {
//     return count + (cleanText.toLowerCase().includes(indicator.toLowerCase()) ? 1 : 0);
//   }, 0);
//
//   // Check for technical complexity
//   const complexityCount = complexityIndicators.reduce((count, indicator) => {
//     return count + (cleanText.toLowerCase().includes(indicator.toLowerCase()) ? 1 : 0);
//   }, 0);
//
//   // Determine sentiment assessment
//   let assessment: 'positive' | 'negative' | 'neutral' | 'frustrated' = 'neutral';
//   if (comparative > 0.2) assessment = 'positive';
//   else if (comparative < -0.2) assessment = 'negative';
//
//   // Adjust assessment based on frustration indicators
//   if (frustrationCount >= 2 && assessment !== 'positive') {
//     assessment = 'frustrated';
//   }
//
//   return {
//     score,
//     comparative,
//     assessment,
//     frustrationIndicators: frustrationCount,
//     complexityIndicators: complexityCount
//   };
// }
//
// /**
//  * Detects sentiment trends over a window of messages
//  * @param messages - Array of message objects with sentiment data
//  * @param windowSize - Size of the sliding window
//  * @returns Array of trend objects
//  */
// function detectSentimentTrends(messages: ProcessedMessage[], windowSize: number): SentimentTrend[] {
//   const trends: SentimentTrend[] = [];
//
//   for (let i = windowSize; i < messages.length; i++) {
//     const window = messages.slice(i - windowSize, i);
//     const userMessages = window.filter(m => m.role === 'user');
//
//     // Skip windows with no user messages
//     if (userMessages.length === 0) continue;
//
//     // Calculate average sentiment in window
//     const avgSentiment = userMessages.reduce((sum, m) => sum + m.sentiment.comparative, 0) / userMessages.length;
//
//     // Detect declining sentiment trend
//     const firstMsg = userMessages[0];
//     const lastMsg = userMessages[userMessages.length - 1];
//     const sentimentDelta = lastMsg.sentiment.comparative - firstMsg.sentiment.comparative;
//
//     if (sentimentDelta < -0.3 || (avgSentiment < argv.threshold && userMessages.length >= 2)) {
//       trends.push({
//         startIndex: i - windowSize,
//         endIndex: i - 1,
//         avgSentiment,
//         sentimentDelta,
//         messages: window,
//         severity: avgSentiment < -0.4 ? 'high' : 'medium'
//       });
//     }
//   }
//
//   return trends;
// }
//
// /**
//  * Generates recommendations based on sentiment analysis
//  * @param messages - Array of message objects with sentiment data
//  * @param trends - Array of detected sentiment trends
//  * @returns Array of recommendation objects
//  */
// function generateRecommendations(messages: ProcessedMessage[], trends: SentimentTrend[]): Recommendation[] {
//   const recommendations: Recommendation[] = [];
//
//   // Analyze overall conversation patterns
//   const userMessages = messages.filter(m => m.role === 'user');
//   const assistantMessages = messages.filter(m => m.role === 'assistant');
//
//   const avgUserSentiment = userMessages.reduce((sum, m) => sum + m.sentiment.comparative, 0) / userMessages.length;
//   const frustrationPoints = trends.filter(t => t.severity === 'high').length;
//
//   // Check for repeated issues
//   const repeatedIssues = new Map<string, number>();
//   userMessages.forEach(msg => {
//     if (msg.sentiment.assessment === 'frustrated') {
//       const text = msg.message_text.toLowerCase();
//       complexityIndicators.forEach(indicator => {
//         if (text.includes(indicator.toLowerCase())) {
//           repeatedIssues.set(indicator, (repeatedIssues.get(indicator) || 0) + 1);
//         }
//       });
//     }
//   });
//
//   // Sort issues by frequency
//   const sortedIssues = [...repeatedIssues.entries()].sort((a, b) => b[1] - a[1]);
//
//   // Generate general recommendations
//   if (avgUserSentiment < -0.2) {
//     recommendations.push({
//       type: 'general',
//       priority: 'high',
//       issue: 'Overall negative sentiment detected',
//       recommendation: 'Review AI response strategy for this type of conversation'
//     });
//   }
//
//   if (frustrationPoints >= 2) {
//     recommendations.push({
//       type: 'general',
//       priority: 'high',
//       issue: 'Multiple frustration points detected',
//       recommendation: 'Implement more proactive problem detection and resolution'
//     });
//   }
//
//   // Generate specific recommendations based on repeated issues
//   sortedIssues.slice(0, 3).forEach(([issue, count]) => {
//     if (count >= 2) {
//       recommendations.push({
//         type: 'specific',
//         priority: count >= 3 ? 'high' : 'medium',
//         issue: `Repeated issues with "${issue}" (${count} occurrences)`,
//         recommendation: `Enhance AI training for ${issue}-related problems`
//       });
//     }
//   });
//
//   // Generate recommendations for each high-severity trend
//   trends.filter(t => t.severity === 'high').forEach(trend => {
//     const trendMessages = trend.messages.map(m => m.message_text).join(' ').toLowerCase();
//
//     // Identify the most likely issue in this trend
//     const issueIndicators = [...frustrationIndicators, ...complexityIndicators];
//     const detectedIssues = issueIndicators.filter(i => trendMessages.includes(i.toLowerCase()));
//
//     if (detectedIssues.length > 0) {
//       recommendations.push({
//         type: 'specific',
//         priority: 'high',
//         issue: `Frustration detected around: ${detectedIssues.slice(0, 3).join(', ')}`,
//         recommendation: 'Change approach when these topics arise in conversation',
//         messageIndexes: trend.messages.map(m => messages.indexOf(m))
//       });
//     }
//   });
//
//   return recommendations;
// }
//
// /**
//  * Helper function to get emoji based on sentiment score
//  */
// function getSentimentEmoji(score: number): string {
//   if (score > 0.3) return '😀';
//   if (score > 0.1) return '🙂';
//   if (score > -0.1) return '😐';
//   if (score > -0.3) return '😕';
//   return '😞';
// }
//
// /**
//  * Main function to analyze chat sentiment
//  */
// async function analyzeChatSentiment() {
//   const chatId = argv.chatId;
//   console.log(chalk.blue(`Analyzing sentiment for chat ID: ${chatId}`));
//
//   // Check for DATABASE_URL
//   const DATABASE_URL = process.env.POSTGRES_URL;
//   if (!DATABASE_URL) {
//     console.error(chalk.red('POSTGRES_URL environment variable is not set'));
//     process.exit(1);
//   }
//
//   // Create database connection
//   const client = postgres(DATABASE_URL);
//   const db = drizzle(client);
//
//   try {
//     // Fetch chat details
//     const chatData = await db.select()
//       .from(chat)
//       .where(eq(chat.id, chatId))
//       .limit(1);
//
//     if (chatData.length === 0) {
//       throw new Error(`Chat not found with ID: ${chatId}`);
//     }
//
//     console.log(chalk.green(`Found chat: "${chatData[0].title}"`));
//
//     // Fetch all messages for the chat
//     const messagesData = await db.select()
//       .from(message)
//       .where(eq(message.chatId, chatId))
//       .orderBy(asc(message.createdAt));
//
//     console.log(chalk.green(`Retrieved ${messagesData.length} messages`));
//
//     // Process messages and analyze sentiment
//     const processedMessages: ProcessedMessage[] = messagesData.map(msg => {
//       // Extract text content from the message
//       let messageText = '';
//       if (msg.content && Array.isArray(msg.content) && msg.content.length > 0) {
//         const textContent = msg.content.find((item: any) => item.type === 'text');
//         messageText = textContent ? textContent.text : '';
//       }
//
//       // Analyze sentiment
//       const sentiment = analyzeSentiment(messageText);
//
//       return {
//         id: msg.id,
//         chatId: msg.chatId,
//         role: msg.role,
//         message_text: messageText,
//         createdAt: msg.createdAt,
//         userId: msg.userId,
//         remoteProvider: msg.remoteProvider,
//         sentiment
//       };
//     });
//
//     // Detect sentiment trends
//     const trends = detectSentimentTrends(processedMessages, argv.window);
//
//     // Generate recommendations
//     const recommendations = generateRecommendations(processedMessages, trends);
//
//     // Output results
//     console.log('\n' + chalk.bold.blue('=== Chat Sentiment Analysis ==='));
//     console.log(chalk.bold(`Chat: ${chatData[0].title}`));
//     console.log(chalk.bold(`Total Messages: ${processedMessages.length}`));
//
//     // Output sentiment summary
//     const userMessages = processedMessages.filter(m => m.role === 'user');
//     const assistantMessages = processedMessages.filter(m => m.role === 'assistant');
//
//     const avgUserSentiment = userMessages.reduce((sum, m) => sum + m.sentiment.comparative, 0) / userMessages.length;
//     console.log(chalk.bold('\nSentiment Summary:'));
//     console.log(`Average User Sentiment: ${avgUserSentiment.toFixed(2)} ${getSentimentEmoji(avgUserSentiment)}`);
//
//     // Count sentiment categories
//     const sentimentCounts = userMessages.reduce((counts: Record<string, number>, msg) => {
//       counts[msg.sentiment.assessment] = (counts[msg.sentiment.assessment] || 0) + 1;
//       return counts;
//     }, {});
//
//     console.log('\nUser Message Sentiment Distribution:');
//     Object.entries(sentimentCounts).forEach(([sentiment, count]) => {
//       const percentage = ((count / userMessages.length) * 100).toFixed(1);
//       let color = chalk.gray;
//       if (sentiment === 'positive') color = chalk.green;
//       if (sentiment === 'negative') color = chalk.yellow;
//       if (sentiment === 'frustrated') color = chalk.red;
//       console.log(color(`  ${sentiment}: ${count} (${percentage}%)`));
//     });
//
//     // Output frustration points
//     if (trends.length > 0) {
//       console.log(chalk.bold.yellow('\nPotential Frustration Points:'));
//       trends.forEach((trend, i) => {
//         const severity = trend.severity === 'high' ? chalk.red('High') : chalk.yellow('Medium');
//         console.log(`${i + 1}. Severity: ${severity}, Messages: ${trend.startIndex + 1}-${trend.endIndex + 1}`);
//
//         // Show the messages in this trend
//         trend.messages.forEach((msg, j) => {
//           const prefix = msg.role === 'user' ? chalk.bold.blue('User: ') : chalk.green('AI: ');
//           const preview = msg.message_text.substring(0, 100) + (msg.message_text.length > 100 ? '...' : '');
//           console.log(`   ${prefix}${preview}`);
//         });
//         console.log('');
//       });
//     } else {
//       console.log(chalk.green('\nNo significant frustration points detected.'));
//     }
//
//     // Output recommendations
//     if (recommendations.length > 0) {
//       console.log(chalk.bold.blue('\nRecommendations:'));
//       recommendations.forEach((rec, i) => {
//         const priority = rec.priority === 'high' ? chalk.red('High') : chalk.yellow('Medium');
//         console.log(`${i + 1}. [${priority}] ${chalk.bold(rec.issue)}`);
//         console.log(`   ${rec.recommendation}`);
//       });
//     }
//
//     // Generate JSON output file
//     const outputData = {
//       chatId,
//       chatTitle: chatData[0].title,
//       analysisDate: new Date().toISOString(),
//       messageCount: processedMessages.length,
//       sentimentSummary: {
//         averageUserSentiment: avgUserSentiment,
//         distribution: sentimentCounts
//       },
//       frustrationPoints: trends,
//       recommendations
//     };
//
//     const outputPath = `./sentiment-analysis-${chatId.substring(0, 8)}.json`;
//     fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2));
//
//     console.log(chalk.bold.green(`\nAnalysis complete! Full results saved to ${outputPath}`));
//
//   } catch (error) {
//     console.error(chalk.red('Error analyzing chat sentiment:'), error);
//     process.exit(1);
//   } finally {
//     // Close database connection
//     await client.end();
//     console.log('Database connection closed');
//   }
// }
//
// // Run the analysis
// analyzeChatSentiment();
