// This script can be run directly with Node.js
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import {eq, and, desc} from 'drizzle-orm';
import { generateUUID } from '../src/lib/utils';

import {
  chat,
  projects,
  message,
  vote,
  fileState,
  deployments,
  type Chat,
  type Project
} from '../src/lib/db/schema';

// Initialize DB connection
if (!process.env.POSTGRES_URL) {
  console.error('POSTGRES_URL environment variable is required');
  process.exit(1);
}
const client = postgres(process.env.POSTGRES_URL);
const db = drizzle(client);

/**
 * Migrates existing chats to the project-based structure
 * This script can be run on demand to convert chats to projects
 */
export async function migrateChatsToProjects() {
  console.log('Starting migration of chats to projects...');
  
  try {
    // Get all chats
    const allChats = await db.select().from(chat).orderBy(desc(chat.createdAt));
    console.log(`Found ${allChats.length} chats to migrate`);
    
    // Process each chat
    for (const chatItem of allChats) {
      try {
        // Skip if chat already has a projectId
        if (chatItem.projectId) {
          console.log(`Chat ${chatItem.id} already has a project (${chatItem.projectId}), skipping`);
          continue;
        }
        
        // Create a project for this chat
        const projectId = generateUUID();
        console.log(`Creating project ${projectId} for chat ${chatItem.id}`);
        
        // Generate a URL-friendly slug from the title
        const slug = chatItem.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-|-$/g, '');

        // Generate a scheme from the slug
        const scheme = slug.replace(/-/g, '');

        // Generate default bundle identifiers based on the slug
        const bundleIdentifier = `life.magically.${slug.replace(/-/g, '')}`;
        const packageName = bundleIdentifier;

        // Insert the project with Expo-specific fields
        await db.insert(projects).values({
          id: projectId,
          appName: chatItem.title, // Initially use the same title for app name
          slug: slug,
          scheme: scheme,
          bundleIdentifier: bundleIdentifier,
          packageName: packageName,
          description: `${chatItem.title} - Created from chat`,
          userId: chatItem.userId,
          connectionId: chatItem.connectionId,
          visibility: chatItem.visibility,
          createdAt: chatItem.createdAt,
          updatedAt: chatItem.updatedAt,
          // Move Supabase details to project level
          supabaseProjectId: chatItem.supabaseProjectId,
          supabaseAnonKey: chatItem.supabaseAnonKey,
          supabaseServiceKey: chatItem.supabaseServiceKey,
        });
        
        // Update the chat to reference the new project
        await db.update(chat)
          .set({ 
            projectId: projectId,
          })
          .where(eq(chat.id, chatItem.id));
        
        // Update messages to reference the project
        const chatMessages = await db.select().from(message).where(eq(message.chatId, chatItem.id));
        for (const msg of chatMessages) {
          await db.update(message)
            .set({ projectId: projectId })
            .where(eq(message.id, msg.id));
        }
        console.log(`Updated ${chatMessages.length} messages for chat ${chatItem.id}`);
        
        // Update votes to reference the project
        const chatVotes = await db.select().from(vote).where(eq(vote.chatId, chatItem.id));
        for (const voteItem of chatVotes) {
          await db.update(vote)
            .set({ projectId: projectId })
            .where(and(
              eq(vote.chatId, voteItem.chatId),
              eq(vote.messageId, voteItem.messageId)
            ));
        }
        console.log(`Updated ${chatVotes.length} votes for chat ${chatItem.id}`);
        
        // Update file states to reference the project
        const chatFileStates = await db.select().from(fileState).where(eq(fileState.chatId, chatItem.id));
        for (const fileStateItem of chatFileStates) {
          await db.update(fileState)
            .set({ projectId: projectId })
            .where(eq(fileState.id, fileStateItem.id));
        }
        console.log(`Updated ${chatFileStates.length} file states for chat ${chatItem.id}`);
        
        // // Update deployments to reference the project
        // const chatDeployments = await db.select().from(deployments).where(eq(deployments.projectId, chatItem.id));
        // for (const deployment of chatDeployments) {
        //   await db.update(deployments)
        //     .set({ projectId: projectId })
        //     .where(eq(deployments.id, deployment.id));
        // }
        // console.log(`Updated ${chatDeployments.length} deployments for chat ${chatItem.id}`);
        
        console.log(`Successfully migrated chat ${chatItem.id} to project ${projectId}`);
      } catch (error) {
        console.error(`Error migrating chat ${chatItem.id}:`, error);
      }
    }
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close the database connection
    await client.end();
  }
}

/**
 * Create a CLI script that can be run directly
 */
if (require.main === module) {
  migrateChatsToProjects()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

// For CLI usage
if (require.main === module) {
  migrateChatsToProjects()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}
