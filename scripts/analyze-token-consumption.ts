#!/usr/bin/env node

/**
 * <PERSON>ript to analyze token consumption and costs
 * Usage: npx tsx scripts/analyze-token-consumption.ts
 */
import dotenv from 'dotenv';

// Load environment variables
require('dotenv').config({path: ".env.local"});

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { tokenConsumption } from '../src/lib/db/schema';
import { avg, sum } from 'drizzle-orm';



const DATABASE_URL = process.env.POSTGRES_URL;
if (!DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
}

// Create a new database connection
const client = postgres(DATABASE_URL);
const db = drizzle(client);

async function analyzeTokenConsumption() {
    try {
        console.log('Starting token consumption analysis...');
        
        // Get all token consumption records with created_at
        const records = await db
            .select()
            .from(tokenConsumption);

        // Group records by week
        const weeklyStats = records.reduce((acc, r) => {
            const date = new Date(r.createdAt);
            const weekStart = new Date(date.setDate(date.getDate() - date.getDay())).toISOString().split('T')[0];
            
            if (!acc[weekStart]) {
                acc[weekStart] = {
                    count: 0,
                    totalCost: 0,
                    cachingDiscount: 0,
                    modelStats: {}
                };
            }

            const week = acc[weekStart];
            week.count++;
            week.totalCost += r.totalCost || 0;
            week.cachingDiscount += r.cacheDiscountPercent || 0;

            const model = r.model || 'unknown';
            if (!week.modelStats[model]) {
                week.modelStats[model] = {
                    count: 0,
                    totalCost: 0
                };
            }
            week.modelStats[model].count++;
            week.modelStats[model].totalCost += r.totalCost || 0;

            return acc;
        }, {} as Record<string, {
            count: number;
            totalCost: number;
            cachingDiscount: number;
            modelStats: Record<string, { count: number; totalCost: number }>
        }>);

        // Display weekly statistics
        Object.entries(weeklyStats)
            .sort(([a], [b]) => a.localeCompare(b))
            .forEach(([week, stats]) => {
                console.log('\nWeek Starting:', week);
                console.log('------------------');
                console.log(`Total Messages: ${stats.count}`);
                console.log(`Total Cost: $${stats.totalCost.toFixed(2)}`);
                console.log(`Average Cost per Message: $${(stats.totalCost / stats.count).toFixed(4)}`);
                console.log(`Average Caching Discount: ${(stats.cachingDiscount / stats.count).toFixed(2)}%`);

                console.log('\nModel Breakdown:');
                Object.entries(stats.modelStats).forEach(([model, modelStats]) => {
                    console.log(`  ${model}:`);
                    console.log(`    Messages: ${modelStats.count}`);
                    console.log(`    Average Cost: $${(modelStats.totalCost / modelStats.count).toFixed(4)}`);
                    console.log(`    Total Cost: $${modelStats.totalCost.toFixed(2)}`);
                });
            });

        await client.end();
        console.log('Database connection closed');
        process.exit(0);
    } catch (error) {
        console.error('Analysis failed:', error);
        // Attempt to close the connection even on error
        client.end().finally(() => {
            process.exit(1);
        });
    }
}

// Run the analysis function
analyzeTokenConsumption()
    .then(() => {
        console.log('Script completed successfully');
        return client.end();
    })
    .then(() => {
        console.log('Database connection closed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error during analysis:', error);
        client.end().finally(() => {
            process.exit(1);
        });
    });
