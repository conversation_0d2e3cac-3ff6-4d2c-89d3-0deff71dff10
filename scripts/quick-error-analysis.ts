#!/usr/bin/env node

/**
 * <PERSON>ript to quickly analyze error messages and extract common patterns
 * Usage: npx tsx scripts/quick-error-analysis.ts --limit <number-of-messages>
 */

// Load environment variables
require('dotenv').config({path: ".env.local"});

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { message } from '../src/lib/db/schema';
import { eq, desc, ilike, or, and } from 'drizzle-orm';
import * as yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import chalk from 'chalk';

// Parse command line arguments
const argv = yargs.default(hideBin(process.argv))
  .option('limit', {
    alias: 'l',
    description: 'Maximum number of messages to analyze',
    type: 'number',
    default: 200
  })
  .help()
  .alias('help', 'h')
  .parseSync();

/**
 * Extract text from message content
 */
function extractText(content: any): string {
  try {
    if (typeof content === 'string') return content;
    if (Array.isArray(content)) {
      return content
        .filter(item => item && typeof item === 'object' && 'text' in item)
        .map(item => item.text)
        .join(' ');
    }
    if (typeof content === 'object') return JSON.stringify(content);
    return String(content);
  } catch (e) {
    return String(content);
  }
}

/**
 * Extract error type from text
 */
function extractErrorType(text: string): string {
  text = text.toLowerCase();
  
  // Database errors
  if (text.includes('database') || 
      text.includes('sql') || 
      text.includes('relation') || 
      text.includes('column') || 
      text.includes('public.') || 
      text.includes('42p01') ||
      text.includes('supabase')) {
    return 'DATABASE_ERROR';
  }
  
  // Module errors
  if (text.includes('unable to resolve module') || 
      text.includes('cannot find module') || 
      text.includes('module not found') ||
      text.includes('no default export') ||
      text.includes('import') && text.includes('export')) {
    return 'MODULE_ERROR';
  }
  
  // React/UI errors
  if (text.includes('element type is invalid') || 
      text.includes('expected a string') || 
      text.includes('property doesn\'t exist') ||
      text.includes('react') ||
      text.includes('component') ||
      text.includes('jsx') ||
      text.includes('tsx') ||
      text.includes('render')) {
    return 'UI_ERROR';
  }
  
  // Hook errors
  if (text.includes('invalid hook call') || 
      text.includes('hooks can only be called') || 
      text.includes('must be used within') ||
      text.includes('provider') ||
      text.includes('context')) {
    return 'HOOK_ERROR';
  }
  
  // Navigation errors
  if (text.includes('navigation') || 
      text.includes('navigator') || 
      text.includes('route') || 
      text.includes('screen')) {
    return 'NAVIGATION_ERROR';
  }
  
  // Type errors
  if (text.includes('undefined') || 
      text.includes('null') || 
      text.includes('is not a function') ||
      text.includes('cannot read property') ||
      text.includes('type')) {
    return 'TYPE_ERROR';
  }
  
  // Syntax errors
  if (text.includes('syntax error') || 
      text.includes('unexpected token') || 
      text.includes('parsing error')) {
    return 'SYNTAX_ERROR';
  }
  
  // Network errors
  if (text.includes('network') || 
      text.includes('fetch') || 
      text.includes('http') || 
      text.includes('request') || 
      text.includes('response')) {
    return 'NETWORK_ERROR';
  }
  
  return 'OTHER_ERROR';
}

/**
 * Extract specific error patterns
 */
function extractErrorPatterns(text: string): string[] {
  const patterns: string[] = [];
  
  // Extract error codes
  const errorCodeMatches = text.match(/"code":"([^"]+)"/gi);
  if (errorCodeMatches) {
    errorCodeMatches.forEach(match => {
      const code = match.replace(/"code":"([^"]+)"/i, '$1');
      patterns.push(`Error code: ${code}`);
    });
  }
  
  // Extract file paths
  const filePathMatches = text.match(/([a-zA-Z0-9_\-\/\.]+\.(tsx|ts|js|jsx))/g);
  if (filePathMatches) {
    filePathMatches.slice(0, 3).forEach(match => {
      patterns.push(`File: ${match}`);
    });
  }
  
  // Extract common error phrases
  const errorPhrases = [
    'cannot find module',
    'unable to resolve module',
    'is not defined',
    'is not a function',
    'cannot read property',
    'undefined is not an object',
    'null is not an object',
    'invalid hook call',
    'element type is invalid',
    'no default export',
    'syntax error',
    'unexpected token',
    'relation does not exist',
    'column does not exist',
    'must be used within',
    'is undefined',
    'is null',
    'not found',
    'missing'
  ];
  
  errorPhrases.forEach(phrase => {
    if (text.toLowerCase().includes(phrase)) {
      patterns.push(`Error: ${phrase}`);
    }
  });
  
  return patterns;
}

/**
 * Main function to analyze error patterns
 */
async function analyzeErrors() {
  const limit = argv.limit;
  
  console.log(chalk.blue(`Quick analysis of up to ${limit} error messages`));

  // Check for DATABASE_URL
  const DATABASE_URL = process.env.POSTGRES_URL;
  if (!DATABASE_URL) {
    console.error(chalk.red('POSTGRES_URL environment variable is not set'));
    process.exit(1);
  }

  // Create database connection
  const client = postgres(DATABASE_URL);
  const db = drizzle(client);

  try {
    // Fetch error messages
    console.log(chalk.yellow('Fetching error messages from database...'));
    
    const errorMessages = await db
      .select({
        id: message.id,
        content: message.content
      })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        or(
          ilike(message.content, '%error%'),
          ilike(message.content, '%issue%'),
          ilike(message.content, '%problem%'),
          ilike(message.content, '%not working%')
        )
      ))
      .orderBy(desc(message.createdAt))
      .limit(limit);
    
    console.log(chalk.green(`Found ${errorMessages.length} error messages`));
    
    // Process and categorize the error messages
    console.log(chalk.yellow('Analyzing errors...'));
    
    const errorTypes: Record<string, number> = {};
    const errorPatternCounts: Record<string, number> = {};
    
    errorMessages.forEach(msg => {
      const text = extractText(msg.content);
      const errorType = extractErrorType(text);
      const patterns = extractErrorPatterns(text);
      
      // Count error types
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
      
      // Count error patterns
      patterns.forEach(pattern => {
        errorPatternCounts[pattern] = (errorPatternCounts[pattern] || 0) + 1;
      });
    });
    
    // Output error type distribution
    console.log('\n' + chalk.bold.blue('=== Error Type Distribution ==='));
    
    const sortedErrorTypes = Object.entries(errorTypes)
      .sort((a, b) => b[1] - a[1]);
    
    sortedErrorTypes.forEach(([type, count]) => {
      const percentage = ((count / errorMessages.length) * 100).toFixed(1);
      console.log(chalk.bold(`${type}: ${count} (${percentage}%)`));
    });
    
    // Output common error patterns
    console.log('\n' + chalk.bold.blue('=== Common Error Patterns ==='));
    
    const sortedPatterns = Object.entries(errorPatternCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20);
    
    sortedPatterns.forEach(([pattern, count]) => {
      const percentage = ((count / errorMessages.length) * 100).toFixed(1);
      console.log(`${pattern}: ${count} (${percentage}%)`);
    });
    
    // Suggest error categories
    console.log('\n' + chalk.bold.blue('=== Suggested Error Categories ==='));
    console.log('Based on the analysis, these categories would cover most errors:');
    
    const topCategories = sortedErrorTypes
      .filter(([_, count]) => count >= errorMessages.length * 0.05)
      .map(([type]) => type);
    
    topCategories.forEach(category => {
      console.log(chalk.bold(`- ${category}`));
    });
    
    if (topCategories.length < 5) {
      console.log(chalk.yellow('\nConsider adding these additional categories to reach 5 total:'));
      const remainingCategories = sortedErrorTypes
        .filter(([type]) => !topCategories.includes(type))
        .slice(0, 5 - topCategories.length)
        .map(([type]) => type);
      
      remainingCategories.forEach(category => {
        console.log(`- ${category}`);
      });
    }
    
    console.log(chalk.bold.green('\nAnalysis complete!'));
    
  } catch (error) {
    console.error(chalk.red('Error during analysis:'), error);
    process.exit(1);
  } finally {
    // Close database connection
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the analysis
analyzeErrors();
