#!/usr/bin/env node

/**
 * <PERSON>ript to analyze the latest 1000 errors and extract specific error types to files
 * Usage: npx tsx scripts/extract-error-details.ts
 */

// Load environment variables
require('dotenv').config({path: ".env.local"});

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { message } from '../src/lib/db/schema';
import { eq, desc } from 'drizzle-orm';
import * as fs from 'fs';
import chalk from 'chalk';

/**
 * Extract text from message content
 */
function extractText(content: any): string {
  try {
    if (typeof content === 'string') return content;
    if (Array.isArray(content)) {
      return content
        .filter(item => item && typeof item === 'object' && 'text' in item)
        .map(item => item.text)
        .join(' ');
    }
    if (typeof content === 'object') return JSON.stringify(content);
    return String(content);
  } catch (e) {
    return String(content);
  }
}

/**
 * Determine if a message contains a missing reference error
 */
function hasMissingReferenceError(text: string): boolean {
  text = text.toLowerCase();
  return text.includes('missing') || 
         text.includes('cannot read property') || 
         text.includes('is undefined') || 
         text.includes('is null') || 
         text.includes('not found') ||
         text.includes('doesn\'t exist') ||
         text.includes('not defined');
}

/**
 * Determine if a message contains a database error
 */
function hasDatabaseError(text: string): boolean {
  text = text.toLowerCase();
  return text.includes('database') || 
         text.includes('sql') || 
         text.includes('relation') || 
         text.includes('column') || 
         text.includes('public.') || 
         text.includes('42p01') ||
         text.includes('supabase') ||
         text.includes('postgres') ||
         text.includes('query');
}

/**
 * Extract error details from text
 */
function extractErrorDetails(text: string): string {
  // Try to extract the most relevant part of the error
  
  // Look for quoted error messages
  const quotedErrors = text.match(/"([^"]*error[^"]*)"/gi) || [];
  if (quotedErrors.length > 0) {
    return quotedErrors.join('\n');
  }
  
  // Look for error: followed by text
  const errorColonPattern = /error:?\s+([^.!?\n]+)/gi;
  const errorColonMatches = text.match(errorColonPattern) || [];
  if (errorColonMatches.length > 0) {
    return errorColonMatches.join('\n');
  }
  
  // Look for common error phrases
  const errorPhrases = [
    /failed to\s+([^.!?\n]+)/gi,
    /cannot\s+([^.!?\n]+)/gi,
    /unable to\s+([^.!?\n]+)/gi,
    /not found:?\s+([^.!?\n]+)/gi,
    /missing\s+([^.!?\n]+)/gi,
    /invalid\s+([^.!?\n]+)/gi,
    /undefined\s+([^.!?\n]+)/gi,
    /null\s+([^.!?\n]+)/gi
  ];
  
  for (const pattern of errorPhrases) {
    const matches = text.match(pattern) || [];
    if (matches.length > 0) {
      return matches.join('\n');
    }
  }
  
  // If we couldn't extract a specific part, return a snippet
  return text.substring(0, 300) + (text.length > 300 ? '...' : '');
}

/**
 * Main function to analyze error messages
 */
async function analyzeErrors() {
  console.log(chalk.blue('Analyzing the latest 1000 error messages...'));

  // Check for DATABASE_URL
  const DATABASE_URL = process.env.POSTGRES_URL;
  if (!DATABASE_URL) {
    console.error(chalk.red('POSTGRES_URL environment variable is not set'));
    process.exit(1);
  }

  // Create database connection
  const client = postgres(DATABASE_URL);
  const db = drizzle(client);

  try {
    // Fetch error messages
    console.log(chalk.yellow('Fetching error messages from database...'));
    
    const errorMessages = await db
      .select({
        id: message.id,
        content: message.content,
        createdAt: message.createdAt
      })
      .from(message)
      .where(eq(message.role, 'user'))
      .orderBy(desc(message.createdAt))
      .limit(1000);
    
    // Filter messages with error-related content
    const filteredMessages = errorMessages.filter(msg => {
      const text = extractText(msg.content).toLowerCase();
      return text.includes('error') || 
             text.includes('issue') || 
             text.includes('problem') || 
             text.includes('not working');
    });
    
    console.log(chalk.green(`Found ${filteredMessages.length} error messages out of ${errorMessages.length} total messages`));
    
    // Process the error messages
    console.log(chalk.yellow('Processing errors...'));
    
    const missingReferenceErrors: { date: string, details: string }[] = [];
    const databaseErrors: { date: string, details: string }[] = [];
    
    filteredMessages.forEach(msg => {
      const text = extractText(msg.content);
      const date = new Date(msg.createdAt).toISOString().split('T')[0];
      const details = extractErrorDetails(text);
      
      if (hasMissingReferenceError(text)) {
        missingReferenceErrors.push({ date, details });
      }
      
      if (hasDatabaseError(text)) {
        databaseErrors.push({ date, details });
      }
    });
    
    // Write missing reference errors to file
    const missingRefPath = './missing-reference-errors.txt';
    const missingRefContent = missingReferenceErrors
      .map(err => `[${err.date}] ${err.details}`)
      .join('\n\n---\n\n');
    
    fs.writeFileSync(missingRefPath, missingRefContent);
    console.log(chalk.green(`Found ${missingReferenceErrors.length} missing reference errors, saved to ${missingRefPath}`));
    
    // Write database errors to file
    const dbErrorPath = './database-errors.txt';
    const dbErrorContent = databaseErrors
      .map(err => `[${err.date}] ${err.details}`)
      .join('\n\n---\n\n');
    
    fs.writeFileSync(dbErrorPath, dbErrorContent);
    console.log(chalk.green(`Found ${databaseErrors.length} database errors, saved to ${dbErrorPath}`));
    
    console.log(chalk.bold.green('\nAnalysis complete!'));
    
  } catch (error) {
    console.error(chalk.red('Error during analysis:'), error);
    process.exit(1);
  } finally {
    // Close database connection
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the analysis
analyzeErrors();
