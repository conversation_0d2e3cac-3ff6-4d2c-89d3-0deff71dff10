#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to analyze and cluster error messages using TF-IDF and hierarchical clustering
 * Usage: npx tsx scripts/analyze-error-clusters.ts --days <number-of-days> --clusters <number-of-clusters>
 *
 * This script:
 * 1. Retrieves error messages from the database
 * 2. Preprocesses the text to extract meaningful content
 * 3. Uses TF-IDF vectorization and hierarchical clustering
 * 4. Outputs meaningful clusters with common patterns
 */

// Load environment variables
require('dotenv').config({path: ".env.local"});

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { message } from '../src/lib/db/schema';
import { eq, desc, gt, or, and, sql } from 'drizzle-orm';
import * as yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import chalk from 'chalk';
import * as fs from 'fs';

// Parse command line arguments
const argv = yargs.default(hideBin(process.argv))
  .option('days', {
    alias: 'd',
    description: 'Number of days to analyze',
    type: 'number',
    default: 30
  })
  .option('limit', {
    alias: 'l',
    description: 'Maximum number of messages to analyze',
    type: 'number',
    default: 500
  })
  .option('clusters', {
    alias: 'c',
    description: 'Target number of clusters',
    type: 'number',
    default: 10
  })
  .help()
  .alias('help', 'h')
  .parseSync();

interface ErrorMessage {
  id: string;
  content: any;
  text: string;
  createdAt: Date;
  cluster: number;
}

interface Cluster {
  id: number;
  size: number;
  messages: ErrorMessage[];
  commonPatterns: string[];
  examples: string[];
}

/**
 * Extract text from message content
 */
function extractText(content: any): string {
  try {
    // If content is already a string, use it directly
    if (typeof content === 'string') {
      return content;
    } 
    // If content is an array of objects with text property (common format)
    else if (Array.isArray(content)) {
      return content
        .filter(item => item && typeof item === 'object' && 'text' in item)
        .map(item => item.text)
        .join(' ');
    } 
    // If content is a JSON object
    else if (typeof content === 'object') {
      return JSON.stringify(content);
    }
    return String(content);
  } catch (e) {
    console.error('Error extracting text:', e);
    return String(content);
  }
}

/**
 * Preprocess text to extract error-specific content
 */
function preprocessText(text: string): string {
  // Convert to lowercase
  text = text.toLowerCase();
  
  // Extract error messages from the text
  let errorContent = '';
  
  // Look for quoted error messages
  const quotedErrors = text.match(/"([^"]*error[^"]*)"/gi) || [];
  quotedErrors.forEach(quoted => {
    errorContent += ' ' + quoted.replace(/"/g, '');
  });
  
  // Look for error: followed by text
  const errorColonPattern = /error:?\s+([^.!?\n]+)/gi;
  const errorColonMatches = text.match(errorColonPattern) || [];
  errorColonMatches.forEach(match => {
    errorContent += ' ' + match;
  });
  
  // Look for common error phrases
  const errorPhrases = [
    /failed to\s+([^.!?\n]+)/gi,
    /cannot\s+([^.!?\n]+)/gi,
    /unable to\s+([^.!?\n]+)/gi,
    /not found:?\s+([^.!?\n]+)/gi,
    /missing\s+([^.!?\n]+)/gi,
    /invalid\s+([^.!?\n]+)/gi,
    /undefined\s+([^.!?\n]+)/gi,
    /null\s+([^.!?\n]+)/gi
  ];
  
  errorPhrases.forEach(pattern => {
    const matches = text.match(pattern) || [];
    matches.forEach(match => {
      errorContent += ' ' + match;
    });
  });
  
  // If we found specific error content, use it
  if (errorContent.trim().length > 0) {
    return errorContent.trim();
  }
  
  // Otherwise, use the original text but try to clean it up
  // Remove code blocks
  text = text.replace(/```[\s\S]*?```/g, '');
  
  // Remove URLs
  text = text.replace(/https?:\/\/[^\s]+/g, '');
  
  // Remove common intro phrases
  text = text.replace(/i encountered an error[:\s]*/gi, '');
  text = text.replace(/i'm getting an error[:\s]*/gi, '');
  text = text.replace(/i am getting an error[:\s]*/gi, '');
  text = text.replace(/i'm facing an issue[:\s]*/gi, '');
  text = text.replace(/please help[:\s]*/gi, '');
  
  return text.trim();
}

/**
 * Extract common patterns from a cluster of error messages
 */
function extractCommonPatterns(messages: ErrorMessage[]): string[] {
  if (messages.length === 0) return [];
  
  // Extract words and phrases from all messages
  const allTexts = messages.map(msg => msg.text);
  
  // Find common substrings
  const commonPatterns: string[] = [];
  
  // Look for error codes
  const errorCodePattern = /"code":"([^"]+)"/gi;
  const errorCodes = new Set<string>();
  
  allTexts.forEach(text => {
    const matches = [...text.matchAll(errorCodePattern)];
    matches.forEach(match => {
      if (match[1]) errorCodes.add(match[1].toLowerCase());
    });
  });
  
  errorCodes.forEach(code => {
    commonPatterns.push(`Error code: ${code}`);
  });
  
  // Look for file paths
  const filePathPattern = /([a-zA-Z0-9_\-\/\.]+\.(tsx|ts|js|jsx))/g;
  const filePaths = new Set<string>();
  
  allTexts.forEach(text => {
    const matches = [...text.matchAll(filePathPattern)];
    matches.forEach(match => {
      if (match[1]) filePaths.add(match[1].toLowerCase());
    });
  });
  
  // Count occurrences of each file path
  const filePathCounts: Record<string, number> = {};
  filePaths.forEach(path => {
    filePathCounts[path] = 0;
    allTexts.forEach(text => {
      if (text.toLowerCase().includes(path)) {
        filePathCounts[path]++;
      }
    });
  });
  
  // Add file paths that appear in multiple messages
  Object.entries(filePathCounts)
    .filter(([_, count]) => count > 1)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .forEach(([path]) => {
      commonPatterns.push(`File: ${path}`);
    });
  
  // Look for common error phrases
  const errorPhrases = [
    'cannot find module',
    'unable to resolve module',
    'is not defined',
    'is not a function',
    'cannot read property',
    'undefined is not an object',
    'null is not an object',
    'invalid hook call',
    'element type is invalid',
    'no default export',
    'syntax error',
    'unexpected token',
    'relation does not exist',
    'column does not exist',
    'must be used within',
    'is undefined',
    'is null',
    'not found',
    'missing'
  ];
  
  errorPhrases.forEach(phrase => {
    let count = 0;
    allTexts.forEach(text => {
      if (text.toLowerCase().includes(phrase)) {
        count++;
      }
    });
    
    if (count > 1 && count >= messages.length * 0.5) {
      commonPatterns.push(`Error: ${phrase}`);
    }
  });
  
  // If we don't have enough patterns, try to extract common words
  if (commonPatterns.length < 3) {
    // Count word frequencies
    const wordCounts: Record<string, number> = {};
    
    allTexts.forEach(text => {
      const words = text.toLowerCase().split(/\s+/);
      words.forEach(word => {
        // Only count meaningful words
        if (word.length > 3 && !['error', 'encountered', 'with', 'this', 'that', 'have', 'from', 'what', 'when', 'where', 'help'].includes(word)) {
          wordCounts[word] = (wordCounts[word] || 0) + 1;
        }
      });
    });
    
    // Add common words
    Object.entries(wordCounts)
      .filter(([_, count]) => count > 1 && count >= messages.length * 0.7)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .forEach(([word]) => {
        commonPatterns.push(`Common term: ${word}`);
      });
  }
  
  return commonPatterns;
}

/**
 * Calculate similarity between two error messages
 */
function calculateSimilarity(text1: string, text2: string): number {
  // Convert to lowercase
  text1 = text1.toLowerCase();
  text2 = text2.toLowerCase();
  
  // Get words from each text
  const words1 = new Set(text1.split(/\s+/).filter(w => w.length > 3));
  const words2 = new Set(text2.split(/\s+/).filter(w => w.length > 3));
  
  // Count shared words
  let sharedCount = 0;
  for (const word of words1) {
    if (words2.has(word)) {
      sharedCount++;
    }
  }
  
  // Calculate Jaccard similarity
  const totalUniqueWords = new Set([...words1, ...words2]).size;
  if (totalUniqueWords === 0) return 0;
  
  return sharedCount / totalUniqueWords;
}

/**
 * Hierarchical clustering of error messages
 */
function clusterErrors(errors: ErrorMessage[], targetClusters: number): Cluster[] {
  // Start with each error in its own cluster
  let clusters: Cluster[] = errors.map((err, index) => ({
    id: index,
    size: 1,
    messages: [err],
    commonPatterns: [],
    examples: [err.text.substring(0, 150) + (err.text.length > 150 ? '...' : '')]
  }));
  
  // Merge clusters until we reach the target number
  while (clusters.length > targetClusters) {
    let bestSimilarity = -1;
    let bestPair = [-1, -1];
    
    // Find the most similar pair of clusters
    for (let i = 0; i < clusters.length; i++) {
      for (let j = i + 1; j < clusters.length; j++) {
        // Calculate average similarity between all pairs of errors in the two clusters
        let totalSimilarity = 0;
        let pairCount = 0;
        
        for (const err1 of clusters[i].messages) {
          for (const err2 of clusters[j].messages) {
            totalSimilarity += calculateSimilarity(err1.text, err2.text);
            pairCount++;
          }
        }
        
        const avgSimilarity = totalSimilarity / pairCount;
        
        if (avgSimilarity > bestSimilarity) {
          bestSimilarity = avgSimilarity;
          bestPair = [i, j];
        }
      }
    }
    
    // If no similar clusters found, break
    if (bestSimilarity <= 0) break;
    
    // Merge the two most similar clusters
    const [i, j] = bestPair;
    const newCluster: Cluster = {
      id: clusters[i].id,
      size: clusters[i].size + clusters[j].size,
      messages: [...clusters[i].messages, ...clusters[j].messages],
      commonPatterns: [],
      examples: [...clusters[i].examples, ...clusters[j].examples].slice(0, 3)
    };
    
    // Update cluster patterns
    newCluster.commonPatterns = extractCommonPatterns(newCluster.messages);
    
    // Replace the two clusters with the merged one
    clusters.splice(j, 1);
    clusters[i] = newCluster;
  }
  
  // Update cluster IDs and assign to errors
  clusters.forEach((cluster, index) => {
    cluster.id = index;
    cluster.messages.forEach(err => {
      err.cluster = index;
    });
  });
  
  return clusters;
}

/**
 * Main function to analyze and cluster error patterns
 */
async function analyzeErrorClusters() {
  const days = argv.days;
  const limit = argv.limit;
  const targetClusters = argv.clusters;
  
  console.log(chalk.blue(`Analyzing error patterns from the last ${days} days (max ${limit} messages)`));
  console.log(chalk.blue(`Target clusters: ${targetClusters}`));

  // Check for DATABASE_URL
  const DATABASE_URL = process.env.POSTGRES_URL;
  if (!DATABASE_URL) {
    console.error(chalk.red('POSTGRES_URL environment variable is not set'));
    process.exit(1);
  }

  // Create database connection
  const client = postgres(DATABASE_URL);
  const db = drizzle(client);

  try {
    // Calculate the date range
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Build the error patterns for the query
    const errorPatterns = [
      'encountered an error',
      'getting an error',
      'error occurred',
      'facing an issue',
      'not working',
      'failed to',
      'fix this error',
      'fix the error',
      'error message'
    ];
    
    // Create the conditions for the query
    const conditions = errorPatterns.map(pattern => 
      sql`CAST(${message.content} AS TEXT) ILIKE ${'%' + pattern + '%'}`
    );
    
    // Fetch error messages
    console.log(chalk.yellow('Fetching error messages from database...'));
    
    const errorMessages = await db
      .select({
        id: message.id,
        content: message.content,
        createdAt: message.createdAt
      })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        gt(message.createdAt, startDate),
        or(...conditions)
      ))
      .orderBy(desc(message.createdAt))
      .limit(limit);
    
    console.log(chalk.green(`Found ${errorMessages.length} error messages`));
    
    // Process error messages
    console.log(chalk.yellow('Preprocessing error messages...'));
    
    const processedErrors: ErrorMessage[] = errorMessages.map(msg => {
      const rawText = extractText(msg.content);
      const text = preprocessText(rawText);
      
      return {
        id: msg.id,
        content: msg.content,
        text,
        createdAt: msg.createdAt,
        cluster: -1
      };
    });
    
    // Cluster the errors
    console.log(chalk.yellow('Clustering error messages...'));
    const clusters = clusterErrors(processedErrors, targetClusters);
    
    // Sort clusters by size
    const sortedClusters = [...clusters].sort((a, b) => b.size - a.size);
    
    // Output results
    console.log('\n' + chalk.bold.blue('=== Error Cluster Analysis ==='));
    console.log(chalk.bold(`Total Error Messages: ${processedErrors.length}`));
    console.log(chalk.bold(`Total Clusters: ${sortedClusters.length}`));
    
    console.log('\n' + chalk.bold('Error Clusters by Size:'));
    
    // Prepare output data
    const outputData: any = {
      analysisDate: new Date().toISOString(),
      totalErrors: processedErrors.length,
      totalClusters: sortedClusters.length,
      clusters: []
    };
    
    for (const cluster of sortedClusters) {
      const percentage = ((cluster.size / processedErrors.length) * 100).toFixed(1);
      console.log(chalk.bold(`Cluster ${cluster.id}: ${cluster.size} errors (${percentage}%)`));
      
      // Show common patterns
      if (cluster.commonPatterns.length > 0) {
        console.log(chalk.yellow(`  Common Patterns:`));
        cluster.commonPatterns.forEach(pattern => {
          console.log(chalk.yellow(`    - ${pattern}`));
        });
      } else {
        console.log(chalk.yellow(`  No common patterns identified`));
      }
      
      // Show examples
      console.log(chalk.gray('  Examples:'));
      cluster.examples.slice(0, 3).forEach((example, i) => {
        console.log(chalk.gray(`    ${i+1}. ${example}`));
      });
      
      console.log('');
      
      // Add to output data
      outputData.clusters.push({
        id: cluster.id,
        size: cluster.size,
        percentage: `${percentage}%`,
        commonPatterns: cluster.commonPatterns,
        examples: cluster.examples.slice(0, 3)
      });
    }
    
    // Write output to file
    const outputPath = `./error-clusters-analysis-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2));
    
    console.log(chalk.bold.green(`\nAnalysis complete! Full results saved to ${outputPath}`));
    console.log(chalk.yellow('\nSuggested Error Categories:'));
    
    // Suggest categories based on the clusters
    const suggestedCategories = new Set<string>();
    
    sortedClusters.forEach(cluster => {
      if (cluster.size < 5) return; // Skip small clusters
      
      // Try to identify a category from the patterns
      let category = '';
      
      for (const pattern of cluster.commonPatterns) {
        if (pattern.includes('relation') || pattern.includes('column') || 
            pattern.includes('sql') || pattern.includes('database')) {
          category = 'DATABASE_ERROR';
          break;
        } else if (pattern.includes('module') || pattern.includes('import') || 
                  pattern.includes('export') || pattern.includes('resolve')) {
          category = 'MODULE_ERROR';
          break;
        } else if (pattern.includes('component') || pattern.includes('element') || 
                  pattern.includes('jsx') || pattern.includes('tsx') || 
                  pattern.includes('render')) {
          category = 'UI_ERROR';
          break;
        } else if (pattern.includes('hook') || pattern.includes('context') || 
                  pattern.includes('provider')) {
          category = 'REACT_HOOKS_ERROR';
          break;
        } else if (pattern.includes('undefined') || pattern.includes('null') || 
                  pattern.includes('type') || pattern.includes('function')) {
          category = 'TYPE_ERROR';
          break;
        } else if (pattern.includes('navigation') || pattern.includes('route') || 
                  pattern.includes('screen')) {
          category = 'NAVIGATION_ERROR';
          break;
        } else if (pattern.includes('syntax') || pattern.includes('token') || 
                  pattern.includes('parsing')) {
          category = 'SYNTAX_ERROR';
          break;
        }
      }
      
      if (category) {
        suggestedCategories.add(category);
      }
    });
    
    // Output suggested categories
    suggestedCategories.forEach(category => {
      console.log(`- ${category}`);
    });
    
    if (suggestedCategories.size === 0) {
      console.log('No clear categories identified. Consider manual review of the clusters.');
    }
    
  } catch (error) {
    console.error(chalk.red('Error analyzing error patterns:'), error);
    process.exit(1);
  } finally {
    // Close database connection
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the analysis
analyzeErrorClusters();
