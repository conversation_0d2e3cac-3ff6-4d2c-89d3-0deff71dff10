#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to analyze error messages and bucket them by similarity
 * Usage: npx tsx scripts/analyze-error-patterns.ts --days <number-of-days>
 *
 * This script:
 * 1. Retrieves error messages from the database
 * 2. Extracts key patterns and phrases
 * 3. Clusters them into buckets based on similarity
 * 4. Outputs a summary of error categories
 */

// Load environment variables
require('dotenv').config({path: ".env.local"});

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { message } from '../src/lib/db/schema';
import { eq, desc, gt, ilike, or, and, sql } from 'drizzle-orm';
import * as yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import chalk from 'chalk';

// Parse command line arguments
const argv = yargs.default(hideBin(process.argv))
  .option('days', {
    alias: 'd',
    description: 'Number of days to analyze',
    type: 'number',
    default: 30
  })
  .option('limit', {
    alias: 'l',
    description: 'Maximum number of messages to analyze',
    type: 'number',
    default: 500
  })
  .help()
  .alias('help', 'h')
  .parseSync();

// Error pattern categories with their key phrases
const ERROR_PATTERNS = {
  DATABASE: [
    'relation', 'table', 'column', 'database', 'postgresql',
    'postgres', 'sql', 'query', 'constraint', 'public.',
    'foreign key', 'unique', 'database connection', 'supabase',
    'code":"42p01"', 'code":"42703"', 'code":"42601"', 'code":"23505"'
  ],
  REACT_COMPONENT: [
    'element type is invalid', 'expected a string', 'property doesn\'t exist',
    'component', 'react', 'jsx', 'tsx', 'render', 'datetimepicker',
    'no default export'
  ],
  REACT_HOOKS: [
    'invalid hook call', 'hooks can only be called', 'useeffect',
    'usestate', 'usereducer', 'usememo', 'usecallback', 'useref'
  ],
  CONTEXT_PROVIDER: [
    'must be used within', 'provider', 'context', 'usetheme', 
    'useauth', 'authprovider', 'themeprovider'
  ],
  NAVIGATION: [
    'navigationcontainer', 'navigator', 'navigation', 'route', 
    'screen', 'stack', 'tab', 'drawer', 'react-navigation'
  ],
  MODULE_RESOLUTION: [
    'unable to resolve module', 'cannot find module', 'module not found',
    'dependency', 'package', 'library', 'node_modules', 'import', 'export'
  ],
  TYPE_ERROR: [
    'type', 'typescript', 'undefined', 'null', 'is not a function',
    'cannot read property', 'object is possibly undefined'
  ],
  SYNTAX_ERROR: [
    'syntax error', 'unexpected token', 'parsing error', 'unexpected end'
  ],
  NETWORK: [
    'network', 'fetch', 'axios', 'http', 'request', 'response', 
    'api', 'server', 'client', 'timeout', 'connection'
  ]
};

interface ErrorMessage {
  id: string;
  content: any;
  extractedText: string;
  createdAt: Date;
  category: string;
  matchScore: number;
}

/**
 * Extract text from message content
 */
function extractText(content: any): string {
  try {
    // If content is already a string, use it directly
    if (typeof content === 'string') {
      return content;
    } 
    // If content is an array of objects with text property (common format)
    else if (Array.isArray(content)) {
      return content
        .filter(item => item && typeof item === 'object' && 'text' in item)
        .map(item => item.text)
        .join(' ');
    } 
    // If content is a JSON object
    else if (typeof content === 'object') {
      return JSON.stringify(content);
    }
    return String(content);
  } catch (e) {
    console.error('Error extracting text:', e);
    return String(content);
  }
}

/**
 * Categorize an error message by matching against patterns
 */
function categorizeError(text: string): { category: string, score: number } {
  text = text.toLowerCase();
  
  let bestCategory = 'UNCATEGORIZED';
  let highestScore = 0;
  
  // Check each category's patterns
  for (const [category, patterns] of Object.entries(ERROR_PATTERNS)) {
    let matchCount = 0;
    
    // Count how many patterns match
    for (const pattern of patterns) {
      if (text.includes(pattern.toLowerCase())) {
        matchCount++;
      }
    }
    
    // Calculate a score based on number of matches and pattern specificity
    const score = matchCount / patterns.length;
    
    if (score > highestScore) {
      highestScore = score;
      bestCategory = category;
    }
  }
  
  // Only categorize if we have a reasonable match
  if (highestScore > 0) {
    return { category: bestCategory, score: highestScore };
  }
  
  return { category: 'UNCATEGORIZED', score: 0 };
}

/**
 * Main function to analyze error patterns
 */
async function analyzeErrorPatterns() {
  const days = argv.days;
  const limit = argv.limit;
  
  console.log(chalk.blue(`Analyzing error patterns from the last ${days} days (max ${limit} messages)`));

  // Check for DATABASE_URL
  const DATABASE_URL = process.env.POSTGRES_URL;
  if (!DATABASE_URL) {
    console.error(chalk.red('POSTGRES_URL environment variable is not set'));
    process.exit(1);
  }

  // Create database connection
  const client = postgres(DATABASE_URL);
  const db = drizzle(client);

  try {
    // Calculate the date range
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Build the error patterns for the query
    const errorPatterns = [
      'encountered an error',
      'getting an error',
      'error occurred',
      'facing an issue',
      'not working',
      'failed to',
      'fix this error',
      'fix the error',
      'error message'
    ];
    
    // Create the conditions for the query
    const conditions = errorPatterns.map(pattern => 
      sql`CAST(${message.content} AS TEXT) ILIKE ${'%' + pattern + '%'}`
    );
    
    // Fetch error messages
    console.log(chalk.yellow('Fetching error messages from database...'));
    
    const errorMessages = await db
      .select({
        id: message.id,
        content: message.content,
        createdAt: message.createdAt
      })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        gt(message.createdAt, startDate),
        or(...conditions)
      ))
      .orderBy(desc(message.createdAt))
      .limit(limit);
    
    console.log(chalk.green(`Found ${errorMessages.length} error messages`));
    
    // Process and categorize the error messages
    console.log(chalk.yellow('Analyzing and categorizing errors...'));
    
    const processedErrors: ErrorMessage[] = errorMessages.map(msg => {
      const extractedText = extractText(msg.content);
      const { category, score } = categorizeError(extractedText);
      
      return {
        id: msg.id,
        content: msg.content,
        extractedText,
        createdAt: msg.createdAt,
        category,
        matchScore: score
      };
    });
    
    // Count errors by category
    const categoryCounts: Record<string, number> = {};
    processedErrors.forEach(error => {
      categoryCounts[error.category] = (categoryCounts[error.category] || 0) + 1;
    });
    
    // Sort categories by count
    const sortedCategories = Object.entries(categoryCounts)
      .sort((a, b) => b[1] - a[1])
      .map(([category, count]) => ({ category, count }));
    
    // Output results
    console.log('\n' + chalk.bold.blue('=== Error Category Analysis ==='));
    console.log(chalk.bold(`Total Error Messages: ${processedErrors.length}`));
    
    console.log('\n' + chalk.bold('Error Categories by Frequency:'));
    sortedCategories.forEach(({ category, count }) => {
      const percentage = ((count / processedErrors.length) * 100).toFixed(1);
      console.log(chalk.bold(`${category}: ${count} (${percentage}%)`));
    });
    
    // Show examples from each category
    console.log('\n' + chalk.bold('Example Errors by Category:'));
    for (const { category, count } of sortedCategories) {
      console.log(chalk.bold.yellow(`\n${category} (${count} errors):`));
      
      // Get top 3 examples with highest match scores
      const examples = processedErrors
        .filter(err => err.category === category)
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, 3);
      
      examples.forEach((example, i) => {
        console.log(chalk.bold(`Example ${i+1} (score: ${example.matchScore.toFixed(2)}):`));
        
        // Show a snippet of the error message
        const snippet = example.extractedText.substring(0, 200) + 
          (example.extractedText.length > 200 ? '...' : '');
        console.log(chalk.gray(snippet));
        console.log('');
      });
    }
    
    console.log(chalk.bold.green('\nAnalysis complete!'));
    
  } catch (error) {
    console.error(chalk.red('Error analyzing error patterns:'), error);
    process.exit(1);
  } finally {
    // Close database connection
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the analysis
analyzeErrorPatterns();
