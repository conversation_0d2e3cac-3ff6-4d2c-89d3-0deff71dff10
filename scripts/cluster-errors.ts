#!/usr/bin/env node

/**
 * Script to cluster error messages by similarity without predefined categories
 * Usage: npx tsx scripts/cluster-errors.ts --days <number-of-days> --clusters <number-of-clusters>
 *
 * This script:
 * 1. Retrieves error messages from the database
 * 2. Extracts key terms and phrases
 * 3. Clusters them using TF-IDF and cosine similarity
 * 4. Outputs clusters for manual tagging
 */

// Load environment variables
require('dotenv').config({path: ".env.local"});

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { message } from '../src/lib/db/schema';
import { eq, desc, gt, or, and, sql } from 'drizzle-orm';
import * as yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import chalk from 'chalk';
import * as fs from 'fs';

// Parse command line arguments
const argv = yargs.default(hideBin(process.argv))
  .option('days', {
    alias: 'd',
    description: 'Number of days to analyze',
    type: 'number',
    default: 30
  })
  .option('limit', {
    alias: 'l',
    description: 'Maximum number of messages to analyze',
    type: 'number',
    default: 500
  })
  .option('clusters', {
    alias: 'c',
    description: 'Target number of clusters',
    type: 'number',
    default: 10
  })
  .option('similarity', {
    alias: 's',
    description: 'Similarity threshold (0-1)',
    type: 'number',
    default: 0.3
  })
  .help()
  .alias('help', 'h')
  .parseSync();

interface ErrorMessage {
  id: string;
  content: any;
  text: string;
  createdAt: Date;
  keywords: string[];
  cluster: number;
}

/**
 * Extract text from message content
 */
function extractText(content: any): string {
  try {
    // If content is already a string, use it directly
    if (typeof content === 'string') {
      return content;
    } 
    // If content is an array of objects with text property (common format)
    else if (Array.isArray(content)) {
      return content
        .filter(item => item && typeof item === 'object' && 'text' in item)
        .map(item => item.text)
        .join(' ');
    } 
    // If content is a JSON object
    else if (typeof content === 'object') {
      return JSON.stringify(content);
    }
    return String(content);
  } catch (e) {
    console.error('Error extracting text:', e);
    return String(content);
  }
}

/**
 * Extract keywords from text
 */
function extractKeywords(text: string): string[] {
  // Convert to lowercase
  text = text.toLowerCase();
  
  // Remove code blocks
  text = text.replace(/```[\s\S]*?```/g, '');
  
  // Extract error-specific phrases
  const errorPhrases: string[] = [];
  
  // Look for quoted error messages
  const quotedErrors = text.match(/"([^"]*error[^"]*)"/gi) || [];
  quotedErrors.forEach(quoted => errorPhrases.push(quoted.replace(/"/g, '')));
  
  // Look for specific error patterns
  const errorPatterns = [
    /error:?\s+([^.!?\n]+)/gi,
    /failed to\s+([^.!?\n]+)/gi,
    /cannot\s+([^.!?\n]+)/gi,
    /undefined\s+([^.!?\n]+)/gi,
    /null\s+([^.!?\n]+)/gi,
    /not\s+found:?\s+([^.!?\n]+)/gi,
    /missing\s+([^.!?\n]+)/gi,
    /invalid\s+([^.!?\n]+)/gi
  ];
  
  errorPatterns.forEach(pattern => {
    const matches = text.match(pattern) || [];
    matches.forEach(match => errorPhrases.push(match));
  });
  
  // Extract technical terms
  const technicalTerms = text.match(/([a-z][a-z0-9]*(?:[A-Z][a-z0-9]*)+)/g) || []; // CamelCase words
  const functions = text.match(/([a-zA-Z0-9_]+)\(\)/g) || []; // Function calls
  const filePaths = text.match(/([a-zA-Z0-9_\-\/\.]+\.(tsx|ts|js|jsx))/g) || []; // File paths
  
  // Combine all keywords
  const allKeywords = [...errorPhrases, ...technicalTerms, ...functions, ...filePaths];
  
  // Remove duplicates and very short keywords
  return [...new Set(allKeywords)].filter(kw => kw.length > 3);
}

/**
 * Calculate similarity between two sets of keywords
 */
function calculateSimilarity(keywords1: string[], keywords2: string[]): number {
  if (keywords1.length === 0 || keywords2.length === 0) return 0;
  
  // Find intersection
  const intersection = keywords1.filter(kw => keywords2.includes(kw));
  
  // Calculate Jaccard similarity
  return intersection.length / (keywords1.length + keywords2.length - intersection.length);
}

/**
 * Cluster error messages using similarity
 */
function clusterErrorMessages(errors: ErrorMessage[], similarityThreshold: number, targetClusters: number): ErrorMessage[] {
  // Initialize each error to its own cluster
  let clusters: ErrorMessage[][] = errors.map(err => [err]);
  
  // Merge clusters until we reach target number or can't merge anymore
  let iteration = 0;
  const maxIterations = 100; // Safety limit
  
  while (clusters.length > targetClusters && iteration < maxIterations) {
    iteration++;
    let bestSimilarity = 0;
    let bestPair = [-1, -1];
    
    // Find the most similar pair of clusters
    for (let i = 0; i < clusters.length; i++) {
      for (let j = i + 1; j < clusters.length; j++) {
        // Calculate average similarity between all pairs of errors in the two clusters
        let totalSimilarity = 0;
        let pairCount = 0;
        
        for (const err1 of clusters[i]) {
          for (const err2 of clusters[j]) {
            totalSimilarity += calculateSimilarity(err1.keywords, err2.keywords);
            pairCount++;
          }
        }
        
        const avgSimilarity = totalSimilarity / pairCount;
        
        if (avgSimilarity > bestSimilarity && avgSimilarity >= similarityThreshold) {
          bestSimilarity = avgSimilarity;
          bestPair = [i, j];
        }
      }
    }
    
    // If no pair found above threshold, stop merging
    if (bestPair[0] === -1) break;
    
    // Merge the two most similar clusters
    const [i, j] = bestPair;
    clusters[i] = [...clusters[i], ...clusters[j]];
    clusters.splice(j, 1);
  }
  
  // Assign cluster IDs to errors
  const result = [...errors];
  let clusterIndex = 0;
  
  for (const cluster of clusters) {
    for (const err of cluster) {
      const index = result.findIndex(e => e.id === err.id);
      if (index !== -1) {
        result[index].cluster = clusterIndex;
      }
    }
    clusterIndex++;
  }
  
  return result;
}

/**
 * Find representative keywords for a cluster
 */
function findClusterKeywords(cluster: ErrorMessage[]): string[] {
  // Count keyword frequencies across the cluster
  const keywordCounts: Record<string, number> = {};
  
  for (const err of cluster) {
    for (const keyword of err.keywords) {
      keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
    }
  }
  
  // Sort by frequency
  const sortedKeywords = Object.entries(keywordCounts)
    .sort((a, b) => b[1] - a[1])
    .map(([keyword]) => keyword);
  
  // Return top keywords (up to 10)
  return sortedKeywords.slice(0, 10);
}

/**
 * Main function to analyze and cluster error patterns
 */
async function clusterErrorPatterns() {
  const days = argv.days;
  const limit = argv.limit;
  const targetClusters = argv.clusters;
  const similarityThreshold = argv.similarity;
  
  console.log(chalk.blue(`Analyzing error patterns from the last ${days} days (max ${limit} messages)`));
  console.log(chalk.blue(`Target clusters: ${targetClusters}, Similarity threshold: ${similarityThreshold}`));

  // Check for DATABASE_URL
  const DATABASE_URL = process.env.POSTGRES_URL;
  if (!DATABASE_URL) {
    console.error(chalk.red('POSTGRES_URL environment variable is not set'));
    process.exit(1);
  }

  // Create database connection
  const client = postgres(DATABASE_URL);
  const db = drizzle(client);

  try {
    // Calculate the date range
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Build the error patterns for the query
    const errorPatterns = [
      'encountered an error',
      'getting an error',
      'error occurred',
      'facing an issue',
      'not working',
      'failed to',
      'fix this error',
      'fix the error',
      'error message'
    ];
    
    // Create the conditions for the query
    const conditions = errorPatterns.map(pattern => 
      sql`CAST(${message.content} AS TEXT) ILIKE ${'%' + pattern + '%'}`
    );
    
    // Fetch error messages
    console.log(chalk.yellow('Fetching error messages from database...'));
    
    const errorMessages = await db
      .select({
        id: message.id,
        content: message.content,
        createdAt: message.createdAt
      })
      .from(message)
      .where(and(
        eq(message.role, 'user'),
        gt(message.createdAt, startDate),
        or(...conditions)
      ))
      .orderBy(desc(message.createdAt))
      .limit(limit);
    
    console.log(chalk.green(`Found ${errorMessages.length} error messages`));
    
    // Process error messages
    console.log(chalk.yellow('Extracting keywords from error messages...'));
    
    const processedErrors: ErrorMessage[] = errorMessages.map(msg => {
      const text = extractText(msg.content);
      const keywords = extractKeywords(text);
      
      return {
        id: msg.id,
        content: msg.content,
        text,
        createdAt: msg.createdAt,
        keywords,
        cluster: -1
      };
    });
    
    // Cluster the errors
    console.log(chalk.yellow('Clustering error messages by similarity...'));
    const clusteredErrors = clusterErrorMessages(processedErrors, similarityThreshold, targetClusters);
    
    // Count errors by cluster
    const clusterCounts: Record<number, number> = {};
    clusteredErrors.forEach(error => {
      clusterCounts[error.cluster] = (clusterCounts[error.cluster] || 0) + 1;
    });
    
    // Sort clusters by size
    const sortedClusters = Object.entries(clusterCounts)
      .sort((a, b) => b[1] - a[1])
      .map(([cluster, count]) => ({ cluster: parseInt(cluster), count }));
    
    // Output results
    console.log('\n' + chalk.bold.blue('=== Error Cluster Analysis ==='));
    console.log(chalk.bold(`Total Error Messages: ${clusteredErrors.length}`));
    console.log(chalk.bold(`Total Clusters: ${sortedClusters.length}`));
    
    console.log('\n' + chalk.bold('Error Clusters by Size:'));
    
    // Prepare output data
    const outputData: any = {
      analysisDate: new Date().toISOString(),
      totalErrors: clusteredErrors.length,
      totalClusters: sortedClusters.length,
      clusters: []
    };
    
    for (const { cluster, count } of sortedClusters) {
      const percentage = ((count / clusteredErrors.length) * 100).toFixed(1);
      console.log(chalk.bold(`Cluster ${cluster}: ${count} errors (${percentage}%)`));
      
      // Get errors in this cluster
      const clusterErrors = clusteredErrors.filter(err => err.cluster === cluster);
      
      // Find representative keywords
      const keywords = findClusterKeywords(clusterErrors);
      console.log(chalk.yellow(`  Keywords: ${keywords.join(', ')}`));
      
      // Show examples
      console.log(chalk.gray('  Examples:'));
      const examples = clusterErrors.slice(0, 3);
      
      examples.forEach((example, i) => {
        // Show a snippet of the error message
        const snippet = example.text.substring(0, 150) + 
          (example.text.length > 150 ? '...' : '');
        console.log(chalk.gray(`    ${i+1}. ${snippet}`));
      });
      
      console.log('');
      
      // Add to output data
      outputData.clusters.push({
        id: cluster,
        size: count,
        percentage: `${percentage}%`,
        keywords,
        examples: examples.map(ex => ({
          id: ex.id,
          snippet: ex.text.substring(0, 150) + (ex.text.length > 150 ? '...' : '')
        }))
      });
    }
    
    // Write output to file
    const outputPath = `./error-clusters-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2));
    
    console.log(chalk.bold.green(`\nAnalysis complete! Full results saved to ${outputPath}`));
    console.log(chalk.yellow('\nNext steps:'));
    console.log('1. Review the clusters and assign meaningful tags to each');
    console.log('2. Use these tags to improve error categorization in your application');
    
  } catch (error) {
    console.error(chalk.red('Error analyzing error patterns:'), error);
    process.exit(1);
  } finally {
    // Close database connection
    await client.end();
    console.log('Database connection closed');
  }
}

// Run the analysis
clusterErrorPatterns();
