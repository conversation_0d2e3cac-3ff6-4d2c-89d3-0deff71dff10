#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to run the chat-to-project migration
 * Usage: npx tsx scripts/run-migration.js
 */

// Ensure environment variables are loaded
require('dotenv').config({path: ".env.local"});

console.log('Starting chat-to-project migration...');

// Since we're using TypeScript, we need to use dynamic import
(async () => {
  try {
    // Import the migration function using dynamic import
    const { migrateChatsToProjects } = await import('./migrate-chats-to-projects.ts');
    
    await migrateChatsToProjects();
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
})();
