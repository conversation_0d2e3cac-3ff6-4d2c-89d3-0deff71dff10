/**
 * Backfill script for adding versionCode to existing deployments
 * 
 * This script:
 * 1. Fetches all deployments grouped by projectId and platform
 * 2. Orders them by creation date
 * 3. Assigns incrementing versionCode values starting from 1
 * 
 * Run with: npx tsx scripts/backfill-version-code.ts
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { deployments } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({path: ".env.local"});

// Create a new database connection for the script
const connectionString = process.env.DATABASE_URL || '';
if (!connectionString) {
  console.error('DATABASE_URL environment variable is not set');
  process.exit(1);
}

const client = postgres(connectionString);
const db = drizzle(client);

async function backfillVersionCodes() {
  console.log('Starting versionCode backfill process...');
  
  // Get all projects with deployments
  const projectsWithDeployments = await db
    .selectDistinct({ projectId: deployments.projectId })
    .from(deployments)
    .where(eq(deployments.projectId, deployments.projectId)); // Non-null check
  
  console.log(`Found ${projectsWithDeployments.length} projects with deployments`);
  
  // Process each project
  for (const { projectId } of projectsWithDeployments) {
    if (!projectId) continue;
    
    console.log(`Processing project: ${projectId}`);
    
    // Get platforms for this project
    const platformsForProject = await db
      .selectDistinct({ platform: deployments.platform })
      .from(deployments)
      .where(eq(deployments.projectId, projectId));
    
    // Process each platform separately
    for (const { platform } of platformsForProject) {
      if (!platform) continue;
      
      console.log(`  Processing platform: ${platform}`);
      
      // Get all deployments for this project and platform, ordered by creation date
      const deploymentsForPlatform = await db
        .select()
        .from(deployments)
        .where(
          and(
            eq(deployments.projectId, projectId),
            eq(deployments.platform, platform)
          )
        )
        .orderBy(deployments.createdAt);
      
      console.log(`    Found ${deploymentsForPlatform.length} deployments`);
      
      // Update each deployment with an incrementing versionCode
      let versionCode = 1;
      for (const deployment of deploymentsForPlatform) {
        await db
          .update(deployments)
          .set({ versionCode })
          .where(eq(deployments.id, deployment.id));
        
        console.log(`    Updated deployment ${deployment.id} with versionCode ${versionCode}`);
        versionCode++;
      }
    }
  }
  
  console.log('Backfill process completed successfully');
}

// Run the backfill function
backfillVersionCodes()
  .then(() => {
    console.log('Script completed successfully');
    // Close the database connection before exiting
    return client.end();
  })
  .then(() => {
    console.log('Database connection closed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during backfill:', error);
    // Attempt to close the connection even on error
    client.end().finally(() => {
      process.exit(1);
    });
  });
