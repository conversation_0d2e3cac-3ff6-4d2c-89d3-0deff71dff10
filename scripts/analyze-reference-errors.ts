// #!/usr/bin/env node
//
// /**
//  * <PERSON><PERSON><PERSON> to analyze missing reference errors and break them down into subcategories
//  * Usage: npx tsx scripts/analyze-reference-errors.ts
//  */
//
// // Load environment variables
// require('dotenv').config({path: ".env.local"});
//
// import { drizzle } from 'drizzle-orm/postgres-js';
// import postgres from 'postgres';
// import { message } from '../src/lib/db/schema';
// import { eq, desc } from 'drizzle-orm';
// import * as fs from 'fs';
// import chalk from 'chalk';
//
// // Define subcategories for missing reference errors
// const REFERENCE_ERROR_SUBCATEGORIES = {
//   UNDEFINED_VARIABLE: {
//     patterns: [
//       'is not defined',
//       'undefined variable',
//       'cannot find name'
//     ],
//     examples: []
//   },
//   NULL_OBJECT_ACCESS: {
//     patterns: [
//       'cannot read property',
//       'cannot read',
//       'of undefined',
//       'of null',
//       'is undefined',
//       'is null',
//       'undefined is not an object',
//       'null is not an object'
//     ],
//     examples: []
//   },
//   CONTEXT_PROVIDER: {
//     patterns: [
//       'must be used within',
//       'provider',
//       'context',
//       'usetheme',
//       'useauth',
//       'authprovider',
//       'themeprovider'
//     ],
//     examples: []
//   },
//   COMPONENT_MISSING: {
//     patterns: [
//       'element type is invalid',
//       'expected a string',
//       'component',
//       'no default export'
//     ],
//     examples: []
//   },
//   PROPERTY_MISSING: {
//     patterns: [
//       'property',
//       'doesn\'t exist',
//       'has no property',
//       'has no member',
//       'not a property'
//     ],
//     examples: []
//   },
//   OTHER_REFERENCE: {
//     patterns: [
//       'missing',
//       'not found'
//     ],
//     examples: []
//   }
// };
//
// /**
//  * Extract text from message content
//  */
// function extractText(content: any): string {
//   try {
//     if (typeof content === 'string') return content;
//     if (Array.isArray(content)) {
//       return content
//         .filter(item => item && typeof item === 'object' && 'text' in item)
//         .map(item => item.text)
//         .join(' ');
//     }
//     if (typeof content === 'object') return JSON.stringify(content);
//     return String(content);
//   } catch (e) {
//     return String(content);
//   }
// }
//
// /**
//  * Determine if a message contains a missing reference error
//  */
// function hasMissingReferenceError(text: string): boolean {
//   text = text.toLowerCase();
//
//   // Check all subcategory patterns
//   for (const subcategory of Object.values(REFERENCE_ERROR_SUBCATEGORIES)) {
//     for (const pattern of subcategory.patterns) {
//       if (text.includes(pattern.toLowerCase())) {
//         return true;
//       }
//     }
//   }
//
//   return false;
// }
//
// /**
//  * Categorize a reference error into a subcategory
//  */
// function categorizeReferenceError(text: string): string {
//   text = text.toLowerCase();
//
//   for (const [subcategory, data] of Object.entries(REFERENCE_ERROR_SUBCATEGORIES)) {
//     for (const pattern of data.patterns) {
//       if (text.includes(pattern.toLowerCase())) {
//         return subcategory;
//       }
//     }
//   }
//
//   return 'OTHER_REFERENCE';
// }
//
// /**
//  * Extract error details from text
//  */
// function extractErrorDetails(text: string): string {
//   // Try to extract the most relevant part of the error
//
//   // Look for quoted error messages
//   const quotedErrors = text.match(/"([^"]*error[^"]*)"/gi) || [];
//   if (quotedErrors.length > 0) {
//     return quotedErrors.join('\n');
//   }
//
//   // Look for error: followed by text
//   const errorColonPattern = /error:?\s+([^.!?\n]+)/gi;
//   const errorColonMatches = text.match(errorColonPattern) || [];
//   if (errorColonMatches.length > 0) {
//     return errorColonMatches.join('\n');
//   }
//
//   // Look for common error phrases
//   const errorPhrases = [
//     /failed to\s+([^.!?\n]+)/gi,
//     /cannot\s+([^.!?\n]+)/gi,
//     /unable to\s+([^.!?\n]+)/gi,
//     /not found:?\s+([^.!?\n]+)/gi,
//     /missing\s+([^.!?\n]+)/gi,
//     /invalid\s+([^.!?\n]+)/gi,
//     /undefined\s+([^.!?\n]+)/gi,
//     /null\s+([^.!?\n]+)/gi,
//     /property\s+([^.!?\n]+)/gi
//   ];
//
//   for (const pattern of errorPhrases) {
//     const matches = text.match(pattern) || [];
//     if (matches.length > 0) {
//       return matches.join('\n');
//     }
//   }
//
//   // If we couldn't extract a specific part, return a snippet
//   return text.substring(0, 300) + (text.length > 300 ? '...' : '');
// }
//
// /**
//  * Main function to analyze reference errors
//  */
// async function analyzeReferenceErrors() {
//   console.log(chalk.blue('Analyzing missing reference errors...'));
//
//   // Check for DATABASE_URL
//   const DATABASE_URL = process.env.POSTGRES_URL;
//   if (!DATABASE_URL) {
//     console.error(chalk.red('POSTGRES_URL environment variable is not set'));
//     process.exit(1);
//   }
//
//   // Create database connection
//   const client = postgres(DATABASE_URL);
//   const db = drizzle(client);
//
//   try {
//     // Fetch error messages
//     console.log(chalk.yellow('Fetching error messages from database...'));
//
//     const errorMessages = await db
//       .select({
//         id: message.id,
//         content: message.content,
//         createdAt: message.createdAt
//       })
//       .from(message)
//       .where(eq(message.role, 'user'))
//       .orderBy(desc(message.createdAt))
//       .limit(1000);
//
//     // Filter messages with reference error content
//     const referenceErrors = errorMessages.filter(msg => {
//       const text = extractText(msg.content);
//       return hasMissingReferenceError(text);
//     });
//
//     console.log(chalk.green(`Found ${referenceErrors.length} reference errors out of ${errorMessages.length} total messages`));
//
//     // Process the reference errors
//     console.log(chalk.yellow('Categorizing reference errors...'));
//
//     const subcategoryCounts: Record<string, number> = {};
//
//     // Initialize counts
//     for (const subcategory of Object.keys(REFERENCE_ERROR_SUBCATEGORIES)) {
//       subcategoryCounts[subcategory] = 0;
//     }
//
//     // Categorize each error
//     referenceErrors.forEach(msg => {
//       const text = extractText(msg.content);
//       const subcategory = categorizeReferenceError(text);
//       const details = extractErrorDetails(text);
//       const date = new Date(msg.createdAt).toISOString().split('T')[0];
//
//       // Increment count
//       subcategoryCounts[subcategory]++;
//
//       // Add to examples (up to 5 per subcategory)
//       if (REFERENCE_ERROR_SUBCATEGORIES[subcategory].examples.length < 5) {
//         REFERENCE_ERROR_SUBCATEGORIES[subcategory].examples.push({
//           date,
//           details
//         });
//       }
//     });
//
//     // Output subcategory counts
//     console.log('\n' + chalk.bold.blue('=== Reference Error Subcategories ==='));
//
//     const sortedSubcategories = Object.entries(subcategoryCounts)
//       .sort((a, b) => b[1] - a[1]);
//
//     sortedSubcategories.forEach(([subcategory, count]) => {
//       const percentage = ((count / referenceErrors.length) * 100).toFixed(1);
//       console.log(chalk.bold(`${subcategory}: ${count} (${percentage}%)`));
//     });
//
//     // Prepare output content
//     let outputContent = '# Reference Error Analysis\n\n';
//     outputContent += `Total Reference Errors: ${referenceErrors.length}\n\n`;
//
//     sortedSubcategories.forEach(([subcategory, count]) => {
//       const percentage = ((count / referenceErrors.length) * 100).toFixed(1);
//       outputContent += `## ${subcategory}: ${count} (${percentage}%)\n\n`;
//
//       // Add patterns
//       outputContent += '### Patterns:\n';
//       REFERENCE_ERROR_SUBCATEGORIES[subcategory].patterns.forEach(pattern => {
//         outputContent += `- ${pattern}\n`;
//       });
//       outputContent += '\n';
//
//       // Add examples
//       outputContent += '### Examples:\n';
//       REFERENCE_ERROR_SUBCATEGORIES[subcategory].examples.forEach((example, index) => {
//         outputContent += `#### Example ${index + 1} [${example.date}]:\n\`\`\`\n${example.details}\n\`\`\`\n\n`;
//       });
//       outputContent += '\n';
//     });
//
//     // Write to file
//     const outputPath = './reference-errors-breakdown.md';
//     fs.writeFileSync(outputPath, outputContent);
//     console.log(chalk.green(`\nDetailed breakdown saved to ${outputPath}`));
//
//     // Also save as JSON for potential programmatic use
//     const jsonOutput = {
//       totalErrors: referenceErrors.length,
//       subcategories: Object.entries(REFERENCE_ERROR_SUBCATEGORIES).map(([name, data]) => ({
//         name,
//         count: subcategoryCounts[name],
//         percentage: ((subcategoryCounts[name] / referenceErrors.length) * 100).toFixed(1),
//         patterns: data.patterns,
//         examples: data.examples
//       }))
//     };
//
//     const jsonPath = './reference-errors-breakdown.json';
//     fs.writeFileSync(jsonPath, JSON.stringify(jsonOutput, null, 2));
//     console.log(chalk.green(`JSON data saved to ${jsonPath}`));
//
//     console.log(chalk.bold.green('\nAnalysis complete!'));
//
//   } catch (error) {
//     console.error(chalk.red('Error during analysis:'), error);
//     process.exit(1);
//   } finally {
//     // Close database connection
//     await client.end();
//     console.log('Database connection closed');
//   }
// }
//
// // Run the analysis
// analyzeReferenceErrors();
